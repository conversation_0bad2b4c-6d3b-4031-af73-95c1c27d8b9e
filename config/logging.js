/**
 * Centralized logging configuration
 * Environment-specific logging setup for all services
 */

const path = require('path');
const winston = require('winston');

/**
 * Service-specific logging configurations
 */
const serviceConfigs = {
    analytics: {
        logLevel: process.env.ANALYTICS_LOG_LEVEL || 'info',
        logPath: process.env.ANALYTICS_LOG_PATH || './logs/analytics',
        enablePerformanceLogging: true,
        enableQueryLogging: true,
        enableCacheLogging: true
    },
    dashboard: {
        logLevel: process.env.DASHBOARD_LOG_LEVEL || 'info', 
        logPath: process.env.DASHBOARD_LOG_PATH || './logs/dashboard',
        enableRequestLogging: true,
        enableAuthLogging: true,
        enableAPILogging: true
    },
    integration: {
        logLevel: process.env.INTEGRATION_LOG_LEVEL || 'info',
        logPath: process.env.INTEGRATION_LOG_PATH || './logs/integration', 
        enableWebhookLogging: true,
        enableExternalAPILogging: true,
        enableRetryLogging: true
    },
    frontend: {
        logLevel: process.env.FRONTEND_LOG_LEVEL || 'warn',
        logPath: process.env.FRONTEND_LOG_PATH || './logs/frontend',
        enableAccessLogging: true,
        enableErrorLogging: true
    }
};

/**
 * Environment-specific configurations
 */
const environmentConfigs = {
    development: {
        enableConsoleLogging: true,
        enableFileLogging: false,
        enableDebugLogging: true,
        logFormat: 'simple',
        logLevel: 'debug',
        enableColoredOutput: true,
        enableStackTraces: true
    },
    test: {
        enableConsoleLogging: false,
        enableFileLogging: false,
        enableDebugLogging: false,
        logFormat: 'json',
        logLevel: 'error',
        silent: true
    },
    staging: {
        enableConsoleLogging: true,
        enableFileLogging: true,
        enableDebugLogging: true,
        logFormat: 'json',
        logLevel: 'debug',
        enableStackTraces: true,
        enablePerformanceMonitoring: true
    },
    production: {
        enableConsoleLogging: false,
        enableFileLogging: true,
        enableDebugLogging: false,
        logFormat: 'json',
        logLevel: 'info',
        enableStackTraces: false,
        enablePerformanceMonitoring: true,
        enableLogRotation: true,
        enableLogCompression: true,
        enableExternalLogging: true
    }
};

/**
 * External logging service configurations
 */
const externalServices = {
    sentry: {
        enabled: !!process.env.SENTRY_DSN,
        dsn: process.env.SENTRY_DSN,
        environment: process.env.NODE_ENV,
        release: process.env.SERVICE_VERSION,
        beforeSend: (event) => {
            // Filter sensitive data
            if (event.request?.data) {
                event.request.data = filterSensitiveData(event.request.data);
            }
            return event;
        }
    },
    datadog: {
        enabled: !!process.env.DATADOG_API_KEY,
        apiKey: process.env.DATADOG_API_KEY,
        service: process.env.SERVICE_NAME,
        env: process.env.NODE_ENV,
        hostname: process.env.HOSTNAME || require('os').hostname()
    },
    elasticsearch: {
        enabled: !!process.env.ELASTICSEARCH_URL,
        url: process.env.ELASTICSEARCH_URL,
        index: process.env.ELASTICSEARCH_INDEX || 'ecommerce-logs',
        type: process.env.ELASTICSEARCH_TYPE || 'log'
    },
    cloudWatch: {
        enabled: !!process.env.AWS_CLOUDWATCH_LOG_GROUP,
        logGroup: process.env.AWS_CLOUDWATCH_LOG_GROUP,
        logStream: process.env.AWS_CLOUDWATCH_LOG_STREAM || process.env.SERVICE_NAME,
        region: process.env.AWS_REGION || 'us-east-1'
    }
};

/**
 * Logging patterns and formats
 */
const logPatterns = {
    http: {
        format: ':remote-addr - :remote-user [:date[clf]] ":method :url HTTP/:http-version" :status :res[content-length] ":referrer" ":user-agent" :response-time ms',
        options: {
            skip: (req, res) => {
                // Skip health checks and static assets
                return req.url.match(/\/(health|metrics|static|assets)/) || res.statusCode < 400;
            }
        }
    },
    performance: {
        slowQueryThreshold: parseInt(process.env.SLOW_QUERY_THRESHOLD) || 1000,
        slowRequestThreshold: parseInt(process.env.SLOW_REQUEST_THRESHOLD) || 5000,
        enableMemoryTracking: process.env.ENABLE_MEMORY_TRACKING === 'true',
        enableCPUTracking: process.env.ENABLE_CPU_TRACKING === 'true'
    },
    security: {
        logFailedLogins: true,
        logUnauthorizedAccess: true,
        logSuspiciousActivity: true,
        logDataAccess: process.env.NODE_ENV === 'production',
        redactSensitiveData: true
    },
    business: {
        logTransactions: true,
        logUserActions: true,
        logIntegrationEvents: true,
        logDataChanges: true,
        enableAuditTrail: process.env.NODE_ENV === 'production'
    }
};

/**
 * Log retention policies
 */
const retentionPolicies = {
    development: {
        maxFiles: 3,
        maxSize: '10m',
        maxAge: '7d'
    },
    staging: {
        maxFiles: 7,
        maxSize: '50m', 
        maxAge: '14d'
    },
    production: {
        maxFiles: 30,
        maxSize: '100m',
        maxAge: '90d',
        archiveAfter: '30d',
        deleteAfter: '365d'
    }
};

/**
 * Filter sensitive data from logs
 */
function filterSensitiveData(data) {
    if (typeof data !== 'object' || data === null) {
        return data;
    }

    const sensitiveFields = [
        'password', 'passwd', 'secret', 'token', 'key', 'auth',
        'authorization', 'cookie', 'x-api-key', 'x-auth-token',
        'credit_card', 'creditcard', 'cc', 'ssn', 'social_security'
    ];

    const filtered = Array.isArray(data) ? [] : {};

    for (const [key, value] of Object.entries(data)) {
        const lowercaseKey = key.toLowerCase();
        const isSensitive = sensitiveFields.some(field => lowercaseKey.includes(field));

        if (isSensitive) {
            filtered[key] = '[REDACTED]';
        } else if (typeof value === 'object' && value !== null) {
            filtered[key] = filterSensitiveData(value);
        } else {
            filtered[key] = value;
        }
    }

    return filtered;
}

/**
 * Create log file transports based on configuration
 */
function createFileTransports(config) {
    const transports = [];
    const env = process.env.NODE_ENV || 'development';
    const retention = retentionPolicies[env] || retentionPolicies.development;

    // Ensure log directory exists
    const fs = require('fs');
    if (!fs.existsSync(config.logPath)) {
        fs.mkdirSync(config.logPath, { recursive: true });
    }

    // Combined log file
    transports.push(new winston.transports.File({
        filename: path.join(config.logPath, 'combined.log'),
        level: config.logLevel,
        maxsize: retention.maxSize,
        maxFiles: retention.maxFiles,
        tailable: true,
        zippedArchive: true
    }));

    // Error log file
    transports.push(new winston.transports.File({
        filename: path.join(config.logPath, 'error.log'),
        level: 'error',
        maxsize: retention.maxSize,
        maxFiles: retention.maxFiles,
        tailable: true,
        zippedArchive: true
    }));

    // HTTP log file (for services that handle HTTP requests)
    if (config.enableRequestLogging || config.enableAPILogging) {
        transports.push(new winston.transports.File({
            filename: path.join(config.logPath, 'http.log'),
            level: 'http',
            maxsize: retention.maxSize,
            maxFiles: retention.maxFiles,
            tailable: true,
            zippedArchive: true
        }));
    }

    // Performance log file
    if (config.enablePerformanceLogging) {
        transports.push(new winston.transports.File({
            filename: path.join(config.logPath, 'performance.log'),
            level: 'info',
            maxsize: retention.maxSize,
            maxFiles: retention.maxFiles,
            tailable: true,
            zippedArchive: true,
            filter: (info) => info.performanceLabel || info.duration
        }));
    }

    // Security log file
    if (logPatterns.security.logFailedLogins) {
        transports.push(new winston.transports.File({
            filename: path.join(config.logPath, 'security.log'),
            level: 'warn',
            maxsize: retention.maxSize,
            maxFiles: retention.maxFiles,
            tailable: true,
            zippedArchive: true,
            filter: (info) => info.securityEvent || info.audit
        }));
    }

    return transports;
}

/**
 * Create external service transports
 */
function createExternalTransports() {
    const transports = [];

    // Sentry transport
    if (externalServices.sentry.enabled) {
        // Note: In a real implementation, you would add Sentry Winston transport
        // const Sentry = require('@sentry/node');
        // transports.push(new SentryTransport());
    }

    // Datadog transport
    if (externalServices.datadog.enabled) {
        // Note: In a real implementation, you would add Datadog Winston transport
        // transports.push(new DatadogTransport());
    }

    // Elasticsearch transport
    if (externalServices.elasticsearch.enabled) {
        // Note: In a real implementation, you would add Elasticsearch Winston transport
        // transports.push(new ElasticsearchTransport());
    }

    return transports;
}

/**
 * Get logging configuration for a service
 */
function getLoggingConfig(serviceName) {
    const env = process.env.NODE_ENV || 'development';
    const serviceConfig = serviceConfigs[serviceName] || serviceConfigs.analytics;
    const envConfig = environmentConfigs[env];

    return {
        ...serviceConfig,
        ...envConfig,
        serviceName,
        environment: env,
        patterns: logPatterns,
        retention: retentionPolicies[env],
        externalServices
    };
}

/**
 * Initialize logging for a service
 */
function initializeLogging(serviceName) {
    const config = getLoggingConfig(serviceName);
    
    // Set service name in environment for logger
    process.env.SERVICE_NAME = serviceName;
    
    // Import and configure logger
    const { logger } = require('../shared/utils/logger');
    
    // Configure log rotation if enabled
    if (config.enableLogRotation) {
        const { logRotation } = require('../shared/utils/logrotation');
        // Log rotation is automatically started in the logrotation module
    }
    
    return logger;
}

/**
 * Middleware factory for service-specific logging
 */
function createLoggingMiddleware(serviceName, options = {}) {
    const config = getLoggingConfig(serviceName);
    const { requestLoggingMiddleware } = require('../shared/utils/logger');
    
    return requestLoggingMiddleware({
        logBody: config.enableRequestLogging && (process.env.NODE_ENV === 'development'),
        logHeaders: config.enableRequestLogging && (process.env.NODE_ENV === 'development'),
        excludePaths: options.excludePaths || ['/health', '/metrics'],
        logLevel: options.logLevel || 'http'
    });
}

module.exports = {
    serviceConfigs,
    environmentConfigs,
    externalServices,
    logPatterns,
    retentionPolicies,
    getLoggingConfig,
    initializeLogging,
    createLoggingMiddleware,
    createFileTransports,
    createExternalTransports,
    filterSensitiveData
};