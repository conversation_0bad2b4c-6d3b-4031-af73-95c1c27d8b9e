/**
 * Production-ready logging configuration using Winston
 * Centralized logging for all services with structured logging, correlation IDs, and multiple transports
 */

const winston = require('winston');
const path = require('path');
const fs = require('fs');

// Ensure logs directory exists
const logDir = process.env.LOG_PATH || './logs';
if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true });
}

/**
 * Custom log levels with priorities
 */
const logLevels = {
    error: 0,
    warn: 1,
    info: 2,
    http: 3,
    debug: 4,
    trace: 5
};

/**
 * Color scheme for console output
 */
const logColors = {
    error: 'red',
    warn: 'yellow', 
    info: 'green',
    http: 'magenta',
    debug: 'blue',
    trace: 'gray'
};

/**
 * Custom log format for structured logging
 */
const logFormat = winston.format.combine(
    winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss.SSS' }),
    winston.format.errors({ stack: true }),
    winston.format.json(),
    winston.format.printf(({ timestamp, level, message, correlationId, service, userId, ...meta }) => {
        const logEntry = {
            timestamp,
            level,
            service: service || process.env.SERVICE_NAME || 'unknown',
            correlationId,
            userId,
            message,
            ...meta
        };

        // Remove undefined fields
        Object.keys(logEntry).forEach(key => {
            if (logEntry[key] === undefined) {
                delete logEntry[key];
            }
        });

        return JSON.stringify(logEntry);
    })
);

/**
 * Console format for development
 */
const consoleFormat = winston.format.combine(
    winston.format.timestamp({ format: 'HH:mm:ss' }),
    winston.format.colorize({ all: true, colors: logColors }),
    winston.format.printf(({ timestamp, level, message, correlationId, service, ...meta }) => {
        const serviceName = service || process.env.SERVICE_NAME || 'app';
        const corrId = correlationId ? `[${correlationId.substring(0, 8)}]` : '';
        const metaStr = Object.keys(meta).length > 0 ? `\n${JSON.stringify(meta, null, 2)}` : '';
        
        return `${timestamp} [${serviceName}] ${level}: ${corrId} ${message}${metaStr}`;
    })
);

/**
 * File rotation configuration
 */
const fileRotationOptions = {
    maxsize: 10 * 1024 * 1024, // 10MB
    maxFiles: 5,
    tailable: true,
    zippedArchive: true
};

/**
 * Create transports based on environment
 */
function createTransports() {
    const transports = [];
    
    // Console transport (always enabled in development)
    if (process.env.NODE_ENV === 'development' || process.env.ENABLE_CONSOLE_LOGS === 'true') {
        transports.push(
            new winston.transports.Console({
                format: consoleFormat,
                level: process.env.LOG_LEVEL || 'debug'
            })
        );
    }
    
    // File transports
    if (process.env.NODE_ENV === 'production' || process.env.ENABLE_FILE_LOGS === 'true') {
        // Combined logs
        transports.push(
            new winston.transports.File({
                filename: path.join(logDir, 'combined.log'),
                format: logFormat,
                level: 'debug',
                ...fileRotationOptions
            })
        );
        
        // Error logs
        transports.push(
            new winston.transports.File({
                filename: path.join(logDir, 'error.log'),
                format: logFormat,
                level: 'error',
                ...fileRotationOptions
            })
        );
        
        // HTTP logs
        transports.push(
            new winston.transports.File({
                filename: path.join(logDir, 'http.log'),
                format: logFormat,
                level: 'http',
                ...fileRotationOptions
            })
        );
    }
    
    // External logging services
    if (process.env.SENTRY_DSN) {
        // Note: Sentry transport would be added here in a real implementation
        // transports.push(new SentryTransport());
    }
    
    if (process.env.DATADOG_API_KEY) {
        // Note: Datadog transport would be added here
        // transports.push(new DatadogTransport());
    }
    
    return transports;
}

/**
 * Create Winston logger instance
 */
const logger = winston.createLogger({
    levels: logLevels,
    level: process.env.LOG_LEVEL || 'info',
    format: logFormat,
    transports: createTransports(),
    exitOnError: false,
    silent: process.env.NODE_ENV === 'test' && !process.env.ENABLE_TEST_LOGS
});

/**
 * Add colors to Winston
 */
winston.addColors(logColors);

/**
 * Enhanced logger with additional methods
 */
class EnhancedLogger {
    constructor(baseLogger) {
        this.logger = baseLogger;
        this.defaultMeta = {
            service: process.env.SERVICE_NAME || 'unknown',
            version: process.env.SERVICE_VERSION || '1.0.0',
            environment: process.env.NODE_ENV || 'development'
        };
    }

    /**
     * Create child logger with persistent metadata
     */
    child(meta = {}) {
        const childLogger = this.logger.child({ ...this.defaultMeta, ...meta });
        return new EnhancedLogger(childLogger);
    }

    /**
     * Log with correlation ID
     */
    withCorrelation(correlationId) {
        return this.child({ correlationId });
    }

    /**
     * Log with user context
     */
    withUser(userId, userEmail = null) {
        return this.child({ userId, userEmail });
    }

    /**
     * Log with request context
     */
    withRequest(req) {
        const requestMeta = {
            correlationId: req.correlationId || req.id,
            userId: req.user?.id,
            userEmail: req.user?.email,
            method: req.method,
            url: req.originalUrl || req.url,
            userAgent: req.get('User-Agent'),
            ip: req.ip || req.connection?.remoteAddress
        };
        
        return this.child(requestMeta);
    }

    /**
     * Performance logging
     */
    time(label) {
        const startTime = process.hrtime.bigint();
        
        return {
            end: (meta = {}) => {
                const endTime = process.hrtime.bigint();
                const duration = Number(endTime - startTime) / 1000000; // Convert to milliseconds
                
                this.info(`Performance: ${label}`, {
                    duration,
                    durationUnit: 'ms',
                    performanceLabel: label,
                    ...meta
                });
                
                return duration;
            }
        };
    }

    /**
     * Database query logging
     */
    logQuery(query, params = [], duration = null, meta = {}) {
        this.debug('Database query executed', {
            query: query.replace(/\s+/g, ' ').trim(),
            params: Array.isArray(params) ? params : [params],
            duration,
            queryType: query.trim().split(' ')[0].toUpperCase(),
            ...meta
        });
    }

    /**
     * API call logging
     */
    logApiCall(url, method, statusCode, duration, meta = {}) {
        const level = statusCode >= 400 ? 'error' : statusCode >= 300 ? 'warn' : 'info';
        
        this[level]('API call completed', {
            url,
            method,
            statusCode,
            duration,
            durationUnit: 'ms',
            ...meta
        });
    }

    /**
     * Business logic logging
     */
    logBusinessEvent(event, data = {}, meta = {}) {
        this.info(`Business event: ${event}`, {
            businessEvent: event,
            eventData: data,
            ...meta
        });
    }

    /**
     * Security event logging
     */
    logSecurityEvent(event, details = {}, severity = 'warn') {
        this[severity](`Security event: ${event}`, {
            securityEvent: event,
            severity,
            ...details
        });
    }

    /**
     * Integration logging
     */
    logIntegration(integration, action, status, meta = {}) {
        const level = status === 'success' ? 'info' : status === 'error' ? 'error' : 'warn';
        
        this[level](`Integration ${action}`, {
            integration,
            action,
            status,
            ...meta
        });
    }

    /**
     * Error logging with context
     */
    logError(error, context = {}) {
        const errorMeta = {
            error: {
                name: error.name,
                message: error.message,
                stack: error.stack,
                code: error.code,
                statusCode: error.statusCode
            },
            ...context
        };

        this.error('Error occurred', errorMeta);
    }

    /**
     * Audit logging
     */
    audit(action, resource, userId, details = {}) {
        this.info('Audit log', {
            audit: true,
            action,
            resource,
            userId,
            ...details,
            timestamp: new Date().toISOString()
        });
    }

    // Delegate standard logging methods
    error(message, meta = {}) {
        this.logger.error(message, { ...this.defaultMeta, ...meta });
    }

    warn(message, meta = {}) {
        this.logger.warn(message, { ...this.defaultMeta, ...meta });
    }

    info(message, meta = {}) {
        this.logger.info(message, { ...this.defaultMeta, ...meta });
    }

    http(message, meta = {}) {
        this.logger.http(message, { ...this.defaultMeta, ...meta });
    }

    debug(message, meta = {}) {
        this.logger.debug(message, { ...this.defaultMeta, ...meta });
    }

    trace(message, meta = {}) {
        this.logger.log('trace', message, { ...this.defaultMeta, ...meta });
    }
}

/**
 * Create enhanced logger instance
 */
const enhancedLogger = new EnhancedLogger(logger);

/**
 * Express middleware for request logging
 */
function requestLoggingMiddleware(options = {}) {
    const {
        logBody = false,
        logHeaders = false,
        excludePaths = ['/health', '/metrics'],
        logLevel = 'http'
    } = options;

    return (req, res, next) => {
        // Skip excluded paths
        if (excludePaths.some(path => req.path.startsWith(path))) {
            return next();
        }

        // Generate correlation ID if not present
        req.correlationId = req.correlationId || req.get('x-correlation-id') || generateCorrelationId();
        
        // Add correlation ID to response headers
        res.setHeader('x-correlation-id', req.correlationId);

        const startTime = Date.now();
        const requestLogger = enhancedLogger.withRequest(req);

        // Log request
        const requestMeta = {
            method: req.method,
            url: req.originalUrl,
            userAgent: req.get('User-Agent'),
            ip: req.ip,
            correlationId: req.correlationId
        };

        if (logHeaders) {
            requestMeta.headers = req.headers;
        }

        if (logBody && req.body) {
            requestMeta.body = req.body;
        }

        requestLogger[logLevel]('HTTP request started', requestMeta);

        // Override res.end to log response
        const originalEnd = res.end;
        res.end = function(chunk, encoding) {
            const duration = Date.now() - startTime;
            
            const responseMeta = {
                method: req.method,
                url: req.originalUrl,
                statusCode: res.statusCode,
                duration,
                durationUnit: 'ms',
                correlationId: req.correlationId
            };

            const level = res.statusCode >= 400 ? 'error' : res.statusCode >= 300 ? 'warn' : logLevel;
            requestLogger[level]('HTTP request completed', responseMeta);

            originalEnd.call(this, chunk, encoding);
        };

        // Add logger to request for use in handlers
        req.logger = requestLogger;

        next();
    };
}

/**
 * Generate correlation ID
 */
function generateCorrelationId() {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Stream for Morgan HTTP logging
 */
const morganStream = {
    write: (message) => {
        enhancedLogger.http(message.trim());
    }
};

/**
 * Graceful shutdown logging
 */
function setupGracefulShutdown() {
    const shutdown = (signal) => {
        enhancedLogger.info(`Received ${signal}, shutting down gracefully`);
        
        // Close logger transports
        logger.end(() => {
            process.exit(0);
        });
    };

    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGINT', () => shutdown('SIGINT'));
}

/**
 * Error handler for uncaught exceptions
 */
function setupErrorHandlers() {
    process.on('uncaughtException', (error) => {
        enhancedLogger.error('Uncaught exception', { error: error.stack });
        process.exit(1);
    });

    process.on('unhandledRejection', (reason, promise) => {
        enhancedLogger.error('Unhandled rejection', { 
            reason: reason.toString(),
            promise: promise.toString()
        });
    });
}

// Setup error handlers in production
if (process.env.NODE_ENV === 'production') {
    setupGracefulShutdown();
    setupErrorHandlers();
}

module.exports = {
    logger: enhancedLogger,
    requestLoggingMiddleware,
    morganStream,
    generateCorrelationId,
    setupGracefulShutdown,
    setupErrorHandlers
};