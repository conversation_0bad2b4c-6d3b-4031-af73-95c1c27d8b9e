/**
 * Log rotation and cleanup utilities
 * Manages log file rotation, compression, and cleanup for production environments
 */

const fs = require('fs').promises;
const path = require('path');
const zlib = require('zlib');
const { promisify } = require('util');
const cron = require('node-cron');

const gzip = promisify(zlib.gzip);

class LogRotation {
    constructor(options = {}) {
        this.logDir = options.logDir || process.env.LOG_PATH || './logs';
        this.maxSize = options.maxSize || 10 * 1024 * 1024; // 10MB
        this.maxFiles = options.maxFiles || 5;
        this.maxAge = options.maxAge || 7 * 24 * 60 * 60 * 1000; // 7 days
        this.compress = options.compress !== false;
        this.cronPattern = options.cronPattern || '0 0 * * *'; // Daily at midnight
        this.enabled = options.enabled !== false;
        
        if (this.enabled) {
            this.startRotationSchedule();
        }
    }

    /**
     * Start the log rotation schedule
     */
    startRotationSchedule() {
        cron.schedule(this.cronPattern, async () => {
            await this.rotateAllLogs();
        });

        // Also check on startup
        setTimeout(() => this.rotateAllLogs(), 5000);
    }

    /**
     * Rotate all log files in the directory
     */
    async rotateAllLogs() {
        try {
            const files = await fs.readdir(this.logDir);
            const logFiles = files.filter(file => 
                file.endsWith('.log') && !file.includes('.') 
            );

            for (const logFile of logFiles) {
                await this.rotateLogFile(logFile);
            }

            await this.cleanupOldLogs();
        } catch (error) {
            console.error('Log rotation error:', error);
        }
    }

    /**
     * Rotate a specific log file
     */
    async rotateLogFile(filename) {
        const filePath = path.join(this.logDir, filename);
        
        try {
            const stats = await fs.stat(filePath);
            
            // Check if rotation is needed
            if (stats.size < this.maxSize && Date.now() - stats.mtime.getTime() < 24 * 60 * 60 * 1000) {
                return;
            }

            // Create backup filename with timestamp
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0];
            const baseName = path.basename(filename, '.log');
            const backupName = `${baseName}.${timestamp}.log`;
            const backupPath = path.join(this.logDir, backupName);

            // Copy current log to backup
            await fs.copyFile(filePath, backupPath);

            // Clear the original log file
            await fs.writeFile(filePath, '');

            // Compress the backup if enabled
            if (this.compress) {
                await this.compressLogFile(backupPath);
            }

            console.log(`Rotated log file: ${filename} -> ${backupName}`);
        } catch (error) {
            console.error(`Error rotating log file ${filename}:`, error);
        }
    }

    /**
     * Compress a log file
     */
    async compressLogFile(filePath) {
        try {
            const data = await fs.readFile(filePath);
            const compressed = await gzip(data);
            const compressedPath = `${filePath}.gz`;
            
            await fs.writeFile(compressedPath, compressed);
            await fs.unlink(filePath);
            
            console.log(`Compressed log file: ${path.basename(filePath)}`);
        } catch (error) {
            console.error(`Error compressing log file ${filePath}:`, error);
        }
    }

    /**
     * Clean up old log files
     */
    async cleanupOldLogs() {
        try {
            const files = await fs.readdir(this.logDir);
            const logFiles = files.filter(file => 
                file.includes('.log') && file !== 'combined.log' && file !== 'error.log' && file !== 'http.log'
            );

            // Group files by base name
            const fileGroups = {};
            logFiles.forEach(file => {
                const baseName = file.split('.')[0];
                if (!fileGroups[baseName]) {
                    fileGroups[baseName] = [];
                }
                fileGroups[baseName].push(file);
            });

            // Clean up each group
            for (const [baseName, files] of Object.entries(fileGroups)) {
                await this.cleanupFileGroup(baseName, files);
            }
        } catch (error) {
            console.error('Error during log cleanup:', error);
        }
    }

    /**
     * Clean up a group of log files
     */
    async cleanupFileGroup(baseName, files) {
        // Sort files by modification time (newest first)
        const filesWithStats = await Promise.all(
            files.map(async (file) => {
                const filePath = path.join(this.logDir, file);
                const stats = await fs.stat(filePath);
                return { file, path: filePath, mtime: stats.mtime };
            })
        );

        filesWithStats.sort((a, b) => b.mtime - a.mtime);

        // Remove files older than maxAge
        const cutoffTime = Date.now() - this.maxAge;
        const oldFiles = filesWithStats.filter(f => f.mtime.getTime() < cutoffTime);
        
        for (const { file, path: filePath } of oldFiles) {
            try {
                await fs.unlink(filePath);
                console.log(`Deleted old log file: ${file}`);
            } catch (error) {
                console.error(`Error deleting old log file ${file}:`, error);
            }
        }

        // Remove excess files (keep only maxFiles)
        const excessFiles = filesWithStats.slice(this.maxFiles);
        for (const { file, path: filePath } of excessFiles) {
            try {
                await fs.unlink(filePath);
                console.log(`Deleted excess log file: ${file}`);
            } catch (error) {
                console.error(`Error deleting excess log file ${file}:`, error);
            }
        }
    }

    /**
     * Get log file statistics
     */
    async getLogStats() {
        try {
            const files = await fs.readdir(this.logDir);
            const logFiles = files.filter(file => file.includes('.log'));
            
            const stats = {};
            let totalSize = 0;

            for (const file of logFiles) {
                const filePath = path.join(this.logDir, file);
                const fileStat = await fs.stat(filePath);
                
                stats[file] = {
                    size: fileStat.size,
                    sizeHuman: this.formatBytes(fileStat.size),
                    modified: fileStat.mtime,
                    age: Date.now() - fileStat.mtime.getTime()
                };
                
                totalSize += fileStat.size;
            }

            return {
                files: stats,
                totalFiles: logFiles.length,
                totalSize,
                totalSizeHuman: this.formatBytes(totalSize),
                directory: this.logDir
            };
        } catch (error) {
            console.error('Error getting log stats:', error);
            return null;
        }
    }

    /**
     * Format bytes to human readable string
     */
    formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * Manual rotation trigger
     */
    async forceRotation() {
        console.log('Starting manual log rotation...');
        await this.rotateAllLogs();
        console.log('Manual log rotation completed');
    }

    /**
     * Stop the rotation schedule
     */
    stop() {
        if (this.cronJob) {
            this.cronJob.stop();
        }
    }
}

/**
 * Log aggregation utilities
 */
class LogAggregator {
    constructor(logDir) {
        this.logDir = logDir || process.env.LOG_PATH || './logs';
    }

    /**
     * Aggregate logs by date range
     */
    async aggregateLogs(startDate, endDate, logLevel = null) {
        try {
            const files = await fs.readdir(this.logDir);
            const logFiles = files.filter(file => file.endsWith('.log'));
            
            const aggregatedLogs = [];

            for (const file of logFiles) {
                const filePath = path.join(this.logDir, file);
                const content = await fs.readFile(filePath, 'utf8');
                const lines = content.split('\n').filter(line => line.trim());

                for (const line of lines) {
                    try {
                        const logEntry = JSON.parse(line);
                        const logDate = new Date(logEntry.timestamp);

                        if (logDate >= startDate && logDate <= endDate) {
                            if (!logLevel || logEntry.level === logLevel) {
                                aggregatedLogs.push(logEntry);
                            }
                        }
                    } catch (parseError) {
                        // Skip invalid JSON lines
                        continue;
                    }
                }
            }

            return aggregatedLogs.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
        } catch (error) {
            console.error('Error aggregating logs:', error);
            return [];
        }
    }

    /**
     * Generate log summary report
     */
    async generateSummaryReport(startDate, endDate) {
        const logs = await this.aggregateLogs(startDate, endDate);
        
        const summary = {
            totalLogs: logs.length,
            timeRange: {
                start: startDate.toISOString(),
                end: endDate.toISOString()
            },
            byLevel: {},
            byService: {},
            errors: [],
            topErrors: {},
            performance: {
                slowQueries: [],
                slowRequests: []
            }
        };

        logs.forEach(log => {
            // Count by level
            summary.byLevel[log.level] = (summary.byLevel[log.level] || 0) + 1;
            
            // Count by service
            summary.byService[log.service] = (summary.byService[log.service] || 0) + 1;
            
            // Collect errors
            if (log.level === 'error') {
                summary.errors.push(log);
                
                const errorKey = log.error?.name || log.message;
                summary.topErrors[errorKey] = (summary.topErrors[errorKey] || 0) + 1;
            }
            
            // Collect performance data
            if (log.duration) {
                if (log.query && log.duration > 1000) {
                    summary.performance.slowQueries.push({
                        query: log.query,
                        duration: log.duration,
                        timestamp: log.timestamp
                    });
                }
                
                if (log.method && log.duration > 5000) {
                    summary.performance.slowRequests.push({
                        method: log.method,
                        url: log.url,
                        duration: log.duration,
                        timestamp: log.timestamp
                    });
                }
            }
        });

        // Sort performance arrays
        summary.performance.slowQueries.sort((a, b) => b.duration - a.duration);
        summary.performance.slowRequests.sort((a, b) => b.duration - a.duration);

        return summary;
    }

    /**
     * Export logs to different formats
     */
    async exportLogs(startDate, endDate, format = 'json') {
        const logs = await this.aggregateLogs(startDate, endDate);
        
        switch (format.toLowerCase()) {
            case 'json':
                return JSON.stringify(logs, null, 2);
                
            case 'csv':
                return this.convertToCSV(logs);
                
            case 'txt':
                return logs.map(log => 
                    `[${log.timestamp}] ${log.level.toUpperCase()}: ${log.message}`
                ).join('\n');
                
            default:
                throw new Error(`Unsupported export format: ${format}`);
        }
    }

    /**
     * Convert logs to CSV format
     */
    convertToCSV(logs) {
        if (logs.length === 0) return '';
        
        const headers = ['timestamp', 'level', 'service', 'message', 'correlationId', 'userId'];
        const csvLines = [headers.join(',')];
        
        logs.forEach(log => {
            const row = headers.map(header => {
                const value = log[header] || '';
                return `"${String(value).replace(/"/g, '""')}"`;
            });
            csvLines.push(row.join(','));
        });
        
        return csvLines.join('\n');
    }
}

// Create singleton instances
const logRotation = new LogRotation({
    enabled: process.env.NODE_ENV === 'production' || process.env.ENABLE_LOG_ROTATION === 'true'
});

const logAggregator = new LogAggregator();

module.exports = {
    LogRotation,
    LogAggregator,
    logRotation,
    logAggregator
};