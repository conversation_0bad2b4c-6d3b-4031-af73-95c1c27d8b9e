/**
 * Centralized error handling middleware with comprehensive logging
 * Handles different types of errors and provides consistent error responses
 */

const { logger } = require('../utils/logger');

/**
 * Custom error classes
 */
class AppError extends Error {
    constructor(message, statusCode = 500, code = null, isOperational = true) {
        super(message);
        this.name = this.constructor.name;
        this.statusCode = statusCode;
        this.code = code;
        this.isOperational = isOperational;
        this.timestamp = new Date().toISOString();
        
        Error.captureStackTrace(this, this.constructor);
    }
}

class ValidationError extends AppError {
    constructor(message, errors = []) {
        super(message, 400, 'VALIDATION_ERROR');
        this.errors = errors;
    }
}

class AuthenticationError extends AppError {
    constructor(message = 'Authentication required') {
        super(message, 401, 'AUTHENTICATION_ERROR');
    }
}

class AuthorizationError extends AppError {
    constructor(message = 'Insufficient permissions') {
        super(message, 403, 'AUTHORIZATION_ERROR');
    }
}

class NotFoundError extends AppError {
    constructor(resource = 'Resource') {
        super(`${resource} not found`, 404, 'NOT_FOUND_ERROR');
    }
}

class ConflictError extends AppError {
    constructor(message = 'Resource conflict') {
        super(message, 409, 'CONFLICT_ERROR');
    }
}

class RateLimitError extends AppError {
    constructor(message = 'Rate limit exceeded') {
        super(message, 429, 'RATE_LIMIT_ERROR');
    }
}

class IntegrationError extends AppError {
    constructor(integration, message, originalError = null) {
        super(`Integration error (${integration}): ${message}`, 502, 'INTEGRATION_ERROR');
        this.integration = integration;
        this.originalError = originalError;
    }
}

class DatabaseError extends AppError {
    constructor(message, originalError = null) {
        super(`Database error: ${message}`, 500, 'DATABASE_ERROR');
        this.originalError = originalError;
    }
}

class CacheError extends AppError {
    constructor(message, originalError = null) {
        super(`Cache error: ${message}`, 500, 'CACHE_ERROR');
        this.originalError = originalError;
    }
}

/**
 * Error context builder
 */
class ErrorContext {
    constructor() {
        this.context = {};
    }

    addRequest(req) {
        this.context.request = {
            method: req.method,
            url: req.originalUrl,
            userAgent: req.get('User-Agent'),
            ip: req.ip,
            correlationId: req.correlationId,
            userId: req.user?.id,
            body: this.sanitizeBody(req.body),
            query: req.query,
            params: req.params
        };
        return this;
    }

    addUser(user) {
        this.context.user = {
            id: user.id,
            email: user.email,
            role: user.role
        };
        return this;
    }

    addDatabase(query, params = []) {
        this.context.database = {
            query: query.replace(/\s+/g, ' ').trim(),
            params: Array.isArray(params) ? params : [params]
        };
        return this;
    }

    addIntegration(integration, action, data = {}) {
        this.context.integration = {
            name: integration,
            action,
            data: this.sanitizeData(data)
        };
        return this;
    }

    addCustom(key, value) {
        this.context[key] = value;
        return this;
    }

    sanitizeBody(body) {
        if (!body) return undefined;
        
        const sanitized = { ...body };
        const sensitiveFields = ['password', 'token', 'secret', 'key', 'auth'];
        
        Object.keys(sanitized).forEach(key => {
            if (sensitiveFields.some(field => key.toLowerCase().includes(field))) {
                sanitized[key] = '[REDACTED]';
            }
        });
        
        return sanitized;
    }

    sanitizeData(data) {
        if (typeof data !== 'object') return data;
        return this.sanitizeBody(data);
    }

    build() {
        return this.context;
    }
}

/**
 * Error notification service
 */
class ErrorNotificationService {
    constructor() {
        this.notifications = [];
        this.enabled = process.env.ERROR_NOTIFICATIONS_ENABLED === 'true';
        this.threshold = parseInt(process.env.ERROR_NOTIFICATION_THRESHOLD) || 10;
        this.timeWindow = parseInt(process.env.ERROR_NOTIFICATION_WINDOW) || 300000; // 5 minutes
    }

    async notify(error, context) {
        if (!this.enabled) return;

        // Check if we should throttle notifications
        if (this.shouldThrottle(error)) {
            return;
        }

        try {
            // Send to external services
            await this.sendToSlack(error, context);
            await this.sendToEmail(error, context);
            await this.sendToSentry(error, context);
        } catch (notificationError) {
            logger.error('Failed to send error notification', {
                originalError: error.message,
                notificationError: notificationError.message
            });
        }
    }

    shouldThrottle(error) {
        const now = Date.now();
        const windowStart = now - this.timeWindow;
        
        // Clean old notifications
        this.notifications = this.notifications.filter(n => n.timestamp > windowStart);
        
        // Count similar errors
        const similarErrors = this.notifications.filter(n => 
            n.message === error.message && n.code === error.code
        ).length;
        
        if (similarErrors >= this.threshold) {
            logger.warn('Error notification throttled', {
                error: error.message,
                count: similarErrors,
                threshold: this.threshold
            });
            return true;
        }

        // Record this notification
        this.notifications.push({
            message: error.message,
            code: error.code,
            timestamp: now
        });

        return false;
    }

    async sendToSlack(error, context) {
        if (!process.env.SLACK_WEBHOOK_URL) return;

        const payload = {
            text: `🚨 Error in ${process.env.SERVICE_NAME || 'Application'}`,
            attachments: [{
                color: 'danger',
                fields: [
                    { title: 'Error', value: error.message, short: false },
                    { title: 'Code', value: error.code || 'Unknown', short: true },
                    { title: 'Status', value: error.statusCode || 500, short: true },
                    { title: 'Environment', value: process.env.NODE_ENV, short: true },
                    { title: 'Correlation ID', value: context.request?.correlationId, short: true }
                ],
                timestamp: Math.floor(Date.now() / 1000)
            }]
        };

        // Implementation would send to Slack webhook
        logger.debug('Would send Slack notification', { payload });
    }

    async sendToEmail(error, context) {
        if (!process.env.ERROR_EMAIL_RECIPIENTS) return;

        const emailData = {
            to: process.env.ERROR_EMAIL_RECIPIENTS.split(','),
            subject: `Error Alert: ${error.message}`,
            body: this.formatEmailBody(error, context)
        };

        // Implementation would send email
        logger.debug('Would send email notification', { emailData });
    }

    async sendToSentry(error, context) {
        if (!process.env.SENTRY_DSN) return;

        // Implementation would send to Sentry
        logger.debug('Would send to Sentry', { error: error.message, context });
    }

    formatEmailBody(error, context) {
        return `
Error Details:
- Message: ${error.message}
- Code: ${error.code || 'Unknown'}
- Status: ${error.statusCode || 500}
- Timestamp: ${error.timestamp}
- Environment: ${process.env.NODE_ENV}

Request Details:
- Method: ${context.request?.method}
- URL: ${context.request?.url}
- User: ${context.request?.userId || 'Anonymous'}
- IP: ${context.request?.ip}
- Correlation ID: ${context.request?.correlationId}

Stack Trace:
${error.stack}
        `.trim();
    }
}

/**
 * Main error handler middleware
 */
function errorHandler(options = {}) {
    const notificationService = new ErrorNotificationService();
    const {
        includeStack = process.env.NODE_ENV === 'development',
        logErrors = true,
        notifyErrors = true
    } = options;

    return async (error, req, res, next) => {
        // Build error context
        const context = new ErrorContext()
            .addRequest(req)
            .addUser(req.user)
            .build();

        // Handle different error types
        let processedError = error;
        
        if (error.name === 'ValidationError') {
            processedError = new ValidationError(error.message, error.errors);
        } else if (error.name === 'CastError') {
            processedError = new ValidationError('Invalid data format');
        } else if (error.code === 11000) {
            processedError = new ConflictError('Duplicate entry');
        } else if (error.name === 'TokenExpiredError') {
            processedError = new AuthenticationError('Token expired');
        } else if (error.name === 'JsonWebTokenError') {
            processedError = new AuthenticationError('Invalid token');
        } else if (error.code === 'ECONNREFUSED') {
            processedError = new IntegrationError('External Service', 'Connection refused', error);
        } else if (!(error instanceof AppError)) {
            // Wrap unknown errors
            processedError = new AppError(
                process.env.NODE_ENV === 'production' ? 'Internal server error' : error.message,
                500,
                'INTERNAL_ERROR',
                false
            );
        }

        // Log the error
        if (logErrors) {
            const logContext = {
                ...context,
                error: {
                    name: processedError.name,
                    message: processedError.message,
                    code: processedError.code,
                    statusCode: processedError.statusCode,
                    stack: processedError.stack,
                    isOperational: processedError.isOperational
                }
            };

            if (processedError.statusCode >= 500) {
                logger.error('Server error occurred', logContext);
            } else {
                logger.warn('Client error occurred', logContext);
            }
        }

        // Send notifications for server errors
        if (notifyErrors && processedError.statusCode >= 500) {
            await notificationService.notify(processedError, context);
        }

        // Prepare response
        const response = {
            error: {
                message: processedError.message,
                code: processedError.code,
                status: processedError.statusCode,
                timestamp: processedError.timestamp
            }
        };

        // Add validation errors if present
        if (processedError.errors) {
            response.error.errors = processedError.errors;
        }

        // Add stack trace in development
        if (includeStack && processedError.stack) {
            response.error.stack = processedError.stack;
        }

        // Add correlation ID for tracking
        if (req.correlationId) {
            response.error.correlationId = req.correlationId;
        }

        // Set security headers
        res.setHeader('X-Content-Type-Options', 'nosniff');
        res.setHeader('X-Frame-Options', 'DENY');

        // Send response
        res.status(processedError.statusCode || 500).json(response);
    };
}

/**
 * Async error wrapper for route handlers
 */
function asyncErrorHandler(fn) {
    return (req, res, next) => {
        Promise.resolve(fn(req, res, next)).catch(next);
    };
}

/**
 * 404 handler middleware
 */
function notFoundHandler() {
    return (req, res, next) => {
        const error = new NotFoundError(`Route ${req.originalUrl}`);
        next(error);
    };
}

/**
 * Health check error handler
 */
function healthCheckErrorHandler() {
    return (error, req, res, next) => {
        logger.error('Health check failed', { error: error.message });
        
        res.status(503).json({
            status: 'error',
            message: 'Service unavailable',
            timestamp: new Date().toISOString()
        });
    };
}

module.exports = {
    // Error classes
    AppError,
    ValidationError,
    AuthenticationError,
    AuthorizationError,
    NotFoundError,
    ConflictError,
    RateLimitError,
    IntegrationError,
    DatabaseError,
    CacheError,
    
    // Utilities
    ErrorContext,
    ErrorNotificationService,
    
    // Middleware
    errorHandler,
    asyncErrorHandler,
    notFoundHandler,
    healthCheckErrorHandler
};