/**
 * Monitoring middleware for Express applications
 * Integrates health checks, metrics collection, and performance monitoring
 */

const { healthManager } = require('../health/healthCheck');
const { metricsManager } = require('../health/metrics');
const { logger } = require('../utils/logger');

/**
 * Health check middleware factory
 */
function healthCheckMiddleware(options = {}) {
    const {
        enableDetailedChecks = true,
        enableCaching = true,
        cacheTimeout = 30000, // 30 seconds
        includeMetrics = false
    } = options;

    let cachedResult = null;
    let cacheTimestamp = 0;

    return async (req, res, next) => {
        try {
            // Check cache if enabled
            if (enableCaching && cachedResult && (Date.now() - cacheTimestamp) < cacheTimeout) {
                return res.json(cachedResult);
            }

            // Run health checks
            const healthResult = await healthManager.runAll();
            
            // Add metrics if requested
            if (includeMetrics) {
                healthResult.metrics = metricsManager.getAllMetrics();
            }

            // Determine HTTP status code based on health
            let statusCode = 200;
            if (healthResult.status === 'unhealthy') {
                statusCode = 503; // Service Unavailable
            } else if (healthResult.status === 'degraded') {
                statusCode = 200; // OK but degraded
            }

            // Cache the result
            if (enableCaching) {
                cachedResult = healthResult;
                cacheTimestamp = Date.now();
            }

            // Set response headers
            res.setHeader('Content-Type', 'application/json');
            res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
            
            res.status(statusCode).json(healthResult);
        } catch (error) {
            logger.error('Health check middleware error', { error: error.message });
            
            res.status(500).json({
                status: 'error',
                message: 'Health check failed',
                timestamp: new Date().toISOString(),
                error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
            });
        }
    };
}

/**
 * Readiness check middleware
 */
function readinessCheckMiddleware() {
    return async (req, res, next) => {
        try {
            // Check only essential services for readiness
            const essentialChecks = await healthManager.runAll('essential');
            
            if (essentialChecks.status === 'healthy') {
                res.status(200).json({
                    status: 'ready',
                    timestamp: new Date().toISOString()
                });
            } else {
                res.status(503).json({
                    status: 'not ready',
                    timestamp: new Date().toISOString(),
                    reason: 'Essential services are not healthy'
                });
            }
        } catch (error) {
            logger.error('Readiness check error', { error: error.message });
            res.status(503).json({
                status: 'not ready',
                timestamp: new Date().toISOString(),
                error: error.message
            });
        }
    };
}

/**
 * Liveness check middleware
 */
function livenessCheckMiddleware() {
    return (req, res, next) => {
        // Simple liveness check - if we can respond, we're alive
        res.status(200).json({
            status: 'alive',
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            pid: process.pid
        });
    };
}

/**
 * Metrics collection middleware
 */
function metricsMiddleware() {
    return (req, res, next) => {
        const httpMetrics = metricsManager.getCollector('http');
        
        // Record request start
        httpMetrics.requestStart(req);
        
        // Override res.end to record completion
        const originalEnd = res.end;
        res.end = function(...args) {
            httpMetrics.requestEnd(req, res);
            originalEnd.apply(this, args);
        };
        
        next();
    };
}

/**
 * Prometheus metrics endpoint middleware
 */
function prometheusMetricsMiddleware() {
    return (req, res, next) => {
        try {
            const prometheusOutput = metricsManager.getPrometheusMetrics();
            
            res.setHeader('Content-Type', 'text/plain; version=0.0.4; charset=utf-8');
            res.status(200).send(prometheusOutput);
        } catch (error) {
            logger.error('Prometheus metrics error', { error: error.message });
            res.status(500).send('Error generating metrics');
        }
    };
}

/**
 * Performance monitoring middleware
 */
function performanceMiddleware(options = {}) {
    const {
        slowRequestThreshold = 5000,
        enableMemoryTracking = false,
        sampleRate = 1.0 // Sample 100% of requests by default
    } = options;

    return (req, res, next) => {
        // Skip sampling if not selected
        if (Math.random() > sampleRate) {
            return next();
        }

        const startTime = process.hrtime.bigint();
        const startMemory = enableMemoryTracking ? process.memoryUsage() : null;
        
        // Add performance context to request
        req.performance = {
            startTime,
            startMemory
        };

        // Override res.end to measure performance
        const originalEnd = res.end;
        res.end = function(...args) {
            const endTime = process.hrtime.bigint();
            const duration = Number(endTime - startTime) / 1000000; // Convert to milliseconds
            
            // Log performance data
            const performanceData = {
                method: req.method,
                url: req.originalUrl,
                statusCode: res.statusCode,
                duration,
                userAgent: req.get('User-Agent'),
                contentLength: res.get('Content-Length'),
                correlationId: req.correlationId
            };

            // Add memory usage if tracking enabled
            if (enableMemoryTracking && startMemory) {
                const endMemory = process.memoryUsage();
                performanceData.memoryDelta = {
                    rss: endMemory.rss - startMemory.rss,
                    heapTotal: endMemory.heapTotal - startMemory.heapTotal,
                    heapUsed: endMemory.heapUsed - startMemory.heapUsed
                };
            }

            // Log slow requests
            if (duration > slowRequestThreshold) {
                logger.warn('Slow request detected', performanceData);
            } else {
                logger.debug('Request performance', performanceData);
            }

            // Record in metrics
            const businessMetrics = metricsManager.getCollector('business');
            if (duration > slowRequestThreshold) {
                businessMetrics.increment('slow_requests_total', 1, {
                    method: req.method,
                    route: req.route?.path || 'unknown'
                });
            }

            originalEnd.apply(this, args);
        };

        next();
    };
}

/**
 * System monitoring middleware
 */
function systemMonitoringMiddleware() {
    let lastCheck = 0;
    const checkInterval = 60000; // 1 minute

    return (req, res, next) => {
        const now = Date.now();
        
        // Periodically update system metrics
        if (now - lastCheck > checkInterval) {
            const systemMetrics = metricsManager.getCollector('system');
            systemMetrics.collectSystemMetrics();
            lastCheck = now;
        }

        next();
    };
}

/**
 * Database monitoring middleware
 */
function databaseMonitoringMiddleware(pool) {
    return (req, res, next) => {
        // Update pool metrics
        const dbMetrics = metricsManager.getCollector('database');
        dbMetrics.updatePoolMetrics(pool);
        
        next();
    };
}

/**
 * Error monitoring middleware
 */
function errorMonitoringMiddleware() {
    return (error, req, res, next) => {
        // Record error metrics
        const businessMetrics = metricsManager.getCollector('business');
        businessMetrics.increment('errors_total', 1, {
            statusCode: error.statusCode || 500,
            errorType: error.name || 'UnknownError',
            route: req.route?.path || 'unknown'
        });

        // Log error details
        logger.error('Request error', {
            error: {
                name: error.name,
                message: error.message,
                stack: error.stack,
                statusCode: error.statusCode
            },
            request: {
                method: req.method,
                url: req.originalUrl,
                userAgent: req.get('User-Agent'),
                ip: req.ip,
                correlationId: req.correlationId
            }
        });

        next(error);
    };
}

/**
 * Custom monitoring setup for specific routes
 */
function customMonitoringSetup(app, options = {}) {
    const {
        healthPath = '/health',
        readinessPath = '/health/ready',
        livenessPath = '/health/live',
        metricsPath = '/metrics',
        enableDetailedHealth = true,
        enablePrometheusMetrics = true
    } = options;

    // Health check endpoints
    app.get(healthPath, healthCheckMiddleware({
        enableDetailedChecks: enableDetailedHealth,
        includeMetrics: process.env.NODE_ENV === 'development'
    }));

    app.get(readinessPath, readinessCheckMiddleware());
    app.get(livenessPath, livenessCheckMiddleware());

    // Metrics endpoint
    if (enablePrometheusMetrics) {
        app.get(metricsPath, prometheusMetricsMiddleware());
    }

    // Admin endpoints for debugging
    if (process.env.NODE_ENV === 'development') {
        app.get('/admin/health/checks', (req, res) => {
            res.json({
                registeredChecks: healthManager.getRegisteredChecks(),
                lastCheck: healthManager.lastFullCheck
            });
        });

        app.get('/admin/metrics/raw', (req, res) => {
            res.json(metricsManager.getAllMetrics());
        });

        app.post('/admin/metrics/reset', (req, res) => {
            metricsManager.resetAll();
            res.json({ message: 'Metrics reset successfully' });
        });
    }

    logger.info('Monitoring endpoints configured', {
        healthPath,
        readinessPath,
        livenessPath,
        metricsPath: enablePrometheusMetrics ? metricsPath : 'disabled'
    });
}

/**
 * Initialize monitoring for a service
 */
function initializeMonitoring(app, dependencies = {}, options = {}) {
    const {
        enableHttpMetrics = true,
        enablePerformanceTracking = true,
        enableSystemMonitoring = true,
        enableErrorMonitoring = true
    } = options;

    // Add basic middleware
    if (enableHttpMetrics) {
        app.use(metricsMiddleware());
    }

    if (enablePerformanceTracking) {
        app.use(performanceMiddleware(options.performance));
    }

    if (enableSystemMonitoring) {
        app.use(systemMonitoringMiddleware());
    }

    // Database monitoring if pool provided
    if (dependencies.database) {
        app.use(databaseMonitoringMiddleware(dependencies.database));
    }

    // Error monitoring
    if (enableErrorMonitoring) {
        app.use(errorMonitoringMiddleware());
    }

    // Setup monitoring endpoints
    customMonitoringSetup(app, options.endpoints);

    // Register health checks for dependencies
    if (dependencies.database) {
        const { DatabaseHealthCheck } = require('../health/healthCheck');
        healthManager.register(new DatabaseHealthCheck(dependencies.database));
    }

    if (dependencies.redis) {
        const { RedisHealthCheck } = require('../health/healthCheck');
        healthManager.register(new RedisHealthCheck());
    }

    // Start background health monitoring
    healthManager.startBackgroundMonitoring(60000); // Every minute

    logger.info('Monitoring system initialized', {
        httpMetrics: enableHttpMetrics,
        performance: enablePerformanceTracking,
        system: enableSystemMonitoring,
        errors: enableErrorMonitoring
    });

    return { healthManager, metricsManager };
}

module.exports = {
    healthCheckMiddleware,
    readinessCheckMiddleware,
    livenessCheckMiddleware,
    metricsMiddleware,
    prometheusMetricsMiddleware,
    performanceMiddleware,
    systemMonitoringMiddleware,
    databaseMonitoringMiddleware,
    errorMonitoringMiddleware,
    customMonitoringSetup,
    initializeMonitoring
};