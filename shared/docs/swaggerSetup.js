/**
 * Swagger/OpenAPI documentation setup for Express applications
 * Provides interactive API documentation with authentication and customization
 */

const swaggerJsdoc = require('swagger-jsdoc');
const swaggerUi = require('swagger-ui-express');
const YAML = require('yamljs');
const path = require('path');
const fs = require('fs');
const { logger } = require('../utils/logger');

/**
 * Default Swagger configuration
 */
const defaultSwaggerOptions = {
    definition: {
        openapi: '3.0.3',
        info: {
            title: 'E-commerce Analytics Platform API',
            version: process.env.SERVICE_VERSION || '1.0.0',
            description: 'Comprehensive analytics platform for e-commerce businesses',
            contact: {
                name: 'API Support',
                email: '<EMAIL>',
                url: 'https://docs.ecommerce-analytics.com'
            },
            license: {
                name: 'MIT',
                url: 'https://opensource.org/licenses/MIT'
            }
        },
        servers: [
            {
                url: process.env.API_BASE_URL || 'http://localhost:3001/api/v1',
                description: 'Development server'
            }
        ],
        security: [
            {
                bearerAuth: []
            }
        ],
        components: {
            securitySchemes: {
                bearerAuth: {
                    type: 'http',
                    scheme: 'bearer',
                    bearerFormat: 'JWT'
                }
            }
        }
    },
    apis: []
};

/**
 * Swagger UI customization options
 */
const swaggerUiOptions = {
    customCss: `
        .swagger-ui .topbar { display: none; }
        .swagger-ui .info h1 { color: #2c3e50; }
        .swagger-ui .info .description { font-size: 14px; }
        .swagger-ui .scheme-container { background: #f8f9fa; padding: 10px; }
        .swagger-ui .auth-wrapper { margin: 20px 0; }
        .swagger-ui .btn.authorize { background-color: #007bff; border-color: #007bff; }
        .swagger-ui .btn.authorize:hover { background-color: #0056b3; }
        .swagger-ui .response-col_status { font-weight: bold; }
        .swagger-ui .response.highlighted { background: rgba(0, 123, 255, 0.1); }
    `,
    customSiteTitle: 'E-commerce Analytics API Documentation',
    customfavIcon: '/favicon.ico',
    swaggerOptions: {
        persistAuthorization: true,
        displayRequestDuration: true,
        docExpansion: 'list',
        filter: true,
        showExtensions: true,
        showCommonExtensions: true,
        tryItOutEnabled: true,
        requestInterceptor: (req) => {
            // Add correlation ID to requests
            req.headers['x-correlation-id'] = generateCorrelationId();
            return req;
        },
        responseInterceptor: (res) => {
            // Log API responses for debugging
            if (process.env.NODE_ENV === 'development') {
                console.log('API Response:', res.status, res.url);
            }
            return res;
        }
    }
};

/**
 * Service-specific configurations
 */
const serviceConfigs = {
    analytics: {
        title: 'Analytics Service API',
        description: 'Core analytics and data processing service',
        version: '1.0.0',
        basePath: '/api/v1',
        tags: [
            { name: 'Analytics', description: 'Core analytics operations' },
            { name: 'Cohorts', description: 'Cohort analysis endpoints' },
            { name: 'Attribution', description: 'Attribution modeling' },
            { name: 'Forecasting', description: 'Predictive analytics' }
        ]
    },
    dashboard: {
        title: 'Dashboard Service API',
        description: 'Dashboard and visualization service',
        version: '1.0.0',
        basePath: '/api/v1',
        tags: [
            { name: 'Dashboard', description: 'Dashboard operations' },
            { name: 'Users', description: 'User management' },
            { name: 'Auth', description: 'Authentication' },
            { name: 'Reports', description: 'Report generation' }
        ]
    },
    integration: {
        title: 'Integration Service API',
        description: 'E-commerce platform integration service',
        version: '1.0.0',
        basePath: '/api/v1',
        tags: [
            { name: 'Integrations', description: 'Platform integrations' },
            { name: 'Webhooks', description: 'Webhook management' },
            { name: 'Sync', description: 'Data synchronization' },
            { name: 'Events', description: 'Event processing' }
        ]
    }
};

/**
 * Generate correlation ID for request tracking
 */
function generateCorrelationId() {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Load OpenAPI specification from YAML file
 */
function loadOpenAPISpec(specPath = null) {
    try {
        const yamlPath = specPath || path.join(__dirname, '../../docs/api/openapi.yml');
        
        if (fs.existsSync(yamlPath)) {
            logger.info('Loading OpenAPI specification', { path: yamlPath });
            return YAML.load(yamlPath);
        } else {
            logger.warn('OpenAPI specification file not found, using default config', { path: yamlPath });
            return null;
        }
    } catch (error) {
        logger.error('Failed to load OpenAPI specification', { error: error.message });
        return null;
    }
}

/**
 * Create Swagger specification for a service
 */
function createSwaggerSpec(serviceName, options = {}) {
    const serviceConfig = serviceConfigs[serviceName] || serviceConfigs.analytics;
    const openApiSpec = loadOpenAPISpec(options.specPath);
    
    let swaggerOptions;
    
    if (openApiSpec) {
        // Use loaded OpenAPI specification
        swaggerOptions = {
            ...openApiSpec,
            info: {
                ...openApiSpec.info,
                title: serviceConfig.title,
                version: serviceConfig.version
            }
        };
        
        // Filter paths and components for this service if needed
        if (options.filterByService) {
            swaggerOptions = filterSpecForService(swaggerOptions, serviceName);
        }
    } else {
        // Use JSDoc-based specification
        swaggerOptions = {
            definition: {
                ...defaultSwaggerOptions.definition,
                info: {
                    ...defaultSwaggerOptions.definition.info,
                    title: serviceConfig.title,
                    description: serviceConfig.description,
                    version: serviceConfig.version
                },
                tags: serviceConfig.tags
            },
            apis: options.apis || [
                path.join(__dirname, `../../services/${serviceName}/src/routes/*.js`),
                path.join(__dirname, `../../services/${serviceName}/src/controllers/*.js`),
                path.join(__dirname, `../../services/${serviceName}/src/models/*.js`)
            ]
        };
    }
    
    return swaggerOptions;
}

/**
 * Filter OpenAPI specification for a specific service
 */
function filterSpecForService(spec, serviceName) {
    const serviceEndpoints = {
        analytics: ['/analytics', '/events', '/forecast'],
        dashboard: ['/dashboard', '/auth', '/users', '/reports'],
        integration: ['/integrations', '/webhooks', '/sync']
    };
    
    const allowedPaths = serviceEndpoints[serviceName] || [];
    const filteredPaths = {};
    
    // Filter paths
    Object.keys(spec.paths || {}).forEach(path => {
        if (allowedPaths.some(prefix => path.startsWith(prefix)) || 
            path.startsWith('/health') || 
            path.startsWith('/metrics')) {
            filteredPaths[path] = spec.paths[path];
        }
    });
    
    return {
        ...spec,
        paths: filteredPaths
    };
}

/**
 * Setup Swagger documentation for Express app
 */
function setupSwagger(app, serviceName, options = {}) {
    const {
        swaggerPath = '/docs',
        apiDocsPath = '/api-docs',
        enableAuth = true,
        customizations = {}
    } = options;
    
    try {
        // Create Swagger specification
        const swaggerSpec = createSwaggerSpec(serviceName, options);
        const specs = swaggerJsdoc.specs ? swaggerJsdoc(swaggerSpec) : swaggerSpec;
        
        // Apply customizations
        const uiOptions = {
            ...swaggerUiOptions,
            ...customizations
        };
        
        // Add authentication handler if enabled
        if (enableAuth) {
            uiOptions.swaggerOptions.onComplete = () => {
                // Try to auto-authenticate in development
                if (process.env.NODE_ENV === 'development' && process.env.DEV_JWT_TOKEN) {
                    window.ui.preauthorizeApiKey('bearerAuth', process.env.DEV_JWT_TOKEN);
                }
            };
        }
        
        // Serve Swagger UI
        app.use(swaggerPath, swaggerUi.serve);
        app.get(swaggerPath, swaggerUi.setup(specs, uiOptions));
        
        // Serve raw OpenAPI spec as JSON
        app.get(apiDocsPath, (req, res) => {
            res.setHeader('Content-Type', 'application/json');
            res.send(specs);
        });
        
        // Serve raw OpenAPI spec as YAML
        app.get(`${apiDocsPath}/yaml`, (req, res) => {
            res.setHeader('Content-Type', 'text/yaml');
            res.send(YAML.stringify(specs, 4));
        });
        
        logger.info('Swagger documentation setup completed', {
            service: serviceName,
            docsPath: swaggerPath,
            specPath: apiDocsPath
        });
        
        return { specs, swaggerPath, apiDocsPath };
        
    } catch (error) {
        logger.error('Failed to setup Swagger documentation', {
            service: serviceName,
            error: error.message
        });
        throw error;
    }
}

/**
 * Middleware to validate requests against OpenAPI spec
 */
function createValidationMiddleware(specs) {
    const OpenAPIValidator = require('express-openapi-validator');
    
    return OpenAPIValidator.middleware({
        apiSpec: specs,
        validateRequests: true,
        validateResponses: process.env.NODE_ENV === 'development',
        ignorePaths: /.*\/docs.*/,
        formats: {
            email: true,
            date: true,
            'date-time': true
        }
    });
}

/**
 * Documentation middleware with caching
 */
function createDocsMiddleware(serviceName, options = {}) {
    let cachedSpecs = null;
    let cacheTimestamp = 0;
    const cacheTimeout = 300000; // 5 minutes
    
    return (req, res, next) => {
        // Check if we should refresh cache
        const now = Date.now();
        if (!cachedSpecs || (now - cacheTimestamp) > cacheTimeout) {
            try {
                cachedSpecs = createSwaggerSpec(serviceName, options);
                cacheTimestamp = now;
                logger.debug('Swagger specs cached', { service: serviceName });
            } catch (error) {
                logger.error('Failed to cache Swagger specs', { error: error.message });
                if (!cachedSpecs) {
                    return res.status(500).json({ error: 'Documentation unavailable' });
                }
            }
        }
        
        req.swaggerSpecs = cachedSpecs;
        next();
    };
}

/**
 * Generate API client code
 */
function generateClientCode(specs, language = 'javascript') {
    // This would integrate with OpenAPI Generator to create client SDKs
    // For now, return basic information
    return {
        language,
        baseUrl: specs.servers?.[0]?.url || 'http://localhost:3001',
        authentication: 'Bearer token required',
        endpoints: Object.keys(specs.paths || {}),
        models: Object.keys(specs.components?.schemas || {})
    };
}

/**
 * API documentation health check
 */
function createDocsHealthCheck(serviceName) {
    return async (req, res) => {
        try {
            const specs = createSwaggerSpec(serviceName);
            const pathCount = Object.keys(specs.paths || {}).length;
            const schemaCount = Object.keys(specs.components?.schemas || {}).length;
            
            res.json({
                status: 'healthy',
                service: serviceName,
                documentation: {
                    paths: pathCount,
                    schemas: schemaCount,
                    version: specs.info?.version || '1.0.0'
                },
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            res.status(500).json({
                status: 'unhealthy',
                error: error.message,
                timestamp: new Date().toISOString()
            });
        }
    };
}

/**
 * Setup comprehensive API documentation
 */
function setupComprehensiveDocs(app, serviceName, options = {}) {
    const docsConfig = setupSwagger(app, serviceName, options);
    
    // Add validation middleware if requested
    if (options.enableValidation) {
        app.use(createValidationMiddleware(docsConfig.specs));
    }
    
    // Add docs health check
    app.get('/docs/health', createDocsHealthCheck(serviceName));
    
    // Add client code generation endpoint
    app.get('/docs/client/:language', (req, res) => {
        try {
            const clientCode = generateClientCode(docsConfig.specs, req.params.language);
            res.json(clientCode);
        } catch (error) {
            res.status(400).json({ error: error.message });
        }
    });
    
    // Add API explorer endpoint
    app.get('/docs/explorer', (req, res) => {
        res.json({
            endpoints: Object.keys(docsConfig.specs.paths || {}),
            tags: docsConfig.specs.tags || [],
            version: docsConfig.specs.info?.version || '1.0.0'
        });
    });
    
    return docsConfig;
}

module.exports = {
    setupSwagger,
    setupComprehensiveDocs,
    createSwaggerSpec,
    createValidationMiddleware,
    createDocsMiddleware,
    createDocsHealthCheck,
    generateClientCode,
    loadOpenAPISpec,
    serviceConfigs,
    swaggerUiOptions
};