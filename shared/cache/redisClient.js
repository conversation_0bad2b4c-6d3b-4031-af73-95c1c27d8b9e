/**
 * Redis client configuration and connection management
 * Shared across all services for consistent caching
 */

const Redis = require('ioredis');
const logger = require('../utils/logger');

class RedisClient {
    constructor() {
        this.client = null;
        this.isConnected = false;
        this.retryAttempts = 0;
        this.maxRetryAttempts = parseInt(process.env.REDIS_MAX_RETRY_ATTEMPTS) || 5;
        this.retryDelay = parseInt(process.env.REDIS_RETRY_DELAY_ON_FAILURE) || 1000;
    }

    /**
     * Initialize Redis connection
     */
    async connect() {
        const config = {
            host: process.env.REDIS_HOST || 'localhost',
            port: parseInt(process.env.REDIS_PORT) || 6379,
            password: process.env.REDIS_PASSWORD || undefined,
            db: parseInt(process.env.REDIS_DB) || 0,
            connectTimeout: parseInt(process.env.REDIS_CONNECT_TIMEOUT) || 10000,
            commandTimeout: parseInt(process.env.REDIS_COMMAND_TIMEOUT) || 5000,
            retryDelayOnFailover: 100,
            enableReadyCheck: true,
            maxRetriesPerRequest: 3,
            lazyConnect: true,
            keepAlive: 30000,
            family: 4,
            keyPrefix: process.env.REDIS_KEY_PREFIX || 'ecommerce:',
        };

        // Cluster configuration if running in cluster mode
        if (process.env.REDIS_CLUSTER_NODES) {
            const clusterNodes = process.env.REDIS_CLUSTER_NODES.split(',').map(node => {
                const [host, port] = node.split(':');
                return { host, port: parseInt(port) };
            });

            this.client = new Redis.Cluster(clusterNodes, {
                redisOptions: config,
                enableOfflineQueue: false,
            });
        } else {
            this.client = new Redis(config);
        }

        this._setupEventHandlers();

        try {
            await this.client.connect();
            this.isConnected = true;
            this.retryAttempts = 0;
            logger.info('Redis client connected successfully', {
                host: config.host,
                port: config.port,
                db: config.db
            });
        } catch (error) {
            logger.error('Failed to connect to Redis', { error: error.message });
            throw error;
        }
    }

    /**
     * Setup Redis event handlers
     */
    _setupEventHandlers() {
        this.client.on('connect', () => {
            logger.info('Redis client connecting...');
        });

        this.client.on('ready', () => {
            this.isConnected = true;
            logger.info('Redis client ready');
        });

        this.client.on('error', (error) => {
            this.isConnected = false;
            logger.error('Redis client error', { error: error.message });
        });

        this.client.on('close', () => {
            this.isConnected = false;
            logger.warn('Redis client connection closed');
        });

        this.client.on('reconnecting', (delay) => {
            logger.info('Redis client reconnecting', { delay });
        });

        this.client.on('end', () => {
            this.isConnected = false;
            logger.warn('Redis client connection ended');
        });
    }

    /**
     * Get value by key
     */
    async get(key) {
        try {
            const value = await this.client.get(key);
            if (value === null) return null;
            
            try {
                return JSON.parse(value);
            } catch {
                return value;
            }
        } catch (error) {
            logger.error('Redis GET error', { key, error: error.message });
            throw error;
        }
    }

    /**
     * Set value with optional TTL
     */
    async set(key, value, ttl = null) {
        try {
            const serializedValue = typeof value === 'string' ? value : JSON.stringify(value);
            
            if (ttl) {
                await this.client.setex(key, ttl, serializedValue);
            } else {
                await this.client.set(key, serializedValue);
            }
            
            return true;
        } catch (error) {
            logger.error('Redis SET error', { key, ttl, error: error.message });
            throw error;
        }
    }

    /**
     * Delete key(s)
     */
    async del(key) {
        try {
            const result = await this.client.del(key);
            return result;
        } catch (error) {
            logger.error('Redis DEL error', { key, error: error.message });
            throw error;
        }
    }

    /**
     * Check if key exists
     */
    async exists(key) {
        try {
            const result = await this.client.exists(key);
            return result === 1;
        } catch (error) {
            logger.error('Redis EXISTS error', { key, error: error.message });
            throw error;
        }
    }

    /**
     * Set TTL for existing key
     */
    async expire(key, ttl) {
        try {
            const result = await this.client.expire(key, ttl);
            return result === 1;
        } catch (error) {
            logger.error('Redis EXPIRE error', { key, ttl, error: error.message });
            throw error;
        }
    }

    /**
     * Increment numeric value
     */
    async incr(key) {
        try {
            return await this.client.incr(key);
        } catch (error) {
            logger.error('Redis INCR error', { key, error: error.message });
            throw error;
        }
    }

    /**
     * Increment by specific amount
     */
    async incrby(key, amount) {
        try {
            return await this.client.incrby(key, amount);
        } catch (error) {
            logger.error('Redis INCRBY error', { key, amount, error: error.message });
            throw error;
        }
    }

    /**
     * Hash operations
     */
    async hget(key, field) {
        try {
            const value = await this.client.hget(key, field);
            if (value === null) return null;
            
            try {
                return JSON.parse(value);
            } catch {
                return value;
            }
        } catch (error) {
            logger.error('Redis HGET error', { key, field, error: error.message });
            throw error;
        }
    }

    async hset(key, field, value) {
        try {
            const serializedValue = typeof value === 'string' ? value : JSON.stringify(value);
            return await this.client.hset(key, field, serializedValue);
        } catch (error) {
            logger.error('Redis HSET error', { key, field, error: error.message });
            throw error;
        }
    }

    async hgetall(key) {
        try {
            const hash = await this.client.hgetall(key);
            const result = {};
            
            for (const [field, value] of Object.entries(hash)) {
                try {
                    result[field] = JSON.parse(value);
                } catch {
                    result[field] = value;
                }
            }
            
            return result;
        } catch (error) {
            logger.error('Redis HGETALL error', { key, error: error.message });
            throw error;
        }
    }

    /**
     * List operations
     */
    async lpush(key, ...values) {
        try {
            const serializedValues = values.map(v => 
                typeof v === 'string' ? v : JSON.stringify(v)
            );
            return await this.client.lpush(key, ...serializedValues);
        } catch (error) {
            logger.error('Redis LPUSH error', { key, error: error.message });
            throw error;
        }
    }

    async rpush(key, ...values) {
        try {
            const serializedValues = values.map(v => 
                typeof v === 'string' ? v : JSON.stringify(v)
            );
            return await this.client.rpush(key, ...serializedValues);
        } catch (error) {
            logger.error('Redis RPUSH error', { key, error: error.message });
            throw error;
        }
    }

    async lpop(key) {
        try {
            const value = await this.client.lpop(key);
            if (value === null) return null;
            
            try {
                return JSON.parse(value);
            } catch {
                return value;
            }
        } catch (error) {
            logger.error('Redis LPOP error', { key, error: error.message });
            throw error;
        }
    }

    async lrange(key, start, stop) {
        try {
            const values = await this.client.lrange(key, start, stop);
            return values.map(value => {
                try {
                    return JSON.parse(value);
                } catch {
                    return value;
                }
            });
        } catch (error) {
            logger.error('Redis LRANGE error', { key, start, stop, error: error.message });
            throw error;
        }
    }

    /**
     * Set operations
     */
    async sadd(key, ...members) {
        try {
            const serializedMembers = members.map(m => 
                typeof m === 'string' ? m : JSON.stringify(m)
            );
            return await this.client.sadd(key, ...serializedMembers);
        } catch (error) {
            logger.error('Redis SADD error', { key, error: error.message });
            throw error;
        }
    }

    async smembers(key) {
        try {
            const members = await this.client.smembers(key);
            return members.map(member => {
                try {
                    return JSON.parse(member);
                } catch {
                    return member;
                }
            });
        } catch (error) {
            logger.error('Redis SMEMBERS error', { key, error: error.message });
            throw error;
        }
    }

    /**
     * Get keys by pattern
     */
    async keys(pattern) {
        try {
            return await this.client.keys(pattern);
        } catch (error) {
            logger.error('Redis KEYS error', { pattern, error: error.message });
            throw error;
        }
    }

    /**
     * Flush database
     */
    async flushdb() {
        try {
            return await this.client.flushdb();
        } catch (error) {
            logger.error('Redis FLUSHDB error', { error: error.message });
            throw error;
        }
    }

    /**
     * Get Redis info
     */
    async info(section = 'all') {
        try {
            return await this.client.info(section);
        } catch (error) {
            logger.error('Redis INFO error', { section, error: error.message });
            throw error;
        }
    }

    /**
     * Health check
     */
    async ping() {
        try {
            const result = await this.client.ping();
            return result === 'PONG';
        } catch (error) {
            logger.error('Redis PING error', { error: error.message });
            return false;
        }
    }

    /**
     * Close connection
     */
    async disconnect() {
        if (this.client) {
            await this.client.quit();
            this.isConnected = false;
            logger.info('Redis client disconnected');
        }
    }

    /**
     * Get connection status
     */
    getStatus() {
        return {
            connected: this.isConnected,
            retryAttempts: this.retryAttempts,
            maxRetryAttempts: this.maxRetryAttempts
        };
    }
}

// Create singleton instance
const redisClient = new RedisClient();

module.exports = redisClient;