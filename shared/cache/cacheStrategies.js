/**
 * Cache strategies and utilities for the e-commerce analytics platform
 * Implements various caching patterns and TTL strategies
 */

const redisClient = require('./redisClient');
const logger = require('../utils/logger');

/**
 * Cache TTL constants (in seconds)
 */
const TTL = {
    VERY_SHORT: 60,        // 1 minute
    SHORT: 300,            // 5 minutes
    MEDIUM: 1800,          // 30 minutes
    LONG: 3600,            // 1 hour
    VERY_LONG: 86400,      // 24 hours
    WEEKLY: 604800,        // 7 days
    
    // Analytics-specific TTLs
    REAL_TIME_METRICS: 30,    // 30 seconds
    DASHBOARD_DATA: 300,      // 5 minutes
    COHORT_ANALYSIS: 3600,    // 1 hour
    ATTRIBUTION_DATA: 1800,   // 30 minutes
    FORECAST_DATA: 86400,     // 24 hours
    CUSTOMER_SEGMENTS: 3600,  // 1 hour
};

/**
 * Cache key generators
 */
const CacheKeys = {
    // Analytics cache keys
    dashboardMetrics: (storeId, timeRange) => `dashboard:metrics:${storeId}:${timeRange}`,
    cohortAnalysis: (storeId, period) => `cohort:analysis:${storeId}:${period}`,
    attributionModel: (storeId, model, timeRange) => `attribution:${model}:${storeId}:${timeRange}`,
    forecastData: (storeId, metric, period) => `forecast:${metric}:${storeId}:${period}`,
    customerSegments: (storeId, segmentType) => `segments:${segmentType}:${storeId}`,
    
    // Integration cache keys
    integrationHealth: (integration) => `integration:health:${integration}`,
    webhookData: (source, eventType) => `webhook:${source}:${eventType}`,
    rateLimit: (ip, endpoint) => `rate_limit:${ip}:${endpoint}`,
    
    // Session and auth cache keys
    userSession: (userId) => `session:${userId}`,
    authToken: (tokenId) => `auth:${tokenId}`,
    refreshToken: (userId) => `refresh:${userId}`,
    
    // Data processing cache keys
    processingQueue: (queueName) => `queue:${queueName}`,
    batchJob: (jobId) => `batch:${jobId}`,
    exportJob: (jobId) => `export:${jobId}`,
};

/**
 * Cache-aside pattern implementation
 */
class CacheAside {
    /**
     * Get data with cache-aside pattern
     * @param {string} key - Cache key
     * @param {Function} fetchFunction - Function to fetch data if not in cache
     * @param {number} ttl - TTL in seconds
     * @param {Object} options - Additional options
     */
    static async get(key, fetchFunction, ttl = TTL.MEDIUM, options = {}) {
        const { forceRefresh = false, timeout = 30000 } = options;
        
        try {
            // Check if force refresh is requested
            if (!forceRefresh) {
                const cachedData = await redisClient.get(key);
                if (cachedData !== null) {
                    logger.debug('Cache hit', { key });
                    return cachedData;
                }
            }
            
            logger.debug('Cache miss, fetching fresh data', { key });
            
            // Fetch fresh data with timeout
            const fetchPromise = Promise.resolve(fetchFunction());
            const timeoutPromise = new Promise((_, reject) => 
                setTimeout(() => reject(new Error('Fetch timeout')), timeout)
            );
            
            const freshData = await Promise.race([fetchPromise, timeoutPromise]);
            
            // Cache the result
            await redisClient.set(key, freshData, ttl);
            
            return freshData;
        } catch (error) {
            logger.error('Cache-aside error', { key, error: error.message });
            
            // Try to return stale data if available
            try {
                const staleData = await redisClient.get(key);
                if (staleData !== null) {
                    logger.warn('Returning stale data due to fetch error', { key });
                    return staleData;
                }
            } catch (staleError) {
                logger.error('Failed to get stale data', { key, error: staleError.message });
            }
            
            throw error;
        }
    }
    
    /**
     * Set data in cache
     */
    static async set(key, data, ttl = TTL.MEDIUM) {
        try {
            await redisClient.set(key, data, ttl);
            logger.debug('Data cached', { key, ttl });
        } catch (error) {
            logger.error('Cache set error', { key, error: error.message });
        }
    }
    
    /**
     * Delete data from cache
     */
    static async delete(key) {
        try {
            await redisClient.del(key);
            logger.debug('Cache invalidated', { key });
        } catch (error) {
            logger.error('Cache delete error', { key, error: error.message });
        }
    }
}

/**
 * Write-through pattern implementation
 */
class WriteThrough {
    /**
     * Write data to both cache and database
     */
    static async write(key, data, writeFunction, ttl = TTL.MEDIUM) {
        try {
            // Write to database first
            const result = await writeFunction(data);
            
            // Then write to cache
            await redisClient.set(key, result, ttl);
            
            logger.debug('Write-through completed', { key });
            return result;
        } catch (error) {
            logger.error('Write-through error', { key, error: error.message });
            throw error;
        }
    }
}

/**
 * Write-behind (write-back) pattern implementation
 */
class WriteBehind {
    constructor() {
        this.writeQueue = [];
        this.batchSize = 10;
        this.flushInterval = 5000; // 5 seconds
        this.isProcessing = false;
        
        // Start background processing
        this.startBackgroundProcessing();
    }
    
    /**
     * Queue write operation
     */
    async queueWrite(key, data, writeFunction, ttl = TTL.MEDIUM) {
        try {
            // Write to cache immediately
            await redisClient.set(key, data, ttl);
            
            // Queue database write
            this.writeQueue.push({
                key,
                data,
                writeFunction,
                timestamp: Date.now()
            });
            
            logger.debug('Write queued for background processing', { key });
        } catch (error) {
            logger.error('Write-behind queue error', { key, error: error.message });
            throw error;
        }
    }
    
    /**
     * Start background processing of write queue
     */
    startBackgroundProcessing() {
        setInterval(async () => {
            if (this.isProcessing || this.writeQueue.length === 0) {
                return;
            }
            
            await this.flushWrites();
        }, this.flushInterval);
    }
    
    /**
     * Flush pending writes to database
     */
    async flushWrites() {
        if (this.isProcessing) return;
        
        this.isProcessing = true;
        
        try {
            const batch = this.writeQueue.splice(0, this.batchSize);
            
            if (batch.length === 0) {
                this.isProcessing = false;
                return;
            }
            
            logger.debug('Flushing write batch', { count: batch.length });
            
            // Process writes in parallel
            const writePromises = batch.map(async ({ key, data, writeFunction }) => {
                try {
                    await writeFunction(data);
                    logger.debug('Background write completed', { key });
                } catch (error) {
                    logger.error('Background write failed', { key, error: error.message });
                    // Could implement retry logic here
                }
            });
            
            await Promise.allSettled(writePromises);
        } catch (error) {
            logger.error('Write batch flush error', { error: error.message });
        } finally {
            this.isProcessing = false;
        }
    }
}

/**
 * Cache warming utilities
 */
class CacheWarming {
    /**
     * Warm up cache with frequently accessed data
     */
    static async warmCache(warmingConfig) {
        logger.info('Starting cache warming', { configCount: warmingConfig.length });
        
        const warmingPromises = warmingConfig.map(async (config) => {
            try {
                const { key, fetchFunction, ttl } = config;
                const data = await fetchFunction();
                await redisClient.set(key, data, ttl);
                logger.debug('Cache warmed', { key });
            } catch (error) {
                logger.error('Cache warming failed', { 
                    key: config.key, 
                    error: error.message 
                });
            }
        });
        
        await Promise.allSettled(warmingPromises);
        logger.info('Cache warming completed');
    }
    
    /**
     * Preload dashboard data for active stores
     */
    static async warmDashboardCache(storeIds, timeRanges) {
        const warmingConfig = [];
        
        storeIds.forEach(storeId => {
            timeRanges.forEach(timeRange => {
                warmingConfig.push({
                    key: CacheKeys.dashboardMetrics(storeId, timeRange),
                    fetchFunction: () => {
                        // This would be replaced with actual dashboard data fetching
                        return Promise.resolve({ storeId, timeRange, data: {} });
                    },
                    ttl: TTL.DASHBOARD_DATA
                });
            });
        });
        
        await this.warmCache(warmingConfig);
    }
}

/**
 * Cache invalidation utilities
 */
class CacheInvalidation {
    /**
     * Invalidate cache by pattern
     */
    static async invalidateByPattern(pattern) {
        try {
            const keys = await redisClient.keys(pattern);
            if (keys.length > 0) {
                await redisClient.del(...keys);
                logger.info('Cache invalidated by pattern', { pattern, keyCount: keys.length });
            }
        } catch (error) {
            logger.error('Pattern invalidation error', { pattern, error: error.message });
        }
    }
    
    /**
     * Invalidate store-related cache when data changes
     */
    static async invalidateStoreCache(storeId) {
        const patterns = [
            `dashboard:metrics:${storeId}:*`,
            `cohort:analysis:${storeId}:*`,
            `attribution:*:${storeId}:*`,
            `segments:*:${storeId}`,
        ];
        
        for (const pattern of patterns) {
            await this.invalidateByPattern(pattern);
        }
    }
    
    /**
     * Smart invalidation based on data type
     */
    static async smartInvalidate(dataType, identifier) {
        const invalidationMap = {
            'order': (id) => this.invalidateStoreCache(id),
            'customer': (id) => this.invalidateByPattern(`segments:*:${id}`),
            'product': (id) => this.invalidateByPattern(`*:${id}:*`),
        };
        
        const invalidationFunction = invalidationMap[dataType];
        if (invalidationFunction) {
            await invalidationFunction(identifier);
        }
    }
}

/**
 * Performance monitoring for cache
 */
class CacheMetrics {
    constructor() {
        this.metrics = {
            hits: 0,
            misses: 0,
            errors: 0,
            avgResponseTime: 0
        };
    }
    
    recordHit() {
        this.metrics.hits++;
    }
    
    recordMiss() {
        this.metrics.misses++;
    }
    
    recordError() {
        this.metrics.errors++;
    }
    
    getHitRate() {
        const total = this.metrics.hits + this.metrics.misses;
        return total > 0 ? (this.metrics.hits / total) * 100 : 0;
    }
    
    getMetrics() {
        return {
            ...this.metrics,
            hitRate: this.getHitRate()
        };
    }
    
    reset() {
        this.metrics = {
            hits: 0,
            misses: 0,
            errors: 0,
            avgResponseTime: 0
        };
    }
}

// Create singleton instances
const writeBehind = new WriteBehind();
const cacheMetrics = new CacheMetrics();

module.exports = {
    TTL,
    CacheKeys,
    CacheAside,
    WriteThrough,
    WriteBehind: writeBehind,
    CacheWarming,
    CacheInvalidation,
    CacheMetrics: cacheMetrics,
};