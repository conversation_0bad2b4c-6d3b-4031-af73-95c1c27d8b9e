/**
 * Cache middleware for Express applications
 * Provides caching functionality for API endpoints
 */

const { CacheAside, TTL, CacheKeys, CacheMetrics } = require('./cacheStrategies');
const logger = require('../utils/logger');

/**
 * Generic cache middleware
 */
function cacheMiddleware(options = {}) {
    const {
        ttl = TTL.MEDIUM,
        keyGenerator = (req) => `${req.method}:${req.originalUrl}`,
        condition = () => true,
        skipCache = (req) => false,
        varyBy = []
    } = options;

    return async (req, res, next) => {
        // Skip caching if condition not met
        if (!condition(req) || skipCache(req)) {
            return next();
        }

        // Generate cache key
        let cacheKey;
        try {
            cacheKey = typeof keyGenerator === 'function' 
                ? keyGenerator(req) 
                : keyGenerator;
            
            // Add vary parameters to key
            if (varyBy.length > 0) {
                const varyValues = varyBy.map(param => req.query[param] || req.headers[param] || '').join(':');
                cacheKey += `:${varyValues}`;
            }
        } catch (error) {
            logger.error('Cache key generation error', { error: error.message });
            return next();
        }

        try {
            // Check cache
            const startTime = Date.now();
            const cachedResponse = await CacheAside.get(
                cacheKey,
                null, // No fetch function - just check cache
                ttl,
                { forceRefresh: true } // Skip fetch, just check cache
            );

            if (cachedResponse) {
                CacheMetrics.recordHit();
                logger.debug('Cache hit for request', { 
                    cacheKey, 
                    responseTime: Date.now() - startTime 
                });
                
                // Set cache headers
                res.setHeader('X-Cache', 'HIT');
                res.setHeader('X-Cache-Key', cacheKey);
                
                return res.json(cachedResponse);
            }

            CacheMetrics.recordMiss();
            logger.debug('Cache miss for request', { cacheKey });

            // Intercept response to cache it
            const originalJson = res.json;
            res.json = function(data) {
                // Cache the response data
                CacheAside.set(cacheKey, data, ttl).catch(error => {
                    logger.error('Failed to cache response', { 
                        cacheKey, 
                        error: error.message 
                    });
                });

                // Set cache headers
                res.setHeader('X-Cache', 'MISS');
                res.setHeader('X-Cache-Key', cacheKey);

                // Call original json method
                originalJson.call(this, data);
            };

            next();
        } catch (error) {
            CacheMetrics.recordError();
            logger.error('Cache middleware error', { 
                cacheKey, 
                error: error.message 
            });
            next();
        }
    };
}

/**
 * Dashboard metrics cache middleware
 */
function dashboardCacheMiddleware(ttl = TTL.DASHBOARD_DATA) {
    return cacheMiddleware({
        ttl,
        keyGenerator: (req) => CacheKeys.dashboardMetrics(
            req.params.storeId || req.query.storeId,
            req.query.timeRange || 'last_30_days'
        ),
        condition: (req) => req.method === 'GET',
        varyBy: ['timeRange', 'metrics', 'currency']
    });
}

/**
 * Cohort analysis cache middleware
 */
function cohortCacheMiddleware(ttl = TTL.COHORT_ANALYSIS) {
    return cacheMiddleware({
        ttl,
        keyGenerator: (req) => CacheKeys.cohortAnalysis(
            req.params.storeId || req.query.storeId,
            req.query.period || 'monthly'
        ),
        condition: (req) => req.method === 'GET',
        varyBy: ['period', 'metric', 'segments']
    });
}

/**
 * Attribution model cache middleware
 */
function attributionCacheMiddleware(ttl = TTL.ATTRIBUTION_DATA) {
    return cacheMiddleware({
        ttl,
        keyGenerator: (req) => CacheKeys.attributionModel(
            req.params.storeId || req.query.storeId,
            req.params.model || req.query.model,
            req.query.timeRange || 'last_30_days'
        ),
        condition: (req) => req.method === 'GET',
        varyBy: ['timeRange', 'channels', 'currency']
    });
}

/**
 * Rate limiting cache middleware
 */
function rateLimitMiddleware(options = {}) {
    const {
        windowMs = 900000, // 15 minutes
        maxRequests = 100,
        keyGenerator = (req) => req.ip,
        skipIf = () => false,
        onLimitReached = null
    } = options;

    const windowSeconds = Math.floor(windowMs / 1000);

    return async (req, res, next) => {
        if (skipIf(req)) {
            return next();
        }

        try {
            const rateLimitKey = CacheKeys.rateLimit(keyGenerator(req), req.route?.path || req.path);
            
            // Increment counter
            const currentCount = await CacheAside.get(
                rateLimitKey,
                async () => {
                    // Initialize counter
                    await CacheAside.set(rateLimitKey, 1, windowSeconds);
                    return 1;
                },
                windowSeconds
            );

            if (currentCount > maxRequests) {
                if (onLimitReached) {
                    onLimitReached(req, res);
                }

                logger.warn('Rate limit exceeded', { 
                    key: rateLimitKey, 
                    count: currentCount,
                    limit: maxRequests
                });

                return res.status(429).json({
                    error: 'Too many requests',
                    retryAfter: windowSeconds
                });
            }

            // Update counter if not first request
            if (currentCount > 1) {
                await CacheAside.set(rateLimitKey, currentCount + 1, windowSeconds);
            }

            // Set rate limit headers
            res.setHeader('X-RateLimit-Limit', maxRequests);
            res.setHeader('X-RateLimit-Remaining', Math.max(0, maxRequests - currentCount));
            res.setHeader('X-RateLimit-Reset', new Date(Date.now() + windowMs).toISOString());

            next();
        } catch (error) {
            logger.error('Rate limit middleware error', { error: error.message });
            next(); // Continue on cache error
        }
    };
}

/**
 * Session cache middleware
 */
function sessionCacheMiddleware() {
    return async (req, res, next) => {
        const sessionId = req.headers['x-session-id'] || req.cookies?.sessionId;
        
        if (!sessionId) {
            return next();
        }

        try {
            const sessionKey = CacheKeys.userSession(sessionId);
            const sessionData = await CacheAside.get(sessionKey, null, TTL.LONG);
            
            if (sessionData) {
                req.session = sessionData;
                req.sessionId = sessionId;
                
                // Extend session TTL
                await CacheAside.set(sessionKey, sessionData, TTL.LONG);
            }
        } catch (error) {
            logger.error('Session cache middleware error', { error: error.message });
        }

        next();
    };
}

/**
 * Cache invalidation middleware
 */
function cacheInvalidationMiddleware(options = {}) {
    const {
        patterns = [],
        condition = (req) => ['POST', 'PUT', 'PATCH', 'DELETE'].includes(req.method),
        afterResponse = true
    } = options;

    return async (req, res, next) => {
        if (!condition(req)) {
            return next();
        }

        if (afterResponse) {
            // Invalidate after response
            const originalEnd = res.end;
            res.end = function(...args) {
                originalEnd.apply(this, args);
                
                // Invalidate cache patterns
                patterns.forEach(async (pattern) => {
                    try {
                        const resolvedPattern = typeof pattern === 'function' 
                            ? pattern(req) 
                            : pattern;
                        await CacheInvalidation.invalidateByPattern(resolvedPattern);
                    } catch (error) {
                        logger.error('Cache invalidation error', { 
                            pattern, 
                            error: error.message 
                        });
                    }
                });
            };
        }

        next();
    };
}

/**
 * Cache health check middleware
 */
function cacheHealthMiddleware() {
    return async (req, res, next) => {
        try {
            const redisClient = require('./redisClient');
            const isHealthy = await redisClient.ping();
            
            if (!isHealthy) {
                logger.error('Cache health check failed');
                return res.status(503).json({
                    error: 'Cache service unavailable'
                });
            }

            req.cacheHealth = {
                redis: { status: 'healthy' },
                metrics: CacheMetrics.getMetrics()
            };
        } catch (error) {
            logger.error('Cache health check error', { error: error.message });
            req.cacheHealth = {
                redis: { status: 'unhealthy', error: error.message },
                metrics: CacheMetrics.getMetrics()
            };
        }

        next();
    };
}

module.exports = {
    cacheMiddleware,
    dashboardCacheMiddleware,
    cohortCacheMiddleware,
    attributionCacheMiddleware,
    rateLimitMiddleware,
    sessionCacheMiddleware,
    cacheInvalidationMiddleware,
    cacheHealthMiddleware,
};