/**
 * Application metrics collection and monitoring
 * Provides performance metrics, business metrics, and system metrics
 */

const { logger } = require('../utils/logger');

/**
 * Metrics collector base class
 */
class MetricsCollector {
    constructor() {
        this.metrics = new Map();
        this.startTime = Date.now();
        this.resetInterval = null;
    }

    /**
     * Initialize a metric
     */
    initMetric(name, type, description, initialValue = 0) {
        this.metrics.set(name, {
            name,
            type,
            description,
            value: initialValue,
            lastUpdated: new Date().toISOString(),
            metadata: {}
        });
    }

    /**
     * Set a metric value
     */
    set(name, value, metadata = {}) {
        if (!this.metrics.has(name)) {
            this.initMetric(name, 'gauge', 'Auto-created metric');
        }

        const metric = this.metrics.get(name);
        metric.value = value;
        metric.lastUpdated = new Date().toISOString();
        metric.metadata = { ...metric.metadata, ...metadata };
    }

    /**
     * Increment a metric
     */
    increment(name, amount = 1, metadata = {}) {
        if (!this.metrics.has(name)) {
            this.initMetric(name, 'counter', 'Auto-created counter');
        }

        const metric = this.metrics.get(name);
        metric.value += amount;
        metric.lastUpdated = new Date().toISOString();
        metric.metadata = { ...metric.metadata, ...metadata };
    }

    /**
     * Decrement a metric
     */
    decrement(name, amount = 1, metadata = {}) {
        this.increment(name, -amount, metadata);
    }

    /**
     * Record a timing metric
     */
    timing(name, duration, metadata = {}) {
        if (!this.metrics.has(name)) {
            this.initMetric(name, 'histogram', 'Auto-created timing metric');
        }

        const metric = this.metrics.get(name);
        
        // Simple histogram implementation
        if (!metric.histogram) {
            metric.histogram = {
                count: 0,
                sum: 0,
                min: Infinity,
                max: 0,
                buckets: new Map()
            };
        }

        const hist = metric.histogram;
        hist.count++;
        hist.sum += duration;
        hist.min = Math.min(hist.min, duration);
        hist.max = Math.max(hist.max, duration);

        // Add to buckets
        const bucket = this.getBucket(duration);
        hist.buckets.set(bucket, (hist.buckets.get(bucket) || 0) + 1);

        metric.value = hist.sum / hist.count; // Average
        metric.lastUpdated = new Date().toISOString();
        metric.metadata = { ...metric.metadata, ...metadata };
    }

    /**
     * Get bucket for histogram
     */
    getBucket(value) {
        const buckets = [1, 5, 10, 25, 50, 100, 250, 500, 1000, 2500, 5000, 10000];
        for (const bucket of buckets) {
            if (value <= bucket) return bucket;
        }
        return 'inf';
    }

    /**
     * Get a metric value
     */
    get(name) {
        return this.metrics.get(name);
    }

    /**
     * Get all metrics
     */
    getAll() {
        const result = {};
        for (const [name, metric] of this.metrics) {
            result[name] = {
                ...metric,
                uptime: Date.now() - this.startTime
            };
        }
        return result;
    }

    /**
     * Reset metrics
     */
    reset() {
        this.metrics.clear();
        this.startTime = Date.now();
    }

    /**
     * Start periodic reset
     */
    startPeriodicReset(intervalMs = 3600000) { // 1 hour
        if (this.resetInterval) {
            clearInterval(this.resetInterval);
        }

        this.resetInterval = setInterval(() => {
            logger.debug('Resetting metrics');
            this.reset();
        }, intervalMs);
    }

    /**
     * Stop periodic reset
     */
    stopPeriodicReset() {
        if (this.resetInterval) {
            clearInterval(this.resetInterval);
            this.resetInterval = null;
        }
    }
}

/**
 * System metrics collector
 */
class SystemMetrics extends MetricsCollector {
    constructor() {
        super();
        this.initializeSystemMetrics();
        this.startSystemMonitoring();
    }

    initializeSystemMetrics() {
        // CPU metrics
        this.initMetric('cpu_usage_percent', 'gauge', 'CPU usage percentage');
        this.initMetric('load_average_1m', 'gauge', '1 minute load average');
        this.initMetric('load_average_5m', 'gauge', '5 minute load average');
        this.initMetric('load_average_15m', 'gauge', '15 minute load average');

        // Memory metrics
        this.initMetric('memory_total_bytes', 'gauge', 'Total system memory');
        this.initMetric('memory_used_bytes', 'gauge', 'Used system memory');
        this.initMetric('memory_free_bytes', 'gauge', 'Free system memory');
        this.initMetric('memory_usage_percent', 'gauge', 'Memory usage percentage');

        // Process metrics
        this.initMetric('process_memory_rss_bytes', 'gauge', 'Process RSS memory');
        this.initMetric('process_memory_heap_total_bytes', 'gauge', 'Process heap total');
        this.initMetric('process_memory_heap_used_bytes', 'gauge', 'Process heap used');
        this.initMetric('process_uptime_seconds', 'gauge', 'Process uptime');

        // GC metrics
        this.initMetric('gc_duration_ms', 'histogram', 'Garbage collection duration');
        this.initMetric('gc_count', 'counter', 'Garbage collection count');
    }

    startSystemMonitoring() {
        // Monitor system metrics every 30 seconds
        setInterval(() => {
            this.collectSystemMetrics();
        }, 30000);

        // Monitor GC if available
        if (global.gc) {
            const v8 = require('v8');
            setInterval(() => {
                const stats = v8.getHeapStatistics();
                this.set('heap_size_limit', stats.heap_size_limit);
                this.set('total_heap_size', stats.total_heap_size);
                this.set('used_heap_size', stats.used_heap_size);
            }, 60000);
        }
    }

    collectSystemMetrics() {
        const os = require('os');
        const process = require('process');

        try {
            // CPU metrics
            const cpus = os.cpus();
            let totalIdle = 0;
            let totalTick = 0;

            cpus.forEach(cpu => {
                for (const type in cpu.times) {
                    totalTick += cpu.times[type];
                }
                totalIdle += cpu.times.idle;
            });

            const idle = totalIdle / cpus.length;
            const total = totalTick / cpus.length;
            const cpuUsage = 100 - ~~(100 * idle / total);

            this.set('cpu_usage_percent', cpuUsage);

            // Load average
            const loadAvg = os.loadavg();
            this.set('load_average_1m', loadAvg[0]);
            this.set('load_average_5m', loadAvg[1]);
            this.set('load_average_15m', loadAvg[2]);

            // Memory metrics
            const totalMem = os.totalmem();
            const freeMem = os.freemem();
            const usedMem = totalMem - freeMem;
            const memUsagePercent = (usedMem / totalMem) * 100;

            this.set('memory_total_bytes', totalMem);
            this.set('memory_used_bytes', usedMem);
            this.set('memory_free_bytes', freeMem);
            this.set('memory_usage_percent', memUsagePercent);

            // Process metrics
            const memUsage = process.memoryUsage();
            this.set('process_memory_rss_bytes', memUsage.rss);
            this.set('process_memory_heap_total_bytes', memUsage.heapTotal);
            this.set('process_memory_heap_used_bytes', memUsage.heapUsed);
            this.set('process_uptime_seconds', process.uptime());

        } catch (error) {
            logger.error('Error collecting system metrics', { error: error.message });
        }
    }
}

/**
 * HTTP metrics collector
 */
class HttpMetrics extends MetricsCollector {
    constructor() {
        super();
        this.initializeHttpMetrics();
    }

    initializeHttpMetrics() {
        // Request metrics
        this.initMetric('http_requests_total', 'counter', 'Total HTTP requests');
        this.initMetric('http_request_duration_ms', 'histogram', 'HTTP request duration');
        this.initMetric('http_requests_in_progress', 'gauge', 'HTTP requests in progress');

        // Response metrics by status code
        [200, 201, 400, 401, 403, 404, 429, 500, 502, 503].forEach(code => {
            this.initMetric(`http_requests_${code}_total`, 'counter', `HTTP ${code} responses`);
        });

        // Route-specific metrics
        this.routeMetrics = new Map();
    }

    /**
     * Record HTTP request start
     */
    requestStart(req) {
        this.increment('http_requests_total');
        this.increment('http_requests_in_progress');
        
        req.startTime = Date.now();
    }

    /**
     * Record HTTP request end
     */
    requestEnd(req, res) {
        if (!req.startTime) return;

        const duration = Date.now() - req.startTime;
        const route = this.normalizeRoute(req.route?.path || req.path);
        const method = req.method;
        const statusCode = res.statusCode;

        // General metrics
        this.decrement('http_requests_in_progress');
        this.timing('http_request_duration_ms', duration, {
            method,
            route,
            statusCode
        });

        // Status code metrics
        this.increment(`http_requests_${statusCode}_total`);

        // Route-specific metrics
        const routeKey = `${method}_${route}`;
        if (!this.routeMetrics.has(routeKey)) {
            this.routeMetrics.set(routeKey, {
                requests: 0,
                totalDuration: 0,
                minDuration: Infinity,
                maxDuration: 0,
                errors: 0
            });
        }

        const routeMetric = this.routeMetrics.get(routeKey);
        routeMetric.requests++;
        routeMetric.totalDuration += duration;
        routeMetric.minDuration = Math.min(routeMetric.minDuration, duration);
        routeMetric.maxDuration = Math.max(routeMetric.maxDuration, duration);

        if (statusCode >= 400) {
            routeMetric.errors++;
        }

        // Log slow requests
        if (duration > 5000) {
            logger.warn('Slow HTTP request', {
                method,
                route,
                duration,
                statusCode,
                userAgent: req.get('User-Agent'),
                ip: req.ip
            });
        }
    }

    /**
     * Normalize route for consistent metrics
     */
    normalizeRoute(route) {
        if (!route) return 'unknown';
        
        // Replace route parameters with placeholders
        return route
            .replace(/\/:\w+/g, '/:id')
            .replace(/\/\d+/g, '/:id')
            .replace(/\/[a-f0-9-]{36}/g, '/:uuid')
            .replace(/\/[a-f0-9]{24}/g, '/:objectid');
    }

    /**
     * Get route metrics
     */
    getRouteMetrics() {
        const result = {};
        for (const [route, metrics] of this.routeMetrics) {
            result[route] = {
                ...metrics,
                avgDuration: metrics.requests > 0 ? metrics.totalDuration / metrics.requests : 0,
                errorRate: metrics.requests > 0 ? (metrics.errors / metrics.requests) * 100 : 0
            };
        }
        return result;
    }
}

/**
 * Database metrics collector
 */
class DatabaseMetrics extends MetricsCollector {
    constructor() {
        super();
        this.initializeDatabaseMetrics();
    }

    initializeDatabaseMetrics() {
        // Query metrics
        this.initMetric('db_queries_total', 'counter', 'Total database queries');
        this.initMetric('db_query_duration_ms', 'histogram', 'Database query duration');
        this.initMetric('db_queries_in_progress', 'gauge', 'Database queries in progress');
        
        // Connection pool metrics
        this.initMetric('db_connections_total', 'gauge', 'Total database connections');
        this.initMetric('db_connections_idle', 'gauge', 'Idle database connections');
        this.initMetric('db_connections_waiting', 'gauge', 'Waiting database connections');
        
        // Error metrics
        this.initMetric('db_errors_total', 'counter', 'Total database errors');
        this.initMetric('db_timeouts_total', 'counter', 'Total database timeouts');
    }

    /**
     * Record database query start
     */
    queryStart(query) {
        this.increment('db_queries_total');
        this.increment('db_queries_in_progress');
        
        return {
            startTime: Date.now(),
            query: query.replace(/\s+/g, ' ').trim()
        };
    }

    /**
     * Record database query end
     */
    queryEnd(context, error = null) {
        if (!context?.startTime) return;

        const duration = Date.now() - context.startTime;
        const queryType = context.query.split(' ')[0].toUpperCase();

        this.decrement('db_queries_in_progress');
        this.timing('db_query_duration_ms', duration, {
            queryType,
            error: !!error
        });

        if (error) {
            this.increment('db_errors_total');
            
            if (error.code === 'ETIMEDOUT' || error.message.includes('timeout')) {
                this.increment('db_timeouts_total');
            }
        }

        // Log slow queries
        if (duration > 1000) {
            logger.warn('Slow database query', {
                query: context.query,
                duration,
                queryType,
                error: error?.message
            });
        }
    }

    /**
     * Update connection pool metrics
     */
    updatePoolMetrics(pool) {
        this.set('db_connections_total', pool.totalCount || 0);
        this.set('db_connections_idle', pool.idleCount || 0);
        this.set('db_connections_waiting', pool.waitingCount || 0);
    }
}

/**
 * Business metrics collector
 */
class BusinessMetrics extends MetricsCollector {
    constructor() {
        super();
        this.initializeBusinessMetrics();
    }

    initializeBusinessMetrics() {
        // Analytics metrics
        this.initMetric('analytics_events_processed', 'counter', 'Analytics events processed');
        this.initMetric('analytics_errors', 'counter', 'Analytics processing errors');
        this.initMetric('analytics_processing_time_ms', 'histogram', 'Analytics processing time');
        
        // Integration metrics
        this.initMetric('integration_webhooks_received', 'counter', 'Webhooks received');
        this.initMetric('integration_api_calls', 'counter', 'External API calls');
        this.initMetric('integration_failures', 'counter', 'Integration failures');
        
        // Cache metrics
        this.initMetric('cache_hits', 'counter', 'Cache hits');
        this.initMetric('cache_misses', 'counter', 'Cache misses');
        this.initMetric('cache_errors', 'counter', 'Cache errors');
        
        // User metrics
        this.initMetric('active_users', 'gauge', 'Currently active users');
        this.initMetric('user_sessions', 'gauge', 'Active user sessions');
    }

    /**
     * Record analytics event
     */
    recordAnalyticsEvent(eventType, processingTime = 0, error = null) {
        this.increment('analytics_events_processed', 1, { eventType });
        
        if (processingTime > 0) {
            this.timing('analytics_processing_time_ms', processingTime, { eventType });
        }
        
        if (error) {
            this.increment('analytics_errors', 1, { eventType, error: error.message });
        }
    }

    /**
     * Record integration activity
     */
    recordIntegration(type, action, success = true, responseTime = 0) {
        if (action === 'webhook') {
            this.increment('integration_webhooks_received', 1, { type });
        } else {
            this.increment('integration_api_calls', 1, { type, action });
        }
        
        if (!success) {
            this.increment('integration_failures', 1, { type, action });
        }
        
        if (responseTime > 0) {
            this.timing(`integration_${action}_time_ms`, responseTime, { type });
        }
    }

    /**
     * Record cache activity
     */
    recordCacheActivity(operation, hit = false, error = null) {
        if (error) {
            this.increment('cache_errors', 1, { operation });
        } else if (hit) {
            this.increment('cache_hits', 1, { operation });
        } else {
            this.increment('cache_misses', 1, { operation });
        }
    }

    /**
     * Update user metrics
     */
    updateUserMetrics(activeUsers, activeSessions) {
        this.set('active_users', activeUsers);
        this.set('user_sessions', activeSessions);
    }
}

/**
 * Main metrics manager
 */
class MetricsManager {
    constructor() {
        this.collectors = {
            system: new SystemMetrics(),
            http: new HttpMetrics(),
            database: new DatabaseMetrics(),
            business: new BusinessMetrics()
        };
    }

    /**
     * Get all metrics
     */
    getAllMetrics() {
        const result = {};
        for (const [name, collector] of Object.entries(this.collectors)) {
            result[name] = collector.getAll();
        }
        return result;
    }

    /**
     * Get specific collector
     */
    getCollector(name) {
        return this.collectors[name];
    }

    /**
     * Get metrics in Prometheus format
     */
    getPrometheusMetrics() {
        const lines = [];
        
        for (const [collectorName, collector] of Object.entries(this.collectors)) {
            const metrics = collector.getAll();
            
            for (const [metricName, metric] of Object.entries(metrics)) {
                const fullName = `${collectorName}_${metricName}`;
                
                lines.push(`# HELP ${fullName} ${metric.description}`);
                lines.push(`# TYPE ${fullName} ${metric.type}`);
                
                if (metric.histogram) {
                    // Histogram metrics
                    const hist = metric.histogram;
                    lines.push(`${fullName}_count ${hist.count}`);
                    lines.push(`${fullName}_sum ${hist.sum}`);
                    
                    for (const [bucket, count] of hist.buckets) {
                        lines.push(`${fullName}_bucket{le="${bucket}"} ${count}`);
                    }
                } else {
                    lines.push(`${fullName} ${metric.value}`);
                }
                
                lines.push('');
            }
        }
        
        return lines.join('\n');
    }

    /**
     * Reset all metrics
     */
    resetAll() {
        for (const collector of Object.values(this.collectors)) {
            collector.reset();
        }
    }
}

// Create singleton instance
const metricsManager = new MetricsManager();

module.exports = {
    MetricsCollector,
    SystemMetrics,
    HttpMetrics,
    DatabaseMetrics,
    BusinessMetrics,
    MetricsManager,
    metricsManager
};