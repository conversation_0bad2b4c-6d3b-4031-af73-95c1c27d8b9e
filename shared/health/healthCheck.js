/**
 * Comprehensive health check system for all services
 * Monitors application health, dependencies, and performance metrics
 */

const { logger } = require('../utils/logger');
const redisClient = require('../cache/redisClient');

/**
 * Health check status constants
 */
const HealthStatus = {
    HEALTHY: 'healthy',
    UNHEALTHY: 'unhealthy',
    DEGRADED: 'degraded',
    UNKNOWN: 'unknown'
};

/**
 * Health check categories
 */
const HealthCategory = {
    ESSENTIAL: 'essential',      // Critical for operation
    IMPORTANT: 'important',      // Important but not critical
    OPTIONAL: 'optional'         // Nice to have
};

/**
 * Base health check class
 */
class BaseHealthCheck {
    constructor(name, category = HealthCategory.ESSENTIAL, timeout = 5000) {
        this.name = name;
        this.category = category;
        this.timeout = timeout;
        this.lastCheck = null;
        this.lastStatus = HealthStatus.UNKNOWN;
        this.lastError = null;
        this.checkCount = 0;
        this.failureCount = 0;
    }

    /**
     * Perform the health check
     */
    async check() {
        const startTime = Date.now();
        
        try {
            this.checkCount++;
            
            // Run the check with timeout
            const result = await Promise.race([
                this.performCheck(),
                new Promise((_, reject) => 
                    setTimeout(() => reject(new Error('Health check timeout')), this.timeout)
                )
            ]);

            const duration = Date.now() - startTime;
            
            this.lastCheck = new Date().toISOString();
            this.lastStatus = HealthStatus.HEALTHY;
            this.lastError = null;

            const healthResult = {
                name: this.name,
                status: this.lastStatus,
                category: this.category,
                duration,
                lastCheck: this.lastCheck,
                details: result || {},
                metadata: this.getMetadata()
            };

            logger.debug('Health check passed', {
                healthCheck: this.name,
                duration,
                status: this.lastStatus
            });

            return healthResult;
        } catch (error) {
            this.failureCount++;
            const duration = Date.now() - startTime;
            
            this.lastCheck = new Date().toISOString();
            this.lastStatus = HealthStatus.UNHEALTHY;
            this.lastError = error.message;

            const healthResult = {
                name: this.name,
                status: this.lastStatus,
                category: this.category,
                duration,
                lastCheck: this.lastCheck,
                error: error.message,
                metadata: this.getMetadata()
            };

            logger.warn('Health check failed', {
                healthCheck: this.name,
                error: error.message,
                duration,
                failureCount: this.failureCount
            });

            return healthResult;
        }
    }

    /**
     * Get metadata about this health check
     */
    getMetadata() {
        return {
            checkCount: this.checkCount,
            failureCount: this.failureCount,
            successRate: this.checkCount > 0 ? 
                ((this.checkCount - this.failureCount) / this.checkCount * 100).toFixed(2) + '%' : 
                'N/A',
            timeout: this.timeout
        };
    }

    /**
     * Abstract method to be implemented by subclasses
     */
    async performCheck() {
        throw new Error('performCheck method must be implemented');
    }
}

/**
 * Database health check
 */
class DatabaseHealthCheck extends BaseHealthCheck {
    constructor(pool, name = 'database') {
        super(name, HealthCategory.ESSENTIAL, 5000);
        this.pool = pool;
    }

    async performCheck() {
        const client = await this.pool.connect();
        
        try {
            // Simple query to test connection
            const result = await client.query('SELECT 1 as healthy, NOW() as timestamp');
            
            // Get connection pool stats
            const poolStats = {
                totalConnections: this.pool.totalCount,
                idleConnections: this.pool.idleCount,
                waitingClients: this.pool.waitingCount
            };

            return {
                connected: true,
                timestamp: result.rows[0].timestamp,
                poolStats,
                healthy: result.rows[0].healthy === 1
            };
        } finally {
            client.release();
        }
    }
}

/**
 * Redis health check
 */
class RedisHealthCheck extends BaseHealthCheck {
    constructor(name = 'redis') {
        super(name, HealthCategory.ESSENTIAL, 3000);
    }

    async performCheck() {
        const pingResult = await redisClient.ping();
        
        if (!pingResult) {
            throw new Error('Redis ping failed');
        }

        // Get Redis info
        const info = await redisClient.info('server');
        const lines = info.split('\r\n');
        const serverInfo = {};
        
        lines.forEach(line => {
            if (line.includes(':')) {
                const [key, value] = line.split(':');
                serverInfo[key] = value;
            }
        });

        return {
            connected: true,
            ping: 'PONG',
            version: serverInfo.redis_version,
            uptime: serverInfo.uptime_in_seconds,
            memory: serverInfo.used_memory_human
        };
    }
}

/**
 * HTTP service health check
 */
class HttpServiceHealthCheck extends BaseHealthCheck {
    constructor(url, name, category = HealthCategory.IMPORTANT) {
        super(name, category, 10000);
        this.url = url;
    }

    async performCheck() {
        const axios = require('axios');
        
        const response = await axios.get(this.url, {
            timeout: this.timeout - 1000, // Leave buffer for our timeout
            validateStatus: (status) => status < 400
        });

        return {
            status: response.status,
            statusText: response.statusText,
            responseTime: response.headers['x-response-time'] || 'unknown',
            version: response.headers['x-service-version'] || 'unknown'
        };
    }
}

/**
 * Disk space health check
 */
class DiskSpaceHealthCheck extends BaseHealthCheck {
    constructor(path = '/', thresholdPercent = 90, name = 'disk_space') {
        super(name, HealthCategory.IMPORTANT, 2000);
        this.path = path;
        this.thresholdPercent = thresholdPercent;
    }

    async performCheck() {
        const fs = require('fs').promises;
        const stats = await fs.statfs(this.path);
        
        const total = stats.blocks * stats.blksize;
        const free = stats.bavail * stats.blksize;
        const used = total - free;
        const usedPercent = (used / total) * 100;

        if (usedPercent > this.thresholdPercent) {
            throw new Error(`Disk usage ${usedPercent.toFixed(2)}% exceeds threshold ${this.thresholdPercent}%`);
        }

        return {
            path: this.path,
            total: this.formatBytes(total),
            used: this.formatBytes(used),
            free: this.formatBytes(free),
            usedPercent: usedPercent.toFixed(2) + '%',
            threshold: this.thresholdPercent + '%'
        };
    }

    formatBytes(bytes) {
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        if (bytes === 0) return '0 Bytes';
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    }
}

/**
 * Memory health check
 */
class MemoryHealthCheck extends BaseHealthCheck {
    constructor(thresholdPercent = 90, name = 'memory') {
        super(name, HealthCategory.IMPORTANT, 1000);
        this.thresholdPercent = thresholdPercent;
    }

    async performCheck() {
        const os = require('os');
        const process = require('process');
        
        const totalMemory = os.totalmem();
        const freeMemory = os.freemem();
        const usedMemory = totalMemory - freeMemory;
        const usedPercent = (usedMemory / totalMemory) * 100;
        
        const processMemory = process.memoryUsage();
        
        if (usedPercent > this.thresholdPercent) {
            throw new Error(`Memory usage ${usedPercent.toFixed(2)}% exceeds threshold ${this.thresholdPercent}%`);
        }

        return {
            system: {
                total: this.formatBytes(totalMemory),
                used: this.formatBytes(usedMemory),
                free: this.formatBytes(freeMemory),
                usedPercent: usedPercent.toFixed(2) + '%'
            },
            process: {
                rss: this.formatBytes(processMemory.rss),
                heapTotal: this.formatBytes(processMemory.heapTotal),
                heapUsed: this.formatBytes(processMemory.heapUsed),
                external: this.formatBytes(processMemory.external)
            },
            threshold: this.thresholdPercent + '%'
        };
    }

    formatBytes(bytes) {
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        if (bytes === 0) return '0 Bytes';
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    }
}

/**
 * Custom business logic health check
 */
class BusinessLogicHealthCheck extends BaseHealthCheck {
    constructor(checkFunction, name, category = HealthCategory.OPTIONAL) {
        super(name, category, 5000);
        this.checkFunction = checkFunction;
    }

    async performCheck() {
        return await this.checkFunction();
    }
}

/**
 * Health check manager
 */
class HealthCheckManager {
    constructor() {
        this.checks = new Map();
        this.isRunning = false;
        this.lastFullCheck = null;
        this.backgroundCheckInterval = null;
    }

    /**
     * Register a health check
     */
    register(healthCheck) {
        this.checks.set(healthCheck.name, healthCheck);
        logger.info('Health check registered', { 
            name: healthCheck.name, 
            category: healthCheck.category 
        });
    }

    /**
     * Unregister a health check
     */
    unregister(name) {
        this.checks.delete(name);
        logger.info('Health check unregistered', { name });
    }

    /**
     * Run all health checks
     */
    async runAll(category = null) {
        if (this.isRunning) {
            logger.warn('Health checks already running, skipping');
            return this.lastFullCheck;
        }

        this.isRunning = true;
        const startTime = Date.now();

        try {
            const checksToRun = category ? 
                Array.from(this.checks.values()).filter(check => check.category === category) :
                Array.from(this.checks.values());

            const results = await Promise.allSettled(
                checksToRun.map(check => check.check())
            );

            const healthResults = results.map((result, index) => {
                if (result.status === 'fulfilled') {
                    return result.value;
                } else {
                    const check = checksToRun[index];
                    return {
                        name: check.name,
                        status: HealthStatus.UNHEALTHY,
                        category: check.category,
                        error: result.reason.message,
                        lastCheck: new Date().toISOString()
                    };
                }
            });

            const duration = Date.now() - startTime;
            
            // Determine overall status
            const essentialChecks = healthResults.filter(r => r.category === HealthCategory.ESSENTIAL);
            const importantChecks = healthResults.filter(r => r.category === HealthCategory.IMPORTANT);
            
            let overallStatus = HealthStatus.HEALTHY;
            
            // If any essential check fails, system is unhealthy
            if (essentialChecks.some(check => check.status === HealthStatus.UNHEALTHY)) {
                overallStatus = HealthStatus.UNHEALTHY;
            }
            // If any important check fails, system is degraded
            else if (importantChecks.some(check => check.status === HealthStatus.UNHEALTHY)) {
                overallStatus = HealthStatus.DEGRADED;
            }

            const summary = {
                status: overallStatus,
                timestamp: new Date().toISOString(),
                duration,
                checks: healthResults,
                summary: {
                    total: healthResults.length,
                    healthy: healthResults.filter(r => r.status === HealthStatus.HEALTHY).length,
                    unhealthy: healthResults.filter(r => r.status === HealthStatus.UNHEALTHY).length,
                    degraded: healthResults.filter(r => r.status === HealthStatus.DEGRADED).length
                },
                service: {
                    name: process.env.SERVICE_NAME || 'unknown',
                    version: process.env.SERVICE_VERSION || '1.0.0',
                    environment: process.env.NODE_ENV || 'development',
                    uptime: process.uptime(),
                    pid: process.pid
                }
            };

            this.lastFullCheck = summary;
            
            logger.info('Health check completed', {
                overallStatus,
                duration,
                totalChecks: healthResults.length,
                healthyChecks: summary.summary.healthy,
                unhealthyChecks: summary.summary.unhealthy
            });

            return summary;
        } finally {
            this.isRunning = false;
        }
    }

    /**
     * Run a specific health check
     */
    async runCheck(name) {
        const check = this.checks.get(name);
        if (!check) {
            throw new Error(`Health check '${name}' not found`);
        }

        return await check.check();
    }

    /**
     * Get the status of a specific check
     */
    getCheckStatus(name) {
        const check = this.checks.get(name);
        if (!check) {
            return null;
        }

        return {
            name: check.name,
            status: check.lastStatus,
            lastCheck: check.lastCheck,
            lastError: check.lastError,
            metadata: check.getMetadata()
        };
    }

    /**
     * Start background health monitoring
     */
    startBackgroundMonitoring(intervalMs = 60000) {
        if (this.backgroundCheckInterval) {
            clearInterval(this.backgroundCheckInterval);
        }

        this.backgroundCheckInterval = setInterval(async () => {
            try {
                await this.runAll();
            } catch (error) {
                logger.error('Background health check failed', { error: error.message });
            }
        }, intervalMs);

        logger.info('Background health monitoring started', { intervalMs });
    }

    /**
     * Stop background health monitoring
     */
    stopBackgroundMonitoring() {
        if (this.backgroundCheckInterval) {
            clearInterval(this.backgroundCheckInterval);
            this.backgroundCheckInterval = null;
            logger.info('Background health monitoring stopped');
        }
    }

    /**
     * Get all registered checks
     */
    getRegisteredChecks() {
        return Array.from(this.checks.keys());
    }

    /**
     * Clear all checks
     */
    clear() {
        this.checks.clear();
        this.stopBackgroundMonitoring();
    }
}

// Create singleton instance
const healthManager = new HealthCheckManager();

module.exports = {
    HealthStatus,
    HealthCategory,
    BaseHealthCheck,
    DatabaseHealthCheck,
    RedisHealthCheck,
    HttpServiceHealthCheck,
    DiskSpaceHealthCheck,
    MemoryHealthCheck,
    BusinessLogicHealthCheck,
    HealthCheckManager,
    healthManager
};