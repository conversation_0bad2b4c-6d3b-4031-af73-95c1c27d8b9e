from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import sentry_sdk
from sentry_sdk.integrations.fastapi import FastApiIntegration

from app.core.config import settings
from app.core.sentry import init_sentry, capture_exception
from app.core.middleware import SentryMiddleware, RequestLoggingMiddleware
from app.api.api_v1.api import api_router


# Initialize Sentry before creating FastAPI app
init_sentry()

# Create FastAPI application
app = FastAPI(
    title=settings.PROJECT_NAME,
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    docs_url="/docs",
    redoc_url="/redoc",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.BACKEND_CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add custom middleware
app.add_middleware(SentryMiddleware)
app.add_middleware(RequestLoggingMiddleware)


# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler that captures exceptions to Sentry."""
    
    # Capture exception to Sentry
    capture_exception(exc, {
        "request_url": str(request.url),
        "request_method": request.method,
        "request_id": getattr(request.state, 'request_id', None),
    })
    
    # Return user-friendly error response
    if isinstance(exc, HTTPException):
        return JSONResponse(
            status_code=exc.status_code,
            content={"detail": exc.detail}
        )
    
    # For non-HTTP exceptions, return generic error
    return JSONResponse(
        status_code=500,
        content={
            "detail": "Internal server error",
            "request_id": getattr(request.state, 'request_id', None)
        }
    )


# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "environment": settings.ENVIRONMENT,
        "project": settings.PROJECT_NAME
    }


# Test Sentry endpoint (for development)
@app.get("/sentry-debug")
async def trigger_error():
    """Endpoint to test Sentry error capture (development only)."""
    if settings.ENVIRONMENT != "development":
        raise HTTPException(status_code=404, detail="Not found")
    
    # Test different types of errors
    division_by_zero = 1 / 0
    return {"message": "This should not be reached"}


# Include API router
app.include_router(api_router, prefix=settings.API_V1_STR)


# Startup event
@app.on_event("startup")
async def startup_event():
    """Application startup event."""
    print(f"🚀 Starting {settings.PROJECT_NAME}")
    print(f"📊 Environment: {settings.ENVIRONMENT}")
    print(f"🔗 API Documentation: http://localhost:8000/docs")
    
    # Set Sentry release info
    if settings.SENTRY_DSN:
        sentry_sdk.set_tag("startup", "completed")


# Shutdown event
@app.on_event("shutdown")
async def shutdown_event():
    """Application shutdown event."""
    print(f"👋 Shutting down {settings.PROJECT_NAME}")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app", 
        host="0.0.0.0", 
        port=8000, 
        reload=True if settings.ENVIRONMENT == "development" else False
    )