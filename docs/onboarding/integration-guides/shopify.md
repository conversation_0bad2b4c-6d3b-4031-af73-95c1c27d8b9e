# Shopify Integration Guide

This guide will walk you through connecting your Shopify store to the E-commerce Analytics platform.

## Prerequisites

Before starting, ensure you have:
- Admin access to your Shopify store
- Active Shopify plan (Basic Shopify or higher)
- Shopify store is live and processing orders
- Admin permissions to install apps

## Quick Setup (3 Minutes)

### Step 1: Initiate Connection
1. Log into your Analytics platform account
2. Navigate to **Integrations** in the left sidebar
3. Click **Add Integration**
4. Select **Shopify** from the platform list
5. Click **Connect with Shopify**

### Step 2: Authorize Access
1. You'll be redirected to Shopify's authorization page
2. Review the permissions requested:
   - **Read orders**: Access order data for analytics
   - **Read customers**: Analyze customer behavior
   - **Read products**: Track product performance
   - **Write webhooks**: Receive real-time updates
3. Click **Install app** to grant access
4. You'll be redirected back to the Analytics platform

### Step 3: Configure Settings
1. Verify your store information is correct
2. Choose synchronization options:
   - **Historical data**: Import past 90 days (recommended)
   - **Real-time sync**: Enable webhook notifications
   - **Data types**: Select what to sync (orders, customers, products)
3. Click **Save Configuration**

### Step 4: Verify Connection
1. Wait 2-3 minutes for initial data sync
2. Check the **Dashboard** for incoming data
3. Visit **Integrations** page to confirm "Active" status
4. Review sync logs for any errors

## Detailed Configuration

### Data Synchronization Options

#### Historical Data Import
Choose how much historical data to import:
- **30 days**: Quick setup, recent trends only
- **90 days**: Recommended for seasonal businesses
- **1 year**: Comprehensive historical analysis
- **All data**: Complete history (may take longer)

#### Real-time Synchronization
Configure webhook events:
```json
{
  "events": [
    "orders/create",
    "orders/updated", 
    "orders/paid",
    "orders/cancelled",
    "customers/create",
    "customers/updated",
    "products/create",
    "products/updated"
  ]
}
```

#### Data Filtering
Set up filters to sync only relevant data:
- **Order status**: All, fulfilled only, or specific statuses
- **Customer types**: All customers or exclude test customers
- **Products**: All products or specific collections
- **Date range**: Limit sync to specific time periods

### Advanced Settings

#### Custom Fields Mapping
Map Shopify fields to Analytics platform fields:

| Shopify Field | Analytics Field | Purpose |
|---------------|-----------------|---------|
| `financial_status` | `payment_status` | Track payment completion |
| `fulfillment_status` | `order_status` | Monitor order processing |
| `source_name` | `traffic_source` | Identify acquisition channels |
| `landing_site` | `landing_page` | Track entry points |
| `referring_site` | `referrer` | Analyze referral traffic |

#### UTM Parameter Tracking
Automatically capture UTM parameters from Shopify:
```javascript
// Shopify automatically captures these in order attributes
{
  "utm_source": "google",
  "utm_medium": "cpc", 
  "utm_campaign": "summer_sale",
  "utm_term": "running_shoes",
  "utm_content": "ad_variant_a"
}
```

#### Revenue Attribution
Configure how revenue is attributed:
- **Gross revenue**: Include taxes and shipping
- **Net revenue**: Exclude taxes and shipping  
- **Profit margins**: Include cost of goods sold (requires manual setup)
- **Currency conversion**: Handle multi-currency stores

### Tracking Code Installation

#### Automatic Installation (Recommended)
The platform automatically installs tracking code via Shopify's Script API:
1. Code is injected into theme templates
2. Updates automatically with platform changes
3. No manual theme modifications required
4. Works with all Shopify themes

#### Manual Installation
If automatic installation fails:

1. **Add to theme.liquid**:
```html
<!-- Add before closing </head> tag -->
<script>
(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://analytics.yourdomain.com/track.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','YOUR_TRACKING_ID');
</script>
```

2. **Add to checkout.liquid** (Shopify Plus only):
```html
<!-- Add before closing </body> tag -->
<script>
analytics.track('Purchase', {
  order_id: '{{ order.order_number }}',
  revenue: {{ order.total_price | money_without_currency }},
  currency: '{{ order.currency }}',
  items: [
    {% for line_item in order.line_items %}
    {
      product_id: '{{ line_item.product_id }}',
      variant_id: '{{ line_item.variant_id }}',
      name: '{{ line_item.title | escape }}',
      price: {{ line_item.price | money_without_currency }},
      quantity: {{ line_item.quantity }}
    }{% unless forloop.last %},{% endunless %}
    {% endfor %}
  ]
});
</script>
```

### Webhook Configuration

#### Required Webhooks
The platform automatically configures these webhooks:

```yaml
webhooks:
  - topic: "orders/create"
    endpoint: "https://api.yourdomain.com/webhooks/shopify/orders/create"
  - topic: "orders/updated" 
    endpoint: "https://api.yourdomain.com/webhooks/shopify/orders/updated"
  - topic: "orders/paid"
    endpoint: "https://api.yourdomain.com/webhooks/shopify/orders/paid"
  - topic: "customers/create"
    endpoint: "https://api.yourdomain.com/webhooks/shopify/customers/create"
```

#### Webhook Verification
Shopify signs webhooks with HMAC-SHA256:
```javascript
const crypto = require('crypto');

function verifyWebhook(data, hmacHeader) {
  const calculated = crypto
    .createHmac('sha256', process.env.SHOPIFY_WEBHOOK_SECRET)
    .update(data, 'utf8')
    .digest('base64');
  
  return calculated === hmacHeader;
}
```

### Data Schema Mapping

#### Orders
Shopify order data mapped to Analytics platform:

```json
{
  "shopify_order": {
    "id": 12345,
    "order_number": "1001", 
    "financial_status": "paid",
    "fulfillment_status": "fulfilled",
    "total_price": "150.00",
    "subtotal_price": "130.00",
    "total_tax": "10.00",
    "total_shipping": "10.00",
    "currency": "USD",
    "customer": {
      "id": 67890,
      "email": "<EMAIL>",
      "first_name": "John",
      "last_name": "Doe"
    },
    "line_items": [...],
    "created_at": "2025-06-24T10:00:00Z"
  },
  "analytics_format": {
    "order_id": "shopify_12345",
    "order_number": "1001",
    "status": "completed",
    "revenue": 150.00,
    "tax": 10.00,
    "shipping": 10.00,
    "currency": "USD",
    "customer_id": "shopify_67890",
    "customer_email": "<EMAIL>",
    "items": [...],
    "created_at": "2025-06-24T10:00:00Z"
  }
}
```

#### Products
Product data synchronization:

```json
{
  "product_id": "shopify_123",
  "title": "Running Shoes",
  "handle": "running-shoes-blue",
  "product_type": "Footwear",
  "vendor": "Nike",
  "tags": ["sports", "running", "shoes"],
  "variants": [
    {
      "id": "shopify_variant_456",
      "title": "Blue / Size 10",
      "price": "129.99",
      "sku": "RS-BLUE-10",
      "inventory_quantity": 50
    }
  ]
}
```

## Troubleshooting

### Common Issues

#### Integration Not Working
**Symptoms**: No data appearing after connection
**Solutions**:
1. Check Shopify app permissions in store admin
2. Verify store is not in development mode
3. Ensure store has processed orders
4. Check webhook delivery logs

#### Tracking Code Not Loading
**Symptoms**: Analytics not recording visits
**Solutions**:
1. Verify tracking code installation
2. Check browser console for JavaScript errors
3. Ensure ad blockers aren't interfering
4. Test in incognito mode

#### Webhook Delivery Failures
**Symptoms**: Real-time data not updating
**Solutions**:
1. Check webhook endpoint URLs
2. Verify HTTPS certificates
3. Review webhook retry attempts
4. Check server response codes

#### Data Discrepancies
**Symptoms**: Analytics data doesn't match Shopify
**Solutions**:
1. Compare date ranges and timezones
2. Check order status filters
3. Verify currency conversion settings
4. Review test order exclusions

### Data Validation

#### Order Reconciliation
Compare key metrics between Shopify and Analytics:

```sql
-- Analytics platform query
SELECT 
  DATE(created_at) as date,
  COUNT(*) as order_count,
  SUM(revenue) as total_revenue
FROM orders 
WHERE integration_id = 'shopify_store_123'
  AND created_at >= '2025-06-01'
GROUP BY DATE(created_at);
```

#### Revenue Verification
Ensure revenue calculations match:
- **Shopify total**: Includes taxes and shipping
- **Analytics revenue**: May exclude based on settings
- **Currency**: Check conversion rates for multi-currency
- **Refunds**: Verify refund handling

### Performance Optimization

#### Sync Performance
Optimize data synchronization:
- **Batch size**: Reduce for large catalogs
- **Frequency**: Adjust based on order volume
- **Filters**: Sync only necessary data
- **Off-peak hours**: Schedule heavy syncs during low traffic

#### Rate Limiting
Shopify API limits:
- **REST API**: 2 requests per second
- **GraphQL**: 1000 points per 60 seconds
- **Webhooks**: No rate limit
- **Bulk operations**: Use for large data sets

## Support

### Getting Help
If you encounter issues:

1. **Check Status Page**: [status.yourdomain.com](https://status.yourdomain.com)
2. **Review Logs**: Available in **Integrations** → **Shopify** → **Logs**
3. **Contact Support**: 
   - Email: <EMAIL>
   - Live Chat: Available in platform
   - Phone: +****************

### Useful Resources
- [Shopify Partner Documentation](https://shopify.dev/docs)
- [Webhook Testing Tool](https://webhook.site)
- [Analytics Platform API Docs](../../api/README.md)
- [Community Forum](https://community.yourdomain.com/shopify)

### Best Practices
1. **Test First**: Use a development store before production
2. **Monitor Regularly**: Check sync status weekly
3. **Update Carefully**: Review changes before applying
4. **Backup Data**: Keep local copies of important data
5. **Document Changes**: Track customizations and settings