# WooCommerce Integration Guide

This comprehensive guide will help you connect your WooCommerce store to the E-commerce Analytics platform.

## Prerequisites

Before starting, ensure you have:
- WordPress website with WooCommerce plugin installed
- WordPress admin access
- WooCommerce version 3.0 or higher
- SSL certificate installed (highly recommended)
- PHP version 7.4 or higher

## Quick Setup (5 Minutes)

### Step 1: Generate API Credentials
1. Log into your WordPress admin dashboard
2. Navigate to **WooCommerce** → **Settings** → **Advanced** → **REST API**
3. Click **Add Key**
4. Fill in the details:
   - **Description**: "Analytics Platform Integration"
   - **User**: Select an admin user
   - **Permissions**: Select "Read/Write"
5. Click **Generate API Key**
6. **Important**: Copy the Consumer Key and Consumer Secret immediately (they won't be shown again)

### Step 2: Connect to Analytics Platform
1. Log into your Analytics platform account
2. Navigate to **Integrations** in the sidebar
3. Click **Add Integration**
4. Select **WooCommerce**
5. Enter the following information:
   - **Store URL**: Your website URL (e.g., https://yourstore.com)
   - **Consumer Key**: Paste the key from Step 1
   - **Consumer Secret**: Paste the secret from Step 1
6. Click **Test Connection** to verify
7. Click **Save Integration**

### Step 3: Configure Sync Settings
1. Choose synchronization options:
   - **Historical Data**: Select how much past data to import
   - **Real-time Sync**: Enable webhook notifications
   - **Data Types**: Choose what to sync (orders, customers, products)
2. Click **Start Sync**

### Step 4: Install Tracking Code
1. The platform will provide a tracking code snippet
2. Add this to your WordPress theme or use our plugin (recommended)
3. Verify tracking is working by checking real-time data

## Detailed Configuration

### API Setup Deep Dive

#### Creating API Credentials
WooCommerce uses OAuth 1.0a for authentication. Here's what each permission level allows:

| Permission | Description | Recommended |
|------------|-------------|-------------|
| **Read** | View orders, products, customers | Minimum required |
| **Write** | Create and update data | For advanced features |
| **Read/Write** | Full access to store data | Recommended |

#### API Endpoint Configuration
The integration uses these WooCommerce REST API endpoints:

```bash
# Orders
GET /wp-json/wc/v3/orders
GET /wp-json/wc/v3/orders/{id}

# Customers  
GET /wp-json/wc/v3/customers
GET /wp-json/wc/v3/customers/{id}

# Products
GET /wp-json/wc/v3/products
GET /wp-json/wc/v3/products/{id}

# Order notes
GET /wp-json/wc/v3/orders/{id}/notes

# Webhooks
POST /wp-json/wc/v3/webhooks
```

### Advanced Integration Settings

#### Historical Data Import
Configure how much historical data to synchronize:

```json
{
  "import_settings": {
    "orders": {
      "date_range": "90_days", // 30_days, 90_days, 1_year, all
      "statuses": ["completed", "processing", "on-hold"],
      "exclude_test": true
    },
    "customers": {
      "include_guests": true,
      "min_order_count": 0
    },
    "products": {
      "published_only": true,
      "include_variations": true
    }
  }
}
```

#### Real-time Synchronization
Enable webhooks for real-time data updates:

```php
// WordPress functions.php or plugin
add_action('woocommerce_order_status_changed', 'send_order_webhook');
add_action('woocommerce_new_customer', 'send_customer_webhook');
add_action('woocommerce_product_saved', 'send_product_webhook');

function send_order_webhook($order_id) {
    $order = wc_get_order($order_id);
    $webhook_url = 'https://api.yourdomain.com/webhooks/woocommerce/orders';
    
    wp_remote_post($webhook_url, array(
        'body' => json_encode($order->get_data()),
        'headers' => array(
            'Content-Type' => 'application/json',
            'X-WC-Webhook-Source' => get_site_url(),
            'X-WC-Webhook-Topic' => 'order.updated',
            'X-WC-Webhook-Signature' => generate_signature($order->get_data())
        )
    ));
}
```

### Tracking Code Installation

#### Method 1: WordPress Plugin (Recommended)
1. Download our official WooCommerce Analytics Plugin
2. Upload via **Plugins** → **Add New** → **Upload Plugin**
3. Activate the plugin
4. Enter your Analytics Platform API key in plugin settings
5. The plugin automatically handles tracking code injection

#### Method 2: Manual Theme Integration
Add tracking code to your theme files:

**In header.php (before closing `</head>`):**
```html
<!-- Analytics Platform Tracking -->
<script>
(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'analytics.start':
new Date().getTime(),event:'analytics.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://analytics.yourdomain.com/track.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','YOUR_TRACKING_ID');
</script>
```

**In woocommerce/checkout/thankyou.php:**
```php
<?php if ($order): ?>
<script>
analytics.track('Purchase', {
    order_id: '<?php echo $order->get_order_number(); ?>',
    revenue: <?php echo $order->get_total(); ?>,
    currency: '<?php echo $order->get_currency(); ?>',
    items: [
        <?php foreach ($order->get_items() as $item): ?>
        {
            product_id: '<?php echo $item->get_product_id(); ?>',
            name: '<?php echo esc_js($item->get_name()); ?>',
            price: <?php echo $item->get_total(); ?>,
            quantity: <?php echo $item->get_quantity(); ?>
        },
        <?php endforeach; ?>
    ]
});
</script>
<?php endif; ?>
```

#### Method 3: Google Tag Manager
If you use GTM, add this data layer code:

```javascript
// Add to checkout thank you page
<script>
dataLayer.push({
    'event': 'purchase',
    'ecommerce': {
        'transaction_id': '<?php echo $order->get_order_number(); ?>',
        'value': <?php echo $order->get_total(); ?>,
        'currency': '<?php echo $order->get_currency(); ?>',
        'items': [
            <?php foreach ($order->get_items() as $item): ?>
            {
                'item_id': '<?php echo $item->get_product_id(); ?>',
                'item_name': '<?php echo esc_js($item->get_name()); ?>',
                'price': <?php echo $item->get_total(); ?>,
                'quantity': <?php echo $item->get_quantity(); ?>
            },
            <?php endforeach; ?>
        ]
    }
});
</script>
```

### Webhook Configuration

#### Setting Up Webhooks
Configure webhooks for real-time data synchronization:

1. **Via WooCommerce Admin**:
   - Go to **WooCommerce** → **Settings** → **Advanced** → **Webhooks**
   - Click **Add Webhook**
   - Configure as shown in the table below

2. **Via REST API**:
```bash
curl -X POST https://yourstore.com/wp-json/wc/v3/webhooks \
  -u consumer_key:consumer_secret \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Order Created",
    "topic": "order.created",
    "delivery_url": "https://api.yourdomain.com/webhooks/woocommerce/orders"
  }'
```

#### Required Webhooks
| Webhook Topic | Delivery URL | Purpose |
|---------------|--------------|---------|
| `order.created` | `/webhooks/woocommerce/orders/created` | New order notifications |
| `order.updated` | `/webhooks/woocommerce/orders/updated` | Order status changes |
| `order.deleted` | `/webhooks/woocommerce/orders/deleted` | Order cancellations |
| `customer.created` | `/webhooks/woocommerce/customers/created` | New customer registrations |
| `product.created` | `/webhooks/woocommerce/products/created` | New product additions |
| `product.updated` | `/webhooks/woocommerce/products/updated` | Product modifications |

#### Webhook Security
Verify webhook authenticity using signature validation:

```php
function verify_woocommerce_webhook($payload, $signature, $secret) {
    $calculated_hmac = base64_encode(hash_hmac('sha256', $payload, $secret, true));
    return hash_equals($signature, $calculated_hmac);
}

// Usage in webhook handler
$payload = file_get_contents('php://input');
$signature = $_SERVER['HTTP_X_WC_WEBHOOK_SIGNATURE'];
$secret = 'your_webhook_secret';

if (!verify_woocommerce_webhook($payload, $signature, $secret)) {
    http_response_code(401);
    exit('Unauthorized');
}
```

### Data Schema Mapping

#### Order Data Mapping
WooCommerce order structure mapped to Analytics platform:

```json
{
  "woocommerce_order": {
    "id": 123,
    "order_key": "wc_order_abc123",
    "status": "completed",
    "currency": "USD",
    "total": "150.00",
    "subtotal": "130.00",
    "total_tax": "10.00",
    "shipping_total": "10.00",
    "customer_id": 456,
    "billing": {
      "first_name": "John",
      "last_name": "Doe",
      "email": "<EMAIL>",
      "country": "US"
    },
    "line_items": [...],
    "date_created": "2025-06-24T10:00:00"
  },
  "analytics_format": {
    "order_id": "wc_123",
    "order_key": "wc_order_abc123", 
    "status": "completed",
    "revenue": 150.00,
    "subtotal": 130.00,
    "tax": 10.00,
    "shipping": 10.00,
    "currency": "USD",
    "customer_id": "wc_456",
    "customer_email": "<EMAIL>",
    "items": [...],
    "created_at": "2025-06-24T10:00:00Z"
  }
}
```

#### Product Data Mapping
```json
{
  "woocommerce_product": {
    "id": 789,
    "name": "Running Shoes",
    "slug": "running-shoes",
    "type": "variable",
    "status": "publish",
    "regular_price": "129.99",
    "sale_price": "99.99",
    "sku": "RS-001",
    "categories": [{"id": 15, "name": "Footwear"}],
    "tags": [{"id": 20, "name": "sports"}],
    "variations": [790, 791]
  },
  "analytics_format": {
    "product_id": "wc_789",
    "name": "Running Shoes",
    "slug": "running-shoes",
    "type": "variable",
    "price": 129.99,
    "sale_price": 99.99,
    "sku": "RS-001",
    "category": "Footwear",
    "tags": ["sports"],
    "variants": ["wc_790", "wc_791"]
  }
}
```

### Custom Field Mapping

#### WooCommerce Custom Fields
Map custom fields to Analytics platform:

```php
// Add custom field mapping in WordPress
add_filter('woocommerce_analytics_custom_fields', function($fields, $object_type) {
    if ($object_type === 'order') {
        $fields['acquisition_channel'] = get_post_meta($order_id, '_acquisition_channel', true);
        $fields['customer_lifetime_value'] = calculate_customer_ltv($customer_id);
    }
    return $fields;
}, 10, 2);
```

#### UTM Parameter Tracking
Capture UTM parameters and save with orders:

```php
// Save UTM parameters with order
add_action('woocommerce_checkout_order_processed', function($order_id) {
    $utm_source = WC()->session->get('utm_source');
    $utm_medium = WC()->session->get('utm_medium');
    $utm_campaign = WC()->session->get('utm_campaign');
    
    if ($utm_source) {
        update_post_meta($order_id, '_utm_source', $utm_source);
    }
    if ($utm_medium) {
        update_post_meta($order_id, '_utm_medium', $utm_medium);
    }
    if ($utm_campaign) {
        update_post_meta($order_id, '_utm_campaign', $utm_campaign);
    }
});

// Capture UTM parameters on site entry
add_action('init', function() {
    if (isset($_GET['utm_source'])) {
        WC()->session->set('utm_source', $_GET['utm_source']);
    }
    if (isset($_GET['utm_medium'])) {
        WC()->session->set('utm_medium', $_GET['utm_medium']);
    }
    if (isset($_GET['utm_campaign'])) {
        WC()->session->set('utm_campaign', $_GET['utm_campaign']);
    }
});
```

## Troubleshooting

### Common Issues

#### API Connection Fails
**Symptoms**: "Connection failed" error during setup
**Solutions**:
1. Verify SSL certificate is valid
2. Check if REST API is enabled in WooCommerce settings
3. Ensure consumer key/secret are correct
4. Test API access manually:
```bash
curl https://yourstore.com/wp-json/wc/v3/orders \
  -u consumer_key:consumer_secret
```

#### No Data Syncing
**Symptoms**: Integration shows "Connected" but no data appears
**Solutions**:
1. Check order statuses included in sync
2. Verify date ranges are correct
3. Ensure orders exist in the specified time period
4. Check webhook delivery logs

#### Tracking Code Not Loading
**Symptoms**: Real-time analytics not working
**Solutions**:
1. Verify tracking code placement in theme
2. Check browser console for JavaScript errors
3. Ensure tracking ID is correct
4. Test with ad blockers disabled

#### Performance Issues
**Symptoms**: Website slowdown after integration
**Solutions**:
1. Reduce API request frequency
2. Enable caching for API responses
3. Optimize database queries
4. Use webhook-based sync instead of polling

### Data Validation

#### Comparing WooCommerce vs Analytics Data
Run these queries to validate data accuracy:

**WooCommerce (MySQL)**:
```sql
SELECT 
    DATE(post_date) as date,
    COUNT(*) as order_count,
    SUM(meta_value) as total_revenue
FROM wp_posts p
JOIN wp_postmeta pm ON p.ID = pm.post_id
WHERE p.post_type = 'shop_order'
    AND p.post_status = 'wc-completed'
    AND pm.meta_key = '_order_total'
    AND p.post_date >= '2025-06-01'
GROUP BY DATE(p.post_date);
```

**Analytics Platform**:
```sql
SELECT 
    DATE(created_at) as date,
    COUNT(*) as order_count,
    SUM(revenue) as total_revenue
FROM orders 
WHERE integration_id = 'woocommerce_store_123'
    AND status = 'completed'
    AND created_at >= '2025-06-01'
GROUP BY DATE(created_at);
```

### Performance Optimization

#### Database Optimization
Optimize WooCommerce database for better API performance:

```sql
-- Add indexes for faster queries
ALTER TABLE wp_postmeta ADD INDEX idx_meta_key_value (meta_key, meta_value(50));
ALTER TABLE wp_posts ADD INDEX idx_post_type_status_date (post_type, post_status, post_date);

-- Clean up transients
DELETE FROM wp_options WHERE option_name LIKE '_transient_%';
DELETE FROM wp_options WHERE option_name LIKE '_site_transient_%';
```

#### Caching Configuration
Enable caching for better performance:

```php
// Add to wp-config.php
define('WP_CACHE', true);

// Add caching headers for API responses
add_action('rest_api_init', function() {
    add_filter('rest_post_dispatch', function($response, $server, $request) {
        if (strpos($request->get_route(), '/wc/v3/') === 0) {
            $response->header('Cache-Control', 'public, max-age=300');
        }
        return $response;
    }, 10, 3);
});
```

## Best Practices

### Security
1. **Use HTTPS**: Always use SSL/TLS for API communications
2. **Rotate Keys**: Regularly update API keys and webhook secrets
3. **Limit Permissions**: Use read-only access when possible
4. **Validate Webhooks**: Always verify webhook signatures
5. **Monitor Access**: Review API access logs regularly

### Performance
1. **Batch Requests**: Use batch API calls for large data sets
2. **Rate Limiting**: Respect WooCommerce API rate limits
3. **Caching**: Implement proper caching strategies
4. **Optimize Queries**: Use efficient database queries
5. **Monitor Resources**: Track CPU and memory usage

### Data Quality
1. **Regular Audits**: Compare data between systems monthly
2. **Test Orders**: Exclude test orders from analytics
3. **Handle Refunds**: Properly track refunds and cancellations
4. **Currency Handling**: Ensure proper multi-currency support
5. **Time Zones**: Standardize on UTC for all timestamps

## Support Resources

### Getting Help
- **Email Support**: <EMAIL>
- **Live Chat**: Available in the Analytics platform
- **Phone Support**: +****************
- **Community Forum**: [community.yourdomain.com/woocommerce](https://community.yourdomain.com/woocommerce)

### Useful Links
- [WooCommerce REST API Documentation](https://woocommerce.github.io/woocommerce-rest-api-docs/)
- [WordPress Codex](https://codex.wordpress.org/)
- [Analytics Platform API Documentation](../../api/README.md)
- [WooCommerce Developer Resources](https://woocommerce.com/developers/)

### Debugging Tools
- **Query Monitor Plugin**: Debug WordPress/WooCommerce queries
- **Webhook Tester**: [webhook.site](https://webhook.site) for testing webhooks
- **Postman Collection**: Available for API testing
- **Log Files**: Check WooCommerce logs in admin dashboard