# Getting Started

This guide will help you set up the E-commerce Analytics SaaS project for development.

## Prerequisites

- **Docker & Docker Compose** - For containerized development
- **Node.js 20+** - For Node.js services development
- **Go 1.21+** - For link tracking service development
- **Git** - Version control
- **Make** - For running development commands
- **curl & jq** (optional) - For testing API endpoints

## Quick Start

1. **Clone the repository**
   ```bash
   git clone <your-repo-url>
   cd ecommerce-analytics-saas
   ```

2. **Initial setup**
   ```bash
   make setup
   ```
   This will create a `.env` file from `.env.example`. Edit it with your configuration.

3. **Start development environment**
   ```bash
   make dev
   ```
   This will:
   - Start PostgreSQL and Redis containers
   - Run database migrations
   - Make services available for development

4. **Verify setup**
   ```bash
   make health
   ```

## Development Workflow

### Starting Services

**Option 1: Using Make (Recommended)**
```bash
# Start core services (database + redis)
make dev

# Start with monitoring (includes Prometheus & Grafana)
make dev-full
```

**Option 2: Using Docker Compose**
```bash
# Start all services
docker-compose up -d

# Start with monitoring
docker-compose --profile monitoring up -d
```

### Service Development

Each service can be developed independently:

**Link Tracking Service (Go)**
```bash
cd services/link-tracking
air -c .air.toml  # Hot reload development
```

**Integration Service (Node.js)**
```bash
cd services/integration
npm run dev  # Nodemon hot reload
```

**Analytics Service (Node.js)**
```bash
cd services/analytics
npm run dev
```

**Dashboard Service (Node.js)**
```bash
cd services/dashboard
npm run dev
```

### Available Services

| Service | URL | Description |
|---------|-----|-------------|
| Link Tracking | http://localhost:8080 | Go service for branded link management |
| Integration | http://localhost:3001 | Node.js service for e-commerce integrations |
| Analytics | http://localhost:3002 | Node.js service for data processing |
| Dashboard | http://localhost:3000 | Node.js API gateway and web dashboard |
| PostgreSQL | localhost:5432 | Primary database |
| Redis | localhost:6379 | Cache and session store |
| Prometheus | http://localhost:9090 | Metrics collection (with monitoring profile) |
| Grafana | http://localhost:3001 | Dashboards (with monitoring profile) |

### Environment Configuration

Copy `.env.example` to `.env` and configure:

```bash
# Database
POSTGRES_HOST=localhost
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password
POSTGRES_DB=ecommerce_analytics

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379

# API Keys (get from respective platforms)
SHOPIFY_API_KEY=your_shopify_key
SHOPIFY_SECRET=your_shopify_secret
WOOCOMMERCE_KEY=your_woocommerce_key
WOOCOMMERCE_SECRET=your_woocommerce_secret
```

## Development Commands

### Core Commands
```bash
make help          # Show all available commands
make dev           # Start development environment
make stop          # Stop all services
make restart       # Restart services
make status        # Show service status
make logs          # Show logs from all services
make health        # Check service health
```

### Database Commands
```bash
make db-migrate    # Run database migrations
make db-reset      # Reset database (WARNING: deletes data)
make db-shell      # Connect to PostgreSQL shell
make redis-shell   # Connect to Redis CLI
```

### Testing Commands
```bash
make test                        # Run all tests
make test-service SERVICE=name   # Test specific service
make lint                        # Run linting
make security-scan              # Run security scans
```

### Build Commands
```bash
make build                       # Build all Docker images
make build-service SERVICE=name  # Build specific service
```

## Project Structure

```
ecommerce-analytics-saas/
├── services/                    # Microservices
│   ├── link-tracking/          # Go service
│   ├── integration/            # Node.js service
│   ├── analytics/              # Node.js service
│   └── dashboard/              # Node.js service
├── k8s/                        # Kubernetes manifests
├── scripts/                    # Development scripts
├── docs/                       # Documentation
├── .github/workflows/          # CI/CD pipelines
├── docker-compose.yml          # Local development
├── Makefile                    # Development commands
└── IMPLEMENTATION_GUIDE.md     # Complete implementation guide
```

## API Testing

### Health Checks
```bash
# Check all services
curl http://localhost:8080/health  # Link Tracking
curl http://localhost:3001/health  # Integration
curl http://localhost:3002/health  # Analytics
curl http://localhost:3000/health  # Dashboard
```

### Link Tracking API
```bash
# Create a branded link
curl -X POST http://localhost:8080/api/v1/links \
  -H "Content-Type: application/json" \
  -d '{
    "target_url": "https://example.com/product/123",
    "title": "Test Product Link",
    "campaign_id": "campaign-123"
  }'

# Test redirect (replace {code} with actual short code)
curl -I http://localhost:8080/{code}
```

## Database Access

**PostgreSQL Shell:**
```bash
make db-shell
# or
docker-compose exec postgres psql -U postgres -d ecommerce_analytics
```

**Redis CLI:**
```bash
make redis-shell
# or
docker-compose exec redis redis-cli
```

## Troubleshooting

### Common Issues

**1. Port conflicts**
```bash
# Check what's using the port
lsof -i :3000
# Kill the process if needed
kill -9 <PID>
```

**2. Database connection issues**
```bash
# Check if PostgreSQL is running
make status
# Restart database
docker-compose restart postgres
```

**3. Permission issues with scripts**
```bash
chmod +x scripts/*.sh
```

**4. Docker issues**
```bash
# Clean up Docker
make clean
# or
docker system prune -f
```

### Logs and Debugging

```bash
# View all logs
make logs

# View specific service logs
make logs-service SERVICE=link-tracking

# Follow logs in real-time
docker-compose logs -f integration
```

### Monitoring and Metrics

With monitoring profile enabled:

**Prometheus Metrics:**
- http://localhost:9090
- View application metrics and health

**Grafana Dashboards:**
- http://localhost:3001 (admin/admin)
- Pre-configured dashboards for services

## Next Steps

1. **Review the Implementation Guide** - See [IMPLEMENTATION_GUIDE.md](../IMPLEMENTATION_GUIDE.md) for detailed architecture
2. **Configure Integrations** - Add your Shopify/WooCommerce/Amazon API credentials
3. **Run Tests** - `make test` to ensure everything is working
4. **Start Development** - Begin implementing features following the guide

## Additional Resources

- [API Documentation](./api.md) - REST API reference
- [Database Schema](./schema.md) - Database design
- [Security Guide](./security.md) - Security best practices
- [Deployment Guide](./deployment.md) - Production deployment

## Getting Help

- Check the [Implementation Guide](../IMPLEMENTATION_GUIDE.md) for detailed information
- Review logs with `make logs`
- Use `make health` to verify service status
- Check Docker containers with `make status`