# Integration Service API

The Integration Service manages third-party platform integrations and webhook processing.

**Base URL**: `/integration`

## Platform Integrations

### GET /platforms

Get available integration platforms.

**Response**:
```json
{
  "success": true,
  "data": {
    "platforms": [
      {
        "id": "shopify",
        "name": "Shopify",
        "description": "E-commerce platform integration",
        "logo": "https://cdn.example.com/logos/shopify.png",
        "status": "active",
        "features": ["order_tracking", "product_sync", "customer_data"],
        "auth_type": "oauth2",
        "webhook_support": true
      },
      {
        "id": "woocommerce",
        "name": "WooCommerce",
        "description": "WordPress e-commerce plugin",
        "logo": "https://cdn.example.com/logos/woocommerce.png",
        "status": "active",
        "features": ["order_tracking", "product_sync"],
        "auth_type": "api_key",
        "webhook_support": true
      }
    ]
  }
}
```

### GET /integrations

Get user's active integrations.

**Headers**: `Authorization: Bearer <token>`

**Response**:
```json
{
  "success": true,
  "data": {
    "integrations": [
      {
        "id": "int_123",
        "platform": "shopify",
        "store_name": "My Awesome Store",
        "store_url": "https://my-store.myshopify.com",
        "status": "active",
        "webhook_url": "https://api.your-domain.com/webhooks/shopify/int_123",
        "last_sync": "2025-06-23T16:35:00.000Z",
        "created_at": "2025-06-01T00:00:00.000Z",
        "settings": {
          "sync_orders": true,
          "sync_customers": true,
          "sync_products": false
        }
      }
    ]
  }
}
```

### POST /integrations

Create a new integration.

**Request Body**:
```json
{
  "platform": "shopify",
  "store_name": "My New Store",
  "credentials": {
    "access_token": "shop_access_token_here",
    "shop_domain": "my-new-store.myshopify.com"
  },
  "settings": {
    "sync_orders": true,
    "sync_customers": true,
    "sync_products": true
  }
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "integration": {
      "id": "int_456",
      "platform": "shopify",
      "store_name": "My New Store",
      "status": "pending_verification",
      "webhook_url": "https://api.your-domain.com/webhooks/shopify/int_456"
    }
  }
}
```

### PUT /integrations/:id

Update an integration.

**Request Body**:
```json
{
  "store_name": "Updated Store Name",
  "settings": {
    "sync_orders": true,
    "sync_customers": false,
    "sync_products": true
  }
}
```

### DELETE /integrations/:id

Delete an integration.

### POST /integrations/:id/sync

Trigger manual sync for an integration.

**Response**:
```json
{
  "success": true,
  "data": {
    "sync_job": {
      "id": "sync_789",
      "status": "started",
      "started_at": "2025-06-23T16:40:00.000Z"
    }
  }
}
```

### GET /integrations/:id/sync-status

Get sync status for an integration.

**Response**:
```json
{
  "success": true,
  "data": {
    "sync_status": {
      "last_sync": "2025-06-23T16:35:00.000Z",
      "status": "completed",
      "records_processed": 150,
      "errors": 0,
      "next_sync": "2025-06-23T17:35:00.000Z"
    }
  }
}
```

## OAuth Integration

### GET /auth/:platform/url

Get OAuth authorization URL for a platform.

**Parameters**:
- `platform` - Platform ID (shopify, woocommerce, etc.)

**Query Parameters**:
- `redirect_uri` (optional) - Custom redirect URI

**Response**:
```json
{
  "success": true,
  "data": {
    "auth_url": "https://mystore.myshopify.com/admin/oauth/authorize?client_id=123&scope=read_orders&redirect_uri=...",
    "state": "oauth_state_token"
  }
}
```

### POST /auth/:platform/callback

Handle OAuth callback.

**Request Body**:
```json
{
  "code": "oauth_authorization_code",
  "state": "oauth_state_token",
  "shop": "mystore.myshopify.com"
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "integration": {
      "id": "int_789",
      "platform": "shopify",
      "store_name": "My Store",
      "status": "active"
    }
  }
}
```

## Webhook Management

### GET /webhooks

Get webhook endpoints.

**Response**:
```json
{
  "success": true,
  "data": {
    "webhooks": [
      {
        "id": "webhook_123",
        "integration_id": "int_123",
        "platform": "shopify",
        "event_type": "orders/create",
        "url": "https://api.your-domain.com/webhooks/shopify/orders",
        "status": "active",
        "last_triggered": "2025-06-23T16:30:00.000Z",
        "success_rate": 99.5
      }
    ]
  }
}
```

### POST /webhooks/test

Test webhook endpoint.

**Request Body**:
```json
{
  "integration_id": "int_123",
  "event_type": "orders/create",
  "test_data": {
    "order_id": "12345",
    "total": 150.00
  }
}
```

### GET /webhooks/:id/logs

Get webhook delivery logs.

**Query Parameters**:
- `limit` (optional) - Number of logs (default: 50)
- `status` (optional) - Filter by status: success, failed, pending

**Response**:
```json
{
  "success": true,
  "data": {
    "logs": [
      {
        "id": "log_123",
        "webhook_id": "webhook_123",
        "status": "success",
        "response_code": 200,
        "delivered_at": "2025-06-23T16:30:00.000Z",
        "payload_size": 1024,
        "retry_count": 0
      }
    ]
  }
}
```

## Data Synchronization

### GET /sync/orders

Get synchronized order data.

**Query Parameters**:
- `integration_id` (optional) - Filter by integration
- `start_date` (optional) - Start date
- `end_date` (optional) - End date
- `status` (optional) - Order status
- `limit` (optional) - Number of results

**Response**:
```json
{
  "success": true,
  "data": {
    "orders": [
      {
        "id": "order_123",
        "integration_id": "int_123",
        "platform_order_id": "shopify_12345",
        "customer_email": "<EMAIL>",
        "total": 150.00,
        "currency": "USD",
        "status": "completed",
        "created_at": "2025-06-23T15:30:00.000Z",
        "items": [
          {
            "product_id": "prod_456",
            "name": "Widget",
            "quantity": 2,
            "price": 75.00
          }
        ]
      }
    ],
    "total": 500,
    "page": 1,
    "per_page": 50
  }
}
```

### GET /sync/customers

Get synchronized customer data.

**Response**:
```json
{
  "success": true,
  "data": {
    "customers": [
      {
        "id": "customer_123",
        "integration_id": "int_123",
        "platform_customer_id": "shopify_cust_456",
        "email": "<EMAIL>",
        "first_name": "John",
        "last_name": "Doe",
        "total_orders": 5,
        "total_spent": 750.00,
        "created_at": "2025-05-01T00:00:00.000Z",
        "last_order_date": "2025-06-23T15:30:00.000Z"
      }
    ]
  }
}
```

### GET /sync/products

Get synchronized product data.

**Response**:
```json
{
  "success": true,
  "data": {
    "products": [
      {
        "id": "product_123",
        "integration_id": "int_123",
        "platform_product_id": "shopify_prod_789",
        "name": "Amazing Widget",
        "sku": "WIDGET-001",
        "price": 75.00,
        "currency": "USD",
        "inventory_quantity": 100,
        "status": "active",
        "category": "Widgets",
        "created_at": "2025-06-01T00:00:00.000Z"
      }
    ]
  }
}
```

## Platform-Specific Endpoints

### Shopify Integration

#### GET /shopify/stores/:shop_domain/verify

Verify Shopify store access.

**Response**:
```json
{
  "success": true,
  "data": {
    "store": {
      "name": "My Awesome Store",
      "domain": "my-store.myshopify.com",
      "plan": "basic",
      "currency": "USD",
      "timezone": "America/New_York",
      "permissions": ["read_orders", "read_customers"]
    }
  }
}
```

#### POST /shopify/webhooks/install

Install Shopify webhooks.

**Request Body**:
```json
{
  "integration_id": "int_123",
  "events": ["orders/create", "orders/updated", "customers/create"]
}
```

### WooCommerce Integration

#### POST /woocommerce/verify

Verify WooCommerce API credentials.

**Request Body**:
```json
{
  "store_url": "https://mystore.com",
  "consumer_key": "ck_123456789",
  "consumer_secret": "cs_987654321"
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "store": {
      "name": "My WooCommerce Store",
      "version": "6.5.0",
      "currency": "USD",
      "capabilities": ["orders", "customers", "products"]
    }
  }
}
```

## Webhook Event Types

### Shopify Events
- `orders/create` - New order created
- `orders/updated` - Order status changed
- `orders/cancelled` - Order cancelled
- `customers/create` - New customer registered
- `customers/updated` - Customer information changed
- `products/create` - New product added
- `products/updated` - Product information changed

### WooCommerce Events
- `order.created` - New order placed
- `order.updated` - Order status changed
- `customer.created` - New customer account
- `product.created` - New product added
- `product.updated` - Product modified

## Error Codes

| Code | Description |
|------|-------------|
| `PLATFORM_NOT_SUPPORTED` | Platform is not supported |
| `INVALID_CREDENTIALS` | Invalid API credentials |
| `INTEGRATION_NOT_FOUND` | Integration does not exist |
| `WEBHOOK_DELIVERY_FAILED` | Webhook delivery failed |
| `OAUTH_ERROR` | OAuth authentication error |
| `SYNC_IN_PROGRESS` | Synchronization already running |
| `RATE_LIMITED` | Platform API rate limit exceeded |
| `STORE_ACCESS_DENIED` | Insufficient store permissions |

## Rate Limits

- **OAuth endpoints**: 10 requests per minute
- **Sync endpoints**: 5 requests per minute
- **Webhook endpoints**: 1000 requests per minute
- **Data retrieval**: 100 requests per minute