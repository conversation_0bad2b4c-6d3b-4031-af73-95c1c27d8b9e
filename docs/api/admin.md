# Admin Service API

The Admin Service provides administrative functions and system management capabilities.

**Base URL**: `/admin`

**Note**: All admin endpoints require administrator-level authentication.

## System Management

### GET /system/status

Get overall system status.

**Headers**: `Authorization: Bearer <admin-token>`

**Response**:
```json
{
  "success": true,
  "data": {
    "system": {
      "status": "healthy",
      "version": "1.2.3",
      "uptime": 172800,
      "environment": "production",
      "last_deployment": "2025-06-20T10:30:00.000Z"
    },
    "services": {
      "dashboard": {
        "status": "healthy",
        "uptime": 99.95,
        "response_time": 125.6,
        "memory_usage": "45%",
        "cpu_usage": "12%"
      },
      "analytics": {
        "status": "healthy",
        "uptime": 99.92,
        "response_time": 245.8,
        "memory_usage": "62%",
        "cpu_usage": "28%"
      },
      "integration": {
        "status": "degraded",
        "uptime": 98.5,
        "response_time": 456.2,
        "memory_usage": "78%",
        "cpu_usage": "35%"
      }
    },
    "infrastructure": {
      "database": {
        "status": "healthy",
        "connections_active": 45,
        "connections_max": 100,
        "query_avg_time": 12.5
      },
      "redis": {
        "status": "healthy",
        "memory_used": "1.2GB",
        "memory_max": "4GB",
        "hit_rate": 96.5
      },
      "storage": {
        "status": "healthy",
        "used": "125GB",
        "available": "375GB",
        "usage_percentage": 25.0
      }
    }
  }
}
```

### GET /system/metrics

Get detailed system metrics.

**Query Parameters**:
- `period` (optional) - Time period: 1h, 24h, 7d, 30d
- `granularity` (optional) - Data granularity: 1m, 5m, 1h

**Response**:
```json
{
  "success": true,
  "data": {
    "metrics": {
      "cpu_usage": [
        {
          "timestamp": "2025-06-23T16:35:00.000Z",
          "value": 28.5
        }
      ],
      "memory_usage": [
        {
          "timestamp": "2025-06-23T16:35:00.000Z",
          "value": 62.3
        }
      ],
      "disk_usage": [
        {
          "timestamp": "2025-06-23T16:35:00.000Z",
          "value": 25.0
        }
      ],
      "network_io": [
        {
          "timestamp": "2025-06-23T16:35:00.000Z",
          "in": 1250.5,
          "out": 890.2
        }
      ]
    }
  }
}
```

### POST /system/restart

Restart system services.

**Request Body**:
```json
{
  "services": ["analytics", "integration"],
  "reason": "Performance optimization"
}
```

### POST /system/maintenance

Set maintenance mode.

**Request Body**:
```json
{
  "enabled": true,
  "message": "System maintenance in progress. Expected completion: 30 minutes.",
  "allowed_ips": ["*************", "********"]
}
```

## User Management

### GET /users

Get all users in the system.

**Query Parameters**:
- `page` (optional) - Page number (default: 1)
- `limit` (optional) - Items per page (default: 50)
- `role` (optional) - Filter by role: admin, user, viewer
- `status` (optional) - Filter by status: active, inactive, suspended
- `search` (optional) - Search by name or email

**Response**:
```json
{
  "success": true,
  "data": {
    "users": [
      {
        "id": "user_123",
        "email": "<EMAIL>",
        "name": "John Doe",
        "role": "admin",
        "status": "active",
        "tenant_id": "tenant_456",
        "tenant_name": "Acme Corp",
        "last_login": "2025-06-23T15:30:00.000Z",
        "created_at": "2025-01-01T00:00:00.000Z",
        "login_count": 145,
        "api_usage": {
          "requests_today": 1250,
          "requests_month": 45000
        }
      }
    ],
    "pagination": {
      "total": 1250,
      "page": 1,
      "per_page": 50,
      "pages": 25
    }
  }
}
```

### GET /users/:id

Get detailed user information.

**Response**:
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user_123",
      "email": "<EMAIL>",
      "name": "John Doe",
      "role": "admin",
      "status": "active",
      "tenant_id": "tenant_456",
      "created_at": "2025-01-01T00:00:00.000Z",
      "last_login": "2025-06-23T15:30:00.000Z",
      "login_history": [
        {
          "timestamp": "2025-06-23T15:30:00.000Z",
          "ip_address": "*************",
          "user_agent": "Mozilla/5.0...",
          "success": true
        }
      ],
      "api_usage": {
        "requests_today": 1250,
        "requests_week": 8500,
        "requests_month": 45000,
        "rate_limit": 10000
      },
      "permissions": [
        "analytics:read",
        "dashboard:write",
        "integrations:manage"
      ]
    }
  }
}
```

### PUT /users/:id

Update user information.

**Request Body**:
```json
{
  "name": "John Smith",
  "role": "user",
  "status": "active",
  "permissions": ["analytics:read", "dashboard:read"]
}
```

### POST /users/:id/suspend

Suspend a user account.

**Request Body**:
```json
{
  "reason": "Suspicious activity detected",
  "duration_days": 7
}
```

### POST /users/:id/reset-password

Force password reset for a user.

### DELETE /users/:id

Delete a user account.

**Request Body**:
```json
{
  "reason": "Account closure requested by user"
}
```

## Tenant Management

### GET /tenants

Get all tenants.

**Query Parameters**:
- `page` (optional) - Page number
- `limit` (optional) - Items per page
- `plan` (optional) - Filter by plan: basic, pro, enterprise
- `status` (optional) - Filter by status: trial, active, suspended, cancelled

**Response**:
```json
{
  "success": true,
  "data": {
    "tenants": [
      {
        "id": "tenant_123",
        "name": "Acme Corp",
        "plan": "pro",
        "status": "active",
        "created_at": "2025-01-01T00:00:00.000Z",
        "user_count": 15,
        "monthly_usage": {
          "api_requests": 245000,
          "data_points": 1250000,
          "storage_gb": 12.5
        },
        "billing": {
          "monthly_cost": 299.00,
          "next_billing_date": "2025-07-01T00:00:00.000Z",
          "payment_method": "credit_card"
        }
      }
    ],
    "pagination": {
      "total": 150,
      "page": 1,
      "per_page": 50,
      "pages": 3
    }
  }
}
```

### GET /tenants/:id

Get detailed tenant information.

**Response**:
```json
{
  "success": true,
  "data": {
    "tenant": {
      "id": "tenant_123",
      "name": "Acme Corp",
      "plan": "pro",
      "status": "active",
      "created_at": "2025-01-01T00:00:00.000Z",
      "settings": {
        "api_rate_limit": 1000,
        "data_retention_days": 365,
        "features": ["advanced_analytics", "custom_reports", "api_access"]
      },
      "usage_statistics": {
        "api_requests_today": 5250,
        "api_requests_month": 245000,
        "data_points_month": 1250000,
        "storage_used_gb": 12.5,
        "bandwidth_used_gb": 45.8
      },
      "integrations": [
        {
          "platform": "shopify",
          "store_count": 3,
          "last_sync": "2025-06-23T16:30:00.000Z"
        }
      ],
      "users": [
        {
          "id": "user_456",
          "email": "<EMAIL>",
          "role": "admin",
          "last_login": "2025-06-23T15:30:00.000Z"
        }
      ]
    }
  }
}
```

### PUT /tenants/:id

Update tenant information.

**Request Body**:
```json
{
  "name": "Acme Corporation",
  "plan": "enterprise",
  "settings": {
    "api_rate_limit": 5000,
    "data_retention_days": 730
  }
}
```

### POST /tenants/:id/suspend

Suspend a tenant.

**Request Body**:
```json
{
  "reason": "Payment overdue",
  "notify_users": true
}
```

## Analytics & Usage

### GET /analytics/usage

Get platform usage analytics.

**Query Parameters**:
- `period` (optional) - Time period: 24h, 7d, 30d, 90d
- `metric` (optional) - Specific metric: api_requests, users, tenants, storage

**Response**:
```json
{
  "success": true,
  "data": {
    "usage": {
      "api_requests": {
        "total": 2450000,
        "today": 125000,
        "growth_rate": 15.2
      },
      "active_users": {
        "total": 1250,
        "today": 450,
        "growth_rate": 8.7
      },
      "active_tenants": {
        "total": 150,
        "new_this_month": 12,
        "churn_rate": 2.1
      },
      "storage_usage": {
        "total_gb": 1250.5,
        "growth_gb_month": 125.8,
        "average_per_tenant": 8.3
      }
    },
    "trends": [
      {
        "date": "2025-06-23",
        "api_requests": 125000,
        "active_users": 450,
        "storage_gb": 1250.5
      }
    ]
  }
}
```

### GET /analytics/performance

Get system performance analytics.

**Response**:
```json
{
  "success": true,
  "data": {
    "performance": {
      "average_response_time": {
        "dashboard": 125.6,
        "analytics": 245.8,
        "integration": 456.2,
        "overall": 275.9
      },
      "error_rates": {
        "dashboard": 0.2,
        "analytics": 0.8,
        "integration": 1.5,
        "overall": 0.8
      },
      "uptime": {
        "dashboard": 99.95,
        "analytics": 99.92,
        "integration": 98.5,
        "overall": 99.46
      }
    }
  }
}
```

### GET /analytics/revenue

Get revenue analytics.

**Response**:
```json
{
  "success": true,
  "data": {
    "revenue": {
      "monthly_recurring": 45750.00,
      "annual_recurring": 549000.00,
      "growth_rate": 12.5,
      "churn_rate": 2.1,
      "average_revenue_per_user": 305.00
    },
    "breakdown_by_plan": [
      {
        "plan": "basic",
        "subscribers": 75,
        "revenue": 7500.00,
        "percentage": 16.4
      },
      {
        "plan": "pro",
        "subscribers": 45,
        "revenue": 13455.00,
        "percentage": 29.4
      },
      {
        "plan": "enterprise",
        "subscribers": 30,
        "revenue": 24795.00,
        "percentage": 54.2
      }
    ]
  }
}
```

## Configuration Management

### GET /config

Get system configuration.

**Response**:
```json
{
  "success": true,
  "data": {
    "config": {
      "global_settings": {
        "maintenance_mode": false,
        "registration_enabled": true,
        "api_rate_limiting": true,
        "data_retention_days": 365
      },
      "security": {
        "password_policy": {
          "min_length": 8,
          "require_numbers": true,
          "require_symbols": true,
          "require_uppercase": true
        },
        "session_timeout": 3600,
        "two_factor_required": false
      },
      "integrations": {
        "email_service": "enabled",
        "sms_service": "enabled",
        "slack_notifications": "enabled"
      },
      "billing": {
        "currency": "USD",
        "tax_rate": 8.25,
        "payment_methods": ["credit_card", "bank_transfer"]
      }
    }
  }
}
```

### PUT /config

Update system configuration.

**Request Body**:
```json
{
  "global_settings": {
    "registration_enabled": false,
    "data_retention_days": 730
  },
  "security": {
    "session_timeout": 7200,
    "two_factor_required": true
  }
}
```

## Audit Logs

### GET /audit

Get system audit logs.

**Query Parameters**:
- `user_id` (optional) - Filter by user
- `action` (optional) - Filter by action type
- `resource` (optional) - Filter by resource
- `start_date` (optional) - Start date
- `end_date` (optional) - End date
- `limit` (optional) - Number of results

**Response**:
```json
{
  "success": true,
  "data": {
    "logs": [
      {
        "id": "audit_123",
        "user_id": "user_456",
        "user_email": "<EMAIL>",
        "action": "user_created",
        "resource": "user",
        "resource_id": "user_789",
        "details": {
          "created_user_email": "<EMAIL>",
          "assigned_role": "user"
        },
        "ip_address": "*************",
        "user_agent": "Mozilla/5.0...",
        "timestamp": "2025-06-23T16:35:00.000Z"
      }
    ],
    "pagination": {
      "total": 5000,
      "page": 1,
      "per_page": 100,
      "pages": 50
    }
  }
}
```

## Backup & Restore

### GET /backup/status

Get backup status.

**Response**:
```json
{
  "success": true,
  "data": {
    "backup_status": {
      "last_backup": "2025-06-23T02:00:00.000Z",
      "next_backup": "2025-06-24T02:00:00.000Z",
      "backup_size": "2.5GB",
      "retention_days": 30,
      "success_rate": 99.8
    },
    "recent_backups": [
      {
        "id": "backup_123",
        "timestamp": "2025-06-23T02:00:00.000Z",
        "size": "2.5GB",
        "duration": 45,
        "status": "completed"
      }
    ]
  }
}
```

### POST /backup/create

Create manual backup.

**Request Body**:
```json
{
  "type": "full",
  "description": "Manual backup before maintenance"
}
```

### POST /backup/restore

Restore from backup.

**Request Body**:
```json
{
  "backup_id": "backup_123",
  "confirm": true
}
```

## Error Codes

| Code | Description |
|------|-------------|
| `INSUFFICIENT_PRIVILEGES` | Administrator access required |
| `USER_NOT_FOUND` | User does not exist |
| `TENANT_NOT_FOUND` | Tenant does not exist |
| `INVALID_CONFIGURATION` | Invalid configuration values |
| `BACKUP_NOT_FOUND` | Backup file not found |
| `SYSTEM_MAINTENANCE` | System is in maintenance mode |

## Security Notes

- All admin endpoints require administrator-level authentication
- Sensitive operations are logged in audit trail
- Rate limiting is enforced (100 requests per minute)
- IP whitelisting can be configured for admin access
- Two-factor authentication is recommended for admin accounts