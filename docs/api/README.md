# E-commerce Analytics SaaS API Documentation

## Overview

This document provides comprehensive API documentation for the E-commerce Analytics SaaS platform. The platform consists of multiple microservices that work together to provide real-time analytics for e-commerce platforms.

## Architecture

The platform is built using a microservices architecture with the following services:

- **Dashboard Service** (Port 3001) - Main dashboard and user interface
- **Analytics Service** (Port 3002) - Data processing and analytics
- **Integration Service** (Port 3003) - Third-party platform integrations
- **Error Tracking Service** (Port 3004) - Application monitoring and error tracking
- **Admin Service** (Port 3005) - Administrative functions

## Authentication

All API endpoints require authentication using JWT tokens. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## Base URLs

- **Development**: `http://localhost:3001/api`
- **Staging**: `https://staging-api.your-domain.com/api`
- **Production**: `https://api.your-domain.com/api`

## Common Response Format

All API responses follow a consistent format:

```json
{
  "success": true,
  "data": {},
  "message": "Success message",
  "timestamp": "2025-06-23T16:40:00.000Z"
}
```

### Error Response Format

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Error description",
    "details": {}
  },
  "timestamp": "2025-06-23T16:40:00.000Z"
}
```

## Rate Limiting

API endpoints are rate limited to prevent abuse:

- **Standard endpoints**: 100 requests per minute
- **Analytics endpoints**: 500 requests per minute
- **Webhook endpoints**: 1000 requests per minute

Rate limit headers are included in responses:

```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1640995200
```

## Service Documentation

### [Dashboard Service](./dashboard.md)
Main dashboard API for user management, authentication, and frontend data.

### [Analytics Service](./analytics.md)
Real-time analytics data processing and retrieval.

### [Integration Service](./integration.md)
Third-party platform integrations and webhook management.

### [Error Tracking Service](./error-tracking.md)
Application monitoring, error tracking, and performance metrics.

### [Admin Service](./admin.md)
Administrative functions and system management.

## WebSocket API

Real-time data is available through WebSocket connections:

```javascript
const ws = new WebSocket('ws://localhost:3002/ws');

ws.onopen = () => {
  // Authenticate
  ws.send(JSON.stringify({
    type: 'auth',
    token: 'your-jwt-token'
  }));
  
  // Subscribe to channels
  ws.send(JSON.stringify({
    type: 'subscribe',
    channels: ['live_metrics', 'click_events']
  }));
};

ws.onmessage = (event) => {
  const data = JSON.parse(event.data);
  console.log('Real-time data:', data);
};
```

## SDKs and Client Libraries

- [JavaScript SDK](./sdks/javascript.md)
- [Node.js Client](./sdks/nodejs.md)
- [Python Client](./sdks/python.md)
- [PHP Client](./sdks/php.md)

## Testing

API testing can be performed using the included Postman collection:

- [Postman Collection](../testing/postman-collection.json)
- [Environment Variables](../testing/postman-environment.json)

## Support

For API support and questions:

- **Documentation**: [https://docs.your-domain.com](https://docs.your-domain.com)
- **Support Email**: <EMAIL>
- **Status Page**: [https://status.your-domain.com](https://status.your-domain.com)

## Changelog

See [CHANGELOG.md](./CHANGELOG.md) for API version history and breaking changes.