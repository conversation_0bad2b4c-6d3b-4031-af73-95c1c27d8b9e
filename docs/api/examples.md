# API Usage Examples

This document provides practical examples of how to use the E-commerce Analytics Platform API. All examples include complete request/response payloads and error handling.

## Authentication

### Login and Get Access Token

```bash
curl -X POST https://api.ecommerce-analytics.com/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "securePassword123"
  }'
```

**Response:**
```json
{
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expiresIn": 604800,
  "user": {
    "id": "user_123",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "role": "admin"
  }
}
```

### Using the Access Token

Include the access token in the Authorization header for all subsequent requests:

```bash
curl -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
     https://api.ecommerce-analytics.com/v1/analytics/dashboard
```

## Dashboard Analytics

### Get Dashboard Overview

```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
     "https://api.ecommerce-analytics.com/v1/analytics/dashboard?timeRange=last_30_days&currency=USD"
```

**Response:**
```json
{
  "timeRange": "last_30_days",
  "currency": "USD",
  "metrics": {
    "revenue": {
      "total": 125000.50,
      "growth": 15.2,
      "average": 4166.68,
      "previousPeriod": 108695.22
    },
    "orders": {
      "total": 1250,
      "growth": 8.5,
      "averageValue": 100.00,
      "frequency": 1.8
    },
    "customers": {
      "total": 890,
      "new": 245,
      "returning": 645,
      "retention": 72.5,
      "ltv": 340.50
    },
    "conversion": {
      "rate": 3.2,
      "funnel": [
        {
          "step": "visitors",
          "visitors": 39062,
          "conversions": 39062,
          "rate": 100.0
        },
        {
          "step": "product_views",
          "visitors": 15625,
          "conversions": 15625,
          "rate": 40.0
        },
        {
          "step": "add_to_cart",
          "visitors": 4687,
          "conversions": 4687,
          "rate": 30.0
        },
        {
          "step": "checkout",
          "visitors": 1875,
          "conversions": 1875,
          "rate": 40.0
        },
        {
          "step": "purchase",
          "visitors": 1250,
          "conversions": 1250,
          "rate": 66.7
        }
      ]
    }
  },
  "trends": [
    {
      "date": "2024-01-01",
      "value": 4200.50,
      "metric": "revenue"
    },
    {
      "date": "2024-01-02",
      "value": 3850.25,
      "metric": "revenue"
    }
  ],
  "topProducts": [
    {
      "id": "prod_123",
      "name": "Premium Widget",
      "revenue": 15000.00,
      "orders": 150,
      "units": 300
    }
  ],
  "topChannels": [
    {
      "channel": "organic_search",
      "revenue": 45000.00,
      "orders": 450,
      "customers": 320,
      "roas": 4.5
    }
  ]
}
```

### Get Dashboard with Custom Date Range

```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
     "https://api.ecommerce-analytics.com/v1/analytics/dashboard?timeRange=custom&startDate=2024-01-01&endDate=2024-01-31"
```

## Event Tracking

### Track Purchase Event

```bash
curl -X POST https://api.ecommerce-analytics.com/v1/events \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "eventType": "purchase",
    "customerId": "customer_456",
    "sessionId": "session_789",
    "timestamp": "2024-01-15T14:30:00Z",
    "properties": {
      "orderId": "order_123",
      "products": [
        {
          "id": "prod_123",
          "name": "Premium Widget",
          "price": 99.99,
          "quantity": 2,
          "category": "Electronics"
        }
      ],
      "shipping": 9.99,
      "tax": 16.00,
      "discount": 10.00
    },
    "revenue": 209.97,
    "currency": "USD",
    "channel": "organic_search",
    "source": "google",
    "medium": "organic",
    "campaign": null
  }'
```

**Response:**
```json
{
  "eventId": "event_abc123",
  "status": "accepted",
  "timestamp": "2024-01-15T14:30:05Z"
}
```

### Track Product View Event

```bash
curl -X POST https://api.ecommerce-analytics.com/v1/events \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "eventType": "product_view",
    "customerId": "customer_456",
    "sessionId": "session_789",
    "timestamp": "2024-01-15T14:25:00Z",
    "properties": {
      "productId": "prod_123",
      "productName": "Premium Widget",
      "productPrice": 99.99,
      "productCategory": "Electronics",
      "pageUrl": "/products/premium-widget",
      "referrer": "https://google.com"
    },
    "channel": "organic_search",
    "source": "google",
    "medium": "organic"
  }'
```

### Batch Track Multiple Events

```bash
curl -X POST https://api.ecommerce-analytics.com/v1/events/batch \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "events": [
      {
        "eventType": "page_view",
        "customerId": "customer_456",
        "sessionId": "session_789",
        "timestamp": "2024-01-15T14:20:00Z",
        "properties": {
          "pageUrl": "/",
          "pageTitle": "Homepage"
        }
      },
      {
        "eventType": "product_view",
        "customerId": "customer_456",
        "sessionId": "session_789",
        "timestamp": "2024-01-15T14:25:00Z",
        "properties": {
          "productId": "prod_123",
          "productName": "Premium Widget"
        }
      }
    ]
  }'
```

## Cohort Analysis

### Get Monthly Retention Cohorts

```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
     "https://api.ecommerce-analytics.com/v1/analytics/cohorts?period=monthly&metric=retention&startDate=2023-01-01&endDate=2024-01-31"
```

**Response:**
```json
{
  "period": "monthly",
  "metric": "retention",
  "cohorts": [
    {
      "id": "cohort_2024_01",
      "name": "January 2024",
      "startDate": "2024-01-01",
      "size": 245,
      "periods": [
        {
          "period": 0,
          "value": 100.0,
          "customers": 245,
          "revenue": 24500.00
        },
        {
          "period": 1,
          "value": 65.3,
          "customers": 160,
          "revenue": 19200.00
        },
        {
          "period": 2,
          "value": 42.9,
          "customers": 105,
          "revenue": 15750.00
        }
      ]
    }
  ],
  "summary": {
    "totalCohorts": 12,
    "averageRetention": 58.7,
    "bestPerforming": "cohort_2023_03",
    "worstPerforming": "cohort_2023_08"
  }
}
```

### Get Revenue Cohorts

```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
     "https://api.ecommerce-analytics.com/v1/analytics/cohorts?period=monthly&metric=revenue&segments[]=high_value&segments[]=medium_value"
```

### Get Specific Cohort Details

```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
     "https://api.ecommerce-analytics.com/v1/analytics/cohorts/cohort_2024_01"
```

**Response:**
```json
{
  "id": "cohort_2024_01",
  "name": "January 2024",
  "startDate": "2024-01-01",
  "size": 245,
  "periods": [
    {
      "period": 0,
      "value": 100.0,
      "customers": 245,
      "revenue": 24500.00
    }
  ],
  "segments": [
    {
      "segment": "high_value",
      "size": 49,
      "value": 4900.00
    },
    {
      "segment": "medium_value",
      "size": 98,
      "value": 9800.00
    },
    {
      "segment": "low_value",
      "size": 98,
      "value": 9800.00
    }
  ],
  "metrics": {
    "averageOrderValue": 100.00,
    "purchaseFrequency": 1.2,
    "customerLifetimeValue": 340.50
  }
}
```

## Attribution Analysis

### Get Last-Touch Attribution

```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
     "https://api.ecommerce-analytics.com/v1/analytics/attribution?model=last_touch&timeRange=last_30_days&currency=USD"
```

**Response:**
```json
{
  "model": "last_touch",
  "timeRange": "last_30_days",
  "totalRevenue": 125000.50,
  "channels": [
    {
      "channel": "organic_search",
      "revenue": 45000.00,
      "attribution": 45000.00,
      "percentage": 36.0,
      "touchpoints": 1250
    },
    {
      "channel": "paid_search",
      "revenue": 30000.00,
      "attribution": 30000.00,
      "percentage": 24.0,
      "touchpoints": 800
    },
    {
      "channel": "social_media",
      "revenue": 25000.00,
      "attribution": 25000.00,
      "percentage": 20.0,
      "touchpoints": 650
    },
    {
      "channel": "email",
      "revenue": 15000.00,
      "attribution": 15000.00,
      "percentage": 12.0,
      "touchpoints": 400
    },
    {
      "channel": "direct",
      "revenue": 10000.50,
      "attribution": 10000.50,
      "percentage": 8.0,
      "touchpoints": 350
    }
  ],
  "journeys": [
    {
      "customerId": "customer_456",
      "touchpoints": [
        {
          "channel": "organic_search",
          "timestamp": "2024-01-15T10:00:00Z",
          "attribution": 0.0,
          "position": 1
        },
        {
          "channel": "email",
          "timestamp": "2024-01-15T14:00:00Z",
          "attribution": 199.99,
          "position": 2
        }
      ],
      "revenue": 199.99,
      "conversion": true
    }
  ]
}
```

### Compare Attribution Models

```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
     "https://api.ecommerce-analytics.com/v1/analytics/attribution/compare?models[]=first_touch&models[]=last_touch&models[]=linear&timeRange=last_30_days"
```

**Response:**
```json
{
  "timeRange": "last_30_days",
  "totalRevenue": 125000.50,
  "models": {
    "first_touch": {
      "channels": [
        {
          "channel": "organic_search",
          "attribution": 50000.00,
          "percentage": 40.0
        }
      ]
    },
    "last_touch": {
      "channels": [
        {
          "channel": "organic_search",
          "attribution": 45000.00,
          "percentage": 36.0
        }
      ]
    },
    "linear": {
      "channels": [
        {
          "channel": "organic_search",
          "attribution": 42500.00,
          "percentage": 34.0
        }
      ]
    }
  }
}
```

## Customer Analytics

### Get Customer Segments

```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
     "https://api.ecommerce-analytics.com/v1/analytics/customers?segment=high_value&timeRange=last_30_days"
```

**Response:**
```json
{
  "segment": "high_value",
  "totalCustomers": 178,
  "metrics": {
    "total": 178,
    "new": 32,
    "returning": 146,
    "retention": 82.0,
    "ltv": 850.75
  },
  "distribution": [
    {
      "segment": "high_value",
      "count": 178,
      "percentage": 20.0,
      "averageLtv": 850.75
    },
    {
      "segment": "medium_value",
      "count": 356,
      "percentage": 40.0,
      "averageLtv": 340.50
    },
    {
      "segment": "low_value",
      "count": 356,
      "percentage": 40.0,
      "averageLtv": 125.25
    }
  ],
  "behavioral": {
    "sessionDuration": 485.5,
    "pageViews": 8.2,
    "bounceRate": 25.5,
    "purchaseFrequency": 2.8
  }
}
```

### Get Individual Customer Profile

```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
     "https://api.ecommerce-analytics.com/v1/analytics/customers/customer_456"
```

**Response:**
```json
{
  "id": "customer_456",
  "email": "<EMAIL>",
  "segment": "high_value",
  "firstPurchase": "2023-03-15T10:30:00Z",
  "lastPurchase": "2024-01-15T14:30:00Z",
  "totalRevenue": 1250.75,
  "orderCount": 8,
  "averageOrderValue": 156.34,
  "ltv": 1450.50,
  "clvSegment": "premium",
  "churnRisk": 0.15,
  "journey": [
    {
      "type": "signup",
      "timestamp": "2023-03-10T09:00:00Z",
      "value": 0.0,
      "metadata": {
        "source": "organic_search"
      }
    },
    {
      "type": "first_purchase",
      "timestamp": "2023-03-15T10:30:00Z",
      "value": 89.99,
      "metadata": {
        "orderId": "order_001"
      }
    },
    {
      "type": "purchase",
      "timestamp": "2024-01-15T14:30:00Z",
      "value": 199.99,
      "metadata": {
        "orderId": "order_123"
      }
    }
  ]
}
```

## Forecasting

### Get Revenue Forecast

```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
     "https://api.ecommerce-analytics.com/v1/analytics/forecast?metric=revenue&period=monthly&horizon=12"
```

**Response:**
```json
{
  "metric": "revenue",
  "period": "monthly",
  "horizon": 12,
  "confidence": 0.85,
  "forecasts": [
    {
      "date": "2024-02-01",
      "value": 135000.00,
      "lowerBound": 125000.00,
      "upperBound": 145000.00,
      "confidence": 0.85
    },
    {
      "date": "2024-03-01",
      "value": 142000.00,
      "lowerBound": 130000.00,
      "upperBound": 155000.00,
      "confidence": 0.82
    }
  ],
  "accuracy": {
    "mape": 8.5,
    "rmse": 12500.00,
    "mae": 9800.00
  },
  "seasonality": {
    "detected": true,
    "strength": 0.7,
    "patterns": [
      {
        "type": "yearly",
        "strength": 0.6,
        "peak_months": [11, 12]
      },
      {
        "type": "weekly",
        "strength": 0.3,
        "peak_days": [1, 6, 7]
      }
    ]
  }
}
```

### Get Customer LTV Forecast

```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
     "https://api.ecommerce-analytics.com/v1/analytics/forecast?metric=ltv&period=weekly&horizon=26"
```

## Integrations

### List All Integrations

```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
     "https://api.ecommerce-analytics.com/v1/integrations"
```

**Response:**
```json
{
  "integrations": [
    {
      "id": "integration_123",
      "name": "My Shopify Store",
      "platform": "shopify",
      "status": "active",
      "createdAt": "2024-01-01T00:00:00Z",
      "lastSync": "2024-01-15T12:00:00Z",
      "settings": {
        "shopUrl": "mystore.myshopify.com",
        "syncFrequency": "hourly"
      },
      "webhookUrl": "https://api.ecommerce-analytics.com/v1/webhooks/shopify/integration_123",
      "syncStatus": {
        "lastSync": "2024-01-15T12:00:00Z",
        "status": "success",
        "recordsProcessed": 1250,
        "errors": []
      }
    }
  ]
}
```

### Create Shopify Integration

```bash
curl -X POST https://api.ecommerce-analytics.com/v1/integrations \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "My Shopify Store",
    "platform": "shopify",
    "settings": {
      "shopUrl": "mystore.myshopify.com",
      "accessToken": "shpat_xxxxxxxxxxxxxxxxxxxxx",
      "syncFrequency": "hourly",
      "syncHistorical": true,
      "historicalDays": 90
    }
  }'
```

**Response:**
```json
{
  "id": "integration_456",
  "name": "My Shopify Store",
  "platform": "shopify",
  "status": "pending",
  "createdAt": "2024-01-15T15:00:00Z",
  "lastSync": null,
  "settings": {
    "shopUrl": "mystore.myshopify.com",
    "syncFrequency": "hourly"
  },
  "webhookUrl": "https://api.ecommerce-analytics.com/v1/webhooks/shopify/integration_456",
  "syncStatus": {
    "status": "pending",
    "recordsProcessed": 0,
    "errors": []
  }
}
```

### Update Integration Settings

```bash
curl -X PUT https://api.ecommerce-analytics.com/v1/integrations/integration_456 \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Updated Store Name",
    "settings": {
      "syncFrequency": "daily"
    },
    "status": "active"
  }'
```

## Data Export

### Create Customer Export

```bash
curl -X POST https://api.ecommerce-analytics.com/v1/exports \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "dataType": "customers",
    "format": "csv",
    "filters": {
      "segment": "high_value",
      "registrationDate": {
        "start": "2023-01-01",
        "end": "2024-01-31"
      }
    },
    "startDate": "2023-01-01",
    "endDate": "2024-01-31"
  }'
```

**Response:**
```json
{
  "id": "export_789",
  "dataType": "customers",
  "format": "csv",
  "status": "pending",
  "progress": 0.0,
  "recordCount": null,
  "fileSize": null,
  "downloadUrl": null,
  "createdAt": "2024-01-15T15:30:00Z",
  "completedAt": null,
  "expiresAt": "2024-01-22T15:30:00Z",
  "error": null
}
```

### Check Export Status

```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
     "https://api.ecommerce-analytics.com/v1/exports/export_789"
```

**Response (Completed):**
```json
{
  "id": "export_789",
  "dataType": "customers",
  "format": "csv",
  "status": "completed",
  "progress": 100.0,
  "recordCount": 178,
  "fileSize": 25600,
  "downloadUrl": "https://api.ecommerce-analytics.com/v1/exports/export_789/download",
  "createdAt": "2024-01-15T15:30:00Z",
  "completedAt": "2024-01-15T15:32:00Z",
  "expiresAt": "2024-01-22T15:30:00Z",
  "error": null
}
```

### Download Export File

```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
     "https://api.ecommerce-analytics.com/v1/exports/export_789/download" \
     --output customers_export.csv
```

### List Export History

```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
     "https://api.ecommerce-analytics.com/v1/exports?status=completed&page=1&limit=20"
```

## Error Handling

### Common Error Responses

#### Authentication Error (401)
```json
{
  "error": {
    "message": "Invalid or expired token",
    "code": "AUTHENTICATION_ERROR",
    "status": 401,
    "timestamp": "2024-01-15T15:00:00Z",
    "correlationId": "req_abc123"
  }
}
```

#### Validation Error (400)
```json
{
  "error": {
    "message": "Validation failed",
    "code": "VALIDATION_ERROR",
    "status": 400,
    "timestamp": "2024-01-15T15:00:00Z",
    "correlationId": "req_abc123",
    "details": {
      "errors": [
        {
          "field": "timeRange",
          "message": "Invalid time range value",
          "value": "invalid_range"
        }
      ]
    }
  }
}
```

#### Rate Limit Error (429)
```json
{
  "error": {
    "message": "Rate limit exceeded",
    "code": "RATE_LIMIT_ERROR",
    "status": 429,
    "timestamp": "2024-01-15T15:00:00Z",
    "correlationId": "req_abc123",
    "details": {
      "retryAfter": 300,
      "limit": 1000,
      "remaining": 0,
      "resetTime": "2024-01-15T16:00:00Z"
    }
  }
}
```

#### Not Found Error (404)
```json
{
  "error": {
    "message": "Customer not found",
    "code": "NOT_FOUND_ERROR",
    "status": 404,
    "timestamp": "2024-01-15T15:00:00Z",
    "correlationId": "req_abc123"
  }
}
```

#### Server Error (500)
```json
{
  "error": {
    "message": "Internal server error",
    "code": "INTERNAL_ERROR",
    "status": 500,
    "timestamp": "2024-01-15T15:00:00Z",
    "correlationId": "req_abc123"
  }
}
```

## SDK Examples

### JavaScript/Node.js

```javascript
const AnalyticsAPI = require('@ecommerce-analytics/sdk');

const client = new AnalyticsAPI({
  baseURL: 'https://api.ecommerce-analytics.com/v1',
  accessToken: 'your_access_token_here'
});

// Get dashboard metrics
const dashboard = await client.analytics.getDashboard({
  timeRange: 'last_30_days',
  currency: 'USD'
});

// Track an event
await client.events.track({
  eventType: 'purchase',
  customerId: 'customer_456',
  properties: {
    orderId: 'order_123',
    revenue: 199.99
  }
});

// Get cohort analysis
const cohorts = await client.analytics.getCohorts({
  period: 'monthly',
  metric: 'retention'
});
```

### Python

```python
from ecommerce_analytics import AnalyticsClient

client = AnalyticsClient(
    base_url='https://api.ecommerce-analytics.com/v1',
    access_token='your_access_token_here'
)

# Get dashboard metrics
dashboard = client.analytics.get_dashboard(
    time_range='last_30_days',
    currency='USD'
)

# Track an event
client.events.track({
    'eventType': 'purchase',
    'customerId': 'customer_456',
    'properties': {
        'orderId': 'order_123',
        'revenue': 199.99
    }
})

# Get cohort analysis
cohorts = client.analytics.get_cohorts(
    period='monthly',
    metric='retention'
)
```

### cURL Examples for Testing

#### Create a test script
```bash
#!/bin/bash

# Set your access token
TOKEN="your_access_token_here"
BASE_URL="https://api.ecommerce-analytics.com/v1"

# Test authentication
echo "Testing authentication..."
curl -s -H "Authorization: Bearer $TOKEN" "$BASE_URL/health" | jq

# Test dashboard
echo "Testing dashboard..."
curl -s -H "Authorization: Bearer $TOKEN" "$BASE_URL/analytics/dashboard?timeRange=last_7_days" | jq

# Test event tracking
echo "Testing event tracking..."
curl -s -X POST "$BASE_URL/events" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "eventType": "page_view",
    "customerId": "test_customer",
    "timestamp": "'$(date -Iseconds)'",
    "properties": {
      "pageUrl": "/test-page"
    }
  }' | jq
```

This comprehensive set of examples should help you understand how to interact with the E-commerce Analytics Platform API effectively. Remember to replace placeholder values with your actual data and always handle errors appropriately in your applications.