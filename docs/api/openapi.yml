openapi: 3.0.3
info:
  title: E-commerce Analytics Platform API
  description: |
    Comprehensive analytics platform for e-commerce businesses providing insights into customer behavior, 
    attribution modeling, cohort analysis, and predictive analytics.
    
    ## Features
    - **Real-time Analytics**: Track customer events and behaviors in real-time
    - **Attribution Modeling**: Multi-touch attribution with 6 different models
    - **Cohort Analysis**: Customer lifecycle and retention analysis
    - **Predictive Analytics**: Revenue forecasting and customer value prediction
    - **Integration Support**: Connect with Shopify, WooCommerce, BigCommerce, and more
    
    ## Authentication
    All API endpoints require authentication using JWT tokens. Include the token in the Authorization header:
    ```
    Authorization: Bearer <your-jwt-token>
    ```
    
    ## Rate Limiting
    API requests are rate limited to 1000 requests per hour per API key. Rate limit headers are included in responses.
    
    ## Webhooks
    The platform supports webhooks for real-time event notifications. Configure webhook endpoints in your dashboard.
  version: '1.0.0'
  contact:
    name: E-commerce Analytics Support
    email: <EMAIL>
    url: https://docs.ecommerce-analytics.com
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT
  termsOfService: https://ecommerce-analytics.com/terms

servers:
  - url: https://api.ecommerce-analytics.com/v1
    description: Production server
  - url: https://staging-api.ecommerce-analytics.com/v1
    description: Staging server
  - url: http://localhost:3001/api/v1
    description: Development server

security:
  - bearerAuth: []

paths:
  # Health and Status
  /health:
    get:
      tags:
        - Health
      summary: Health check
      description: Returns the health status of the API and its dependencies
      operationId: getHealth
      security: []
      responses:
        '200':
          description: Service is healthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthResponse'
        '503':
          description: Service is unhealthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /health/ready:
    get:
      tags:
        - Health
      summary: Readiness check
      description: Returns whether the service is ready to accept traffic
      operationId: getReadiness
      security: []
      responses:
        '200':
          description: Service is ready
        '503':
          description: Service is not ready

  # Authentication
  /auth/login:
    post:
      tags:
        - Authentication
      summary: User login
      description: Authenticate user and return JWT token
      operationId: login
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoginRequest'
      responses:
        '200':
          description: Login successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoginResponse'
        '401':
          description: Invalid credentials
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /auth/refresh:
    post:
      tags:
        - Authentication
      summary: Refresh JWT token
      description: Refresh an expired JWT token using refresh token
      operationId: refreshToken
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RefreshTokenRequest'
      responses:
        '200':
          description: Token refreshed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoginResponse'
        '401':
          description: Invalid refresh token

  # Dashboard Analytics
  /analytics/dashboard:
    get:
      tags:
        - Analytics
      summary: Get dashboard metrics
      description: Retrieve comprehensive dashboard metrics for a specific time period
      operationId: getDashboardMetrics
      parameters:
        - name: timeRange
          in: query
          description: Time range for metrics
          required: false
          schema:
            type: string
            enum: [last_7_days, last_30_days, last_90_days, last_6_months, last_year, custom]
            default: last_30_days
        - name: startDate
          in: query
          description: Start date for custom time range (ISO 8601)
          required: false
          schema:
            type: string
            format: date-time
        - name: endDate
          in: query
          description: End date for custom time range (ISO 8601)
          required: false
          schema:
            type: string
            format: date-time
        - name: storeId
          in: query
          description: Store ID to filter metrics
          required: false
          schema:
            type: string
        - name: currency
          in: query
          description: Currency for monetary values
          required: false
          schema:
            type: string
            default: USD
      responses:
        '200':
          description: Dashboard metrics retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DashboardMetrics'
        '400':
          description: Invalid parameters
        '404':
          description: Store not found

  # Cohort Analysis
  /analytics/cohorts:
    get:
      tags:
        - Cohort Analysis
      summary: Get cohort analysis
      description: Retrieve cohort analysis data for customer retention and behavior
      operationId: getCohortAnalysis
      parameters:
        - name: period
          in: query
          description: Cohort period
          required: false
          schema:
            type: string
            enum: [daily, weekly, monthly, quarterly]
            default: monthly
        - name: metric
          in: query
          description: Metric to analyze
          required: false
          schema:
            type: string
            enum: [revenue, retention, orders, customers]
            default: retention
        - name: segments
          in: query
          description: Customer segments to include
          required: false
          schema:
            type: array
            items:
              type: string
        - name: startDate
          in: query
          description: Start date for analysis
          schema:
            type: string
            format: date
        - name: endDate
          in: query
          description: End date for analysis
          schema:
            type: string
            format: date
      responses:
        '200':
          description: Cohort analysis data retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CohortAnalysis'

  /analytics/cohorts/{cohortId}:
    get:
      tags:
        - Cohort Analysis
      summary: Get specific cohort details
      description: Retrieve detailed information about a specific cohort
      operationId: getCohortById
      parameters:
        - name: cohortId
          in: path
          required: true
          description: Cohort ID
          schema:
            type: string
      responses:
        '200':
          description: Cohort details retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CohortDetails'
        '404':
          description: Cohort not found

  # Attribution Analysis
  /analytics/attribution:
    get:
      tags:
        - Attribution
      summary: Get attribution analysis
      description: Retrieve multi-touch attribution analysis for marketing channels
      operationId: getAttribution
      parameters:
        - name: model
          in: query
          description: Attribution model to use
          required: false
          schema:
            type: string
            enum: [first_touch, last_touch, linear, time_decay, position_based, data_driven]
            default: last_touch
        - name: timeRange
          in: query
          description: Time range for attribution analysis
          schema:
            type: string
            enum: [last_7_days, last_30_days, last_90_days]
            default: last_30_days
        - name: channels
          in: query
          description: Marketing channels to include
          schema:
            type: array
            items:
              type: string
        - name: currency
          in: query
          description: Currency for revenue attribution
          schema:
            type: string
            default: USD
      responses:
        '200':
          description: Attribution analysis retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AttributionAnalysis'

  # Customer Analytics
  /analytics/customers:
    get:
      tags:
        - Customer Analytics
      summary: Get customer analytics
      description: Retrieve customer behavior and segmentation analytics
      operationId: getCustomerAnalytics
      parameters:
        - name: segment
          in: query
          description: Customer segment
          schema:
            type: string
            enum: [high_value, medium_value, low_value, at_risk, new, returning]
        - name: timeRange
          in: query
          description: Time range for analysis
          schema:
            type: string
            default: last_30_days
      responses:
        '200':
          description: Customer analytics retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerAnalytics'

  /analytics/customers/{customerId}:
    get:
      tags:
        - Customer Analytics
      summary: Get individual customer analysis
      description: Retrieve detailed analytics for a specific customer
      operationId: getCustomerById
      parameters:
        - name: customerId
          in: path
          required: true
          description: Customer ID
          schema:
            type: string
      responses:
        '200':
          description: Customer analysis retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerProfile'
        '404':
          description: Customer not found

  # Forecasting
  /analytics/forecast:
    get:
      tags:
        - Forecasting
      summary: Get revenue forecast
      description: Retrieve predictive analytics and revenue forecasting
      operationId: getForecast
      parameters:
        - name: metric
          in: query
          description: Metric to forecast
          required: false
          schema:
            type: string
            enum: [revenue, orders, customers, ltv]
            default: revenue
        - name: period
          in: query
          description: Forecast period
          required: false
          schema:
            type: string
            enum: [daily, weekly, monthly]
            default: monthly
        - name: horizon
          in: query
          description: Forecast horizon in periods
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 52
            default: 12
      responses:
        '200':
          description: Forecast data retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ForecastData'

  # Events
  /events:
    post:
      tags:
        - Events
      summary: Track customer event
      description: Track a customer event for analytics processing
      operationId: trackEvent
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EventData'
      responses:
        '201':
          description: Event tracked successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EventResponse'
        '400':
          description: Invalid event data

    get:
      tags:
        - Events
      summary: Get events
      description: Retrieve tracked events with filtering and pagination
      operationId: getEvents
      parameters:
        - name: customerId
          in: query
          description: Filter by customer ID
          schema:
            type: string
        - name: eventType
          in: query
          description: Filter by event type
          schema:
            type: string
        - name: startDate
          in: query
          description: Start date for events
          schema:
            type: string
            format: date-time
        - name: endDate
          in: query
          description: End date for events
          schema:
            type: string
            format: date-time
        - name: page
          in: query
          description: Page number for pagination
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          description: Number of events per page
          schema:
            type: integer
            minimum: 1
            maximum: 1000
            default: 100
      responses:
        '200':
          description: Events retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EventsResponse'

  # Integrations
  /integrations:
    get:
      tags:
        - Integrations
      summary: Get integrations
      description: Retrieve configured integrations
      operationId: getIntegrations
      responses:
        '200':
          description: Integrations retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IntegrationsResponse'

    post:
      tags:
        - Integrations
      summary: Create integration
      description: Create a new integration with an e-commerce platform
      operationId: createIntegration
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateIntegrationRequest'
      responses:
        '201':
          description: Integration created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Integration'
        '400':
          description: Invalid integration data

  /integrations/{integrationId}:
    get:
      tags:
        - Integrations
      summary: Get integration details
      description: Retrieve details of a specific integration
      operationId: getIntegrationById
      parameters:
        - name: integrationId
          in: path
          required: true
          description: Integration ID
          schema:
            type: string
      responses:
        '200':
          description: Integration details retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Integration'
        '404':
          description: Integration not found

    put:
      tags:
        - Integrations
      summary: Update integration
      description: Update an existing integration configuration
      operationId: updateIntegration
      parameters:
        - name: integrationId
          in: path
          required: true
          description: Integration ID
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateIntegrationRequest'
      responses:
        '200':
          description: Integration updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Integration'
        '404':
          description: Integration not found

    delete:
      tags:
        - Integrations
      summary: Delete integration
      description: Delete an integration
      operationId: deleteIntegration
      parameters:
        - name: integrationId
          in: path
          required: true
          description: Integration ID
          schema:
            type: string
      responses:
        '204':
          description: Integration deleted successfully
        '404':
          description: Integration not found

  # Webhooks
  /webhooks:
    post:
      tags:
        - Webhooks
      summary: Receive webhook
      description: Endpoint for receiving webhooks from integrated platforms
      operationId: receiveWebhook
      security: []
      parameters:
        - name: x-webhook-signature
          in: header
          description: Webhook signature for verification
          required: false
          schema:
            type: string
        - name: x-webhook-source
          in: header
          description: Source platform of the webhook
          required: false
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              description: Webhook payload (varies by source)
      responses:
        '200':
          description: Webhook processed successfully
        '400':
          description: Invalid webhook data
        '401':
          description: Invalid webhook signature

  # Exports
  /exports:
    post:
      tags:
        - Exports
      summary: Create data export
      description: Create an export job for analytics data
      operationId: createExport
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateExportRequest'
      responses:
        '201':
          description: Export job created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExportJob'

    get:
      tags:
        - Exports
      summary: Get export jobs
      description: Retrieve export job history
      operationId: getExports
      parameters:
        - name: status
          in: query
          description: Filter by export status
          schema:
            type: string
            enum: [pending, processing, completed, failed]
        - name: page
          in: query
          schema:
            type: integer
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            default: 20
      responses:
        '200':
          description: Export jobs retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExportsResponse'

  /exports/{exportId}:
    get:
      tags:
        - Exports
      summary: Get export job details
      description: Retrieve details of a specific export job
      operationId: getExportById
      parameters:
        - name: exportId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Export job details retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExportJob'
        '404':
          description: Export job not found

  /exports/{exportId}/download:
    get:
      tags:
        - Exports
      summary: Download export file
      description: Download the completed export file
      operationId: downloadExport
      parameters:
        - name: exportId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Export file downloaded successfully
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
        '404':
          description: Export file not found
        '409':
          description: Export not completed yet

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    # Health and Status
    HealthResponse:
      type: object
      properties:
        status:
          type: string
          enum: [healthy, unhealthy, degraded]
        timestamp:
          type: string
          format: date-time
        duration:
          type: number
          description: Health check duration in milliseconds
        checks:
          type: array
          items:
            $ref: '#/components/schemas/HealthCheck'
        service:
          $ref: '#/components/schemas/ServiceInfo'

    HealthCheck:
      type: object
      properties:
        name:
          type: string
        status:
          type: string
          enum: [healthy, unhealthy, degraded]
        category:
          type: string
          enum: [essential, important, optional]
        duration:
          type: number
        lastCheck:
          type: string
          format: date-time
        details:
          type: object
        error:
          type: string

    ServiceInfo:
      type: object
      properties:
        name:
          type: string
        version:
          type: string
        environment:
          type: string
        uptime:
          type: number
        pid:
          type: integer

    # Authentication
    LoginRequest:
      type: object
      required:
        - email
        - password
      properties:
        email:
          type: string
          format: email
          example: <EMAIL>
        password:
          type: string
          format: password
          example: securePassword123

    LoginResponse:
      type: object
      properties:
        accessToken:
          type: string
          description: JWT access token
        refreshToken:
          type: string
          description: Refresh token for obtaining new access tokens
        expiresIn:
          type: integer
          description: Token expiration time in seconds
        user:
          $ref: '#/components/schemas/User'

    RefreshTokenRequest:
      type: object
      required:
        - refreshToken
      properties:
        refreshToken:
          type: string

    User:
      type: object
      properties:
        id:
          type: string
        email:
          type: string
          format: email
        firstName:
          type: string
        lastName:
          type: string
        role:
          type: string
          enum: [admin, user, viewer]
        createdAt:
          type: string
          format: date-time
        lastLogin:
          type: string
          format: date-time

    # Analytics
    DashboardMetrics:
      type: object
      properties:
        timeRange:
          type: string
        currency:
          type: string
        metrics:
          type: object
          properties:
            revenue:
              $ref: '#/components/schemas/RevenueMetrics'
            orders:
              $ref: '#/components/schemas/OrderMetrics'
            customers:
              $ref: '#/components/schemas/CustomerMetrics'
            conversion:
              $ref: '#/components/schemas/ConversionMetrics'
        trends:
          type: array
          items:
            $ref: '#/components/schemas/TrendData'
        topProducts:
          type: array
          items:
            $ref: '#/components/schemas/ProductMetric'
        topChannels:
          type: array
          items:
            $ref: '#/components/schemas/ChannelMetric'

    RevenueMetrics:
      type: object
      properties:
        total:
          type: number
          format: double
        growth:
          type: number
          format: double
        average:
          type: number
          format: double
        previousPeriod:
          type: number
          format: double

    OrderMetrics:
      type: object
      properties:
        total:
          type: integer
        growth:
          type: number
          format: double
        averageValue:
          type: number
          format: double
        frequency:
          type: number
          format: double

    CustomerMetrics:
      type: object
      properties:
        total:
          type: integer
        new:
          type: integer
        returning:
          type: integer
        retention:
          type: number
          format: double
        ltv:
          type: number
          format: double

    ConversionMetrics:
      type: object
      properties:
        rate:
          type: number
          format: double
        funnel:
          type: array
          items:
            $ref: '#/components/schemas/FunnelStep'

    FunnelStep:
      type: object
      properties:
        step:
          type: string
        visitors:
          type: integer
        conversions:
          type: integer
        rate:
          type: number
          format: double

    TrendData:
      type: object
      properties:
        date:
          type: string
          format: date
        value:
          type: number
          format: double
        metric:
          type: string

    ProductMetric:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        revenue:
          type: number
          format: double
        orders:
          type: integer
        units:
          type: integer

    ChannelMetric:
      type: object
      properties:
        channel:
          type: string
        revenue:
          type: number
          format: double
        orders:
          type: integer
        customers:
          type: integer
        roas:
          type: number
          format: double

    # Cohort Analysis
    CohortAnalysis:
      type: object
      properties:
        period:
          type: string
        metric:
          type: string
        cohorts:
          type: array
          items:
            $ref: '#/components/schemas/Cohort'
        summary:
          $ref: '#/components/schemas/CohortSummary'

    Cohort:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        startDate:
          type: string
          format: date
        size:
          type: integer
        periods:
          type: array
          items:
            $ref: '#/components/schemas/CohortPeriod'

    CohortPeriod:
      type: object
      properties:
        period:
          type: integer
        value:
          type: number
          format: double
        customers:
          type: integer
        revenue:
          type: number
          format: double

    CohortSummary:
      type: object
      properties:
        totalCohorts:
          type: integer
        averageRetention:
          type: number
          format: double
        bestPerforming:
          type: string
        worstPerforming:
          type: string

    CohortDetails:
      type: object
      allOf:
        - $ref: '#/components/schemas/Cohort'
        - type: object
          properties:
            segments:
              type: array
              items:
                $ref: '#/components/schemas/CohortSegment'
            metrics:
              type: object

    CohortSegment:
      type: object
      properties:
        segment:
          type: string
        size:
          type: integer
        value:
          type: number
          format: double

    # Attribution
    AttributionAnalysis:
      type: object
      properties:
        model:
          type: string
        timeRange:
          type: string
        totalRevenue:
          type: number
          format: double
        channels:
          type: array
          items:
            $ref: '#/components/schemas/AttributionChannel'
        journeys:
          type: array
          items:
            $ref: '#/components/schemas/CustomerJourney'

    AttributionChannel:
      type: object
      properties:
        channel:
          type: string
        revenue:
          type: number
          format: double
        attribution:
          type: number
          format: double
        percentage:
          type: number
          format: double
        touchpoints:
          type: integer

    CustomerJourney:
      type: object
      properties:
        customerId:
          type: string
        touchpoints:
          type: array
          items:
            $ref: '#/components/schemas/Touchpoint'
        revenue:
          type: number
          format: double
        conversion:
          type: boolean

    Touchpoint:
      type: object
      properties:
        channel:
          type: string
        timestamp:
          type: string
          format: date-time
        attribution:
          type: number
          format: double
        position:
          type: integer

    # Customer Analytics
    CustomerAnalytics:
      type: object
      properties:
        segment:
          type: string
        totalCustomers:
          type: integer
        metrics:
          $ref: '#/components/schemas/CustomerMetrics'
        distribution:
          type: array
          items:
            $ref: '#/components/schemas/CustomerDistribution'
        behavioral:
          $ref: '#/components/schemas/BehavioralMetrics'

    CustomerProfile:
      type: object
      properties:
        id:
          type: string
        email:
          type: string
        segment:
          type: string
        firstPurchase:
          type: string
          format: date-time
        lastPurchase:
          type: string
          format: date-time
        totalRevenue:
          type: number
          format: double
        orderCount:
          type: integer
        averageOrderValue:
          type: number
          format: double
        ltv:
          type: number
          format: double
        clvSegment:
          type: string
        churnRisk:
          type: number
          format: double
        journey:
          type: array
          items:
            $ref: '#/components/schemas/CustomerEvent'

    CustomerDistribution:
      type: object
      properties:
        segment:
          type: string
        count:
          type: integer
        percentage:
          type: number
          format: double
        averageLtv:
          type: number
          format: double

    BehavioralMetrics:
      type: object
      properties:
        sessionDuration:
          type: number
          format: double
        pageViews:
          type: number
          format: double
        bounceRate:
          type: number
          format: double
        purchaseFrequency:
          type: number
          format: double

    CustomerEvent:
      type: object
      properties:
        type:
          type: string
        timestamp:
          type: string
          format: date-time
        value:
          type: number
          format: double
        metadata:
          type: object

    # Forecasting
    ForecastData:
      type: object
      properties:
        metric:
          type: string
        period:
          type: string
        horizon:
          type: integer
        confidence:
          type: number
          format: double
        forecasts:
          type: array
          items:
            $ref: '#/components/schemas/ForecastPoint'
        accuracy:
          $ref: '#/components/schemas/ForecastAccuracy'
        seasonality:
          $ref: '#/components/schemas/SeasonalityData'

    ForecastPoint:
      type: object
      properties:
        date:
          type: string
          format: date
        value:
          type: number
          format: double
        lowerBound:
          type: number
          format: double
        upperBound:
          type: number
          format: double
        confidence:
          type: number
          format: double

    ForecastAccuracy:
      type: object
      properties:
        mape:
          type: number
          format: double
        rmse:
          type: number
          format: double
        mae:
          type: number
          format: double

    SeasonalityData:
      type: object
      properties:
        detected:
          type: boolean
        strength:
          type: number
          format: double
        patterns:
          type: array
          items:
            type: object

    # Events
    EventData:
      type: object
      required:
        - eventType
        - customerId
        - timestamp
      properties:
        eventType:
          type: string
          enum: [page_view, product_view, add_to_cart, purchase, signup, login]
        customerId:
          type: string
        sessionId:
          type: string
        timestamp:
          type: string
          format: date-time
        properties:
          type: object
          additionalProperties: true
        revenue:
          type: number
          format: double
        currency:
          type: string
        channel:
          type: string
        source:
          type: string
        medium:
          type: string
        campaign:
          type: string

    EventResponse:
      type: object
      properties:
        eventId:
          type: string
        status:
          type: string
          enum: [accepted, processed, queued]
        timestamp:
          type: string
          format: date-time

    EventsResponse:
      type: object
      properties:
        events:
          type: array
          items:
            $ref: '#/components/schemas/Event'
        pagination:
          $ref: '#/components/schemas/Pagination'

    Event:
      type: object
      allOf:
        - $ref: '#/components/schemas/EventData'
        - type: object
          properties:
            id:
              type: string
            processedAt:
              type: string
              format: date-time

    Pagination:
      type: object
      properties:
        page:
          type: integer
        limit:
          type: integer
        total:
          type: integer
        pages:
          type: integer
        hasNext:
          type: boolean
        hasPrev:
          type: boolean

    # Integrations
    IntegrationsResponse:
      type: object
      properties:
        integrations:
          type: array
          items:
            $ref: '#/components/schemas/Integration'

    Integration:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        platform:
          type: string
          enum: [shopify, woocommerce, bigcommerce, magento, custom]
        status:
          type: string
          enum: [active, inactive, error, pending]
        createdAt:
          type: string
          format: date-time
        lastSync:
          type: string
          format: date-time
        settings:
          type: object
        webhookUrl:
          type: string
        syncStatus:
          $ref: '#/components/schemas/SyncStatus'

    CreateIntegrationRequest:
      type: object
      required:
        - name
        - platform
      properties:
        name:
          type: string
        platform:
          type: string
          enum: [shopify, woocommerce, bigcommerce, magento, custom]
        settings:
          type: object
          additionalProperties: true

    UpdateIntegrationRequest:
      type: object
      properties:
        name:
          type: string
        settings:
          type: object
          additionalProperties: true
        status:
          type: string
          enum: [active, inactive]

    SyncStatus:
      type: object
      properties:
        lastSync:
          type: string
          format: date-time
        status:
          type: string
          enum: [success, failed, in_progress]
        recordsProcessed:
          type: integer
        errors:
          type: array
          items:
            type: string

    # Exports
    CreateExportRequest:
      type: object
      required:
        - dataType
        - format
      properties:
        dataType:
          type: string
          enum: [customers, orders, events, analytics, cohorts, attribution]
        format:
          type: string
          enum: [csv, json, xlsx]
        filters:
          type: object
          additionalProperties: true
        startDate:
          type: string
          format: date
        endDate:
          type: string
          format: date

    ExportJob:
      type: object
      properties:
        id:
          type: string
        dataType:
          type: string
        format:
          type: string
        status:
          type: string
          enum: [pending, processing, completed, failed]
        progress:
          type: number
          format: double
        recordCount:
          type: integer
        fileSize:
          type: integer
        downloadUrl:
          type: string
        createdAt:
          type: string
          format: date-time
        completedAt:
          type: string
          format: date-time
        expiresAt:
          type: string
          format: date-time
        error:
          type: string

    ExportsResponse:
      type: object
      properties:
        exports:
          type: array
          items:
            $ref: '#/components/schemas/ExportJob'
        pagination:
          $ref: '#/components/schemas/Pagination'

    # Common
    ErrorResponse:
      type: object
      properties:
        error:
          type: object
          properties:
            message:
              type: string
            code:
              type: string
            status:
              type: integer
            timestamp:
              type: string
              format: date-time
            correlationId:
              type: string
            details:
              type: object

tags:
  - name: Health
    description: Service health and status endpoints
  - name: Authentication
    description: User authentication and authorization
  - name: Analytics
    description: Core analytics and dashboard metrics
  - name: Cohort Analysis
    description: Customer cohort and retention analysis
  - name: Attribution
    description: Multi-touch attribution modeling
  - name: Customer Analytics
    description: Customer behavior and segmentation
  - name: Forecasting
    description: Predictive analytics and forecasting
  - name: Events
    description: Event tracking and management
  - name: Integrations
    description: E-commerce platform integrations
  - name: Webhooks
    description: Webhook endpoints for real-time data
  - name: Exports
    description: Data export and download functionality