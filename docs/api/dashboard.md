# Dashboard Service API

The Dashboard Service provides user management, authentication, and main dashboard functionality.

**Base URL**: `/dashboard`

## Authentication Endpoints

### POST /auth/login

User authentication.

**Request Body**:
```json
{
  "email": "<EMAIL>",
  "password": "securepassword",
  "remember_me": true
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user_123",
      "email": "<EMAIL>",
      "name": "<PERSON>",
      "role": "admin",
      "tenant_id": "tenant_456"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_at": "2025-06-24T16:40:00.000Z"
  }
}
```

### POST /auth/register

User registration.

**Request Body**:
```json
{
  "email": "<EMAIL>",
  "password": "securepassword",
  "name": "<PERSON>",
  "company": "Acme Corp"
}
```

### POST /auth/logout

User logout.

**Headers**: `Authorization: Bearer <token>`

### POST /auth/refresh

Refresh authentication token.

**Request Body**:
```json
{
  "refresh_token": "refresh_token_here"
}
```

### POST /auth/forgot-password

Request password reset.

**Request Body**:
```json
{
  "email": "<EMAIL>"
}
```

### POST /auth/reset-password

Reset password with token.

**Request Body**:
```json
{
  "token": "reset_token_here",
  "password": "newpassword"
}
```

## User Management

### GET /users/profile

Get current user profile.

**Headers**: `Authorization: Bearer <token>`

**Response**:
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user_123",
      "email": "<EMAIL>",
      "name": "John Doe",
      "role": "admin",
      "tenant_id": "tenant_456",
      "created_at": "2025-01-01T00:00:00.000Z",
      "last_login": "2025-06-23T16:40:00.000Z",
      "preferences": {
        "theme": "dark",
        "notifications": true,
        "timezone": "UTC"
      }
    }
  }
}
```

### PUT /users/profile

Update user profile.

**Request Body**:
```json
{
  "name": "John Smith",
  "preferences": {
    "theme": "light",
    "notifications": false,
    "timezone": "America/New_York"
  }
}
```

### PUT /users/password

Change user password.

**Request Body**:
```json
{
  "current_password": "oldpassword",
  "new_password": "newpassword"
}
```

## Tenant Management

### GET /tenants

Get tenant information.

**Response**:
```json
{
  "success": true,
  "data": {
    "tenant": {
      "id": "tenant_456",
      "name": "Acme Corp",
      "plan": "pro",
      "status": "active",
      "created_at": "2025-01-01T00:00:00.000Z",
      "settings": {
        "api_rate_limit": 1000,
        "data_retention_days": 365,
        "features": ["advanced_analytics", "custom_reports"]
      }
    }
  }
}
```

### PUT /tenants/settings

Update tenant settings.

**Request Body**:
```json
{
  "settings": {
    "api_rate_limit": 2000,
    "data_retention_days": 730
  }
}
```

## Dashboard Data

### GET /dashboard/overview

Get dashboard overview data.

**Query Parameters**:
- `period` (optional) - Time period: today, week, month, quarter, year

**Response**:
```json
{
  "success": true,
  "data": {
    "metrics": {
      "total_clicks": 1250,
      "total_conversions": 45,
      "conversion_rate": 3.6,
      "revenue": 12500.00,
      "change_from_previous": {
        "clicks": 15.2,
        "conversions": 8.7,
        "conversion_rate": -2.1,
        "revenue": 22.1
      }
    },
    "charts": {
      "clicks_over_time": [
        {"date": "2025-06-23", "value": 125},
        {"date": "2025-06-22", "value": 108}
      ],
      "top_platforms": [
        {"platform": "shopify", "percentage": 68.0},
        {"platform": "woocommerce", "percentage": 32.0}
      ]
    },
    "recent_activity": [
      {
        "type": "conversion",
        "description": "New order on Shopify",
        "value": "$150.00",
        "timestamp": "2025-06-23T16:35:00.000Z"
      }
    ]
  }
}
```

### GET /dashboard/widgets

Get available dashboard widgets.

**Response**:
```json
{
  "success": true,
  "data": {
    "widgets": [
      {
        "id": "revenue_chart",
        "name": "Revenue Chart",
        "type": "chart",
        "description": "Daily revenue trends",
        "settings": {
          "period": "30d",
          "chart_type": "line"
        }
      },
      {
        "id": "conversion_funnel",
        "name": "Conversion Funnel",
        "type": "funnel",
        "description": "Customer journey analysis"
      }
    ]
  }
}
```

### POST /dashboard/widgets

Add widget to dashboard.

**Request Body**:
```json
{
  "widget_id": "revenue_chart",
  "position": {
    "x": 0,
    "y": 0,
    "width": 6,
    "height": 4
  },
  "settings": {
    "period": "7d",
    "chart_type": "bar"
  }
}
```

### GET /dashboard/reports

Get available reports.

**Response**:
```json
{
  "success": true,
  "data": {
    "reports": [
      {
        "id": "weekly_summary",
        "name": "Weekly Summary",
        "description": "Comprehensive weekly performance report",
        "schedule": "weekly",
        "last_generated": "2025-06-23T00:00:00.000Z",
        "size": "2.3 MB",
        "format": "pdf"
      }
    ]
  }
}
```

### POST /dashboard/reports/generate

Generate a custom report.

**Request Body**:
```json
{
  "name": "Monthly Analytics Report",
  "type": "custom",
  "period": {
    "start": "2025-06-01",
    "end": "2025-06-30"
  },
  "metrics": ["clicks", "conversions", "revenue"],
  "format": "pdf",
  "email_to": ["<EMAIL>"]
}
```

## Link Management

### GET /links

Get managed links.

**Query Parameters**:
- `page` (optional) - Page number (default: 1)
- `limit` (optional) - Items per page (default: 20)
- `search` (optional) - Search term
- `status` (optional) - Filter by status: active, inactive, archived

**Response**:
```json
{
  "success": true,
  "data": {
    "links": [
      {
        "id": "link_123",
        "title": "Summer Sale Campaign",
        "url": "https://store.com/summer-sale",
        "short_url": "https://track.ly/abc123",
        "status": "active",
        "clicks": 1250,
        "conversions": 45,
        "created_at": "2025-06-01T00:00:00.000Z"
      }
    ],
    "pagination": {
      "page": 1,
      "per_page": 20,
      "total": 150,
      "pages": 8
    }
  }
}
```

### POST /links

Create a new tracked link.

**Request Body**:
```json
{
  "title": "New Campaign",
  "url": "https://store.com/new-product",
  "campaign": "product_launch",
  "source": "email",
  "medium": "newsletter",
  "custom_domain": "track.ly"
}
```

### PUT /links/:id

Update a link.

**Request Body**:
```json
{
  "title": "Updated Campaign Name",
  "status": "active"
}
```

### DELETE /links/:id

Delete a link.

### GET /links/:id/analytics

Get analytics for a specific link.

**Response**:
```json
{
  "success": true,
  "data": {
    "link": {
      "id": "link_123",
      "title": "Summer Sale Campaign",
      "url": "https://store.com/summer-sale"
    },
    "analytics": {
      "total_clicks": 1250,
      "unique_clicks": 890,
      "conversions": 45,
      "conversion_rate": 3.6,
      "revenue": 6750.00,
      "click_history": [
        {"date": "2025-06-23", "clicks": 125},
        {"date": "2025-06-22", "clicks": 108}
      ],
      "geographic_data": [
        {"country": "US", "clicks": 750},
        {"country": "CA", "clicks": 250}
      ],
      "device_breakdown": [
        {"device": "desktop", "clicks": 625},
        {"device": "mobile", "clicks": 425}
      ]
    }
  }
}
```

## Notifications

### GET /notifications

Get user notifications.

**Query Parameters**:
- `unread_only` (optional) - Show only unread notifications
- `limit` (optional) - Number of notifications (default: 50)

**Response**:
```json
{
  "success": true,
  "data": {
    "notifications": [
      {
        "id": "notif_123",
        "type": "alert",
        "title": "High Traffic Alert",
        "message": "Your campaign is receiving unusually high traffic",
        "read": false,
        "created_at": "2025-06-23T16:40:00.000Z",
        "data": {
          "link_id": "link_123",
          "traffic_increase": "300%"
        }
      }
    ],
    "unread_count": 5
  }
}
```

### PUT /notifications/:id/read

Mark notification as read.

### POST /notifications/mark-all-read

Mark all notifications as read.

## Settings

### GET /settings

Get user settings.

**Response**:
```json
{
  "success": true,
  "data": {
    "settings": {
      "notifications": {
        "email": true,
        "push": false,
        "alerts": true
      },
      "dashboard": {
        "theme": "dark",
        "layout": "grid",
        "widgets": ["revenue", "clicks", "conversions"]
      },
      "integrations": {
        "google_analytics": {
          "enabled": true,
          "tracking_id": "UA-123456-1"
        },
        "facebook_pixel": {
          "enabled": false,
          "pixel_id": ""
        }
      }
    }
  }
}
```

### PUT /settings

Update user settings.

**Request Body**:
```json
{
  "notifications": {
    "email": false,
    "push": true
  },
  "dashboard": {
    "theme": "light"
  }
}
```

## Error Codes

| Code | Description |
|------|-------------|
| `INVALID_CREDENTIALS` | Invalid email or password |
| `USER_NOT_FOUND` | User does not exist |
| `EMAIL_ALREADY_EXISTS` | Email already registered |
| `INVALID_TOKEN` | Invalid or expired token |
| `PASSWORD_TOO_WEAK` | Password doesn't meet requirements |
| `LINK_NOT_FOUND` | Requested link not found |
| `UNAUTHORIZED_ACCESS` | Insufficient permissions |