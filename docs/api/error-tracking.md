# Error Tracking Service API

The Error Tracking Service provides application monitoring, error tracking, and performance metrics.

**Base URL**: `/error-tracking`

## Error Management

### GET /errors

Get application errors.

**Query Parameters**:
- `status` (optional) - Filter by status: new, acknowledged, resolved, ignored
- `severity` (optional) - Filter by severity: low, medium, high, critical
- `service` (optional) - Filter by service name
- `start_date` (optional) - Start date (ISO 8601)
- `end_date` (optional) - End date (ISO 8601)
- `limit` (optional) - Number of results (default: 50)
- `offset` (optional) - Pagination offset

**Response**:
```json
{
  "success": true,
  "data": {
    "errors": [
      {
        "id": "error_123",
        "fingerprint": "DatabaseConnectionError-analytics-service",
        "message": "Connection to database failed",
        "service": "analytics-service",
        "severity": "high",
        "status": "new",
        "first_seen": "2025-06-23T15:30:00.000Z",
        "last_seen": "2025-06-23T16:35:00.000Z",
        "count": 15,
        "users_affected": 8,
        "stack_trace": "Error: Connection timeout\n    at Database.connect...",
        "context": {
          "environment": "production",
          "version": "1.2.3",
          "user_id": "user_456",
          "request_id": "req_789"
        }
      }
    ],
    "pagination": {
      "total": 250,
      "page": 1,
      "per_page": 50,
      "pages": 5
    }
  }
}
```

### GET /errors/:id

Get detailed error information.

**Response**:
```json
{
  "success": true,
  "data": {
    "error": {
      "id": "error_123",
      "fingerprint": "DatabaseConnectionError-analytics-service",
      "message": "Connection to database failed",
      "service": "analytics-service",
      "severity": "high",
      "status": "new",
      "first_seen": "2025-06-23T15:30:00.000Z",
      "last_seen": "2025-06-23T16:35:00.000Z",
      "count": 15,
      "users_affected": 8,
      "stack_trace": "Error: Connection timeout\n    at Database.connect (db.js:45:12)\n    at async AnalyticsService.getData (analytics.js:123:5)",
      "breadcrumbs": [
        {
          "timestamp": "2025-06-23T16:34:58.000Z",
          "category": "query",
          "message": "Executing query: SELECT * FROM analytics",
          "level": "info"
        },
        {
          "timestamp": "2025-06-23T16:35:00.000Z",
          "category": "error",
          "message": "Database connection timeout",
          "level": "error"
        }
      ],
      "context": {
        "environment": "production",
        "version": "1.2.3",
        "user_id": "user_456",
        "request_id": "req_789",
        "url": "/api/analytics/summary",
        "method": "GET",
        "ip_address": "*************"
      },
      "occurrences": [
        {
          "id": "occ_456",
          "timestamp": "2025-06-23T16:35:00.000Z",
          "user_id": "user_456",
          "context": {
            "request_id": "req_789"
          }
        }
      ]
    }
  }
}
```

### POST /errors

Report a new error.

**Request Body**:
```json
{
  "message": "Database connection failed",
  "service": "analytics-service",
  "severity": "high",
  "stack_trace": "Error: Connection timeout\n    at Database.connect...",
  "context": {
    "environment": "production",
    "version": "1.2.3",
    "user_id": "user_456",
    "request_id": "req_789",
    "url": "/api/analytics/summary",
    "method": "GET"
  },
  "breadcrumbs": [
    {
      "timestamp": "2025-06-23T16:34:58.000Z",
      "category": "query",
      "message": "Executing database query",
      "level": "info"
    }
  ]
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "error": {
      "id": "error_456",
      "fingerprint": "DatabaseConnectionError-analytics-service",
      "status": "new"
    }
  }
}
```

### PUT /errors/:id/status

Update error status.

**Request Body**:
```json
{
  "status": "resolved",
  "resolution_note": "Fixed database connection pool settings"
}
```

### POST /errors/:id/ignore

Ignore an error (won't trigger alerts).

### POST /errors/bulk-action

Perform bulk actions on multiple errors.

**Request Body**:
```json
{
  "action": "resolve",
  "error_ids": ["error_123", "error_456", "error_789"],
  "note": "Fixed in version 1.2.4"
}
```

## Performance Monitoring

### GET /performance/overview

Get performance overview.

**Query Parameters**:
- `period` (optional) - Time period: 1h, 24h, 7d, 30d
- `service` (optional) - Filter by service

**Response**:
```json
{
  "success": true,
  "data": {
    "overview": {
      "average_response_time": 245.6,
      "error_rate": 0.8,
      "throughput": 1250.5,
      "apdex_score": 0.94,
      "availability": 99.9
    },
    "trends": {
      "response_time_change": -12.3,
      "error_rate_change": 15.7,
      "throughput_change": 8.4
    },
    "services": [
      {
        "name": "analytics-service",
        "avg_response_time": 180.2,
        "error_rate": 0.5,
        "throughput": 450.3
      },
      {
        "name": "dashboard-service",
        "avg_response_time": 125.8,
        "error_rate": 0.2,
        "throughput": 800.2
      }
    ]
  }
}
```

### GET /performance/transactions

Get transaction performance data.

**Query Parameters**:
- `service` (optional) - Filter by service
- `endpoint` (optional) - Filter by endpoint
- `start_date` (optional) - Start date
- `end_date` (optional) - End date

**Response**:
```json
{
  "success": true,
  "data": {
    "transactions": [
      {
        "name": "GET /api/analytics/summary",
        "service": "analytics-service",
        "avg_response_time": 245.6,
        "median_response_time": 198.3,
        "p95_response_time": 456.2,
        "p99_response_time": 789.1,
        "throughput": 125.5,
        "error_rate": 1.2,
        "apdex_score": 0.89
      }
    ]
  }
}
```

### GET /performance/metrics

Get detailed performance metrics.

**Query Parameters**:
- `metric` - Metric type: response_time, throughput, error_rate, memory_usage, cpu_usage
- `granularity` (optional) - Data granularity: 1m, 5m, 1h, 1d

**Response**:
```json
{
  "success": true,
  "data": {
    "metric": "response_time",
    "unit": "milliseconds",
    "data_points": [
      {
        "timestamp": "2025-06-23T16:00:00.000Z",
        "value": 245.6,
        "service": "analytics-service"
      },
      {
        "timestamp": "2025-06-23T16:05:00.000Z",
        "value": 198.3,
        "service": "analytics-service"
      }
    ]
  }
}
```

### POST /performance/events

Track a performance event.

**Request Body**:
```json
{
  "type": "transaction",
  "name": "GET /api/analytics/summary",
  "service": "analytics-service",
  "duration": 245.6,
  "timestamp": "2025-06-23T16:35:00.000Z",
  "status": "success",
  "context": {
    "user_id": "user_456",
    "request_id": "req_789",
    "memory_usage": 245.8,
    "cpu_usage": 12.3
  }
}
```

## Health Monitoring

### GET /health/services

Get health status of all services.

**Response**:
```json
{
  "success": true,
  "data": {
    "services": [
      {
        "name": "analytics-service",
        "status": "healthy",
        "uptime": 99.95,
        "last_check": "2025-06-23T16:40:00.000Z",
        "response_time": 15.6,
        "checks": {
          "database": "healthy",
          "redis": "healthy",
          "memory": "healthy",
          "cpu": "healthy"
        }
      },
      {
        "name": "dashboard-service",
        "status": "degraded",
        "uptime": 98.2,
        "last_check": "2025-06-23T16:40:00.000Z",
        "response_time": 45.2,
        "checks": {
          "database": "healthy",
          "redis": "unhealthy",
          "memory": "warning",
          "cpu": "healthy"
        }
      }
    ],
    "overall_status": "degraded"
  }
}
```

### GET /health/incidents

Get incident history.

**Response**:
```json
{
  "success": true,
  "data": {
    "incidents": [
      {
        "id": "incident_123",
        "title": "Analytics Service Database Connection Issues",
        "status": "resolved",
        "severity": "major",
        "started_at": "2025-06-23T14:30:00.000Z",
        "resolved_at": "2025-06-23T15:45:00.000Z",
        "duration": 75,
        "affected_services": ["analytics-service"],
        "timeline": [
          {
            "timestamp": "2025-06-23T14:30:00.000Z",
            "status": "investigating",
            "message": "Investigating database connection issues"
          },
          {
            "timestamp": "2025-06-23T15:45:00.000Z",
            "status": "resolved",
            "message": "Database connection pool settings adjusted"
          }
        ]
      }
    ]
  }
}
```

### POST /health/incidents

Create a new incident.

**Request Body**:
```json
{
  "title": "Service Outage",
  "severity": "critical",
  "affected_services": ["analytics-service", "dashboard-service"],
  "description": "Multiple services experiencing high error rates"
}
```

## Alerting

### GET /alerts

Get alert rules.

**Response**:
```json
{
  "success": true,
  "data": {
    "alerts": [
      {
        "id": "alert_123",
        "name": "High Error Rate",
        "description": "Triggers when error rate exceeds 5%",
        "enabled": true,
        "conditions": {
          "metric": "error_rate",
          "operator": "greater_than",
          "threshold": 5.0,
          "duration": "5m"
        },
        "notifications": {
          "email": ["<EMAIL>"],
          "slack": ["#alerts"],
          "webhook": ["https://hooks.slack.com/..."]
        },
        "last_triggered": "2025-06-22T10:30:00.000Z"
      }
    ]
  }
}
```

### POST /alerts

Create a new alert rule.

**Request Body**:
```json
{
  "name": "Database Connection Alert",
  "description": "Alert when database connections fail",
  "enabled": true,
  "conditions": {
    "metric": "database_errors",
    "operator": "greater_than",
    "threshold": 10,
    "duration": "2m",
    "service": "analytics-service"
  },
  "notifications": {
    "email": ["<EMAIL>"],
    "slack": ["#critical-alerts"]
  }
}
```

### PUT /alerts/:id

Update an alert rule.

### DELETE /alerts/:id

Delete an alert rule.

### GET /alerts/history

Get alert history.

**Response**:
```json
{
  "success": true,
  "data": {
    "history": [
      {
        "id": "alert_trigger_456",
        "alert_id": "alert_123",
        "alert_name": "High Error Rate",
        "triggered_at": "2025-06-23T16:35:00.000Z",
        "resolved_at": "2025-06-23T16:42:00.000Z",
        "duration": 7,
        "severity": "warning",
        "value": 7.2,
        "threshold": 5.0
      }
    ]
  }
}
```

## Analytics & Reporting

### GET /reports/errors

Get error analytics report.

**Query Parameters**:
- `period` (optional) - Time period: 24h, 7d, 30d
- `group_by` (optional) - Group by: service, severity, date

**Response**:
```json
{
  "success": true,
  "data": {
    "summary": {
      "total_errors": 1250,
      "unique_errors": 45,
      "users_affected": 234,
      "resolution_time_avg": 45.6
    },
    "breakdown": [
      {
        "service": "analytics-service",
        "error_count": 567,
        "percentage": 45.4
      },
      {
        "service": "dashboard-service",
        "error_count": 423,
        "percentage": 33.8
      }
    ],
    "trends": [
      {
        "date": "2025-06-23",
        "errors": 125,
        "resolved": 98
      }
    ]
  }
}
```

### GET /reports/performance

Get performance analytics report.

**Response**:
```json
{
  "success": true,
  "data": {
    "summary": {
      "avg_response_time": 245.6,
      "median_response_time": 198.3,
      "throughput": 1250.5,
      "error_rate": 0.8,
      "apdex_score": 0.94
    },
    "slowest_endpoints": [
      {
        "endpoint": "GET /api/analytics/reports",
        "avg_response_time": 1250.6,
        "call_count": 450
      }
    ],
    "performance_trends": [
      {
        "hour": "16:00",
        "avg_response_time": 245.6,
        "throughput": 125.5
      }
    ]
  }
}
```

## Configuration

### GET /config

Get error tracking configuration.

**Response**:
```json
{
  "success": true,
  "data": {
    "config": {
      "retention_days": 90,
      "sampling_rate": 1.0,
      "alert_cooldown": 300,
      "auto_resolve": true,
      "auto_resolve_days": 7,
      "notification_settings": {
        "email_enabled": true,
        "slack_enabled": true,
        "webhook_enabled": false
      }
    }
  }
}
```

### PUT /config

Update configuration.

**Request Body**:
```json
{
  "retention_days": 180,
  "sampling_rate": 0.5,
  "auto_resolve_days": 14
}
```

## Error Codes

| Code | Description |
|------|-------------|
| `ERROR_NOT_FOUND` | Error record not found |
| `INVALID_SEVERITY` | Invalid severity level |
| `ALERT_RULE_EXISTS` | Alert rule already exists |
| `INSUFFICIENT_DATA` | Not enough data for analysis |
| `SERVICE_UNAVAILABLE` | Monitoring service unavailable |

## Data Models

### Error Event
```typescript
interface ErrorEvent {
  id: string;
  fingerprint: string;
  message: string;
  service: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  stack_trace?: string;
  context: Record<string, any>;
  breadcrumbs: Breadcrumb[];
  timestamp: Date;
}
```

### Performance Event
```typescript
interface PerformanceEvent {
  type: 'transaction' | 'span';
  name: string;
  service: string;
  duration: number;
  timestamp: Date;
  status: 'success' | 'error';
  context: Record<string, any>;
}
```