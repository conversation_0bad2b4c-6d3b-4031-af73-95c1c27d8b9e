# Analytics Service API

The Analytics Service provides real-time data processing and analytics for e-commerce platforms.

**Base URL**: `/analytics`

## Endpoints

### GET /summary

Get comprehensive analytics summary.

**Query Parameters**:
- `start_date` (optional) - Start date (ISO 8601 format)
- `end_date` (optional) - End date (ISO 8601 format)
- `platform` (optional) - Filter by platform (shopify, woocommerce, etc.)

**Example Request**:
```bash
curl -H "Authorization: Bearer <token>" \
  "http://localhost:3002/api/analytics/summary?start_date=2025-06-01&end_date=2025-06-23"
```

**Response**:
```json
{
  "success": true,
  "data": {
    "summary": {
      "total_clicks": 1250,
      "total_conversions": 45,
      "conversion_rate": 3.6,
      "total_revenue": 12500.00,
      "average_order_value": 277.78
    },
    "platforms": {
      "shopify": {
        "clicks": 800,
        "conversions": 30,
        "revenue": 8500.00
      },
      "woocommerce": {
        "clicks": 450,
        "conversions": 15,
        "revenue": 4000.00
      }
    },
    "trend": {
      "clicks_change": 15.2,
      "conversions_change": 8.7,
      "revenue_change": 22.1
    }
  }
}
```

### GET /metrics/clicks

Get detailed click metrics.

**Query Parameters**:
- `start_date` (optional) - Start date
- `end_date` (optional) - End date
- `platform` (optional) - Filter by platform
- `group_by` (optional) - Group by: day, hour, platform, source
- `limit` (optional) - Number of results (default: 100)
- `offset` (optional) - Pagination offset

**Response**:
```json
{
  "success": true,
  "data": {
    "metrics": [
      {
        "date": "2025-06-23",
        "platform": "shopify",
        "clicks": 125,
        "unique_clicks": 98,
        "bounce_rate": 0.45,
        "avg_session_duration": 180
      }
    ],
    "total": 500,
    "page": 1,
    "per_page": 100
  }
}
```

### GET /metrics/conversions

Get conversion analytics.

**Query Parameters**:
- `start_date` (optional) - Start date
- `end_date` (optional) - End date
- `platform` (optional) - Filter by platform
- `product_id` (optional) - Filter by product

**Response**:
```json
{
  "success": true,
  "data": {
    "conversions": [
      {
        "date": "2025-06-23",
        "platform": "shopify",
        "conversions": 12,
        "conversion_rate": 3.8,
        "revenue": 2850.00,
        "avg_order_value": 237.50
      }
    ],
    "funnel": {
      "impressions": 10000,
      "clicks": 1250,
      "add_to_cart": 450,
      "checkout_started": 200,
      "purchases": 45
    }
  }
}
```

### GET /metrics/revenue

Get revenue analytics.

**Response**:
```json
{
  "success": true,
  "data": {
    "revenue": {
      "total": 12500.00,
      "today": 450.00,
      "yesterday": 380.00,
      "this_week": 2850.00,
      "last_week": 2400.00,
      "this_month": 12500.00,
      "last_month": 11200.00
    },
    "breakdown": [
      {
        "platform": "shopify",
        "revenue": 8500.00,
        "percentage": 68.0
      },
      {
        "platform": "woocommerce",
        "revenue": 4000.00,
        "percentage": 32.0
      }
    ]
  }
}
```

### GET /metrics/attribution

Get attribution analytics.

**Query Parameters**:
- `model` (optional) - Attribution model: first_touch, last_touch, linear, time_decay

**Response**:
```json
{
  "success": true,
  "data": {
    "attribution": {
      "model": "last_touch",
      "channels": [
        {
          "channel": "google",
          "conversions": 25,
          "revenue": 7500.00,
          "percentage": 60.0
        },
        {
          "channel": "facebook",
          "conversions": 15,
          "revenue": 3500.00,
          "percentage": 28.0
        },
        {
          "channel": "direct",
          "conversions": 5,
          "revenue": 1500.00,
          "percentage": 12.0
        }
      ]
    }
  }
}
```

### GET /reports/custom

Generate custom analytics reports.

**Request Body**:
```json
{
  "name": "Weekly Performance Report",
  "metrics": ["clicks", "conversions", "revenue"],
  "dimensions": ["platform", "date"],
  "filters": {
    "platform": ["shopify", "woocommerce"],
    "date_range": {
      "start": "2025-06-16",
      "end": "2025-06-23"
    }
  },
  "sort": {
    "field": "revenue",
    "direction": "desc"
  }
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "report_id": "rpt_abc123",
    "name": "Weekly Performance Report",
    "data": [
      {
        "platform": "shopify",
        "date": "2025-06-23",
        "clicks": 125,
        "conversions": 8,
        "revenue": 1200.00
      }
    ],
    "generated_at": "2025-06-23T16:40:00.000Z"
  }
}
```

### POST /events/click

Track a click event.

**Request Body**:
```json
{
  "session_id": "sess_abc123",
  "user_id": "user_456",
  "product_id": "prod_789",
  "platform": "shopify",
  "source": "google",
  "campaign": "summer_sale",
  "ip_address": "***********",
  "user_agent": "Mozilla/5.0...",
  "page_url": "https://store.com/products/widget",
  "referrer": "https://google.com/search"
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "event_id": "evt_abc123",
    "tracked_at": "2025-06-23T16:40:00.000Z"
  }
}
```

### POST /events/conversion

Track a conversion event.

**Request Body**:
```json
{
  "session_id": "sess_abc123",
  "user_id": "user_456",
  "order_id": "order_789",
  "platform": "shopify",
  "revenue": 150.00,
  "currency": "USD",
  "products": [
    {
      "id": "prod_123",
      "quantity": 2,
      "price": 75.00
    }
  ]
}
```

### GET /health

Service health check.

**Response**:
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "uptime": 3600,
    "database": "connected",
    "redis": "connected",
    "memory_usage": "45%",
    "cpu_usage": "12%"
  }
}
```

## Real-time Data

### WebSocket Events

Connect to `/ws` for real-time analytics data.

**Available Channels**:
- `live_metrics` - Real-time metric updates
- `click_events` - Live click tracking
- `conversion_events` - Live conversion tracking
- `alerts` - System alerts and notifications

**Example Usage**:
```javascript
const ws = new WebSocket('ws://localhost:3002/ws');

ws.onopen = () => {
  ws.send(JSON.stringify({
    type: 'auth',
    token: 'your-jwt-token'
  }));
  
  ws.send(JSON.stringify({
    type: 'subscribe',
    channels: ['live_metrics', 'click_events']
  }));
};

ws.onmessage = (event) => {
  const data = JSON.parse(event.data);
  
  switch(data.type) {
    case 'live_metrics':
      updateDashboard(data.metrics);
      break;
    case 'click_event':
      trackClick(data.event);
      break;
  }
};
```

## Error Codes

| Code | Description |
|------|-------------|
| `INVALID_DATE_RANGE` | Invalid or missing date range |
| `PLATFORM_NOT_FOUND` | Unknown platform specified |
| `INSUFFICIENT_DATA` | Not enough data for analysis |
| `RATE_LIMIT_EXCEEDED` | Too many requests |
| `UNAUTHORIZED` | Invalid or missing authentication |

## Data Models

### Click Event
```typescript
interface ClickEvent {
  id: string;
  session_id: string;
  user_id?: string;
  product_id?: string;
  platform: string;
  source: string;
  campaign?: string;
  ip_address: string;
  user_agent: string;
  page_url: string;
  referrer?: string;
  timestamp: Date;
}
```

### Conversion Event
```typescript
interface ConversionEvent {
  id: string;
  session_id: string;
  user_id?: string;
  order_id: string;
  platform: string;
  revenue: number;
  currency: string;
  products: Product[];
  timestamp: Date;
}
```