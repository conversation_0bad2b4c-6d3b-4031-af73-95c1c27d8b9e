# Security Configuration Guide

This document outlines the security measures implemented in the E-commerce Analytics Platform and provides guidance for secure deployment and operation.

## Security Overview

The platform implements multiple layers of security including:
- SSL/TLS encryption with secure headers
- Network security with firewall and intrusion prevention
- Application security with authentication and authorization
- Container security with Docker hardening
- Data protection with encryption and backup
- Monitoring and alerting for security events

## SSL/TLS Configuration

### Certificate Generation

Generate SSL certificates using the provided script:

```bash
# Self-signed certificate for development
./scripts/generate-ssl-certs.sh

# Let's Encrypt certificate for production
./scripts/generate-ssl-certs.sh -t letsencrypt -d yourdomain.com -e <EMAIL>
```

### Certificate Files

The SSL setup creates the following files in `nginx/ssl/`:
- `cert.pem` - Server certificate
- `key.pem` - Private key (keep secure!)
- `chain.pem` - Certificate chain
- `dhparam.pem` - DH parameters for perfect forward secrecy

### SSL Configuration Features

- **TLS 1.2 and 1.3 only** - Disabled older vulnerable protocols
- **Strong cipher suites** - ECDHE and AES-GCM ciphers preferred
- **Perfect Forward Secrecy** - DH parameters for key exchange
- **OCSP Stapling** - Certificate status verification
- **HSTS** - HTTP Strict Transport Security headers

## Security Headers

The nginx configuration includes comprehensive security headers:

### Core Security Headers
```
Strict-Transport-Security: max-age=31536000; includeSubDomains; preload
X-Frame-Options: SAMEORIGIN
X-Content-Type-Options: nosniff
X-XSS-Protection: 1; mode=block
Referrer-Policy: strict-origin-when-cross-origin
```

### Content Security Policy
```
Content-Security-Policy: default-src 'self'; 
  script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net; 
  style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; 
  font-src 'self' https://fonts.gstatic.com data:; 
  img-src 'self' data: https: blob:; 
  connect-src 'self' ws: wss: https:;
```

### Additional Headers
```
Permissions-Policy: geolocation=(), microphone=(), camera=()
Expect-CT: max-age=86400, enforce
Cross-Origin-Embedder-Policy: require-corp
Cross-Origin-Opener-Policy: same-origin
Cross-Origin-Resource-Policy: same-origin
```

## Network Security

### Firewall Configuration

UFW (Ubuntu/Debian) or firewalld (CentOS/RHEL) rules:
- **SSH (22)** - Restricted to specific IPs in production
- **HTTP (80)** - Redirects to HTTPS
- **HTTPS (443)** - Main application access
- **Database (5432)** - Docker network only
- **Redis (6379)** - Docker network only

### Rate Limiting

Nginx implements multiple rate limiting zones:
- **General requests**: 30 requests/second
- **API endpoints**: 10 requests/second
- **Authentication**: 5 requests/second

### Intrusion Prevention

Fail2ban monitors and blocks malicious activity:
- **SSH brute force** - 3 attempts, 10 minute ban
- **HTTP authentication** - 5 attempts, 10 minute ban
- **Rate limit violations** - 2 attempts, 10 minute ban
- **Bot/scanner activity** - 2 attempts, 10 minute ban

## Application Security

### Authentication
- **JWT tokens** with secure signing keys
- **Password hashing** using bcrypt with salt
- **Session management** with secure cookies
- **Multi-factor authentication** support

### Authorization
- **Role-based access control** (RBAC)
- **API key management** for integrations
- **Resource-level permissions**
- **Audit logging** for access attempts

### Input Validation
- **SQL injection prevention** with parameterized queries
- **XSS protection** with input sanitization
- **CSRF protection** with tokens
- **File upload restrictions** with type and size limits

## Container Security

### Docker Security Configuration

```json
{
    "log-driver": "json-file",
    "log-opts": {
        "max-size": "10m",
        "max-file": "3"
    },
    "live-restore": true,
    "userland-proxy": false,
    "no-new-privileges": true,
    "seccomp-profile": "/etc/docker/seccomp.json"
}
```

### Container Hardening
- **Non-root users** in containers
- **Read-only file systems** where possible
- **Resource limits** for CPU and memory
- **Network isolation** with custom networks
- **Secrets management** with Docker secrets

## Data Protection

### Database Security
- **Encrypted connections** (SSL/TLS)
- **Strong passwords** with regular rotation
- **Network isolation** within Docker network
- **Regular backups** with encryption
- **Access logging** and monitoring

### Redis Security
- **Password authentication**
- **Network isolation**
- **Memory encryption** options
- **Command renaming** for dangerous operations

### Backup Security
- **Encrypted backups** with GPG
- **Secure storage** (S3 with encryption)
- **Access controls** with IAM policies
- **Retention policies** for compliance

## Environment Variables

### Required Security Variables

```bash
# Database
DB_PASSWORD=<strong-random-password>

# Redis
REDIS_PASSWORD=<strong-random-password>

# JWT
JWT_SECRET=<strong-random-secret>

# SSL
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem

# Backup encryption
BACKUP_ENCRYPTION_KEY=<strong-encryption-key>
```

### Optional Security Variables

```bash
# Rate limiting
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=1000

# Session security
SESSION_SECRET=<session-secret>
COOKIE_SECURE=true
COOKIE_SAME_SITE=strict

# CORS
CORS_ORIGIN=https://yourdomain.com
```

## Monitoring and Alerting

### Security Event Monitoring

The platform monitors for:
- **Failed authentication attempts**
- **Rate limit violations**
- **Suspicious API usage patterns**
- **Database connection anomalies**
- **Container security events**

### Log Management

Security logs are collected from:
- **Nginx access/error logs**
- **Application authentication logs**
- **Database audit logs**
- **System security logs**
- **Container runtime logs**

### Alerting Channels

Configure alerts via:
- **Email notifications**
- **Slack webhooks**
- **PagerDuty integration**
- **Custom webhook endpoints**

## Security Checklist

### Pre-deployment
- [ ] Generate production SSL certificates
- [ ] Configure strong passwords for all services
- [ ] Set up firewall rules
- [ ] Configure fail2ban
- [ ] Review and update security headers
- [ ] Test rate limiting configuration
- [ ] Verify container security settings

### Post-deployment
- [ ] Monitor security logs
- [ ] Test SSL certificate renewal
- [ ] Verify backup encryption
- [ ] Review access patterns
- [ ] Update security documentation
- [ ] Schedule security audits

### Regular Maintenance
- [ ] Update system packages
- [ ] Rotate passwords and secrets
- [ ] Review firewall rules
- [ ] Update SSL certificates
- [ ] Security vulnerability scanning
- [ ] Penetration testing

## Security Tools Integration

### Recommended Tools

1. **Vulnerability Scanning**
   - OWASP ZAP
   - Nessus
   - OpenVAS

2. **Security Monitoring**
   - OSSEC
   - Suricata
   - Wazuh

3. **Log Analysis**
   - ELK Stack
   - Splunk
   - Graylog

4. **Container Security**
   - Clair
   - Anchore
   - Twistlock

## Incident Response

### Security Incident Procedure

1. **Detection** - Monitor alerts and logs
2. **Assessment** - Determine scope and impact
3. **Containment** - Isolate affected systems
4. **Eradication** - Remove threats and vulnerabilities
5. **Recovery** - Restore normal operations
6. **Lessons Learned** - Document and improve

### Emergency Contacts

Maintain an updated list of:
- Security team contacts
- System administrators
- Cloud provider support
- External security consultants

## Compliance Considerations

### Data Protection Regulations
- **GDPR** - European data protection
- **CCPA** - California consumer privacy
- **PCI DSS** - Payment card security
- **SOC 2** - Service organization controls

### Security Standards
- **ISO 27001** - Information security management
- **NIST Cybersecurity Framework**
- **CIS Controls** - Critical security controls

## Security Updates

### Automatic Updates

The platform includes automatic security updates:
- **System packages** - Daily security updates
- **Container images** - Weekly base image updates
- **Dependencies** - Automated vulnerability patching

### Manual Updates

Regular manual reviews required for:
- **SSL certificates** - Renewal before expiration
- **Security configurations** - Annual review
- **Access controls** - Quarterly review
- **Backup procedures** - Monthly testing

## Security Support

For security-related questions or incidents:
- Review this documentation
- Check system logs and monitoring
- Contact security team or administrators
- Follow incident response procedures

Remember: Security is an ongoing process, not a one-time setup. Regular monitoring, updates, and improvements are essential for maintaining a secure platform.