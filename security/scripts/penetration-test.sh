#!/bin/bash

# Penetration Testing Script for E-commerce Analytics SaaS Platform
# This script performs automated penetration testing using industry-standard tools

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
RESULTS_DIR="$SCRIPT_DIR/../results"
LOG_FILE="$RESULTS_DIR/pentest-$(date +%Y%m%d-%H%M%S).log"

# Service URLs (customize for your environment)
ANALYTICS_URL="${ANALYTICS_URL:-http://localhost:3001}"
DASHBOARD_URL="${DASHBOARD_URL:-http://localhost:3000}"
BILLING_URL="${BILLING_URL:-http://localhost:3003}"
INTEGRATION_URL="${INTEGRATION_URL:-http://localhost:3002}"

# Test credentials
TEST_API_KEY="${TEST_API_KEY:-test-api-key}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Create results directory
mkdir -p "$RESULTS_DIR"

# Logging function
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

print_banner() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║              E-commerce Analytics SaaS Platform             ║"
    echo "║                   Penetration Testing Suite                 ║"
    echo "║                                                              ║"
    echo "║  ⚠️  WARNING: Only run against authorized test systems  ⚠️   ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

check_dependencies() {
    log "Checking dependencies..."
    
    local deps=("curl" "nmap" "nikto" "sqlmap" "python3")
    local missing_deps=()
    
    for dep in "${deps[@]}"; do
        if ! command -v "$dep" &> /dev/null; then
            missing_deps+=("$dep")
        fi
    done
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        echo -e "${RED}Missing dependencies: ${missing_deps[*]}${NC}"
        echo "Please install missing tools before running penetration tests"
        exit 1
    fi
    
    log "All dependencies satisfied"
}

test_service_discovery() {
    log "Starting service discovery..."
    
    # Extract hostnames and ports from URLs
    local services=("$ANALYTICS_URL" "$DASHBOARD_URL" "$BILLING_URL" "$INTEGRATION_URL")
    
    for service_url in "${services[@]}"; do
        local hostname=$(echo "$service_url" | sed -e 's|^[^/]*//||' -e 's|:.*||' -e 's|/.*||')
        local port=$(echo "$service_url" | sed -e 's|^[^/]*//[^:]*:||' -e 's|/.*||')
        
        # Default ports if not specified
        if [[ "$port" == "$hostname" ]]; then
            if [[ "$service_url" == https* ]]; then
                port=443
            else
                port=80
            fi
        fi
        
        log "Scanning $hostname:$port..."
        
        # Port scan with nmap
        nmap -sS -sV -O -A "$hostname" -p "$port" -oN "$RESULTS_DIR/nmap-$hostname-$port.txt" 2>/dev/null || {
            log "nmap scan failed for $hostname:$port - may require sudo"
            # Fallback to basic connectivity test
            if curl -s --connect-timeout 5 "$service_url/health" >/dev/null; then
                log "$hostname:$port is reachable"
            else
                log "WARNING: $hostname:$port is not reachable"
            fi
        }
    done
}

test_web_vulnerabilities() {
    log "Starting web vulnerability scanning..."
    
    local services=("analytics:$ANALYTICS_URL" "dashboard:$DASHBOARD_URL" "billing:$BILLING_URL" "integration:$INTEGRATION_URL")
    
    for service in "${services[@]}"; do
        local service_name=$(echo "$service" | cut -d: -f1)
        local service_url=$(echo "$service" | cut -d: -f2-)
        
        log "Scanning $service_name service at $service_url..."
        
        # Nikto web vulnerability scanner
        if command -v nikto &> /dev/null; then
            log "Running Nikto scan on $service_name..."
            nikto -h "$service_url" -output "$RESULTS_DIR/nikto-$service_name.txt" -Format txt 2>/dev/null || {
                log "Nikto scan failed for $service_name"
            }
        fi
        
        # Custom web tests
        test_http_methods "$service_name" "$service_url"
        test_directory_traversal "$service_name" "$service_url"
        test_xss_vulnerabilities "$service_name" "$service_url"
    done
}

test_http_methods() {
    local service_name="$1"
    local service_url="$2"
    
    log "Testing HTTP methods for $service_name..."
    
    local methods=("GET" "POST" "PUT" "DELETE" "PATCH" "HEAD" "OPTIONS" "TRACE" "CONNECT")
    
    for method in "${methods[@]}"; do
        local response=$(curl -s -X "$method" "$service_url/" -w "%{http_code}" -o /dev/null 2>/dev/null || echo "000")
        
        case "$response" in
            "200"|"405"|"404") 
                # Expected responses
                ;;
            "500")
                log "WARNING: $service_name returns 500 for $method - potential vulnerability"
                ;;
            *)
                if [[ "$method" == "TRACE" && "$response" == "200" ]]; then
                    log "CRITICAL: $service_name allows TRACE method - XSS vulnerability"
                fi
                ;;
        esac
    done
}

test_directory_traversal() {
    local service_name="$1"
    local service_url="$2"
    
    log "Testing directory traversal for $service_name..."
    
    local payloads=(
        "../../../etc/passwd"
        "..\\..\\..\\windows\\system32\\drivers\\etc\\hosts"
        "....//....//....//etc/passwd"
        "%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd"
    )
    
    for payload in "${payloads[@]}"; do
        local response=$(curl -s "$service_url/api/file?path=$payload" 2>/dev/null || echo "")
        
        if echo "$response" | grep -q "root:"; then
            log "CRITICAL: $service_name vulnerable to directory traversal"
            break
        fi
    done
}

test_xss_vulnerabilities() {
    local service_name="$1"
    local service_url="$2"
    
    log "Testing XSS vulnerabilities for $service_name..."
    
    local payloads=(
        "<script>alert('XSS')</script>"
        "<img src=x onerror=alert('XSS')>"
        "javascript:alert('XSS')"
        "<svg onload=alert('XSS')>"
    )
    
    for payload in "${payloads[@]}"; do
        local response=$(curl -s "$service_url/api/search?q=$payload" 2>/dev/null || echo "")
        
        if echo "$response" | grep -q "$payload"; then
            log "HIGH: $service_name may be vulnerable to XSS - payload reflected"
            break
        fi
    done
}

test_sql_injection() {
    log "Testing SQL injection vulnerabilities..."
    
    if ! command -v sqlmap &> /dev/null; then
        log "SQLMap not found - skipping SQL injection tests"
        return
    fi
    
    local services=("analytics:$ANALYTICS_URL" "billing:$BILLING_URL")
    
    for service in "${services[@]}"; do
        local service_name=$(echo "$service" | cut -d: -f1)
        local service_url=$(echo "$service" | cut -d: -f2-)
        
        log "Testing SQL injection on $service_name..."
        
        # Test common injection points
        local test_urls=(
            "$service_url/api/analytics/query?metric=revenue&filter=1"
            "$service_url/api/subscriptions?id=1"
        )
        
        for test_url in "${test_urls[@]}"; do
            # Basic SQLMap scan with limited risk and time
            timeout 300 sqlmap -u "$test_url" \
                --batch \
                --level 1 \
                --risk 1 \
                --random-agent \
                --output-dir="$RESULTS_DIR/sqlmap-$service_name" \
                --no-logging \
                2>/dev/null || {
                log "SQLMap test completed/timed out for $test_url"
            }
        done
    done
}

test_authentication_bypass() {
    log "Testing authentication bypass vulnerabilities..."
    
    local services=("analytics:$ANALYTICS_URL" "dashboard:$DASHBOARD_URL" "billing:$BILLING_URL" "integration:$INTEGRATION_URL")
    
    for service in "${services[@]}"; do
        local service_name=$(echo "$service" | cut -d: -f1)
        local service_url=$(echo "$service" | cut -d: -f2-)
        
        log "Testing authentication bypass on $service_name..."
        
        # Test protected endpoints without authentication
        local endpoints=("/api/analytics/query" "/api/subscriptions" "/api/dashboard" "/api/integrations")
        
        for endpoint in "${endpoints[@]}"; do
            local response_code=$(curl -s -w "%{http_code}" -o /dev/null "$service_url$endpoint" 2>/dev/null || echo "000")
            
            if [[ "$response_code" == "200" ]]; then
                log "CRITICAL: $service_name$endpoint accessible without authentication"
            fi
        done
        
        # Test with invalid tokens
        local invalid_tokens=("invalid" "Bearer invalid" "Bearer expired.token.here")
        
        for token in "${invalid_tokens[@]}"; do
            local response_code=$(curl -s -H "Authorization: $token" -w "%{http_code}" -o /dev/null "$service_url/api/test" 2>/dev/null || echo "000")
            
            if [[ "$response_code" == "200" ]]; then
                log "CRITICAL: $service_name accepts invalid authentication token"
                break
            fi
        done
    done
}

test_rate_limiting() {
    log "Testing rate limiting..."
    
    local services=("analytics:$ANALYTICS_URL" "billing:$BILLING_URL")
    
    for service in "${services[@]}"; do
        local service_name=$(echo "$service" | cut -d: -f1)
        local service_url=$(echo "$service" | cut -d: -f2-)
        
        log "Testing rate limiting on $service_name..."
        
        # Send rapid requests
        local rate_limited=false
        for i in {1..20}; do
            local response_code=$(curl -s -w "%{http_code}" -o /dev/null "$service_url/health" 2>/dev/null || echo "000")
            
            if [[ "$response_code" == "429" ]]; then
                rate_limited=true
                break
            fi
            
            sleep 0.1
        done
        
        if [[ "$rate_limited" == false ]]; then
            log "MEDIUM: $service_name does not implement rate limiting"
        else
            log "GOOD: $service_name implements rate limiting"
        fi
    done
}

test_ssl_vulnerabilities() {
    log "Testing SSL/TLS vulnerabilities..."
    
    local services=("$ANALYTICS_URL" "$DASHBOARD_URL" "$BILLING_URL" "$INTEGRATION_URL")
    
    for service_url in "${services[@]}"; do
        if [[ "$service_url" == https* ]]; then
            local hostname=$(echo "$service_url" | sed -e 's|^[^/]*//||' -e 's|:.*||' -e 's|/.*||')
            local port=$(echo "$service_url" | sed -e 's|^[^/]*//[^:]*:||' -e 's|/.*||')
            
            # Default HTTPS port
            if [[ "$port" == "$hostname" ]]; then
                port=443
            fi
            
            log "Testing SSL/TLS for $hostname:$port..."
            
            # Test SSL configuration with nmap scripts
            nmap --script ssl-enum-ciphers,ssl-cert,ssl-date,ssl-heartbleed \
                 -p "$port" "$hostname" \
                 -oN "$RESULTS_DIR/ssl-$hostname-$port.txt" 2>/dev/null || {
                log "SSL vulnerability scan failed for $hostname:$port"
            }
            
            # Test for weak ciphers with OpenSSL
            if command -v openssl &> /dev/null; then
                log "Testing cipher strength for $hostname:$port..."
                
                # Test for weak ciphers
                local weak_ciphers=("RC4" "DES" "3DES" "MD5")
                for cipher in "${weak_ciphers[@]}"; do
                    if echo | timeout 5 openssl s_client -cipher "$cipher" -connect "$hostname:$port" 2>/dev/null | grep -q "Cipher is"; then
                        log "HIGH: $hostname:$port supports weak cipher: $cipher"
                    fi
                done
            fi
        fi
    done
}

test_business_logic() {
    log "Testing business logic vulnerabilities..."
    
    # Test billing logic
    log "Testing billing business logic..."
    
    # Test for price manipulation
    local billing_tests=(
        '{"amount": -100, "currency": "USD"}'
        '{"amount": 0.01, "currency": "USD"}'
        '{"amount": 999999999, "currency": "USD"}'
    )
    
    for payload in "${billing_tests[@]}"; do
        local response=$(curl -s -X POST \
            -H "Authorization: Bearer $TEST_API_KEY" \
            -H "Content-Type: application/json" \
            -d "$payload" \
            "$BILLING_URL/api/payments" 2>/dev/null || echo "")
        
        if echo "$response" | grep -q "success.*true"; then
            log "HIGH: Billing service may accept manipulated amounts"
            break
        fi
    done
    
    # Test for subscription manipulation
    local subscription_tests=(
        '{"plan_id": "enterprise", "trial_days": 9999}'
        '{"plan_id": "free", "features": {"unlimited": true}}'
    )
    
    for payload in "${subscription_tests[@]}"; do
        local response=$(curl -s -X POST \
            -H "Authorization: Bearer $TEST_API_KEY" \
            -H "Content-Type: application/json" \
            -d "$payload" \
            "$BILLING_URL/api/subscriptions" 2>/dev/null || echo "")
        
        if echo "$response" | grep -q "success.*true"; then
            log "HIGH: Subscription service may accept manipulated parameters"
            break
        fi
    done
}

test_data_exposure() {
    log "Testing data exposure vulnerabilities..."
    
    local services=("analytics:$ANALYTICS_URL" "dashboard:$DASHBOARD_URL" "billing:$BILLING_URL" "integration:$INTEGRATION_URL")
    
    for service in "${services[@]}"; do
        local service_name=$(echo "$service" | cut -d: -f1)
        local service_url=$(echo "$service" | cut -d: -f2-)
        
        log "Testing data exposure in $service_name..."
        
        # Test for sensitive data in error messages
        local error_response=$(curl -s "$service_url/api/nonexistent" 2>/dev/null || echo "")
        
        local sensitive_patterns=("password" "secret" "private.*key" "database.*url" "connection.*string")
        
        for pattern in "${sensitive_patterns[@]}"; do
            if echo "$error_response" | grep -iq "$pattern"; then
                log "MEDIUM: $service_name may expose sensitive data in error messages"
                break
            fi
        done
        
        # Test for debug information exposure
        if echo "$error_response" | grep -iq "stack.*trace\|debug.*info\|internal.*error"; then
            log "LOW: $service_name exposes debug information"
        fi
    done
}

generate_report() {
    log "Generating penetration test report..."
    
    local report_file="$RESULTS_DIR/pentest-report-$(date +%Y%m%d-%H%M%S).html"
    
    cat > "$report_file" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>Penetration Test Report - E-commerce Analytics SaaS</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #2c3e50; color: white; padding: 20px; text-align: center; }
        .critical { color: #e74c3c; font-weight: bold; }
        .high { color: #e67e22; font-weight: bold; }
        .medium { color: #f39c12; font-weight: bold; }
        .low { color: #27ae60; font-weight: bold; }
        .good { color: #27ae60; }
        .section { margin: 20px 0; padding: 15px; border-left: 4px solid #3498db; }
        pre { background: #ecf0f1; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Penetration Test Report</h1>
        <h2>E-commerce Analytics SaaS Platform</h2>
        <p>Test Date: $(date)</p>
    </div>
    
    <div class="section">
        <h2>Executive Summary</h2>
        <p>This report contains the results of a comprehensive penetration test performed on the E-commerce Analytics SaaS platform.</p>
        
        <h3>Services Tested:</h3>
        <ul>
            <li>Analytics Service: $ANALYTICS_URL</li>
            <li>Dashboard Service: $DASHBOARD_URL</li>
            <li>Billing Service: $BILLING_URL</li>
            <li>Integration Service: $INTEGRATION_URL</li>
        </ul>
    </div>
    
    <div class="section">
        <h2>Test Results Summary</h2>
        <p>Detailed findings have been logged to: <code>$(basename "$LOG_FILE")</code></p>
        
        <h3>Key Findings:</h3>
        <pre>$(grep -E "(CRITICAL|HIGH|MEDIUM|LOW|WARNING|GOOD):" "$LOG_FILE" | tail -20)</pre>
    </div>
    
    <div class="section">
        <h2>Recommendations</h2>
        <ol>
            <li><strong>Critical/High Issues:</strong> Address immediately before production deployment</li>
            <li><strong>Medium Issues:</strong> Plan remediation within 30 days</li>
            <li><strong>Low Issues:</strong> Include in next development cycle</li>
            <li><strong>Continuous Testing:</strong> Integrate security testing into CI/CD pipeline</li>
        </ol>
    </div>
    
    <div class="section">
        <h2>Detailed Results</h2>
        <p>Additional scan results are available in the following files:</p>
        <ul>
$(find "$RESULTS_DIR" -name "*.txt" -o -name "*.xml" -o -name "*.json" | sed 's|.*/||' | sort | sed 's/^/            <li>/' | sed 's/$/<\/li>/')
        </ul>
    </div>
    
    <div class="section">
        <h2>Next Steps</h2>
        <ol>
            <li>Review all identified vulnerabilities</li>
            <li>Prioritize remediation based on risk levels</li>
            <li>Implement security fixes</li>
            <li>Re-test to verify fixes</li>
            <li>Schedule regular penetration testing</li>
        </ol>
    </div>
</body>
</html>
EOF
    
    log "Report generated: $report_file"
    
    # Count vulnerabilities by severity
    local critical_count=$(grep -c "CRITICAL:" "$LOG_FILE" 2>/dev/null || echo "0")
    local high_count=$(grep -c "HIGH:" "$LOG_FILE" 2>/dev/null || echo "0")
    local medium_count=$(grep -c "MEDIUM:" "$LOG_FILE" 2>/dev/null || echo "0")
    local low_count=$(grep -c "LOW:" "$LOG_FILE" 2>/dev/null || echo "0")
    
    echo -e "\n${BLUE}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${BLUE}║                    PENETRATION TEST SUMMARY                 ║${NC}"
    echo -e "${BLUE}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo -e "${RED}Critical Issues: $critical_count${NC}"
    echo -e "${YELLOW}High Issues: $high_count${NC}"
    echo -e "${YELLOW}Medium Issues: $medium_count${NC}"
    echo -e "${GREEN}Low Issues: $low_count${NC}"
    echo -e "\nDetailed log: $LOG_FILE"
    echo -e "HTML Report: $report_file"
    
    # Return appropriate exit code
    if [[ $critical_count -gt 0 ]]; then
        return 2
    elif [[ $high_count -gt 0 ]]; then
        return 1
    else
        return 0
    fi
}

main() {
    print_banner
    
    log "Starting penetration test suite..."
    
    check_dependencies
    
    # Run all penetration tests
    test_service_discovery
    test_web_vulnerabilities
    test_sql_injection
    test_authentication_bypass
    test_rate_limiting
    test_ssl_vulnerabilities
    test_business_logic
    test_data_exposure
    
    # Generate final report
    generate_report
    local exit_code=$?
    
    log "Penetration testing completed"
    
    exit $exit_code
}

# Run main function
main "$@"