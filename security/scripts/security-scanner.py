#!/usr/bin/env python3
"""
Security Scanner for E-commerce Analytics SaaS Platform
Automated security testing and vulnerability assessment
"""

import os
import sys
import json
import time
import requests
import subprocess
import re
from datetime import datetime
from urllib.parse import urljoin
import ssl
import socket
from concurrent.futures import ThreadPoolExecutor, as_completed

class SecurityScanner:
    def __init__(self, base_urls=None, api_key=None):
        self.base_urls = base_urls or {
            'analytics': 'http://localhost:3001',
            'dashboard': 'http://localhost:3000', 
            'billing': 'http://localhost:3003',
            'integration': 'http://localhost:3002'
        }
        self.api_key = api_key or 'test-api-key'
        self.session = requests.Session()
        self.session.timeout = 30
        self.vulnerabilities = []
        self.results = {
            'scan_time': datetime.now().isoformat(),
            'services_scanned': list(self.base_urls.keys()),
            'vulnerabilities': [],
            'security_headers': {},
            'ssl_analysis': {},
            'api_security': {},
            'summary': {}
        }

    def run_comprehensive_scan(self):
        """Run all security tests"""
        print("🔒 Starting Comprehensive Security Scan")
        print("=" * 50)
        
        # Infrastructure security tests
        self.test_ssl_configuration()
        self.test_security_headers()
        
        # Application security tests
        self.test_authentication_security()
        self.test_api_security()
        self.test_input_validation()
        self.test_authorization()
        
        # Data security tests
        self.test_data_exposure()
        self.test_error_handling()
        
        # Network security tests
        self.test_rate_limiting()
        self.test_cors_configuration()
        
        # Generate final report
        self.generate_security_report()
        
        return self.results

    def test_ssl_configuration(self):
        """Test SSL/TLS configuration"""
        print("\n🔐 Testing SSL/TLS Configuration...")
        
        for service_name, base_url in self.base_urls.items():
            if not base_url.startswith('https://'):
                self.add_vulnerability(
                    'HIGH',
                    'SSL Not Enforced',
                    f'{service_name} service not using HTTPS',
                    service_name,
                    'Implement HTTPS for all services'
                )
                continue
                
            try:
                # Parse hostname and port
                hostname = base_url.replace('https://', '').split(':')[0]
                port = int(base_url.split(':')[2]) if ':' in base_url.split('://')[1] else 443
                
                # Test SSL connection
                context = ssl.create_default_context()
                with socket.create_connection((hostname, port), timeout=10) as sock:
                    with context.wrap_socket(sock, server_hostname=hostname) as ssock:
                        cert = ssock.getpeercert()
                        cipher = ssock.cipher()
                        
                        # Check cipher strength
                        if cipher[2] < 256:
                            self.add_vulnerability(
                                'MEDIUM',
                                'Weak Cipher',
                                f'{service_name} using weak cipher: {cipher[0]}',
                                service_name,
                                'Use stronger ciphers (AES-256 or better)'
                            )
                        
                        # Check certificate expiry
                        import ssl
                        expiry_date = datetime.strptime(cert['notAfter'], '%b %d %H:%M:%S %Y %Z')
                        days_until_expiry = (expiry_date - datetime.now()).days
                        
                        if days_until_expiry < 30:
                            self.add_vulnerability(
                                'HIGH',
                                'Certificate Expiring Soon',
                                f'{service_name} certificate expires in {days_until_expiry} days',
                                service_name,
                                'Renew SSL certificate'
                            )
                        
                        self.results['ssl_analysis'][service_name] = {
                            'protocol': ssock.version(),
                            'cipher': cipher[0],
                            'cipher_strength': cipher[2],
                            'certificate_expires': cert['notAfter'],
                            'days_until_expiry': days_until_expiry
                        }
                        
            except Exception as e:
                self.add_vulnerability(
                    'HIGH',
                    'SSL Connection Failed',
                    f'{service_name} SSL connection failed: {str(e)}',
                    service_name,
                    'Fix SSL configuration'
                )

    def test_security_headers(self):
        """Test HTTP security headers"""
        print("\n🛡️ Testing Security Headers...")
        
        for service_name, base_url in self.base_urls.items():
            try:
                response = self.session.get(f"{base_url}/health", timeout=10)
                headers = response.headers
                
                # Required security headers
                required_headers = {
                    'X-Content-Type-Options': 'nosniff',
                    'X-Frame-Options': ['DENY', 'SAMEORIGIN'],
                    'X-XSS-Protection': '1; mode=block',
                    'Strict-Transport-Security': None,  # HSTS
                    'Content-Security-Policy': None,     # CSP
                    'Referrer-Policy': 'strict-origin-when-cross-origin'
                }
                
                missing_headers = []
                weak_headers = []
                
                for header, expected_values in required_headers.items():
                    if header not in headers:
                        missing_headers.append(header)
                    elif expected_values:
                        if isinstance(expected_values, list):
                            if headers[header] not in expected_values:
                                weak_headers.append(f"{header}: {headers[header]}")
                        elif headers[header] != expected_values:
                            weak_headers.append(f"{header}: {headers[header]}")
                
                if missing_headers:
                    self.add_vulnerability(
                        'HIGH',
                        'Missing Security Headers',
                        f'{service_name} missing headers: {", ".join(missing_headers)}',
                        service_name,
                        'Implement missing security headers'
                    )
                
                if weak_headers:
                    self.add_vulnerability(
                        'MEDIUM',
                        'Weak Security Headers',
                        f'{service_name} weak headers: {", ".join(weak_headers)}',
                        service_name,
                        'Strengthen security header values'
                    )
                
                self.results['security_headers'][service_name] = {
                    'present_headers': {k: v for k, v in headers.items() 
                                     if k.lower().startswith(('x-', 'strict-transport', 'content-security'))},
                    'missing_headers': missing_headers,
                    'weak_headers': weak_headers
                }
                
            except Exception as e:
                print(f"Error testing headers for {service_name}: {e}")

    def test_authentication_security(self):
        """Test authentication mechanisms"""
        print("\n🔑 Testing Authentication Security...")
        
        for service_name, base_url in self.base_urls.items():
            # Test unauthenticated access to protected endpoints
            protected_endpoints = [
                '/api/analytics/query',
                '/api/subscriptions',
                '/api/dashboard',
                '/api/integrations'
            ]
            
            for endpoint in protected_endpoints:
                try:
                    # Test without authentication
                    response = self.session.get(f"{base_url}{endpoint}", timeout=10)
                    
                    if response.status_code == 200:
                        self.add_vulnerability(
                            'CRITICAL',
                            'Unauthenticated Access',
                            f'{service_name}{endpoint} accessible without authentication',
                            service_name,
                            'Implement proper authentication checks'
                        )
                    
                    # Test with invalid token
                    headers = {'Authorization': 'Bearer invalid-token'}
                    response = self.session.get(f"{base_url}{endpoint}", headers=headers, timeout=10)
                    
                    if response.status_code == 200:
                        self.add_vulnerability(
                            'CRITICAL',
                            'Invalid Token Accepted',
                            f'{service_name}{endpoint} accepts invalid tokens',
                            service_name,
                            'Implement proper token validation'
                        )
                        
                except Exception as e:
                    # Connection errors are expected for some endpoints
                    continue

    def test_api_security(self):
        """Test API security"""
        print("\n🔌 Testing API Security...")
        
        for service_name, base_url in self.base_urls.items():
            api_tests = {
                'sql_injection': [
                    "'; DROP TABLE users; --",
                    "' OR '1'='1",
                    "1' UNION SELECT * FROM users --"
                ],
                'xss_payload': [
                    "<script>alert('XSS')</script>",
                    "javascript:alert('XSS')",
                    "<img src=x onerror=alert('XSS')>"
                ],
                'path_traversal': [
                    "../../../etc/passwd",
                    "..\\..\\..\\windows\\system32\\drivers\\etc\\hosts",
                    "....//....//....//etc/passwd"
                ]
            }
            
            # Test different attack vectors
            for attack_type, payloads in api_tests.items():
                for payload in payloads:
                    try:
                        # Test in query parameters
                        response = self.session.get(
                            f"{base_url}/api/test",
                            params={'q': payload},
                            timeout=5
                        )
                        
                        # Check for vulnerable responses
                        if self.check_vulnerable_response(response, attack_type, payload):
                            self.add_vulnerability(
                                'HIGH',
                                f'{attack_type.title()} Vulnerability',
                                f'{service_name} vulnerable to {attack_type}',
                                service_name,
                                f'Implement input validation for {attack_type}'
                            )
                            
                    except Exception:
                        # Timeouts and errors are expected
                        continue

    def test_input_validation(self):
        """Test input validation"""
        print("\n✅ Testing Input Validation...")
        
        # Test oversized payloads
        large_payload = "A" * 10000
        
        for service_name, base_url in self.base_urls.items():
            try:
                # Test large POST payload
                response = self.session.post(
                    f"{base_url}/api/test",
                    json={'data': large_payload},
                    timeout=10
                )
                
                if response.status_code == 200:
                    self.add_vulnerability(
                        'MEDIUM',
                        'Insufficient Input Size Validation',
                        f'{service_name} accepts oversized payloads',
                        service_name,
                        'Implement payload size limits'
                    )
                    
            except Exception:
                continue

    def test_authorization(self):
        """Test authorization controls"""
        print("\n🚪 Testing Authorization Controls...")
        
        # Test with valid but limited API key
        headers = {'Authorization': f'Bearer {self.api_key}'}
        
        for service_name, base_url in self.base_urls.items():
            # Test access to admin endpoints
            admin_endpoints = [
                '/api/admin/users',
                '/api/admin/system',
                '/api/admin/logs'
            ]
            
            for endpoint in admin_endpoints:
                try:
                    response = self.session.get(
                        f"{base_url}{endpoint}",
                        headers=headers,
                        timeout=10
                    )
                    
                    if response.status_code == 200:
                        self.add_vulnerability(
                            'HIGH',
                            'Privilege Escalation',
                            f'{service_name}{endpoint} accessible with regular API key',
                            service_name,
                            'Implement proper role-based access control'
                        )
                        
                except Exception:
                    continue

    def test_data_exposure(self):
        """Test for data exposure"""
        print("\n📊 Testing Data Exposure...")
        
        headers = {'Authorization': f'Bearer {self.api_key}'}
        
        for service_name, base_url in self.base_urls.items():
            try:
                # Test for sensitive data in responses
                response = self.session.get(f"{base_url}/health", headers=headers, timeout=10)
                
                # Check for sensitive information in response
                sensitive_patterns = [
                    r'password',
                    r'secret',
                    r'private.*key',
                    r'api.*key',
                    r'token',
                    r'database.*url'
                ]
                
                for pattern in sensitive_patterns:
                    if re.search(pattern, response.text, re.IGNORECASE):
                        self.add_vulnerability(
                            'HIGH',
                            'Sensitive Data Exposure',
                            f'{service_name} exposes sensitive data in response',
                            service_name,
                            'Remove sensitive data from API responses'
                        )
                        break
                        
            except Exception:
                continue

    def test_error_handling(self):
        """Test error handling security"""
        print("\n❌ Testing Error Handling...")
        
        for service_name, base_url in self.base_urls.items():
            try:
                # Test 404 responses
                response = self.session.get(f"{base_url}/nonexistent-endpoint", timeout=10)
                
                # Check for information disclosure in error messages
                error_patterns = [
                    r'stack trace',
                    r'database error',
                    r'sql',
                    r'internal server error.*details',
                    r'debug.*info'
                ]
                
                for pattern in error_patterns:
                    if re.search(pattern, response.text, re.IGNORECASE):
                        self.add_vulnerability(
                            'MEDIUM',
                            'Information Disclosure in Errors',
                            f'{service_name} exposes internal details in error messages',
                            service_name,
                            'Implement generic error messages for production'
                        )
                        break
                        
            except Exception:
                continue

    def test_rate_limiting(self):
        """Test rate limiting"""
        print("\n🚦 Testing Rate Limiting...")
        
        headers = {'Authorization': f'Bearer {self.api_key}'}
        
        for service_name, base_url in self.base_urls.items():
            try:
                # Make rapid requests to test rate limiting
                responses = []
                for i in range(20):
                    response = self.session.get(f"{base_url}/health", headers=headers, timeout=5)
                    responses.append(response.status_code)
                
                # Check if any requests were rate limited (429 status)
                rate_limited = any(status == 429 for status in responses)
                
                if not rate_limited:
                    self.add_vulnerability(
                        'MEDIUM',
                        'Insufficient Rate Limiting',
                        f'{service_name} does not implement rate limiting',
                        service_name,
                        'Implement API rate limiting'
                    )
                    
            except Exception:
                continue

    def test_cors_configuration(self):
        """Test CORS configuration"""
        print("\n🌐 Testing CORS Configuration...")
        
        for service_name, base_url in self.base_urls.items():
            try:
                # Test CORS with OPTIONS request
                headers = {
                    'Origin': 'https://evil.com',
                    'Access-Control-Request-Method': 'GET'
                }
                
                response = self.session.options(f"{base_url}/api/test", headers=headers, timeout=10)
                
                cors_headers = {
                    'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
                    'Access-Control-Allow-Credentials': response.headers.get('Access-Control-Allow-Credentials')
                }
                
                # Check for overly permissive CORS
                if cors_headers['Access-Control-Allow-Origin'] == '*':
                    if cors_headers['Access-Control-Allow-Credentials'] == 'true':
                        self.add_vulnerability(
                            'HIGH',
                            'Dangerous CORS Configuration',
                            f'{service_name} allows any origin with credentials',
                            service_name,
                            'Restrict CORS origins or disable credentials'
                        )
                
            except Exception:
                continue

    def check_vulnerable_response(self, response, attack_type, payload):
        """Check if response indicates vulnerability"""
        if attack_type == 'sql_injection':
            # Look for SQL error messages
            sql_errors = ['sql syntax', 'mysql', 'postgresql', 'sqlite', 'syntax error']
            return any(error in response.text.lower() for error in sql_errors)
        
        elif attack_type == 'xss_payload':
            # Check if payload is reflected unescaped
            return payload in response.text
            
        elif attack_type == 'path_traversal':
            # Look for file contents
            path_indicators = ['root:', '/bin/bash', '[drivers]']
            return any(indicator in response.text for indicator in path_indicators)
            
        return False

    def add_vulnerability(self, severity, title, description, service, remediation):
        """Add a vulnerability to the results"""
        vulnerability = {
            'severity': severity,
            'title': title,
            'description': description,
            'service': service,
            'remediation': remediation,
            'timestamp': datetime.now().isoformat()
        }
        
        self.vulnerabilities.append(vulnerability)
        self.results['vulnerabilities'].append(vulnerability)
        
        # Print vulnerability
        severity_colors = {
            'CRITICAL': '\033[91m',  # Red
            'HIGH': '\033[91m',      # Red
            'MEDIUM': '\033[93m',    # Yellow
            'LOW': '\033[92m'        # Green
        }
        
        color = severity_colors.get(severity, '\033[0m')
        print(f"  {color}[{severity}]\033[0m {title}: {description}")

    def generate_security_report(self):
        """Generate comprehensive security report"""
        print("\n📋 Generating Security Report...")
        
        # Calculate summary statistics
        severity_counts = {'CRITICAL': 0, 'HIGH': 0, 'MEDIUM': 0, 'LOW': 0}
        for vuln in self.vulnerabilities:
            severity_counts[vuln['severity']] += 1
        
        self.results['summary'] = {
            'total_vulnerabilities': len(self.vulnerabilities),
            'severity_breakdown': severity_counts,
            'services_tested': len(self.base_urls),
            'scan_duration': 'completed',
            'overall_risk_score': self.calculate_risk_score(severity_counts)
        }
        
        # Save detailed results
        report_file = f"security-scan-{datetime.now().strftime('%Y%m%d-%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(self.results, f, indent=2)
        
        # Print summary
        print(f"\n🔒 Security Scan Complete")
        print(f"=" * 30)
        print(f"Total Vulnerabilities: {len(self.vulnerabilities)}")
        print(f"Critical: {severity_counts['CRITICAL']}")
        print(f"High: {severity_counts['HIGH']}")
        print(f"Medium: {severity_counts['MEDIUM']}")
        print(f"Low: {severity_counts['LOW']}")
        print(f"\nDetailed report saved to: {report_file}")
        
        # Return exit code based on findings
        if severity_counts['CRITICAL'] > 0 or severity_counts['HIGH'] > 0:
            print("\n⚠️  CRITICAL or HIGH severity vulnerabilities found!")
            return 1
        elif severity_counts['MEDIUM'] > 0:
            print("\n⚠️  MEDIUM severity vulnerabilities found")
            return 0
        else:
            print("\n✅ No significant vulnerabilities found")
            return 0

    def calculate_risk_score(self, severity_counts):
        """Calculate overall risk score"""
        weights = {'CRITICAL': 10, 'HIGH': 7, 'MEDIUM': 4, 'LOW': 1}
        score = sum(count * weights[severity] for severity, count in severity_counts.items())
        
        if score >= 30:
            return 'CRITICAL'
        elif score >= 20:
            return 'HIGH'
        elif score >= 10:
            return 'MEDIUM'
        else:
            return 'LOW'

def main():
    """Main function"""
    # Parse command line arguments
    import argparse
    parser = argparse.ArgumentParser(description='Security Scanner for E-commerce Analytics SaaS')
    parser.add_argument('--analytics-url', default='http://localhost:3001', help='Analytics service URL')
    parser.add_argument('--dashboard-url', default='http://localhost:3000', help='Dashboard service URL')
    parser.add_argument('--billing-url', default='http://localhost:3003', help='Billing service URL')
    parser.add_argument('--integration-url', default='http://localhost:3002', help='Integration service URL')
    parser.add_argument('--api-key', default='test-api-key', help='API key for testing')
    
    args = parser.parse_args()
    
    base_urls = {
        'analytics': args.analytics_url,
        'dashboard': args.dashboard_url,
        'billing': args.billing_url,
        'integration': args.integration_url
    }
    
    # Run security scan
    scanner = SecurityScanner(base_urls, args.api_key)
    exit_code = scanner.run_comprehensive_scan()
    
    sys.exit(exit_code)

if __name__ == '__main__':
    main()