#!/usr/bin/env python3
"""
Conversational Analytics Chatbot for E-commerce Analytics
Advanced AI chatbot for interactive business intelligence and data exploration
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import os
import sys
import json
import asyncio
import re
from typing import Dict, List, Tuple, Optional, Any, Union
from dataclasses import dataclass
from collections import defaultdict, deque
import warnings

# Import our natural language query engine
from natural_language_query_engine import NaturalLanguageQueryEngine, NLQueryConfig

# Advanced NLP and conversational AI
import openai
import transformers
from transformers import (
    AutoTokenizer, AutoModelForCausalLM, AutoModelForSequenceClassification,
    BlenderbotTokenizer, BlenderbotForConditionalGeneration,
    pipeline, Conversation
)

# Conversation management
from sentence_transformers import SentenceTransformer
from sklearn.metrics.pairwise import cosine_similarity

# Real-time chat interfaces
import websocket
import socketio
from fastapi import FastAPI, WebSocket, HTTPException
from fastapi.responses import HTMLResponse
import uvicorn

# Voice processing (optional)
import speech_recognition as sr
from gtts import gTTS
import pygame

# Visualization generation
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import plotly.io as pio

# Database and storage
from sqlalchemy import create_engine, text
import redis

# MLflow integration
import mlflow
import mlflow.transformers
from mlflow.tracking import MlflowClient

warnings.filterwarnings('ignore')

# Configuration
@dataclass
class ChatbotConfig:
    # Conversational AI models
    conversation_model: str = "microsoft/DialoGPT-medium"
    intent_model: str = "microsoft/DialoGPT-small"
    sentiment_model: str = "cardiffnlp/twitter-roberta-base-sentiment-latest"
    
    # OpenAI configuration (preferred for better conversations)
    openai_api_key: str = os.getenv('OPENAI_API_KEY', '')
    openai_model: str = "gpt-3.5-turbo"
    openai_max_tokens: int = 500
    openai_temperature: float = 0.7
    
    # Conversation parameters
    max_conversation_length: int = 10  # Number of exchanges to remember
    conversation_timeout_minutes: int = 30
    context_similarity_threshold: float = 0.7
    
    # Voice capabilities
    enable_voice_input: bool = False
    enable_voice_output: bool = False
    voice_language: str = 'en'
    
    # Personality and behavior
    chatbot_name: str = "AnalyticsBot"
    chatbot_personality: str = "professional_friendly"
    response_style: str = "concise_insightful"
    
    # Data access
    max_data_rows_in_response: int = 10
    enable_data_visualization: bool = True
    auto_generate_insights: bool = True
    
    # Security and privacy
    enable_data_filtering: bool = True
    user_permissions: Dict[str, List[str]] = None
    sensitive_data_fields: List[str] = None
    
    # Performance
    response_timeout_seconds: int = 30
    enable_response_caching: bool = True
    cache_expiry_minutes: int = 15
    
    # Integration
    slack_bot_token: str = os.getenv('SLACK_BOT_TOKEN', '')
    discord_bot_token: str = os.getenv('DISCORD_BOT_TOKEN', '')
    teams_webhook_url: str = os.getenv('TEAMS_WEBHOOK_URL', '')
    
    # Redis for conversation storage
    redis_host: str = os.getenv('REDIS_HOST', 'redis-cluster')
    redis_port: int = int(os.getenv('REDIS_PORT', '6379'))
    redis_db: int = int(os.getenv('REDIS_DB', '4'))
    
    # FastAPI server
    server_host: str = '0.0.0.0'
    server_port: int = int(os.getenv('CHATBOT_PORT', '8080'))
    
    # MLflow configuration
    mlflow_tracking_uri: str = os.getenv('MLFLOW_TRACKING_URI', 'http://mlflow-server.mlflow:5000')
    experiment_name: str = 'conversational-analytics'
    
    def __post_init__(self):
        if self.user_permissions is None:
            self.user_permissions = {
                'admin': ['all'],
                'analyst': ['sales', 'customers', 'products'],
                'manager': ['sales', 'revenue', 'performance'],
                'viewer': ['basic_stats']
            }
        
        if self.sensitive_data_fields is None:
            self.sensitive_data_fields = [
                'email', 'phone', 'address', 'credit_card', 'ssn', 'password'
            ]

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ConversationMemory:
    """Manages conversation history and context"""
    
    def __init__(self, config: ChatbotConfig):
        self.config = config
        self.conversations = {}  # user_id -> conversation history
        self.redis_client = None
        self.sentence_transformer = None
        
        # Initialize components
        self._initialize_redis()
        self._initialize_models()
    
    def _initialize_redis(self):
        """Initialize Redis for conversation persistence"""
        try:
            self.redis_client = redis.Redis(
                host=self.config.redis_host,
                port=self.config.redis_port,
                db=self.config.redis_db,
                decode_responses=True
            )
            self.redis_client.ping()
            logger.info("Redis connection established for conversation memory")
        except Exception as e:
            logger.warning(f"Redis not available for conversation memory: {str(e)}")
    
    def _initialize_models(self):
        """Initialize models for conversation understanding"""
        try:
            self.sentence_transformer = SentenceTransformer('all-MiniLM-L6-v2')
            logger.info("Sentence transformer model loaded for conversation memory")
        except Exception as e:
            logger.error(f"Error loading sentence transformer: {str(e)}")
    
    def add_message(self, user_id: str, message: str, response: str, context: Dict = None):
        """Add a message exchange to conversation history"""
        try:
            conversation_key = f"conversation:{user_id}"
            
            # Create conversation entry
            entry = {
                'timestamp': datetime.now().isoformat(),
                'user_message': message,
                'bot_response': response,
                'context': context or {}
            }
            
            # Store in memory
            if user_id not in self.conversations:
                self.conversations[user_id] = deque(maxlen=self.config.max_conversation_length)
            
            self.conversations[user_id].append(entry)
            
            # Store in Redis if available
            if self.redis_client:
                self.redis_client.lpush(conversation_key, json.dumps(entry))
                self.redis_client.ltrim(conversation_key, 0, self.config.max_conversation_length - 1)
                self.redis_client.expire(conversation_key, self.config.conversation_timeout_minutes * 60)
            
        except Exception as e:
            logger.error(f"Error adding message to conversation memory: {str(e)}")
    
    def get_conversation_history(self, user_id: str) -> List[Dict]:
        """Get conversation history for a user"""
        try:
            # Try memory first
            if user_id in self.conversations:
                return list(self.conversations[user_id])
            
            # Try Redis
            if self.redis_client:
                conversation_key = f"conversation:{user_id}"
                history_json = self.redis_client.lrange(conversation_key, 0, -1)
                
                if history_json:
                    history = [json.loads(entry) for entry in reversed(history_json)]
                    
                    # Load into memory
                    self.conversations[user_id] = deque(history, maxlen=self.config.max_conversation_length)
                    
                    return history
            
            return []
            
        except Exception as e:
            logger.error(f"Error getting conversation history: {str(e)}")
            return []
    
    def get_relevant_context(self, user_id: str, current_message: str) -> Dict:
        """Get relevant context from conversation history"""
        try:
            if not self.sentence_transformer:
                return {}
            
            history = self.get_conversation_history(user_id)
            
            if not history:
                return {}
            
            # Get embedding for current message
            current_embedding = self.sentence_transformer.encode([current_message])
            
            # Find most relevant previous exchanges
            relevant_exchanges = []
            
            for entry in history[-5:]:  # Look at last 5 exchanges
                previous_message = entry['user_message']
                previous_embedding = self.sentence_transformer.encode([previous_message])
                
                similarity = cosine_similarity(current_embedding, previous_embedding)[0][0]
                
                if similarity > self.config.context_similarity_threshold:
                    relevant_exchanges.append({
                        'message': previous_message,
                        'response': entry['bot_response'],
                        'context': entry.get('context', {}),
                        'similarity': similarity
                    })
            
            # Sort by similarity
            relevant_exchanges.sort(key=lambda x: x['similarity'], reverse=True)
            
            return {
                'relevant_exchanges': relevant_exchanges[:3],  # Top 3 most relevant
                'conversation_length': len(history),
                'last_interaction': history[-1]['timestamp'] if history else None
            }
            
        except Exception as e:
            logger.error(f"Error getting relevant context: {str(e)}")
            return {}
    
    def clear_conversation(self, user_id: str):
        """Clear conversation history for a user"""
        try:
            # Clear from memory
            if user_id in self.conversations:
                del self.conversations[user_id]
            
            # Clear from Redis
            if self.redis_client:
                conversation_key = f"conversation:{user_id}"
                self.redis_client.delete(conversation_key)
            
            logger.info(f"Cleared conversation history for user {user_id}")
            
        except Exception as e:
            logger.error(f"Error clearing conversation: {str(e)}")

class ResponseGenerator:
    """Generates conversational responses using various AI models"""
    
    def __init__(self, config: ChatbotConfig):
        self.config = config
        self.openai_client = None
        self.local_model = None
        self.tokenizer = None
        self.sentiment_analyzer = None
        
        # Initialize models
        self._initialize_models()
    
    def _initialize_models(self):
        """Initialize AI models for response generation"""
        try:
            # Initialize OpenAI if API key is available
            if self.config.openai_api_key:
                openai.api_key = self.config.openai_api_key
                self.openai_client = openai
                logger.info("OpenAI client initialized")
            
            # Initialize local conversational model as fallback
            try:
                self.tokenizer = BlenderbotTokenizer.from_pretrained("facebook/blenderbot-400M-distill")
                self.local_model = BlenderbotForConditionalGeneration.from_pretrained("facebook/blenderbot-400M-distill")
                logger.info("Local conversational model loaded")
            except Exception as e:
                logger.warning(f"Could not load local model: {str(e)}")
            
            # Initialize sentiment analyzer
            try:
                self.sentiment_analyzer = pipeline(
                    "sentiment-analysis",
                    model=self.config.sentiment_model,
                    return_all_scores=True
                )
                logger.info("Sentiment analyzer initialized")
            except Exception as e:
                logger.warning(f"Could not load sentiment analyzer: {str(e)}")
                
        except Exception as e:
            logger.error(f"Error initializing response generator: {str(e)}")
    
    async def generate_response(self, message: str, context: Dict, user_info: Dict) -> Dict[str, Any]:
        """Generate conversational response"""
        try:
            # Analyze sentiment
            sentiment = self._analyze_sentiment(message)
            
            # Determine response type
            response_type = self._determine_response_type(message, context)
            
            # Generate appropriate response
            if response_type == 'data_query':
                response = await self._generate_data_response(message, context, user_info)
            elif response_type == 'greeting':
                response = await self._generate_greeting_response(message, context, user_info)
            elif response_type == 'help':
                response = await self._generate_help_response(message, context, user_info)
            elif response_type == 'clarification':
                response = await self._generate_clarification_response(message, context, user_info)
            else:
                response = await self._generate_general_response(message, context, user_info)
            
            # Add personality and style
            response = await self._apply_personality(response, sentiment, user_info)
            
            return {
                'text': response,
                'sentiment': sentiment,
                'response_type': response_type,
                'context': context
            }
            
        except Exception as e:
            logger.error(f"Error generating response: {str(e)}")
            return {
                'text': "I apologize, but I encountered an error while processing your request. Please try again.",
                'error': str(e)
            }
    
    def _analyze_sentiment(self, message: str) -> Dict:
        """Analyze sentiment of user message"""
        try:
            if self.sentiment_analyzer:
                results = self.sentiment_analyzer(message)
                if results and len(results[0]) > 0:
                    # Get the sentiment with highest score
                    best_sentiment = max(results[0], key=lambda x: x['score'])
                    return {
                        'label': best_sentiment['label'],
                        'score': best_sentiment['score'],
                        'all_scores': results[0]
                    }
            
            # Fallback: simple keyword-based sentiment
            positive_words = ['good', 'great', 'excellent', 'amazing', 'wonderful', 'fantastic']
            negative_words = ['bad', 'terrible', 'awful', 'horrible', 'disappointing', 'frustrated']
            
            message_lower = message.lower()
            positive_count = sum(1 for word in positive_words if word in message_lower)
            negative_count = sum(1 for word in negative_words if word in message_lower)
            
            if positive_count > negative_count:
                return {'label': 'POSITIVE', 'score': 0.7}
            elif negative_count > positive_count:
                return {'label': 'NEGATIVE', 'score': 0.7}
            else:
                return {'label': 'NEUTRAL', 'score': 0.8}
                
        except Exception as e:
            logger.error(f"Error analyzing sentiment: {str(e)}")
            return {'label': 'NEUTRAL', 'score': 0.5}
    
    def _determine_response_type(self, message: str, context: Dict) -> str:
        """Determine the type of response needed"""
        message_lower = message.lower()
        
        # Greetings
        if any(word in message_lower for word in ['hello', 'hi', 'hey', 'good morning', 'good afternoon']):
            return 'greeting'
        
        # Help requests
        if any(word in message_lower for word in ['help', 'how do', 'can you', 'what can', 'commands']):
            return 'help'
        
        # Data queries (questions about business data)
        if any(word in message_lower for word in ['sales', 'revenue', 'customers', 'products', 'orders', 'show me', 'what', 'how many']):
            return 'data_query'
        
        # Clarification requests
        if any(word in message_lower for word in ['what do you mean', 'clarify', 'explain', 'more details']):
            return 'clarification'
        
        # Default to general conversation
        return 'general'
    
    async def _generate_data_response(self, message: str, context: Dict, user_info: Dict) -> str:
        """Generate response for data queries"""
        try:
            # This would integrate with the NL Query Engine
            # For now, provide a helpful response about data capabilities
            
            response_templates = [
                "I can help you analyze your business data. Let me look that up for you...",
                "Great question! I'll analyze your data to find that information.",
                "I'll pull that data for you right away. One moment please...",
                "Let me check your analytics data to answer that question."
            ]
            
            # Select response based on context
            if context.get('relevant_exchanges'):
                return "Based on our previous conversation, let me get you updated information on that."
            
            return np.random.choice(response_templates)
            
        except Exception as e:
            logger.error(f"Error generating data response: {str(e)}")
            return "I'd be happy to help you with your data analysis. Could you please rephrase your question?"
    
    async def _generate_greeting_response(self, message: str, context: Dict, user_info: Dict) -> str:
        """Generate greeting response"""
        try:
            user_name = user_info.get('name', 'there')
            time_of_day = datetime.now().hour
            
            if time_of_day < 12:
                time_greeting = "Good morning"
            elif time_of_day < 18:
                time_greeting = "Good afternoon"
            else:
                time_greeting = "Good evening"
            
            if context.get('conversation_length', 0) > 0:
                return f"Welcome back, {user_name}! How can I help you with your analytics today?"
            else:
                return f"{time_greeting}, {user_name}! I'm {self.config.chatbot_name}, your analytics assistant. I can help you explore your business data, generate insights, and answer questions about your e-commerce performance. What would you like to know?"
                
        except Exception as e:
            logger.error(f"Error generating greeting: {str(e)}")
            return f"Hello! I'm {self.config.chatbot_name}, your analytics assistant. How can I help you today?"
    
    async def _generate_help_response(self, message: str, context: Dict, user_info: Dict) -> str:
        """Generate help response"""
        help_text = f"""
I'm {self.config.chatbot_name}, your AI analytics assistant! Here's what I can help you with:

📊 **Data Analysis**
• "What were our sales last month?"
• "Show me our top 10 customers"
• "How many orders did we have this week?"

📈 **Trends & Insights**
• "What's our sales trend over time?"
• "Which products are trending up?"
• "Compare this month vs last month"

🎯 **Business Intelligence**
• "Who are our most valuable customers?"
• "What's our average order value?"
• "Which categories perform best?"

Just ask me questions in plain English, and I'll analyze your data to provide insights and visualizations!

**Example questions:**
• "Show me revenue by product category"
• "What's our customer retention rate?"
• "Which marketing channels drive the most sales?"

Need specific help with something? Just ask!
        """
        
        return help_text.strip()
    
    async def _generate_clarification_response(self, message: str, context: Dict, user_info: Dict) -> str:
        """Generate clarification response"""
        try:
            if context.get('relevant_exchanges'):
                last_exchange = context['relevant_exchanges'][0]
                return f"I was referring to: {last_exchange['response'][:100]}... Would you like me to provide more details or explain differently?"
            
            return "I'd be happy to clarify! Could you let me know which part you'd like me to explain in more detail?"
            
        except Exception as e:
            logger.error(f"Error generating clarification: {str(e)}")
            return "I'd be happy to help clarify. What specific part would you like me to explain?"
    
    async def _generate_general_response(self, message: str, context: Dict, user_info: Dict) -> str:
        """Generate general conversational response"""
        try:
            # Use OpenAI if available
            if self.openai_client:
                return await self._generate_openai_response(message, context, user_info)
            
            # Use local model
            elif self.local_model and self.tokenizer:
                return await self._generate_local_response(message, context, user_info)
            
            # Fallback responses
            else:
                return await self._generate_fallback_response(message, context, user_info)
                
        except Exception as e:
            logger.error(f"Error generating general response: {str(e)}")
            return "I understand. How can I help you with your analytics needs?"
    
    async def _generate_openai_response(self, message: str, context: Dict, user_info: Dict) -> str:
        """Generate response using OpenAI"""
        try:
            # Build conversation context
            messages = [
                {
                    "role": "system",
                    "content": f"""You are {self.config.chatbot_name}, a helpful AI analytics assistant for an e-commerce business. 
                    
                    Your personality: {self.config.chatbot_personality}
                    Response style: {self.config.response_style}
                    
                    You help users understand their business data, generate insights, and answer questions about e-commerce analytics. 
                    Keep responses concise but informative. Always offer to help with data analysis when appropriate.
                    
                    User information: {json.dumps(user_info)}"""
                }
            ]
            
            # Add relevant conversation history
            if context.get('relevant_exchanges'):
                for exchange in context['relevant_exchanges'][-2:]:  # Last 2 relevant exchanges
                    messages.append({"role": "user", "content": exchange['message']})
                    messages.append({"role": "assistant", "content": exchange['response']})
            
            # Add current message
            messages.append({"role": "user", "content": message})
            
            # Generate response
            response = await asyncio.to_thread(
                self.openai_client.ChatCompletion.create,
                model=self.config.openai_model,
                messages=messages,
                max_tokens=self.config.openai_max_tokens,
                temperature=self.config.openai_temperature
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            logger.error(f"Error with OpenAI response: {str(e)}")
            return await self._generate_fallback_response(message, context, user_info)
    
    async def _generate_local_response(self, message: str, context: Dict, user_info: Dict) -> str:
        """Generate response using local model"""
        try:
            # Prepare input
            inputs = self.tokenizer(message, return_tensors="pt")
            
            # Generate response
            reply_ids = self.local_model.generate(**inputs, max_length=150, num_beams=3, temperature=0.7)
            response = self.tokenizer.decode(reply_ids[0], skip_special_tokens=True)
            
            # Remove input from response (BlenderBot includes it)
            if message in response:
                response = response.replace(message, "").strip()
            
            return response or "I understand. How can I help you with your analytics?"
            
        except Exception as e:
            logger.error(f"Error with local model response: {str(e)}")
            return await self._generate_fallback_response(message, context, user_info)
    
    async def _generate_fallback_response(self, message: str, context: Dict, user_info: Dict) -> str:
        """Generate fallback response"""
        fallback_responses = [
            "That's interesting! I'm here to help you with your business analytics. What data would you like to explore?",
            "I understand. Is there anything specific about your business performance you'd like to analyze?",
            "I'm focused on helping you understand your business data. What metrics are you most interested in?",
            "Thanks for sharing! I can help you dive deep into your sales, customer, and product analytics. What would you like to know?",
            "I appreciate that! As your analytics assistant, I'm here to help you make sense of your business data. Any questions about your performance?"
        ]
        
        return np.random.choice(fallback_responses)
    
    async def _apply_personality(self, response: str, sentiment: Dict, user_info: Dict) -> str:
        """Apply personality and style to response"""
        try:
            # Adjust tone based on user sentiment
            if sentiment.get('label') == 'NEGATIVE' and sentiment.get('score', 0) > 0.7:
                # More empathetic tone for negative sentiment
                if not any(word in response.lower() for word in ['sorry', 'understand', 'help']):
                    response = "I understand that might be frustrating. " + response
            
            elif sentiment.get('label') == 'POSITIVE' and sentiment.get('score', 0) > 0.7:
                # More enthusiastic tone for positive sentiment
                if not any(word in response.lower() for word in ['great', 'excellent', 'wonderful']):
                    response = "That's great! " + response
            
            # Apply response style
            if self.config.response_style == 'concise_insightful':
                # Ensure response is not too long
                if len(response) > 300:
                    sentences = response.split('. ')
                    response = '. '.join(sentences[:3]) + '.'
            
            # Add helpful closing when appropriate
            if len(response) < 100 and 'help' not in response.lower():
                response += " Is there anything else I can help you analyze?"
            
            return response
            
        except Exception as e:
            logger.error(f"Error applying personality: {str(e)}")
            return response

class ConversationalAnalyticsChatbot:
    """Main conversational analytics chatbot"""
    
    def __init__(self, config: ChatbotConfig):
        self.config = config
        self.conversation_memory = ConversationMemory(config)
        self.response_generator = ResponseGenerator(config)
        self.nl_query_engine = None
        
        # FastAPI app for web interface
        self.app = FastAPI(title="Analytics Chatbot API")
        self._setup_fastapi_routes()
        
        # Response cache
        self.response_cache = {}
        
        # User sessions
        self.active_sessions = {}
        
        # MLflow setup
        mlflow.set_tracking_uri(self.config.mlflow_tracking_uri)
        mlflow.set_experiment(self.config.experiment_name)
    
    async def initialize(self):
        """Initialize all components"""
        try:
            # Initialize NL Query Engine
            nl_config = NLQueryConfig()
            self.nl_query_engine = NaturalLanguageQueryEngine(nl_config)
            await self.nl_query_engine.initialize()
            
            logger.info("Conversational analytics chatbot initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing chatbot: {str(e)}")
            raise
    
    async def process_message(self, user_id: str, message: str, user_info: Dict = None) -> Dict[str, Any]:
        """Process a user message and generate response"""
        try:
            start_time = datetime.now()
            
            # Default user info
            if user_info is None:
                user_info = {'name': 'User', 'role': 'viewer'}
            
            # Check cache
            if self.config.enable_response_caching:
                cached_response = self._get_cached_response(user_id, message)
                if cached_response:
                    return cached_response
            
            # Get conversation context
            context = self.conversation_memory.get_relevant_context(user_id, message)
            
            # Check if this is a data query
            is_data_query = self._is_data_query(message)
            
            # Generate response
            if is_data_query and self.nl_query_engine:
                response_data = await self._process_data_query(message, user_id, user_info, context)
            else:
                response_data = await self.response_generator.generate_response(message, context, user_info)
            
            # Add conversation metadata
            response_data['conversation_metadata'] = {
                'user_id': user_id,
                'timestamp': datetime.now().isoformat(),
                'processing_time_seconds': (datetime.now() - start_time).total_seconds(),
                'is_data_query': is_data_query,
                'context_available': bool(context.get('relevant_exchanges'))
            }
            
            # Store in conversation memory
            self.conversation_memory.add_message(
                user_id, 
                message, 
                response_data.get('text', ''), 
                context
            )
            
            # Cache response
            if self.config.enable_response_caching:
                self._cache_response(user_id, message, response_data)
            
            # Log metrics
            with mlflow.start_run(run_name=f"chat_message_{int(start_time.timestamp())}"):
                mlflow.log_param("user_id", user_id)
                mlflow.log_param("message_length", len(message))
                mlflow.log_param("is_data_query", is_data_query)
                mlflow.log_metric("processing_time", response_data['conversation_metadata']['processing_time_seconds'])
                mlflow.log_metric("response_length", len(response_data.get('text', '')))
                
                if 'sentiment' in response_data:
                    mlflow.log_param("user_sentiment", response_data['sentiment']['label'])
                    mlflow.log_metric("sentiment_score", response_data['sentiment']['score'])
            
            return response_data
            
        except Exception as e:
            logger.error(f"Error processing message: {str(e)}")
            return {
                'text': "I apologize, but I encountered an error while processing your message. Please try again.",
                'error': str(e),
                'conversation_metadata': {
                    'user_id': user_id,
                    'timestamp': datetime.now().isoformat(),
                    'processing_time_seconds': (datetime.now() - start_time).total_seconds(),
                    'error': True
                }
            }
    
    def _is_data_query(self, message: str) -> bool:
        """Determine if message is a data query"""
        data_query_keywords = [
            'sales', 'revenue', 'customers', 'products', 'orders', 'transactions',
            'show me', 'what are', 'how many', 'total', 'average', 'top', 'best',
            'worst', 'trend', 'growth', 'performance', 'compare', 'vs', 'versus',
            'last month', 'this year', 'yesterday', 'today', 'weekly', 'monthly'
        ]
        
        message_lower = message.lower()
        return any(keyword in message_lower for keyword in data_query_keywords)
    
    async def _process_data_query(self, message: str, user_id: str, user_info: Dict, context: Dict) -> Dict[str, Any]:
        """Process data query using NL Query Engine"""
        try:
            # Check user permissions
            if not self._check_data_permissions(user_info, message):
                return {
                    'text': "I'm sorry, but you don't have permission to access that data. Please contact your administrator for access.",
                    'error': 'permission_denied'
                }
            
            # Process query with NL Engine
            query_result = await self.nl_query_engine.process_query(message, user_id)
            
            if 'error' in query_result:
                return {
                    'text': f"I had trouble understanding your query: {query_result['error']}. Could you please rephrase it?",
                    'error': 'query_processing_error'
                }
            
            # Format response with data
            response_text = self._format_data_response(query_result, message)
            
            # Prepare response data
            response_data = {
                'text': response_text,
                'data': query_result.get('data', [])[:self.config.max_data_rows_in_response],
                'insights': query_result.get('insights', []),
                'visualizations': query_result.get('visualizations', []) if self.config.enable_data_visualization else [],
                'query_metadata': query_result.get('query_metadata', {}),
                'response_type': 'data_query'
            }
            
            return response_data
            
        except Exception as e:
            logger.error(f"Error processing data query: {str(e)}")
            return {
                'text': "I encountered an error while analyzing your data. Please try rephrasing your question.",
                'error': str(e)
            }
    
    def _check_data_permissions(self, user_info: Dict, message: str) -> bool:
        """Check if user has permission to access requested data"""
        try:
            if not self.config.enable_data_filtering:
                return True
            
            user_role = user_info.get('role', 'viewer')
            user_permissions = self.config.user_permissions.get(user_role, [])
            
            # Admin has access to everything
            if 'all' in user_permissions:
                return True
            
            # Check specific permissions
            message_lower = message.lower()
            
            # Check for sensitive data
            for sensitive_field in self.config.sensitive_data_fields:
                if sensitive_field in message_lower:
                    return False  # Deny access to sensitive data
            
            # Check access to specific data types
            if any(perm in message_lower for perm in user_permissions):
                return True
            
            # Default to basic access for simple queries
            basic_keywords = ['total', 'count', 'average', 'summary']
            if any(keyword in message_lower for keyword in basic_keywords):
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking permissions: {str(e)}")
            return True  # Default to allow on error
    
    def _format_data_response(self, query_result: Dict, original_message: str) -> str:
        """Format data query result into conversational response"""
        try:
            data = query_result.get('data', [])
            insights = query_result.get('insights', [])
            intent = query_result.get('query_metadata', {}).get('intent', 'unknown')
            
            # Start with acknowledgment
            response_parts = []
            
            # Add data summary
            if data:
                data_count = len(data)
                if data_count == 1:
                    response_parts.append(f"Here's what I found:")
                else:
                    response_parts.append(f"I found {data_count} results for your query:")
                
                # Add top results in a readable format
                if intent == 'sales_analysis':
                    response_parts.append(self._format_sales_data(data))
                elif intent == 'customer_analysis':
                    response_parts.append(self._format_customer_data(data))
                elif intent == 'product_analysis':
                    response_parts.append(self._format_product_data(data))
                else:
                    response_parts.append(self._format_generic_data(data))
            else:
                response_parts.append("I didn't find any data matching your criteria.")
            
            # Add insights
            if insights:
                response_parts.append("\n**Key Insights:**")
                for insight in insights[:3]:  # Top 3 insights
                    response_parts.append(f"• {insight}")
            
            # Add helpful follow-up
            if data:
                response_parts.append("\nWould you like me to analyze this data further or look at a different aspect?")
            
            return "\n".join(response_parts)
            
        except Exception as e:
            logger.error(f"Error formatting data response: {str(e)}")
            return "I found some data for your query, but had trouble formatting it. Please try a different question."
    
    def _format_sales_data(self, data: List[Dict]) -> str:
        """Format sales data for conversational response"""
        try:
            if not data:
                return "No sales data found."
            
            # Handle different sales data structures
            first_row = data[0]
            
            if 'total_sales' in first_row:
                total = first_row['total_sales']
                return f"💰 Total sales: **${total:,.2f}**"
            
            elif 'sales' in first_row and 'period' in first_row:
                # Time series sales data
                total_sales = sum(row.get('sales', 0) for row in data)
                periods = len(data)
                return f"📈 Sales across {periods} periods: **${total_sales:,.2f}** total"
            
            elif 'product_name' in first_row and 'sales' in first_row:
                # Product sales data
                top_product = data[0]
                return f"🏆 Top product: **{top_product['product_name']}** with **${top_product['sales']:,.2f}** in sales"
            
            else:
                # Generic sales data
                return f"📊 Found {len(data)} sales records"
                
        except Exception as e:
            logger.error(f"Error formatting sales data: {str(e)}")
            return "Sales data found but couldn't format properly."
    
    def _format_customer_data(self, data: List[Dict]) -> str:
        """Format customer data for conversational response"""
        try:
            if not data:
                return "No customer data found."
            
            first_row = data[0]
            
            if 'customer_count' in first_row:
                count = first_row['customer_count']
                return f"👥 Total customers: **{count:,}**"
            
            elif 'total_spent' in first_row:
                # Top customers data
                top_customer = data[0]
                customer_name = top_customer.get('first_name', 'Customer')
                total_spent = top_customer['total_spent']
                return f"🥇 Top customer: **{customer_name}** with **${total_spent:,.2f}** total spending"
            
            else:
                return f"👥 Found {len(data)} customer records"
                
        except Exception as e:
            logger.error(f"Error formatting customer data: {str(e)}")
            return "Customer data found but couldn't format properly."
    
    def _format_product_data(self, data: List[Dict]) -> str:
        """Format product data for conversational response"""
        try:
            if not data:
                return "No product data found."
            
            first_row = data[0]
            
            if 'units_sold' in first_row:
                top_product = data[0]
                product_name = top_product.get('name', 'Product')
                units_sold = top_product['units_sold']
                return f"🏆 Top product: **{product_name}** with **{units_sold:,}** units sold"
            
            elif 'revenue' in first_row:
                top_product = data[0]
                product_name = top_product.get('name', 'Product')
                revenue = top_product['revenue']
                return f"💰 Top revenue product: **{product_name}** with **${revenue:,.2f}**"
            
            else:
                return f"📦 Found {len(data)} product records"
                
        except Exception as e:
            logger.error(f"Error formatting product data: {str(e)}")
            return "Product data found but couldn't format properly."
    
    def _format_generic_data(self, data: List[Dict]) -> str:
        """Format generic data for conversational response"""
        try:
            if not data:
                return "No data found."
            
            # Show first few rows in a readable format
            formatted_rows = []
            for i, row in enumerate(data[:3]):  # Show top 3 rows
                row_parts = []
                for key, value in row.items():
                    if isinstance(value, (int, float)):
                        if key.lower() in ['price', 'amount', 'revenue', 'sales', 'total']:
                            row_parts.append(f"{key}: ${value:,.2f}")
                        else:
                            row_parts.append(f"{key}: {value:,}")
                    else:
                        row_parts.append(f"{key}: {value}")
                
                formatted_rows.append(f"{i+1}. " + ", ".join(row_parts[:3]))  # First 3 columns
            
            result = "\n".join(formatted_rows)
            
            if len(data) > 3:
                result += f"\n... and {len(data) - 3} more records"
            
            return result
            
        except Exception as e:
            logger.error(f"Error formatting generic data: {str(e)}")
            return f"Found {len(data)} records"
    
    def _get_cached_response(self, user_id: str, message: str) -> Optional[Dict]:
        """Get cached response for message"""
        try:
            cache_key = f"{user_id}:{hash(message.lower().strip())}"
            
            if cache_key in self.response_cache:
                cached_item = self.response_cache[cache_key]
                
                # Check if cache is still valid
                cache_time = cached_item['cached_at']
                expiry_time = cache_time + timedelta(minutes=self.config.cache_expiry_minutes)
                
                if datetime.now() < expiry_time:
                    cached_item['response']['conversation_metadata']['from_cache'] = True
                    return cached_item['response']
                else:
                    # Remove expired cache
                    del self.response_cache[cache_key]
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting cached response: {str(e)}")
            return None
    
    def _cache_response(self, user_id: str, message: str, response: Dict):
        """Cache response for future use"""
        try:
            cache_key = f"{user_id}:{hash(message.lower().strip())}"
            
            self.response_cache[cache_key] = {
                'response': response.copy(),
                'cached_at': datetime.now()
            }
            
            # Limit cache size
            if len(self.response_cache) > 1000:
                # Remove oldest entries
                sorted_cache = sorted(
                    self.response_cache.items(),
                    key=lambda x: x[1]['cached_at']
                )
                
                # Keep newest 800 entries
                self.response_cache = dict(sorted_cache[-800:])
            
        except Exception as e:
            logger.error(f"Error caching response: {str(e)}")
    
    def _setup_fastapi_routes(self):
        """Setup FastAPI routes for web interface"""
        
        @self.app.get("/", response_class=HTMLResponse)
        async def get_chat_interface():
            """Serve chat interface"""
            html_content = """
            <!DOCTYPE html>
            <html>
            <head>
                <title>Analytics Chatbot</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
                    .chat-container { max-width: 800px; margin: 0 auto; background: white; border-radius: 10px; padding: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                    .chat-messages { height: 400px; overflow-y: auto; border: 1px solid #ddd; padding: 15px; margin-bottom: 15px; border-radius: 5px; }
                    .message { margin-bottom: 15px; }
                    .user-message { text-align: right; }
                    .bot-message { text-align: left; }
                    .message-text { display: inline-block; padding: 8px 12px; border-radius: 15px; max-width: 70%; }
                    .user-message .message-text { background: #007bff; color: white; }
                    .bot-message .message-text { background: #e9ecef; color: #333; }
                    .input-container { display: flex; gap: 10px; }
                    .input-container input { flex: 1; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
                    .input-container button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
                    .input-container button:hover { background: #0056b3; }
                </style>
            </head>
            <body>
                <div class="chat-container">
                    <h1>🤖 Analytics Chatbot</h1>
                    <div id="chatMessages" class="chat-messages">
                        <div class="message bot-message">
                            <div class="message-text">Hi! I'm your analytics assistant. Ask me questions about your business data!</div>
                        </div>
                    </div>
                    <div class="input-container">
                        <input type="text" id="messageInput" placeholder="Ask me about your sales, customers, products..." onkeypress="if(event.key==='Enter') sendMessage()">
                        <button onclick="sendMessage()">Send</button>
                    </div>
                </div>
                
                <script>
                    async function sendMessage() {
                        const input = document.getElementById('messageInput');
                        const message = input.value.trim();
                        if (!message) return;
                        
                        const chatMessages = document.getElementById('chatMessages');
                        
                        // Add user message
                        const userDiv = document.createElement('div');
                        userDiv.className = 'message user-message';
                        userDiv.innerHTML = `<div class="message-text">${message}</div>`;
                        chatMessages.appendChild(userDiv);
                        
                        input.value = '';
                        chatMessages.scrollTop = chatMessages.scrollHeight;
                        
                        try {
                            const response = await fetch('/chat', {
                                method: 'POST',
                                headers: { 'Content-Type': 'application/json' },
                                body: JSON.stringify({ message: message, user_id: 'web_user' })
                            });
                            
                            const data = await response.json();
                            
                            // Add bot response
                            const botDiv = document.createElement('div');
                            botDiv.className = 'message bot-message';
                            botDiv.innerHTML = `<div class="message-text">${data.text.replace(/\\n/g, '<br>')}</div>`;
                            chatMessages.appendChild(botDiv);
                            
                        } catch (error) {
                            const errorDiv = document.createElement('div');
                            errorDiv.className = 'message bot-message';
                            errorDiv.innerHTML = '<div class="message-text">Sorry, I encountered an error. Please try again.</div>';
                            chatMessages.appendChild(errorDiv);
                        }
                        
                        chatMessages.scrollTop = chatMessages.scrollHeight;
                    }
                </script>
            </body>
            </html>
            """
            return html_content
        
        @self.app.post("/chat")
        async def chat_endpoint(request: dict):
            """Chat API endpoint"""
            try:
                message = request.get('message', '')
                user_id = request.get('user_id', 'anonymous')
                user_info = request.get('user_info', {'name': 'User', 'role': 'viewer'})
                
                if not message:
                    raise HTTPException(status_code=400, detail="Message is required")
                
                response = await self.process_message(user_id, message, user_info)
                return response
                
            except Exception as e:
                logger.error(f"Error in chat endpoint: {str(e)}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/health")
        async def health_check():
            """Health check endpoint"""
            return {"status": "healthy", "chatbot": self.config.chatbot_name}
    
    async def start_server(self):
        """Start the FastAPI server"""
        try:
            import uvicorn
            config = uvicorn.Config(
                self.app,
                host=self.config.server_host,
                port=self.config.server_port,
                log_level="info"
            )
            server = uvicorn.Server(config)
            await server.serve()
            
        except Exception as e:
            logger.error(f"Error starting server: {str(e)}")
            raise

async def main():
    """Main execution function"""
    config = ChatbotConfig()
    
    # Initialize chatbot
    chatbot = ConversationalAnalyticsChatbot(config)
    await chatbot.initialize()
    
    # Test conversation
    test_messages = [
        "Hello!",
        "What were our sales last month?",
        "Show me the top 5 products by revenue",
        "How many customers do we have?",
        "Thank you for your help!"
    ]
    
    user_id = "test_user"
    user_info = {"name": "Test User", "role": "analyst"}
    
    for message in test_messages:
        print(f"\nUser: {message}")
        response = await chatbot.process_message(user_id, message, user_info)
        print(f"Bot: {response.get('text', 'No response')}")
        
        if 'data' in response:
            print(f"Data: {len(response['data'])} records")
        
        if 'insights' in response:
            print(f"Insights: {response['insights']}")
    
    # Start web server (uncomment to run server)
    # await chatbot.start_server()
    
    logger.info("Conversational analytics chatbot test completed")

if __name__ == "__main__":
    asyncio.run(main())