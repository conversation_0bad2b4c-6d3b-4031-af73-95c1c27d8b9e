#!/usr/bin/env python3
"""
Automated Report Generation with NLP for E-commerce Analytics
Advanced system for generating narrative business reports using natural language processing
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import os
import sys
import json
import asyncio
import re
from typing import Dict, List, Tuple, Optional, Any, Union
from dataclasses import dataclass
from collections import defaultdict
import warnings

# Import our analytics engines
from natural_language_query_engine import NaturalLanguageQueryEngine, NLQueryConfig

# NLP and text generation
import openai
import transformers
from transformers import (
    AutoTokenizer, AutoModelForCausalLM, 
    T5ForConditionalGeneration, T5Tokenizer,
    pipeline
)

# Template engines
from jinja2 import Environment, FileSystemLoader, Template
import markdown
from markdown.extensions import codehilite, tables

# Document generation
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Image, Table, TableStyle
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
import io

# Email and distribution
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.application import MIMEApplication

# Visualization
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import plotly.io as pio
import matplotlib.pyplot as plt
import seaborn as sns

# Scheduling
import schedule
from croniter import croniter

# Database and storage
from sqlalchemy import create_engine, text
import boto3

# MLflow integration
import mlflow
import mlflow.transformers
from mlflow.tracking import MlflowClient

warnings.filterwarnings('ignore')

# Configuration
@dataclass
class ReportConfig:
    # Text generation models
    text_generation_model: str = "gpt2-medium"
    summarization_model: str = "t5-small"
    openai_api_key: str = os.getenv('OPENAI_API_KEY', '')
    openai_model: str = "gpt-3.5-turbo"
    
    # Report parameters
    default_report_period: str = "monthly"  # daily, weekly, monthly, quarterly
    max_report_length: int = 5000  # words
    include_visualizations: bool = True
    include_recommendations: bool = True
    include_executive_summary: bool = True
    
    # Report types
    supported_report_types: List[str] = None
    
    # Distribution settings
    enable_email_distribution: bool = True
    enable_slack_distribution: bool = True
    enable_dashboard_publishing: bool = True
    
    # Email configuration
    smtp_server: str = os.getenv('SMTP_SERVER', 'smtp.gmail.com')
    smtp_port: int = int(os.getenv('SMTP_PORT', '587'))
    email_username: str = os.getenv('EMAIL_USERNAME', '')
    email_password: str = os.getenv('EMAIL_PASSWORD', '')
    
    # Slack configuration
    slack_bot_token: str = os.getenv('SLACK_BOT_TOKEN', '')
    
    # AWS S3 for report storage
    s3_bucket: str = os.getenv('S3_BUCKET', 'ecommerce-analytics-reports')
    s3_region: str = os.getenv('AWS_REGION', 'us-east-1')
    
    # Template directories
    template_directory: str = '/templates/reports'
    output_directory: str = '/reports/generated'
    
    # Scheduling
    enable_scheduled_reports: bool = True
    default_schedule: str = "0 9 1 * *"  # Monthly at 9 AM on 1st day
    
    # Database configuration
    db_host: str = os.getenv('DB_HOST', 'analytics-db.cluster-xyz.us-east-1.rds.amazonaws.com')
    db_name: str = os.getenv('DB_NAME', 'ecommerce_analytics')
    db_user: str = os.getenv('DB_USER', 'analytics_user')
    db_password: str = os.getenv('DB_PASSWORD', 'analytics_password')
    
    # MLflow configuration
    mlflow_tracking_uri: str = os.getenv('MLFLOW_TRACKING_URI', 'http://mlflow-server.mlflow:5000')
    experiment_name: str = 'automated-reports'
    
    def __post_init__(self):
        if self.supported_report_types is None:
            self.supported_report_types = [
                'executive_summary', 'sales_performance', 'customer_analytics',
                'product_performance', 'marketing_analysis', 'financial_summary',
                'operational_metrics', 'growth_analysis', 'competitive_analysis'
            ]

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DataAnalyzer:
    """Analyzes data and generates insights for reports"""
    
    def __init__(self, config: ReportConfig):
        self.config = config
        self.nl_query_engine = None
        self.engine = None
        
    async def initialize(self):
        """Initialize data analyzer"""
        try:
            # Initialize NL Query Engine
            nl_config = NLQueryConfig()
            self.nl_query_engine = NaturalLanguageQueryEngine(nl_config)
            await self.nl_query_engine.initialize()
            
            # Initialize database connection
            self.engine = create_engine(
                f"postgresql://{self.config.db_user}:{self.config.db_password}@"
                f"{self.config.db_host}/{self.config.db_name}"
            )
            
            logger.info("Data analyzer initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing data analyzer: {str(e)}")
            raise
    
    async def analyze_sales_performance(self, start_date: str, end_date: str) -> Dict[str, Any]:
        """Analyze sales performance for report period"""
        try:
            analysis = {}
            
            # Total sales
            total_sales_query = f"What was our total sales from {start_date} to {end_date}?"
            total_sales_result = await self.nl_query_engine.process_query(total_sales_query)
            analysis['total_sales'] = total_sales_result
            
            # Sales trend
            sales_trend_query = f"Show me daily sales from {start_date} to {end_date}"
            sales_trend_result = await self.nl_query_engine.process_query(sales_trend_query)
            analysis['sales_trend'] = sales_trend_result
            
            # Top products
            top_products_query = f"What are the top 10 products by revenue from {start_date} to {end_date}?"
            top_products_result = await self.nl_query_engine.process_query(top_products_query)
            analysis['top_products'] = top_products_result
            
            # Sales by category
            category_sales_query = f"Show me sales by product category from {start_date} to {end_date}"
            category_sales_result = await self.nl_query_engine.process_query(category_sales_query)
            analysis['category_sales'] = category_sales_result
            
            # Growth comparison
            prev_start_date = self._get_previous_period_start(start_date, end_date)
            prev_end_date = self._get_previous_period_end(start_date, end_date)
            
            prev_sales_query = f"What was our total sales from {prev_start_date} to {prev_end_date}?"
            prev_sales_result = await self.nl_query_engine.process_query(prev_sales_query)
            analysis['previous_period_sales'] = prev_sales_result
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing sales performance: {str(e)}")
            return {}
    
    async def analyze_customer_metrics(self, start_date: str, end_date: str) -> Dict[str, Any]:
        """Analyze customer metrics for report period"""
        try:
            analysis = {}
            
            # Customer count
            customer_count_query = f"How many unique customers did we have from {start_date} to {end_date}?"
            customer_count_result = await self.nl_query_engine.process_query(customer_count_query)
            analysis['customer_count'] = customer_count_result
            
            # Top customers
            top_customers_query = f"Who are our top 10 customers by total spending from {start_date} to {end_date}?"
            top_customers_result = await self.nl_query_engine.process_query(top_customers_query)
            analysis['top_customers'] = top_customers_result
            
            # New vs returning customers
            new_customers_query = f"How many new customers did we acquire from {start_date} to {end_date}?"
            new_customers_result = await self.nl_query_engine.process_query(new_customers_query)
            analysis['new_customers'] = new_customers_result
            
            # Average order value
            aov_query = f"What was our average order value from {start_date} to {end_date}?"
            aov_result = await self.nl_query_engine.process_query(aov_query)
            analysis['average_order_value'] = aov_result
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing customer metrics: {str(e)}")
            return {}
    
    async def analyze_product_performance(self, start_date: str, end_date: str) -> Dict[str, Any]:
        """Analyze product performance for report period"""
        try:
            analysis = {}
            
            # Best performing products
            best_products_query = f"What are the top 15 products by units sold from {start_date} to {end_date}?"
            best_products_result = await self.nl_query_engine.process_query(best_products_query)
            analysis['best_products'] = best_products_result
            
            # Revenue by product
            revenue_products_query = f"Show me products ranked by revenue from {start_date} to {end_date}"
            revenue_products_result = await self.nl_query_engine.process_query(revenue_products_query)
            analysis['revenue_products'] = revenue_products_result
            
            # Category performance
            category_performance_query = f"Compare product category performance from {start_date} to {end_date}"
            category_performance_result = await self.nl_query_engine.process_query(category_performance_query)
            analysis['category_performance'] = category_performance_result
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing product performance: {str(e)}")
            return {}
    
    async def generate_executive_insights(self, all_analysis: Dict[str, Any]) -> List[str]:
        """Generate executive-level insights from analysis"""
        try:
            insights = []
            
            # Sales insights
            if 'sales_performance' in all_analysis:
                sales_data = all_analysis['sales_performance']
                
                # Total sales insight
                if 'total_sales' in sales_data and sales_data['total_sales'].get('data'):
                    total_current = sales_data['total_sales']['data'][0].get('total_sales', 0)
                    insights.append(f"Generated ${total_current:,.2f} in total revenue during the reporting period")
                
                # Growth insight
                if ('previous_period_sales' in sales_data and 
                    sales_data['previous_period_sales'].get('data') and
                    sales_data['total_sales'].get('data')):
                    
                    current = sales_data['total_sales']['data'][0].get('total_sales', 0)
                    previous = sales_data['previous_period_sales']['data'][0].get('total_sales', 0)
                    
                    if previous > 0:
                        growth_rate = ((current - previous) / previous) * 100
                        if growth_rate > 0:
                            insights.append(f"Sales increased by {growth_rate:.1f}% compared to the previous period")
                        else:
                            insights.append(f"Sales decreased by {abs(growth_rate):.1f}% compared to the previous period")
            
            # Customer insights
            if 'customer_metrics' in all_analysis:
                customer_data = all_analysis['customer_metrics']
                
                if 'customer_count' in customer_data and customer_data['customer_count'].get('data'):
                    customer_count = customer_data['customer_count']['data'][0].get('customer_count', 0)
                    insights.append(f"Served {customer_count:,} unique customers during this period")
                
                if 'average_order_value' in customer_data and customer_data['average_order_value'].get('data'):
                    aov = customer_data['average_order_value']['data'][0].get('avg_amount', 0)
                    insights.append(f"Achieved an average order value of ${aov:.2f}")
            
            # Product insights
            if 'product_performance' in all_analysis:
                product_data = all_analysis['product_performance']
                
                if 'best_products' in product_data and product_data['best_products'].get('data'):
                    top_product = product_data['best_products']['data'][0]
                    product_name = top_product.get('name', 'Unknown')
                    units_sold = top_product.get('units_sold', 0)
                    insights.append(f"Top-selling product was '{product_name}' with {units_sold:,} units sold")
            
            return insights
            
        except Exception as e:
            logger.error(f"Error generating executive insights: {str(e)}")
            return ["Unable to generate insights from the available data"]
    
    def _get_previous_period_start(self, start_date: str, end_date: str) -> str:
        """Calculate previous period start date"""
        try:
            start_dt = datetime.strptime(start_date, '%Y-%m-%d')
            end_dt = datetime.strptime(end_date, '%Y-%m-%d')
            period_length = (end_dt - start_dt).days
            
            prev_start = start_dt - timedelta(days=period_length + 1)
            return prev_start.strftime('%Y-%m-%d')
            
        except Exception as e:
            logger.error(f"Error calculating previous period start: {str(e)}")
            return start_date
    
    def _get_previous_period_end(self, start_date: str, end_date: str) -> str:
        """Calculate previous period end date"""
        try:
            start_dt = datetime.strptime(start_date, '%Y-%m-%d')
            end_dt = datetime.strptime(end_date, '%Y-%m-%d')
            period_length = (end_dt - start_dt).days
            
            prev_end = start_dt - timedelta(days=1)
            return prev_end.strftime('%Y-%m-%d')
            
        except Exception as e:
            logger.error(f"Error calculating previous period end: {str(e)}")
            return end_date

class NarrativeGenerator:
    """Generates narrative text for reports using NLP models"""
    
    def __init__(self, config: ReportConfig):
        self.config = config
        self.text_generator = None
        self.summarizer = None
        self.openai_client = None
        
    def initialize(self):
        """Initialize text generation models"""
        try:
            # Initialize OpenAI if available
            if self.config.openai_api_key:
                openai.api_key = self.config.openai_api_key
                self.openai_client = openai
                logger.info("OpenAI client initialized for text generation")
            
            # Initialize local models as fallback
            try:
                self.text_generator = pipeline(
                    "text-generation",
                    model=self.config.text_generation_model,
                    max_length=500,
                    do_sample=True,
                    temperature=0.7
                )
                logger.info("Local text generation model loaded")
            except Exception as e:
                logger.warning(f"Could not load text generation model: {str(e)}")
            
            try:
                self.summarizer = pipeline(
                    "summarization",
                    model=self.config.summarization_model,
                    max_length=150,
                    min_length=50,
                    do_sample=False
                )
                logger.info("Summarization model loaded")
            except Exception as e:
                logger.warning(f"Could not load summarization model: {str(e)}")
                
        except Exception as e:
            logger.error(f"Error initializing narrative generator: {str(e)}")
    
    async def generate_executive_summary(self, insights: List[str], analysis_data: Dict) -> str:
        """Generate executive summary narrative"""
        try:
            if self.openai_client:
                return await self._generate_openai_summary(insights, analysis_data)
            else:
                return await self._generate_template_summary(insights, analysis_data)
                
        except Exception as e:
            logger.error(f"Error generating executive summary: {str(e)}")
            return self._generate_fallback_summary(insights)
    
    async def _generate_openai_summary(self, insights: List[str], analysis_data: Dict) -> str:
        """Generate summary using OpenAI"""
        try:
            insights_text = "\n".join([f"• {insight}" for insight in insights])
            
            prompt = f"""
            Write a professional executive summary for a business analytics report based on these key insights:
            
            {insights_text}
            
            The summary should be:
            - Concise but comprehensive (2-3 paragraphs)
            - Written for executives and decision-makers
            - Focused on business impact and actionable insights
            - Professional and data-driven in tone
            
            Executive Summary:
            """
            
            response = await asyncio.to_thread(
                self.openai_client.ChatCompletion.create,
                model=self.config.openai_model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=400,
                temperature=0.7
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            logger.error(f"Error with OpenAI summary generation: {str(e)}")
            return await self._generate_template_summary(insights, analysis_data)
    
    async def _generate_template_summary(self, insights: List[str], analysis_data: Dict) -> str:
        """Generate summary using templates"""
        try:
            # Create a structured summary
            summary_parts = []
            
            summary_parts.append("## Executive Summary")
            summary_parts.append("")
            
            # Opening paragraph
            summary_parts.append("This report provides a comprehensive analysis of our business performance during the reporting period. Key highlights include significant metrics across sales, customer engagement, and product performance.")
            summary_parts.append("")
            
            # Key insights
            summary_parts.append("### Key Performance Indicators")
            for insight in insights[:5]:  # Top 5 insights
                summary_parts.append(f"• {insight}")
            
            summary_parts.append("")
            
            # Conclusion
            summary_parts.append("These metrics demonstrate our current market position and provide valuable insights for strategic decision-making. Detailed analysis and recommendations are provided in the following sections.")
            
            return "\n".join(summary_parts)
            
        except Exception as e:
            logger.error(f"Error generating template summary: {str(e)}")
            return self._generate_fallback_summary(insights)
    
    def _generate_fallback_summary(self, insights: List[str]) -> str:
        """Generate basic fallback summary"""
        summary = "## Executive Summary\n\n"
        summary += "During the reporting period, our analysis revealed several key performance indicators:\n\n"
        
        for insight in insights[:5]:
            summary += f"• {insight}\n"
        
        summary += "\nThese findings provide important insights into our business performance and market position."
        
        return summary
    
    async def generate_section_narrative(self, section_title: str, data: Dict, insights: List[str]) -> str:
        """Generate narrative for a specific report section"""
        try:
            if self.openai_client:
                return await self._generate_openai_section(section_title, data, insights)
            else:
                return await self._generate_template_section(section_title, data, insights)
                
        except Exception as e:
            logger.error(f"Error generating section narrative: {str(e)}")
            return self._generate_fallback_section(section_title, insights)
    
    async def _generate_openai_section(self, section_title: str, data: Dict, insights: List[str]) -> str:
        """Generate section using OpenAI"""
        try:
            insights_text = "\n".join([f"• {insight}" for insight in insights])
            
            prompt = f"""
            Write a professional section for a business analytics report with the title "{section_title}".
            
            Key insights for this section:
            {insights_text}
            
            The section should be:
            - Professional and analytical in tone
            - Include specific data points and metrics
            - Provide actionable insights
            - Be 2-3 paragraphs long
            - Include relevant business context
            
            {section_title}:
            """
            
            response = await asyncio.to_thread(
                self.openai_client.ChatCompletion.create,
                model=self.config.openai_model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=350,
                temperature=0.7
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            logger.error(f"Error with OpenAI section generation: {str(e)}")
            return await self._generate_template_section(section_title, data, insights)
    
    async def _generate_template_section(self, section_title: str, data: Dict, insights: List[str]) -> str:
        """Generate section using templates"""
        try:
            section_parts = []
            
            section_parts.append(f"## {section_title}")
            section_parts.append("")
            
            # Introduction
            section_parts.append(f"Our analysis of {section_title.lower()} reveals important trends and performance indicators during the reporting period.")
            section_parts.append("")
            
            # Key findings
            section_parts.append("### Key Findings")
            for insight in insights:
                section_parts.append(f"• {insight}")
            
            section_parts.append("")
            
            # Analysis paragraph
            if insights:
                section_parts.append("These metrics indicate strong performance across key areas, with notable achievements in customer engagement and revenue generation. The data suggests opportunities for continued growth and optimization in the coming period.")
            
            return "\n".join(section_parts)
            
        except Exception as e:
            logger.error(f"Error generating template section: {str(e)}")
            return self._generate_fallback_section(section_title, insights)
    
    def _generate_fallback_section(self, section_title: str, insights: List[str]) -> str:
        """Generate basic fallback section"""
        section = f"## {section_title}\n\n"
        
        if insights:
            section += "Key findings:\n\n"
            for insight in insights:
                section += f"• {insight}\n"
        else:
            section += "Analysis completed for this section.\n"
        
        return section
    
    async def generate_recommendations(self, analysis_data: Dict) -> List[str]:
        """Generate actionable recommendations based on analysis"""
        try:
            recommendations = []
            
            # Sales recommendations
            if 'sales_performance' in analysis_data:
                sales_data = analysis_data['sales_performance']
                
                # Growth recommendations
                if ('total_sales' in sales_data and 'previous_period_sales' in sales_data and
                    sales_data['total_sales'].get('data') and sales_data['previous_period_sales'].get('data')):
                    
                    current = sales_data['total_sales']['data'][0].get('total_sales', 0)
                    previous = sales_data['previous_period_sales']['data'][0].get('total_sales', 0)
                    
                    if previous > 0:
                        growth_rate = ((current - previous) / previous) * 100
                        if growth_rate < 0:
                            recommendations.append("Focus on customer retention and acquisition strategies to reverse the sales decline")
                        elif growth_rate < 5:
                            recommendations.append("Implement growth initiatives to accelerate sales performance beyond current levels")
                
                # Product recommendations
                if 'top_products' in sales_data and sales_data['top_products'].get('data'):
                    top_products = sales_data['top_products']['data'][:3]
                    product_names = [p.get('name', 'Product') for p in top_products]
                    recommendations.append(f"Increase marketing focus on top-performing products: {', '.join(product_names)}")
            
            # Customer recommendations
            if 'customer_metrics' in analysis_data:
                customer_data = analysis_data['customer_metrics']
                
                if 'average_order_value' in customer_data and customer_data['average_order_value'].get('data'):
                    aov = customer_data['average_order_value']['data'][0].get('avg_amount', 0)
                    if aov < 100:  # Arbitrary threshold
                        recommendations.append("Implement upselling and cross-selling strategies to increase average order value")
            
            # Default recommendations if none generated
            if not recommendations:
                recommendations = [
                    "Continue monitoring key performance indicators for trend analysis",
                    "Focus on customer satisfaction and retention strategies",
                    "Optimize marketing campaigns based on product performance data",
                    "Implement data-driven decision making across all business units"
                ]
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Error generating recommendations: {str(e)}")
            return ["Review current business metrics and consider strategic improvements"]

class ReportTemplateEngine:
    """Manages report templates and formatting"""
    
    def __init__(self, config: ReportConfig):
        self.config = config
        self.jinja_env = None
        self._initialize_templates()
    
    def _initialize_templates(self):
        """Initialize Jinja2 template environment"""
        try:
            if os.path.exists(self.config.template_directory):
                self.jinja_env = Environment(loader=FileSystemLoader(self.config.template_directory))
            else:
                # Create default templates
                os.makedirs(self.config.template_directory, exist_ok=True)
                self._create_default_templates()
                self.jinja_env = Environment(loader=FileSystemLoader(self.config.template_directory))
            
            logger.info("Report templates initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing templates: {str(e)}")
    
    def _create_default_templates(self):
        """Create default report templates"""
        try:
            # HTML template
            html_template = """
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>{{ report_title }}</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; color: #333; }
                    .header { border-bottom: 3px solid #007bff; padding-bottom: 20px; margin-bottom: 30px; }
                    .report-title { color: #007bff; font-size: 28px; margin: 0; }
                    .report-subtitle { color: #666; font-size: 16px; margin: 5px 0 0 0; }
                    .section { margin-bottom: 40px; }
                    .section h2 { color: #007bff; border-bottom: 2px solid #e9ecef; padding-bottom: 10px; }
                    .metric-card { background: #f8f9fa; border-left: 4px solid #007bff; padding: 15px; margin: 15px 0; }
                    .metric-value { font-size: 24px; font-weight: bold; color: #007bff; }
                    .metric-label { color: #666; font-size: 14px; }
                    .insights ul { padding-left: 20px; }
                    .insights li { margin-bottom: 10px; }
                    .recommendations { background: #e7f3ff; border: 1px solid #b8daff; border-radius: 5px; padding: 20px; }
                    .chart-container { text-align: center; margin: 20px 0; }
                    .footer { border-top: 1px solid #e9ecef; padding-top: 20px; margin-top: 40px; text-align: center; color: #666; }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1 class="report-title">{{ report_title }}</h1>
                    <p class="report-subtitle">{{ report_period }} | Generated on {{ generation_date }}</p>
                </div>
                
                {% if executive_summary %}
                <div class="section">
                    {{ executive_summary | safe }}
                </div>
                {% endif %}
                
                {% for section in sections %}
                <div class="section">
                    {{ section.content | safe }}
                </div>
                {% endfor %}
                
                {% if recommendations %}
                <div class="section">
                    <h2>Recommendations</h2>
                    <div class="recommendations">
                        <ul>
                        {% for recommendation in recommendations %}
                            <li>{{ recommendation }}</li>
                        {% endfor %}
                        </ul>
                    </div>
                </div>
                {% endif %}
                
                <div class="footer">
                    <p>This report was automatically generated by the Analytics System</p>
                    <p>For questions or additional analysis, please contact the analytics team</p>
                </div>
            </body>
            </html>
            """
            
            with open(f"{self.config.template_directory}/report_template.html", 'w') as f:
                f.write(html_template)
            
            # Markdown template
            markdown_template = """
# {{ report_title }}

**{{ report_period }}** | Generated on {{ generation_date }}

---

{% if executive_summary %}
{{ executive_summary }}

---
{% endif %}

{% for section in sections %}
{{ section.content }}

---
{% endfor %}

{% if recommendations %}
## Recommendations

{% for recommendation in recommendations %}
- {{ recommendation }}
{% endfor %}

---
{% endif %}

*This report was automatically generated by the Analytics System*
            """
            
            with open(f"{self.config.template_directory}/report_template.md", 'w') as f:
                f.write(markdown_template)
            
            logger.info("Default templates created successfully")
            
        except Exception as e:
            logger.error(f"Error creating default templates: {str(e)}")
    
    def render_html_report(self, report_data: Dict) -> str:
        """Render report as HTML"""
        try:
            template = self.jinja_env.get_template('report_template.html')
            return template.render(**report_data)
            
        except Exception as e:
            logger.error(f"Error rendering HTML report: {str(e)}")
            return f"<html><body><h1>Error rendering report: {str(e)}</h1></body></html>"
    
    def render_markdown_report(self, report_data: Dict) -> str:
        """Render report as Markdown"""
        try:
            template = self.jinja_env.get_template('report_template.md')
            return template.render(**report_data)
            
        except Exception as e:
            logger.error(f"Error rendering Markdown report: {str(e)}")
            return f"# Error rendering report: {str(e)}"
    
    def convert_markdown_to_html(self, markdown_content: str) -> str:
        """Convert Markdown to HTML"""
        try:
            html = markdown.markdown(
                markdown_content,
                extensions=['tables', 'codehilite', 'toc']
            )
            return html
            
        except Exception as e:
            logger.error(f"Error converting Markdown to HTML: {str(e)}")
            return markdown_content

class ReportDistributor:
    """Handles report distribution via email, Slack, etc."""
    
    def __init__(self, config: ReportConfig):
        self.config = config
        self.s3_client = None
        
        if config.enable_email_distribution:
            self._initialize_s3()
    
    def _initialize_s3(self):
        """Initialize S3 client for report storage"""
        try:
            self.s3_client = boto3.client('s3', region_name=self.config.s3_region)
            logger.info("S3 client initialized for report storage")
        except Exception as e:
            logger.warning(f"Could not initialize S3 client: {str(e)}")
    
    async def distribute_report(self, report_content: str, report_format: str, 
                              recipients: List[str], report_title: str) -> Dict[str, bool]:
        """Distribute report to specified recipients"""
        try:
            distribution_results = {}
            
            # Save report to S3
            if self.s3_client:
                s3_url = await self._upload_to_s3(report_content, report_format, report_title)
                distribution_results['s3_upload'] = bool(s3_url)
            
            # Email distribution
            if self.config.enable_email_distribution and recipients:
                email_success = await self._send_email_report(
                    report_content, report_format, recipients, report_title
                )
                distribution_results['email'] = email_success
            
            # Slack distribution
            if self.config.enable_slack_distribution and self.config.slack_bot_token:
                slack_success = await self._send_slack_report(report_content, report_title)
                distribution_results['slack'] = slack_success
            
            return distribution_results
            
        except Exception as e:
            logger.error(f"Error distributing report: {str(e)}")
            return {'error': str(e)}
    
    async def _upload_to_s3(self, content: str, format: str, title: str) -> Optional[str]:
        """Upload report to S3"""
        try:
            if not self.s3_client:
                return None
            
            # Generate filename
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{title.replace(' ', '_').lower()}_{timestamp}.{format}"
            
            # Upload to S3
            self.s3_client.put_object(
                Bucket=self.config.s3_bucket,
                Key=f"reports/{filename}",
                Body=content.encode('utf-8'),
                ContentType='text/html' if format == 'html' else 'text/plain'
            )
            
            s3_url = f"https://{self.config.s3_bucket}.s3.{self.config.s3_region}.amazonaws.com/reports/{filename}"
            logger.info(f"Report uploaded to S3: {s3_url}")
            return s3_url
            
        except Exception as e:
            logger.error(f"Error uploading to S3: {str(e)}")
            return None
    
    async def _send_email_report(self, content: str, format: str, recipients: List[str], title: str) -> bool:
        """Send report via email"""
        try:
            if not self.config.email_username or not self.config.email_password:
                logger.warning("Email credentials not configured")
                return False
            
            # Create message
            msg = MIMEMultipart('alternative')
            msg['Subject'] = f"Analytics Report: {title}"
            msg['From'] = self.config.email_username
            msg['To'] = ', '.join(recipients)
            
            # Attach content
            if format == 'html':
                html_part = MIMEText(content, 'html')
                msg.attach(html_part)
            else:
                text_part = MIMEText(content, 'plain')
                msg.attach(text_part)
            
            # Send email
            with smtplib.SMTP(self.config.smtp_server, self.config.smtp_port) as server:
                server.starttls()
                server.login(self.config.email_username, self.config.email_password)
                server.send_message(msg)
            
            logger.info(f"Report emailed to {len(recipients)} recipients")
            return True
            
        except Exception as e:
            logger.error(f"Error sending email report: {str(e)}")
            return False
    
    async def _send_slack_report(self, content: str, title: str) -> bool:
        """Send report summary to Slack"""
        try:
            # This would use Slack SDK to send report summary
            # For now, just log the action
            logger.info(f"Slack report distribution for: {title}")
            return True
            
        except Exception as e:
            logger.error(f"Error sending Slack report: {str(e)}")
            return False

class AutomatedReportGenerator:
    """Main automated report generation system"""
    
    def __init__(self, config: ReportConfig):
        self.config = config
        self.data_analyzer = DataAnalyzer(config)
        self.narrative_generator = NarrativeGenerator(config)
        self.template_engine = ReportTemplateEngine(config)
        self.distributor = ReportDistributor(config)
        
        # Scheduled jobs
        self.scheduled_reports = {}
        
        # MLflow setup
        mlflow.set_tracking_uri(self.config.mlflow_tracking_uri)
        mlflow.set_experiment(self.config.experiment_name)
    
    async def initialize(self):
        """Initialize all components"""
        try:
            await self.data_analyzer.initialize()
            self.narrative_generator.initialize()
            
            logger.info("Automated report generator initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing report generator: {str(e)}")
            raise
    
    async def generate_report(self, report_type: str, start_date: str, end_date: str,
                            recipients: List[str] = None, custom_title: str = None) -> Dict[str, Any]:
        """Generate a complete report"""
        try:
            start_time = datetime.now()
            
            # Validate report type
            if report_type not in self.config.supported_report_types:
                raise ValueError(f"Unsupported report type: {report_type}")
            
            # Set report title
            report_title = custom_title or f"{report_type.replace('_', ' ').title()} Report"
            
            # Analyze data
            logger.info(f"Analyzing data for {report_type} report...")
            analysis_data = await self._analyze_data_for_report_type(report_type, start_date, end_date)
            
            # Generate insights
            logger.info("Generating executive insights...")
            executive_insights = await self.data_analyzer.generate_executive_insights(analysis_data)
            
            # Generate narrative content
            logger.info("Generating narrative content...")
            executive_summary = await self.narrative_generator.generate_executive_summary(
                executive_insights, analysis_data
            )
            
            # Generate sections
            sections = await self._generate_report_sections(report_type, analysis_data)
            
            # Generate recommendations
            logger.info("Generating recommendations...")
            recommendations = await self.narrative_generator.generate_recommendations(analysis_data)
            
            # Prepare report data
            report_data = {
                'report_title': report_title,
                'report_period': f"{start_date} to {end_date}",
                'generation_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'executive_summary': executive_summary,
                'sections': sections,
                'recommendations': recommendations,
                'analysis_data': analysis_data
            }
            
            # Render report
            logger.info("Rendering report...")
            html_content = self.template_engine.render_html_report(report_data)
            markdown_content = self.template_engine.render_markdown_report(report_data)
            
            # Distribute report
            distribution_results = {}
            if recipients:
                logger.info("Distributing report...")
                distribution_results = await self.distributor.distribute_report(
                    html_content, 'html', recipients, report_title
                )
            
            # Log metrics
            processing_time = (datetime.now() - start_time).total_seconds()
            
            with mlflow.start_run(run_name=f"report_generation_{report_type}"):
                mlflow.log_param("report_type", report_type)
                mlflow.log_param("start_date", start_date)
                mlflow.log_param("end_date", end_date)
                mlflow.log_param("report_title", report_title)
                mlflow.log_metric("processing_time_seconds", processing_time)
                mlflow.log_metric("content_length_html", len(html_content))
                mlflow.log_metric("content_length_markdown", len(markdown_content))
                mlflow.log_metric("insights_count", len(executive_insights))
                mlflow.log_metric("recommendations_count", len(recommendations))
                
                if recipients:
                    mlflow.log_param("recipients_count", len(recipients))
            
            logger.info(f"Report generation completed in {processing_time:.2f} seconds")
            
            return {
                'success': True,
                'report_title': report_title,
                'html_content': html_content,
                'markdown_content': markdown_content,
                'distribution_results': distribution_results,
                'processing_time_seconds': processing_time,
                'insights_count': len(executive_insights),
                'recommendations_count': len(recommendations)
            }
            
        except Exception as e:
            logger.error(f"Error generating report: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'processing_time_seconds': (datetime.now() - start_time).total_seconds()
            }
    
    async def _analyze_data_for_report_type(self, report_type: str, start_date: str, end_date: str) -> Dict[str, Any]:
        """Analyze data based on report type"""
        try:
            analysis_data = {}
            
            if report_type in ['executive_summary', 'sales_performance', 'financial_summary']:
                analysis_data['sales_performance'] = await self.data_analyzer.analyze_sales_performance(
                    start_date, end_date
                )
            
            if report_type in ['executive_summary', 'customer_analytics']:
                analysis_data['customer_metrics'] = await self.data_analyzer.analyze_customer_metrics(
                    start_date, end_date
                )
            
            if report_type in ['executive_summary', 'product_performance']:
                analysis_data['product_performance'] = await self.data_analyzer.analyze_product_performance(
                    start_date, end_date
                )
            
            return analysis_data
            
        except Exception as e:
            logger.error(f"Error analyzing data for report type {report_type}: {str(e)}")
            return {}
    
    async def _generate_report_sections(self, report_type: str, analysis_data: Dict) -> List[Dict]:
        """Generate sections based on report type and analysis data"""
        try:
            sections = []
            
            # Sales Performance Section
            if 'sales_performance' in analysis_data:
                sales_insights = analysis_data['sales_performance'].get('total_sales', {}).get('insights', [])
                sales_content = await self.narrative_generator.generate_section_narrative(
                    "Sales Performance", analysis_data['sales_performance'], sales_insights
                )
                sections.append({
                    'title': 'Sales Performance',
                    'content': sales_content
                })
            
            # Customer Analytics Section
            if 'customer_metrics' in analysis_data:
                customer_insights = analysis_data['customer_metrics'].get('customer_count', {}).get('insights', [])
                customer_content = await self.narrative_generator.generate_section_narrative(
                    "Customer Analytics", analysis_data['customer_metrics'], customer_insights
                )
                sections.append({
                    'title': 'Customer Analytics',
                    'content': customer_content
                })
            
            # Product Performance Section
            if 'product_performance' in analysis_data:
                product_insights = analysis_data['product_performance'].get('best_products', {}).get('insights', [])
                product_content = await self.narrative_generator.generate_section_narrative(
                    "Product Performance", analysis_data['product_performance'], product_insights
                )
                sections.append({
                    'title': 'Product Performance',
                    'content': product_content
                })
            
            return sections
            
        except Exception as e:
            logger.error(f"Error generating report sections: {str(e)}")
            return []
    
    def schedule_report(self, report_type: str, schedule_expression: str, 
                       recipients: List[str], report_config: Dict = None):
        """Schedule a recurring report"""
        try:
            job_id = f"{report_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            self.scheduled_reports[job_id] = {
                'report_type': report_type,
                'schedule': schedule_expression,
                'recipients': recipients,
                'config': report_config or {},
                'created_at': datetime.now(),
                'active': True
            }
            
            # Parse cron expression and schedule
            cron = croniter(schedule_expression, datetime.now())
            next_run = cron.get_next(datetime)
            
            logger.info(f"Scheduled {report_type} report (ID: {job_id}) for next run: {next_run}")
            
            return job_id
            
        except Exception as e:
            logger.error(f"Error scheduling report: {str(e)}")
            return None
    
    async def run_scheduled_reports(self):
        """Run scheduled reports that are due"""
        try:
            current_time = datetime.now()
            
            for job_id, job_config in self.scheduled_reports.items():
                if not job_config['active']:
                    continue
                
                try:
                    # Check if report is due
                    cron = croniter(job_config['schedule'], job_config.get('last_run', current_time))
                    next_run = cron.get_next(datetime)
                    
                    if next_run <= current_time:
                        logger.info(f"Running scheduled report: {job_id}")
                        
                        # Calculate date range (default to last month)
                        end_date = current_time.strftime('%Y-%m-%d')
                        start_date = (current_time - timedelta(days=30)).strftime('%Y-%m-%d')
                        
                        # Generate report
                        result = await self.generate_report(
                            job_config['report_type'],
                            start_date,
                            end_date,
                            job_config['recipients']
                        )
                        
                        # Update last run time
                        self.scheduled_reports[job_id]['last_run'] = current_time
                        
                        if result['success']:
                            logger.info(f"Scheduled report {job_id} completed successfully")
                        else:
                            logger.error(f"Scheduled report {job_id} failed: {result.get('error')}")
                
                except Exception as e:
                    logger.error(f"Error running scheduled report {job_id}: {str(e)}")
                    
        except Exception as e:
            logger.error(f"Error running scheduled reports: {str(e)}")

async def main():
    """Main execution function"""
    config = ReportConfig()
    
    # Initialize report generator
    generator = AutomatedReportGenerator(config)
    await generator.initialize()
    
    # Generate test report
    start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    end_date = datetime.now().strftime('%Y-%m-%d')
    
    result = await generator.generate_report(
        report_type='executive_summary',
        start_date=start_date,
        end_date=end_date,
        recipients=['<EMAIL>'],
        custom_title='Monthly Business Performance Report'
    )
    
    if result['success']:
        print(f"Report generated successfully!")
        print(f"Processing time: {result['processing_time_seconds']:.2f} seconds")
        print(f"Insights generated: {result['insights_count']}")
        print(f"Recommendations: {result['recommendations_count']}")
        
        # Save report to file
        output_dir = config.output_directory
        os.makedirs(output_dir, exist_ok=True)
        
        with open(f"{output_dir}/test_report.html", 'w') as f:
            f.write(result['html_content'])
        
        print(f"Report saved to {output_dir}/test_report.html")
    else:
        print(f"Report generation failed: {result['error']}")
    
    logger.info("Automated report generator test completed")

if __name__ == "__main__":
    asyncio.run(main())