#!/usr/bin/env python3
"""
Natural Language Query Engine for E-commerce Analytics
Advanced NLP system for converting natural language queries to SQL and analytics insights
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import os
import sys
import json
import asyncio
import re
from typing import Dict, List, Tuple, Optional, Any, Union
from dataclasses import dataclass
from collections import defaultdict
import warnings

# NLP and language models
import spacy
import nltk
from nltk.corpus import stopwords
from nltk.tokenize import word_tokenize, sent_tokenize
from nltk.stem import WordNetLemmatizer
from nltk.chunk import ne_chunk
from nltk.tag import pos_tag

# Advanced NLP libraries
import transformers
from transformers import (
    AutoTokenizer, AutoModelForSequenceClassification,
    AutoModelForQuestionAnswering, pipeline
)
from sentence_transformers import SentenceTransformer
import openai

# SQL generation and parsing
import sqlparse
from sqlalchemy import create_engine, text, MetaData, inspect
import sqlalchemy as sa

# Text similarity and matching
from fuzzywuzzy import fuzz, process
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity

# Date/time parsing
import dateparser
import parsedatetime

# Database schema understanding
from sqlalchemy.schema import Table, Column
from sqlalchemy.types import String, Integer, Float, DateTime, Boolean

# Visualization generation
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import matplotlib.pyplot as plt
import seaborn as sns

# MLflow integration
import mlflow
import mlflow.transformers
from mlflow.tracking import MlflowClient

warnings.filterwarnings('ignore')

# Configuration
@dataclass
class NLQueryConfig:
    # Model configurations
    intent_model_name: str = "microsoft/DialoGPT-medium"
    qa_model_name: str = "distilbert-base-cased-distilled-squad"
    sentence_transformer_model: str = "all-MiniLM-L6-v2"
    
    # OpenAI API (optional)
    openai_api_key: str = os.getenv('OPENAI_API_KEY', '')
    openai_model: str = "gpt-3.5-turbo"
    
    # Database connection
    db_host: str = os.getenv('DB_HOST', 'analytics-db.cluster-xyz.us-east-1.rds.amazonaws.com')
    db_name: str = os.getenv('DB_NAME', 'ecommerce_analytics')
    db_user: str = os.getenv('DB_USER', 'analytics_user')
    db_password: str = os.getenv('DB_PASSWORD', 'analytics_password')
    
    # Query understanding parameters
    confidence_threshold: float = 0.7
    max_results: int = 1000
    query_timeout_seconds: int = 30
    
    # Cache settings
    enable_query_cache: bool = True
    cache_expiry_hours: int = 24
    
    # Supported query types
    supported_intents: List[str] = None
    
    # MLflow configuration
    mlflow_tracking_uri: str = os.getenv('MLFLOW_TRACKING_URI', 'http://mlflow-server.mlflow:5000')
    experiment_name: str = 'natural-language-queries'
    
    def __post_init__(self):
        if self.supported_intents is None:
            self.supported_intents = [
                'sales_analysis', 'customer_analysis', 'product_analysis',
                'revenue_analysis', 'trend_analysis', 'comparison',
                'forecasting', 'anomaly_detection', 'segmentation'
            ]

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DatabaseSchemaMapper:
    """Maps database schema to natural language concepts"""
    
    def __init__(self, config: NLQueryConfig):
        self.config = config
        self.engine = None
        self.metadata = None
        self.schema_mapping = {}
        self.column_descriptions = {}
        self.table_relationships = {}
        
    def initialize(self):
        """Initialize database connection and load schema"""
        try:
            self.engine = create_engine(
                f"postgresql://{self.config.db_user}:{self.config.db_password}@"
                f"{self.config.db_host}/{self.config.db_name}"
            )
            
            self.metadata = MetaData()
            self.metadata.reflect(bind=self.engine)
            
            self._build_schema_mapping()
            self._build_column_descriptions()
            self._build_table_relationships()
            
            logger.info("Database schema loaded successfully")
            
        except Exception as e:
            logger.error(f"Error initializing database schema: {str(e)}")
            raise
    
    def _build_schema_mapping(self):
        """Build mapping from natural language to database tables"""
        self.schema_mapping = {
            # Table mappings
            'customers': ['customers', 'users', 'buyers', 'clients', 'shoppers'],
            'orders': ['orders', 'purchases', 'transactions', 'sales', 'checkouts'],
            'products': ['products', 'items', 'goods', 'merchandise', 'catalog'],
            'order_items': ['order_items', 'line_items', 'cart_items', 'purchased_items'],
            'product_reviews': ['reviews', 'ratings', 'feedback', 'comments'],
            'merchants': ['merchants', 'sellers', 'vendors', 'stores', 'shops'],
            'categories': ['categories', 'product_categories', 'sections', 'departments'],
            'payments': ['payments', 'payment_methods', 'billing', 'charges'],
            
            # Common analytics concepts
            'revenue': ['revenue', 'sales', 'income', 'earnings', 'money', 'dollars'],
            'profit': ['profit', 'margin', 'earnings', 'net_income'],
            'quantity': ['quantity', 'amount', 'count', 'number', 'volume'],
            'price': ['price', 'cost', 'amount', 'value', 'rate'],
            'date': ['date', 'time', 'when', 'period', 'month', 'year', 'day'],
            'customer_id': ['customer', 'user', 'buyer', 'client'],
            'product_id': ['product', 'item', 'goods'],
            'order_id': ['order', 'purchase', 'transaction']
        }
    
    def _build_column_descriptions(self):
        """Build descriptions for database columns"""
        self.column_descriptions = {
            'customers': {
                'customer_id': 'Unique identifier for customers',
                'email': 'Customer email address',
                'first_name': 'Customer first name',
                'last_name': 'Customer last name',
                'registration_date': 'When customer registered',
                'total_orders': 'Total number of orders placed',
                'total_spent': 'Total amount spent by customer',
                'city': 'Customer city',
                'state': 'Customer state',
                'country': 'Customer country'
            },
            'orders': {
                'order_id': 'Unique identifier for orders',
                'customer_id': 'Customer who placed the order',
                'order_date': 'When the order was placed',
                'total_amount': 'Total order value',
                'order_status': 'Current status of the order',
                'payment_method': 'How the order was paid',
                'shipping_address': 'Where the order is shipped'
            },
            'products': {
                'product_id': 'Unique identifier for products',
                'name': 'Product name',
                'description': 'Product description',
                'price': 'Product price',
                'category_id': 'Product category',
                'brand': 'Product brand',
                'stock_quantity': 'Available inventory',
                'created_at': 'When product was added'
            },
            'order_items': {
                'order_item_id': 'Unique identifier for order items',
                'order_id': 'Associated order',
                'product_id': 'Product in the order',
                'quantity': 'Quantity ordered',
                'unit_price': 'Price per unit',
                'total_price': 'Total price for this line item'
            }
        }
    
    def _build_table_relationships(self):
        """Build table relationship mappings"""
        self.table_relationships = {
            'customers': {
                'orders': 'customers.customer_id = orders.customer_id',
                'product_reviews': 'customers.customer_id = product_reviews.customer_id'
            },
            'orders': {
                'customers': 'orders.customer_id = customers.customer_id',
                'order_items': 'orders.order_id = order_items.order_id',
                'payments': 'orders.order_id = payments.order_id'
            },
            'products': {
                'order_items': 'products.product_id = order_items.product_id',
                'product_reviews': 'products.product_id = product_reviews.product_id',
                'categories': 'products.category_id = categories.category_id'
            },
            'order_items': {
                'orders': 'order_items.order_id = orders.order_id',
                'products': 'order_items.product_id = products.product_id'
            }
        }
    
    def find_table_for_concept(self, concept: str) -> Optional[str]:
        """Find database table for a natural language concept"""
        concept_lower = concept.lower()
        
        for table, synonyms in self.schema_mapping.items():
            if concept_lower in synonyms:
                return table
        
        # Try fuzzy matching
        all_concepts = []
        table_map = {}
        for table, synonyms in self.schema_mapping.items():
            for synonym in synonyms:
                all_concepts.append(synonym)
                table_map[synonym] = table
        
        best_match = process.extractOne(concept_lower, all_concepts)
        if best_match and best_match[1] > 80:  # 80% similarity threshold
            return table_map[best_match[0]]
        
        return None
    
    def find_column_for_concept(self, concept: str, table: str = None) -> Tuple[Optional[str], Optional[str]]:
        """Find database column for a natural language concept"""
        concept_lower = concept.lower()
        
        # Direct mapping
        for table_name, columns in self.column_descriptions.items():
            if table and table_name != table:
                continue
                
            for column, description in columns.items():
                if concept_lower in column.lower() or concept_lower in description.lower():
                    return table_name, column
        
        # Try schema mapping
        for mapped_concept, synonyms in self.schema_mapping.items():
            if concept_lower in synonyms:
                # Look for this concept as a column
                for table_name, columns in self.column_descriptions.items():
                    if table and table_name != table:
                        continue
                    if mapped_concept in columns:
                        return table_name, mapped_concept
        
        return None, None

class QueryIntentClassifier:
    """Classify the intent of natural language queries"""
    
    def __init__(self, config: NLQueryConfig):
        self.config = config
        self.model = None
        self.tokenizer = None
        self.sentence_transformer = None
        self.intent_patterns = {}
        self._build_intent_patterns()
        
    def initialize(self):
        """Initialize NLP models"""
        try:
            # Load sentence transformer for semantic similarity
            self.sentence_transformer = SentenceTransformer(self.config.sentence_transformer_model)
            
            # Load intent classification model (could be fine-tuned)
            if self.config.openai_api_key:
                openai.api_key = self.config.openai_api_key
            
            logger.info("Query intent classifier initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing intent classifier: {str(e)}")
            raise
    
    def _build_intent_patterns(self):
        """Build patterns for different query intents"""
        self.intent_patterns = {
            'sales_analysis': [
                'sales', 'revenue', 'income', 'earnings', 'total sales',
                'how much did we sell', 'what are our sales', 'sales performance'
            ],
            'customer_analysis': [
                'customers', 'users', 'buyers', 'clients', 'customer base',
                'how many customers', 'customer behavior', 'customer segments'
            ],
            'product_analysis': [
                'products', 'items', 'bestselling', 'top products', 'popular products',
                'product performance', 'inventory', 'stock'
            ],
            'trend_analysis': [
                'trend', 'trending', 'over time', 'growth', 'decline',
                'increasing', 'decreasing', 'pattern', 'seasonality'
            ],
            'comparison': [
                'compare', 'versus', 'vs', 'compared to', 'difference between',
                'which is better', 'higher', 'lower', 'more than', 'less than'
            ],
            'forecasting': [
                'predict', 'forecast', 'projection', 'future', 'next month',
                'expected', 'anticipated', 'will be', 'estimate'
            ],
            'anomaly_detection': [
                'unusual', 'abnormal', 'outlier', 'strange', 'unexpected',
                'anomaly', 'spike', 'drop', 'suspicious'
            ],
            'segmentation': [
                'segment', 'group', 'category', 'type', 'classification',
                'breakdown', 'split by', 'divided by'
            ]
        }
    
    def classify_intent(self, query: str) -> Tuple[str, float]:
        """Classify the intent of a query"""
        try:
            query_lower = query.lower()
            intent_scores = {}
            
            # Pattern-based matching
            for intent, patterns in self.intent_patterns.items():
                score = 0
                for pattern in patterns:
                    if pattern in query_lower:
                        score += 1
                    else:
                        # Use fuzzy matching
                        similarity = fuzz.partial_ratio(pattern, query_lower) / 100.0
                        if similarity > 0.7:
                            score += similarity
                
                intent_scores[intent] = score / len(patterns)
            
            # Use semantic similarity if available
            if self.sentence_transformer:
                query_embedding = self.sentence_transformer.encode([query])
                
                for intent, patterns in self.intent_patterns.items():
                    pattern_embeddings = self.sentence_transformer.encode(patterns)
                    similarities = cosine_similarity(query_embedding, pattern_embeddings)[0]
                    max_similarity = np.max(similarities)
                    
                    # Combine with pattern score
                    intent_scores[intent] = (intent_scores.get(intent, 0) + max_similarity) / 2
            
            # Get best intent
            if intent_scores:
                best_intent = max(intent_scores, key=intent_scores.get)
                confidence = intent_scores[best_intent]
                
                if confidence >= self.config.confidence_threshold:
                    return best_intent, confidence
            
            return 'general_query', 0.5
            
        except Exception as e:
            logger.error(f"Error classifying intent: {str(e)}")
            return 'general_query', 0.0

class EntityExtractor:
    """Extract entities and concepts from natural language queries"""
    
    def __init__(self, config: NLQueryConfig):
        self.config = config
        self.nlp = None
        self.cal = None
        
    def initialize(self):
        """Initialize NLP models"""
        try:
            # Load spaCy model
            try:
                self.nlp = spacy.load("en_core_web_sm")
            except OSError:
                logger.warning("spaCy model not found, downloading...")
                os.system("python -m spacy download en_core_web_sm")
                self.nlp = spacy.load("en_core_web_sm")
            
            # Initialize calendar parser
            self.cal = parsedatetime.Calendar()
            
            logger.info("Entity extractor initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing entity extractor: {str(e)}")
            raise
    
    def extract_entities(self, query: str) -> Dict[str, List[str]]:
        """Extract entities from query"""
        try:
            entities = {
                'dates': [],
                'numbers': [],
                'products': [],
                'customers': [],
                'locations': [],
                'metrics': [],
                'time_periods': [],
                'comparisons': []
            }
            
            # Use spaCy for NER
            if self.nlp:
                doc = self.nlp(query)
                
                for ent in doc.ents:
                    if ent.label_ in ['DATE', 'TIME']:
                        entities['dates'].append(ent.text)
                    elif ent.label_ in ['MONEY', 'CARDINAL', 'QUANTITY']:
                        entities['numbers'].append(ent.text)
                    elif ent.label_ in ['PERSON', 'ORG']:
                        entities['customers'].append(ent.text)
                    elif ent.label_ in ['GPE', 'LOC']:
                        entities['locations'].append(ent.text)
                    elif ent.label_ in ['PRODUCT']:
                        entities['products'].append(ent.text)
            
            # Extract dates using dateparser
            date_entities = self._extract_dates(query)
            entities['dates'].extend(date_entities)
            
            # Extract metrics
            metric_entities = self._extract_metrics(query)
            entities['metrics'].extend(metric_entities)
            
            # Extract time periods
            time_period_entities = self._extract_time_periods(query)
            entities['time_periods'].extend(time_period_entities)
            
            # Extract comparison operators
            comparison_entities = self._extract_comparisons(query)
            entities['comparisons'].extend(comparison_entities)
            
            # Remove duplicates
            for key in entities:
                entities[key] = list(set(entities[key]))
            
            return entities
            
        except Exception as e:
            logger.error(f"Error extracting entities: {str(e)}")
            return {}
    
    def _extract_dates(self, query: str) -> List[str]:
        """Extract date expressions"""
        try:
            dates = []
            
            # Common date patterns
            date_patterns = [
                r'\b\d{4}-\d{2}-\d{2}\b',  # YYYY-MM-DD
                r'\b\d{1,2}/\d{1,2}/\d{4}\b',  # MM/DD/YYYY
                r'\b(last|past|previous)\s+(week|month|year|quarter)\b',
                r'\b(this|current)\s+(week|month|year|quarter)\b',
                r'\b(next)\s+(week|month|year|quarter)\b',
                r'\b(today|yesterday|tomorrow)\b',
                r'\b\d{1,2}\s+(days?|weeks?|months?|years?)\s+ago\b'
            ]
            
            for pattern in date_patterns:
                matches = re.findall(pattern, query, re.IGNORECASE)
                dates.extend(matches)
            
            # Use dateparser for more complex expressions
            words = query.split()
            for i, word in enumerate(words):
                # Try parsing 1-3 word combinations
                for j in range(1, min(4, len(words) - i + 1)):
                    phrase = ' '.join(words[i:i+j])
                    parsed_date = dateparser.parse(phrase)
                    if parsed_date:
                        dates.append(phrase)
            
            return dates
            
        except Exception as e:
            logger.error(f"Error extracting dates: {str(e)}")
            return []
    
    def _extract_metrics(self, query: str) -> List[str]:
        """Extract metric names"""
        metrics = []
        
        metric_patterns = [
            'revenue', 'sales', 'profit', 'margin', 'income', 'earnings',
            'orders', 'transactions', 'purchases', 'customers', 'users',
            'products', 'items', 'quantity', 'amount', 'count', 'total',
            'average', 'mean', 'median', 'sum', 'min', 'max', 'growth',
            'rate', 'percentage', 'ratio', 'conversion'
        ]
        
        query_lower = query.lower()
        for metric in metric_patterns:
            if metric in query_lower:
                metrics.append(metric)
        
        return metrics
    
    def _extract_time_periods(self, query: str) -> List[str]:
        """Extract time period specifications"""
        time_periods = []
        
        period_patterns = [
            r'\b(daily|weekly|monthly|quarterly|yearly|annual)\b',
            r'\b(day|week|month|quarter|year)\b',
            r'\b(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)[a-z]*\b',
            r'\b(january|february|march|april|may|june|july|august|september|october|november|december)\b',
            r'\b(q1|q2|q3|q4)\b',
            r'\b\d{4}\b'  # Years
        ]
        
        for pattern in period_patterns:
            matches = re.findall(pattern, query, re.IGNORECASE)
            time_periods.extend(matches)
        
        return time_periods
    
    def _extract_comparisons(self, query: str) -> List[str]:
        """Extract comparison operators"""
        comparisons = []
        
        comparison_patterns = [
            r'\b(greater than|more than|higher than|above|over)\b',
            r'\b(less than|lower than|below|under)\b',
            r'\b(equal to|equals|same as)\b',
            r'\b(between|from)\b',
            r'\b(top|bottom|best|worst|highest|lowest)\b',
            r'\b(compare|vs|versus|compared to)\b'
        ]
        
        for pattern in comparison_patterns:
            matches = re.findall(pattern, query, re.IGNORECASE)
            comparisons.extend(matches)
        
        return comparisons

class SQLQueryGenerator:
    """Generate SQL queries from natural language"""
    
    def __init__(self, config: NLQueryConfig, schema_mapper: DatabaseSchemaMapper):
        self.config = config
        self.schema_mapper = schema_mapper
        self.query_templates = {}
        self._build_query_templates()
        
    def _build_query_templates(self):
        """Build SQL query templates for different intents"""
        self.query_templates = {
            'sales_analysis': {
                'total_sales': """
                    SELECT SUM(total_amount) as total_sales
                    FROM orders 
                    WHERE order_date >= '{start_date}' AND order_date <= '{end_date}'
                    AND order_status = 'completed'
                """,
                'sales_by_period': """
                    SELECT DATE_TRUNC('{period}', order_date) as period,
                           SUM(total_amount) as sales
                    FROM orders 
                    WHERE order_date >= '{start_date}' AND order_date <= '{end_date}'
                    AND order_status = 'completed'
                    GROUP BY DATE_TRUNC('{period}', order_date)
                    ORDER BY period
                """,
                'sales_by_product': """
                    SELECT p.name as product_name,
                           SUM(oi.quantity * oi.unit_price) as sales
                    FROM order_items oi
                    JOIN products p ON oi.product_id = p.product_id
                    JOIN orders o ON oi.order_id = o.order_id
                    WHERE o.order_date >= '{start_date}' AND o.order_date <= '{end_date}'
                    AND o.order_status = 'completed'
                    GROUP BY p.product_id, p.name
                    ORDER BY sales DESC
                    LIMIT {limit}
                """
            },
            'customer_analysis': {
                'customer_count': """
                    SELECT COUNT(DISTINCT customer_id) as customer_count
                    FROM orders
                    WHERE order_date >= '{start_date}' AND order_date <= '{end_date}'
                """,
                'top_customers': """
                    SELECT c.customer_id, c.email, c.first_name, c.last_name,
                           COUNT(o.order_id) as order_count,
                           SUM(o.total_amount) as total_spent
                    FROM customers c
                    JOIN orders o ON c.customer_id = o.customer_id
                    WHERE o.order_date >= '{start_date}' AND o.order_date <= '{end_date}'
                    AND o.order_status = 'completed'
                    GROUP BY c.customer_id, c.email, c.first_name, c.last_name
                    ORDER BY total_spent DESC
                    LIMIT {limit}
                """
            },
            'product_analysis': {
                'top_products': """
                    SELECT p.product_id, p.name, p.category_id,
                           SUM(oi.quantity) as units_sold,
                           SUM(oi.quantity * oi.unit_price) as revenue
                    FROM products p
                    JOIN order_items oi ON p.product_id = oi.product_id
                    JOIN orders o ON oi.order_id = o.order_id
                    WHERE o.order_date >= '{start_date}' AND o.order_date <= '{end_date}'
                    AND o.order_status = 'completed'
                    GROUP BY p.product_id, p.name, p.category_id
                    ORDER BY {order_by} DESC
                    LIMIT {limit}
                """,
                'product_performance': """
                    SELECT p.name as product_name,
                           COUNT(oi.order_item_id) as times_ordered,
                           SUM(oi.quantity) as total_quantity,
                           AVG(oi.unit_price) as avg_price,
                           SUM(oi.quantity * oi.unit_price) as total_revenue
                    FROM products p
                    JOIN order_items oi ON p.product_id = oi.product_id
                    JOIN orders o ON oi.order_id = o.order_id
                    WHERE o.order_date >= '{start_date}' AND o.order_date <= '{end_date}'
                    AND o.order_status = 'completed'
                    GROUP BY p.product_id, p.name
                    ORDER BY total_revenue DESC
                """
            }
        }
    
    def generate_sql(self, intent: str, entities: Dict, query: str) -> Tuple[str, Dict]:
        """Generate SQL query based on intent and entities"""
        try:
            # Extract date range
            start_date, end_date = self._extract_date_range(entities, query)
            
            # Extract limit
            limit = self._extract_limit(entities, query)
            
            # Extract grouping period
            period = self._extract_period(entities, query)
            
            # Determine specific query type
            query_type = self._determine_query_type(intent, entities, query)
            
            # Get template
            if intent in self.query_templates and query_type in self.query_templates[intent]:
                template = self.query_templates[intent][query_type]
                
                # Fill template parameters
                sql_params = {
                    'start_date': start_date,
                    'end_date': end_date,
                    'limit': limit,
                    'period': period
                }
                
                # Add specific parameters based on query type
                if query_type == 'top_products':
                    if 'revenue' in query.lower() or 'sales' in query.lower():
                        sql_params['order_by'] = 'revenue'
                    else:
                        sql_params['order_by'] = 'units_sold'
                
                # Format SQL
                sql_query = template.format(**sql_params)
                
                # Validate and clean SQL
                sql_query = self._validate_and_clean_sql(sql_query)
                
                return sql_query, sql_params
            else:
                # Generate custom SQL
                return self._generate_custom_sql(intent, entities, query)
                
        except Exception as e:
            logger.error(f"Error generating SQL: {str(e)}")
            return "", {}
    
    def _extract_date_range(self, entities: Dict, query: str) -> Tuple[str, str]:
        """Extract date range from entities and query"""
        try:
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')  # Default: last 30 days
            
            # Check for specific date mentions
            if entities.get('dates'):
                for date_expr in entities['dates']:
                    parsed_date = dateparser.parse(date_expr)
                    if parsed_date:
                        if 'last' in date_expr.lower() or 'past' in date_expr.lower():
                            start_date = parsed_date.strftime('%Y-%m-%d')
                        elif 'until' in date_expr.lower() or 'through' in date_expr.lower():
                            end_date = parsed_date.strftime('%Y-%m-%d')
            
            # Check for time period mentions
            if entities.get('time_periods'):
                for period in entities['time_periods']:
                    if 'month' in period.lower():
                        if 'last' in query.lower() or 'past' in query.lower():
                            start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
                        elif 'this' in query.lower():
                            start_date = datetime.now().replace(day=1).strftime('%Y-%m-%d')
                    elif 'year' in period.lower():
                        if 'last' in query.lower() or 'past' in query.lower():
                            start_date = (datetime.now() - timedelta(days=365)).strftime('%Y-%m-%d')
                        elif 'this' in query.lower():
                            start_date = datetime.now().replace(month=1, day=1).strftime('%Y-%m-%d')
                    elif 'week' in period.lower():
                        if 'last' in query.lower() or 'past' in query.lower():
                            start_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
            
            return start_date, end_date
            
        except Exception as e:
            logger.error(f"Error extracting date range: {str(e)}")
            # Return default range
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
            return start_date, end_date
    
    def _extract_limit(self, entities: Dict, query: str) -> int:
        """Extract result limit from query"""
        default_limit = 10
        
        # Look for explicit numbers
        if entities.get('numbers'):
            for number_str in entities['numbers']:
                try:
                    number = int(re.sub(r'[^0-9]', '', number_str))
                    if 1 <= number <= 1000:  # Reasonable range
                        return number
                except:
                    continue
        
        # Look for common limit expressions
        if 'top' in query.lower():
            # Extract number after "top"
            top_match = re.search(r'top\s+(\d+)', query.lower())
            if top_match:
                return int(top_match.group(1))
            else:
                return 10  # Default for "top" queries
        
        if 'best' in query.lower() or 'worst' in query.lower():
            return 10
        
        return default_limit
    
    def _extract_period(self, entities: Dict, query: str) -> str:
        """Extract grouping period from query"""
        default_period = 'day'
        
        if entities.get('time_periods'):
            for period in entities['time_periods']:
                if 'day' in period.lower() or 'daily' in period.lower():
                    return 'day'
                elif 'week' in period.lower() or 'weekly' in period.lower():
                    return 'week'
                elif 'month' in period.lower() or 'monthly' in period.lower():
                    return 'month'
                elif 'quarter' in period.lower() or 'quarterly' in period.lower():
                    return 'quarter'
                elif 'year' in period.lower() or 'yearly' in period.lower() or 'annual' in period.lower():
                    return 'year'
        
        # Check query for period keywords
        if 'monthly' in query.lower() or 'by month' in query.lower():
            return 'month'
        elif 'weekly' in query.lower() or 'by week' in query.lower():
            return 'week'
        elif 'yearly' in query.lower() or 'by year' in query.lower():
            return 'year'
        
        return default_period
    
    def _determine_query_type(self, intent: str, entities: Dict, query: str) -> str:
        """Determine specific query type within an intent"""
        query_lower = query.lower()
        
        if intent == 'sales_analysis':
            if 'total' in query_lower and ('sales' in query_lower or 'revenue' in query_lower):
                return 'total_sales'
            elif 'by' in query_lower and any(period in query_lower for period in ['day', 'week', 'month', 'year']):
                return 'sales_by_period'
            elif 'product' in query_lower:
                return 'sales_by_product'
            else:
                return 'total_sales'  # Default
        
        elif intent == 'customer_analysis':
            if 'how many' in query_lower or 'count' in query_lower:
                return 'customer_count'
            elif 'top' in query_lower or 'best' in query_lower:
                return 'top_customers'
            else:
                return 'customer_count'  # Default
        
        elif intent == 'product_analysis':
            if 'top' in query_lower or 'best' in query_lower or 'popular' in query_lower:
                return 'top_products'
            else:
                return 'product_performance'  # Default
        
        return 'general'
    
    def _generate_custom_sql(self, intent: str, entities: Dict, query: str) -> Tuple[str, Dict]:
        """Generate custom SQL for complex queries"""
        try:
            # This is a simplified custom SQL generator
            # In a production system, this would be much more sophisticated
            
            # Basic SELECT statement
            select_clause = "SELECT "
            from_clause = "FROM "
            where_clause = "WHERE 1=1 "
            group_by_clause = ""
            order_by_clause = ""
            limit_clause = ""
            
            # Determine main table based on entities
            main_table = "orders"  # Default
            if entities.get('products') or 'product' in query.lower():
                main_table = "products"
            elif entities.get('customers') or 'customer' in query.lower():
                main_table = "customers"
            
            # Build basic query structure
            if intent == 'sales_analysis':
                select_clause += "SUM(total_amount) as total_sales"
                from_clause += "orders o"
                where_clause += "AND o.order_status = 'completed'"
            elif intent == 'customer_analysis':
                select_clause += "COUNT(DISTINCT customer_id) as customer_count"
                from_clause += "orders o"
            elif intent == 'product_analysis':
                select_clause += "p.name, SUM(oi.quantity) as units_sold"
                from_clause += "products p JOIN order_items oi ON p.product_id = oi.product_id"
                group_by_clause = "GROUP BY p.product_id, p.name"
            
            # Add date filter
            start_date, end_date = self._extract_date_range(entities, query)
            where_clause += f" AND order_date >= '{start_date}' AND order_date <= '{end_date}'"
            
            # Add limit
            limit = self._extract_limit(entities, query)
            limit_clause = f"LIMIT {limit}"
            
            # Combine clauses
            sql_query = f"{select_clause} {from_clause} {where_clause} {group_by_clause} {order_by_clause} {limit_clause}"
            
            sql_params = {
                'start_date': start_date,
                'end_date': end_date,
                'limit': limit
            }
            
            return sql_query.strip(), sql_params
            
        except Exception as e:
            logger.error(f"Error generating custom SQL: {str(e)}")
            return "", {}
    
    def _validate_and_clean_sql(self, sql_query: str) -> str:
        """Validate and clean SQL query"""
        try:
            # Parse SQL to check for syntax errors
            parsed = sqlparse.parse(sql_query)
            
            if not parsed:
                raise ValueError("Invalid SQL syntax")
            
            # Format SQL nicely
            formatted_sql = sqlparse.format(sql_query, reindent=True, keyword_case='upper')
            
            return formatted_sql
            
        except Exception as e:
            logger.error(f"Error validating SQL: {str(e)}")
            return sql_query  # Return original if validation fails

class QueryResultProcessor:
    """Process and format query results"""
    
    def __init__(self, config: NLQueryConfig):
        self.config = config
        
    def process_results(self, results: pd.DataFrame, intent: str, query: str) -> Dict[str, Any]:
        """Process query results and generate insights"""
        try:
            processed_results = {
                'data': results.to_dict('records'),
                'summary': self._generate_summary(results, intent),
                'insights': self._generate_insights(results, intent),
                'visualizations': self._generate_visualizations(results, intent),
                'metadata': {
                    'total_rows': len(results),
                    'columns': list(results.columns),
                    'intent': intent,
                    'query': query
                }
            }
            
            return processed_results
            
        except Exception as e:
            logger.error(f"Error processing results: {str(e)}")
            return {'error': str(e)}
    
    def _generate_summary(self, results: pd.DataFrame, intent: str) -> Dict[str, Any]:
        """Generate summary statistics"""
        try:
            summary = {}
            
            if len(results) == 0:
                return {'message': 'No data found for the specified criteria'}
            
            # Identify numeric columns
            numeric_columns = results.select_dtypes(include=[np.number]).columns.tolist()
            
            if numeric_columns:
                for col in numeric_columns:
                    summary[col] = {
                        'total': float(results[col].sum()),
                        'average': float(results[col].mean()),
                        'min': float(results[col].min()),
                        'max': float(results[col].max()),
                        'count': int(results[col].count())
                    }
            
            # Add categorical summaries
            categorical_columns = results.select_dtypes(include=['object']).columns.tolist()
            for col in categorical_columns[:3]:  # Limit to first 3 categorical columns
                if len(results[col].unique()) <= 20:  # Only for columns with reasonable cardinality
                    summary[f'{col}_distribution'] = results[col].value_counts().to_dict()
            
            return summary
            
        except Exception as e:
            logger.error(f"Error generating summary: {str(e)}")
            return {}
    
    def _generate_insights(self, results: pd.DataFrame, intent: str) -> List[str]:
        """Generate automated insights from results"""
        try:
            insights = []
            
            if len(results) == 0:
                insights.append("No data available for the specified time period or criteria.")
                return insights
            
            # Sales analysis insights
            if intent == 'sales_analysis':
                if 'total_sales' in results.columns:
                    total_sales = results['total_sales'].sum()
                    insights.append(f"Total sales amount to ${total_sales:,.2f}")
                
                if 'sales' in results.columns and len(results) > 1:
                    # Growth analysis
                    sales_values = results['sales'].values
                    if len(sales_values) >= 2:
                        recent_sales = sales_values[-1]
                        previous_sales = sales_values[-2]
                        if previous_sales > 0:
                            growth_rate = ((recent_sales - previous_sales) / previous_sales) * 100
                            if growth_rate > 0:
                                insights.append(f"Sales increased by {growth_rate:.1f}% in the latest period")
                            else:
                                insights.append(f"Sales decreased by {abs(growth_rate):.1f}% in the latest period")
            
            # Product analysis insights
            elif intent == 'product_analysis':
                if 'units_sold' in results.columns:
                    top_product = results.loc[results['units_sold'].idxmax()]
                    insights.append(f"The top-selling product is '{top_product.get('name', 'N/A')}' with {top_product['units_sold']} units sold")
                
                if 'revenue' in results.columns:
                    total_revenue = results['revenue'].sum()
                    insights.append(f"These products generated a total revenue of ${total_revenue:,.2f}")
            
            # Customer analysis insights
            elif intent == 'customer_analysis':
                if 'customer_count' in results.columns:
                    customer_count = results['customer_count'].iloc[0]
                    insights.append(f"There are {customer_count} unique customers in the specified period")
                
                if 'total_spent' in results.columns:
                    avg_spend = results['total_spent'].mean()
                    insights.append(f"Average customer spending is ${avg_spend:.2f}")
            
            # General insights
            numeric_columns = results.select_dtypes(include=[np.number]).columns.tolist()
            if numeric_columns:
                for col in numeric_columns[:2]:  # Limit to 2 columns
                    col_std = results[col].std()
                    col_mean = results[col].mean()
                    if col_mean > 0 and col_std / col_mean > 0.5:  # High coefficient of variation
                        insights.append(f"There's high variability in {col.replace('_', ' ')}")
            
            return insights
            
        except Exception as e:
            logger.error(f"Error generating insights: {str(e)}")
            return ["Unable to generate insights from the data"]
    
    def _generate_visualizations(self, results: pd.DataFrame, intent: str) -> List[Dict]:
        """Generate visualization recommendations"""
        try:
            visualizations = []
            
            if len(results) == 0:
                return visualizations
            
            # Identify column types
            numeric_columns = results.select_dtypes(include=[np.number]).columns.tolist()
            categorical_columns = results.select_dtypes(include=['object']).columns.tolist()
            date_columns = results.select_dtypes(include=['datetime64']).columns.tolist()
            
            # Time series visualization
            if date_columns and numeric_columns:
                for date_col in date_columns[:1]:  # Use first date column
                    for num_col in numeric_columns[:2]:  # Use first 2 numeric columns
                        visualizations.append({
                            'type': 'line_chart',
                            'title': f'{num_col.replace("_", " ").title()} Over Time',
                            'x_axis': date_col,
                            'y_axis': num_col,
                            'description': f'Trend of {num_col} over {date_col}'
                        })
            
            # Bar chart for categorical vs numeric
            if categorical_columns and numeric_columns:
                for cat_col in categorical_columns[:1]:
                    for num_col in numeric_columns[:1]:
                        if len(results[cat_col].unique()) <= 20:  # Reasonable number of categories
                            visualizations.append({
                                'type': 'bar_chart',
                                'title': f'{num_col.replace("_", " ").title()} by {cat_col.replace("_", " ").title()}',
                                'x_axis': cat_col,
                                'y_axis': num_col,
                                'description': f'Comparison of {num_col} across different {cat_col}'
                            })
            
            # Pie chart for categorical distributions
            if categorical_columns:
                for cat_col in categorical_columns[:1]:
                    if len(results[cat_col].unique()) <= 10:  # Good for pie charts
                        visualizations.append({
                            'type': 'pie_chart',
                            'title': f'Distribution of {cat_col.replace("_", " ").title()}',
                            'values': cat_col,
                            'description': f'Breakdown by {cat_col}'
                        })
            
            # Histogram for numeric distributions
            if numeric_columns:
                for num_col in numeric_columns[:1]:
                    visualizations.append({
                        'type': 'histogram',
                        'title': f'Distribution of {num_col.replace("_", " ").title()}',
                        'x_axis': num_col,
                        'description': f'Frequency distribution of {num_col}'
                    })
            
            return visualizations
            
        except Exception as e:
            logger.error(f"Error generating visualizations: {str(e)}")
            return []

class NaturalLanguageQueryEngine:
    """Main natural language query engine"""
    
    def __init__(self, config: NLQueryConfig):
        self.config = config
        self.schema_mapper = DatabaseSchemaMapper(config)
        self.intent_classifier = QueryIntentClassifier(config)
        self.entity_extractor = EntityExtractor(config)
        self.sql_generator = SQLQueryGenerator(config, self.schema_mapper)
        self.result_processor = QueryResultProcessor(config)
        
        # Query cache
        self.query_cache = {}
        
        # MLflow setup
        mlflow.set_tracking_uri(self.config.mlflow_tracking_uri)
        mlflow.set_experiment(self.config.experiment_name)
    
    async def initialize(self):
        """Initialize all components"""
        try:
            self.schema_mapper.initialize()
            self.intent_classifier.initialize()
            self.entity_extractor.initialize()
            
            logger.info("Natural language query engine initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing NL query engine: {str(e)}")
            raise
    
    async def process_query(self, query: str, user_id: str = None) -> Dict[str, Any]:
        """Process a natural language query"""
        try:
            start_time = datetime.now()
            
            # Check cache first
            if self.config.enable_query_cache:
                cached_result = self._get_cached_result(query)
                if cached_result:
                    logger.info(f"Returning cached result for query: {query[:50]}...")
                    return cached_result
            
            # Step 1: Classify intent
            intent, intent_confidence = self.intent_classifier.classify_intent(query)
            
            # Step 2: Extract entities
            entities = self.entity_extractor.extract_entities(query)
            
            # Step 3: Generate SQL
            sql_query, sql_params = self.sql_generator.generate_sql(intent, entities, query)
            
            if not sql_query:
                return {
                    'error': 'Unable to generate SQL query from natural language',
                    'query': query,
                    'intent': intent,
                    'entities': entities
                }
            
            # Step 4: Execute SQL
            results_df = await self._execute_sql(sql_query)
            
            # Step 5: Process results
            processed_results = self.result_processor.process_results(results_df, intent, query)
            
            # Add query metadata
            processed_results['query_metadata'] = {
                'original_query': query,
                'intent': intent,
                'intent_confidence': intent_confidence,
                'entities': entities,
                'sql_query': sql_query,
                'sql_params': sql_params,
                'processing_time_seconds': (datetime.now() - start_time).total_seconds()
            }
            
            # Cache result
            if self.config.enable_query_cache:
                self._cache_result(query, processed_results)
            
            # Log metrics
            with mlflow.start_run(run_name=f"nl_query_{int(start_time.timestamp())}"):
                mlflow.log_param("query", query[:100])  # First 100 chars
                mlflow.log_param("intent", intent)
                mlflow.log_metric("intent_confidence", intent_confidence)
                mlflow.log_metric("processing_time", processed_results['query_metadata']['processing_time_seconds'])
                mlflow.log_metric("result_count", len(processed_results.get('data', [])))
                
                if user_id:
                    mlflow.log_param("user_id", user_id)
            
            logger.info(f"Processed NL query successfully: {query[:50]}...")
            return processed_results
            
        except Exception as e:
            logger.error(f"Error processing query: {str(e)}")
            return {
                'error': str(e),
                'query': query,
                'processing_time_seconds': (datetime.now() - start_time).total_seconds()
            }
    
    async def _execute_sql(self, sql_query: str) -> pd.DataFrame:
        """Execute SQL query and return results"""
        try:
            with self.schema_mapper.engine.connect() as conn:
                result = conn.execute(text(sql_query))
                df = pd.DataFrame(result.fetchall(), columns=result.keys())
                
                # Limit results
                if len(df) > self.config.max_results:
                    df = df.head(self.config.max_results)
                    logger.warning(f"Results limited to {self.config.max_results} rows")
                
                return df
                
        except Exception as e:
            logger.error(f"Error executing SQL: {str(e)}")
            logger.error(f"SQL Query: {sql_query}")
            raise
    
    def _get_cached_result(self, query: str) -> Optional[Dict]:
        """Get cached result for query"""
        try:
            query_hash = hash(query.lower().strip())
            
            if query_hash in self.query_cache:
                cached_item = self.query_cache[query_hash]
                
                # Check if cache is still valid
                cache_time = cached_item['cached_at']
                expiry_time = cache_time + timedelta(hours=self.config.cache_expiry_hours)
                
                if datetime.now() < expiry_time:
                    return cached_item['result']
                else:
                    # Remove expired cache
                    del self.query_cache[query_hash]
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting cached result: {str(e)}")
            return None
    
    def _cache_result(self, query: str, result: Dict):
        """Cache query result"""
        try:
            query_hash = hash(query.lower().strip())
            
            self.query_cache[query_hash] = {
                'result': result,
                'cached_at': datetime.now()
            }
            
            # Limit cache size
            if len(self.query_cache) > 1000:
                # Remove oldest entries
                sorted_cache = sorted(
                    self.query_cache.items(),
                    key=lambda x: x[1]['cached_at']
                )
                
                # Keep newest 800 entries
                self.query_cache = dict(sorted_cache[-800:])
            
        except Exception as e:
            logger.error(f"Error caching result: {str(e)}")
    
    def get_suggested_queries(self) -> List[str]:
        """Get suggested example queries"""
        return [
            "What were our total sales last month?",
            "Show me the top 10 products by revenue",
            "How many customers did we have this quarter?",
            "What's the trend in our monthly sales this year?",
            "Which products are selling the most units?",
            "Who are our top 5 customers by total spending?",
            "What's our average order value over time?",
            "Compare sales between this month and last month",
            "Show me sales by product category",
            "What are the most popular products in electronics?"
        ]

async def main():
    """Main execution function"""
    config = NLQueryConfig()
    
    # Initialize query engine
    engine = NaturalLanguageQueryEngine(config)
    await engine.initialize()
    
    # Test queries
    test_queries = [
        "What were our total sales last month?",
        "Show me the top 10 products by revenue",
        "How many customers did we have this quarter?"
    ]
    
    for query in test_queries:
        print(f"\nProcessing query: {query}")
        result = await engine.process_query(query)
        
        if 'error' in result:
            print(f"Error: {result['error']}")
        else:
            print(f"Intent: {result['query_metadata']['intent']}")
            print(f"SQL: {result['query_metadata']['sql_query']}")
            print(f"Results: {len(result.get('data', []))} rows")
            if result.get('insights'):
                print(f"Insights: {result['insights']}")
    
    logger.info("Natural language query engine test completed")

if __name__ == "__main__":
    asyncio.run(main())