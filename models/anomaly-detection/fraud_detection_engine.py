#!/usr/bin/env python3
"""
Advanced Fraud Detection Engine for E-commerce Analytics
Multi-layered anomaly detection using statistical methods, machine learning, and deep learning
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import os
import sys
import json
import warnings
from typing import Dict, List, Tuple, Optional, Any, Union
from dataclasses import dataclass
from collections import defaultdict, deque
import asyncio
import aiohttp

# Statistical and ML libraries
from scipy import stats
from scipy.spatial.distance import mahalanobis
from sklearn.ensemble import IsolationForest, RandomForestClassifier
from sklearn.svm import OneClassSVM
from sklearn.preprocessing import StandardScaler, RobustScaler, LabelEncoder
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score
from sklearn.cluster import DBSCAN, KMeans
import xgboost as xgb
import lightgbm as lgb

# Deep learning for advanced anomaly detection
import tensorflow as tf
from tensorflow.keras.models import Model, Sequential
from tensorflow.keras.layers import Input, Dense, LSTM, Dropout, BatchNormalization
from tensorflow.keras.layers import Conv1D, GlobalMaxPooling1D, Attention
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.losses import MeanSquaredError
from tensorflow.keras.callbacks import EarlyStopping

# Time series anomaly detection
from pyod.models.knn import KNN
from pyod.models.lof import LOF
from pyod.models.ocsvm import OCSVM
from pyod.models.auto_encoder import AutoEncoder
import stumpy

# Graph analysis for network anomalies
import networkx as nx
from stellargraph import StellarGraph
from stellargraph.mapper import GraphSAINTRandomWalkNodeGenerator
from stellargraph.layer import GraphSAINT

# Real-time processing
import redis
from kafka import KafkaProducer, KafkaConsumer
import asyncpg

# Visualization
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots

# MLflow integration
import mlflow
import mlflow.sklearn
import mlflow.tensorflow
from mlflow.tracking import MlflowClient

# Database and cloud
import boto3
from sqlalchemy import create_engine, text

warnings.filterwarnings('ignore')

# Configuration
@dataclass
class FraudDetectionConfig:
    # Model parameters
    contamination_rate: float = 0.01  # Expected fraud rate
    n_estimators: int = 100
    random_state: int = 42
    
    # Anomaly detection algorithms
    use_isolation_forest: bool = True
    use_local_outlier_factor: bool = True
    use_one_class_svm: bool = True
    use_autoencoder: bool = True
    use_statistical_methods: bool = True
    
    # Time series parameters
    time_window_minutes: int = 60
    seasonal_decomposition: bool = True
    trend_analysis_days: int = 30
    
    # Graph analysis parameters
    enable_network_analysis: bool = True
    min_network_connections: int = 5
    community_detection: bool = True
    
    # Feature engineering
    velocity_features: bool = True
    behavioral_features: bool = True
    temporal_features: bool = True
    geolocation_features: bool = True
    
    # Scoring thresholds
    high_risk_threshold: float = 0.8
    medium_risk_threshold: float = 0.5
    low_risk_threshold: float = 0.2
    
    # Real-time parameters
    enable_real_time_scoring: bool = True
    batch_processing_interval_minutes: int = 5
    model_update_frequency_hours: int = 24
    
    # Business rules
    max_transaction_amount_multiplier: float = 10.0  # Compared to user average
    max_transactions_per_minute: int = 10
    suspicious_location_threshold_km: float = 1000  # Distance from usual location
    
    # MLflow configuration
    mlflow_tracking_uri: str = os.getenv('MLFLOW_TRACKING_URI', 'http://mlflow-server.mlflow:5000')
    experiment_name: str = 'fraud-detection'
    
    # Database connections
    db_host: str = os.getenv('DB_HOST', 'analytics-db.cluster-xyz.us-east-1.rds.amazonaws.com')
    db_name: str = os.getenv('DB_NAME', 'ecommerce_analytics')
    db_user: str = os.getenv('DB_USER', 'analytics_user')
    db_password: str = os.getenv('DB_PASSWORD', 'analytics_password')
    
    # Redis configuration
    redis_host: str = os.getenv('REDIS_HOST', 'redis-cluster')
    redis_port: int = int(os.getenv('REDIS_PORT', '6379'))
    redis_db: int = int(os.getenv('REDIS_DB', '1'))
    
    # Kafka configuration
    kafka_bootstrap_servers: str = os.getenv('KAFKA_BOOTSTRAP_SERVERS', 'kafka-cluster:9092')
    kafka_topic_transactions: str = 'transactions'
    kafka_topic_alerts: str = 'fraud-alerts'

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class FeatureEngineer:
    """Advanced feature engineering for fraud detection"""
    
    def __init__(self, config: FraudDetectionConfig):
        self.config = config
        self.scaler = RobustScaler()
        self.label_encoders = {}
        
    def extract_transaction_features(self, transactions_df: pd.DataFrame) -> pd.DataFrame:
        """Extract comprehensive features from transaction data"""
        try:
            features_df = transactions_df.copy()
            
            # Basic transaction features
            features_df['transaction_hour'] = pd.to_datetime(features_df['transaction_time']).dt.hour
            features_df['transaction_day_of_week'] = pd.to_datetime(features_df['transaction_time']).dt.dayofweek
            features_df['transaction_month'] = pd.to_datetime(features_df['transaction_time']).dt.month
            features_df['is_weekend'] = features_df['transaction_day_of_week'].isin([5, 6]).astype(int)
            features_df['is_night_time'] = features_df['transaction_hour'].isin(range(22, 24) or range(0, 6)).astype(int)
            
            # Amount-based features
            features_df['amount_log'] = np.log1p(features_df['amount'])
            features_df['amount_zscore'] = stats.zscore(features_df['amount'])
            
            # Velocity features (if enabled)
            if self.config.velocity_features:
                features_df = self._add_velocity_features(features_df)
            
            # Behavioral features (if enabled)
            if self.config.behavioral_features:
                features_df = self._add_behavioral_features(features_df)
            
            # Temporal features (if enabled)
            if self.config.temporal_features:
                features_df = self._add_temporal_features(features_df)
            
            # Geolocation features (if enabled)
            if self.config.geolocation_features:
                features_df = self._add_geolocation_features(features_df)
            
            logger.info(f"Extracted features shape: {features_df.shape}")
            return features_df
            
        except Exception as e:
            logger.error(f"Error extracting transaction features: {str(e)}")
            raise
    
    def _add_velocity_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add velocity-based features"""
        try:
            # Sort by customer and time
            df_sorted = df.sort_values(['customer_id', 'transaction_time'])
            
            # Calculate time differences between transactions
            df_sorted['time_since_last_transaction'] = df_sorted.groupby('customer_id')['transaction_time'].diff()
            df_sorted['time_since_last_transaction_minutes'] = df_sorted['time_since_last_transaction'].dt.total_seconds() / 60
            
            # Transaction frequency features
            df_sorted['transactions_last_hour'] = df_sorted.groupby('customer_id').rolling(
                window='1H', on='transaction_time', min_periods=1
            )['transaction_id'].count().reset_index(level=0, drop=True)
            
            df_sorted['transactions_last_day'] = df_sorted.groupby('customer_id').rolling(
                window='1D', on='transaction_time', min_periods=1
            )['transaction_id'].count().reset_index(level=0, drop=True)
            
            # Amount velocity features
            df_sorted['amount_last_hour'] = df_sorted.groupby('customer_id').rolling(
                window='1H', on='transaction_time', min_periods=1
            )['amount'].sum().reset_index(level=0, drop=True)
            
            df_sorted['avg_amount_last_24h'] = df_sorted.groupby('customer_id').rolling(
                window='1D', on='transaction_time', min_periods=1
            )['amount'].mean().reset_index(level=0, drop=True)
            
            return df_sorted
            
        except Exception as e:
            logger.error(f"Error adding velocity features: {str(e)}")
            return df
    
    def _add_behavioral_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add behavioral pattern features"""
        try:
            # Customer historical patterns
            customer_stats = df.groupby('customer_id').agg({
                'amount': ['mean', 'std', 'min', 'max'],
                'transaction_hour': ['mean', 'std'],
                'merchant_id': 'nunique',
                'payment_method': lambda x: x.mode().iloc[0] if len(x.mode()) > 0 else 'unknown'
            }).reset_index()
            
            # Flatten column names
            customer_stats.columns = ['customer_id'] + [
                f'customer_{col[0]}_{col[1]}' if col[1] != '' else f'customer_{col[0]}'
                for col in customer_stats.columns[1:]
            ]
            
            # Merge back to main dataframe
            df = df.merge(customer_stats, on='customer_id', how='left')
            
            # Deviation from personal patterns
            df['amount_deviation_from_personal_mean'] = abs(
                df['amount'] - df['customer_amount_mean']
            ) / (df['customer_amount_std'] + 1e-8)
            
            df['hour_deviation_from_personal_mean'] = abs(
                df['transaction_hour'] - df['customer_transaction_hour_mean']
            ) / (df['customer_transaction_hour_std'] + 1e-8)
            
            # Merchant and payment method novelty
            df['is_new_merchant'] = ~df['merchant_id'].isin(
                df.groupby('customer_id')['merchant_id'].transform(
                    lambda x: x.shift(1).dropna().unique()
                )
            ).astype(int)
            
            df['is_new_payment_method'] = (
                df['payment_method'] != df['customer_payment_method_mode']
            ).astype(int)
            
            return df
            
        except Exception as e:
            logger.error(f"Error adding behavioral features: {str(e)}")
            return df
    
    def _add_temporal_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add temporal pattern features"""
        try:
            df['transaction_time'] = pd.to_datetime(df['transaction_time'])
            
            # Cyclical encoding for time features
            df['hour_sin'] = np.sin(2 * np.pi * df['transaction_hour'] / 24)
            df['hour_cos'] = np.cos(2 * np.pi * df['transaction_hour'] / 24)
            df['day_sin'] = np.sin(2 * np.pi * df['transaction_day_of_week'] / 7)
            df['day_cos'] = np.cos(2 * np.pi * df['transaction_day_of_week'] / 7)
            df['month_sin'] = np.sin(2 * np.pi * df['transaction_month'] / 12)
            df['month_cos'] = np.cos(2 * np.pi * df['transaction_month'] / 12)
            
            # Time since account creation
            if 'account_created_date' in df.columns:
                df['days_since_account_creation'] = (
                    df['transaction_time'] - pd.to_datetime(df['account_created_date'])
                ).dt.days
                
                df['is_new_account'] = (df['days_since_account_creation'] <= 30).astype(int)
            
            # Holiday and special event indicators
            df['is_holiday_season'] = df['transaction_month'].isin([11, 12]).astype(int)
            df['is_payday'] = df['transaction_time'].dt.day.isin([1, 15]).astype(int)
            
            return df
            
        except Exception as e:
            logger.error(f"Error adding temporal features: {str(e)}")
            return df
    
    def _add_geolocation_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add geolocation-based features"""
        try:
            if 'latitude' not in df.columns or 'longitude' not in df.columns:
                logger.warning("Geolocation data not available")
                return df
            
            # Calculate distance from home location
            customer_home_locations = df.groupby('customer_id').agg({
                'latitude': 'mean',  # Approximate home location
                'longitude': 'mean'
            }).reset_index()
            customer_home_locations.columns = ['customer_id', 'home_lat', 'home_lon']
            
            df = df.merge(customer_home_locations, on='customer_id', how='left')
            
            # Haversine distance calculation
            df['distance_from_home_km'] = self._calculate_haversine_distance(
                df['latitude'], df['longitude'],
                df['home_lat'], df['home_lon']
            )
            
            # Location-based risk features
            df['is_far_from_home'] = (
                df['distance_from_home_km'] > self.config.suspicious_location_threshold_km
            ).astype(int)
            
            # Velocity of location change
            df_sorted = df.sort_values(['customer_id', 'transaction_time'])
            df_sorted['prev_lat'] = df_sorted.groupby('customer_id')['latitude'].shift(1)
            df_sorted['prev_lon'] = df_sorted.groupby('customer_id')['longitude'].shift(1)
            df_sorted['prev_time'] = df_sorted.groupby('customer_id')['transaction_time'].shift(1)
            
            df_sorted['distance_from_prev_km'] = self._calculate_haversine_distance(
                df_sorted['latitude'], df_sorted['longitude'],
                df_sorted['prev_lat'], df_sorted['prev_lon']
            )
            
            df_sorted['time_diff_hours'] = (
                df_sorted['transaction_time'] - df_sorted['prev_time']
            ).dt.total_seconds() / 3600
            
            df_sorted['location_velocity_kmh'] = (
                df_sorted['distance_from_prev_km'] / (df_sorted['time_diff_hours'] + 1e-8)
            )
            
            # Impossible travel speed (e.g., >500 km/h by land)
            df_sorted['impossible_travel'] = (df_sorted['location_velocity_kmh'] > 500).astype(int)
            
            return df_sorted
            
        except Exception as e:
            logger.error(f"Error adding geolocation features: {str(e)}")
            return df
    
    def _calculate_haversine_distance(self, lat1, lon1, lat2, lon2):
        """Calculate the great circle distance between two points on earth"""
        # Convert latitude and longitude from degrees to radians
        lat1, lon1, lat2, lon2 = map(np.radians, [lat1, lon1, lat2, lon2])
        
        # Haversine formula
        dlat = lat2 - lat1
        dlon = lon2 - lon1
        a = np.sin(dlat/2)**2 + np.cos(lat1) * np.cos(lat2) * np.sin(dlon/2)**2
        c = 2 * np.arcsin(np.sqrt(a))
        
        # Radius of earth in kilometers
        r = 6371
        
        return c * r

class StatisticalAnomalyDetector:
    """Statistical methods for anomaly detection"""
    
    def __init__(self, config: FraudDetectionConfig):
        self.config = config
        
    def detect_statistical_anomalies(self, df: pd.DataFrame) -> pd.DataFrame:
        """Detect anomalies using statistical methods"""
        try:
            anomaly_scores = pd.DataFrame(index=df.index)
            
            # Z-score based detection
            anomaly_scores['zscore_anomaly'] = self._zscore_anomaly_detection(df)
            
            # Interquartile range based detection
            anomaly_scores['iqr_anomaly'] = self._iqr_anomaly_detection(df)
            
            # Mahalanobis distance based detection
            anomaly_scores['mahalanobis_anomaly'] = self._mahalanobis_anomaly_detection(df)
            
            # Time series based detection
            if 'transaction_time' in df.columns:
                anomaly_scores['time_series_anomaly'] = self._time_series_anomaly_detection(df)
            
            # Combine statistical scores
            anomaly_scores['statistical_score'] = anomaly_scores.mean(axis=1)
            
            return anomaly_scores
            
        except Exception as e:
            logger.error(f"Error in statistical anomaly detection: {str(e)}")
            return pd.DataFrame(index=df.index)
    
    def _zscore_anomaly_detection(self, df: pd.DataFrame) -> pd.Series:
        """Z-score based anomaly detection"""
        try:
            numerical_cols = df.select_dtypes(include=[np.number]).columns
            z_scores = np.abs(stats.zscore(df[numerical_cols], nan_policy='omit'))
            
            # Consider anomaly if any feature has |z-score| > 3
            anomaly_scores = (z_scores > 3).any(axis=1).astype(float)
            
            return pd.Series(anomaly_scores, index=df.index)
            
        except Exception as e:
            logger.error(f"Error in Z-score anomaly detection: {str(e)}")
            return pd.Series(0, index=df.index)
    
    def _iqr_anomaly_detection(self, df: pd.DataFrame) -> pd.Series:
        """IQR based anomaly detection"""
        try:
            numerical_cols = df.select_dtypes(include=[np.number]).columns
            anomaly_scores = np.zeros(len(df))
            
            for col in numerical_cols:
                Q1 = df[col].quantile(0.25)
                Q3 = df[col].quantile(0.75)
                IQR = Q3 - Q1
                
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                
                outliers = (df[col] < lower_bound) | (df[col] > upper_bound)
                anomaly_scores += outliers.astype(float)
            
            # Normalize by number of features
            anomaly_scores = anomaly_scores / len(numerical_cols)
            
            return pd.Series(anomaly_scores, index=df.index)
            
        except Exception as e:
            logger.error(f"Error in IQR anomaly detection: {str(e)}")
            return pd.Series(0, index=df.index)
    
    def _mahalanobis_anomaly_detection(self, df: pd.DataFrame) -> pd.Series:
        """Mahalanobis distance based anomaly detection"""
        try:
            numerical_cols = df.select_dtypes(include=[np.number]).columns
            data = df[numerical_cols].fillna(0)
            
            if len(data) < 2:
                return pd.Series(0, index=df.index)
            
            # Calculate covariance matrix
            cov_matrix = np.cov(data.T)
            
            # Handle singular matrix
            try:
                inv_cov_matrix = np.linalg.inv(cov_matrix)
            except np.linalg.LinAlgError:
                # Use pseudo-inverse for singular matrices
                inv_cov_matrix = np.linalg.pinv(cov_matrix)
            
            # Calculate mean
            mean = np.mean(data, axis=0)
            
            # Calculate Mahalanobis distance for each point
            mahal_distances = []
            for _, row in data.iterrows():
                distance = mahalanobis(row, mean, inv_cov_matrix)
                mahal_distances.append(distance)
            
            # Convert to anomaly scores (higher distance = higher anomaly score)
            mahal_distances = np.array(mahal_distances)
            anomaly_scores = (mahal_distances - np.mean(mahal_distances)) / (np.std(mahal_distances) + 1e-8)
            anomaly_scores = np.clip(anomaly_scores, 0, 1)  # Normalize to [0, 1]
            
            return pd.Series(anomaly_scores, index=df.index)
            
        except Exception as e:
            logger.error(f"Error in Mahalanobis anomaly detection: {str(e)}")
            return pd.Series(0, index=df.index)
    
    def _time_series_anomaly_detection(self, df: pd.DataFrame) -> pd.Series:
        """Time series based anomaly detection"""
        try:
            # Group by time windows and detect volume anomalies
            df['transaction_time'] = pd.to_datetime(df['transaction_time'])
            df_hourly = df.set_index('transaction_time').resample('1H').agg({
                'amount': 'sum',
                'transaction_id': 'count'
            }).fillna(0)
            
            # Use moving average and standard deviation
            window_size = 24  # 24 hours
            df_hourly['amount_ma'] = df_hourly['amount'].rolling(window=window_size, min_periods=1).mean()
            df_hourly['amount_std'] = df_hourly['amount'].rolling(window=window_size, min_periods=1).std()
            df_hourly['count_ma'] = df_hourly['transaction_id'].rolling(window=window_size, min_periods=1).mean()
            df_hourly['count_std'] = df_hourly['transaction_id'].rolling(window=window_size, min_periods=1).std()
            
            # Calculate anomaly scores based on deviation from moving average
            df_hourly['amount_anomaly'] = np.abs(df_hourly['amount'] - df_hourly['amount_ma']) / (df_hourly['amount_std'] + 1e-8)
            df_hourly['count_anomaly'] = np.abs(df_hourly['transaction_id'] - df_hourly['count_ma']) / (df_hourly['count_std'] + 1e-8)
            
            # Combine anomaly scores
            df_hourly['time_series_score'] = (df_hourly['amount_anomaly'] + df_hourly['count_anomaly']) / 2
            
            # Map back to original transactions
            df_with_ts = df.merge(
                df_hourly[['time_series_score']].reset_index(),
                left_on=df['transaction_time'].dt.floor('1H'),
                right_on='transaction_time',
                how='left'
            )
            
            return df_with_ts['time_series_score'].fillna(0)
            
        except Exception as e:
            logger.error(f"Error in time series anomaly detection: {str(e)}")
            return pd.Series(0, index=df.index)

class MLAnomalyDetector:
    """Machine learning based anomaly detection"""
    
    def __init__(self, config: FraudDetectionConfig):
        self.config = config
        self.models = {}
        self.scaler = StandardScaler()
        self.is_trained = False
        
    def train_ml_models(self, df: pd.DataFrame, labels: pd.Series = None):
        """Train multiple ML models for anomaly detection"""
        try:
            # Prepare features
            feature_cols = df.select_dtypes(include=[np.number]).columns
            X = df[feature_cols].fillna(0)
            X_scaled = self.scaler.fit_transform(X)
            
            # Isolation Forest
            if self.config.use_isolation_forest:
                self.models['isolation_forest'] = IsolationForest(
                    contamination=self.config.contamination_rate,
                    random_state=self.config.random_state,
                    n_estimators=self.config.n_estimators
                )
                self.models['isolation_forest'].fit(X_scaled)
            
            # Local Outlier Factor
            if self.config.use_local_outlier_factor:
                self.models['lof'] = LOF(contamination=self.config.contamination_rate)
                self.models['lof'].fit(X_scaled)
            
            # One-Class SVM
            if self.config.use_one_class_svm:
                self.models['one_class_svm'] = OCSVM(contamination=self.config.contamination_rate)
                self.models['one_class_svm'].fit(X_scaled)
            
            # Autoencoder (if enabled)
            if self.config.use_autoencoder:
                self.models['autoencoder'] = self._train_autoencoder(X_scaled)
            
            # Supervised model (if labels available)
            if labels is not None:
                self.models['supervised'] = self._train_supervised_model(X_scaled, labels)
            
            self.is_trained = True
            logger.info(f"Trained {len(self.models)} ML anomaly detection models")
            
        except Exception as e:
            logger.error(f"Error training ML models: {str(e)}")
            raise
    
    def predict_anomalies(self, df: pd.DataFrame) -> pd.DataFrame:
        """Predict anomalies using trained models"""
        try:
            if not self.is_trained:
                raise ValueError("Models not trained yet")
            
            # Prepare features
            feature_cols = df.select_dtypes(include=[np.number]).columns
            X = df[feature_cols].fillna(0)
            X_scaled = self.scaler.transform(X)
            
            anomaly_scores = pd.DataFrame(index=df.index)
            
            # Get predictions from each model
            for model_name, model in self.models.items():
                try:
                    if model_name == 'autoencoder':
                        scores = self._predict_autoencoder_anomalies(model, X_scaled)
                    elif model_name == 'supervised':
                        scores = model.predict_proba(X_scaled)[:, 1]  # Probability of being anomalous
                    else:
                        # PyOD models
                        scores = model.decision_function(X_scaled)
                        # Normalize scores to [0, 1]
                        scores = (scores - scores.min()) / (scores.max() - scores.min() + 1e-8)
                    
                    anomaly_scores[f'{model_name}_score'] = scores
                    
                except Exception as e:
                    logger.warning(f"Error predicting with {model_name}: {str(e)}")
                    anomaly_scores[f'{model_name}_score'] = 0
            
            # Ensemble prediction
            if len(anomaly_scores.columns) > 0:
                anomaly_scores['ml_ensemble_score'] = anomaly_scores.mean(axis=1)
            else:
                anomaly_scores['ml_ensemble_score'] = 0
            
            return anomaly_scores
            
        except Exception as e:
            logger.error(f"Error predicting anomalies: {str(e)}")
            return pd.DataFrame(index=df.index)
    
    def _train_autoencoder(self, X: np.ndarray) -> Model:
        """Train autoencoder for anomaly detection"""
        try:
            input_dim = X.shape[1]
            encoding_dim = max(int(input_dim / 2), 8)
            
            # Build autoencoder
            input_layer = Input(shape=(input_dim,))
            encoder = Dense(encoding_dim, activation='relu')(input_layer)
            encoder = Dropout(0.2)(encoder)
            encoder = Dense(int(encoding_dim / 2), activation='relu')(encoder)
            
            decoder = Dense(encoding_dim, activation='relu')(encoder)
            decoder = Dropout(0.2)(decoder)
            decoder = Dense(input_dim, activation='linear')(decoder)
            
            autoencoder = Model(input_layer, decoder)
            autoencoder.compile(optimizer=Adam(learning_rate=0.001), loss='mse')
            
            # Train autoencoder
            early_stopping = EarlyStopping(monitor='val_loss', patience=10, restore_best_weights=True)
            
            X_train, X_val = train_test_split(X, test_size=0.2, random_state=self.config.random_state)
            
            autoencoder.fit(
                X_train, X_train,
                epochs=100,
                batch_size=32,
                validation_data=(X_val, X_val),
                callbacks=[early_stopping],
                verbose=0
            )
            
            return autoencoder
            
        except Exception as e:
            logger.error(f"Error training autoencoder: {str(e)}")
            return None
    
    def _predict_autoencoder_anomalies(self, autoencoder: Model, X: np.ndarray) -> np.ndarray:
        """Predict anomalies using autoencoder reconstruction error"""
        try:
            reconstructed = autoencoder.predict(X, verbose=0)
            mse = np.mean(np.square(X - reconstructed), axis=1)
            
            # Normalize MSE to [0, 1]
            normalized_mse = (mse - mse.min()) / (mse.max() - mse.min() + 1e-8)
            
            return normalized_mse
            
        except Exception as e:
            logger.error(f"Error predicting autoencoder anomalies: {str(e)}")
            return np.zeros(len(X))
    
    def _train_supervised_model(self, X: np.ndarray, y: np.ndarray) -> Any:
        """Train supervised model for fraud detection"""
        try:
            # Use XGBoost for supervised fraud detection
            model = xgb.XGBClassifier(
                n_estimators=self.config.n_estimators,
                max_depth=6,
                learning_rate=0.1,
                subsample=0.8,
                colsample_bytree=0.8,
                random_state=self.config.random_state,
                scale_pos_weight=len(y) / np.sum(y)  # Handle class imbalance
            )
            
            model.fit(X, y)
            
            return model
            
        except Exception as e:
            logger.error(f"Error training supervised model: {str(e)}")
            return None

class NetworkAnomalyDetector:
    """Graph-based network anomaly detection"""
    
    def __init__(self, config: FraudDetectionConfig):
        self.config = config
        
    def detect_network_anomalies(self, transactions_df: pd.DataFrame) -> pd.DataFrame:
        """Detect anomalies in transaction networks"""
        try:
            if not self.config.enable_network_analysis:
                return pd.DataFrame(index=transactions_df.index)
            
            # Build transaction network
            network_features = self._build_transaction_network(transactions_df)
            
            # Detect community anomalies
            community_anomalies = self._detect_community_anomalies(transactions_df)
            
            # Combine network-based anomaly scores
            network_scores = pd.DataFrame(index=transactions_df.index)
            network_scores['network_centrality_anomaly'] = network_features.get('centrality_anomaly', 0)
            network_scores['network_community_anomaly'] = community_anomalies
            network_scores['network_score'] = network_scores.mean(axis=1)
            
            return network_scores
            
        except Exception as e:
            logger.error(f"Error detecting network anomalies: {str(e)}")
            return pd.DataFrame(index=transactions_df.index)
    
    def _build_transaction_network(self, transactions_df: pd.DataFrame) -> Dict:
        """Build network graph from transaction data"""
        try:
            # Create bipartite graph: customers <-> merchants
            G = nx.Graph()
            
            # Add nodes and edges
            for _, transaction in transactions_df.iterrows():
                customer = f"customer_{transaction['customer_id']}"
                merchant = f"merchant_{transaction['merchant_id']}"
                
                if not G.has_node(customer):
                    G.add_node(customer, node_type='customer')
                if not G.has_node(merchant):
                    G.add_node(merchant, node_type='merchant')
                
                # Add edge with transaction amount as weight
                if G.has_edge(customer, merchant):
                    G[customer][merchant]['weight'] += transaction['amount']
                    G[customer][merchant]['count'] += 1
                else:
                    G.add_edge(customer, merchant, weight=transaction['amount'], count=1)
            
            # Calculate network features
            network_features = {}
            
            # Calculate centrality measures
            centrality_scores = nx.degree_centrality(G)
            betweenness_scores = nx.betweenness_centrality(G)
            
            # Detect anomalous centrality scores
            centrality_values = list(centrality_scores.values())
            centrality_threshold = np.percentile(centrality_values, 95)
            
            network_features['centrality_anomaly'] = [
                1 if centrality_scores.get(f"customer_{cid}", 0) > centrality_threshold else 0
                for cid in transactions_df['customer_id']
            ]
            
            return network_features
            
        except Exception as e:
            logger.error(f"Error building transaction network: {str(e)}")
            return {}
    
    def _detect_community_anomalies(self, transactions_df: pd.DataFrame) -> pd.Series:
        """Detect anomalies in transaction communities"""
        try:
            if not self.config.community_detection:
                return pd.Series(0, index=transactions_df.index)
            
            # Simple community detection based on merchant clustering
            customer_merchant_matrix = transactions_df.groupby(['customer_id', 'merchant_id'])['amount'].sum().unstack(fill_value=0)
            
            if len(customer_merchant_matrix) < 2:
                return pd.Series(0, index=transactions_df.index)
            
            # Use KMeans for community detection
            kmeans = KMeans(n_clusters=min(5, len(customer_merchant_matrix)), random_state=42)
            communities = kmeans.fit_predict(customer_merchant_matrix)
            
            # Calculate community-based anomaly scores
            community_scores = []
            for _, transaction in transactions_df.iterrows():
                customer_idx = customer_merchant_matrix.index.get_loc(transaction['customer_id'])
                community = communities[customer_idx]
                
                # Calculate distance to community centroid
                centroid = kmeans.cluster_centers_[community]
                customer_vector = customer_merchant_matrix.iloc[customer_idx].values
                distance = np.linalg.norm(customer_vector - centroid)
                
                # Normalize distance
                max_distance = np.max([np.linalg.norm(kmeans.cluster_centers_[i] - centroid) 
                                     for i in range(len(kmeans.cluster_centers_))])
                normalized_distance = distance / (max_distance + 1e-8)
                
                community_scores.append(normalized_distance)
            
            return pd.Series(community_scores, index=transactions_df.index)
            
        except Exception as e:
            logger.error(f"Error detecting community anomalies: {str(e)}")
            return pd.Series(0, index=transactions_df.index)

class FraudDetectionEngine:
    """Main fraud detection engine"""
    
    def __init__(self, config: FraudDetectionConfig):
        self.config = config
        self.feature_engineer = FeatureEngineer(config)
        self.statistical_detector = StatisticalAnomalyDetector(config)
        self.ml_detector = MLAnomalyDetector(config)
        self.network_detector = NetworkAnomalyDetector(config)
        
        # Initialize real-time components
        self.redis_client = None
        self.kafka_producer = None
        
        if config.enable_real_time_scoring:
            self._initialize_real_time_components()
        
        # MLflow setup
        mlflow.set_tracking_uri(self.config.mlflow_tracking_uri)
        mlflow.set_experiment(self.config.experiment_name)
    
    def _initialize_real_time_components(self):
        """Initialize Redis and Kafka for real-time processing"""
        try:
            # Initialize Redis
            self.redis_client = redis.Redis(
                host=self.config.redis_host,
                port=self.config.redis_port,
                db=self.config.redis_db,
                decode_responses=True
            )
            self.redis_client.ping()
            
            # Initialize Kafka producer
            self.kafka_producer = KafkaProducer(
                bootstrap_servers=self.config.kafka_bootstrap_servers,
                value_serializer=lambda v: json.dumps(v).encode('utf-8')
            )
            
            logger.info("Real-time components initialized successfully")
            
        except Exception as e:
            logger.warning(f"Could not initialize real-time components: {str(e)}")
    
    def load_training_data(self) -> Tuple[pd.DataFrame, pd.Series]:
        """Load training data from database"""
        try:
            engine = create_engine(
                f"postgresql://{self.config.db_user}:{self.config.db_password}@"
                f"{self.config.db_host}/{self.config.db_name}"
            )
            
            # Load transaction data with labels
            query = """
            SELECT 
                t.transaction_id,
                t.customer_id,
                t.merchant_id,
                t.amount,
                t.transaction_time,
                t.payment_method,
                t.latitude,
                t.longitude,
                c.account_created_date,
                COALESCE(f.is_fraud, 0) as is_fraud
            FROM transactions t
            LEFT JOIN customers c ON t.customer_id = c.customer_id
            LEFT JOIN fraud_labels f ON t.transaction_id = f.transaction_id
            WHERE t.transaction_time >= NOW() - INTERVAL '90 days'
            ORDER BY t.transaction_time DESC
            LIMIT 100000
            """
            
            df = pd.read_sql(query, engine)
            
            # Separate features and labels
            labels = df['is_fraud']
            features_df = df.drop(['is_fraud'], axis=1)
            
            logger.info(f"Loaded {len(df)} transactions for training. Fraud rate: {labels.mean():.3f}")
            
            return features_df, labels
            
        except Exception as e:
            logger.error(f"Error loading training data: {str(e)}")
            raise
    
    def train_fraud_detection_models(self) -> str:
        """Train all fraud detection models"""
        try:
            with mlflow.start_run(run_name="fraud_detection_training"):
                # Load training data
                transactions_df, labels = self.load_training_data()
                
                # Feature engineering
                logger.info("Performing feature engineering...")
                features_df = self.feature_engineer.extract_transaction_features(transactions_df)
                
                # Train ML models
                logger.info("Training ML models...")
                self.ml_detector.train_ml_models(features_df, labels)
                
                # Log training parameters
                mlflow.log_param("n_transactions", len(transactions_df))
                mlflow.log_param("fraud_rate", labels.mean())
                mlflow.log_param("n_features", features_df.shape[1])
                mlflow.log_param("contamination_rate", self.config.contamination_rate)
                
                # Evaluate models
                logger.info("Evaluating models...")
                evaluation_metrics = self.evaluate_models(features_df, labels)
                
                # Log evaluation metrics
                for metric_name, metric_value in evaluation_metrics.items():
                    mlflow.log_metric(metric_name, metric_value)
                
                run_id = mlflow.active_run().info.run_id
                logger.info(f"Fraud detection training completed. Run ID: {run_id}")
                
                return run_id
                
        except Exception as e:
            logger.error(f"Error training fraud detection models: {str(e)}")
            raise
    
    def detect_fraud(self, transaction_data: Union[Dict, pd.DataFrame]) -> Dict[str, Any]:
        """Detect fraud in real-time or batch mode"""
        try:
            # Convert single transaction to DataFrame if needed
            if isinstance(transaction_data, dict):
                transactions_df = pd.DataFrame([transaction_data])
            else:
                transactions_df = transaction_data.copy()
            
            start_time = datetime.now()
            
            # Feature engineering
            features_df = self.feature_engineer.extract_transaction_features(transactions_df)
            
            # Get anomaly scores from different detectors
            anomaly_scores = pd.DataFrame(index=features_df.index)
            
            # Statistical anomaly detection
            if self.config.use_statistical_methods:
                statistical_scores = self.statistical_detector.detect_statistical_anomalies(features_df)
                anomaly_scores = pd.concat([anomaly_scores, statistical_scores], axis=1)
            
            # ML-based anomaly detection
            if self.ml_detector.is_trained:
                ml_scores = self.ml_detector.predict_anomalies(features_df)
                anomaly_scores = pd.concat([anomaly_scores, ml_scores], axis=1)
            
            # Network-based anomaly detection
            if self.config.enable_network_analysis:
                network_scores = self.network_detector.detect_network_anomalies(transactions_df)
                anomaly_scores = pd.concat([anomaly_scores, network_scores], axis=1)
            
            # Combine all scores
            if len(anomaly_scores.columns) > 0:
                final_scores = anomaly_scores.mean(axis=1)
            else:
                final_scores = pd.Series(0, index=features_df.index)
            
            # Apply business rules
            business_rule_scores = self._apply_business_rules(transactions_df)
            combined_scores = (final_scores + business_rule_scores) / 2
            
            # Generate results
            results = []
            for idx, (_, transaction) in enumerate(transactions_df.iterrows()):
                fraud_score = combined_scores.iloc[idx]
                risk_level = self._classify_risk_level(fraud_score)
                
                result = {
                    'transaction_id': transaction.get('transaction_id'),
                    'customer_id': transaction.get('customer_id'),
                    'fraud_score': float(fraud_score),
                    'risk_level': risk_level,
                    'is_fraud_predicted': fraud_score > self.config.high_risk_threshold,
                    'anomaly_details': {
                        'statistical_score': anomaly_scores.get('statistical_score', pd.Series(0)).iloc[idx] if 'statistical_score' in anomaly_scores.columns else 0,
                        'ml_score': anomaly_scores.get('ml_ensemble_score', pd.Series(0)).iloc[idx] if 'ml_ensemble_score' in anomaly_scores.columns else 0,
                        'network_score': anomaly_scores.get('network_score', pd.Series(0)).iloc[idx] if 'network_score' in anomaly_scores.columns else 0,
                        'business_rule_score': business_rule_scores.iloc[idx]
                    },
                    'processing_time_ms': (datetime.now() - start_time).total_seconds() * 1000
                }
                
                results.append(result)
                
                # Send real-time alerts for high-risk transactions
                if result['risk_level'] == 'HIGH' and self.kafka_producer:
                    self._send_fraud_alert(result)
            
            # Return single result for single transaction, list for batch
            if isinstance(transaction_data, dict):
                return results[0]
            else:
                return {'results': results, 'total_processed': len(results)}
                
        except Exception as e:
            logger.error(f"Error detecting fraud: {str(e)}")
            return {'error': str(e)}
    
    def _apply_business_rules(self, transactions_df: pd.DataFrame) -> pd.Series:
        """Apply business rules for fraud detection"""
        try:
            business_scores = pd.Series(0.0, index=transactions_df.index)
            
            # Rule 1: Unusually high transaction amount
            if 'customer_amount_mean' in transactions_df.columns:
                amount_multiplier = transactions_df['amount'] / (transactions_df['customer_amount_mean'] + 1e-8)
                high_amount_score = np.clip(
                    (amount_multiplier - self.config.max_transaction_amount_multiplier) / 
                    self.config.max_transaction_amount_multiplier, 0, 1
                )
                business_scores += high_amount_score * 0.3
            
            # Rule 2: Too many transactions in short time
            if 'transactions_last_hour' in transactions_df.columns:
                high_frequency_score = np.clip(
                    (transactions_df['transactions_last_hour'] - self.config.max_transactions_per_minute) / 
                    self.config.max_transactions_per_minute, 0, 1
                )
                business_scores += high_frequency_score * 0.3
            
            # Rule 3: Impossible travel
            if 'impossible_travel' in transactions_df.columns:
                business_scores += transactions_df['impossible_travel'] * 0.4
            
            # Rule 4: New account with high-value transaction
            if 'is_new_account' in transactions_df.columns:
                new_account_high_value = (
                    transactions_df['is_new_account'] * 
                    (transactions_df['amount'] > transactions_df['amount'].quantile(0.9))
                ).astype(float)
                business_scores += new_account_high_value * 0.2
            
            return np.clip(business_scores, 0, 1)
            
        except Exception as e:
            logger.error(f"Error applying business rules: {str(e)}")
            return pd.Series(0, index=transactions_df.index)
    
    def _classify_risk_level(self, fraud_score: float) -> str:
        """Classify risk level based on fraud score"""
        if fraud_score >= self.config.high_risk_threshold:
            return 'HIGH'
        elif fraud_score >= self.config.medium_risk_threshold:
            return 'MEDIUM'
        elif fraud_score >= self.config.low_risk_threshold:
            return 'LOW'
        else:
            return 'MINIMAL'
    
    def _send_fraud_alert(self, fraud_result: Dict):
        """Send real-time fraud alert"""
        try:
            alert = {
                'alert_type': 'fraud_detection',
                'transaction_id': fraud_result['transaction_id'],
                'customer_id': fraud_result['customer_id'],
                'fraud_score': fraud_result['fraud_score'],
                'risk_level': fraud_result['risk_level'],
                'timestamp': datetime.now().isoformat(),
                'anomaly_details': fraud_result['anomaly_details']
            }
            
            # Send to Kafka
            if self.kafka_producer:
                self.kafka_producer.send(self.config.kafka_topic_alerts, alert)
            
            # Cache in Redis
            if self.redis_client:
                alert_key = f"fraud_alert:{fraud_result['transaction_id']}"
                self.redis_client.setex(alert_key, 86400, json.dumps(alert))  # 24 hour expiry
            
            logger.info(f"Fraud alert sent for transaction {fraud_result['transaction_id']}")
            
        except Exception as e:
            logger.error(f"Error sending fraud alert: {str(e)}")
    
    def evaluate_models(self, features_df: pd.DataFrame, labels: pd.Series) -> Dict[str, float]:
        """Evaluate fraud detection models"""
        try:
            # Get predictions
            anomaly_scores = self.ml_detector.predict_anomalies(features_df)
            
            if 'ml_ensemble_score' not in anomaly_scores.columns:
                return {}
            
            predictions = (anomaly_scores['ml_ensemble_score'] > self.config.high_risk_threshold).astype(int)
            
            # Calculate metrics
            from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
            
            metrics = {
                'accuracy': accuracy_score(labels, predictions),
                'precision': precision_score(labels, predictions, zero_division=0),
                'recall': recall_score(labels, predictions, zero_division=0),
                'f1_score': f1_score(labels, predictions, zero_division=0)
            }
            
            # Add AUC if we have probability scores
            try:
                auc_score = roc_auc_score(labels, anomaly_scores['ml_ensemble_score'])
                metrics['auc_score'] = auc_score
            except:
                pass
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error evaluating models: {str(e)}")
            return {}

def main():
    """Main execution function"""
    config = FraudDetectionConfig()
    
    # Initialize fraud detection engine
    engine = FraudDetectionEngine(config)
    
    # Train models
    run_id = engine.train_fraud_detection_models()
    
    # Test with sample transaction
    sample_transaction = {
        'transaction_id': 'test_001',
        'customer_id': 'customer_123',
        'merchant_id': 'merchant_456',
        'amount': 1500.00,
        'transaction_time': datetime.now(),
        'payment_method': 'credit_card',
        'latitude': 40.7128,
        'longitude': -74.0060
    }
    
    fraud_result = engine.detect_fraud(sample_transaction)
    
    print(f"Fraud detection result for test transaction:")
    print(f"Fraud Score: {fraud_result['fraud_score']:.3f}")
    print(f"Risk Level: {fraud_result['risk_level']}")
    print(f"Predicted Fraud: {fraud_result['is_fraud_predicted']}")
    
    logger.info("Fraud detection engine setup completed successfully")

if __name__ == "__main__":
    main()