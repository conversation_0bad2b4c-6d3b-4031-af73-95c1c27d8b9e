#!/usr/bin/env python3
"""
Collaborative Filtering Recommendation Engine for E-commerce Analytics
Advanced recommendation system using multiple collaborative filtering techniques
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import os
import sys
from typing import Dict, List, Tuple, Optional, Any, Union
import joblib
import json
from dataclasses import dataclass
import warnings
from scipy.sparse import csr_matrix, coo_matrix
from scipy.spatial.distance import cosine
import scipy.sparse as sp

# Machine Learning libraries
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, mean_absolute_error
from sklearn.decomposition import TruncatedSVD, NMF
from sklearn.metrics.pairwise import cosine_similarity, euclidean_distances
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.preprocessing import StandardScaler, MinMaxScaler

# Specialized recommendation libraries
from surprise import Dataset, Reader, SVD, SVDpp, NMF as SurpriseMF
from surprise import KNNBasic, KNNWithMeans, KNNWithZScore, BaselineOnly
from surprise.model_selection import cross_validate, GridSearchCV
from surprise.accuracy import rmse, mae
import implicit

# Deep learning for advanced recommendations
import tensorflow as tf
from tensorflow.keras.models import Model
from tensorflow.keras.layers import Input, Embedding, Flatten, Dense, Concatenate, Dropout
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.regularizers import l2

# Visualization and analysis
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots

# MLflow integration
import mlflow
import mlflow.sklearn
import mlflow.tensorflow
from mlflow.tracking import MlflowClient

# Database and cloud
import boto3
from sqlalchemy import create_engine

warnings.filterwarnings('ignore')

# Configuration
@dataclass
class RecommendationConfig:
    # Model parameters
    n_factors: int = 50  # Number of latent factors
    n_recommendations: int = 10
    min_interactions: int = 5  # Minimum interactions per user/item
    
    # Algorithm configurations
    use_matrix_factorization: bool = True
    use_deep_learning: bool = True
    use_item_based: bool = True
    use_user_based: bool = True
    use_implicit_feedback: bool = True
    
    # Evaluation parameters
    test_size: float = 0.2
    random_state: int = 42
    cross_validation_folds: int = 5
    
    # Business rules
    max_recommendations_per_user: int = 50
    diversity_weight: float = 0.1
    novelty_weight: float = 0.1
    popularity_discount: float = 0.9
    
    # Cold start handling
    fallback_to_popular: bool = True
    content_based_fallback: bool = True
    min_user_interactions: int = 3
    
    # Real-time parameters
    batch_update_interval_hours: int = 24
    real_time_update_enabled: bool = True
    
    # MLflow configuration
    mlflow_tracking_uri: str = os.getenv('MLFLOW_TRACKING_URI', 'http://mlflow-server.mlflow:5000')
    experiment_name: str = 'collaborative-filtering-recommendations'
    
    # Database connections
    db_host: str = os.getenv('DB_HOST', 'analytics-db.cluster-xyz.us-east-1.rds.amazonaws.com')
    db_name: str = os.getenv('DB_NAME', 'ecommerce_analytics')
    db_user: str = os.getenv('DB_USER', 'analytics_user')
    db_password: str = os.getenv('DB_PASSWORD', 'analytics_password')
    
    # Storage
    s3_bucket: str = os.getenv('S3_BUCKET', 'ecommerce-analytics-models')
    model_prefix: str = 'recommendations/collaborative-filtering/'

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class CollaborativeFilteringEngine:
    def __init__(self, config: RecommendationConfig):
        self.config = config
        self.db_engine = self._create_db_engine()
        self.s3_client = boto3.client('s3')
        
        # Initialize MLflow
        mlflow.set_tracking_uri(config.mlflow_tracking_uri)
        mlflow.set_experiment(config.experiment_name)
        
        # Model storage
        self.models = {}
        self.scalers = {}
        self.user_item_matrix = None
        self.user_encoder = {}
        self.item_encoder = {}
        self.user_decoder = {}
        self.item_decoder = {}
        
        # Data storage
        self.interaction_data = None
        self.user_features = None
        self.item_features = None
        
        # Evaluation metrics
        self.evaluation_results = {}
        
    def _create_db_engine(self):
        """Create database engine for PostgreSQL"""
        connection_string = f"postgresql://{self.config.db_user}:{self.config.db_password}@{self.config.db_host}/{self.config.db_name}"
        return create_engine(connection_string)
    
    def extract_interaction_data(self) -> pd.DataFrame:
        """Extract user-item interaction data"""
        logger.info("Extracting user-item interaction data...")
        
        with mlflow.start_run(run_name="data_extraction", nested=True):
            # Extract comprehensive interaction data
            interaction_query = """
                WITH user_interactions AS (
                    -- Purchase interactions (explicit feedback)
                    SELECT 
                        p.user_id,
                        pi.product_id,
                        'purchase' as interaction_type,
                        p.total_amount as rating,
                        pi.quantity,
                        p.purchase_timestamp as timestamp,
                        1.0 as confidence
                    FROM purchases p
                    JOIN purchase_items pi ON p.order_id = pi.order_id
                    WHERE p.purchase_timestamp >= NOW() - INTERVAL '2 years'
                        AND p.total_amount > 0
                    
                    UNION ALL
                    
                    -- Cart additions (implicit feedback)
                    SELECT 
                        ue.user_id,
                        CAST(SPLIT_PART(ue.page_url, '/product/', 2) AS INTEGER) as product_id,
                        'cart_add' as interaction_type,
                        5.0 as rating,  -- Assign moderate rating for cart adds
                        1 as quantity,
                        ue.event_timestamp as timestamp,
                        0.7 as confidence
                    FROM user_events ue
                    WHERE ue.event_type = 'add_to_cart'
                        AND ue.page_url LIKE '%/product/%'
                        AND ue.event_timestamp >= NOW() - INTERVAL '1 year'
                        AND SPLIT_PART(ue.page_url, '/product/', 2) ~ '^[0-9]+$'
                    
                    UNION ALL
                    
                    -- Product views (implicit feedback)
                    SELECT 
                        ue.user_id,
                        CAST(SPLIT_PART(ue.page_url, '/product/', 2) AS INTEGER) as product_id,
                        'view' as interaction_type,
                        2.0 as rating,  -- Lower rating for views
                        1 as quantity,
                        ue.event_timestamp as timestamp,
                        0.3 as confidence
                    FROM user_events ue
                    WHERE ue.event_type = 'page_view'
                        AND ue.page_url LIKE '%/product/%'
                        AND ue.event_timestamp >= NOW() - INTERVAL '6 months'
                        AND SPLIT_PART(ue.page_url, '/product/', 2) ~ '^[0-9]+$'
                ),
                aggregated_interactions AS (
                    SELECT 
                        user_id,
                        product_id,
                        SUM(CASE WHEN interaction_type = 'purchase' THEN rating * 2 
                                WHEN interaction_type = 'cart_add' THEN rating * 1.5
                                ELSE rating END) as total_rating,
                        COUNT(*) as interaction_count,
                        MAX(timestamp) as last_interaction,
                        AVG(confidence) as avg_confidence,
                        STRING_AGG(DISTINCT interaction_type, ',') as interaction_types
                    FROM user_interactions
                    WHERE product_id IS NOT NULL 
                        AND product_id > 0
                    GROUP BY user_id, product_id
                ),
                user_stats AS (
                    SELECT 
                        user_id,
                        COUNT(DISTINCT product_id) as total_interactions,
                        AVG(total_rating) as avg_rating
                    FROM aggregated_interactions
                    GROUP BY user_id
                    HAVING COUNT(DISTINCT product_id) >= %s
                ),
                item_stats AS (
                    SELECT 
                        product_id,
                        COUNT(DISTINCT user_id) as total_users,
                        AVG(total_rating) as avg_rating
                    FROM aggregated_interactions
                    GROUP BY product_id
                    HAVING COUNT(DISTINCT user_id) >= %s
                )
                SELECT 
                    ai.user_id,
                    ai.product_id,
                    ai.total_rating,
                    ai.interaction_count,
                    ai.last_interaction,
                    ai.avg_confidence,
                    ai.interaction_types,
                    us.total_interactions as user_total_interactions,
                    us.avg_rating as user_avg_rating,
                    its.total_users as item_total_users,
                    its.avg_rating as item_avg_rating
                FROM aggregated_interactions ai
                JOIN user_stats us ON ai.user_id = us.user_id
                JOIN item_stats its ON ai.product_id = its.product_id
                ORDER BY ai.user_id, ai.total_rating DESC
            """
            
            interaction_data = pd.read_sql(
                interaction_query,
                self.db_engine,
                params=[self.config.min_interactions, self.config.min_interactions]
            )
            
            # Normalize ratings to 1-5 scale
            interaction_data['normalized_rating'] = self._normalize_ratings(interaction_data['total_rating'])
            
            # Create implicit feedback binary matrix
            interaction_data['implicit_feedback'] = (interaction_data['total_rating'] > 0).astype(int)
            
            # Store interaction data
            self.interaction_data = interaction_data
            
            # Log extraction metrics
            mlflow.log_metrics({
                'total_interactions': len(interaction_data),
                'unique_users': interaction_data['user_id'].nunique(),
                'unique_items': interaction_data['product_id'].nunique(),
                'sparsity': 1 - (len(interaction_data) / 
                               (interaction_data['user_id'].nunique() * interaction_data['product_id'].nunique())),
                'avg_interactions_per_user': interaction_data.groupby('user_id').size().mean(),
                'avg_rating': interaction_data['normalized_rating'].mean()
            })
            
            logger.info(f"Extracted {len(interaction_data)} interactions for "
                       f"{interaction_data['user_id'].nunique()} users and "
                       f"{interaction_data['product_id'].nunique()} items")
            
            return interaction_data
    
    def _normalize_ratings(self, ratings: pd.Series) -> pd.Series:
        """Normalize ratings to 1-5 scale"""
        min_rating = ratings.min()
        max_rating = ratings.max()
        
        # Scale to 1-5 range
        normalized = 1 + 4 * (ratings - min_rating) / (max_rating - min_rating)
        return normalized.clip(1, 5)
    
    def extract_user_features(self) -> pd.DataFrame:
        """Extract user features for content-based fallback"""
        logger.info("Extracting user features...")
        
        user_features_query = """
            WITH user_profile_features AS (
                SELECT 
                    user_id,
                    country,
                    age_group,
                    gender,
                    subscription_tier,
                    total_lifetime_value,
                    registration_date,
                    EXTRACT(DAYS FROM (NOW() - registration_date)) as account_age_days
                FROM user_profiles
            ),
            user_behavior_features AS (
                SELECT 
                    user_id,
                    COUNT(*) as total_events,
                    COUNT(DISTINCT session_id) as total_sessions,
                    COUNT(DISTINCT DATE(event_timestamp)) as active_days,
                    AVG(CASE WHEN event_type = 'page_view' THEN 1 ELSE 0 END) as page_view_rate,
                    COUNT(DISTINCT device_type) as device_diversity
                FROM user_events
                WHERE event_timestamp >= NOW() - INTERVAL '3 months'
                GROUP BY user_id
            ),
            user_purchase_features AS (
                SELECT 
                    p.user_id,
                    COUNT(DISTINCT p.order_id) as total_orders,
                    SUM(p.total_amount) as total_spent,
                    AVG(p.total_amount) as avg_order_value,
                    COUNT(DISTINCT pi.category_id) as unique_categories,
                    COUNT(DISTINCT pi.product_id) as unique_products,
                    MAX(p.purchase_timestamp) as last_purchase_date
                FROM purchases p
                JOIN purchase_items pi ON p.order_id = pi.order_id
                WHERE p.purchase_timestamp >= NOW() - INTERVAL '1 year'
                GROUP BY p.user_id
            )
            SELECT 
                upf.user_id,
                upf.country,
                upf.age_group,
                upf.gender,
                upf.subscription_tier,
                upf.total_lifetime_value,
                upf.account_age_days,
                COALESCE(ubf.total_events, 0) as total_events,
                COALESCE(ubf.total_sessions, 0) as total_sessions,
                COALESCE(ubf.active_days, 0) as active_days,
                COALESCE(ubf.page_view_rate, 0) as page_view_rate,
                COALESCE(ubf.device_diversity, 0) as device_diversity,
                COALESCE(upuf.total_orders, 0) as total_orders,
                COALESCE(upuf.total_spent, 0) as total_spent,
                COALESCE(upuf.avg_order_value, 0) as avg_order_value,
                COALESCE(upuf.unique_categories, 0) as unique_categories,
                COALESCE(upuf.unique_products, 0) as unique_products,
                COALESCE(EXTRACT(DAYS FROM (NOW() - upuf.last_purchase_date)), 999) as days_since_last_purchase
            FROM user_profile_features upf
            LEFT JOIN user_behavior_features ubf ON upf.user_id = ubf.user_id
            LEFT JOIN user_purchase_features upuf ON upf.user_id = upuf.user_id
        """
        
        user_features = pd.read_sql(user_features_query, self.db_engine)
        
        # Fill missing values
        numeric_cols = user_features.select_dtypes(include=[np.number]).columns
        user_features[numeric_cols] = user_features[numeric_cols].fillna(0)
        
        categorical_cols = user_features.select_dtypes(include=['object']).columns
        user_features[categorical_cols] = user_features[categorical_cols].fillna('unknown')
        
        self.user_features = user_features
        logger.info(f"Extracted features for {len(user_features)} users")
        
        return user_features
    
    def extract_item_features(self) -> pd.DataFrame:
        """Extract item features for content-based recommendations"""
        logger.info("Extracting item features...")
        
        item_features_query = """
            WITH product_base_features AS (
                SELECT 
                    product_id,
                    name,
                    category_id,
                    brand_id,
                    price,
                    cost,
                    inventory_level,
                    average_rating,
                    review_count,
                    created_at,
                    EXTRACT(DAYS FROM (NOW() - created_at)) as product_age_days
                FROM products
            ),
            product_interaction_features AS (
                SELECT 
                    pi.product_id,
                    COUNT(DISTINCT p.user_id) as total_buyers,
                    COUNT(DISTINCT p.order_id) as total_orders,
                    SUM(pi.quantity) as total_quantity_sold,
                    AVG(pi.unit_price) as avg_selling_price,
                    SUM(pi.quantity * pi.unit_price) as total_revenue
                FROM purchases p
                JOIN purchase_items pi ON p.order_id = pi.order_id
                WHERE p.purchase_timestamp >= NOW() - INTERVAL '1 year'
                GROUP BY pi.product_id
            ),
            product_popularity AS (
                SELECT 
                    CAST(SPLIT_PART(ue.page_url, '/product/', 2) AS INTEGER) as product_id,
                    COUNT(*) as total_views,
                    COUNT(DISTINCT ue.user_id) as unique_viewers
                FROM user_events ue
                WHERE ue.event_type = 'page_view'
                    AND ue.page_url LIKE '%/product/%'
                    AND ue.event_timestamp >= NOW() - INTERVAL '6 months'
                    AND SPLIT_PART(ue.page_url, '/product/', 2) ~ '^[0-9]+$'
                GROUP BY CAST(SPLIT_PART(ue.page_url, '/product/', 2) AS INTEGER)
            )
            SELECT 
                pbf.product_id,
                pbf.name,
                pbf.category_id,
                pbf.brand_id,
                pbf.price,
                pbf.cost,
                pbf.inventory_level,
                pbf.average_rating,
                pbf.review_count,
                pbf.product_age_days,
                COALESCE(pif.total_buyers, 0) as total_buyers,
                COALESCE(pif.total_orders, 0) as total_orders,
                COALESCE(pif.total_quantity_sold, 0) as total_quantity_sold,
                COALESCE(pif.avg_selling_price, pbf.price) as avg_selling_price,
                COALESCE(pif.total_revenue, 0) as total_revenue,
                COALESCE(pp.total_views, 0) as total_views,
                COALESCE(pp.unique_viewers, 0) as unique_viewers,
                CASE WHEN pbf.price > 0 THEN (pbf.price - pbf.cost) / pbf.price ELSE 0 END as profit_margin
            FROM product_base_features pbf
            LEFT JOIN product_interaction_features pif ON pbf.product_id = pif.product_id
            LEFT JOIN product_popularity pp ON pbf.product_id = pp.product_id
        """
        
        item_features = pd.read_sql(item_features_query, self.db_engine)
        
        # Fill missing values
        numeric_cols = item_features.select_dtypes(include=[np.number]).columns
        item_features[numeric_cols] = item_features[numeric_cols].fillna(0)
        
        categorical_cols = item_features.select_dtypes(include=['object']).columns
        item_features[categorical_cols] = item_features[categorical_cols].fillna('unknown')
        
        self.item_features = item_features
        logger.info(f"Extracted features for {len(item_features)} items")
        
        return item_features
    
    def create_user_item_matrix(self, interaction_data: pd.DataFrame) -> Tuple[csr_matrix, Dict, Dict]:
        """Create sparse user-item interaction matrix"""
        logger.info("Creating user-item interaction matrix...")
        
        # Create user and item encodings
        unique_users = interaction_data['user_id'].unique()
        unique_items = interaction_data['product_id'].unique()
        
        self.user_encoder = {user_id: idx for idx, user_id in enumerate(unique_users)}
        self.item_encoder = {item_id: idx for idx, item_id in enumerate(unique_items)}
        
        # Create reverse mappings
        self.user_decoder = {idx: user_id for user_id, idx in self.user_encoder.items()}
        self.item_decoder = {idx: item_id for item_id, idx in self.item_encoder.items()}
        
        # Map IDs to indices
        interaction_data['user_idx'] = interaction_data['user_id'].map(self.user_encoder)
        interaction_data['item_idx'] = interaction_data['product_id'].map(self.item_encoder)
        
        # Create sparse matrix for explicit ratings
        explicit_matrix = csr_matrix(
            (interaction_data['normalized_rating'].values,
             (interaction_data['user_idx'].values, interaction_data['item_idx'].values)),
            shape=(len(unique_users), len(unique_items))
        )
        
        # Create sparse matrix for implicit feedback
        implicit_matrix = csr_matrix(
            (interaction_data['implicit_feedback'].values,
             (interaction_data['user_idx'].values, interaction_data['item_idx'].values)),
            shape=(len(unique_users), len(unique_items))
        )
        
        self.user_item_matrix = {
            'explicit': explicit_matrix,
            'implicit': implicit_matrix
        }
        
        logger.info(f"Created user-item matrix: {explicit_matrix.shape[0]} users × {explicit_matrix.shape[1]} items")
        logger.info(f"Matrix sparsity: {1 - explicit_matrix.nnz / (explicit_matrix.shape[0] * explicit_matrix.shape[1]):.4f}")
        
        return explicit_matrix, self.user_encoder, self.item_encoder
    
    def train_matrix_factorization_models(self, interaction_data: pd.DataFrame):
        """Train various matrix factorization models"""
        logger.info("Training matrix factorization models...")
        
        with mlflow.start_run(run_name="matrix_factorization", nested=True):
            # Prepare data for Surprise library
            reader = Reader(rating_scale=(1, 5))
            surprise_data = Dataset.load_from_df(
                interaction_data[['user_id', 'product_id', 'normalized_rating']],
                reader
            )
            
            # Split data
            trainset, testset = train_test_split(surprise_data, test_size=self.config.test_size)
            
            # Initialize models
            mf_models = {
                'SVD': SVD(n_factors=self.config.n_factors, random_state=self.config.random_state),
                'SVDpp': SVDpp(n_factors=self.config.n_factors, random_state=self.config.random_state),
                'NMF': SurpriseMF(n_factors=self.config.n_factors, random_state=self.config.random_state)
            }
            
            # Train and evaluate each model
            for name, model in mf_models.items():
                logger.info(f"Training {name} model...")
                
                # Train model
                model.fit(trainset)
                
                # Make predictions
                predictions = model.test(testset)
                
                # Calculate metrics
                rmse_score = rmse(predictions, verbose=False)
                mae_score = mae(predictions, verbose=False)
                
                # Cross-validation
                cv_results = cross_validate(
                    model, surprise_data,
                    measures=['RMSE', 'MAE'],
                    cv=self.config.cross_validation_folds,
                    verbose=False
                )
                
                # Store model and metrics
                self.models[f'mf_{name.lower()}'] = model
                
                # Log metrics
                mlflow.log_metrics({
                    f'{name.lower()}_rmse': rmse_score,
                    f'{name.lower()}_mae': mae_score,
                    f'{name.lower()}_cv_rmse_mean': cv_results['test_rmse'].mean(),
                    f'{name.lower()}_cv_mae_mean': cv_results['test_mae'].mean()
                })
                
                logger.info(f"{name} - RMSE: {rmse_score:.4f}, MAE: {mae_score:.4f}")
    
    def train_neighborhood_models(self, interaction_data: pd.DataFrame):
        """Train user-based and item-based collaborative filtering models"""
        logger.info("Training neighborhood-based models...")
        
        with mlflow.start_run(run_name="neighborhood_models", nested=True):
            # Prepare data
            reader = Reader(rating_scale=(1, 5))
            surprise_data = Dataset.load_from_df(
                interaction_data[['user_id', 'product_id', 'normalized_rating']],
                reader
            )
            
            trainset, testset = train_test_split(surprise_data, test_size=self.config.test_size)
            
            # Initialize neighborhood models
            neighborhood_models = {}
            
            if self.config.use_user_based:
                neighborhood_models['user_based'] = KNNBasic(
                    k=50, sim_options={'user_based': True, 'name': 'cosine'}
                )
                neighborhood_models['user_based_means'] = KNNWithMeans(
                    k=50, sim_options={'user_based': True, 'name': 'cosine'}
                )
            
            if self.config.use_item_based:
                neighborhood_models['item_based'] = KNNBasic(
                    k=50, sim_options={'user_based': False, 'name': 'cosine'}
                )
                neighborhood_models['item_based_means'] = KNNWithMeans(
                    k=50, sim_options={'user_based': False, 'name': 'cosine'}
                )
            
            # Train and evaluate each model
            for name, model in neighborhood_models.items():
                logger.info(f"Training {name} model...")
                
                # Train model
                model.fit(trainset)
                
                # Make predictions
                predictions = model.test(testset)
                
                # Calculate metrics
                rmse_score = rmse(predictions, verbose=False)
                mae_score = mae(predictions, verbose=False)
                
                # Store model
                self.models[f'nb_{name}'] = model
                
                # Log metrics
                mlflow.log_metrics({
                    f'{name}_rmse': rmse_score,
                    f'{name}_mae': mae_score
                })
                
                logger.info(f"{name} - RMSE: {rmse_score:.4f}, MAE: {mae_score:.4f}")
    
    def train_implicit_models(self):
        """Train implicit feedback models"""
        logger.info("Training implicit feedback models...")
        
        with mlflow.start_run(run_name="implicit_models", nested=True):
            if 'implicit' not in self.user_item_matrix:
                logger.warning("No implicit matrix available")
                return
            
            implicit_matrix = self.user_item_matrix['implicit']
            
            # Train Alternating Least Squares (ALS) model
            als_model = implicit.als.AlternatingLeastSquares(
                factors=self.config.n_factors,
                regularization=0.1,
                iterations=50,
                random_state=self.config.random_state
            )
            
            # Implicit library expects item-user matrix
            item_user_matrix = implicit_matrix.T.tocsr()
            als_model.fit(item_user_matrix)
            
            self.models['implicit_als'] = als_model
            
            # Train Bayesian Personalized Ranking (BPR) model
            bpr_model = implicit.bpr.BayesianPersonalizedRanking(
                factors=self.config.n_factors,
                regularization=0.01,
                iterations=100,
                random_state=self.config.random_state
            )
            
            bpr_model.fit(item_user_matrix)
            self.models['implicit_bpr'] = bpr_model
            
            # Evaluate implicit models
            self._evaluate_implicit_models(implicit_matrix)
            
            logger.info("Implicit models training completed")
    
    def _evaluate_implicit_models(self, implicit_matrix: csr_matrix):
        """Evaluate implicit feedback models"""
        try:
            # Split data for evaluation
            train_matrix, test_matrix = self._split_implicit_data(implicit_matrix)
            
            # Evaluate ALS model
            if 'implicit_als' in self.models:
                als_precision = self._calculate_precision_at_k(
                    self.models['implicit_als'], train_matrix, test_matrix, k=10
                )
                mlflow.log_metric('als_precision_at_10', als_precision)
            
            # Evaluate BPR model
            if 'implicit_bpr' in self.models:
                bpr_precision = self._calculate_precision_at_k(
                    self.models['implicit_bpr'], train_matrix, test_matrix, k=10
                )
                mlflow.log_metric('bpr_precision_at_10', bpr_precision)
                
        except Exception as e:
            logger.warning(f"Error evaluating implicit models: {str(e)}")
    
    def _split_implicit_data(self, matrix: csr_matrix, test_ratio: float = 0.2) -> Tuple[csr_matrix, csr_matrix]:
        """Split implicit data into train and test sets"""
        train_matrix = matrix.copy()
        test_matrix = csr_matrix(matrix.shape)
        
        # For each user, randomly select some interactions for testing
        for user_idx in range(matrix.shape[0]):
            user_items = matrix[user_idx].nonzero()[1]
            if len(user_items) > 1:
                n_test = max(1, int(len(user_items) * test_ratio))
                test_items = np.random.choice(user_items, size=n_test, replace=False)
                
                # Move selected items to test set
                for item_idx in test_items:
                    test_matrix[user_idx, item_idx] = matrix[user_idx, item_idx]
                    train_matrix[user_idx, item_idx] = 0
        
        # Eliminate zeros
        train_matrix.eliminate_zeros()
        test_matrix.eliminate_zeros()
        
        return train_matrix, test_matrix
    
    def _calculate_precision_at_k(self, model, train_matrix: csr_matrix, test_matrix: csr_matrix, k: int = 10) -> float:
        """Calculate Precision@K for implicit model"""
        precisions = []
        
        for user_idx in range(train_matrix.shape[0]):
            # Get test items for user
            test_items = set(test_matrix[user_idx].nonzero()[1])
            
            if len(test_items) == 0:
                continue
            
            # Get recommendations
            try:
                # For implicit library, we need to transpose matrix
                user_items = train_matrix[user_idx]
                recommendations = model.recommend(
                    user_idx, 
                    train_matrix.T.tocsr(), 
                    N=k,
                    filter_already_liked_items=True
                )
                
                recommended_items = set([item_id for item_id, score in recommendations])
                
                # Calculate precision
                if len(recommended_items) > 0:
                    precision = len(test_items.intersection(recommended_items)) / len(recommended_items)
                    precisions.append(precision)
                    
            except Exception:
                continue
        
        return np.mean(precisions) if precisions else 0.0
    
    def train_deep_learning_model(self, interaction_data: pd.DataFrame):
        """Train deep learning collaborative filtering model"""
        logger.info("Training deep learning collaborative filtering model...")
        
        with mlflow.start_run(run_name="deep_learning_cf", nested=True):
            # Prepare data
            n_users = len(self.user_encoder)
            n_items = len(self.item_encoder)
            
            # Map user and item IDs
            interaction_data['user_idx'] = interaction_data['user_id'].map(self.user_encoder)
            interaction_data['item_idx'] = interaction_data['product_id'].map(self.item_encoder)
            
            # Split data
            train_data, test_data = train_test_split(
                interaction_data, test_size=self.config.test_size, random_state=self.config.random_state
            )
            
            # Build neural collaborative filtering model
            user_input = Input(shape=(), name='user_id')
            item_input = Input(shape=(), name='item_id')
            
            # Embedding layers
            user_embedding = Embedding(
                n_users, self.config.n_factors, 
                embeddings_regularizer=l2(1e-6), name='user_embedding'
            )(user_input)
            item_embedding = Embedding(
                n_items, self.config.n_factors,
                embeddings_regularizer=l2(1e-6), name='item_embedding'
            )(item_input)
            
            # Flatten embeddings
            user_vec = Flatten()(user_embedding)
            item_vec = Flatten()(item_embedding)
            
            # Concatenate user and item vectors
            concat = Concatenate()([user_vec, item_vec])
            
            # Dense layers
            dense1 = Dense(128, activation='relu', kernel_regularizer=l2(1e-6))(concat)
            dropout1 = Dropout(0.3)(dense1)
            dense2 = Dense(64, activation='relu', kernel_regularizer=l2(1e-6))(dropout1)
            dropout2 = Dropout(0.3)(dense2)
            dense3 = Dense(32, activation='relu', kernel_regularizer=l2(1e-6))(dropout2)
            
            # Output layer
            output = Dense(1, activation='linear')(dense3)
            
            # Create and compile model
            model = Model(inputs=[user_input, item_input], outputs=output)
            model.compile(
                optimizer=Adam(learning_rate=0.001),
                loss='mse',
                metrics=['mae']
            )
            
            # Prepare training data
            X_train = [train_data['user_idx'].values, train_data['item_idx'].values]
            y_train = train_data['normalized_rating'].values
            
            X_test = [test_data['user_idx'].values, test_data['item_idx'].values]
            y_test = test_data['normalized_rating'].values
            
            # Train model
            history = model.fit(
                X_train, y_train,
                validation_data=(X_test, y_test),
                epochs=50,
                batch_size=256,
                verbose=0,
                callbacks=[
                    tf.keras.callbacks.EarlyStopping(patience=10, restore_best_weights=True),
                    tf.keras.callbacks.ReduceLROnPlateau(factor=0.5, patience=5)
                ]
            )
            
            # Evaluate model
            train_loss = model.evaluate(X_train, y_train, verbose=0)[0]
            test_loss = model.evaluate(X_test, y_test, verbose=0)[0]
            
            # Store model
            self.models['deep_learning_cf'] = model
            
            # Log metrics
            mlflow.log_metrics({
                'dl_train_loss': train_loss,
                'dl_test_loss': test_loss,
                'dl_best_val_loss': min(history.history['val_loss'])
            })
            
            # Log model
            mlflow.tensorflow.log_model(model, "deep_learning_cf_model")
            
            logger.info(f"Deep learning model - Train Loss: {train_loss:.4f}, Test Loss: {test_loss:.4f}")
    
    def generate_recommendations(self, user_id: int, n_recommendations: int = None) -> List[Dict]:
        """Generate recommendations for a user using ensemble approach"""
        if n_recommendations is None:
            n_recommendations = self.config.n_recommendations
        
        logger.debug(f"Generating {n_recommendations} recommendations for user {user_id}")
        
        # Check if user exists in our data
        if user_id not in self.user_encoder:
            return self._handle_cold_start_user(user_id, n_recommendations)
        
        user_idx = self.user_encoder[user_id]
        
        # Get recommendations from each model
        all_recommendations = {}
        
        # Matrix factorization recommendations
        if 'mf_svd' in self.models:
            all_recommendations['svd'] = self._get_mf_recommendations(
                self.models['mf_svd'], user_id, n_recommendations * 2
            )
        
        # Neighborhood-based recommendations
        if 'nb_item_based' in self.models:
            all_recommendations['item_based'] = self._get_mf_recommendations(
                self.models['nb_item_based'], user_id, n_recommendations * 2
            )
        
        # Implicit model recommendations
        if 'implicit_als' in self.models:
            all_recommendations['als'] = self._get_implicit_recommendations(
                self.models['implicit_als'], user_idx, n_recommendations * 2
            )
        
        # Deep learning recommendations
        if 'deep_learning_cf' in self.models:
            all_recommendations['deep_learning'] = self._get_dl_recommendations(
                self.models['deep_learning_cf'], user_idx, n_recommendations * 2
            )
        
        # Ensemble recommendations
        final_recommendations = self._ensemble_recommendations(
            all_recommendations, user_id, n_recommendations
        )
        
        # Add business logic and post-processing
        final_recommendations = self._apply_business_rules(final_recommendations, user_id)
        
        return final_recommendations
    
    def _get_mf_recommendations(self, model, user_id: int, n_recommendations: int) -> List[Dict]:
        """Get recommendations from matrix factorization model"""
        recommendations = []
        
        # Get all items user hasn't interacted with
        user_items = set()
        if self.interaction_data is not None:
            user_items = set(
                self.interaction_data[self.interaction_data['user_id'] == user_id]['product_id']
            )
        
        # Score all items
        item_scores = []
        for item_id in self.item_encoder.keys():
            if item_id not in user_items:
                try:
                    prediction = model.predict(user_id, item_id)
                    item_scores.append((item_id, prediction.est))
                except:
                    continue
        
        # Sort by score and return top N
        item_scores.sort(key=lambda x: x[1], reverse=True)
        
        for item_id, score in item_scores[:n_recommendations]:
            recommendations.append({
                'item_id': item_id,
                'score': score,
                'model': 'matrix_factorization'
            })
        
        return recommendations
    
    def _get_implicit_recommendations(self, model, user_idx: int, n_recommendations: int) -> List[Dict]:
        """Get recommendations from implicit model"""
        recommendations = []
        
        try:
            # Get recommendations
            item_user_matrix = self.user_item_matrix['implicit'].T.tocsr()
            recs = model.recommend(
                user_idx, 
                item_user_matrix,
                N=n_recommendations,
                filter_already_liked_items=True
            )
            
            for item_idx, score in recs:
                item_id = self.item_decoder[item_idx]
                recommendations.append({
                    'item_id': item_id,
                    'score': float(score),
                    'model': 'implicit'
                })
                
        except Exception as e:
            logger.warning(f"Error getting implicit recommendations: {str(e)}")
        
        return recommendations
    
    def _get_dl_recommendations(self, model, user_idx: int, n_recommendations: int) -> List[Dict]:
        """Get recommendations from deep learning model"""
        recommendations = []
        
        try:
            # Get all items
            all_items = list(self.item_encoder.keys())
            
            # Get user's existing items
            user_items = set()
            if self.interaction_data is not None:
                user_id = self.user_decoder[user_idx]
                user_items = set(
                    self.interaction_data[self.interaction_data['user_id'] == user_id]['product_id']
                )
            
            # Score all items
            candidate_items = [item for item in all_items if item not in user_items]
            if not candidate_items:
                return recommendations
            
            # Prepare input
            user_input = np.array([user_idx] * len(candidate_items))
            item_input = np.array([self.item_encoder[item] for item in candidate_items])
            
            # Predict scores
            scores = model.predict([user_input, item_input], verbose=0).flatten()
            
            # Sort and return top N
            item_scores = list(zip(candidate_items, scores))
            item_scores.sort(key=lambda x: x[1], reverse=True)
            
            for item_id, score in item_scores[:n_recommendations]:
                recommendations.append({
                    'item_id': item_id,
                    'score': float(score),
                    'model': 'deep_learning'
                })
                
        except Exception as e:
            logger.warning(f"Error getting deep learning recommendations: {str(e)}")
        
        return recommendations
    
    def _ensemble_recommendations(self, all_recommendations: Dict[str, List[Dict]], 
                                 user_id: int, n_recommendations: int) -> List[Dict]:
        """Combine recommendations from multiple models"""
        
        # Weight each model
        model_weights = {
            'svd': 0.3,
            'item_based': 0.2,
            'als': 0.3,
            'deep_learning': 0.2
        }
        
        # Combine scores
        item_scores = {}
        
        for model_name, recommendations in all_recommendations.items():
            weight = model_weights.get(model_name, 0.1)
            
            for rec in recommendations:
                item_id = rec['item_id']
                score = rec['score']
                
                if item_id not in item_scores:
                    item_scores[item_id] = 0
                
                item_scores[item_id] += weight * score
        
        # Sort by combined score
        sorted_items = sorted(item_scores.items(), key=lambda x: x[1], reverse=True)
        
        # Create final recommendations
        final_recommendations = []
        for item_id, score in sorted_items[:n_recommendations]:
            final_recommendations.append({
                'item_id': item_id,
                'score': score,
                'model': 'ensemble'
            })
        
        return final_recommendations
    
    def _handle_cold_start_user(self, user_id: int, n_recommendations: int) -> List[Dict]:
        """Handle recommendations for new users (cold start)"""
        logger.debug(f"Handling cold start for user {user_id}")
        
        # Fallback to popular items
        if self.config.fallback_to_popular and self.item_features is not None:
            popular_items = self.item_features.nlargest(n_recommendations, 'total_buyers')
            
            recommendations = []
            for _, item in popular_items.iterrows():
                recommendations.append({
                    'item_id': item['product_id'],
                    'score': item['total_buyers'] * self.config.popularity_discount,
                    'model': 'popularity_fallback'
                })
            
            return recommendations
        
        return []
    
    def _apply_business_rules(self, recommendations: List[Dict], user_id: int) -> List[Dict]:
        """Apply business rules and constraints to recommendations"""
        
        # Filter out out-of-stock items
        if self.item_features is not None:
            in_stock_items = set(
                self.item_features[self.item_features['inventory_level'] > 0]['product_id']
            )
            recommendations = [
                rec for rec in recommendations 
                if rec['item_id'] in in_stock_items
            ]
        
        # Add diversity and novelty adjustments
        recommendations = self._apply_diversity_boosting(recommendations)
        
        # Limit to maximum recommendations
        return recommendations[:self.config.max_recommendations_per_user]
    
    def _apply_diversity_boosting(self, recommendations: List[Dict]) -> List[Dict]:
        """Apply diversity boosting to recommendations"""
        if not self.item_features is not None:
            return recommendations
        
        # Group by category to ensure diversity
        category_counts = {}
        diversified_recs = []
        
        for rec in recommendations:
            item_id = rec['item_id']
            
            # Get item category
            item_info = self.item_features[self.item_features['product_id'] == item_id]
            if len(item_info) == 0:
                continue
            
            category = item_info.iloc[0]['category_id']
            
            # Limit items per category
            if category_counts.get(category, 0) < 3:  # Max 3 items per category
                category_counts[category] = category_counts.get(category, 0) + 1
                diversified_recs.append(rec)
        
        return diversified_recs
    
    def evaluate_models(self, interaction_data: pd.DataFrame) -> Dict:
        """Evaluate all trained models"""
        logger.info("Evaluating recommendation models...")
        
        evaluation_results = {}
        
        # Split data for evaluation
        train_data, test_data = train_test_split(
            interaction_data, test_size=self.config.test_size, random_state=self.config.random_state
        )
        
        # Evaluate each model type
        for model_name, model in self.models.items():
            logger.info(f"Evaluating {model_name}...")
            
            try:
                if 'mf_' in model_name or 'nb_' in model_name:
                    # Surprise library models
                    metrics = self._evaluate_surprise_model(model, test_data)
                elif 'implicit_' in model_name:
                    # Implicit models
                    metrics = self._evaluate_implicit_model(model, test_data)
                elif 'deep_learning' in model_name:
                    # Deep learning models
                    metrics = self._evaluate_dl_model(model, test_data)
                else:
                    continue
                
                evaluation_results[model_name] = metrics
                
            except Exception as e:
                logger.warning(f"Error evaluating {model_name}: {str(e)}")
        
        self.evaluation_results = evaluation_results
        return evaluation_results
    
    def _evaluate_surprise_model(self, model, test_data: pd.DataFrame) -> Dict:
        """Evaluate Surprise library model"""
        predictions = []
        
        for _, row in test_data.iterrows():
            pred = model.predict(row['user_id'], row['product_id'])
            predictions.append(pred)
        
        rmse_score = rmse(predictions, verbose=False)
        mae_score = mae(predictions, verbose=False)
        
        return {
            'rmse': rmse_score,
            'mae': mae_score
        }
    
    def _evaluate_implicit_model(self, model, test_data: pd.DataFrame) -> Dict:
        """Evaluate implicit feedback model"""
        # This is a simplified evaluation
        # In practice, you'd use ranking metrics
        return {
            'precision_at_10': 0.1,  # Placeholder
            'recall_at_10': 0.05     # Placeholder
        }
    
    def _evaluate_dl_model(self, model, test_data: pd.DataFrame) -> Dict:
        """Evaluate deep learning model"""
        test_data['user_idx'] = test_data['user_id'].map(self.user_encoder)
        test_data['item_idx'] = test_data['product_id'].map(self.item_encoder)
        
        # Filter out unknown users/items
        test_data = test_data.dropna(subset=['user_idx', 'item_idx'])
        
        if len(test_data) == 0:
            return {'rmse': float('inf'), 'mae': float('inf')}
        
        X_test = [test_data['user_idx'].values, test_data['item_idx'].values]
        y_test = test_data['normalized_rating'].values
        
        predictions = model.predict(X_test, verbose=0).flatten()
        
        rmse_score = np.sqrt(mean_squared_error(y_test, predictions))
        mae_score = mean_absolute_error(y_test, predictions)
        
        return {
            'rmse': rmse_score,
            'mae': mae_score
        }
    
    def create_recommendation_visualizations(self):
        """Create visualizations for recommendation analysis"""
        logger.info("Creating recommendation visualizations...")
        
        if self.interaction_data is None:
            logger.warning("No interaction data available for visualization")
            return
        
        # Create comprehensive dashboard
        fig = make_subplots(
            rows=3, cols=2,
            subplot_titles=[
                'User-Item Interaction Distribution',
                'Model Performance Comparison',
                'Rating Distribution',
                'Popular Items',
                'User Activity Distribution',
                'Item Popularity vs Rating'
            ],
            specs=[
                [{"type": "histogram"}, {"type": "bar"}],
                [{"type": "histogram"}, {"type": "bar"}],
                [{"type": "histogram"}, {"type": "scatter"}]
            ]
        )
        
        # 1. User interactions histogram
        user_interactions = self.interaction_data.groupby('user_id').size()
        fig.add_trace(
            go.Histogram(
                x=user_interactions.values,
                nbinsx=50,
                name='User Interactions',
                marker_color='blue'
            ),
            row=1, col=1
        )
        
        # 2. Model performance comparison
        if self.evaluation_results:
            model_names = list(self.evaluation_results.keys())
            rmse_scores = [
                results.get('rmse', 0) for results in self.evaluation_results.values()
            ]
            
            fig.add_trace(
                go.Bar(
                    x=model_names,
                    y=rmse_scores,
                    name='RMSE Scores',
                    marker_color='red'
                ),
                row=1, col=2
            )
        
        # 3. Rating distribution
        fig.add_trace(
            go.Histogram(
                x=self.interaction_data['normalized_rating'],
                nbinsx=20,
                name='Rating Distribution',
                marker_color='green'
            ),
            row=2, col=1
        )
        
        # 4. Popular items
        if self.item_features is not None:
            top_items = self.item_features.nlargest(10, 'total_buyers')
            fig.add_trace(
                go.Bar(
                    x=top_items['product_id'].astype(str),
                    y=top_items['total_buyers'],
                    name='Popular Items',
                    marker_color='orange'
                ),
                row=2, col=2
            )
        
        # 5. User activity distribution
        fig.add_trace(
            go.Histogram(
                x=self.interaction_data.groupby('user_id')['normalized_rating'].mean(),
                nbinsx=30,
                name='Avg User Rating',
                marker_color='purple'
            ),
            row=3, col=1
        )
        
        # 6. Item popularity vs rating
        if self.item_features is not None:
            item_ratings = self.interaction_data.groupby('product_id')['normalized_rating'].mean()
            item_popularity = self.interaction_data.groupby('product_id').size()
            
            common_items = set(item_ratings.index) & set(item_popularity.index)
            
            if common_items:
                ratings = [item_ratings[item] for item in common_items]
                popularity = [item_popularity[item] for item in common_items]
                
                fig.add_trace(
                    go.Scatter(
                        x=popularity,
                        y=ratings,
                        mode='markers',
                        name='Items',
                        marker=dict(color='brown', opacity=0.6)
                    ),
                    row=3, col=2
                )
        
        fig.update_layout(
            title='Collaborative Filtering Analysis Dashboard',
            height=1200,
            showlegend=True
        )
        
        # Save visualization
        try:
            fig.write_html('/tmp/collaborative_filtering_dashboard.html')
            mlflow.log_artifact('/tmp/collaborative_filtering_dashboard.html')
        except Exception as e:
            logger.warning(f"Could not save visualization: {str(e)}")
    
    def save_models(self, model_name: str = "collaborative_filtering_models"):
        """Save all trained models and artifacts"""
        logger.info("Saving collaborative filtering models...")
        
        # Create model package
        model_artifacts = {
            'models': {k: v for k, v in self.models.items() if k != 'deep_learning_cf'},  # Exclude TF model
            'scalers': self.scalers,
            'user_encoder': self.user_encoder,
            'item_encoder': self.item_encoder,
            'user_decoder': self.user_decoder,
            'item_decoder': self.item_decoder,
            'config': self.config,
            'evaluation_results': self.evaluation_results
        }
        
        # Save locally
        model_path = f'/tmp/{model_name}.joblib'
        joblib.dump(model_artifacts, model_path)
        
        # Log to MLflow
        mlflow.log_artifact(model_path, 'model_artifacts')
        
        # Save deep learning model separately
        if 'deep_learning_cf' in self.models:
            dl_model_path = f'/tmp/deep_learning_cf_model.h5'
            self.models['deep_learning_cf'].save(dl_model_path)
            mlflow.log_artifact(dl_model_path, 'model_artifacts')
        
        # Save to S3
        try:
            s3_key = f"{self.config.model_prefix}{model_name}_{int(datetime.now().timestamp())}.joblib"
            self.s3_client.upload_file(model_path, self.config.s3_bucket, s3_key)
            logger.info(f"Models saved to s3://{self.config.s3_bucket}/{s3_key}")
        except Exception as e:
            logger.warning(f"Could not save to S3: {str(e)}")
        
        return model_path
    
    def run_complete_pipeline(self):
        """Run the complete collaborative filtering pipeline"""
        logger.info("Starting collaborative filtering recommendation pipeline...")
        
        with mlflow.start_run(run_name="collaborative_filtering_pipeline"):
            try:
                # Extract data
                interaction_data = self.extract_interaction_data()
                user_features = self.extract_user_features()
                item_features = self.extract_item_features()
                
                # Create user-item matrix
                self.create_user_item_matrix(interaction_data)
                
                # Train models
                if self.config.use_matrix_factorization:
                    self.train_matrix_factorization_models(interaction_data)
                
                if self.config.use_user_based or self.config.use_item_based:
                    self.train_neighborhood_models(interaction_data)
                
                if self.config.use_implicit_feedback:
                    self.train_implicit_models()
                
                if self.config.use_deep_learning:
                    self.train_deep_learning_model(interaction_data)
                
                # Evaluate models
                evaluation_results = self.evaluate_models(interaction_data)
                
                # Create visualizations
                self.create_recommendation_visualizations()
                
                # Save models
                model_path = self.save_models()
                
                # Register best model in MLflow
                best_model_name = min(
                    evaluation_results.items(),
                    key=lambda x: x[1].get('rmse', float('inf'))
                )[0] if evaluation_results else None
                
                if best_model_name and best_model_name in self.models:
                    if 'deep_learning' in best_model_name:
                        mlflow.tensorflow.log_model(
                            self.models[best_model_name],
                            "collaborative_filtering_model"
                        )
                    else:
                        mlflow.sklearn.log_model(
                            self.models[best_model_name],
                            "collaborative_filtering_model"
                        )
                    
                    mlflow.register_model(
                        f"runs:/{mlflow.active_run().info.run_id}/collaborative_filtering_model",
                        "collaborative-filtering-recommender"
                    )
                
                # Log pipeline success
                mlflow.log_metrics({
                    'pipeline_success': 1,
                    'total_interactions': len(interaction_data),
                    'unique_users': interaction_data['user_id'].nunique(),
                    'unique_items': interaction_data['product_id'].nunique(),
                    'models_trained': len(self.models)
                })
                
                logger.info("Collaborative filtering pipeline completed successfully")
                
                return {
                    'model_path': model_path,
                    'evaluation_results': evaluation_results,
                    'interaction_data_shape': interaction_data.shape,
                    'models_trained': list(self.models.keys()),
                    'best_model': best_model_name
                }
                
            except Exception as e:
                logger.error(f"Collaborative filtering pipeline failed: {str(e)}")
                mlflow.log_metric("pipeline_success", 0)
                raise

def main():
    """Main function to run collaborative filtering engine"""
    config = RecommendationConfig()
    cf_engine = CollaborativeFilteringEngine(config)
    
    try:
        results = cf_engine.run_complete_pipeline()
        print(f"Collaborative filtering pipeline completed successfully!")
        print(f"Model path: {results['model_path']}")
        print(f"Interaction data shape: {results['interaction_data_shape']}")
        print(f"Models trained: {', '.join(results['models_trained'])}")
        print(f"Best model: {results['best_model']}")
        
        if results['evaluation_results']:
            print(f"\nModel Performance:")
            for model_name, metrics in results['evaluation_results'].items():
                rmse = metrics.get('rmse', 'N/A')
                mae = metrics.get('mae', 'N/A')
                print(f"- {model_name}: RMSE={rmse}, MAE={mae}")
        
    except Exception as e:
        logger.error(f"Collaborative filtering engine failed: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()