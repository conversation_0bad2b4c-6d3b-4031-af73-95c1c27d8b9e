#!/usr/bin/env python3
"""
Content-Based Recommendation Engine for E-commerce Analytics
Advanced content-based filtering using product features, TF-IDF, and deep learning
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import os
import sys
from typing import Dict, List, Tuple, Optional, Any, Union
import joblib
import json
from dataclasses import dataclass
import warnings
from collections import defaultdict

# Text processing and feature extraction
from sklearn.feature_extraction.text import TfidfVectorizer, CountVectorizer
from sklearn.metrics.pairwise import cosine_similarity, euclidean_distances
from sklearn.preprocessing import StandardScaler, MinMaxScaler, LabelEncoder
from sklearn.decomposition import TruncatedSVD, LatentDirichletAllocation
from sklearn.cluster import KMeans
import nltk
from nltk.corpus import stopwords
from nltk.tokenize import word_tokenize
from nltk.stem import PorterStemmer, WordNetLemmatizer
import spacy

# Deep learning for content features
import tensorflow as tf
from tensorflow.keras.models import Model, Sequential
from tensorflow.keras.layers import Input, Embedding, Dense, Concatenate, Dropout, BatchNormalization
from tensorflow.keras.layers import Conv1D, GlobalMaxPooling1D, LSTM, Bidirectional
from tensorflow.keras.optimizers import Adam
from sentence_transformers import SentenceTransformer

# Computer vision for image features
import cv2
from tensorflow.keras.applications import ResNet50, VGG16
from tensorflow.keras.applications.resnet50 import preprocess_input
from PIL import Image

# Visualization
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots

# MLflow integration
import mlflow
import mlflow.sklearn
import mlflow.tensorflow
from mlflow.tracking import MlflowClient

# Database and cloud
import boto3
from sqlalchemy import create_engine

warnings.filterwarnings('ignore')

# Configuration
@dataclass
class ContentBasedConfig:
    # Feature extraction parameters
    max_features_tfidf: int = 10000
    ngram_range: Tuple[int, int] = (1, 3)
    min_df: int = 2
    max_df: float = 0.95
    
    # Text processing
    language: str = 'english'
    remove_stopwords: bool = True
    use_stemming: bool = True
    use_lemmatization: bool = True
    
    # Image processing
    image_feature_dim: int = 2048
    image_resize_shape: Tuple[int, int] = (224, 224)
    use_pretrained_cnn: str = 'resnet50'  # resnet50, vgg16, or custom
    
    # Similarity metrics
    similarity_metric: str = 'cosine'  # cosine, euclidean, jaccard
    use_weighted_features: bool = True
    category_weight: float = 0.3
    price_weight: float = 0.2
    brand_weight: float = 0.2
    description_weight: float = 0.3
    
    # Recommendation parameters
    n_recommendations: int = 10
    min_similarity_threshold: float = 0.1
    diversity_factor: float = 0.15
    
    # Model parameters
    embedding_dim: int = 128
    use_deep_features: bool = True
    train_embeddings: bool = True
    
    # Cold start handling
    fallback_to_popular: bool = True
    use_demographic_fallback: bool = True
    
    # MLflow configuration
    mlflow_tracking_uri: str = os.getenv('MLFLOW_TRACKING_URI', 'http://mlflow-server.mlflow:5000')
    experiment_name: str = 'content-based-recommendations'
    
    # Database connections
    db_host: str = os.getenv('DB_HOST', 'analytics-db.cluster-xyz.us-east-1.rds.amazonaws.com')
    db_name: str = os.getenv('DB_NAME', 'ecommerce_analytics')
    db_user: str = os.getenv('DB_USER', 'analytics_user')
    db_password: str = os.getenv('DB_PASSWORD', 'analytics_password')
    
    # S3 configuration
    s3_bucket: str = os.getenv('S3_BUCKET', 'ecommerce-analytics-models')

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ProductFeatureExtractor:
    """Extract and process product features for content-based recommendations"""
    
    def __init__(self, config: ContentBasedConfig):
        self.config = config
        self.tfidf_vectorizer = None
        self.category_encoder = None
        self.brand_encoder = None
        self.scaler = None
        self.sentence_transformer = None
        self.cnn_model = None
        
    def initialize_models(self):
        """Initialize text processing and image processing models"""
        try:
            # Initialize TF-IDF vectorizer
            self.tfidf_vectorizer = TfidfVectorizer(
                max_features=self.config.max_features_tfidf,
                ngram_range=self.config.ngram_range,
                min_df=self.config.min_df,
                max_df=self.config.max_df,
                stop_words=self.config.language if self.config.remove_stopwords else None
            )
            
            # Initialize sentence transformer for semantic embeddings
            self.sentence_transformer = SentenceTransformer('all-MiniLM-L6-v2')
            
            # Initialize CNN for image features
            if self.config.use_pretrained_cnn == 'resnet50':
                base_model = ResNet50(weights='imagenet', include_top=False, pooling='avg')
                self.cnn_model = Model(inputs=base_model.input, outputs=base_model.output)
            elif self.config.use_pretrained_cnn == 'vgg16':
                base_model = VGG16(weights='imagenet', include_top=False, pooling='avg')
                self.cnn_model = Model(inputs=base_model.input, outputs=base_model.output)
            
            # Initialize encoders and scalers
            self.category_encoder = LabelEncoder()
            self.brand_encoder = LabelEncoder()
            self.scaler = StandardScaler()
            
            logger.info("Feature extraction models initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing models: {str(e)}")
            raise
    
    def preprocess_text(self, text: str) -> str:
        """Preprocess text data for feature extraction"""
        if pd.isna(text) or text == '':
            return ''
        
        # Convert to lowercase
        text = text.lower()
        
        # Remove special characters and digits
        import re
        text = re.sub(r'[^a-zA-Z\s]', '', text)
        
        # Tokenize
        tokens = word_tokenize(text)
        
        # Remove stopwords
        if self.config.remove_stopwords:
            stop_words = set(stopwords.words(self.config.language))
            tokens = [token for token in tokens if token not in stop_words]
        
        # Stemming or lemmatization
        if self.config.use_stemming:
            stemmer = PorterStemmer()
            tokens = [stemmer.stem(token) for token in tokens]
        elif self.config.use_lemmatization:
            lemmatizer = WordNetLemmatizer()
            tokens = [lemmatizer.lemmatize(token) for token in tokens]
        
        return ' '.join(tokens)
    
    def extract_text_features(self, products_df: pd.DataFrame) -> np.ndarray:
        """Extract TF-IDF features from product descriptions"""
        try:
            # Combine text fields
            text_features = products_df.apply(lambda row: 
                f"{row.get('name', '')} {row.get('description', '')} {row.get('tags', '')}", 
                axis=1
            )
            
            # Preprocess text
            processed_text = text_features.apply(self.preprocess_text)
            
            # Extract TF-IDF features
            tfidf_features = self.tfidf_vectorizer.fit_transform(processed_text)
            
            logger.info(f"Extracted TF-IDF features shape: {tfidf_features.shape}")
            return tfidf_features.toarray()
            
        except Exception as e:
            logger.error(f"Error extracting text features: {str(e)}")
            raise
    
    def extract_semantic_features(self, products_df: pd.DataFrame) -> np.ndarray:
        """Extract semantic embeddings using sentence transformers"""
        try:
            # Combine text fields
            text_features = products_df.apply(lambda row: 
                f"{row.get('name', '')} {row.get('description', '')}", 
                axis=1
            ).fillna('')
            
            # Get semantic embeddings
            embeddings = self.sentence_transformer.encode(text_features.tolist())
            
            logger.info(f"Extracted semantic features shape: {embeddings.shape}")
            return embeddings
            
        except Exception as e:
            logger.error(f"Error extracting semantic features: {str(e)}")
            raise
    
    def extract_categorical_features(self, products_df: pd.DataFrame) -> np.ndarray:
        """Extract encoded categorical features"""
        try:
            categorical_features = []
            
            # Encode categories
            if 'category' in products_df.columns:
                encoded_categories = self.category_encoder.fit_transform(
                    products_df['category'].fillna('unknown')
                )
                categorical_features.append(encoded_categories.reshape(-1, 1))
            
            # Encode brands
            if 'brand' in products_df.columns:
                encoded_brands = self.brand_encoder.fit_transform(
                    products_df['brand'].fillna('unknown')
                )
                categorical_features.append(encoded_brands.reshape(-1, 1))
            
            if categorical_features:
                features = np.hstack(categorical_features)
                logger.info(f"Extracted categorical features shape: {features.shape}")
                return features
            else:
                return np.array([]).reshape(len(products_df), 0)
                
        except Exception as e:
            logger.error(f"Error extracting categorical features: {str(e)}")
            raise
    
    def extract_numerical_features(self, products_df: pd.DataFrame) -> np.ndarray:
        """Extract and scale numerical features"""
        try:
            numerical_cols = ['price', 'rating', 'review_count', 'discount_percentage']
            available_cols = [col for col in numerical_cols if col in products_df.columns]
            
            if available_cols:
                numerical_features = products_df[available_cols].fillna(0)
                scaled_features = self.scaler.fit_transform(numerical_features)
                
                logger.info(f"Extracted numerical features shape: {scaled_features.shape}")
                return scaled_features
            else:
                return np.array([]).reshape(len(products_df), 0)
                
        except Exception as e:
            logger.error(f"Error extracting numerical features: {str(e)}")
            raise
    
    def extract_image_features(self, image_paths: List[str]) -> np.ndarray:
        """Extract CNN features from product images"""
        try:
            if not self.cnn_model:
                logger.warning("CNN model not initialized, skipping image features")
                return np.array([]).reshape(len(image_paths), 0)
            
            features_list = []
            
            for image_path in image_paths:
                try:
                    if pd.isna(image_path) or not os.path.exists(image_path):
                        # Use zero vector for missing images
                        features_list.append(np.zeros(self.config.image_feature_dim))
                        continue
                    
                    # Load and preprocess image
                    image = Image.open(image_path).convert('RGB')
                    image = image.resize(self.config.image_resize_shape)
                    image_array = np.array(image)
                    image_array = preprocess_input(image_array)
                    image_array = np.expand_dims(image_array, axis=0)
                    
                    # Extract features
                    features = self.cnn_model.predict(image_array, verbose=0)
                    features_list.append(features.flatten())
                    
                except Exception as e:
                    logger.warning(f"Error processing image {image_path}: {str(e)}")
                    features_list.append(np.zeros(self.config.image_feature_dim))
            
            image_features = np.vstack(features_list)
            logger.info(f"Extracted image features shape: {image_features.shape}")
            return image_features
            
        except Exception as e:
            logger.error(f"Error extracting image features: {str(e)}")
            raise

class ContentBasedRecommendationEngine:
    """Main content-based recommendation engine"""
    
    def __init__(self, config: ContentBasedConfig):
        self.config = config
        self.feature_extractor = ProductFeatureExtractor(config)
        self.product_features = None
        self.products_df = None
        self.similarity_matrix = None
        
        # MLflow setup
        mlflow.set_tracking_uri(self.config.mlflow_tracking_uri)
        mlflow.set_experiment(self.config.experiment_name)
    
    def load_data(self) -> pd.DataFrame:
        """Load product data from database"""
        try:
            engine = create_engine(
                f"postgresql://{self.config.db_user}:{self.config.db_password}@"
                f"{self.config.db_host}/{self.config.db_name}"
            )
            
            query = """
            SELECT 
                p.product_id,
                p.name,
                p.description,
                p.category,
                p.brand,
                p.price,
                p.image_url,
                p.tags,
                COALESCE(pr.avg_rating, 0) as rating,
                COALESCE(pr.review_count, 0) as review_count,
                COALESCE(p.discount_percentage, 0) as discount_percentage,
                p.created_at,
                p.updated_at
            FROM products p
            LEFT JOIN (
                SELECT 
                    product_id,
                    AVG(rating) as avg_rating,
                    COUNT(*) as review_count
                FROM product_reviews
                GROUP BY product_id
            ) pr ON p.product_id = pr.product_id
            WHERE p.active = true
            """
            
            products_df = pd.read_sql(query, engine)
            logger.info(f"Loaded {len(products_df)} products from database")
            
            return products_df
            
        except Exception as e:
            logger.error(f"Error loading data: {str(e)}")
            raise
    
    def build_product_features(self, products_df: pd.DataFrame) -> np.ndarray:
        """Build comprehensive product feature matrix"""
        try:
            with mlflow.start_run(run_name="content_features_extraction"):
                # Initialize feature extractor
                self.feature_extractor.initialize_models()
                
                # Extract different types of features
                tfidf_features = self.feature_extractor.extract_text_features(products_df)
                semantic_features = self.feature_extractor.extract_semantic_features(products_df)
                categorical_features = self.feature_extractor.extract_categorical_features(products_df)
                numerical_features = self.feature_extractor.extract_numerical_features(products_df)
                
                # Extract image features if available
                image_features = np.array([]).reshape(len(products_df), 0)
                if 'image_url' in products_df.columns:
                    # Note: In production, you'd download and process actual images
                    # For now, we'll simulate with random features
                    image_features = np.random.rand(len(products_df), self.config.image_feature_dim)
                
                # Combine all features
                feature_list = [tfidf_features, semantic_features, categorical_features, 
                               numerical_features, image_features]
                
                # Remove empty feature arrays
                feature_list = [f for f in feature_list if f.shape[1] > 0]
                
                if feature_list:
                    combined_features = np.hstack(feature_list)
                else:
                    raise ValueError("No features could be extracted")
                
                # Log feature information
                mlflow.log_param("tfidf_features_dim", tfidf_features.shape[1])
                mlflow.log_param("semantic_features_dim", semantic_features.shape[1])
                mlflow.log_param("categorical_features_dim", categorical_features.shape[1])
                mlflow.log_param("numerical_features_dim", numerical_features.shape[1])
                mlflow.log_param("image_features_dim", image_features.shape[1])
                mlflow.log_param("total_features_dim", combined_features.shape[1])
                
                logger.info(f"Built product feature matrix shape: {combined_features.shape}")
                
                return combined_features
                
        except Exception as e:
            logger.error(f"Error building product features: {str(e)}")
            raise
    
    def compute_similarity_matrix(self, features: np.ndarray) -> np.ndarray:
        """Compute similarity matrix between products"""
        try:
            if self.config.similarity_metric == 'cosine':
                similarity_matrix = cosine_similarity(features)
            elif self.config.similarity_metric == 'euclidean':
                # Convert distances to similarities
                distances = euclidean_distances(features)
                similarity_matrix = 1 / (1 + distances)
            else:
                raise ValueError(f"Unsupported similarity metric: {self.config.similarity_metric}")
            
            # Set diagonal to 0 (don't recommend item to itself)
            np.fill_diagonal(similarity_matrix, 0)
            
            logger.info(f"Computed similarity matrix shape: {similarity_matrix.shape}")
            return similarity_matrix
            
        except Exception as e:
            logger.error(f"Error computing similarity matrix: {str(e)}")
            raise
    
    def get_content_recommendations(self, product_id: str, n_recommendations: int = None) -> List[Dict]:
        """Get content-based recommendations for a product"""
        try:
            if n_recommendations is None:
                n_recommendations = self.config.n_recommendations
            
            # Find product index
            product_idx = self.products_df[
                self.products_df['product_id'] == product_id
            ].index
            
            if len(product_idx) == 0:
                logger.warning(f"Product {product_id} not found")
                return []
            
            product_idx = product_idx[0]
            
            # Get similarity scores
            similarity_scores = self.similarity_matrix[product_idx]
            
            # Filter by minimum threshold
            valid_indices = np.where(
                similarity_scores >= self.config.min_similarity_threshold
            )[0]
            
            # Sort by similarity
            sorted_indices = valid_indices[np.argsort(similarity_scores[valid_indices])[::-1]]
            
            # Apply diversity if configured
            if self.config.diversity_factor > 0:
                sorted_indices = self._apply_diversity_filter(
                    sorted_indices, similarity_scores, product_idx
                )
            
            # Get top recommendations
            top_indices = sorted_indices[:n_recommendations]
            
            # Build recommendation results
            recommendations = []
            for idx in top_indices:
                product_row = self.products_df.iloc[idx]
                recommendations.append({
                    'product_id': product_row['product_id'],
                    'name': product_row['name'],
                    'category': product_row['category'],
                    'brand': product_row['brand'],
                    'price': product_row['price'],
                    'rating': product_row['rating'],
                    'similarity_score': float(similarity_scores[idx]),
                    'recommendation_reason': self._generate_recommendation_reason(
                        product_idx, idx
                    )
                })
            
            logger.info(f"Generated {len(recommendations)} recommendations for product {product_id}")
            return recommendations
            
        except Exception as e:
            logger.error(f"Error getting recommendations: {str(e)}")
            return []
    
    def get_user_recommendations(self, user_id: str, n_recommendations: int = None) -> List[Dict]:
        """Get content-based recommendations for a user based on their purchase history"""
        try:
            if n_recommendations is None:
                n_recommendations = self.config.n_recommendations
            
            # Get user's purchase history
            user_products = self._get_user_purchase_history(user_id)
            
            if not user_products:
                # Cold start: return popular products
                return self._get_popular_recommendations(n_recommendations)
            
            # Get recommendations for each purchased product
            all_recommendations = {}
            for product_id in user_products:
                product_recs = self.get_content_recommendations(product_id, n_recommendations * 2)
                for rec in product_recs:
                    rec_id = rec['product_id']
                    if rec_id not in user_products:  # Don't recommend already purchased items
                        if rec_id not in all_recommendations:
                            all_recommendations[rec_id] = rec
                            all_recommendations[rec_id]['score'] = rec['similarity_score']
                        else:
                            # Aggregate scores from multiple source products
                            all_recommendations[rec_id]['score'] += rec['similarity_score']
            
            # Sort by aggregated scores
            sorted_recommendations = sorted(
                all_recommendations.values(),
                key=lambda x: x['score'],
                reverse=True
            )
            
            return sorted_recommendations[:n_recommendations]
            
        except Exception as e:
            logger.error(f"Error getting user recommendations: {str(e)}")
            return []
    
    def _apply_diversity_filter(self, sorted_indices: np.ndarray, 
                               similarity_scores: np.ndarray, 
                               source_idx: int) -> np.ndarray:
        """Apply diversity filtering to recommendations"""
        try:
            if len(sorted_indices) <= 1:
                return sorted_indices
            
            diverse_indices = [sorted_indices[0]]  # Always include most similar
            
            for idx in sorted_indices[1:]:
                # Check diversity with already selected items
                diversity_scores = []
                for selected_idx in diverse_indices:
                    diversity_score = 1 - self.similarity_matrix[idx, selected_idx]
                    diversity_scores.append(diversity_score)
                
                avg_diversity = np.mean(diversity_scores)
                
                # Combined score: similarity + diversity
                combined_score = (
                    (1 - self.config.diversity_factor) * similarity_scores[idx] +
                    self.config.diversity_factor * avg_diversity
                )
                
                # Add if meets threshold
                if combined_score >= self.config.min_similarity_threshold:
                    diverse_indices.append(idx)
            
            return np.array(diverse_indices)
            
        except Exception as e:
            logger.error(f"Error applying diversity filter: {str(e)}")
            return sorted_indices
    
    def _generate_recommendation_reason(self, source_idx: int, target_idx: int) -> str:
        """Generate explanation for why this product was recommended"""
        source_product = self.products_df.iloc[source_idx]
        target_product = self.products_df.iloc[target_idx]
        
        reasons = []
        
        # Check category match
        if source_product['category'] == target_product['category']:
            reasons.append(f"same category ({source_product['category']})")
        
        # Check brand match
        if source_product['brand'] == target_product['brand']:
            reasons.append(f"same brand ({source_product['brand']})")
        
        # Check price similarity
        price_diff = abs(source_product['price'] - target_product['price'])
        if price_diff / source_product['price'] < 0.2:  # Within 20%
            reasons.append("similar price range")
        
        if not reasons:
            reasons.append("similar product features")
        
        return "Recommended because: " + ", ".join(reasons)
    
    def _get_user_purchase_history(self, user_id: str) -> List[str]:
        """Get user's purchase history"""
        try:
            engine = create_engine(
                f"postgresql://{self.config.db_user}:{self.config.db_password}@"
                f"{self.config.db_host}/{self.config.db_name}"
            )
            
            query = """
            SELECT DISTINCT product_id
            FROM order_items oi
            JOIN orders o ON oi.order_id = o.order_id
            WHERE o.customer_id = %s
            AND o.order_status = 'completed'
            ORDER BY o.order_date DESC
            LIMIT 20
            """
            
            result = pd.read_sql(query, engine, params=[user_id])
            return result['product_id'].tolist()
            
        except Exception as e:
            logger.error(f"Error getting user purchase history: {str(e)}")
            return []
    
    def _get_popular_recommendations(self, n_recommendations: int) -> List[Dict]:
        """Get popular products as fallback recommendations"""
        try:
            # Sort by rating and review count
            popular_products = self.products_df.nlargest(
                n_recommendations, 
                ['rating', 'review_count']
            )
            
            recommendations = []
            for _, product in popular_products.iterrows():
                recommendations.append({
                    'product_id': product['product_id'],
                    'name': product['name'],
                    'category': product['category'],
                    'brand': product['brand'],
                    'price': product['price'],
                    'rating': product['rating'],
                    'similarity_score': 0.0,
                    'recommendation_reason': "Popular product (cold start recommendation)"
                })
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Error getting popular recommendations: {str(e)}")
            return []
    
    def train_and_save_model(self) -> str:
        """Train the content-based recommendation model and save artifacts"""
        try:
            with mlflow.start_run(run_name="content_based_training"):
                # Load data
                self.products_df = self.load_data()
                
                # Build features
                self.product_features = self.build_product_features(self.products_df)
                
                # Compute similarity matrix
                self.similarity_matrix = self.compute_similarity_matrix(self.product_features)
                
                # Log model parameters
                mlflow.log_param("n_products", len(self.products_df))
                mlflow.log_param("feature_dim", self.product_features.shape[1])
                mlflow.log_param("similarity_metric", self.config.similarity_metric)
                mlflow.log_param("min_similarity_threshold", self.config.min_similarity_threshold)
                
                # Save artifacts
                artifacts_dir = "/tmp/content_based_model"
                os.makedirs(artifacts_dir, exist_ok=True)
                
                # Save feature extractor
                joblib.dump(
                    self.feature_extractor, 
                    f"{artifacts_dir}/feature_extractor.pkl"
                )
                
                # Save similarity matrix
                np.save(f"{artifacts_dir}/similarity_matrix.npy", self.similarity_matrix)
                
                # Save product data
                self.products_df.to_parquet(f"{artifacts_dir}/products_data.parquet")
                
                # Log artifacts
                mlflow.log_artifacts(artifacts_dir)
                
                # Log model
                mlflow.sklearn.log_model(
                    self.feature_extractor.tfidf_vectorizer,
                    "tfidf_vectorizer"
                )
                
                run_id = mlflow.active_run().info.run_id
                logger.info(f"Content-based model training completed. Run ID: {run_id}")
                
                return run_id
                
        except Exception as e:
            logger.error(f"Error training model: {str(e)}")
            raise
    
    def evaluate_model(self) -> Dict[str, float]:
        """Evaluate the content-based recommendation model"""
        try:
            with mlflow.start_run(run_name="content_based_evaluation"):
                # Test recommendation quality
                sample_products = self.products_df.sample(min(100, len(self.products_df)))
                
                total_recommendations = 0
                valid_recommendations = 0
                avg_similarity_scores = []
                category_match_rate = 0
                
                for _, product in sample_products.iterrows():
                    recommendations = self.get_content_recommendations(
                        product['product_id'], 10
                    )
                    
                    if recommendations:
                        total_recommendations += len(recommendations)
                        valid_recommendations += len([r for r in recommendations 
                                                    if r['similarity_score'] >= self.config.min_similarity_threshold])
                        avg_similarity_scores.extend([r['similarity_score'] for r in recommendations])
                        
                        # Check category match rate
                        category_matches = sum(1 for r in recommendations 
                                             if r['category'] == product['category'])
                        category_match_rate += category_matches / len(recommendations)
                
                # Calculate metrics
                metrics = {
                    'avg_recommendations_per_product': total_recommendations / len(sample_products),
                    'valid_recommendation_rate': valid_recommendations / total_recommendations if total_recommendations > 0 else 0,
                    'avg_similarity_score': np.mean(avg_similarity_scores) if avg_similarity_scores else 0,
                    'category_match_rate': category_match_rate / len(sample_products),
                    'feature_diversity': self._calculate_feature_diversity()
                }
                
                # Log metrics
                for metric_name, metric_value in metrics.items():
                    mlflow.log_metric(metric_name, metric_value)
                
                logger.info(f"Model evaluation completed: {metrics}")
                return metrics
                
        except Exception as e:
            logger.error(f"Error evaluating model: {str(e)}")
            return {}
    
    def _calculate_feature_diversity(self) -> float:
        """Calculate diversity of feature representations"""
        try:
            # Calculate variance across feature dimensions
            feature_variance = np.var(self.product_features, axis=0)
            return float(np.mean(feature_variance))
        except:
            return 0.0

def main():
    """Main execution function"""
    config = ContentBasedConfig()
    
    # Initialize recommendation engine
    engine = ContentBasedRecommendationEngine(config)
    
    # Train model
    run_id = engine.train_and_save_model()
    
    # Evaluate model
    metrics = engine.evaluate_model()
    
    # Test recommendations
    sample_product_id = engine.products_df.iloc[0]['product_id']
    recommendations = engine.get_content_recommendations(sample_product_id)
    
    print(f"Sample recommendations for product {sample_product_id}:")
    for i, rec in enumerate(recommendations[:5], 1):
        print(f"{i}. {rec['name']} (similarity: {rec['similarity_score']:.3f})")
    
    logger.info("Content-based recommendation engine setup completed successfully")

if __name__ == "__main__":
    main()