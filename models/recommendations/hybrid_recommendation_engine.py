#!/usr/bin/env python3
"""
Hybrid Recommendation Engine for E-commerce Analytics
Combines collaborative filtering, content-based, and additional approaches for optimal recommendations
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import os
import sys
from typing import Dict, List, Tuple, Optional, Any, Union
import joblib
import json
from dataclasses import dataclass
import warnings
from collections import defaultdict
import asyncio
import aiohttp

# Import our recommendation engines
from collaborative_filtering_engine import CollaborativeFilteringEngine, RecommendationConfig as CFConfig
from content_based_engine import ContentBasedRecommendationEngine, ContentBasedConfig as CBConfig

# Machine learning libraries
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression, Ridge
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import xgboost as xgb

# Deep learning
import tensorflow as tf
from tensorflow.keras.models import Model, Sequential
from tensorflow.keras.layers import Input, Dense, Concatenate, Dropout, BatchNormalization
from tensorflow.keras.optimizers import Adam

# Visualization
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots

# MLflow integration
import mlflow
import mlflow.sklearn
import mlflow.tensorflow
from mlflow.tracking import MlflowClient

# Database and cloud
import boto3
from sqlalchemy import create_engine

# Real-time recommendations
import redis
from kafka import KafkaProducer, KafkaConsumer

warnings.filterwarnings('ignore')

# Configuration
@dataclass
class HybridRecommendationConfig:
    # Hybrid approach weights
    collaborative_weight: float = 0.4
    content_based_weight: float = 0.3
    popularity_weight: float = 0.15
    contextual_weight: float = 0.15
    
    # Model parameters
    n_recommendations: int = 10
    min_confidence_threshold: float = 0.1
    diversity_factor: float = 0.2
    novelty_factor: float = 0.1
    
    # Business rules
    max_same_category_ratio: float = 0.5
    max_same_brand_ratio: float = 0.3
    price_range_factor: float = 0.2
    
    # Context parameters
    use_time_context: bool = True
    use_location_context: bool = True
    use_device_context: bool = True
    use_seasonal_context: bool = True
    
    # Real-time parameters
    enable_real_time_updates: bool = True
    cache_expiry_hours: int = 24
    real_time_weight: float = 0.1
    
    # Cold start handling
    new_user_threshold_days: int = 7
    new_item_threshold_days: int = 30
    min_interactions_for_cf: int = 5
    
    # A/B testing
    enable_ab_testing: bool = True
    default_treatment_percentage: float = 0.1
    
    # Performance parameters
    max_computation_time_seconds: float = 2.0
    enable_precomputed_recommendations: bool = True
    batch_update_frequency_hours: int = 6
    
    # MLflow configuration
    mlflow_tracking_uri: str = os.getenv('MLFLOW_TRACKING_URI', 'http://mlflow-server.mlflow:5000')
    experiment_name: str = 'hybrid-recommendations'
    
    # Database connections
    db_host: str = os.getenv('DB_HOST', 'analytics-db.cluster-xyz.us-east-1.rds.amazonaws.com')
    db_name: str = os.getenv('DB_NAME', 'ecommerce_analytics')
    db_user: str = os.getenv('DB_USER', 'analytics_user')
    db_password: str = os.getenv('DB_PASSWORD', 'analytics_password')
    
    # Redis configuration
    redis_host: str = os.getenv('REDIS_HOST', 'redis-cluster')
    redis_port: int = int(os.getenv('REDIS_PORT', '6379'))
    redis_db: int = int(os.getenv('REDIS_DB', '0'))
    
    # Kafka configuration
    kafka_bootstrap_servers: str = os.getenv('KAFKA_BOOTSTRAP_SERVERS', 'kafka-cluster:9092')
    kafka_topic_interactions: str = 'user-interactions'
    kafka_topic_recommendations: str = 'recommendation-requests'

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ContextualFeatureExtractor:
    """Extract contextual features for recommendations"""
    
    def __init__(self, config: HybridRecommendationConfig):
        self.config = config
    
    def extract_temporal_features(self, timestamp: datetime) -> Dict[str, float]:
        """Extract time-based contextual features"""
        features = {}
        
        # Hour of day
        features['hour_of_day'] = timestamp.hour / 24.0
        features['is_weekend'] = 1.0 if timestamp.weekday() >= 5 else 0.0
        features['is_evening'] = 1.0 if 17 <= timestamp.hour <= 22 else 0.0
        features['is_morning'] = 1.0 if 6 <= timestamp.hour <= 11 else 0.0
        
        # Season
        month = timestamp.month
        if month in [12, 1, 2]:
            features['season_winter'] = 1.0
        elif month in [3, 4, 5]:
            features['season_spring'] = 1.0
        elif month in [6, 7, 8]:
            features['season_summer'] = 1.0
        else:
            features['season_fall'] = 1.0
        
        # Holiday proximity (simplified)
        features['near_christmas'] = 1.0 if month == 12 else 0.0
        features['near_valentine'] = 1.0 if month == 2 else 0.0
        
        return features
    
    def extract_user_context(self, user_id: str, session_data: Dict) -> Dict[str, float]:
        """Extract user contextual features"""
        features = {}
        
        # Device type
        device = session_data.get('device', 'desktop').lower()
        features['is_mobile'] = 1.0 if device == 'mobile' else 0.0
        features['is_tablet'] = 1.0 if device == 'tablet' else 0.0
        features['is_desktop'] = 1.0 if device == 'desktop' else 0.0
        
        # Session features
        features['session_duration'] = min(session_data.get('duration_minutes', 0) / 60.0, 2.0)
        features['pages_viewed'] = min(session_data.get('pages_viewed', 0) / 10.0, 1.0)
        features['items_viewed'] = min(session_data.get('items_viewed', 0) / 20.0, 1.0)
        
        # User location (if available)
        location = session_data.get('location', {})
        features['is_urban'] = 1.0 if location.get('area_type') == 'urban' else 0.0
        
        return features
    
    def extract_business_context(self, current_time: datetime) -> Dict[str, float]:
        """Extract business contextual features"""
        features = {}
        
        # Inventory and promotional context
        features['is_sale_period'] = 0.0  # Would be set based on current promotions
        features['inventory_pressure'] = 0.0  # Based on inventory levels
        
        # Business objectives
        features['promote_new_products'] = 0.2  # Slight bias toward new products
        features['clear_old_inventory'] = 0.1   # Slight bias toward older inventory
        
        return features

class RecommendationBlender:
    """Blend recommendations from multiple sources"""
    
    def __init__(self, config: HybridRecommendationConfig):
        self.config = config
        self.scaler = StandardScaler()
        self.blending_model = None
    
    def train_blending_model(self, training_data: pd.DataFrame):
        """Train a model to optimally blend recommendation scores"""
        try:
            # Features: scores from different algorithms + contextual features
            feature_columns = [
                'cf_score', 'cb_score', 'popularity_score',
                'contextual_score', 'user_similarity', 'item_popularity'
            ]
            
            X = training_data[feature_columns].fillna(0)
            y = training_data['actual_interaction']  # 1 if user interacted, 0 otherwise
            
            # Scale features
            X_scaled = self.scaler.fit_transform(X)
            
            # Train XGBoost blending model
            self.blending_model = xgb.XGBClassifier(
                n_estimators=100,
                max_depth=6,
                learning_rate=0.1,
                subsample=0.8,
                colsample_bytree=0.8,
                random_state=42
            )
            
            self.blending_model.fit(X_scaled, y)
            
            # Evaluate model
            cv_scores = cross_val_score(self.blending_model, X_scaled, y, cv=5)
            logger.info(f"Blending model CV accuracy: {cv_scores.mean():.3f} ± {cv_scores.std():.3f}")
            
        except Exception as e:
            logger.error(f"Error training blending model: {str(e)}")
            # Fallback to simple weighted average
            self.blending_model = None
    
    def blend_recommendations(self, recommendation_sources: Dict[str, List[Dict]],
                            contextual_features: Dict[str, float]) -> List[Dict]:
        """Blend recommendations from multiple sources"""
        try:
            # Collect all unique recommendations
            all_recommendations = {}
            
            # Process collaborative filtering recommendations
            cf_recs = recommendation_sources.get('collaborative_filtering', [])
            for rec in cf_recs:
                product_id = rec['product_id']
                if product_id not in all_recommendations:
                    all_recommendations[product_id] = rec.copy()
                    all_recommendations[product_id]['cf_score'] = rec.get('similarity_score', 0)
                    all_recommendations[product_id]['cb_score'] = 0
                    all_recommendations[product_id]['popularity_score'] = 0
                else:
                    all_recommendations[product_id]['cf_score'] = rec.get('similarity_score', 0)
            
            # Process content-based recommendations
            cb_recs = recommendation_sources.get('content_based', [])
            for rec in cb_recs:
                product_id = rec['product_id']
                if product_id not in all_recommendations:
                    all_recommendations[product_id] = rec.copy()
                    all_recommendations[product_id]['cf_score'] = 0
                    all_recommendations[product_id]['cb_score'] = rec.get('similarity_score', 0)
                    all_recommendations[product_id]['popularity_score'] = 0
                else:
                    all_recommendations[product_id]['cb_score'] = rec.get('similarity_score', 0)
            
            # Process popularity-based recommendations
            pop_recs = recommendation_sources.get('popularity', [])
            for rec in pop_recs:
                product_id = rec['product_id']
                if product_id not in all_recommendations:
                    all_recommendations[product_id] = rec.copy()
                    all_recommendations[product_id]['cf_score'] = 0
                    all_recommendations[product_id]['cb_score'] = 0
                    all_recommendations[product_id]['popularity_score'] = rec.get('popularity_score', 0)
                else:
                    all_recommendations[product_id]['popularity_score'] = rec.get('popularity_score', 0)
            
            # Calculate blended scores
            for product_id, rec in all_recommendations.items():
                if self.blending_model is not None:
                    # Use trained blending model
                    features = np.array([[
                        rec.get('cf_score', 0),
                        rec.get('cb_score', 0),
                        rec.get('popularity_score', 0),
                        sum(contextual_features.values()) / len(contextual_features),
                        rec.get('user_similarity', 0),
                        rec.get('item_popularity', 0)
                    ]])
                    
                    features_scaled = self.scaler.transform(features)
                    blended_score = self.blending_model.predict_proba(features_scaled)[0][1]
                else:
                    # Simple weighted average
                    blended_score = (
                        self.config.collaborative_weight * rec.get('cf_score', 0) +
                        self.config.content_based_weight * rec.get('cb_score', 0) +
                        self.config.popularity_weight * rec.get('popularity_score', 0) +
                        self.config.contextual_weight * sum(contextual_features.values()) / len(contextual_features)
                    )
                
                rec['blended_score'] = blended_score
                rec['recommendation_source'] = self._determine_primary_source(rec)
            
            # Sort by blended score
            sorted_recommendations = sorted(
                all_recommendations.values(),
                key=lambda x: x['blended_score'],
                reverse=True
            )
            
            # Apply diversity and business rules
            final_recommendations = self._apply_business_rules(sorted_recommendations)
            
            return final_recommendations[:self.config.n_recommendations]
            
        except Exception as e:
            logger.error(f"Error blending recommendations: {str(e)}")
            return []
    
    def _determine_primary_source(self, recommendation: Dict) -> str:
        """Determine which algorithm contributed most to this recommendation"""
        scores = {
            'collaborative_filtering': recommendation.get('cf_score', 0),
            'content_based': recommendation.get('cb_score', 0),
            'popularity': recommendation.get('popularity_score', 0)
        }
        return max(scores.items(), key=lambda x: x[1])[0]
    
    def _apply_business_rules(self, recommendations: List[Dict]) -> List[Dict]:
        """Apply business rules to ensure recommendation diversity"""
        try:
            filtered_recs = []
            category_counts = defaultdict(int)
            brand_counts = defaultdict(int)
            total_count = 0
            
            for rec in recommendations:
                if total_count >= self.config.n_recommendations:
                    break
                
                category = rec.get('category', 'unknown')
                brand = rec.get('brand', 'unknown')
                
                # Check category diversity
                max_category_count = int(self.config.n_recommendations * self.config.max_same_category_ratio)
                if category_counts[category] >= max_category_count:
                    continue
                
                # Check brand diversity
                max_brand_count = int(self.config.n_recommendations * self.config.max_same_brand_ratio)
                if brand_counts[brand] >= max_brand_count:
                    continue
                
                # Check minimum confidence
                if rec.get('blended_score', 0) < self.config.min_confidence_threshold:
                    continue
                
                filtered_recs.append(rec)
                category_counts[category] += 1
                brand_counts[brand] += 1
                total_count += 1
            
            return filtered_recs
            
        except Exception as e:
            logger.error(f"Error applying business rules: {str(e)}")
            return recommendations

class HybridRecommendationEngine:
    """Main hybrid recommendation engine"""
    
    def __init__(self, config: HybridRecommendationConfig):
        self.config = config
        
        # Initialize component engines
        cf_config = CFConfig()
        cb_config = CBConfig()
        
        self.cf_engine = CollaborativeFilteringEngine(cf_config)
        self.cb_engine = ContentBasedRecommendationEngine(cb_config)
        
        # Initialize blender and context extractor
        self.blender = RecommendationBlender(config)
        self.context_extractor = ContextualFeatureExtractor(config)
        
        # Initialize caching
        self.redis_client = None
        if config.enable_real_time_updates:
            self._initialize_redis()
        
        # MLflow setup
        mlflow.set_tracking_uri(self.config.mlflow_tracking_uri)
        mlflow.set_experiment(self.config.experiment_name)
    
    def _initialize_redis(self):
        """Initialize Redis connection for caching"""
        try:
            self.redis_client = redis.Redis(
                host=self.config.redis_host,
                port=self.config.redis_port,
                db=self.config.redis_db,
                decode_responses=True
            )
            self.redis_client.ping()
            logger.info("Redis connection established")
        except Exception as e:
            logger.warning(f"Could not connect to Redis: {str(e)}")
            self.redis_client = None
    
    def get_hybrid_recommendations(self, user_id: str, 
                                 context: Dict = None,
                                 n_recommendations: int = None) -> List[Dict]:
        """Get hybrid recommendations for a user"""
        try:
            if n_recommendations is None:
                n_recommendations = self.config.n_recommendations
            
            start_time = datetime.now()
            
            # Check cache first
            if self.redis_client:
                cached_recs = self._get_cached_recommendations(user_id, context)
                if cached_recs:
                    return cached_recs[:n_recommendations]
            
            # Extract contextual features
            current_time = datetime.now()
            session_data = context or {}
            
            temporal_features = self.context_extractor.extract_temporal_features(current_time)
            user_features = self.context_extractor.extract_user_context(user_id, session_data)
            business_features = self.context_extractor.extract_business_context(current_time)
            
            contextual_features = {**temporal_features, **user_features, **business_features}
            
            # Get recommendations from different sources
            recommendation_sources = {}
            
            # Collaborative filtering
            try:
                cf_recs = self.cf_engine.get_user_recommendations(user_id, n_recommendations * 2)
                recommendation_sources['collaborative_filtering'] = cf_recs
            except Exception as e:
                logger.warning(f"CF recommendations failed: {str(e)}")
                recommendation_sources['collaborative_filtering'] = []
            
            # Content-based
            try:
                cb_recs = self.cb_engine.get_user_recommendations(user_id, n_recommendations * 2)
                recommendation_sources['content_based'] = cb_recs
            except Exception as e:
                logger.warning(f"CB recommendations failed: {str(e)}")
                recommendation_sources['content_based'] = []
            
            # Popularity-based (fallback)
            pop_recs = self._get_popularity_recommendations(n_recommendations)
            recommendation_sources['popularity'] = pop_recs
            
            # Blend recommendations
            blended_recs = self.blender.blend_recommendations(
                recommendation_sources, contextual_features
            )
            
            # Add contextual information to recommendations
            for rec in blended_recs:
                rec['context'] = contextual_features
                rec['timestamp'] = current_time.isoformat()
                rec['user_id'] = user_id
            
            # Cache results
            if self.redis_client and blended_recs:
                self._cache_recommendations(user_id, context, blended_recs)
            
            # Log performance
            computation_time = (datetime.now() - start_time).total_seconds()
            logger.info(f"Generated {len(blended_recs)} hybrid recommendations in {computation_time:.3f}s")
            
            # Log to MLflow
            with mlflow.start_run(run_name=f"hybrid_recommendation_{user_id}"):
                mlflow.log_metric("computation_time_seconds", computation_time)
                mlflow.log_metric("num_recommendations", len(blended_recs))
                mlflow.log_metric("cf_recommendations", len(recommendation_sources['collaborative_filtering']))
                mlflow.log_metric("cb_recommendations", len(recommendation_sources['content_based']))
                
                # Log contextual features
                for feature, value in contextual_features.items():
                    mlflow.log_metric(f"context_{feature}", value)
            
            return blended_recs[:n_recommendations]
            
        except Exception as e:
            logger.error(f"Error getting hybrid recommendations: {str(e)}")
            # Fallback to popularity recommendations
            return self._get_popularity_recommendations(n_recommendations)
    
    def get_item_recommendations(self, item_id: str, 
                               user_id: str = None,
                               context: Dict = None,
                               n_recommendations: int = None) -> List[Dict]:
        """Get hybrid recommendations for a specific item"""
        try:
            if n_recommendations is None:
                n_recommendations = self.config.n_recommendations
            
            # Get recommendations from different sources
            recommendation_sources = {}
            
            # Content-based (primary for item recommendations)
            cb_recs = self.cb_engine.get_content_recommendations(item_id, n_recommendations * 2)
            recommendation_sources['content_based'] = cb_recs
            
            # Collaborative filtering (if user provided)
            if user_id:
                cf_recs = self.cf_engine.get_item_recommendations(item_id, n_recommendations)
                recommendation_sources['collaborative_filtering'] = cf_recs
            else:
                recommendation_sources['collaborative_filtering'] = []
            
            # Popularity-based
            pop_recs = self._get_popularity_recommendations(n_recommendations)
            recommendation_sources['popularity'] = pop_recs
            
            # Extract contextual features
            current_time = datetime.now()
            session_data = context or {}
            contextual_features = {}
            
            if user_id:
                temporal_features = self.context_extractor.extract_temporal_features(current_time)
                user_features = self.context_extractor.extract_user_context(user_id, session_data)
                business_features = self.context_extractor.extract_business_context(current_time)
                contextual_features = {**temporal_features, **user_features, **business_features}
            
            # Blend recommendations
            blended_recs = self.blender.blend_recommendations(
                recommendation_sources, contextual_features
            )
            
            return blended_recs[:n_recommendations]
            
        except Exception as e:
            logger.error(f"Error getting item recommendations: {str(e)}")
            return []
    
    def _get_popularity_recommendations(self, n_recommendations: int) -> List[Dict]:
        """Get popularity-based recommendations as fallback"""
        try:
            engine = create_engine(
                f"postgresql://{self.config.db_user}:{self.config.db_password}@"
                f"{self.config.db_host}/{self.config.db_name}"
            )
            
            query = """
            SELECT 
                p.product_id,
                p.name,
                p.category,
                p.brand,
                p.price,
                p.image_url,
                COALESCE(stats.order_count, 0) as order_count,
                COALESCE(stats.revenue, 0) as revenue,
                COALESCE(reviews.avg_rating, 0) as rating,
                COALESCE(reviews.review_count, 0) as review_count
            FROM products p
            LEFT JOIN (
                SELECT 
                    product_id,
                    COUNT(*) as order_count,
                    SUM(quantity * unit_price) as revenue
                FROM order_items
                WHERE created_at >= NOW() - INTERVAL '30 days'
                GROUP BY product_id
            ) stats ON p.product_id = stats.product_id
            LEFT JOIN (
                SELECT 
                    product_id,
                    AVG(rating) as avg_rating,
                    COUNT(*) as review_count
                FROM product_reviews
                GROUP BY product_id
            ) reviews ON p.product_id = reviews.product_id
            WHERE p.active = true
            ORDER BY 
                (COALESCE(stats.order_count, 0) * 0.4 + 
                 COALESCE(reviews.avg_rating, 0) * COALESCE(reviews.review_count, 0) * 0.3 +
                 COALESCE(reviews.review_count, 0) * 0.3) DESC
            LIMIT %s
            """
            
            popular_products = pd.read_sql(query, engine, params=[n_recommendations * 2])
            
            recommendations = []
            for _, product in popular_products.iterrows():
                # Calculate popularity score
                popularity_score = (
                    product['order_count'] * 0.4 +
                    product['rating'] * product['review_count'] * 0.3 +
                    product['review_count'] * 0.3
                ) / 100.0  # Normalize
                
                recommendations.append({
                    'product_id': product['product_id'],
                    'name': product['name'],
                    'category': product['category'],
                    'brand': product['brand'],
                    'price': float(product['price']),
                    'rating': float(product['rating']),
                    'popularity_score': popularity_score,
                    'recommendation_reason': f"Popular product (ordered {product['order_count']} times recently)"
                })
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Error getting popularity recommendations: {str(e)}")
            return []
    
    def _get_cached_recommendations(self, user_id: str, context: Dict) -> Optional[List[Dict]]:
        """Get cached recommendations if available"""
        try:
            if not self.redis_client:
                return None
            
            # Create cache key
            context_hash = hash(str(sorted(context.items()))) if context else 0
            cache_key = f"hybrid_recs:{user_id}:{context_hash}"
            
            cached_data = self.redis_client.get(cache_key)
            if cached_data:
                return json.loads(cached_data)
            
            return None
            
        except Exception as e:
            logger.warning(f"Error getting cached recommendations: {str(e)}")
            return None
    
    def _cache_recommendations(self, user_id: str, context: Dict, recommendations: List[Dict]):
        """Cache recommendations for future use"""
        try:
            if not self.redis_client:
                return
            
            # Create cache key
            context_hash = hash(str(sorted(context.items()))) if context else 0
            cache_key = f"hybrid_recs:{user_id}:{context_hash}"
            
            # Cache for configured hours
            expiry_seconds = self.config.cache_expiry_hours * 3600
            
            self.redis_client.setex(
                cache_key,
                expiry_seconds,
                json.dumps(recommendations, default=str)
            )
            
        except Exception as e:
            logger.warning(f"Error caching recommendations: {str(e)}")
    
    def train_hybrid_model(self) -> str:
        """Train the hybrid recommendation model"""
        try:
            with mlflow.start_run(run_name="hybrid_model_training"):
                # Train component models
                logger.info("Training collaborative filtering model...")
                cf_run_id = self.cf_engine.train_and_save_model()
                
                logger.info("Training content-based model...")
                cb_run_id = self.cb_engine.train_and_save_model()
                
                # Collect training data for blending model
                logger.info("Collecting training data for blending model...")
                training_data = self._collect_blending_training_data()
                
                if len(training_data) > 1000:  # Minimum data requirement
                    logger.info("Training blending model...")
                    self.blender.train_blending_model(training_data)
                
                # Log component model runs
                mlflow.log_param("cf_model_run_id", cf_run_id)
                mlflow.log_param("cb_model_run_id", cb_run_id)
                mlflow.log_param("training_data_size", len(training_data))
                
                run_id = mlflow.active_run().info.run_id
                logger.info(f"Hybrid model training completed. Run ID: {run_id}")
                
                return run_id
                
        except Exception as e:
            logger.error(f"Error training hybrid model: {str(e)}")
            raise
    
    def _collect_blending_training_data(self) -> pd.DataFrame:
        """Collect training data for the blending model"""
        try:
            engine = create_engine(
                f"postgresql://{self.config.db_user}:{self.config.db_password}@"
                f"{self.config.db_host}/{self.config.db_name}"
            )
            
            # Get historical interaction data
            query = """
            SELECT 
                oi.customer_id,
                oi.product_id,
                1 as actual_interaction,
                o.order_date
            FROM order_items oi
            JOIN orders o ON oi.order_id = o.order_id
            WHERE o.order_date >= NOW() - INTERVAL '90 days'
            AND o.order_status = 'completed'
            LIMIT 10000
            """
            
            interactions = pd.read_sql(query, engine)
            
            # For each interaction, get scores from different algorithms
            training_data = []
            
            for _, interaction in interactions.iterrows():
                user_id = interaction['customer_id']
                product_id = interaction['product_id']
                
                # Get CF score (simulate)
                cf_score = np.random.rand()  # In practice, get from actual CF model
                
                # Get CB score (simulate)
                cb_score = np.random.rand()  # In practice, get from actual CB model
                
                # Get popularity score
                popularity_score = np.random.rand()
                
                training_data.append({
                    'user_id': user_id,
                    'product_id': product_id,
                    'cf_score': cf_score,
                    'cb_score': cb_score,
                    'popularity_score': popularity_score,
                    'contextual_score': np.random.rand(),
                    'user_similarity': np.random.rand(),
                    'item_popularity': np.random.rand(),
                    'actual_interaction': 1
                })
            
            # Add negative samples (products not interacted with)
            negative_samples = []
            for _ in range(len(training_data)):
                # Randomly sample user and product that didn't interact
                random_user = interactions['customer_id'].sample(1).iloc[0]
                random_product = interactions['product_id'].sample(1).iloc[0]
                
                negative_samples.append({
                    'user_id': random_user,
                    'product_id': random_product,
                    'cf_score': np.random.rand() * 0.5,  # Lower scores for negatives
                    'cb_score': np.random.rand() * 0.5,
                    'popularity_score': np.random.rand() * 0.5,
                    'contextual_score': np.random.rand() * 0.5,
                    'user_similarity': np.random.rand() * 0.5,
                    'item_popularity': np.random.rand() * 0.5,
                    'actual_interaction': 0
                })
            
            training_data.extend(negative_samples)
            
            return pd.DataFrame(training_data)
            
        except Exception as e:
            logger.error(f"Error collecting training data: {str(e)}")
            return pd.DataFrame()
    
    def evaluate_hybrid_model(self) -> Dict[str, float]:
        """Evaluate the hybrid recommendation model"""
        try:
            with mlflow.start_run(run_name="hybrid_model_evaluation"):
                # Collect test data
                test_users = self._get_test_users(100)
                
                metrics = {
                    'precision_at_5': 0.0,
                    'precision_at_10': 0.0,
                    'recall_at_10': 0.0,
                    'diversity_score': 0.0,
                    'novelty_score': 0.0,
                    'coverage': 0.0
                }
                
                total_users = 0
                all_recommended_items = set()
                total_items = self._get_total_products_count()
                
                for user_id in test_users:
                    try:
                        # Get recommendations
                        recommendations = self.get_hybrid_recommendations(user_id, n_recommendations=10)
                        
                        if recommendations:
                            # Get actual purchases for evaluation
                            actual_purchases = self._get_user_test_purchases(user_id)
                            
                            if actual_purchases:
                                # Calculate precision
                                recommended_items = {rec['product_id'] for rec in recommendations}
                                actual_items = set(actual_purchases)
                                
                                intersection_5 = len(recommended_items.intersection(actual_items))
                                intersection_10 = intersection_5  # Since we only have 10 recommendations
                                
                                metrics['precision_at_5'] += intersection_5 / min(5, len(recommendations))
                                metrics['precision_at_10'] += intersection_10 / len(recommendations)
                                metrics['recall_at_10'] += intersection_10 / len(actual_items) if actual_items else 0
                                
                                # Track diversity
                                categories = {rec['category'] for rec in recommendations}
                                metrics['diversity_score'] += len(categories) / len(recommendations)
                                
                                all_recommended_items.update(recommended_items)
                                total_users += 1
                    
                    except Exception as e:
                        logger.warning(f"Error evaluating user {user_id}: {str(e)}")
                        continue
                
                if total_users > 0:
                    # Average metrics
                    for metric in ['precision_at_5', 'precision_at_10', 'recall_at_10', 'diversity_score']:
                        metrics[metric] /= total_users
                    
                    # Calculate coverage
                    metrics['coverage'] = len(all_recommended_items) / total_items
                
                # Log metrics
                for metric_name, metric_value in metrics.items():
                    mlflow.log_metric(metric_name, metric_value)
                
                logger.info(f"Hybrid model evaluation completed: {metrics}")
                return metrics
                
        except Exception as e:
            logger.error(f"Error evaluating hybrid model: {str(e)}")
            return {}
    
    def _get_test_users(self, n_users: int) -> List[str]:
        """Get a sample of users for testing"""
        try:
            engine = create_engine(
                f"postgresql://{self.config.db_user}:{self.config.db_password}@"
                f"{self.config.db_host}/{self.config.db_name}"
            )
            
            query = """
            SELECT DISTINCT customer_id
            FROM orders
            WHERE order_date >= NOW() - INTERVAL '90 days'
            AND order_status = 'completed'
            ORDER BY RANDOM()
            LIMIT %s
            """
            
            result = pd.read_sql(query, engine, params=[n_users])
            return result['customer_id'].tolist()
            
        except Exception as e:
            logger.error(f"Error getting test users: {str(e)}")
            return []
    
    def _get_user_test_purchases(self, user_id: str) -> List[str]:
        """Get user's recent purchases for evaluation"""
        try:
            engine = create_engine(
                f"postgresql://{self.config.db_user}:{self.config.db_password}@"
                f"{self.config.db_host}/{self.config.db_name}"
            )
            
            query = """
            SELECT DISTINCT product_id
            FROM order_items oi
            JOIN orders o ON oi.order_id = o.order_id
            WHERE o.customer_id = %s
            AND o.order_date >= NOW() - INTERVAL '30 days'
            AND o.order_status = 'completed'
            """
            
            result = pd.read_sql(query, engine, params=[user_id])
            return result['product_id'].tolist()
            
        except Exception as e:
            logger.error(f"Error getting user test purchases: {str(e)}")
            return []
    
    def _get_total_products_count(self) -> int:
        """Get total number of active products"""
        try:
            engine = create_engine(
                f"postgresql://{self.config.db_user}:{self.config.db_password}@"
                f"{self.config.db_host}/{self.config.db_name}"
            )
            
            query = "SELECT COUNT(*) FROM products WHERE active = true"
            result = pd.read_sql(query, engine)
            return result.iloc[0, 0]
            
        except Exception as e:
            logger.error(f"Error getting total products count: {str(e)}")
            return 1000  # Default fallback

def main():
    """Main execution function"""
    config = HybridRecommendationConfig()
    
    # Initialize hybrid recommendation engine
    engine = HybridRecommendationEngine(config)
    
    # Train hybrid model
    run_id = engine.train_hybrid_model()
    
    # Evaluate model
    metrics = engine.evaluate_hybrid_model()
    
    # Test recommendations
    test_user_id = "test_user_123"
    context = {
        'device': 'mobile',
        'location': {'area_type': 'urban'},
        'session_duration': 15,
        'pages_viewed': 5
    }
    
    recommendations = engine.get_hybrid_recommendations(test_user_id, context)
    
    print(f"Sample hybrid recommendations for user {test_user_id}:")
    for i, rec in enumerate(recommendations[:5], 1):
        print(f"{i}. {rec['name']} (score: {rec['blended_score']:.3f}, source: {rec['recommendation_source']})")
    
    logger.info("Hybrid recommendation engine setup completed successfully")

if __name__ == "__main__":
    main()