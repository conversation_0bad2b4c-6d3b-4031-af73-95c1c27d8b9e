#!/usr/bin/env python3
"""
Customer Churn Prediction Model for E-commerce Analytics
Advanced ML model to predict customer churn with feature engineering and ensemble methods
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import os
import sys
from typing import Dict, List, Tuple, Optional, Any
import joblib
import json
from dataclasses import dataclass

# ML libraries
from sklearn.model_selection import train_test_split, StratifiedKFold, cross_val_score
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, VotingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import (
    classification_report, confusion_matrix, roc_auc_score,
    precision_recall_curve, roc_curve, f1_score, precision_score, recall_score
)
from sklearn.feature_selection import SelectKBest, f_classif, RFE
import xgboost as xgb
import lightgbm as lgb
from imblearn.over_sampling import SMOTE
from imblearn.under_sampling import RandomUnderSampler
from imblearn.pipeline import Pipeline as ImbPipeline

# Visualization
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots

# MLflow and Feast integration
import mlflow
import mlflow.sklearn
import mlflow.xgboost
import mlflow.lightgbm
from mlflow.tracking import MlflowClient
from feast import FeatureStore

# Database and cloud
import boto3
from sqlalchemy import create_engine
import redis

# Configuration
@dataclass
class ChurnConfig:
    # Model parameters
    test_size: float = 0.2
    random_state: int = 42
    cv_folds: int = 5
    
    # Feature engineering
    lookback_days: int = 90
    churn_definition_days: int = 30
    min_purchase_amount: float = 10.0
    
    # Class imbalance handling
    use_smote: bool = True
    sampling_strategy: str = 'auto'
    
    # Model selection
    use_ensemble: bool = True
    enable_feature_selection: bool = True
    n_features_to_select: int = 50
    
    # MLflow configuration
    mlflow_tracking_uri: str = os.getenv('MLFLOW_TRACKING_URI', 'http://mlflow-server.mlflow:5000')
    experiment_name: str = 'customer-churn-prediction'
    
    # Database connections
    db_host: str = os.getenv('DB_HOST', 'analytics-db.cluster-xyz.us-east-1.rds.amazonaws.com')
    db_name: str = os.getenv('DB_NAME', 'ecommerce_analytics')
    db_user: str = os.getenv('DB_USER', 'analytics_user')
    db_password: str = os.getenv('DB_PASSWORD', 'analytics_password')
    
    # Feature store
    feast_repo_path: str = os.getenv('FEAST_REPO_PATH', '/feature_repo')
    
    # S3 configuration
    s3_bucket: str = os.getenv('S3_BUCKET', 'ecommerce-analytics-models')
    model_prefix: str = 'churn-prediction/'

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ChurnPredictor:
    def __init__(self, config: ChurnConfig):
        self.config = config
        self.db_engine = self._create_db_engine()
        self.s3_client = boto3.client('s3')
        
        # Initialize MLflow
        mlflow.set_tracking_uri(config.mlflow_tracking_uri)
        mlflow.set_experiment(config.experiment_name)
        
        # Initialize feature store
        self.feast_store = FeatureStore(repo_path=config.feast_repo_path)
        
        # Model components
        self.scaler = StandardScaler()
        self.label_encoders = {}
        self.feature_selector = None
        self.model = None
        self.feature_names = []
        self.feature_importance = {}
        
    def _create_db_engine(self):
        """Create database engine for PostgreSQL"""
        connection_string = f"postgresql://{self.config.db_user}:{self.config.db_password}@{self.config.db_host}/{self.config.db_name}"
        return create_engine(connection_string)
    
    def extract_training_data(self) -> pd.DataFrame:
        """Extract and prepare training data with churn labels"""
        logger.info("Extracting training data for churn prediction...")
        
        with mlflow.start_run(run_name="data_extraction", nested=True) as run:
            # Define time windows
            end_date = datetime.now()
            observation_end = end_date - timedelta(days=self.config.churn_definition_days)
            observation_start = observation_end - timedelta(days=self.config.lookback_days)
            
            # Extract user activity data
            activity_query = """
                WITH user_activity AS (
                    SELECT 
                        user_id,
                        COUNT(*) as total_events,
                        COUNT(DISTINCT session_id) as unique_sessions,
                        COUNT(DISTINCT DATE(event_timestamp)) as active_days,
                        AVG(CASE WHEN event_type = 'page_view' THEN 1 ELSE 0 END) as page_view_rate,
                        AVG(CASE WHEN event_type = 'add_to_cart' THEN 1 ELSE 0 END) as cart_add_rate,
                        AVG(CASE WHEN event_type = 'purchase' THEN 1 ELSE 0 END) as purchase_rate,
                        MIN(event_timestamp) as first_activity,
                        MAX(event_timestamp) as last_activity,
                        COUNT(DISTINCT device_type) as device_diversity,
                        COUNT(DISTINCT traffic_source) as traffic_source_diversity
                    FROM user_events 
                    WHERE event_timestamp >= %s AND event_timestamp <= %s
                    GROUP BY user_id
                ),
                user_purchases AS (
                    SELECT 
                        p.user_id,
                        COUNT(DISTINCT p.order_id) as total_orders,
                        SUM(p.total_amount) as total_purchase_amount,
                        AVG(p.total_amount) as avg_order_value,
                        MIN(p.purchase_timestamp) as first_purchase,
                        MAX(p.purchase_timestamp) as last_purchase,
                        COUNT(DISTINCT pi.product_id) as unique_products_purchased,
                        COUNT(DISTINCT pi.category_id) as unique_categories_purchased,
                        AVG(pi.unit_price) as avg_product_price,
                        SUM(pi.quantity) as total_quantity
                    FROM purchases p
                    JOIN purchase_items pi ON p.order_id = pi.order_id
                    WHERE p.purchase_timestamp >= %s AND p.purchase_timestamp <= %s
                        AND p.total_amount >= %s
                    GROUP BY p.user_id
                ),
                user_profiles AS (
                    SELECT 
                        user_id,
                        registration_date,
                        EXTRACT(DAYS FROM (%s - registration_date)) as days_since_registration,
                        country,
                        age_group,
                        gender,
                        subscription_tier,
                        total_lifetime_value,
                        preferred_language
                    FROM user_profiles
                    WHERE registration_date <= %s
                )
                SELECT 
                    up.user_id,
                    up.days_since_registration,
                    up.country,
                    up.age_group,
                    up.gender,
                    up.subscription_tier,
                    up.total_lifetime_value,
                    up.preferred_language,
                    COALESCE(ua.total_events, 0) as total_events,
                    COALESCE(ua.unique_sessions, 0) as unique_sessions,
                    COALESCE(ua.active_days, 0) as active_days,
                    COALESCE(ua.page_view_rate, 0) as page_view_rate,
                    COALESCE(ua.cart_add_rate, 0) as cart_add_rate,
                    COALESCE(ua.purchase_rate, 0) as purchase_rate,
                    COALESCE(ua.device_diversity, 0) as device_diversity,
                    COALESCE(ua.traffic_source_diversity, 0) as traffic_source_diversity,
                    COALESCE(upu.total_orders, 0) as total_orders,
                    COALESCE(upu.total_purchase_amount, 0) as total_purchase_amount,
                    COALESCE(upu.avg_order_value, 0) as avg_order_value,
                    COALESCE(upu.unique_products_purchased, 0) as unique_products_purchased,
                    COALESCE(upu.unique_categories_purchased, 0) as unique_categories_purchased,
                    COALESCE(upu.avg_product_price, 0) as avg_product_price,
                    COALESCE(upu.total_quantity, 0) as total_quantity,
                    ua.first_activity,
                    ua.last_activity,
                    upu.first_purchase,
                    upu.last_purchase
                FROM user_profiles up
                LEFT JOIN user_activity ua ON up.user_id = ua.user_id
                LEFT JOIN user_purchases upu ON up.user_id = upu.user_id
                WHERE up.registration_date <= %s
            """
            
            training_data = pd.read_sql(
                activity_query,
                self.db_engine,
                params=[
                    observation_start, observation_end,  # activity period
                    observation_start, observation_end,  # purchase period
                    self.config.min_purchase_amount,     # min purchase amount
                    observation_end,                     # for days calculation
                    observation_end,                     # registration cutoff
                    observation_end                      # final registration cutoff
                ]
            )
            
            # Create churn labels
            churn_query = """
                SELECT DISTINCT user_id
                FROM user_events 
                WHERE event_timestamp > %s
                   OR user_id IN (
                       SELECT user_id 
                       FROM purchases 
                       WHERE purchase_timestamp > %s
                   )
            """
            
            active_users = pd.read_sql(
                churn_query,
                self.db_engine,
                params=[observation_end, observation_end]
            )
            
            # Label churned users
            training_data['is_churned'] = ~training_data['user_id'].isin(active_users['user_id'])
            training_data['is_churned'] = training_data['is_churned'].astype(int)
            
            # Log data extraction metrics
            mlflow.log_metrics({
                'total_users': len(training_data),
                'churned_users': training_data['is_churned'].sum(),
                'churn_rate': training_data['is_churned'].mean(),
                'observation_period_days': self.config.lookback_days,
                'churn_definition_days': self.config.churn_definition_days
            })
            
            logger.info(f"Extracted training data: {len(training_data)} users, churn rate: {training_data['is_churned'].mean():.3f}")
            
            return training_data
    
    def engineer_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Engineer additional features for churn prediction"""
        logger.info("Engineering features for churn prediction...")
        
        with mlflow.start_run(run_name="feature_engineering", nested=True):
            # Create a copy to avoid modifying original data
            features_df = data.copy()
            
            # Temporal features
            now = datetime.now()
            
            # Days since last activity
            features_df['days_since_last_activity'] = (
                now - pd.to_datetime(features_df['last_activity'])
            ).dt.days.fillna(999)
            
            # Days since last purchase
            features_df['days_since_last_purchase'] = (
                now - pd.to_datetime(features_df['last_purchase'])
            ).dt.days.fillna(999)
            
            # Days since first activity
            features_df['days_since_first_activity'] = (
                now - pd.to_datetime(features_df['first_activity'])
            ).dt.days.fillna(0)
            
            # Customer lifecycle features
            features_df['customer_age_days'] = (
                pd.to_datetime(features_df['last_activity']) - 
                pd.to_datetime(features_df['first_activity'])
            ).dt.days.fillna(0)
            
            # Engagement features
            features_df['avg_events_per_day'] = (
                features_df['total_events'] / (features_df['active_days'] + 1)
            )
            
            features_df['avg_sessions_per_day'] = (
                features_df['unique_sessions'] / (features_df['active_days'] + 1)
            )
            
            features_df['events_per_session'] = (
                features_df['total_events'] / (features_df['unique_sessions'] + 1)
            )
            
            # Purchase behavior features
            features_df['orders_per_day'] = (
                features_df['total_orders'] / (features_df['active_days'] + 1)
            )
            
            features_df['purchase_frequency'] = (
                features_df['total_orders'] / (features_df['customer_age_days'] + 1)
            )
            
            features_df['revenue_per_day'] = (
                features_df['total_purchase_amount'] / (features_df['active_days'] + 1)
            )
            
            features_df['items_per_order'] = (
                features_df['total_quantity'] / (features_df['total_orders'] + 1)
            )
            
            # Product diversity features
            features_df['product_exploration_rate'] = (
                features_df['unique_products_purchased'] / (features_df['total_orders'] + 1)
            )
            
            features_df['category_diversity_score'] = (
                features_df['unique_categories_purchased'] / (features_df['unique_products_purchased'] + 1)
            )
            
            # Value-based features
            features_df['price_sensitivity'] = (
                features_df['avg_product_price'] / (features_df['avg_order_value'] + 1)
            )
            
            features_df['lifetime_value_per_day'] = (
                features_df['total_lifetime_value'] / (features_df['days_since_registration'] + 1)
            )
            
            # Behavioral consistency features
            features_df['purchase_to_activity_ratio'] = (
                features_df['total_orders'] / (features_df['total_events'] + 1)
            )
            
            features_df['session_efficiency'] = (
                features_df['total_orders'] / (features_df['unique_sessions'] + 1)
            )
            
            # Recency, Frequency, Monetary (RFM) features
            features_df['recency_score'] = pd.qcut(
                features_df['days_since_last_purchase'].rank(method='first'),
                q=5, labels=False, duplicates='drop'
            )
            
            features_df['frequency_score'] = pd.qcut(
                features_df['total_orders'].rank(method='first'),
                q=5, labels=False, duplicates='drop'
            )
            
            features_df['monetary_score'] = pd.qcut(
                features_df['total_purchase_amount'].rank(method='first'),
                q=5, labels=False, duplicates='drop'
            )
            
            features_df['rfm_score'] = (
                features_df['recency_score'] + 
                features_df['frequency_score'] + 
                features_df['monetary_score']
            )
            
            # Interaction features
            features_df['high_value_frequent_buyer'] = (
                (features_df['avg_order_value'] > features_df['avg_order_value'].quantile(0.8)) &
                (features_df['total_orders'] > features_df['total_orders'].quantile(0.8))
            ).astype(int)
            
            features_df['at_risk_segment'] = (
                (features_df['days_since_last_activity'] > 14) &
                (features_df['total_purchase_amount'] > 0)
            ).astype(int)
            
            # Remove datetime columns
            datetime_cols = ['first_activity', 'last_activity', 'first_purchase', 'last_purchase']
            features_df = features_df.drop(columns=[col for col in datetime_cols if col in features_df.columns])
            
            # Handle infinite values
            features_df = features_df.replace([np.inf, -np.inf], np.nan)
            
            # Fill remaining NaN values
            numeric_cols = features_df.select_dtypes(include=[np.number]).columns
            features_df[numeric_cols] = features_df[numeric_cols].fillna(0)
            
            categorical_cols = features_df.select_dtypes(include=['object']).columns
            features_df[categorical_cols] = features_df[categorical_cols].fillna('unknown')
            
            # Log feature engineering metrics
            mlflow.log_metrics({
                'total_features': len(features_df.columns) - 2,  # excluding user_id and target
                'numeric_features': len(numeric_cols) - 2,
                'categorical_features': len(categorical_cols) - 1  # excluding user_id
            })
            
            logger.info(f"Feature engineering completed. Generated {len(features_df.columns)} features")
            
            return features_df
    
    def preprocess_data(self, data: pd.DataFrame, is_training: bool = True) -> Tuple[np.ndarray, np.ndarray]:
        """Preprocess data for model training"""
        logger.info("Preprocessing data...")
        
        # Separate features and target
        feature_cols = [col for col in data.columns if col not in ['user_id', 'is_churned']]
        X = data[feature_cols].copy()
        
        if 'is_churned' in data.columns:
            y = data['is_churned'].values
        else:
            y = None
        
        # Store feature names
        if is_training:
            self.feature_names = feature_cols
        
        # Handle categorical variables
        categorical_cols = X.select_dtypes(include=['object']).columns
        for col in categorical_cols:
            if is_training:
                le = LabelEncoder()
                X[col] = le.fit_transform(X[col].astype(str))
                self.label_encoders[col] = le
            else:
                if col in self.label_encoders:
                    # Handle unseen categories
                    le = self.label_encoders[col]
                    X[col] = X[col].astype(str)
                    unseen_mask = ~X[col].isin(le.classes_)
                    X.loc[unseen_mask, col] = 'unknown'
                    X[col] = le.transform(X[col])
                else:
                    X[col] = 0  # Default for unseen categorical
        
        # Scale numerical features
        if is_training:
            X_scaled = self.scaler.fit_transform(X)
        else:
            X_scaled = self.scaler.transform(X)
        
        return X_scaled, y
    
    def select_features(self, X: np.ndarray, y: np.ndarray) -> np.ndarray:
        """Select most important features"""
        logger.info("Selecting features...")
        
        if not self.config.enable_feature_selection:
            return X
        
        with mlflow.start_run(run_name="feature_selection", nested=True):
            # Use SelectKBest with f_classif
            selector = SelectKBest(
                score_func=f_classif,
                k=min(self.config.n_features_to_select, X.shape[1])
            )
            
            X_selected = selector.fit_transform(X, y)
            
            # Store feature selector
            self.feature_selector = selector
            
            # Get selected feature names
            selected_indices = selector.get_support(indices=True)
            selected_features = [self.feature_names[i] for i in selected_indices]
            
            # Log feature selection metrics
            mlflow.log_metrics({
                'original_features': X.shape[1],
                'selected_features': X_selected.shape[1],
                'feature_reduction_ratio': 1 - (X_selected.shape[1] / X.shape[1])
            })
            
            # Log selected features
            mlflow.log_dict({
                'selected_features': selected_features,
                'feature_scores': dict(zip(selected_features, selector.scores_[selected_indices]))
            }, 'selected_features.json')
            
            logger.info(f"Selected {X_selected.shape[1]} features out of {X.shape[1]}")
            
            return X_selected
    
    def train_model(self, X: np.ndarray, y: np.ndarray) -> Any:
        """Train churn prediction model"""
        logger.info("Training churn prediction model...")
        
        with mlflow.start_run(run_name="model_training", nested=True):
            # Handle class imbalance with SMOTE
            if self.config.use_smote:
                logger.info("Applying SMOTE for class balance...")
                smote = SMOTE(
                    sampling_strategy=self.config.sampling_strategy,
                    random_state=self.config.random_state
                )
                X_resampled, y_resampled = smote.fit_resample(X, y)
                
                mlflow.log_metrics({
                    'original_samples': len(y),
                    'resampled_samples': len(y_resampled),
                    'original_positive_class': y.sum(),
                    'resampled_positive_class': y_resampled.sum()
                })
            else:
                X_resampled, y_resampled = X, y
            
            # Split data
            X_train, X_val, y_train, y_val = train_test_split(
                X_resampled, y_resampled,
                test_size=self.config.test_size,
                random_state=self.config.random_state,
                stratify=y_resampled
            )
            
            if self.config.use_ensemble:
                # Create ensemble model
                models = {
                    'random_forest': RandomForestClassifier(
                        n_estimators=100,
                        max_depth=10,
                        random_state=self.config.random_state,
                        n_jobs=-1
                    ),
                    'gradient_boosting': GradientBoostingClassifier(
                        n_estimators=100,
                        max_depth=6,
                        random_state=self.config.random_state
                    ),
                    'xgboost': xgb.XGBClassifier(
                        n_estimators=100,
                        max_depth=6,
                        random_state=self.config.random_state,
                        eval_metric='logloss'
                    ),
                    'lightgbm': lgb.LGBMClassifier(
                        n_estimators=100,
                        max_depth=6,
                        random_state=self.config.random_state,
                        verbosity=-1
                    )
                }
                
                # Train individual models and create ensemble
                trained_models = []
                for name, model in models.items():
                    logger.info(f"Training {name}...")
                    model.fit(X_train, y_train)
                    
                    # Evaluate individual model
                    val_predictions = model.predict(X_val)
                    val_proba = model.predict_proba(X_val)[:, 1]
                    
                    mlflow.log_metrics({
                        f'{name}_val_accuracy': (val_predictions == y_val).mean(),
                        f'{name}_val_f1': f1_score(y_val, val_predictions),
                        f'{name}_val_auc': roc_auc_score(y_val, val_proba)
                    })
                    
                    trained_models.append((name, model))
                
                # Create voting classifier
                voting_classifier = VotingClassifier(
                    estimators=trained_models,
                    voting='soft'
                )
                
                voting_classifier.fit(X_train, y_train)
                final_model = voting_classifier
                
            else:
                # Train single XGBoost model
                final_model = xgb.XGBClassifier(
                    n_estimators=200,
                    max_depth=8,
                    learning_rate=0.1,
                    random_state=self.config.random_state,
                    eval_metric='logloss'
                )
                
                final_model.fit(
                    X_train, y_train,
                    eval_set=[(X_val, y_val)],
                    early_stopping_rounds=20,
                    verbose=False
                )
            
            # Final evaluation
            val_predictions = final_model.predict(X_val)
            val_proba = final_model.predict_proba(X_val)[:, 1]
            
            # Calculate metrics
            metrics = {
                'val_accuracy': (val_predictions == y_val).mean(),
                'val_precision': precision_score(y_val, val_predictions),
                'val_recall': recall_score(y_val, val_predictions),
                'val_f1': f1_score(y_val, val_predictions),
                'val_auc': roc_auc_score(y_val, val_proba)
            }
            
            mlflow.log_metrics(metrics)
            
            # Cross-validation
            cv_scores = cross_val_score(
                final_model, X_resampled, y_resampled,
                cv=StratifiedKFold(n_splits=self.config.cv_folds, shuffle=True, random_state=self.config.random_state),
                scoring='roc_auc'
            )
            
            mlflow.log_metrics({
                'cv_auc_mean': cv_scores.mean(),
                'cv_auc_std': cv_scores.std()
            })
            
            # Feature importance
            if hasattr(final_model, 'feature_importances_'):
                importance = final_model.feature_importances_
            elif hasattr(final_model, 'coef_'):
                importance = np.abs(final_model.coef_[0])
            else:
                # For ensemble models, average importance
                importance = np.mean([
                    estimator.feature_importances_ 
                    for name, estimator in final_model.estimators_ 
                    if hasattr(estimator, 'feature_importances_')
                ], axis=0)
            
            # Store feature importance
            if self.config.enable_feature_selection:
                # Map back to original feature names
                selected_indices = self.feature_selector.get_support(indices=True)
                selected_features = [self.feature_names[i] for i in selected_indices]
                self.feature_importance = dict(zip(selected_features, importance))
            else:
                self.feature_importance = dict(zip(self.feature_names, importance))
            
            # Log feature importance
            mlflow.log_dict(self.feature_importance, 'feature_importance.json')
            
            # Log model
            if self.config.use_ensemble:
                mlflow.sklearn.log_model(final_model, "churn_model")
            else:
                mlflow.xgboost.log_model(final_model, "churn_model")
            
            logger.info(f"Model training completed. Validation AUC: {metrics['val_auc']:.4f}")
            
            return final_model
    
    def create_visualizations(self, X_val: np.ndarray, y_val: np.ndarray, y_proba: np.ndarray):
        """Create visualizations for model performance"""
        logger.info("Creating model visualizations...")
        
        # ROC Curve
        fpr, tpr, _ = roc_curve(y_val, y_proba)
        auc_score = roc_auc_score(y_val, y_proba)
        
        fig_roc = go.Figure()
        fig_roc.add_trace(go.Scatter(
            x=fpr, y=tpr,
            mode='lines',
            name=f'ROC Curve (AUC = {auc_score:.3f})'
        ))
        fig_roc.add_trace(go.Scatter(
            x=[0, 1], y=[0, 1],
            mode='lines',
            line=dict(dash='dash'),
            name='Random'
        ))
        fig_roc.update_layout(
            title='ROC Curve',
            xaxis_title='False Positive Rate',
            yaxis_title='True Positive Rate'
        )
        
        # Precision-Recall Curve
        precision, recall, _ = precision_recall_curve(y_val, y_proba)
        
        fig_pr = go.Figure()
        fig_pr.add_trace(go.Scatter(
            x=recall, y=precision,
            mode='lines',
            name='Precision-Recall Curve'
        ))
        fig_pr.update_layout(
            title='Precision-Recall Curve',
            xaxis_title='Recall',
            yaxis_title='Precision'
        )
        
        # Feature Importance
        if self.feature_importance:
            top_features = sorted(
                self.feature_importance.items(),
                key=lambda x: x[1],
                reverse=True
            )[:20]  # Top 20 features
            
            feature_names, importance_values = zip(*top_features)
            
            fig_importance = go.Figure(go.Bar(
                x=importance_values,
                y=feature_names,
                orientation='h'
            ))
            fig_importance.update_layout(
                title='Top 20 Feature Importance',
                xaxis_title='Importance',
                yaxis_title='Features',
                height=600
            )
        
        # Save plots
        try:
            fig_roc.write_html('/tmp/roc_curve.html')
            fig_pr.write_html('/tmp/precision_recall_curve.html')
            if self.feature_importance:
                fig_importance.write_html('/tmp/feature_importance.html')
            
            # Log to MLflow
            mlflow.log_artifact('/tmp/roc_curve.html')
            mlflow.log_artifact('/tmp/precision_recall_curve.html')
            if self.feature_importance:
                mlflow.log_artifact('/tmp/feature_importance.html')
                
        except Exception as e:
            logger.warning(f"Could not save visualizations: {str(e)}")
    
    def save_model(self, model: Any, model_name: str = "churn_prediction_model"):
        """Save model and preprocessing components"""
        logger.info("Saving model artifacts...")
        
        # Create model package
        model_artifacts = {
            'model': model,
            'scaler': self.scaler,
            'label_encoders': self.label_encoders,
            'feature_selector': self.feature_selector,
            'feature_names': self.feature_names,
            'feature_importance': self.feature_importance,
            'config': self.config
        }
        
        # Save locally
        model_path = f'/tmp/{model_name}.joblib'
        joblib.dump(model_artifacts, model_path)
        
        # Log to MLflow
        mlflow.log_artifact(model_path, 'model_artifacts')
        
        # Save to S3
        try:
            s3_key = f"{self.config.model_prefix}{model_name}_{int(datetime.now().timestamp())}.joblib"
            self.s3_client.upload_file(
                model_path,
                self.config.s3_bucket,
                s3_key
            )
            logger.info(f"Model saved to s3://{self.config.s3_bucket}/{s3_key}")
        except Exception as e:
            logger.warning(f"Could not save to S3: {str(e)}")
        
        return model_path
    
    def predict_churn(self, user_data: pd.DataFrame) -> Dict:
        """Predict churn for new users"""
        if self.model is None:
            raise ValueError("Model not trained. Please train the model first.")
        
        # Preprocess data
        X, _ = self.preprocess_data(user_data, is_training=False)
        
        # Select features if enabled
        if self.config.enable_feature_selection and self.feature_selector:
            X = self.feature_selector.transform(X)
        
        # Make predictions
        predictions = self.model.predict(X)
        probabilities = self.model.predict_proba(X)[:, 1]
        
        results = {
            'user_ids': user_data['user_id'].tolist(),
            'churn_predictions': predictions.tolist(),
            'churn_probabilities': probabilities.tolist(),
            'prediction_timestamp': datetime.now().isoformat()
        }
        
        return results
    
    def train_full_pipeline(self):
        """Run the complete training pipeline"""
        logger.info("Starting complete churn prediction training pipeline...")
        
        with mlflow.start_run(run_name="churn_prediction_pipeline"):
            try:
                # Extract training data
                training_data = self.extract_training_data()
                
                # Engineer features
                features_data = self.engineer_features(training_data)
                
                # Preprocess data
                X, y = self.preprocess_data(features_data, is_training=True)
                
                # Feature selection
                X_selected = self.select_features(X, y)
                
                # Train model
                self.model = self.train_model(X_selected, y)
                
                # Create validation set for visualizations
                X_train, X_val, y_train, y_val = train_test_split(
                    X_selected, y,
                    test_size=0.2,
                    random_state=self.config.random_state,
                    stratify=y
                )
                
                y_val_proba = self.model.predict_proba(X_val)[:, 1]
                
                # Create visualizations
                self.create_visualizations(X_val, y_val, y_val_proba)
                
                # Save model
                model_path = self.save_model(self.model)
                
                # Register model in MLflow
                mlflow.register_model(
                    f"runs:/{mlflow.active_run().info.run_id}/churn_model",
                    "customer-churn-predictor"
                )
                
                logger.info("Churn prediction pipeline completed successfully")
                
                return {
                    'model_path': model_path,
                    'training_samples': len(training_data),
                    'feature_count': len(self.feature_names),
                    'validation_auc': roc_auc_score(y_val, y_val_proba)
                }
                
            except Exception as e:
                logger.error(f"Training pipeline failed: {str(e)}")
                mlflow.log_metric("pipeline_success", 0)
                raise

def main():
    """Main function to run churn prediction training"""
    config = ChurnConfig()
    predictor = ChurnPredictor(config)
    
    try:
        results = predictor.train_full_pipeline()
        print(f"Training completed successfully!")
        print(f"Model path: {results['model_path']}")
        print(f"Training samples: {results['training_samples']}")
        print(f"Features: {results['feature_count']}")
        print(f"Validation AUC: {results['validation_auc']:.4f}")
        
    except Exception as e:
        logger.error(f"Training failed: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()