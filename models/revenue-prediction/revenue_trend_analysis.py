#!/usr/bin/env python3
"""
Revenue Prediction and Trend Analysis for E-commerce Analytics
Advanced revenue modeling with trend analysis, seasonality detection, and business insights
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import os
import sys
from typing import Dict, List, Tuple, Optional, Any, Union
import joblib
import json
import warnings
from dataclasses import dataclass

# Statistical and ML libraries
from scipy import stats
from scipy.signal import find_peaks
from statsmodels.tsa.seasonal import seasonal_decompose
from statsmodels.tsa.stattools import adfuller, acf, pacf
from statsmodels.stats.diagnostic import acorr_ljungbox
from sklearn.linear_model import LinearRegression, Ridge, Lasso
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.preprocessing import StandardScaler, PolynomialFeatures
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.model_selection import train_test_split, cross_val_score
import xgboost as xgb
import lightgbm as lgb

# Time series analysis
import pmdarima as pm
from prophet import Prophet
from prophet.diagnostics import cross_validation, performance_metrics

# Advanced analytics
from sklearn.cluster import KMeans
from sklearn.decomposition import PCA
from scipy.cluster.hierarchy import dendrogram, linkage, fcluster
import networkx as nx

# Visualization
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.figure_factory as ff

# MLflow integration
import mlflow
import mlflow.sklearn
from mlflow.tracking import MlflowClient

# Database and cloud
import boto3
from sqlalchemy import create_engine

warnings.filterwarnings('ignore')

# Configuration
@dataclass
class RevenueAnalysisConfig:
    # Analysis parameters
    analysis_window_days: int = 730  # 2 years
    forecast_horizon_days: int = 90
    trend_detection_window: int = 30
    seasonality_periods: List[int] = None
    
    # Statistical parameters
    confidence_level: float = 0.95
    trend_significance_threshold: float = 0.05
    outlier_threshold: float = 3.0
    
    # Segmentation parameters
    revenue_segments: int = 5
    customer_segments: int = 4
    product_segments: int = 6
    
    # Model parameters
    use_ensemble: bool = True
    polynomial_degree: int = 3
    cross_validation_folds: int = 5
    
    # MLflow configuration
    mlflow_tracking_uri: str = os.getenv('MLFLOW_TRACKING_URI', 'http://mlflow-server.mlflow:5000')
    experiment_name: str = 'revenue-prediction-analysis'
    
    # Database connections
    db_host: str = os.getenv('DB_HOST', 'analytics-db.cluster-xyz.us-east-1.rds.amazonaws.com')
    db_name: str = os.getenv('DB_NAME', 'ecommerce_analytics')
    db_user: str = os.getenv('DB_USER', 'analytics_user')
    db_password: str = os.getenv('DB_PASSWORD', 'analytics_password')
    
    # S3 configuration
    s3_bucket: str = os.getenv('S3_BUCKET', 'ecommerce-analytics-insights')
    report_prefix: str = 'revenue-analysis/'
    
    def __post_init__(self):
        if self.seasonality_periods is None:
            self.seasonality_periods = [7, 30, 91, 365]  # Weekly, Monthly, Quarterly, Yearly

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class RevenueAnalyzer:
    def __init__(self, config: RevenueAnalysisConfig):
        self.config = config
        self.db_engine = self._create_db_engine()
        self.s3_client = boto3.client('s3')
        
        # Initialize MLflow
        mlflow.set_tracking_uri(config.mlflow_tracking_uri)
        mlflow.set_experiment(config.experiment_name)
        
        # Analysis results storage
        self.revenue_data = None
        self.trend_analysis = {}
        self.seasonality_analysis = {}
        self.segment_analysis = {}
        self.forecasts = {}
        self.insights = {}
        
    def _create_db_engine(self):
        """Create database engine for PostgreSQL"""
        connection_string = f"postgresql://{self.config.db_user}:{self.config.db_password}@{self.config.db_host}/{self.config.db_name}"
        return create_engine(connection_string)
    
    def extract_revenue_data(self) -> pd.DataFrame:
        """Extract comprehensive revenue data for analysis"""
        logger.info("Extracting revenue data for analysis...")
        
        with mlflow.start_run(run_name="data_extraction", nested=True):
            end_date = datetime.now()
            start_date = end_date - timedelta(days=self.config.analysis_window_days)
            
            # Comprehensive revenue query with multiple dimensions
            revenue_query = """
                WITH daily_revenue AS (
                    SELECT 
                        DATE(p.purchase_timestamp) as date,
                        SUM(p.total_amount) as total_revenue,
                        COUNT(DISTINCT p.order_id) as total_orders,
                        COUNT(DISTINCT p.user_id) as unique_customers,
                        AVG(p.total_amount) as avg_order_value,
                        STDDEV(p.total_amount) as revenue_volatility,
                        
                        -- Payment method breakdown
                        SUM(CASE WHEN p.payment_method = 'credit_card' THEN p.total_amount ELSE 0 END) as cc_revenue,
                        SUM(CASE WHEN p.payment_method = 'paypal' THEN p.total_amount ELSE 0 END) as paypal_revenue,
                        SUM(CASE WHEN p.payment_method = 'bank_transfer' THEN p.total_amount ELSE 0 END) as bank_revenue,
                        
                        -- Customer type breakdown
                        SUM(CASE WHEN up.subscription_tier = 'premium' THEN p.total_amount ELSE 0 END) as premium_revenue,
                        SUM(CASE WHEN up.subscription_tier = 'standard' THEN p.total_amount ELSE 0 END) as standard_revenue,
                        SUM(CASE WHEN up.subscription_tier = 'free' THEN p.total_amount ELSE 0 END) as free_revenue,
                        
                        -- Geographic breakdown
                        SUM(CASE WHEN up.country IN ('US', 'CA') THEN p.total_amount ELSE 0 END) as north_america_revenue,
                        SUM(CASE WHEN up.country IN ('GB', 'DE', 'FR', 'IT', 'ES') THEN p.total_amount ELSE 0 END) as europe_revenue,
                        SUM(CASE WHEN up.country IN ('JP', 'CN', 'KR', 'IN') THEN p.total_amount ELSE 0 END) as asia_revenue,
                        
                        -- Order size segments
                        COUNT(CASE WHEN p.total_amount < 25 THEN 1 END) as small_orders,
                        COUNT(CASE WHEN p.total_amount BETWEEN 25 AND 100 THEN 1 END) as medium_orders,
                        COUNT(CASE WHEN p.total_amount BETWEEN 100 AND 500 THEN 1 END) as large_orders,
                        COUNT(CASE WHEN p.total_amount > 500 THEN 1 END) as enterprise_orders
                        
                    FROM purchases p
                    LEFT JOIN user_profiles up ON p.user_id = up.user_id
                    WHERE p.purchase_timestamp >= %s 
                        AND p.purchase_timestamp <= %s
                        AND p.total_amount > 0
                    GROUP BY DATE(p.purchase_timestamp)
                ),
                daily_products AS (
                    SELECT 
                        DATE(p.purchase_timestamp) as date,
                        COUNT(DISTINCT pi.product_id) as unique_products_sold,
                        COUNT(DISTINCT pi.category_id) as unique_categories_sold,
                        SUM(pi.quantity) as total_units_sold,
                        AVG(pi.unit_price) as avg_product_price,
                        
                        -- Category revenue breakdown (top categories)
                        SUM(CASE WHEN cat.name = 'Electronics' THEN pi.quantity * pi.unit_price ELSE 0 END) as electronics_revenue,
                        SUM(CASE WHEN cat.name = 'Clothing' THEN pi.quantity * pi.unit_price ELSE 0 END) as clothing_revenue,
                        SUM(CASE WHEN cat.name = 'Home & Garden' THEN pi.quantity * pi.unit_price ELSE 0 END) as home_revenue,
                        SUM(CASE WHEN cat.name = 'Books' THEN pi.quantity * pi.unit_price ELSE 0 END) as books_revenue,
                        SUM(CASE WHEN cat.name = 'Sports' THEN pi.quantity * pi.unit_price ELSE 0 END) as sports_revenue
                        
                    FROM purchases p
                    JOIN purchase_items pi ON p.order_id = pi.order_id
                    LEFT JOIN categories cat ON pi.category_id = cat.category_id
                    WHERE p.purchase_timestamp >= %s 
                        AND p.purchase_timestamp <= %s
                    GROUP BY DATE(p.purchase_timestamp)
                ),
                daily_traffic AS (
                    SELECT 
                        DATE(event_timestamp) as date,
                        COUNT(DISTINCT user_id) as unique_visitors,
                        COUNT(DISTINCT session_id) as total_sessions,
                        COUNT(*) as total_events,
                        SUM(CASE WHEN event_type = 'page_view' THEN 1 ELSE 0 END) as page_views,
                        SUM(CASE WHEN event_type = 'add_to_cart' THEN 1 ELSE 0 END) as cart_adds,
                        SUM(CASE WHEN device_type = 'mobile' THEN 1 ELSE 0 END) as mobile_traffic,
                        SUM(CASE WHEN device_type = 'desktop' THEN 1 ELSE 0 END) as desktop_traffic
                    FROM user_events 
                    WHERE event_timestamp >= %s 
                        AND event_timestamp <= %s
                    GROUP BY DATE(event_timestamp)
                )
                SELECT 
                    COALESCE(dr.date, dp.date, dt.date) as date,
                    COALESCE(dr.total_revenue, 0) as total_revenue,
                    COALESCE(dr.total_orders, 0) as total_orders,
                    COALESCE(dr.unique_customers, 0) as unique_customers,
                    COALESCE(dr.avg_order_value, 0) as avg_order_value,
                    COALESCE(dr.revenue_volatility, 0) as revenue_volatility,
                    COALESCE(dr.cc_revenue, 0) as cc_revenue,
                    COALESCE(dr.paypal_revenue, 0) as paypal_revenue,
                    COALESCE(dr.bank_revenue, 0) as bank_revenue,
                    COALESCE(dr.premium_revenue, 0) as premium_revenue,
                    COALESCE(dr.standard_revenue, 0) as standard_revenue,
                    COALESCE(dr.free_revenue, 0) as free_revenue,
                    COALESCE(dr.north_america_revenue, 0) as north_america_revenue,
                    COALESCE(dr.europe_revenue, 0) as europe_revenue,
                    COALESCE(dr.asia_revenue, 0) as asia_revenue,
                    COALESCE(dr.small_orders, 0) as small_orders,
                    COALESCE(dr.medium_orders, 0) as medium_orders,
                    COALESCE(dr.large_orders, 0) as large_orders,
                    COALESCE(dr.enterprise_orders, 0) as enterprise_orders,
                    COALESCE(dp.unique_products_sold, 0) as unique_products_sold,
                    COALESCE(dp.unique_categories_sold, 0) as unique_categories_sold,
                    COALESCE(dp.total_units_sold, 0) as total_units_sold,
                    COALESCE(dp.avg_product_price, 0) as avg_product_price,
                    COALESCE(dp.electronics_revenue, 0) as electronics_revenue,
                    COALESCE(dp.clothing_revenue, 0) as clothing_revenue,
                    COALESCE(dp.home_revenue, 0) as home_revenue,
                    COALESCE(dp.books_revenue, 0) as books_revenue,
                    COALESCE(dp.sports_revenue, 0) as sports_revenue,
                    COALESCE(dt.unique_visitors, 0) as unique_visitors,
                    COALESCE(dt.total_sessions, 0) as total_sessions,
                    COALESCE(dt.total_events, 0) as total_events,
                    COALESCE(dt.page_views, 0) as page_views,
                    COALESCE(dt.cart_adds, 0) as cart_adds,
                    COALESCE(dt.mobile_traffic, 0) as mobile_traffic,
                    COALESCE(dt.desktop_traffic, 0) as desktop_traffic
                FROM daily_revenue dr
                FULL OUTER JOIN daily_products dp ON dr.date = dp.date
                FULL OUTER JOIN daily_traffic dt ON dr.date = dt.date
                ORDER BY date
            """
            
            revenue_data = pd.read_sql(
                revenue_query,
                self.db_engine,
                params=[start_date, end_date] * 3
            )
            
            # Convert date column and handle missing dates
            revenue_data['date'] = pd.to_datetime(revenue_data['date'])
            
            # Create complete date range and fill missing values
            date_range = pd.date_range(start=start_date, end=end_date, freq='D')
            revenue_data = revenue_data.set_index('date').reindex(date_range).fillna(0).reset_index()
            revenue_data.rename(columns={'index': 'date'}, inplace=True)
            
            # Add derived metrics
            revenue_data['conversion_rate'] = revenue_data['total_orders'] / (revenue_data['unique_visitors'] + 1)
            revenue_data['cart_conversion_rate'] = revenue_data['total_orders'] / (revenue_data['cart_adds'] + 1)
            revenue_data['revenue_per_visitor'] = revenue_data['total_revenue'] / (revenue_data['unique_visitors'] + 1)
            revenue_data['mobile_traffic_ratio'] = revenue_data['mobile_traffic'] / (revenue_data['total_events'] + 1)
            
            # Add time-based features
            revenue_data = self._add_temporal_features(revenue_data)
            
            # Store data
            self.revenue_data = revenue_data
            
            # Log extraction metrics
            mlflow.log_metrics({
                'total_days_analyzed': len(revenue_data),
                'total_revenue': revenue_data['total_revenue'].sum(),
                'avg_daily_revenue': revenue_data['total_revenue'].mean(),
                'revenue_std': revenue_data['total_revenue'].std(),
                'max_daily_revenue': revenue_data['total_revenue'].max(),
                'zero_revenue_days': (revenue_data['total_revenue'] == 0).sum()
            })
            
            logger.info(f"Extracted {len(revenue_data)} days of revenue data")
            return revenue_data
    
    def _add_temporal_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Add temporal features for analysis"""
        data = data.copy()
        
        # Basic time features
        data['year'] = data['date'].dt.year
        data['month'] = data['date'].dt.month
        data['day'] = data['date'].dt.day
        data['dayofweek'] = data['date'].dt.dayofweek
        data['dayofyear'] = data['date'].dt.dayofyear
        data['week'] = data['date'].dt.isocalendar().week
        data['quarter'] = data['date'].dt.quarter
        
        # Derived temporal features
        data['is_weekend'] = (data['dayofweek'] >= 5).astype(int)
        data['is_month_start'] = data['date'].dt.is_month_start.astype(int)
        data['is_month_end'] = data['date'].dt.is_month_end.astype(int)
        data['is_quarter_start'] = data['date'].dt.is_quarter_start.astype(int)
        data['is_quarter_end'] = data['date'].dt.is_quarter_end.astype(int)
        
        # Cyclical encoding
        data['month_sin'] = np.sin(2 * np.pi * data['month'] / 12)
        data['month_cos'] = np.cos(2 * np.pi * data['month'] / 12)
        data['dayofweek_sin'] = np.sin(2 * np.pi * data['dayofweek'] / 7)
        data['dayofweek_cos'] = np.cos(2 * np.pi * data['dayofweek'] / 7)
        data['dayofyear_sin'] = np.sin(2 * np.pi * data['dayofyear'] / 365)
        data['dayofyear_cos'] = np.cos(2 * np.pi * data['dayofyear'] / 365)
        
        return data
    
    def detect_trends(self, data: pd.DataFrame) -> Dict:
        """Detect and analyze revenue trends"""
        logger.info("Detecting revenue trends...")
        
        with mlflow.start_run(run_name="trend_analysis", nested=True):
            trends = {}
            
            # Overall trend analysis
            trends['overall'] = self._analyze_overall_trend(data)
            
            # Segment-wise trend analysis
            trends['payment_methods'] = self._analyze_segment_trends(
                data, ['cc_revenue', 'paypal_revenue', 'bank_revenue']
            )
            
            trends['geographic'] = self._analyze_segment_trends(
                data, ['north_america_revenue', 'europe_revenue', 'asia_revenue']
            )
            
            trends['customer_tiers'] = self._analyze_segment_trends(
                data, ['premium_revenue', 'standard_revenue', 'free_revenue']
            )
            
            trends['categories'] = self._analyze_segment_trends(
                data, ['electronics_revenue', 'clothing_revenue', 'home_revenue', 'books_revenue', 'sports_revenue']
            )
            
            # Trend change detection
            trends['change_points'] = self._detect_trend_changes(data['total_revenue'])
            
            # Log trend metrics
            mlflow.log_metrics({
                'overall_trend_slope': trends['overall']['slope'],
                'overall_trend_pvalue': trends['overall']['p_value'],
                'trend_is_significant': int(trends['overall']['is_significant']),
                'change_points_detected': len(trends['change_points'])
            })
            
            self.trend_analysis = trends
            return trends
    
    def _analyze_overall_trend(self, data: pd.DataFrame) -> Dict:
        """Analyze overall revenue trend"""
        revenue = data['total_revenue'].values
        time_index = np.arange(len(revenue))
        
        # Linear regression for trend
        slope, intercept, r_value, p_value, std_err = stats.linregress(time_index, revenue)
        
        # Trend classification
        trend_direction = 'increasing' if slope > 0 else 'decreasing'
        is_significant = p_value < self.config.trend_significance_threshold
        
        # Trend strength
        if abs(r_value) > 0.7:
            trend_strength = 'strong'
        elif abs(r_value) > 0.3:
            trend_strength = 'moderate'
        else:
            trend_strength = 'weak'
        
        # Calculate trend metrics
        daily_growth_rate = (slope / revenue.mean()) * 100 if revenue.mean() > 0 else 0
        
        return {
            'slope': slope,
            'intercept': intercept,
            'r_squared': r_value ** 2,
            'p_value': p_value,
            'std_error': std_err,
            'direction': trend_direction,
            'strength': trend_strength,
            'is_significant': is_significant,
            'daily_growth_rate_pct': daily_growth_rate
        }
    
    def _analyze_segment_trends(self, data: pd.DataFrame, columns: List[str]) -> Dict:
        """Analyze trends for specific segments"""
        segment_trends = {}
        
        for column in columns:
            if column in data.columns:
                values = data[column].values
                time_index = np.arange(len(values))
                
                if values.sum() > 0:  # Only analyze if there's data
                    slope, intercept, r_value, p_value, std_err = stats.linregress(time_index, values)
                    
                    segment_trends[column] = {
                        'slope': slope,
                        'r_squared': r_value ** 2,
                        'p_value': p_value,
                        'direction': 'increasing' if slope > 0 else 'decreasing',
                        'is_significant': p_value < self.config.trend_significance_threshold,
                        'daily_growth_rate_pct': (slope / values.mean()) * 100 if values.mean() > 0 else 0
                    }
        
        return segment_trends
    
    def _detect_trend_changes(self, series: pd.Series) -> List[Dict]:
        """Detect significant trend changes using change point detection"""
        values = series.values
        change_points = []
        
        # Simple change point detection using rolling correlation
        window = self.config.trend_detection_window
        
        if len(values) > window * 2:
            for i in range(window, len(values) - window):
                # Compare trends before and after point
                before = values[i-window:i]
                after = values[i:i+window]
                
                # Linear regression for both segments
                time_before = np.arange(len(before))
                time_after = np.arange(len(after))
                
                slope_before = stats.linregress(time_before, before)[0]
                slope_after = stats.linregress(time_after, after)[0]
                
                # Check for significant change in slope
                slope_change = abs(slope_after - slope_before)
                relative_change = slope_change / (abs(slope_before) + 1e-6)
                
                if relative_change > 1.0:  # Significant change threshold
                    change_points.append({
                        'index': i,
                        'date': series.index[i] if hasattr(series, 'index') else i,
                        'slope_before': slope_before,
                        'slope_after': slope_after,
                        'slope_change': slope_change,
                        'relative_change': relative_change
                    })
        
        return change_points
    
    def analyze_seasonality(self, data: pd.DataFrame) -> Dict:
        """Analyze seasonality patterns in revenue"""
        logger.info("Analyzing seasonality patterns...")
        
        with mlflow.start_run(run_name="seasonality_analysis", nested=True):
            seasonality = {}
            
            # Time series decomposition
            revenue_ts = data.set_index('date')['total_revenue']
            
            # Seasonal decomposition for different periods
            for period in self.config.seasonality_periods:
                if len(revenue_ts) > period * 2:
                    try:
                        decomposition = seasonal_decompose(
                            revenue_ts, 
                            model='multiplicative',
                            period=period,
                            extrapolate_trend='freq'
                        )
                        
                        seasonality[f'period_{period}'] = {
                            'seasonal_strength': np.var(decomposition.seasonal) / np.var(revenue_ts),
                            'trend_strength': np.var(decomposition.trend.dropna()) / np.var(revenue_ts),
                            'residual_strength': np.var(decomposition.resid.dropna()) / np.var(revenue_ts),
                            'seasonal_component': decomposition.seasonal.tolist(),
                            'trend_component': decomposition.trend.dropna().tolist()
                        }
                    except Exception as e:
                        logger.warning(f"Could not decompose for period {period}: {str(e)}")
            
            # Weekly patterns
            weekly_patterns = data.groupby('dayofweek')['total_revenue'].agg(['mean', 'std'])
            seasonality['weekly_patterns'] = {
                'avg_revenue_by_dow': weekly_patterns['mean'].to_dict(),
                'revenue_volatility_by_dow': weekly_patterns['std'].to_dict(),
                'strongest_day': weekly_patterns['mean'].idxmax(),
                'weakest_day': weekly_patterns['mean'].idxmin()
            }
            
            # Monthly patterns
            monthly_patterns = data.groupby('month')['total_revenue'].agg(['mean', 'std'])
            seasonality['monthly_patterns'] = {
                'avg_revenue_by_month': monthly_patterns['mean'].to_dict(),
                'revenue_volatility_by_month': monthly_patterns['std'].to_dict(),
                'strongest_month': monthly_patterns['mean'].idxmax(),
                'weakest_month': monthly_patterns['mean'].idxmin()
            }
            
            # Quarterly patterns
            quarterly_patterns = data.groupby('quarter')['total_revenue'].agg(['mean', 'std'])
            seasonality['quarterly_patterns'] = {
                'avg_revenue_by_quarter': quarterly_patterns['mean'].to_dict(),
                'revenue_volatility_by_quarter': quarterly_patterns['std'].to_dict(),
                'strongest_quarter': quarterly_patterns['mean'].idxmax(),
                'weakest_quarter': quarterly_patterns['mean'].idxmin()
            }
            
            # Holiday effect analysis (simplified)
            seasonality['special_periods'] = self._analyze_special_periods(data)
            
            # Log seasonality metrics
            mlflow.log_metrics({
                'weekly_seasonality_strength': seasonality.get('period_7', {}).get('seasonal_strength', 0),
                'monthly_seasonality_strength': seasonality.get('period_30', {}).get('seasonal_strength', 0),
                'strongest_dow': seasonality['weekly_patterns']['strongest_day'],
                'strongest_month': seasonality['monthly_patterns']['strongest_month']
            })
            
            self.seasonality_analysis = seasonality
            return seasonality
    
    def _analyze_special_periods(self, data: pd.DataFrame) -> Dict:
        """Analyze revenue during special periods (simplified)"""
        special_periods = {}
        
        # Month-end analysis
        month_end_data = data[data['is_month_end'] == 1]
        month_start_data = data[data['is_month_start'] == 1]
        
        special_periods['month_end_effect'] = {
            'avg_revenue': month_end_data['total_revenue'].mean(),
            'vs_overall_avg': month_end_data['total_revenue'].mean() / data['total_revenue'].mean(),
            'is_significant': len(month_end_data) > 10
        }
        
        special_periods['month_start_effect'] = {
            'avg_revenue': month_start_data['total_revenue'].mean(),
            'vs_overall_avg': month_start_data['total_revenue'].mean() / data['total_revenue'].mean(),
            'is_significant': len(month_start_data) > 10
        }
        
        # Weekend vs weekday analysis
        weekend_data = data[data['is_weekend'] == 1]
        weekday_data = data[data['is_weekend'] == 0]
        
        special_periods['weekend_effect'] = {
            'weekend_avg': weekend_data['total_revenue'].mean(),
            'weekday_avg': weekday_data['total_revenue'].mean(),
            'weekend_vs_weekday_ratio': weekend_data['total_revenue'].mean() / weekday_data['total_revenue'].mean()
        }
        
        return special_periods
    
    def perform_revenue_segmentation(self, data: pd.DataFrame) -> Dict:
        """Perform revenue segmentation analysis"""
        logger.info("Performing revenue segmentation analysis...")
        
        with mlflow.start_run(run_name="segmentation_analysis", nested=True):
            segments = {}
            
            # Revenue distribution analysis
            segments['revenue_distribution'] = self._analyze_revenue_distribution(data)
            
            # Customer value segmentation
            segments['customer_segments'] = self._segment_customers(data)
            
            # Product category performance
            segments['category_performance'] = self._analyze_category_performance(data)
            
            # Geographic performance
            segments['geographic_performance'] = self._analyze_geographic_performance(data)
            
            # Payment method analysis
            segments['payment_method_analysis'] = self._analyze_payment_methods(data)
            
            # Time-based segments
            segments['temporal_segments'] = self._analyze_temporal_segments(data)
            
            self.segment_analysis = segments
            return segments
    
    def _analyze_revenue_distribution(self, data: pd.DataFrame) -> Dict:
        """Analyze revenue distribution patterns"""
        revenue = data['total_revenue']
        
        # Statistical summary
        distribution = {
            'mean': revenue.mean(),
            'median': revenue.median(),
            'std': revenue.std(),
            'skewness': stats.skew(revenue),
            'kurtosis': stats.kurtosis(revenue),
            'percentiles': {
                '25th': revenue.quantile(0.25),
                '50th': revenue.quantile(0.5),
                '75th': revenue.quantile(0.75),
                '90th': revenue.quantile(0.9),
                '95th': revenue.quantile(0.95),
                '99th': revenue.quantile(0.99)
            }
        }
        
        # Revenue concentration analysis
        sorted_revenue = revenue.sort_values(ascending=False)
        total_revenue = sorted_revenue.sum()
        
        distribution['concentration'] = {
            'top_10_percent_share': sorted_revenue.head(int(len(sorted_revenue) * 0.1)).sum() / total_revenue,
            'top_20_percent_share': sorted_revenue.head(int(len(sorted_revenue) * 0.2)).sum() / total_revenue,
            'bottom_50_percent_share': sorted_revenue.tail(int(len(sorted_revenue) * 0.5)).sum() / total_revenue
        }
        
        # Outlier detection
        q1, q3 = revenue.quantile([0.25, 0.75])
        iqr = q3 - q1
        outlier_threshold = q3 + 1.5 * iqr
        
        distribution['outliers'] = {
            'count': (revenue > outlier_threshold).sum(),
            'percentage': (revenue > outlier_threshold).mean() * 100,
            'threshold': outlier_threshold
        }
        
        return distribution
    
    def _segment_customers(self, data: pd.DataFrame) -> Dict:
        """Segment customers based on behavior patterns"""
        # Aggregate customer metrics
        customer_metrics = []
        
        # For this analysis, we'll use daily aggregated data
        # In a real implementation, you'd aggregate by customer
        for i in range(0, len(data), 30):  # 30-day windows
            window = data.iloc[i:i+30]
            if len(window) > 0:
                customer_metrics.append([
                    window['total_revenue'].sum(),
                    window['total_orders'].sum(),
                    window['unique_customers'].sum(),
                    window['avg_order_value'].mean(),
                    window['conversion_rate'].mean()
                ])
        
        if len(customer_metrics) < self.config.customer_segments:
            return {'error': 'Insufficient data for customer segmentation'}
        
        # K-means clustering
        customer_array = np.array(customer_metrics)
        scaler = StandardScaler()
        customer_scaled = scaler.fit_transform(customer_array)
        
        kmeans = KMeans(n_clusters=self.config.customer_segments, random_state=42)
        clusters = kmeans.fit_predict(customer_scaled)
        
        # Analyze clusters
        segments = {}
        for i in range(self.config.customer_segments):
            cluster_data = customer_array[clusters == i]
            
            segments[f'segment_{i}'] = {
                'size': len(cluster_data),
                'avg_revenue': cluster_data[:, 0].mean(),
                'avg_orders': cluster_data[:, 1].mean(),
                'avg_customers': cluster_data[:, 2].mean(),
                'avg_order_value': cluster_data[:, 3].mean(),
                'avg_conversion_rate': cluster_data[:, 4].mean()
            }
        
        return segments
    
    def _analyze_category_performance(self, data: pd.DataFrame) -> Dict:
        """Analyze performance by product category"""
        category_cols = ['electronics_revenue', 'clothing_revenue', 'home_revenue', 'books_revenue', 'sports_revenue']
        
        performance = {}
        total_category_revenue = data[category_cols].sum().sum()
        
        for category in category_cols:
            category_name = category.replace('_revenue', '')
            category_revenue = data[category].sum()
            
            performance[category_name] = {
                'total_revenue': category_revenue,
                'market_share': category_revenue / total_category_revenue if total_category_revenue > 0 else 0,
                'avg_daily_revenue': data[category].mean(),
                'revenue_volatility': data[category].std(),
                'growth_trend': self._calculate_growth_trend(data[category]),
                'peak_revenue_day': data.loc[data[category].idxmax(), 'date'].strftime('%Y-%m-%d') if data[category].max() > 0 else None
            }
        
        return performance
    
    def _analyze_geographic_performance(self, data: pd.DataFrame) -> Dict:
        """Analyze performance by geographic region"""
        geo_cols = ['north_america_revenue', 'europe_revenue', 'asia_revenue']
        
        performance = {}
        total_geo_revenue = data[geo_cols].sum().sum()
        
        for region in geo_cols:
            region_name = region.replace('_revenue', '')
            region_revenue = data[region].sum()
            
            performance[region_name] = {
                'total_revenue': region_revenue,
                'market_share': region_revenue / total_geo_revenue if total_geo_revenue > 0 else 0,
                'avg_daily_revenue': data[region].mean(),
                'revenue_volatility': data[region].std(),
                'growth_trend': self._calculate_growth_trend(data[region])
            }
        
        return performance
    
    def _analyze_payment_methods(self, data: pd.DataFrame) -> Dict:
        """Analyze performance by payment method"""
        payment_cols = ['cc_revenue', 'paypal_revenue', 'bank_revenue']
        
        analysis = {}
        total_payment_revenue = data[payment_cols].sum().sum()
        
        for method in payment_cols:
            method_name = method.replace('_revenue', '')
            method_revenue = data[method].sum()
            
            analysis[method_name] = {
                'total_revenue': method_revenue,
                'market_share': method_revenue / total_payment_revenue if total_payment_revenue > 0 else 0,
                'avg_daily_revenue': data[method].mean(),
                'growth_trend': self._calculate_growth_trend(data[method])
            }
        
        return analysis
    
    def _analyze_temporal_segments(self, data: pd.DataFrame) -> Dict:
        """Analyze revenue patterns across different time segments"""
        temporal = {}
        
        # Hour of day analysis (if we had hourly data)
        # For now, we'll analyze day of week and month patterns
        
        # Day of week performance
        dow_performance = data.groupby('dayofweek')['total_revenue'].agg(['mean', 'std', 'sum'])
        temporal['day_of_week'] = {
            'best_performing_day': dow_performance['mean'].idxmax(),
            'worst_performing_day': dow_performance['mean'].idxmin(),
            'weekday_vs_weekend': {
                'weekday_avg': data[data['dayofweek'] < 5]['total_revenue'].mean(),
                'weekend_avg': data[data['dayofweek'] >= 5]['total_revenue'].mean()
            }
        }
        
        # Month performance
        month_performance = data.groupby('month')['total_revenue'].agg(['mean', 'std', 'sum'])
        temporal['month'] = {
            'best_performing_month': month_performance['mean'].idxmax(),
            'worst_performing_month': month_performance['mean'].idxmin(),
            'seasonal_variation': month_performance['std'].mean() / month_performance['mean'].mean()
        }
        
        return temporal
    
    def _calculate_growth_trend(self, series: pd.Series) -> Dict:
        """Calculate growth trend for a time series"""
        if len(series) < 2:
            return {'growth_rate': 0, 'trend': 'insufficient_data'}
        
        # Simple linear regression
        time_index = np.arange(len(series))
        slope, intercept, r_value, p_value, std_err = stats.linregress(time_index, series)
        
        # Calculate growth rate
        mean_value = series.mean()
        growth_rate = (slope / mean_value) * 100 if mean_value > 0 else 0
        
        return {
            'growth_rate': growth_rate,
            'trend': 'increasing' if slope > 0 else 'decreasing',
            'trend_strength': abs(r_value),
            'is_significant': p_value < 0.05
        }
    
    def generate_revenue_forecasts(self, data: pd.DataFrame) -> Dict:
        """Generate revenue forecasts using multiple models"""
        logger.info("Generating revenue forecasts...")
        
        with mlflow.start_run(run_name="revenue_forecasting", nested=True):
            forecasts = {}
            
            # Prophet forecast
            forecasts['prophet'] = self._forecast_with_prophet(data)
            
            # Linear trend forecast
            forecasts['linear_trend'] = self._forecast_with_linear_trend(data)
            
            # Seasonal naive forecast
            forecasts['seasonal_naive'] = self._forecast_with_seasonal_naive(data)
            
            # Moving average forecast
            forecasts['moving_average'] = self._forecast_with_moving_average(data)
            
            # Ensemble forecast
            if len(forecasts) > 1:
                forecasts['ensemble'] = self._create_ensemble_forecast(forecasts)
            
            self.forecasts = forecasts
            return forecasts
    
    def _forecast_with_prophet(self, data: pd.DataFrame) -> Dict:
        """Generate forecast using Prophet"""
        try:
            # Prepare data for Prophet
            prophet_data = data[['date', 'total_revenue']].copy()
            prophet_data.columns = ['ds', 'y']
            
            # Add additional regressors
            prophet_data['weekend'] = data['is_weekend']
            prophet_data['month_end'] = data['is_month_end']
            
            # Initialize and fit Prophet model
            model = Prophet(
                yearly_seasonality=True,
                weekly_seasonality=True,
                daily_seasonality=False,
                seasonality_mode='multiplicative'
            )
            
            model.add_regressor('weekend')
            model.add_regressor('month_end')
            
            model.fit(prophet_data)
            
            # Create future dataframe
            future = model.make_future_dataframe(periods=self.config.forecast_horizon_days)
            future['weekend'] = (future['ds'].dt.dayofweek >= 5).astype(int)
            future['month_end'] = future['ds'].dt.is_month_end.astype(int)
            
            # Generate forecast
            forecast = model.predict(future)
            
            # Extract forecast for the future period
            future_forecast = forecast.tail(self.config.forecast_horizon_days)
            
            return {
                'forecast': future_forecast['yhat'].tolist(),
                'lower_bound': future_forecast['yhat_lower'].tolist(),
                'upper_bound': future_forecast['yhat_upper'].tolist(),
                'dates': future_forecast['ds'].dt.strftime('%Y-%m-%d').tolist(),
                'model_type': 'prophet'
            }
            
        except Exception as e:
            logger.error(f"Prophet forecast failed: {str(e)}")
            return {'error': str(e)}
    
    def _forecast_with_linear_trend(self, data: pd.DataFrame) -> Dict:
        """Generate forecast using linear trend"""
        revenue = data['total_revenue'].values
        time_index = np.arange(len(revenue))
        
        # Fit linear regression
        slope, intercept, r_value, p_value, std_err = stats.linregress(time_index, revenue)
        
        # Generate forecast
        future_time = np.arange(len(revenue), len(revenue) + self.config.forecast_horizon_days)
        forecast = slope * future_time + intercept
        
        # Calculate confidence intervals (simplified)
        forecast_std = std_err * np.sqrt(1 + 1/len(revenue) + (future_time - time_index.mean())**2 / np.sum((time_index - time_index.mean())**2))
        
        return {
            'forecast': forecast.tolist(),
            'lower_bound': (forecast - 1.96 * forecast_std).tolist(),
            'upper_bound': (forecast + 1.96 * forecast_std).tolist(),
            'model_type': 'linear_trend'
        }
    
    def _forecast_with_seasonal_naive(self, data: pd.DataFrame) -> Dict:
        """Generate forecast using seasonal naive method"""
        revenue = data['total_revenue'].values
        
        # Use weekly seasonality (7 days)
        season_length = 7
        
        if len(revenue) >= season_length:
            # Repeat the last seasonal pattern
            last_season = revenue[-season_length:]
            forecast = np.tile(last_season, (self.config.forecast_horizon_days // season_length) + 1)[:self.config.forecast_horizon_days]
        else:
            # If not enough data, use simple average
            forecast = np.full(self.config.forecast_horizon_days, revenue.mean())
        
        return {
            'forecast': forecast.tolist(),
            'model_type': 'seasonal_naive'
        }
    
    def _forecast_with_moving_average(self, data: pd.DataFrame, window: int = 7) -> Dict:
        """Generate forecast using moving average"""
        revenue = data['total_revenue'].values
        
        # Calculate moving average
        if len(revenue) >= window:
            ma_value = revenue[-window:].mean()
        else:
            ma_value = revenue.mean()
        
        # Simple forecast: extend moving average
        forecast = np.full(self.config.forecast_horizon_days, ma_value)
        
        return {
            'forecast': forecast.tolist(),
            'model_type': 'moving_average'
        }
    
    def _create_ensemble_forecast(self, forecasts: Dict) -> Dict:
        """Create ensemble forecast from multiple models"""
        # Extract forecast arrays
        forecast_arrays = []
        weights = []
        
        for model_name, forecast_data in forecasts.items():
            if 'forecast' in forecast_data and 'error' not in forecast_data:
                forecast_arrays.append(np.array(forecast_data['forecast']))
                # Simple equal weighting for now
                weights.append(1.0)
        
        if not forecast_arrays:
            return {'error': 'No valid forecasts to ensemble'}
        
        # Normalize weights
        weights = np.array(weights) / np.sum(weights)
        
        # Calculate ensemble forecast
        ensemble_forecast = np.average(forecast_arrays, axis=0, weights=weights)
        
        return {
            'forecast': ensemble_forecast.tolist(),
            'model_type': 'ensemble',
            'component_models': list(forecasts.keys())
        }
    
    def generate_insights(self) -> Dict:
        """Generate business insights from analysis"""
        logger.info("Generating business insights...")
        
        insights = {
            'executive_summary': self._generate_executive_summary(),
            'key_findings': self._identify_key_findings(),
            'recommendations': self._generate_recommendations(),
            'risk_factors': self._identify_risk_factors(),
            'opportunities': self._identify_opportunities()
        }
        
        self.insights = insights
        return insights
    
    def _generate_executive_summary(self) -> Dict:
        """Generate executive summary"""
        if self.revenue_data is None:
            return {'error': 'No revenue data available'}
        
        total_revenue = self.revenue_data['total_revenue'].sum()
        avg_daily_revenue = self.revenue_data['total_revenue'].mean()
        revenue_growth = self.trend_analysis.get('overall', {}).get('daily_growth_rate_pct', 0)
        
        summary = {
            'total_revenue': total_revenue,
            'analysis_period_days': len(self.revenue_data),
            'avg_daily_revenue': avg_daily_revenue,
            'revenue_growth_rate': revenue_growth,
            'trend_direction': self.trend_analysis.get('overall', {}).get('direction', 'unknown'),
            'seasonality_detected': len(self.seasonality_analysis) > 0,
            'forecast_confidence': 'high' if abs(revenue_growth) < 5 else 'medium'
        }
        
        return summary
    
    def _identify_key_findings(self) -> List[str]:
        """Identify key findings from the analysis"""
        findings = []
        
        # Trend findings
        if self.trend_analysis.get('overall', {}).get('is_significant', False):
            direction = self.trend_analysis['overall']['direction']
            growth_rate = self.trend_analysis['overall']['daily_growth_rate_pct']
            findings.append(f"Revenue shows a statistically significant {direction} trend with {growth_rate:.2f}% daily growth rate")
        
        # Seasonality findings
        if 'weekly_patterns' in self.seasonality_analysis:
            strongest_day = self.seasonality_analysis['weekly_patterns']['strongest_day']
            weakest_day = self.seasonality_analysis['weekly_patterns']['weakest_day']
            findings.append(f"Strong weekly seasonality detected: highest revenue on day {strongest_day}, lowest on day {weakest_day}")
        
        # Segment findings
        if 'category_performance' in self.segment_analysis:
            categories = self.segment_analysis['category_performance']
            best_category = max(categories.items(), key=lambda x: x[1]['total_revenue'])
            findings.append(f"'{best_category[0]}' is the top-performing category with {best_category[1]['market_share']:.1%} market share")
        
        return findings
    
    def _generate_recommendations(self) -> List[str]:
        """Generate actionable recommendations"""
        recommendations = []
        
        # Based on trend analysis
        trend_direction = self.trend_analysis.get('overall', {}).get('direction', 'unknown')
        if trend_direction == 'decreasing':
            recommendations.append("Consider implementing retention campaigns and promotional strategies to reverse declining revenue trend")
        elif trend_direction == 'increasing':
            recommendations.append("Scale successful strategies and consider expanding into new markets to accelerate growth")
        
        # Based on seasonality
        if 'weekend_effect' in self.seasonality_analysis.get('special_periods', {}):
            weekend_ratio = self.seasonality_analysis['special_periods']['weekend_effect']['weekend_vs_weekday_ratio']
            if weekend_ratio < 0.8:
                recommendations.append("Implement weekend-specific promotions to boost lower weekend performance")
        
        # Based on segment performance
        if 'category_performance' in self.segment_analysis:
            categories = self.segment_analysis['category_performance']
            declining_categories = [cat for cat, data in categories.items() 
                                 if data.get('growth_trend', {}).get('trend') == 'decreasing']
            if declining_categories:
                recommendations.append(f"Focus on revitalizing declining categories: {', '.join(declining_categories)}")
        
        return recommendations
    
    def _identify_risk_factors(self) -> List[str]:
        """Identify potential risk factors"""
        risks = []
        
        # Revenue concentration risk
        if 'concentration' in self.segment_analysis.get('revenue_distribution', {}):
            top_10_share = self.segment_analysis['revenue_distribution']['concentration']['top_10_percent_share']
            if top_10_share > 0.5:
                risks.append(f"High revenue concentration: top 10% of days account for {top_10_share:.1%} of total revenue")
        
        # Volatility risk
        if self.revenue_data is not None:
            cv = self.revenue_data['total_revenue'].std() / self.revenue_data['total_revenue'].mean()
            if cv > 0.5:
                risks.append(f"High revenue volatility (CV: {cv:.2f}) indicates unpredictable business performance")
        
        # Declining trend risk
        if self.trend_analysis.get('overall', {}).get('direction') == 'decreasing':
            risks.append("Declining revenue trend poses significant business risk")
        
        return risks
    
    def _identify_opportunities(self) -> List[str]:
        """Identify growth opportunities"""
        opportunities = []
        
        # Growth trend opportunity
        if self.trend_analysis.get('overall', {}).get('direction') == 'increasing':
            opportunities.append("Positive revenue trend provides opportunity for strategic expansion")
        
        # Underperforming segments
        if 'geographic_performance' in self.segment_analysis:
            geo_performance = self.segment_analysis['geographic_performance']
            underperforming = [region for region, data in geo_performance.items() 
                             if data['market_share'] < 0.2]
            if underperforming:
                opportunities.append(f"Geographic expansion opportunities in: {', '.join(underperforming)}")
        
        # Seasonal opportunities
        if 'monthly_patterns' in self.seasonality_analysis:
            weakest_month = self.seasonality_analysis['monthly_patterns']['weakest_month']
            opportunities.append(f"Seasonal marketing opportunity in month {weakest_month} (historically weakest)")
        
        return opportunities
    
    def create_visualizations(self):
        """Create comprehensive visualizations"""
        logger.info("Creating revenue analysis visualizations...")
        
        if self.revenue_data is None:
            logger.warning("No data available for visualization")
            return
        
        # Create comprehensive dashboard
        fig = make_subplots(
            rows=3, cols=2,
            subplot_titles=[
                'Revenue Trend Over Time',
                'Revenue Distribution',
                'Seasonal Patterns',
                'Category Performance',
                'Geographic Performance',
                'Forecast Results'
            ],
            specs=[
                [{"secondary_y": False}, {"type": "histogram"}],
                [{"secondary_y": False}, {"type": "bar"}],
                [{"type": "bar"}, {"secondary_y": False}]
            ]
        )
        
        # 1. Revenue trend
        fig.add_trace(
            go.Scatter(
                x=self.revenue_data['date'],
                y=self.revenue_data['total_revenue'],
                mode='lines',
                name='Daily Revenue',
                line=dict(color='blue')
            ),
            row=1, col=1
        )
        
        # Add trend line
        if 'overall' in self.trend_analysis:
            trend_line = (
                self.trend_analysis['overall']['slope'] * np.arange(len(self.revenue_data)) + 
                self.trend_analysis['overall']['intercept']
            )
            fig.add_trace(
                go.Scatter(
                    x=self.revenue_data['date'],
                    y=trend_line,
                    mode='lines',
                    name='Trend Line',
                    line=dict(color='red', dash='dash')
                ),
                row=1, col=1
            )
        
        # 2. Revenue distribution
        fig.add_trace(
            go.Histogram(
                x=self.revenue_data['total_revenue'],
                nbinsx=50,
                name='Revenue Distribution',
                marker_color='lightblue'
            ),
            row=1, col=2
        )
        
        # 3. Weekly seasonality
        if 'weekly_patterns' in self.seasonality_analysis:
            weekly_data = self.seasonality_analysis['weekly_patterns']['avg_revenue_by_dow']
            fig.add_trace(
                go.Bar(
                    x=list(weekly_data.keys()),
                    y=list(weekly_data.values()),
                    name='Weekly Pattern',
                    marker_color='green'
                ),
                row=2, col=1
            )
        
        # 4. Category performance
        if 'category_performance' in self.segment_analysis:
            categories = self.segment_analysis['category_performance']
            cat_names = list(categories.keys())
            cat_revenues = [data['total_revenue'] for data in categories.values()]
            
            fig.add_trace(
                go.Bar(
                    x=cat_names,
                    y=cat_revenues,
                    name='Category Revenue',
                    marker_color='orange'
                ),
                row=2, col=2
            )
        
        # 5. Geographic performance
        if 'geographic_performance' in self.segment_analysis:
            geo_performance = self.segment_analysis['geographic_performance']
            geo_names = list(geo_performance.keys())
            geo_revenues = [data['total_revenue'] for data in geo_performance.values()]
            
            fig.add_trace(
                go.Bar(
                    x=geo_names,
                    y=geo_revenues,
                    name='Geographic Revenue',
                    marker_color='purple'
                ),
                row=3, col=1
            )
        
        # 6. Forecast
        if 'ensemble' in self.forecasts and 'forecast' in self.forecasts['ensemble']:
            forecast_dates = pd.date_range(
                start=self.revenue_data['date'].max() + timedelta(days=1),
                periods=len(self.forecasts['ensemble']['forecast']),
                freq='D'
            )
            
            fig.add_trace(
                go.Scatter(
                    x=forecast_dates,
                    y=self.forecasts['ensemble']['forecast'],
                    mode='lines',
                    name='Revenue Forecast',
                    line=dict(color='red')
                ),
                row=3, col=2
            )
        
        fig.update_layout(
            title='Comprehensive Revenue Analysis Dashboard',
            height=1200,
            showlegend=True
        )
        
        # Save visualization
        try:
            fig.write_html('/tmp/revenue_analysis_dashboard.html')
            mlflow.log_artifact('/tmp/revenue_analysis_dashboard.html')
        except Exception as e:
            logger.warning(f"Could not save visualization: {str(e)}")
    
    def save_analysis_results(self, analysis_name: str = "revenue_analysis"):
        """Save all analysis results"""
        logger.info("Saving analysis results...")
        
        # Compile all results
        results = {
            'analysis_timestamp': datetime.now().isoformat(),
            'config': self.config.__dict__,
            'data_summary': {
                'total_days': len(self.revenue_data) if self.revenue_data is not None else 0,
                'total_revenue': self.revenue_data['total_revenue'].sum() if self.revenue_data is not None else 0
            },
            'trend_analysis': self.trend_analysis,
            'seasonality_analysis': self.seasonality_analysis,
            'segment_analysis': self.segment_analysis,
            'forecasts': self.forecasts,
            'insights': self.insights
        }
        
        # Save locally
        results_path = f'/tmp/{analysis_name}_results.json'
        with open(results_path, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        # Log to MLflow
        mlflow.log_artifact(results_path, 'analysis_results')
        
        # Save to S3
        try:
            s3_key = f"{self.config.report_prefix}{analysis_name}_{int(datetime.now().timestamp())}.json"
            self.s3_client.upload_file(results_path, self.config.s3_bucket, s3_key)
            logger.info(f"Results saved to s3://{self.config.s3_bucket}/{s3_key}")
        except Exception as e:
            logger.warning(f"Could not save to S3: {str(e)}")
        
        return results_path
    
    def run_complete_analysis(self):
        """Run the complete revenue analysis pipeline"""
        logger.info("Starting comprehensive revenue analysis...")
        
        with mlflow.start_run(run_name="revenue_analysis_pipeline"):
            try:
                # Extract data
                revenue_data = self.extract_revenue_data()
                
                # Perform analyses
                trends = self.detect_trends(revenue_data)
                seasonality = self.analyze_seasonality(revenue_data)
                segments = self.perform_revenue_segmentation(revenue_data)
                forecasts = self.generate_revenue_forecasts(revenue_data)
                insights = self.generate_insights()
                
                # Create visualizations
                self.create_visualizations()
                
                # Save results
                results_path = self.save_analysis_results()
                
                # Log success metrics
                mlflow.log_metrics({
                    'analysis_success': 1,
                    'data_quality_score': (revenue_data['total_revenue'] > 0).mean(),
                    'trend_significance': int(trends.get('overall', {}).get('is_significant', False)),
                    'forecast_horizon_days': self.config.forecast_horizon_days
                })
                
                logger.info("Revenue analysis completed successfully")
                
                return {
                    'results_path': results_path,
                    'summary': insights.get('executive_summary', {}),
                    'key_findings': insights.get('key_findings', []),
                    'recommendations': insights.get('recommendations', [])
                }
                
            except Exception as e:
                logger.error(f"Revenue analysis failed: {str(e)}")
                mlflow.log_metric("analysis_success", 0)
                raise

def main():
    """Main function to run revenue analysis"""
    config = RevenueAnalysisConfig()
    analyzer = RevenueAnalyzer(config)
    
    try:
        results = analyzer.run_complete_analysis()
        print(f"Revenue analysis completed successfully!")
        print(f"Results saved to: {results['results_path']}")
        print(f"\nKey Findings:")
        for finding in results['key_findings'][:5]:  # Top 5 findings
            print(f"- {finding}")
        print(f"\nRecommendations:")
        for rec in results['recommendations'][:5]:  # Top 5 recommendations
            print(f"- {rec}")
        
    except Exception as e:
        logger.error(f"Revenue analysis failed: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()