#!/usr/bin/env python3
"""
Sales Forecasting Model for E-commerce Analytics
Advanced time series forecasting using multiple algorithms including Prophet, ARIMA, and LSTM
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import os
import sys
from typing import Dict, List, Tuple, Optional, Any
import joblib
import json
import warnings
from dataclasses import dataclass

# Time series libraries
from statsmodels.tsa.arima.model import ARIMA
from statsmodels.tsa.seasonal import seasonal_decompose
from statsmodels.tsa.stattools import adfuller
from statsmodels.graphics.tsaplots import plot_acf, plot_pacf
import pmdarima as pm
from prophet import Prophet
from prophet.diagnostics import cross_validation, performance_metrics

# ML libraries
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Dropout, BatchNormalization
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import xgboost as xgb
import lightgbm as lgb

# Visualization
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots

# MLflow integration
import mlflow
import mlflow.sklearn
import mlflow.tensorflow
from mlflow.tracking import MlflowClient

# Database and cloud
import boto3
from sqlalchemy import create_engine

warnings.filterwarnings('ignore')

# Configuration
@dataclass
class ForecastingConfig:
    # Forecasting parameters
    forecast_horizon_days: int = 30
    training_window_days: int = 365
    validation_split: float = 0.2
    
    # Model parameters
    prophet_seasonality_mode: str = 'multiplicative'
    prophet_yearly_seasonality: bool = True
    prophet_weekly_seasonality: bool = True
    prophet_daily_seasonality: bool = False
    
    # LSTM parameters
    lstm_sequence_length: int = 30
    lstm_units: int = 50
    lstm_dropout: float = 0.2
    lstm_epochs: int = 100
    lstm_batch_size: int = 32
    
    # Ensemble parameters
    use_ensemble: bool = True
    ensemble_weights: Dict[str, float] = None
    
    # MLflow configuration
    mlflow_tracking_uri: str = os.getenv('MLFLOW_TRACKING_URI', 'http://mlflow-server.mlflow:5000')
    experiment_name: str = 'sales-forecasting'
    
    # Database connections
    db_host: str = os.getenv('DB_HOST', 'analytics-db.cluster-xyz.us-east-1.rds.amazonaws.com')
    db_name: str = os.getenv('DB_NAME', 'ecommerce_analytics')
    db_user: str = os.getenv('DB_USER', 'analytics_user')
    db_password: str = os.getenv('DB_PASSWORD', 'analytics_password')
    
    # S3 configuration
    s3_bucket: str = os.getenv('S3_BUCKET', 'ecommerce-analytics-models')
    model_prefix: str = 'sales-forecasting/'
    
    def __post_init__(self):
        if self.ensemble_weights is None:
            self.ensemble_weights = {
                'prophet': 0.3,
                'arima': 0.2,
                'lstm': 0.3,
                'xgboost': 0.2
            }

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SalesForecaster:
    def __init__(self, config: ForecastingConfig):
        self.config = config
        self.db_engine = self._create_db_engine()
        self.s3_client = boto3.client('s3')
        
        # Initialize MLflow
        mlflow.set_tracking_uri(config.mlflow_tracking_uri)
        mlflow.set_experiment(config.experiment_name)
        
        # Model components
        self.models = {}
        self.scalers = {}
        self.feature_columns = []
        self.target_scaler = MinMaxScaler()
        
    def _create_db_engine(self):
        """Create database engine for PostgreSQL"""
        connection_string = f"postgresql://{self.config.db_user}:{self.config.db_password}@{self.config.db_host}/{self.config.db_name}"
        return create_engine(connection_string)
    
    def extract_sales_data(self) -> pd.DataFrame:
        """Extract historical sales data for forecasting"""
        logger.info("Extracting sales data for forecasting...")
        
        with mlflow.start_run(run_name="data_extraction", nested=True):
            end_date = datetime.now()
            start_date = end_date - timedelta(days=self.config.training_window_days + 60)  # Extra buffer
            
            # Extract daily sales data with additional features
            sales_query = """
                WITH daily_sales AS (
                    SELECT 
                        DATE(purchase_timestamp) as date,
                        SUM(total_amount) as total_revenue,
                        COUNT(DISTINCT order_id) as total_orders,
                        COUNT(DISTINCT user_id) as unique_customers,
                        AVG(total_amount) as avg_order_value,
                        SUM(CASE WHEN payment_method = 'credit_card' THEN total_amount ELSE 0 END) as credit_card_revenue,
                        SUM(CASE WHEN payment_method = 'paypal' THEN total_amount ELSE 0 END) as paypal_revenue,
                        COUNT(DISTINCT CASE WHEN total_amount > 100 THEN order_id END) as high_value_orders
                    FROM purchases 
                    WHERE purchase_timestamp >= %s 
                        AND purchase_timestamp <= %s
                        AND total_amount > 0
                    GROUP BY DATE(purchase_timestamp)
                ),
                daily_traffic AS (
                    SELECT 
                        DATE(event_timestamp) as date,
                        COUNT(*) as total_events,
                        COUNT(DISTINCT user_id) as unique_visitors,
                        COUNT(DISTINCT session_id) as total_sessions,
                        SUM(CASE WHEN event_type = 'page_view' THEN 1 ELSE 0 END) as page_views,
                        SUM(CASE WHEN event_type = 'add_to_cart' THEN 1 ELSE 0 END) as cart_adds,
                        SUM(CASE WHEN device_type = 'mobile' THEN 1 ELSE 0 END) as mobile_events,
                        SUM(CASE WHEN device_type = 'desktop' THEN 1 ELSE 0 END) as desktop_events
                    FROM user_events 
                    WHERE event_timestamp >= %s 
                        AND event_timestamp <= %s
                    GROUP BY DATE(event_timestamp)
                ),
                daily_products AS (
                    SELECT 
                        DATE(p.purchase_timestamp) as date,
                        COUNT(DISTINCT pi.product_id) as unique_products_sold,
                        COUNT(DISTINCT pi.category_id) as unique_categories_sold,
                        SUM(pi.quantity) as total_quantity_sold,
                        AVG(pi.unit_price) as avg_product_price
                    FROM purchases p
                    JOIN purchase_items pi ON p.order_id = pi.order_id
                    WHERE p.purchase_timestamp >= %s 
                        AND p.purchase_timestamp <= %s
                    GROUP BY DATE(p.purchase_timestamp)
                )
                SELECT 
                    COALESCE(ds.date, dt.date, dp.date) as date,
                    COALESCE(ds.total_revenue, 0) as total_revenue,
                    COALESCE(ds.total_orders, 0) as total_orders,
                    COALESCE(ds.unique_customers, 0) as unique_customers,
                    COALESCE(ds.avg_order_value, 0) as avg_order_value,
                    COALESCE(ds.credit_card_revenue, 0) as credit_card_revenue,
                    COALESCE(ds.paypal_revenue, 0) as paypal_revenue,
                    COALESCE(ds.high_value_orders, 0) as high_value_orders,
                    COALESCE(dt.total_events, 0) as total_events,
                    COALESCE(dt.unique_visitors, 0) as unique_visitors,
                    COALESCE(dt.total_sessions, 0) as total_sessions,
                    COALESCE(dt.page_views, 0) as page_views,
                    COALESCE(dt.cart_adds, 0) as cart_adds,
                    COALESCE(dt.mobile_events, 0) as mobile_events,
                    COALESCE(dt.desktop_events, 0) as desktop_events,
                    COALESCE(dp.unique_products_sold, 0) as unique_products_sold,
                    COALESCE(dp.unique_categories_sold, 0) as unique_categories_sold,
                    COALESCE(dp.total_quantity_sold, 0) as total_quantity_sold,
                    COALESCE(dp.avg_product_price, 0) as avg_product_price
                FROM daily_sales ds
                FULL OUTER JOIN daily_traffic dt ON ds.date = dt.date
                FULL OUTER JOIN daily_products dp ON ds.date = dp.date
                ORDER BY date
            """
            
            sales_data = pd.read_sql(
                sales_query,
                self.db_engine,
                params=[start_date, end_date] * 3
            )
            
            # Convert date column
            sales_data['date'] = pd.to_datetime(sales_data['date'])
            
            # Create complete date range
            date_range = pd.date_range(
                start=sales_data['date'].min(),
                end=sales_data['date'].max(),
                freq='D'
            )
            
            # Reindex to fill missing dates
            sales_data = sales_data.set_index('date').reindex(date_range).fillna(0).reset_index()
            sales_data.rename(columns={'index': 'date'}, inplace=True)
            
            # Add time-based features
            sales_data = self._add_time_features(sales_data)
            
            # Log extraction metrics
            mlflow.log_metrics({
                'total_days': len(sales_data),
                'total_revenue': sales_data['total_revenue'].sum(),
                'avg_daily_revenue': sales_data['total_revenue'].mean(),
                'max_daily_revenue': sales_data['total_revenue'].max(),
                'data_completeness': (sales_data['total_revenue'] > 0).mean()
            })
            
            logger.info(f"Extracted {len(sales_data)} days of sales data")
            
            return sales_data
    
    def _add_time_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Add time-based features for forecasting"""
        logger.info("Adding time-based features...")
        
        data = data.copy()
        
        # Extract time components
        data['year'] = data['date'].dt.year
        data['month'] = data['date'].dt.month
        data['day'] = data['date'].dt.day
        data['dayofweek'] = data['date'].dt.dayofweek
        data['dayofyear'] = data['date'].dt.dayofyear
        data['week'] = data['date'].dt.isocalendar().week
        data['quarter'] = data['date'].dt.quarter
        
        # Weekend indicator
        data['is_weekend'] = (data['dayofweek'] >= 5).astype(int)
        
        # Month end/start indicators
        data['is_month_start'] = data['date'].dt.is_month_start.astype(int)
        data['is_month_end'] = data['date'].dt.is_month_end.astype(int)
        
        # Holiday indicators (simplified - you would want a proper holiday calendar)
        data['is_holiday'] = 0  # Placeholder for holiday logic
        
        # Cyclical encoding for time features
        data['month_sin'] = np.sin(2 * np.pi * data['month'] / 12)
        data['month_cos'] = np.cos(2 * np.pi * data['month'] / 12)
        data['dayofweek_sin'] = np.sin(2 * np.pi * data['dayofweek'] / 7)
        data['dayofweek_cos'] = np.cos(2 * np.pi * data['dayofweek'] / 7)
        data['dayofyear_sin'] = np.sin(2 * np.pi * data['dayofyear'] / 365)
        data['dayofyear_cos'] = np.cos(2 * np.pi * data['dayofyear'] / 365)
        
        # Lag features
        for lag in [1, 7, 14, 30]:
            data[f'revenue_lag_{lag}'] = data['total_revenue'].shift(lag)
            data[f'orders_lag_{lag}'] = data['total_orders'].shift(lag)
        
        # Rolling statistics
        for window in [7, 14, 30]:
            data[f'revenue_rolling_mean_{window}'] = data['total_revenue'].rolling(window=window).mean()
            data[f'revenue_rolling_std_{window}'] = data['total_revenue'].rolling(window=window).std()
            data[f'orders_rolling_mean_{window}'] = data['total_orders'].rolling(window=window).mean()
        
        # Conversion rates
        data['conversion_rate'] = data['total_orders'] / (data['unique_visitors'] + 1)
        data['cart_conversion_rate'] = data['total_orders'] / (data['cart_adds'] + 1)
        
        # Growth rates
        data['revenue_growth_1d'] = data['total_revenue'].pct_change(1)
        data['revenue_growth_7d'] = data['total_revenue'].pct_change(7)
        data['orders_growth_1d'] = data['total_orders'].pct_change(1)
        
        # Fill NaN values created by lag and rolling features
        data = data.fillna(0)
        
        return data
    
    def prepare_prophet_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Prepare data for Prophet model"""
        prophet_data = data[['date', 'total_revenue']].copy()
        prophet_data.columns = ['ds', 'y']
        
        # Add additional regressors
        additional_features = [
            'total_orders', 'unique_customers', 'total_events',
            'unique_visitors', 'is_weekend', 'is_month_end'
        ]
        
        for feature in additional_features:
            if feature in data.columns:
                prophet_data[feature] = data[feature]
        
        return prophet_data
    
    def train_prophet_model(self, data: pd.DataFrame) -> Prophet:
        """Train Prophet forecasting model"""
        logger.info("Training Prophet model...")
        
        with mlflow.start_run(run_name="prophet_training", nested=True):
            # Prepare data
            prophet_data = self.prepare_prophet_data(data)
            
            # Initialize Prophet model
            model = Prophet(
                yearly_seasonality=self.config.prophet_yearly_seasonality,
                weekly_seasonality=self.config.prophet_weekly_seasonality,
                daily_seasonality=self.config.prophet_daily_seasonality,
                seasonality_mode=self.config.prophet_seasonality_mode,
                interval_width=0.8
            )
            
            # Add additional regressors
            additional_features = [
                'total_orders', 'unique_customers', 'total_events',
                'unique_visitors', 'is_weekend', 'is_month_end'
            ]
            
            for feature in additional_features:
                if feature in prophet_data.columns:
                    model.add_regressor(feature)
            
            # Fit model
            model.fit(prophet_data)
            
            # Cross-validation
            df_cv = cross_validation(
                model, 
                initial='90 days', 
                period='30 days', 
                horizon='30 days'
            )
            df_p = performance_metrics(df_cv)
            
            # Log metrics
            mlflow.log_metrics({
                'prophet_mape': df_p['mape'].mean(),
                'prophet_mae': df_p['mae'].mean(),
                'prophet_rmse': df_p['rmse'].mean()
            })
            
            logger.info("Prophet model training completed")
            
            return model
    
    def train_arima_model(self, data: pd.DataFrame) -> Any:
        """Train ARIMA model with automatic parameter selection"""
        logger.info("Training ARIMA model...")
        
        with mlflow.start_run(run_name="arima_training", nested=True):
            # Prepare time series
            ts = data.set_index('date')['total_revenue']
            
            # Check stationarity
            adf_result = adfuller(ts)
            is_stationary = adf_result[1] < 0.05
            
            mlflow.log_metrics({
                'adf_statistic': adf_result[0],
                'adf_pvalue': adf_result[1],
                'is_stationary': int(is_stationary)
            })
            
            # Auto ARIMA
            model = pm.auto_arima(
                ts,
                start_p=1, start_q=1,
                max_p=5, max_q=5,
                seasonal=True,
                stepwise=True,
                suppress_warnings=True,
                error_action='ignore',
                max_order=None,
                trace=False
            )
            
            # Log ARIMA parameters
            mlflow.log_params({
                'arima_order': str(model.order),
                'arima_seasonal_order': str(model.seasonal_order)
            })
            
            # Calculate in-sample metrics
            fitted_values = model.fittedvalues()
            mae = mean_absolute_error(ts, fitted_values)
            mse = mean_squared_error(ts, fitted_values)
            rmse = np.sqrt(mse)
            
            mlflow.log_metrics({
                'arima_mae': mae,
                'arima_mse': mse,
                'arima_rmse': rmse
            })
            
            logger.info(f"ARIMA model training completed. Order: {model.order}")
            
            return model
    
    def prepare_lstm_data(self, data: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """Prepare data for LSTM model"""
        # Select features for LSTM
        feature_cols = [
            'total_revenue', 'total_orders', 'unique_customers',
            'avg_order_value', 'total_events', 'unique_visitors',
            'conversion_rate', 'is_weekend', 'month_sin', 'month_cos',
            'dayofweek_sin', 'dayofweek_cos'
        ]
        
        # Filter available features
        available_features = [col for col in feature_cols if col in data.columns]
        self.feature_columns = available_features
        
        # Scale features
        features = data[available_features].values
        self.scalers['lstm_features'] = MinMaxScaler()
        features_scaled = self.scalers['lstm_features'].fit_transform(features)
        
        # Scale target
        target = data['total_revenue'].values.reshape(-1, 1)
        target_scaled = self.target_scaler.fit_transform(target)
        
        # Create sequences
        X, y = [], []
        for i in range(self.config.lstm_sequence_length, len(features_scaled)):
            X.append(features_scaled[i-self.config.lstm_sequence_length:i])
            y.append(target_scaled[i])
        
        return np.array(X), np.array(y)
    
    def train_lstm_model(self, data: pd.DataFrame) -> tf.keras.Model:
        """Train LSTM forecasting model"""
        logger.info("Training LSTM model...")
        
        with mlflow.start_run(run_name="lstm_training", nested=True):
            # Prepare data
            X, y = self.prepare_lstm_data(data)
            
            # Train-validation split
            split_idx = int(len(X) * (1 - self.config.validation_split))
            X_train, X_val = X[:split_idx], X[split_idx:]
            y_train, y_val = y[:split_idx], y[split_idx:]
            
            # Build LSTM model
            model = Sequential([
                LSTM(self.config.lstm_units, return_sequences=True, 
                     input_shape=(X.shape[1], X.shape[2])),
                Dropout(self.config.lstm_dropout),
                BatchNormalization(),
                
                LSTM(self.config.lstm_units // 2, return_sequences=False),
                Dropout(self.config.lstm_dropout),
                BatchNormalization(),
                
                Dense(25, activation='relu'),
                Dropout(self.config.lstm_dropout),
                Dense(1)
            ])
            
            model.compile(
                optimizer=Adam(learning_rate=0.001),
                loss='mse',
                metrics=['mae']
            )
            
            # Callbacks
            early_stopping = EarlyStopping(
                monitor='val_loss',
                patience=10,
                restore_best_weights=True
            )
            
            reduce_lr = ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=5,
                min_lr=1e-7
            )
            
            # Train model
            history = model.fit(
                X_train, y_train,
                validation_data=(X_val, y_val),
                epochs=self.config.lstm_epochs,
                batch_size=self.config.lstm_batch_size,
                callbacks=[early_stopping, reduce_lr],
                verbose=0
            )
            
            # Calculate metrics
            train_loss = min(history.history['loss'])
            val_loss = min(history.history['val_loss'])
            train_mae = min(history.history['mae'])
            val_mae = min(history.history['val_mae'])
            
            mlflow.log_metrics({
                'lstm_train_loss': train_loss,
                'lstm_val_loss': val_loss,
                'lstm_train_mae': train_mae,
                'lstm_val_mae': val_mae,
                'lstm_epochs_trained': len(history.history['loss'])
            })
            
            logger.info("LSTM model training completed")
            
            return model
    
    def train_xgboost_model(self, data: pd.DataFrame) -> xgb.XGBRegressor:
        """Train XGBoost model for sales forecasting"""
        logger.info("Training XGBoost model...")
        
        with mlflow.start_run(run_name="xgboost_training", nested=True):
            # Prepare features
            feature_cols = [col for col in data.columns if col not in ['date', 'total_revenue']]
            X = data[feature_cols].fillna(0)
            y = data['total_revenue']
            
            # Train-validation split
            split_idx = int(len(X) * (1 - self.config.validation_split))
            X_train, X_val = X[:split_idx], X[split_idx:]
            y_train, y_val = y[:split_idx], y[split_idx:]
            
            # Train XGBoost model
            model = xgb.XGBRegressor(
                n_estimators=200,
                max_depth=6,
                learning_rate=0.1,
                subsample=0.8,
                colsample_bytree=0.8,
                random_state=42
            )
            
            model.fit(
                X_train, y_train,
                eval_set=[(X_val, y_val)],
                early_stopping_rounds=20,
                verbose=False
            )
            
            # Calculate metrics
            train_pred = model.predict(X_train)
            val_pred = model.predict(X_val)
            
            train_mae = mean_absolute_error(y_train, train_pred)
            val_mae = mean_absolute_error(y_val, val_pred)
            train_rmse = np.sqrt(mean_squared_error(y_train, train_pred))
            val_rmse = np.sqrt(mean_squared_error(y_val, val_pred))
            
            mlflow.log_metrics({
                'xgboost_train_mae': train_mae,
                'xgboost_val_mae': val_mae,
                'xgboost_train_rmse': train_rmse,
                'xgboost_val_rmse': val_rmse
            })
            
            logger.info("XGBoost model training completed")
            
            return model
    
    def create_ensemble_forecast(self, forecasts: Dict[str, np.ndarray]) -> np.ndarray:
        """Create ensemble forecast from multiple models"""
        logger.info("Creating ensemble forecast...")
        
        ensemble_forecast = np.zeros_like(list(forecasts.values())[0])
        
        for model_name, forecast in forecasts.items():
            weight = self.config.ensemble_weights.get(model_name, 0)
            ensemble_forecast += weight * forecast
        
        return ensemble_forecast
    
    def generate_forecasts(self, data: pd.DataFrame, horizon_days: int = None) -> Dict[str, Any]:
        """Generate forecasts using all trained models"""
        if horizon_days is None:
            horizon_days = self.config.forecast_horizon_days
        
        logger.info(f"Generating {horizon_days}-day forecasts...")
        
        # Create future dates
        last_date = data['date'].max()
        future_dates = pd.date_range(
            start=last_date + timedelta(days=1),
            periods=horizon_days,
            freq='D'
        )
        
        forecasts = {}
        
        # Prophet forecast
        if 'prophet' in self.models:
            prophet_future = self.models['prophet'].make_future_dataframe(periods=horizon_days)
            
            # Add additional regressors (using last known values or simple extrapolation)
            additional_features = ['total_orders', 'unique_customers', 'total_events',
                                 'unique_visitors', 'is_weekend', 'is_month_end']
            
            for feature in additional_features:
                if feature in data.columns:
                    # Simple approach: use mean of last 30 days for future values
                    last_30_mean = data[feature].tail(30).mean()
                    prophet_future[feature] = last_30_mean
            
            prophet_forecast = self.models['prophet'].predict(prophet_future)
            forecasts['prophet'] = prophet_forecast.tail(horizon_days)['yhat'].values
        
        # ARIMA forecast
        if 'arima' in self.models:
            arima_forecast = self.models['arima'].predict(n_periods=horizon_days)
            forecasts['arima'] = arima_forecast
        
        # LSTM forecast
        if 'lstm' in self.models:
            # Use last sequence to predict future
            last_sequence = data[self.feature_columns].tail(self.config.lstm_sequence_length).values
            last_sequence_scaled = self.scalers['lstm_features'].transform(last_sequence)
            
            lstm_predictions = []
            current_sequence = last_sequence_scaled.reshape(1, self.config.lstm_sequence_length, -1)
            
            for _ in range(horizon_days):
                pred_scaled = self.models['lstm'].predict(current_sequence, verbose=0)
                pred = self.target_scaler.inverse_transform(pred_scaled)[0, 0]
                lstm_predictions.append(pred)
                
                # Update sequence for next prediction (simplified approach)
                new_row = current_sequence[0, -1:, :].copy()
                new_row[0, 0] = pred_scaled[0, 0]  # Update revenue with prediction
                current_sequence = np.concatenate([current_sequence[:, 1:, :], new_row.reshape(1, 1, -1)], axis=1)
            
            forecasts['lstm'] = np.array(lstm_predictions)
        
        # XGBoost forecast
        if 'xgboost' in self.models:
            # Create future features (simplified approach)
            last_row = data.iloc[-1].copy()
            xgb_predictions = []
            
            for i in range(horizon_days):
                future_date = last_date + timedelta(days=i+1)
                
                # Update time features
                last_row['date'] = future_date
                last_row = self._update_time_features_single_row(last_row)
                
                # Prepare features
                feature_cols = [col for col in data.columns if col not in ['date', 'total_revenue']]
                X_future = last_row[feature_cols].values.reshape(1, -1)
                
                # Predict
                pred = self.models['xgboost'].predict(X_future)[0]
                xgb_predictions.append(pred)
                
                # Update for next iteration (simplified)
                last_row['total_revenue'] = pred
            
            forecasts['xgboost'] = np.array(xgb_predictions)
        
        # Create ensemble forecast
        if self.config.use_ensemble and len(forecasts) > 1:
            forecasts['ensemble'] = self.create_ensemble_forecast(forecasts)
        
        # Create forecast dataframe
        forecast_df = pd.DataFrame({
            'date': future_dates,
            **{f'{model}_forecast': forecast for model, forecast in forecasts.items()}
        })
        
        return {
            'forecasts': forecasts,
            'forecast_df': forecast_df,
            'horizon_days': horizon_days
        }
    
    def _update_time_features_single_row(self, row: pd.Series) -> pd.Series:
        """Update time features for a single row"""
        date = row['date']
        
        row['year'] = date.year
        row['month'] = date.month
        row['day'] = date.day
        row['dayofweek'] = date.dayofweek
        row['dayofyear'] = date.dayofyear
        row['week'] = date.isocalendar().week
        row['quarter'] = date.quarter
        row['is_weekend'] = int(date.dayofweek >= 5)
        row['is_month_start'] = int(date.is_month_start)
        row['is_month_end'] = int(date.is_month_end)
        
        # Cyclical encoding
        row['month_sin'] = np.sin(2 * np.pi * date.month / 12)
        row['month_cos'] = np.cos(2 * np.pi * date.month / 12)
        row['dayofweek_sin'] = np.sin(2 * np.pi * date.dayofweek / 7)
        row['dayofweek_cos'] = np.cos(2 * np.pi * date.dayofweek / 7)
        row['dayofyear_sin'] = np.sin(2 * np.pi * date.dayofyear / 365)
        row['dayofyear_cos'] = np.cos(2 * np.pi * date.dayofyear / 365)
        
        return row
    
    def create_forecast_visualizations(self, data: pd.DataFrame, forecast_results: Dict):
        """Create visualizations for forecast results"""
        logger.info("Creating forecast visualizations...")
        
        # Historical data and forecasts plot
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=['Sales Forecast', 'Model Comparison', 'Forecast Uncertainty', 'Residuals'],
            specs=[[{"secondary_y": False}, {"secondary_y": False}],
                   [{"secondary_y": False}, {"secondary_y": False}]]
        )
        
        # Plot historical data
        fig.add_trace(
            go.Scatter(
                x=data['date'],
                y=data['total_revenue'],
                mode='lines',
                name='Historical Sales',
                line=dict(color='blue')
            ),
            row=1, col=1
        )
        
        # Plot forecasts
        forecast_df = forecast_results['forecast_df']
        colors = ['red', 'green', 'orange', 'purple', 'brown']
        
        for i, (model, forecast) in enumerate(forecast_results['forecasts'].items()):
            fig.add_trace(
                go.Scatter(
                    x=forecast_df['date'],
                    y=forecast,
                    mode='lines',
                    name=f'{model.title()} Forecast',
                    line=dict(color=colors[i % len(colors)])
                ),
                row=1, col=1
            )
        
        # Model comparison (last 30 days vs forecast)
        last_30_days = data.tail(30)
        for i, (model, forecast) in enumerate(forecast_results['forecasts'].items()):
            if model != 'ensemble':
                fig.add_trace(
                    go.Bar(
                        x=[model],
                        y=[np.mean(forecast[:30])],
                        name=f'{model.title()} Avg',
                        showlegend=False
                    ),
                    row=1, col=2
                )
        
        fig.update_layout(
            title='Sales Forecasting Results',
            height=800,
            showlegend=True
        )
        
        # Save plot
        try:
            fig.write_html('/tmp/sales_forecast.html')
            mlflow.log_artifact('/tmp/sales_forecast.html')
        except Exception as e:
            logger.warning(f"Could not save visualization: {str(e)}")
    
    def evaluate_forecast_accuracy(self, data: pd.DataFrame) -> Dict:
        """Evaluate forecast accuracy using backtesting"""
        logger.info("Evaluating forecast accuracy...")
        
        # Use last 30 days as test set
        train_data = data[:-30]
        test_data = data[-30:]
        
        # Train models on training data
        temp_models = {}
        
        # Train Prophet on subset
        prophet_train_data = self.prepare_prophet_data(train_data)
        prophet_model = Prophet(
            yearly_seasonality=self.config.prophet_yearly_seasonality,
            weekly_seasonality=self.config.prophet_weekly_seasonality,
            daily_seasonality=self.config.prophet_daily_seasonality,
            seasonality_mode=self.config.prophet_seasonality_mode
        )
        prophet_model.fit(prophet_train_data)
        temp_models['prophet'] = prophet_model
        
        # Generate predictions for test period
        prophet_future = prophet_model.make_future_dataframe(periods=30)
        prophet_pred = prophet_model.predict(prophet_future).tail(30)['yhat'].values
        
        # Calculate accuracy metrics
        actual = test_data['total_revenue'].values
        
        mae = mean_absolute_error(actual, prophet_pred)
        rmse = np.sqrt(mean_squared_error(actual, prophet_pred))
        mape = np.mean(np.abs((actual - prophet_pred) / actual)) * 100
        r2 = r2_score(actual, prophet_pred)
        
        accuracy_metrics = {
            'mae': mae,
            'rmse': rmse,
            'mape': mape,
            'r2_score': r2,
            'mean_actual': actual.mean(),
            'mean_predicted': prophet_pred.mean()
        }
        
        mlflow.log_metrics({f'backtest_{k}': v for k, v in accuracy_metrics.items()})
        
        return accuracy_metrics
    
    def save_models(self, model_name: str = "sales_forecasting_models"):
        """Save all trained models"""
        logger.info("Saving forecasting models...")
        
        # Create model package
        model_artifacts = {
            'models': self.models,
            'scalers': self.scalers,
            'feature_columns': self.feature_columns,
            'target_scaler': self.target_scaler,
            'config': self.config
        }
        
        # Save locally
        model_path = f'/tmp/{model_name}.joblib'
        joblib.dump(model_artifacts, model_path)
        
        # Log to MLflow
        mlflow.log_artifact(model_path, 'model_artifacts')
        
        # Save LSTM model separately if exists
        if 'lstm' in self.models:
            lstm_path = f'/tmp/lstm_model.h5'
            self.models['lstm'].save(lstm_path)
            mlflow.log_artifact(lstm_path, 'model_artifacts')
        
        # Save to S3
        try:
            s3_key = f"{self.config.model_prefix}{model_name}_{int(datetime.now().timestamp())}.joblib"
            self.s3_client.upload_file(model_path, self.config.s3_bucket, s3_key)
            logger.info(f"Models saved to s3://{self.config.s3_bucket}/{s3_key}")
        except Exception as e:
            logger.warning(f"Could not save to S3: {str(e)}")
        
        return model_path
    
    def train_all_models(self, data: pd.DataFrame):
        """Train all forecasting models"""
        logger.info("Training all forecasting models...")
        
        # Train Prophet
        self.models['prophet'] = self.train_prophet_model(data)
        
        # Train ARIMA
        self.models['arima'] = self.train_arima_model(data)
        
        # Train LSTM
        self.models['lstm'] = self.train_lstm_model(data)
        
        # Train XGBoost
        self.models['xgboost'] = self.train_xgboost_model(data)
        
        logger.info("All models trained successfully")
    
    def run_forecasting_pipeline(self):
        """Run the complete sales forecasting pipeline"""
        logger.info("Starting sales forecasting pipeline...")
        
        with mlflow.start_run(run_name="sales_forecasting_pipeline"):
            try:
                # Extract sales data
                sales_data = self.extract_sales_data()
                
                # Train all models
                self.train_all_models(sales_data)
                
                # Generate forecasts
                forecast_results = self.generate_forecasts(sales_data)
                
                # Evaluate accuracy
                accuracy_metrics = self.evaluate_forecast_accuracy(sales_data)
                
                # Create visualizations
                self.create_forecast_visualizations(sales_data, forecast_results)
                
                # Save models
                model_path = self.save_models()
                
                # Log pipeline success
                mlflow.log_metrics({
                    'pipeline_success': 1,
                    'forecast_horizon_days': self.config.forecast_horizon_days,
                    'models_trained': len(self.models)
                })
                
                # Register ensemble model
                if 'ensemble' in forecast_results['forecasts']:
                    mlflow.sklearn.log_model(
                        self.models['prophet'],  # Use Prophet as representative
                        "sales_forecast_model"
                    )
                    
                    mlflow.register_model(
                        f"runs:/{mlflow.active_run().info.run_id}/sales_forecast_model",
                        "sales-forecasting-model"
                    )
                
                logger.info("Sales forecasting pipeline completed successfully")
                
                return {
                    'model_path': model_path,
                    'forecast_results': forecast_results,
                    'accuracy_metrics': accuracy_metrics,
                    'data_points': len(sales_data)
                }
                
            except Exception as e:
                logger.error(f"Forecasting pipeline failed: {str(e)}")
                mlflow.log_metric("pipeline_success", 0)
                raise

def main():
    """Main function to run sales forecasting"""
    config = ForecastingConfig()
    forecaster = SalesForecaster(config)
    
    try:
        results = forecaster.run_forecasting_pipeline()
        print(f"Sales forecasting completed successfully!")
        print(f"Model path: {results['model_path']}")
        print(f"Forecast horizon: {config.forecast_horizon_days} days")
        print(f"Data points used: {results['data_points']}")
        print(f"Accuracy metrics: {results['accuracy_metrics']}")
        
    except Exception as e:
        logger.error(f"Sales forecasting failed: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()