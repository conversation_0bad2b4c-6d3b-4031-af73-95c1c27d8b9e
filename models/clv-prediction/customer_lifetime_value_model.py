#!/usr/bin/env python3
"""
Customer Lifetime Value (CLV) Prediction Model for E-commerce Analytics
Advanced CLV prediction using RFM analysis, cohort modeling, and machine learning
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import os
import sys
from typing import Dict, List, Tuple, Optional, Any, Union
import joblib
import json
import warnings
from dataclasses import dataclass

# Statistical and ML libraries
from scipy import stats
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression, Ridge, Lasso, ElasticNet
from sklearn.preprocessing import StandardScaler, RobustScaler, PolynomialFeatures
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score, mean_absolute_percentage_error
from sklearn.cluster import KMeans
import xgboost as xgb
import lightgbm as lgb

# Specialized libraries for CLV
from lifetimes import BetaGeoFitter, GammaGammaFitter, ModifiedBetaGeoFitter
from lifetimes.utils import calibration_and_holdout_data, summary_data_from_transaction_data
from lifetimes.plotting import plot_frequency_recency_matrix, plot_probability_alive_matrix

# Deep learning
import tensorflow as tf
from tensorflow.keras.models import Sequential, Model
from tensorflow.keras.layers import Dense, Dropout, BatchNormalization, Input
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau

# Visualization
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots

# MLflow integration
import mlflow
import mlflow.sklearn
import mlflow.tensorflow
from mlflow.tracking import MlflowClient

# Database and cloud
import boto3
from sqlalchemy import create_engine

warnings.filterwarnings('ignore')

# Configuration
@dataclass
class CLVConfig:
    # Modeling parameters
    prediction_horizon_days: int = 365  # 1 year CLV prediction
    observation_period_days: int = 730  # 2 years of historical data
    cohort_analysis_months: int = 12
    
    # RFM parameters
    rfm_quantiles: int = 5
    frequency_threshold: int = 2  # Minimum purchases to be considered active
    
    # Model parameters
    test_size: float = 0.2
    random_state: int = 42
    cv_folds: int = 5
    
    # CLV calculation methods
    use_traditional_models: bool = True  # BG/NBD, Gamma-Gamma
    use_ml_models: bool = True
    use_deep_learning: bool = True
    use_ensemble: bool = True
    
    # Feature engineering
    include_behavioral_features: bool = True
    include_temporal_features: bool = True
    include_product_features: bool = True
    
    # MLflow configuration
    mlflow_tracking_uri: str = os.getenv('MLFLOW_TRACKING_URI', 'http://mlflow-server.mlflow:5000')
    experiment_name: str = 'customer-lifetime-value'
    
    # Database connections
    db_host: str = os.getenv('DB_HOST', 'analytics-db.cluster-xyz.us-east-1.rds.amazonaws.com')
    db_name: str = os.getenv('DB_NAME', 'ecommerce_analytics')
    db_user: str = os.getenv('DB_USER', 'analytics_user')
    db_password: str = os.getenv('DB_PASSWORD', 'analytics_password')
    
    # S3 configuration
    s3_bucket: str = os.getenv('S3_BUCKET', 'ecommerce-analytics-models')
    model_prefix: str = 'clv-prediction/'

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class CLVPredictor:
    def __init__(self, config: CLVConfig):
        self.config = config
        self.db_engine = self._create_db_engine()
        self.s3_client = boto3.client('s3')
        
        # Initialize MLflow
        mlflow.set_tracking_uri(config.mlflow_tracking_uri)
        mlflow.set_experiment(config.experiment_name)
        
        # Model storage
        self.models = {}
        self.scalers = {}
        self.feature_names = []
        self.customer_data = None
        self.rfm_data = None
        
    def _create_db_engine(self):
        """Create database engine for PostgreSQL"""
        connection_string = f"postgresql://{self.config.db_user}:{self.config.db_password}@{self.config.db_host}/{self.config.db_name}"
        return create_engine(connection_string)
    
    def extract_customer_data(self) -> pd.DataFrame:
        """Extract comprehensive customer data for CLV analysis"""
        logger.info("Extracting customer data for CLV analysis...")
        
        with mlflow.start_run(run_name="data_extraction", nested=True):
            end_date = datetime.now()
            start_date = end_date - timedelta(days=self.config.observation_period_days)
            
            # Comprehensive customer query
            customer_query = """
                WITH customer_transactions AS (
                    SELECT 
                        p.user_id,
                        p.purchase_timestamp::date as purchase_date,
                        p.total_amount,
                        p.order_id,
                        ROW_NUMBER() OVER (PARTITION BY p.user_id ORDER BY p.purchase_timestamp) as purchase_number,
                        LAG(p.purchase_timestamp::date) OVER (PARTITION BY p.user_id ORDER BY p.purchase_timestamp) as prev_purchase_date,
                        LEAD(p.purchase_timestamp::date) OVER (PARTITION BY p.user_id ORDER BY p.purchase_timestamp) as next_purchase_date
                    FROM purchases p
                    WHERE p.purchase_timestamp >= %s 
                        AND p.purchase_timestamp <= %s
                        AND p.total_amount > 0
                ),
                customer_metrics AS (
                    SELECT 
                        user_id,
                        COUNT(*) as total_orders,
                        SUM(total_amount) as total_revenue,
                        AVG(total_amount) as avg_order_value,
                        STDDEV(total_amount) as order_value_std,
                        MIN(purchase_date) as first_purchase_date,
                        MAX(purchase_date) as last_purchase_date,
                        MAX(purchase_date) - MIN(purchase_date) as customer_lifespan_days,
                        
                        -- Frequency metrics
                        COUNT(*) as frequency,
                        COUNT(DISTINCT purchase_date) as unique_purchase_days,
                        
                        -- Recency (days since last purchase)
                        %s::date - MAX(purchase_date) as recency_days,
                        
                        -- Average days between purchases
                        CASE 
                            WHEN COUNT(*) > 1 THEN 
                                (MAX(purchase_date) - MIN(purchase_date)) / NULLIF(COUNT(*) - 1, 0)
                            ELSE NULL 
                        END as avg_days_between_purchases,
                        
                        -- Purchase patterns
                        COUNT(CASE WHEN EXTRACT(dow FROM purchase_date) IN (0,6) THEN 1 END) as weekend_purchases,
                        COUNT(CASE WHEN EXTRACT(dow FROM purchase_date) IN (1,2,3,4,5) THEN 1 END) as weekday_purchases
                        
                    FROM customer_transactions
                    GROUP BY user_id
                ),
                customer_products AS (
                    SELECT 
                        p.user_id,
                        COUNT(DISTINCT pi.product_id) as unique_products_purchased,
                        COUNT(DISTINCT pi.category_id) as unique_categories_purchased,
                        SUM(pi.quantity) as total_items_purchased,
                        AVG(pi.unit_price) as avg_product_price,
                        MAX(pi.unit_price) as max_product_price,
                        MIN(pi.unit_price) as min_product_price
                    FROM purchases p
                    JOIN purchase_items pi ON p.order_id = pi.order_id
                    WHERE p.purchase_timestamp >= %s 
                        AND p.purchase_timestamp <= %s
                    GROUP BY p.user_id
                ),
                customer_behavior AS (
                    SELECT 
                        ue.user_id,
                        COUNT(*) as total_events,
                        COUNT(DISTINCT ue.session_id) as total_sessions,
                        COUNT(DISTINCT DATE(ue.event_timestamp)) as active_days,
                        AVG(CASE WHEN event_type = 'page_view' THEN 1 ELSE 0 END) as page_view_rate,
                        AVG(CASE WHEN event_type = 'add_to_cart' THEN 1 ELSE 0 END) as cart_add_rate,
                        COUNT(DISTINCT device_type) as device_diversity,
                        MAX(event_timestamp::date) as last_activity_date
                    FROM user_events ue
                    WHERE ue.event_timestamp >= %s 
                        AND ue.event_timestamp <= %s
                    GROUP BY ue.user_id
                ),
                customer_profiles AS (
                    SELECT 
                        user_id,
                        registration_date,
                        country,
                        age_group,
                        gender,
                        subscription_tier,
                        total_lifetime_value,
                        preferred_language,
                        %s::date - registration_date::date as account_age_days
                    FROM user_profiles
                    WHERE registration_date <= %s
                )
                SELECT 
                    cm.user_id,
                    cm.total_orders,
                    cm.total_revenue,
                    cm.avg_order_value,
                    cm.order_value_std,
                    cm.first_purchase_date,
                    cm.last_purchase_date,
                    cm.customer_lifespan_days,
                    cm.frequency,
                    cm.unique_purchase_days,
                    cm.recency_days,
                    cm.avg_days_between_purchases,
                    cm.weekend_purchases,
                    cm.weekday_purchases,
                    
                    COALESCE(cp_prod.unique_products_purchased, 0) as unique_products_purchased,
                    COALESCE(cp_prod.unique_categories_purchased, 0) as unique_categories_purchased,
                    COALESCE(cp_prod.total_items_purchased, 0) as total_items_purchased,
                    COALESCE(cp_prod.avg_product_price, 0) as avg_product_price,
                    COALESCE(cp_prod.max_product_price, 0) as max_product_price,
                    COALESCE(cp_prod.min_product_price, 0) as min_product_price,
                    
                    COALESCE(cb.total_events, 0) as total_events,
                    COALESCE(cb.total_sessions, 0) as total_sessions,
                    COALESCE(cb.active_days, 0) as active_days,
                    COALESCE(cb.page_view_rate, 0) as page_view_rate,
                    COALESCE(cb.cart_add_rate, 0) as cart_add_rate,
                    COALESCE(cb.device_diversity, 0) as device_diversity,
                    cb.last_activity_date,
                    
                    cp_prof.country,
                    cp_prof.age_group,
                    cp_prof.gender,
                    cp_prof.subscription_tier,
                    cp_prof.total_lifetime_value,
                    cp_prof.preferred_language,
                    cp_prof.account_age_days,
                    cp_prof.registration_date
                    
                FROM customer_metrics cm
                LEFT JOIN customer_products cp_prod ON cm.user_id = cp_prod.user_id
                LEFT JOIN customer_behavior cb ON cm.user_id = cb.user_id
                LEFT JOIN customer_profiles cp_prof ON cm.user_id = cp_prof.user_id
                WHERE cm.frequency >= %s  -- Only customers with minimum purchase frequency
                ORDER BY cm.total_revenue DESC
            """
            
            customer_data = pd.read_sql(
                customer_query,
                self.db_engine,
                params=[
                    start_date, end_date,  # transaction period
                    end_date,              # for recency calculation
                    start_date, end_date,  # product period
                    start_date, end_date,  # behavior period
                    end_date,              # for account age calculation
                    end_date,              # profile cutoff
                    self.config.frequency_threshold  # minimum frequency
                ]
            )
            
            # Convert date columns
            date_columns = ['first_purchase_date', 'last_purchase_date', 'last_activity_date', 'registration_date']
            for col in date_columns:
                if col in customer_data.columns:
                    customer_data[col] = pd.to_datetime(customer_data[col])
            
            # Fill missing values
            customer_data = customer_data.fillna(0)
            
            # Store customer data
            self.customer_data = customer_data
            
            # Log extraction metrics
            mlflow.log_metrics({
                'total_customers': len(customer_data),
                'avg_customer_revenue': customer_data['total_revenue'].mean(),
                'avg_customer_orders': customer_data['total_orders'].mean(),
                'avg_customer_lifespan': customer_data['customer_lifespan_days'].mean(),
                'data_completeness': (customer_data['total_revenue'] > 0).mean()
            })
            
            logger.info(f"Extracted data for {len(customer_data)} customers")
            return customer_data
    
    def calculate_rfm_metrics(self, data: pd.DataFrame) -> pd.DataFrame:
        """Calculate RFM (Recency, Frequency, Monetary) metrics"""
        logger.info("Calculating RFM metrics...")
        
        with mlflow.start_run(run_name="rfm_calculation", nested=True):
            rfm_data = data.copy()
            
            # Calculate RFM scores
            rfm_data['R_Score'] = pd.qcut(
                rfm_data['recency_days'].rank(method='first', ascending=False),
                q=self.config.rfm_quantiles,
                labels=range(1, self.config.rfm_quantiles + 1)
            ).astype(int)
            
            rfm_data['F_Score'] = pd.qcut(
                rfm_data['frequency'].rank(method='first'),
                q=self.config.rfm_quantiles,
                labels=range(1, self.config.rfm_quantiles + 1),
                duplicates='drop'
            ).astype(int)
            
            rfm_data['M_Score'] = pd.qcut(
                rfm_data['total_revenue'].rank(method='first'),
                q=self.config.rfm_quantiles,
                labels=range(1, self.config.rfm_quantiles + 1),
                duplicates='drop'
            ).astype(int)
            
            # Combined RFM score
            rfm_data['RFM_Score'] = (
                rfm_data['R_Score'].astype(str) + 
                rfm_data['F_Score'].astype(str) + 
                rfm_data['M_Score'].astype(str)
            )
            
            # RFM segment classification
            rfm_data['RFM_Segment'] = rfm_data.apply(self._classify_rfm_segment, axis=1)
            
            # Calculate additional RFM-based metrics
            rfm_data['RFM_Combined_Score'] = rfm_data['R_Score'] + rfm_data['F_Score'] + rfm_data['M_Score']
            rfm_data['RF_Score'] = rfm_data['R_Score'] + rfm_data['F_Score']
            rfm_data['FM_Score'] = rfm_data['F_Score'] + rfm_data['M_Score']
            
            # Store RFM data
            self.rfm_data = rfm_data
            
            # Log RFM metrics
            segment_distribution = rfm_data['RFM_Segment'].value_counts()
            mlflow.log_metrics({
                'champions_pct': (segment_distribution.get('Champions', 0) / len(rfm_data)) * 100,
                'loyal_customers_pct': (segment_distribution.get('Loyal Customers', 0) / len(rfm_data)) * 100,
                'at_risk_pct': (segment_distribution.get('At Risk', 0) / len(rfm_data)) * 100,
                'avg_rfm_score': rfm_data['RFM_Combined_Score'].mean()
            })
            
            logger.info(f"RFM analysis completed for {len(rfm_data)} customers")
            return rfm_data
    
    def _classify_rfm_segment(self, row: pd.Series) -> str:
        """Classify customers into RFM segments"""
        r, f, m = row['R_Score'], row['F_Score'], row['M_Score']
        
        if r >= 4 and f >= 4 and m >= 4:
            return 'Champions'
        elif r >= 3 and f >= 3 and m >= 3:
            return 'Loyal Customers'
        elif r >= 3 and f >= 2:
            return 'Potential Loyalists'
        elif r >= 4 and f <= 2:
            return 'New Customers'
        elif r >= 3 and f <= 2 and m >= 3:
            return 'Promising'
        elif r <= 2 and f >= 3 and m >= 3:
            return 'At Risk'
        elif r <= 2 and f <= 2 and m >= 3:
            return 'Cannot Lose Them'
        elif r <= 2 and f >= 2:
            return 'Hibernating'
        else:
            return 'Lost'
    
    def engineer_clv_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Engineer features specifically for CLV prediction"""
        logger.info("Engineering CLV features...")
        
        with mlflow.start_run(run_name="feature_engineering", nested=True):
            features_df = data.copy()
            
            # Behavioral features
            if self.config.include_behavioral_features:
                features_df = self._add_behavioral_features(features_df)
            
            # Temporal features
            if self.config.include_temporal_features:
                features_df = self._add_temporal_features(features_df)
            
            # Product affinity features
            if self.config.include_product_features:
                features_df = self._add_product_features(features_df)
            
            # Customer value features
            features_df = self._add_value_features(features_df)
            
            # Engagement features
            features_df = self._add_engagement_features(features_df)
            
            # Handle missing values and infinite values
            features_df = features_df.replace([np.inf, -np.inf], np.nan)
            
            # Fill NaN values
            numeric_cols = features_df.select_dtypes(include=[np.number]).columns
            features_df[numeric_cols] = features_df[numeric_cols].fillna(0)
            
            categorical_cols = features_df.select_dtypes(include=['object']).columns
            features_df[categorical_cols] = features_df[categorical_cols].fillna('unknown')
            
            # Log feature engineering metrics
            mlflow.log_metrics({
                'total_features': len(features_df.columns),
                'numeric_features': len(numeric_cols),
                'categorical_features': len(categorical_cols)
            })
            
            logger.info(f"Feature engineering completed. Generated {len(features_df.columns)} features")
            return features_df
    
    def _add_behavioral_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Add behavioral pattern features"""
        data = data.copy()
        
        # Purchase behavior patterns
        data['purchase_consistency'] = 1 / (data['order_value_std'] / (data['avg_order_value'] + 1))
        data['weekend_purchase_ratio'] = data['weekend_purchases'] / (data['total_orders'] + 1)
        data['purchase_velocity'] = data['total_orders'] / (data['customer_lifespan_days'] + 1)
        
        # Engagement patterns
        data['events_per_session'] = data['total_events'] / (data['total_sessions'] + 1)
        data['sessions_per_day'] = data['total_sessions'] / (data['active_days'] + 1)
        data['conversion_rate'] = data['total_orders'] / (data['total_sessions'] + 1)
        
        # Product exploration
        data['product_exploration_rate'] = data['unique_products_purchased'] / (data['total_orders'] + 1)
        data['category_diversity'] = data['unique_categories_purchased'] / (data['unique_products_purchased'] + 1)
        data['avg_items_per_order'] = data['total_items_purchased'] / (data['total_orders'] + 1)
        
        return data
    
    def _add_temporal_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Add temporal pattern features"""
        data = data.copy()
        
        # Time-based patterns
        data['days_since_first_purchase'] = (datetime.now() - data['first_purchase_date']).dt.days
        data['days_since_last_purchase'] = data['recency_days']
        data['purchase_frequency_score'] = data['total_orders'] / (data['days_since_first_purchase'] + 1)
        
        # Customer lifecycle stage
        data['customer_age_ratio'] = data['account_age_days'] / (data['days_since_first_purchase'] + 1)
        data['active_ratio'] = data['customer_lifespan_days'] / (data['account_age_days'] + 1)
        
        # Recency indicators
        data['is_recent_customer'] = (data['recency_days'] <= 30).astype(int)
        data['is_dormant_customer'] = (data['recency_days'] > 90).astype(int)
        data['is_churned_customer'] = (data['recency_days'] > 180).astype(int)
        
        return data
    
    def _add_product_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Add product affinity features"""
        data = data.copy()
        
        # Price sensitivity
        data['price_range_preference'] = data['max_product_price'] - data['min_product_price']
        data['premium_product_affinity'] = data['avg_product_price'] / (data['min_product_price'] + 1)
        
        # Purchase depth
        data['purchase_depth_score'] = (
            data['unique_products_purchased'] * data['unique_categories_purchased']
        ) / (data['total_orders'] + 1)
        
        return data
    
    def _add_value_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Add customer value features"""
        data = data.copy()
        
        # Value metrics
        data['revenue_per_day'] = data['total_revenue'] / (data['customer_lifespan_days'] + 1)
        data['revenue_per_session'] = data['total_revenue'] / (data['total_sessions'] + 1)
        data['revenue_growth_potential'] = data['total_revenue'] / (data['account_age_days'] + 1)
        
        # Value concentration
        data['order_value_consistency'] = 1 - (data['order_value_std'] / (data['avg_order_value'] + 1))
        data['high_value_purchase_indicator'] = (data['max_product_price'] > data['avg_product_price'] * 2).astype(int)
        
        return data
    
    def _add_engagement_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Add customer engagement features"""
        data = data.copy()
        
        # Engagement intensity
        data['engagement_intensity'] = (
            data['total_events'] * data['page_view_rate'] * data['cart_add_rate']
        ) / (data['total_sessions'] + 1)
        
        # Multi-channel engagement
        data['cross_device_user'] = (data['device_diversity'] > 1).astype(int)
        
        # Subscription tier value
        tier_mapping = {'free': 1, 'standard': 2, 'premium': 3}
        data['subscription_tier_numeric'] = data['subscription_tier'].map(tier_mapping).fillna(1)
        
        return data
    
    def prepare_traditional_clv_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Prepare data for traditional CLV models (BG/NBD, Gamma-Gamma)"""
        logger.info("Preparing data for traditional CLV models...")
        
        # Create transaction data format required by lifetimes
        transaction_data = []
        
        # For each customer, we need to simulate their transaction history
        # In a real implementation, you would extract actual transaction dates
        for _, customer in data.iterrows():
            user_id = customer['user_id']
            total_orders = int(customer['total_orders'])
            first_purchase = customer['first_purchase_date']
            last_purchase = customer['last_purchase_date']
            avg_order_value = customer['avg_order_value']
            
            # Simulate transaction dates (in practice, use actual dates)
            if total_orders > 1:
                purchase_dates = pd.date_range(
                    start=first_purchase,
                    end=last_purchase,
                    periods=total_orders
                )
            else:
                purchase_dates = [first_purchase]
            
            # Create transaction records
            for i, date in enumerate(purchase_dates):
                transaction_data.append({
                    'customer_id': user_id,
                    'order_date': date,
                    'revenue': avg_order_value * np.random.normal(1, 0.2)  # Add some variance
                })
        
        transactions_df = pd.DataFrame(transaction_data)
        
        # Create summary data for lifetimes models
        summary_df = summary_data_from_transaction_data(
            transactions_df,
            'customer_id',
            'order_date',
            'revenue',
            observation_period_end=datetime.now().strftime('%Y-%m-%d')
        )
        
        return summary_df
    
    def train_traditional_clv_models(self, summary_data: pd.DataFrame) -> Dict:
        """Train traditional CLV models (BG/NBD and Gamma-Gamma)"""
        logger.info("Training traditional CLV models...")
        
        with mlflow.start_run(run_name="traditional_clv_models", nested=True):
            models = {}
            
            try:
                # BG/NBD Model for predicting future purchases
                bgf = BetaGeoFitter(penalizer_coef=0.0)
                bgf.fit(summary_data['frequency'], summary_data['recency'], summary_data['T'])
                models['bg_nbd'] = bgf
                
                # Gamma-Gamma Model for predicting customer value
                returning_customers = summary_data[summary_data['frequency'] > 0]
                ggf = GammaGammaFitter(penalizer_coef=0.0)
                ggf.fit(returning_customers['frequency'], returning_customers['monetary_value'])
                models['gamma_gamma'] = ggf
                
                # Calculate CLV predictions
                t = self.config.prediction_horizon_days
                
                # Predict future purchases
                expected_purchases = bgf.conditional_expected_number_of_purchases_up_to_time(
                    t, summary_data['frequency'], summary_data['recency'], summary_data['T']
                )
                
                # Predict customer value (for returning customers only)
                expected_values = np.zeros(len(summary_data))
                returning_mask = summary_data['frequency'] > 0
                expected_values[returning_mask] = ggf.conditional_expected_average_profit(
                    returning_customers['frequency'], returning_customers['monetary_value']
                )
                
                # Calculate CLV
                traditional_clv = expected_purchases * expected_values
                
                # Add predictions to summary data
                summary_data['predicted_purchases'] = expected_purchases
                summary_data['predicted_avg_value'] = expected_values
                summary_data['traditional_clv'] = traditional_clv
                
                # Calculate model performance metrics
                if 'monetary_value' in summary_data.columns:
                    # Use current monetary value as a baseline for comparison
                    baseline_clv = summary_data['monetary_value'] * (expected_purchases / summary_data['frequency'].clip(lower=1))
                    mae = mean_absolute_error(baseline_clv, traditional_clv)
                    
                    mlflow.log_metrics({
                        'traditional_clv_mae': mae,
                        'avg_predicted_clv': traditional_clv.mean(),
                        'avg_predicted_purchases': expected_purchases.mean()
                    })
                
                logger.info("Traditional CLV models trained successfully")
                
            except Exception as e:
                logger.error(f"Error training traditional CLV models: {str(e)}")
                models = {}
            
            return models, summary_data
    
    def train_ml_clv_models(self, features_data: pd.DataFrame) -> Dict:
        """Train machine learning models for CLV prediction"""
        logger.info("Training ML models for CLV prediction...")
        
        with mlflow.start_run(run_name="ml_clv_models", nested=True):
            models = {}
            
            # Prepare features and target
            X, y, feature_names = self._prepare_ml_features(features_data)
            
            if len(X) == 0:
                logger.error("No features available for ML training")
                return models
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=self.config.test_size, random_state=self.config.random_state
            )
            
            # Scale features
            scaler = RobustScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_test_scaled = scaler.transform(X_test)
            self.scalers['ml_features'] = scaler
            self.feature_names = feature_names
            
            # Train multiple models
            ml_models = {
                'random_forest': RandomForestRegressor(
                    n_estimators=200,
                    max_depth=15,
                    random_state=self.config.random_state,
                    n_jobs=-1
                ),
                'gradient_boosting': GradientBoostingRegressor(
                    n_estimators=200,
                    max_depth=8,
                    learning_rate=0.1,
                    random_state=self.config.random_state
                ),
                'xgboost': xgb.XGBRegressor(
                    n_estimators=200,
                    max_depth=8,
                    learning_rate=0.1,
                    random_state=self.config.random_state
                ),
                'lightgbm': lgb.LGBMRegressor(
                    n_estimators=200,
                    max_depth=8,
                    learning_rate=0.1,
                    random_state=self.config.random_state,
                    verbosity=-1
                )
            }
            
            # Train and evaluate each model
            for name, model in ml_models.items():
                logger.info(f"Training {name}...")
                
                # Train model
                if name in ['random_forest', 'gradient_boosting']:
                    model.fit(X_train_scaled, y_train)
                else:
                    model.fit(X_train, y_train)
                
                # Make predictions
                if name in ['random_forest', 'gradient_boosting']:
                    train_pred = model.predict(X_train_scaled)
                    test_pred = model.predict(X_test_scaled)
                else:
                    train_pred = model.predict(X_train)
                    test_pred = model.predict(X_test)
                
                # Calculate metrics
                train_mae = mean_absolute_error(y_train, train_pred)
                test_mae = mean_absolute_error(y_test, test_pred)
                train_rmse = np.sqrt(mean_squared_error(y_train, train_pred))
                test_rmse = np.sqrt(mean_squared_error(y_test, test_pred))
                test_r2 = r2_score(y_test, test_pred)
                
                # Log metrics
                mlflow.log_metrics({
                    f'{name}_train_mae': train_mae,
                    f'{name}_test_mae': test_mae,
                    f'{name}_train_rmse': train_rmse,
                    f'{name}_test_rmse': test_rmse,
                    f'{name}_test_r2': test_r2
                })
                
                # Store model
                models[name] = model
                
                logger.info(f"{name} - Test MAE: {test_mae:.2f}, Test R²: {test_r2:.3f}")
            
            return models
    
    def _prepare_ml_features(self, data: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray, List[str]]:
        """Prepare features and target for ML models"""
        
        # Define target variable
        # We'll predict total_lifetime_value or use total_revenue as proxy for CLV
        if 'total_lifetime_value' in data.columns and data['total_lifetime_value'].sum() > 0:
            target_col = 'total_lifetime_value'
        else:
            # Create a proxy CLV target based on current behavior
            data['clv_target'] = (
                data['total_revenue'] * 
                (1 + data['purchase_frequency_score']) * 
                (1 - data['recency_days'] / 365)  # Discount for recency
            ).clip(lower=0)
            target_col = 'clv_target'
        
        # Select features (exclude target and ID columns)
        exclude_cols = [
            'user_id', target_col, 'first_purchase_date', 'last_purchase_date',
            'last_activity_date', 'registration_date', 'RFM_Score'
        ]
        
        feature_cols = [col for col in data.columns if col not in exclude_cols]
        
        # Handle categorical variables
        categorical_cols = data[feature_cols].select_dtypes(include=['object']).columns
        for col in categorical_cols:
            if col in feature_cols:
                # Simple label encoding for categorical variables
                unique_values = data[col].unique()
                if len(unique_values) <= 10:  # Only encode if reasonable number of categories
                    data[col] = pd.Categorical(data[col]).codes
                else:
                    feature_cols.remove(col)  # Remove high-cardinality categorical features
        
        # Prepare final feature matrix
        X = data[feature_cols].values
        y = data[target_col].values
        
        # Remove any remaining non-numeric columns
        numeric_mask = np.all(np.isfinite(X), axis=0)
        X = X[:, numeric_mask]
        feature_names = [feature_cols[i] for i, mask in enumerate(numeric_mask) if mask]
        
        return X, y, feature_names
    
    def train_deep_learning_clv_model(self, features_data: pd.DataFrame) -> Optional[tf.keras.Model]:
        """Train deep learning model for CLV prediction"""
        logger.info("Training deep learning CLV model...")
        
        with mlflow.start_run(run_name="deep_learning_clv", nested=True):
            try:
                # Prepare data
                X, y, feature_names = self._prepare_ml_features(features_data)
                
                if len(X) == 0:
                    logger.error("No features available for deep learning training")
                    return None
                
                # Split data
                X_train, X_test, y_train, y_test = train_test_split(
                    X, y, test_size=self.config.test_size, random_state=self.config.random_state
                )
                
                # Scale features
                scaler = StandardScaler()
                X_train_scaled = scaler.fit_transform(X_train)
                X_test_scaled = scaler.transform(X_test)
                self.scalers['dl_features'] = scaler
                
                # Scale target
                target_scaler = StandardScaler()
                y_train_scaled = target_scaler.fit_transform(y_train.reshape(-1, 1)).flatten()
                y_test_scaled = target_scaler.transform(y_test.reshape(-1, 1)).flatten()
                self.scalers['target'] = target_scaler
                
                # Build deep learning model
                model = Sequential([
                    Dense(128, activation='relu', input_shape=(X_train_scaled.shape[1],)),
                    BatchNormalization(),
                    Dropout(0.3),
                    
                    Dense(64, activation='relu'),
                    BatchNormalization(),
                    Dropout(0.3),
                    
                    Dense(32, activation='relu'),
                    Dropout(0.2),
                    
                    Dense(16, activation='relu'),
                    Dense(1, activation='linear')
                ])
                
                model.compile(
                    optimizer=Adam(learning_rate=0.001),
                    loss='mse',
                    metrics=['mae']
                )
                
                # Callbacks
                early_stopping = EarlyStopping(
                    monitor='val_loss',
                    patience=20,
                    restore_best_weights=True
                )
                
                reduce_lr = ReduceLROnPlateau(
                    monitor='val_loss',
                    factor=0.5,
                    patience=10,
                    min_lr=1e-7
                )
                
                # Train model
                history = model.fit(
                    X_train_scaled, y_train_scaled,
                    validation_data=(X_test_scaled, y_test_scaled),
                    epochs=100,
                    batch_size=32,
                    callbacks=[early_stopping, reduce_lr],
                    verbose=0
                )
                
                # Make predictions
                train_pred_scaled = model.predict(X_train_scaled, verbose=0)
                test_pred_scaled = model.predict(X_test_scaled, verbose=0)
                
                # Inverse transform predictions
                train_pred = target_scaler.inverse_transform(train_pred_scaled).flatten()
                test_pred = target_scaler.inverse_transform(test_pred_scaled).flatten()
                
                # Calculate metrics
                train_mae = mean_absolute_error(y_train, train_pred)
                test_mae = mean_absolute_error(y_test, test_pred)
                train_rmse = np.sqrt(mean_squared_error(y_train, train_pred))
                test_rmse = np.sqrt(mean_squared_error(y_test, test_pred))
                test_r2 = r2_score(y_test, test_pred)
                
                # Log metrics
                mlflow.log_metrics({
                    'dl_train_mae': train_mae,
                    'dl_test_mae': test_mae,
                    'dl_train_rmse': train_rmse,
                    'dl_test_rmse': test_rmse,
                    'dl_test_r2': test_r2,
                    'dl_epochs_trained': len(history.history['loss'])
                })
                
                logger.info(f"Deep learning model - Test MAE: {test_mae:.2f}, Test R²: {test_r2:.3f}")
                
                return model
                
            except Exception as e:
                logger.error(f"Error training deep learning model: {str(e)}")
                return None
    
    def create_ensemble_clv_model(self, ml_models: Dict, traditional_models: Dict, 
                                 dl_model: Optional[tf.keras.Model], features_data: pd.DataFrame) -> Dict:
        """Create ensemble CLV model combining all approaches"""
        logger.info("Creating ensemble CLV model...")
        
        with mlflow.start_run(run_name="ensemble_clv", nested=True):
            ensemble_predictions = {}
            
            # Prepare data
            X, y, feature_names = self._prepare_ml_features(features_data)
            
            if len(X) == 0:
                logger.error("No features available for ensemble")
                return {}
            
            # Get predictions from ML models
            ml_predictions = []
            ml_weights = []
            
            for name, model in ml_models.items():
                if name in ['random_forest', 'gradient_boosting']:
                    pred = model.predict(self.scalers['ml_features'].transform(X))
                else:
                    pred = model.predict(X)
                
                ml_predictions.append(pred)
                ml_weights.append(1.0)  # Equal weighting for now
                ensemble_predictions[f'ml_{name}'] = pred
            
            # Get predictions from traditional models
            if traditional_models and 'bg_nbd' in traditional_models and 'gamma_gamma' in traditional_models:
                try:
                    # This requires mapping back to the summary data format
                    # For simplicity, we'll use the stored traditional CLV if available
                    if hasattr(self, 'traditional_clv_predictions'):
                        traditional_pred = self.traditional_clv_predictions
                        ml_predictions.append(traditional_pred[:len(X)])
                        ml_weights.append(1.5)  # Higher weight for traditional models
                        ensemble_predictions['traditional'] = traditional_pred[:len(X)]
                except Exception as e:
                    logger.warning(f"Could not include traditional models in ensemble: {str(e)}")
            
            # Get predictions from deep learning model
            if dl_model is not None:
                try:
                    X_scaled = self.scalers['dl_features'].transform(X)
                    dl_pred_scaled = dl_model.predict(X_scaled, verbose=0)
                    dl_pred = self.scalers['target'].inverse_transform(dl_pred_scaled).flatten()
                    
                    ml_predictions.append(dl_pred)
                    ml_weights.append(1.2)  # Slightly higher weight for DL
                    ensemble_predictions['deep_learning'] = dl_pred
                except Exception as e:
                    logger.warning(f"Could not include deep learning model in ensemble: {str(e)}")
            
            # Create ensemble prediction
            if ml_predictions:
                ml_weights = np.array(ml_weights) / np.sum(ml_weights)  # Normalize weights
                ensemble_pred = np.average(ml_predictions, axis=0, weights=ml_weights)
                ensemble_predictions['ensemble'] = ensemble_pred
                
                # Calculate ensemble metrics (if we have true targets)
                if len(y) == len(ensemble_pred):
                    ensemble_mae = mean_absolute_error(y, ensemble_pred)
                    ensemble_rmse = np.sqrt(mean_squared_error(y, ensemble_pred))
                    ensemble_r2 = r2_score(y, ensemble_pred)
                    
                    mlflow.log_metrics({
                        'ensemble_mae': ensemble_mae,
                        'ensemble_rmse': ensemble_rmse,
                        'ensemble_r2': ensemble_r2,
                        'ensemble_models_count': len(ml_predictions)
                    })
                    
                    logger.info(f"Ensemble model - MAE: {ensemble_mae:.2f}, R²: {ensemble_r2:.3f}")
            
            return ensemble_predictions
    
    def analyze_clv_segments(self, clv_predictions: Dict) -> Dict:
        """Analyze CLV segments and provide insights"""
        logger.info("Analyzing CLV segments...")
        
        if 'ensemble' not in clv_predictions:
            logger.warning("No ensemble predictions available for segmentation")
            return {}
        
        clv_values = clv_predictions['ensemble']
        
        # Create CLV segments based on quintiles
        clv_segments = pd.qcut(clv_values, q=5, labels=['Low', 'Medium-Low', 'Medium', 'Medium-High', 'High'])
        
        # Analyze segments
        segment_analysis = {}
        
        for segment in clv_segments.categories:
            segment_mask = clv_segments == segment
            segment_customers = self.customer_data[segment_mask] if self.customer_data is not None else None
            
            if segment_customers is not None and len(segment_customers) > 0:
                segment_analysis[segment] = {
                    'count': len(segment_customers),
                    'percentage': len(segment_customers) / len(self.customer_data) * 100,
                    'avg_clv': clv_values[segment_mask].mean(),
                    'avg_current_revenue': segment_customers['total_revenue'].mean(),
                    'avg_orders': segment_customers['total_orders'].mean(),
                    'avg_recency': segment_customers['recency_days'].mean(),
                    'avg_frequency': segment_customers['frequency'].mean(),
                    'characteristics': self._analyze_segment_characteristics(segment_customers)
                }
        
        # Overall CLV distribution
        segment_analysis['overall_stats'] = {
            'total_customers': len(clv_values),
            'avg_clv': clv_values.mean(),
            'median_clv': np.median(clv_values),
            'std_clv': clv_values.std(),
            'min_clv': clv_values.min(),
            'max_clv': clv_values.max(),
            'clv_concentration': {
                'top_20_pct_share': np.sort(clv_values)[-int(len(clv_values)*0.2):].sum() / clv_values.sum(),
                'top_10_pct_share': np.sort(clv_values)[-int(len(clv_values)*0.1):].sum() / clv_values.sum()
            }
        }
        
        return segment_analysis
    
    def _analyze_segment_characteristics(self, segment_data: pd.DataFrame) -> Dict:
        """Analyze characteristics of a customer segment"""
        characteristics = {}
        
        # Behavioral characteristics
        characteristics['behavior'] = {
            'avg_purchase_frequency': segment_data['purchase_velocity'].mean(),
            'avg_order_value': segment_data['avg_order_value'].mean(),
            'avg_product_diversity': segment_data['unique_products_purchased'].mean(),
            'weekend_purchase_preference': segment_data['weekend_purchase_ratio'].mean()
        }
        
        # Demographic characteristics (if available)
        if 'country' in segment_data.columns:
            characteristics['demographics'] = {
                'top_countries': segment_data['country'].value_counts().head(3).to_dict(),
                'subscription_distribution': segment_data['subscription_tier'].value_counts().to_dict()
            }
        
        # Engagement characteristics
        characteristics['engagement'] = {
            'avg_sessions': segment_data['total_sessions'].mean(),
            'avg_conversion_rate': segment_data['conversion_rate'].mean(),
            'multi_device_users_pct': (segment_data['device_diversity'] > 1).mean() * 100
        }
        
        return characteristics
    
    def create_clv_visualizations(self, clv_predictions: Dict, segment_analysis: Dict):
        """Create comprehensive CLV visualizations"""
        logger.info("Creating CLV visualizations...")
        
        if not clv_predictions or 'ensemble' not in clv_predictions:
            logger.warning("No predictions available for visualization")
            return
        
        # Create comprehensive dashboard
        fig = make_subplots(
            rows=3, cols=2,
            subplot_titles=[
                'CLV Distribution',
                'CLV by RFM Segments',
                'Model Comparison',
                'CLV vs Current Revenue',
                'CLV Segments Analysis',
                'Feature Importance'
            ],
            specs=[
                [{"type": "histogram"}, {"type": "box"}],
                [{"type": "bar"}, {"type": "scatter"}],
                [{"type": "bar"}, {"type": "bar"}]
            ]
        )
        
        clv_values = clv_predictions['ensemble']
        
        # 1. CLV Distribution
        fig.add_trace(
            go.Histogram(
                x=clv_values,
                nbinsx=50,
                name='CLV Distribution',
                marker_color='lightblue'
            ),
            row=1, col=1
        )
        
        # 2. CLV by RFM Segments (if available)
        if self.rfm_data is not None:
            rfm_clv = []
            rfm_segments = []
            for segment in self.rfm_data['RFM_Segment'].unique():
                segment_mask = self.rfm_data['RFM_Segment'] == segment
                if segment_mask.sum() > 0:
                    segment_clv = clv_values[segment_mask]
                    rfm_clv.extend(segment_clv)
                    rfm_segments.extend([segment] * len(segment_clv))
            
            fig.add_trace(
                go.Box(
                    x=rfm_segments,
                    y=rfm_clv,
                    name='CLV by RFM Segment'
                ),
                row=1, col=2
            )
        
        # 3. Model Comparison
        model_names = []
        model_maes = []
        
        for model_name in clv_predictions.keys():
            if model_name != 'ensemble':
                model_names.append(model_name)
                # Calculate MAE for each model (simplified)
                model_mae = mean_absolute_error(clv_values, clv_predictions[model_name])
                model_maes.append(model_mae)
        
        if model_names:
            fig.add_trace(
                go.Bar(
                    x=model_names,
                    y=model_maes,
                    name='Model MAE',
                    marker_color='orange'
                ),
                row=2, col=1
            )
        
        # 4. CLV vs Current Revenue
        if self.customer_data is not None:
            current_revenue = self.customer_data['total_revenue'].values[:len(clv_values)]
            fig.add_trace(
                go.Scatter(
                    x=current_revenue,
                    y=clv_values,
                    mode='markers',
                    name='CLV vs Current Revenue',
                    marker=dict(color='green', opacity=0.6)
                ),
                row=2, col=2
            )
        
        # 5. CLV Segments Analysis
        if 'overall_stats' in segment_analysis:
            segments = ['Low', 'Medium-Low', 'Medium', 'Medium-High', 'High']
            segment_counts = [segment_analysis.get(seg, {}).get('count', 0) for seg in segments]
            
            fig.add_trace(
                go.Bar(
                    x=segments,
                    y=segment_counts,
                    name='Customer Count by CLV Segment',
                    marker_color='purple'
                ),
                row=3, col=1
            )
        
        # 6. Feature Importance (if available from ML models)
        if hasattr(self, 'feature_names') and self.models:
            # Get feature importance from best performing model
            feature_importance = None
            
            if 'random_forest' in self.models:
                feature_importance = self.models['random_forest'].feature_importances_
            elif 'xgboost' in self.models:
                feature_importance = self.models['xgboost'].feature_importances_
            
            if feature_importance is not None and len(feature_importance) == len(self.feature_names):
                # Get top 10 features
                top_indices = np.argsort(feature_importance)[-10:]
                top_features = [self.feature_names[i] for i in top_indices]
                top_importance = feature_importance[top_indices]
                
                fig.add_trace(
                    go.Bar(
                        x=top_importance,
                        y=top_features,
                        orientation='h',
                        name='Feature Importance',
                        marker_color='red'
                    ),
                    row=3, col=2
                )
        
        fig.update_layout(
            title='Customer Lifetime Value Analysis Dashboard',
            height=1200,
            showlegend=True
        )
        
        # Save visualization
        try:
            fig.write_html('/tmp/clv_analysis_dashboard.html')
            mlflow.log_artifact('/tmp/clv_analysis_dashboard.html')
        except Exception as e:
            logger.warning(f"Could not save visualization: {str(e)}")
    
    def save_clv_models(self, model_name: str = "clv_prediction_models"):
        """Save all CLV models and artifacts"""
        logger.info("Saving CLV models...")
        
        # Create model package
        model_artifacts = {
            'models': self.models,
            'scalers': self.scalers,
            'feature_names': self.feature_names,
            'config': self.config,
            'rfm_data': self.rfm_data
        }
        
        # Save locally
        model_path = f'/tmp/{model_name}.joblib'
        joblib.dump(model_artifacts, model_path)
        
        # Log to MLflow
        mlflow.log_artifact(model_path, 'model_artifacts')
        
        # Save individual models
        for name, model in self.models.items():
            if hasattr(model, 'save'):  # TensorFlow model
                tf_model_path = f'/tmp/{name}_model.h5'
                model.save(tf_model_path)
                mlflow.log_artifact(tf_model_path, 'model_artifacts')
        
        # Save to S3
        try:
            s3_key = f"{self.config.model_prefix}{model_name}_{int(datetime.now().timestamp())}.joblib"
            self.s3_client.upload_file(model_path, self.config.s3_bucket, s3_key)
            logger.info(f"Models saved to s3://{self.config.s3_bucket}/{s3_key}")
        except Exception as e:
            logger.warning(f"Could not save to S3: {str(e)}")
        
        return model_path
    
    def run_complete_clv_pipeline(self):
        """Run the complete CLV prediction pipeline"""
        logger.info("Starting complete CLV prediction pipeline...")
        
        with mlflow.start_run(run_name="clv_prediction_pipeline"):
            try:
                # Extract customer data
                customer_data = self.extract_customer_data()
                
                # Calculate RFM metrics
                rfm_data = self.calculate_rfm_metrics(customer_data)
                
                # Engineer features
                features_data = self.engineer_clv_features(rfm_data)
                
                # Initialize results storage
                all_models = {}
                all_predictions = {}
                
                # Train traditional CLV models
                if self.config.use_traditional_models:
                    traditional_summary = self.prepare_traditional_clv_data(customer_data)
                    traditional_models, summary_with_clv = self.train_traditional_clv_models(traditional_summary)
                    all_models.update(traditional_models)
                    
                    # Store traditional CLV predictions for ensemble
                    if 'traditional_clv' in summary_with_clv.columns:
                        self.traditional_clv_predictions = summary_with_clv['traditional_clv'].values
                
                # Train ML models
                if self.config.use_ml_models:
                    ml_models = self.train_ml_clv_models(features_data)
                    all_models.update(ml_models)
                    self.models.update(ml_models)
                
                # Train deep learning model
                dl_model = None
                if self.config.use_deep_learning:
                    dl_model = self.train_deep_learning_clv_model(features_data)
                    if dl_model is not None:
                        all_models['deep_learning'] = dl_model
                        self.models['deep_learning'] = dl_model
                
                # Create ensemble model
                if self.config.use_ensemble:
                    ensemble_predictions = self.create_ensemble_clv_model(
                        self.models, traditional_models if 'traditional_models' in locals() else {},
                        dl_model, features_data
                    )
                    all_predictions.update(ensemble_predictions)
                
                # Analyze CLV segments
                segment_analysis = self.analyze_clv_segments(all_predictions)
                
                # Create visualizations
                self.create_clv_visualizations(all_predictions, segment_analysis)
                
                # Save models
                model_path = self.save_clv_models()
                
                # Register model in MLflow
                if 'ensemble' in all_predictions:
                    # Use the best performing individual model for registration
                    best_model = max(self.models.items(), key=lambda x: x[0] if isinstance(x[1], (RandomForestRegressor, xgb.XGBRegressor)) else 'z')
                    
                    if best_model and hasattr(best_model[1], 'predict'):
                        mlflow.sklearn.log_model(best_model[1], "clv_model")
                        mlflow.register_model(
                            f"runs:/{mlflow.active_run().info.run_id}/clv_model",
                            "customer-lifetime-value-predictor"
                        )
                
                # Log pipeline success
                mlflow.log_metrics({
                    'pipeline_success': 1,
                    'total_customers_analyzed': len(customer_data),
                    'models_trained': len(all_models),
                    'avg_predicted_clv': all_predictions.get('ensemble', [0]).mean() if 'ensemble' in all_predictions else 0
                })
                
                logger.info("CLV prediction pipeline completed successfully")
                
                return {
                    'model_path': model_path,
                    'predictions': all_predictions,
                    'segment_analysis': segment_analysis,
                    'customers_analyzed': len(customer_data),
                    'models_trained': list(all_models.keys())
                }
                
            except Exception as e:
                logger.error(f"CLV prediction pipeline failed: {str(e)}")
                mlflow.log_metric("pipeline_success", 0)
                raise

def main():
    """Main function to run CLV prediction"""
    config = CLVConfig()
    predictor = CLVPredictor(config)
    
    try:
        results = predictor.run_complete_clv_pipeline()
        print(f"CLV prediction completed successfully!")
        print(f"Model path: {results['model_path']}")
        print(f"Customers analyzed: {results['customers_analyzed']}")
        print(f"Models trained: {', '.join(results['models_trained'])}")
        
        if 'ensemble' in results['predictions']:
            avg_clv = results['predictions']['ensemble'].mean()
            print(f"Average predicted CLV: ${avg_clv:.2f}")
        
        if results['segment_analysis']:
            print(f"\nCLV Segment Distribution:")
            for segment, data in results['segment_analysis'].items():
                if segment != 'overall_stats' and isinstance(data, dict):
                    print(f"- {segment}: {data.get('count', 0)} customers ({data.get('percentage', 0):.1f}%)")
        
    except Exception as e:
        logger.error(f"CLV prediction failed: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()