{"name": "ecommerce-analytics-shared-types", "version": "1.0.0", "private": true, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "scripts": {"build": "tsup src/index.ts --format cjs,esm --dts", "dev": "tsup src/index.ts --format cjs,esm --dts --watch", "type-check": "tsc --noEmit", "clean": "rm -rf dist", "generate": "bun run src/scripts/generate.ts"}, "devDependencies": {"tsup": "^8.0.1", "typescript": "^5.3.3"}, "dependencies": {"@hey-api/openapi-ts": "^0.27.38"}}