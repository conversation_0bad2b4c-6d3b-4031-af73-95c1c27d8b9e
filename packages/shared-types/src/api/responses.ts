import { BaseResponse } from '../models/base';
import { Link, LinkAnalytics } from '../models/link';
import { Analytics } from '../models/analytics';
import { User } from '../models/user';

// BFF-specific response types that aggregate data from multiple services
export interface LinkWithAnalyticsResponse extends BaseResponse<LinkWithAnalytics> {}

export interface LinkWithAnalytics extends Link {
  analytics: LinkAnalytics;
  recent_clicks?: Array<{
    clicked_at: string;
    country?: string;
    device_type?: string;
    referrer?: string;
  }>;
  geographic_stats?: Array<{
    country: string;
    clicks: number;
    percentage: number;
  }>;
  device_stats?: Array<{
    device: string;
    clicks: number;
    percentage: number;
  }>;
  hourly_distribution?: Array<{
    hour: number;
    clicks: number;
  }>;
}

export interface DashboardDataResponse extends BaseResponse<DashboardData> {}

export interface DashboardData {
  user: User;
  analytics: Analytics;
  recent_links: Link[];
  top_performing_links: Array<Link & { performance_score: number }>;
  alerts: DashboardAlert[];
}

export interface DashboardAlert {
  id: string;
  type: 'info' | 'warning' | 'error' | 'success';
  title: string;
  message: string;
  action_url?: string;
  created_at: string;
  read: boolean;
}

export interface HealthCheckResponse {
  status: 'healthy' | 'unhealthy';
  timestamp: string;
  version: string;
  services?: Record<string, ServiceStatus>;
}

export interface ServiceStatus {
  status: 'healthy' | 'unhealthy';
  response_time?: number;
  error?: string;
}