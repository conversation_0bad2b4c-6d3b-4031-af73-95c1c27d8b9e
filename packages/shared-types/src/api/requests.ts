// API Request types for BFF endpoints
export interface ApiRequest<T = unknown> {
  data?: T;
  params?: Record<string, string>;
  query?: Record<string, string | number | boolean>;
}

// Aggregated requests that combine data from multiple services
export interface DashboardDataRequest {
  date_range?: {
    from: string;
    to: string;
  };
  metrics?: string[];
  filters?: {
    platform?: string;
    campaign_id?: string;
    user_id?: string;
  };
}

export interface BulkLinkOperationRequest {
  operation: 'activate' | 'deactivate' | 'delete' | 'update';
  link_ids: string[];
  data?: Record<string, unknown>;
}