import { BaseEntity } from './base';

export interface User extends BaseEntity {
  email: string;
  name?: string;
  first_name?: string;
  last_name?: string;
  company?: string;
  role: UserRole;
  is_active: boolean;
  email_verified: boolean;
  last_login_at?: string;
  tenant_id?: string;
  password_hash?: string;
}

export type UserRole = 'admin' | 'user' | 'viewer';

export interface CreateUserRequest {
  email: string;
  password: string;
  first_name?: string;
  last_name?: string;
  company?: string;
  role?: UserRole;
}

export interface UpdateUserRequest {
  first_name?: string;
  last_name?: string;
  company?: string;
  role?: UserRole;
  is_active?: boolean;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  user: User;
  token: string;
  expires_at: string;
}

export interface ChangePasswordRequest {
  current_password: string;
  new_password: string;
}