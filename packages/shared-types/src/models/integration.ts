import { BaseEntity } from './base';

export interface Integration extends BaseEntity {
  tenant_id?: string;
  platform: IntegrationPlatform;
  store_name?: string;
  store_url?: string;
  api_credentials: Record<string, unknown>;
  webhook_url?: string;
  webhook_secret?: string;
  is_active: boolean;
  last_sync_at?: string;
  user_id?: string;
}

export type IntegrationPlatform = 'shopify' | 'woocommerce' | 'amazon';

export interface CreateIntegrationRequest {
  platform: IntegrationPlatform;
  store_name?: string;
  store_url?: string;
  api_credentials: Record<string, unknown>;
  webhook_secret?: string;
}

export interface UpdateIntegrationRequest {
  store_name?: string;
  store_url?: string;
  api_credentials?: Record<string, unknown>;
  webhook_secret?: string;
  is_active?: boolean;
}

export interface Order extends BaseEntity {
  tenant_id?: string;
  integration_id?: string;
  platform_order_id?: string;
  customer_email?: string;
  customer_id?: string;
  total_amount?: number;
  currency?: string;
  status?: string;
  tracking_id?: string;
  order_data?: Record<string, unknown>;
  platform_created_at?: string;
}

export interface OrderItem extends BaseEntity {
  order_id: string;
  product_id?: string;
  variant_id?: string;
  title: string;
  quantity: number;
  price?: number;
  product_data?: Record<string, unknown>;
}

export interface Attribution extends BaseEntity {
  tenant_id?: string;
  click_id?: string;
  order_id?: string;
  link_id?: string;
  campaign_id?: string;
  attribution_model: string;
  conversion_value?: number;
  commission_rate?: number;
  commission_amount?: number;
  time_to_purchase?: number;
}