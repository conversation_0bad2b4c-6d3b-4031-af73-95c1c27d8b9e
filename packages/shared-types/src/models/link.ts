import { BaseEntity } from './base';

export interface Link extends BaseEntity {
  tenant_id?: string;
  campaign_id?: string;
  short_code: string;
  original_url: string;
  target_url?: string;
  title?: string;
  description?: string;
  domain?: string;
  utm_source?: string;
  utm_medium?: string;
  utm_campaign?: string;
  utm_term?: string;
  utm_content?: string;
  is_active: boolean;
  expires_at?: string;
  user_id?: string;
  total_clicks?: number;
  total_conversions?: number;
  total_revenue?: number;
  conversion_rate?: number;
  last_click_at?: string;
  tags?: string[];
}

export interface CreateLinkRequest {
  tenant_id?: string;
  campaign_id?: string;
  target_url: string;
  title?: string;
  description?: string;
  domain?: string;
  custom_code?: string;
  utm_source?: string;
  utm_medium?: string;
  utm_campaign?: string;
  utm_term?: string;
  utm_content?: string;
  expires_at?: string;
}

export interface UpdateLinkRequest {
  target_url?: string;
  title?: string;
  description?: string;
  is_active?: boolean;
  expires_at?: string;
}

export interface LinkAnalytics {
  total_clicks: number;
  unique_clicks: number;
  conversion_rate: number;
  revenue_generated: number;
  last_click_at?: string;
}

export interface CountryClick {
  country: string;
  clicks: number;
}

export interface ReferrerClick {
  referrer: string;
  clicks: number;
}

export interface DeviceClick {
  device_type: string;
  clicks: number;
}

export interface BrowserClick {
  browser: string;
  clicks: number;
}

export interface HourlyClick {
  hour: number;
  clicks: number;
}