import { BaseEntity, DateRange, PaginationParams } from './base';

export interface Click extends BaseEntity {
  link_id: string;
  session_id?: string;
  ip_address?: string;
  user_agent?: string;
  referrer?: string;
  country?: string;
  city?: string;
  device_type?: string;
  browser?: string;
  os?: string;
  clicked_at: string;
  tracking_id?: string;
}

export interface Analytics {
  total_clicks: number;
  total_conversions: number;
  conversion_rate: number;
  total_revenue: number;
  average_order_value: number;
  top_countries: Array<{ country: string; clicks: number; conversions?: number }>;
  time_series: Array<{ date: string; clicks: number; conversions: number; revenue: number }>;
}

export interface AnalyticsSummary {
  total_clicks: number;
  total_conversions: number;
  total_links: number;
  total_revenue: number;
  avg_order_value: number;
  conversion_rate: number;
}

export interface PlatformAnalytics {
  platform: string;
  clicks: number;
  conversions: number;
  revenue: number;
  conversion_rate: number;
}

export interface TrendData {
  date: string;
  clicks: number;
  conversions: number;
  revenue: number;
}

export interface AnalyticsParams extends DateRange, PaginationParams {
  platform?: string;
  link_id?: string;
  integration_id?: string;
}

export interface GeographicAnalytics {
  country: string;
  clicks: number;
  conversions: number;
  revenue: number;
}

export interface TimeSeriesParams extends DateRange {
  metric?: 'clicks' | 'conversions' | 'revenue';
  granularity?: 'hour' | 'day' | 'week' | 'month';
  link_id?: string;
  platform?: string;
}

export interface TopPerformersParams extends DateRange {
  metric?: 'clicks' | 'conversions' | 'revenue' | 'conversion_rate';
  limit?: number;
}

export interface ConversionFunnel {
  steps: ConversionStep[];
}

export interface ConversionStep {
  name: string;
  value: number;
  conversion_rate?: number;
}