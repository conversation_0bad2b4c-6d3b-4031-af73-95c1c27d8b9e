import React, { ReactNode } from 'react';
import ErrorBoundary from './ErrorBoundary';
import { reportApiError } from '../services/errorReporting';

interface ApiErrorBoundaryProps {
  children: ReactNode;
  retry?: () => void;
  resourceName?: string;
}

const ApiErrorFallback: React.FC<{
  retry?: () => void;
  resourceName?: string;
}> = ({ retry, resourceName = 'data' }) => {
  return (
    <div className="bg-white border border-red-200 rounded-lg p-6 max-w-md mx-auto mt-8">
      <div className="flex items-center mb-4">
        <div className="flex-shrink-0">
          <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
            <svg className="w-5 h-5 text-red-600" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
          </div>
        </div>
        <div className="ml-3">
          <h3 className="text-lg font-medium text-gray-900">
            Failed to Load {resourceName}
          </h3>
        </div>
      </div>
      
      <div className="mb-4">
        <p className="text-sm text-gray-600">
          We couldn't load the {resourceName.toLowerCase()}. This might be due to a temporary network issue or server problem.
        </p>
      </div>

      <div className="flex space-x-3">
        {retry && (
          <button
            onClick={retry}
            className="flex-1 bg-red-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
          >
            Try Again
          </button>
        )}
        <button
          onClick={() => window.location.reload()}
          className="flex-1 bg-gray-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500"
        >
          Refresh Page
        </button>
      </div>
    </div>
  );
};

const ApiErrorBoundary: React.FC<ApiErrorBoundaryProps> = ({ 
  children, 
  retry, 
  resourceName 
}) => {
  const handleError = (error: Error) => {
    // Report API error with enhanced context
    const isNetworkError = error.message.includes('fetch') || error.message.includes('network');
    const endpoint = resourceName || 'unknown';

    reportApiError(
      error,
      endpoint,
      'GET', // Default method, could be enhanced to detect actual method
      undefined, // Status code not available in this context
      {
        isNetworkError,
        resourceName,
        timestamp: new Date().toISOString(),
      }
    );

    // Log API errors specifically in development
    if (process.env.NODE_ENV === 'development') {
      console.error(`API Error in ${resourceName || 'component'}:`, error);

      if (isNetworkError) {
        console.error('Network error detected');
      }
    }
  };

  return (
    <ErrorBoundary
      onError={handleError}
      fallback={<ApiErrorFallback retry={retry} resourceName={resourceName} />}
    >
      {children}
    </ErrorBoundary>
  );
};

export default ApiErrorBoundary;