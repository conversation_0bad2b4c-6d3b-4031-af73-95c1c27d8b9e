/**
 * Data Quality Monitoring
 * Automated validation, profiling, and quality assessment with real-time monitoring
 */

import React, { useState, useMemo, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { motion } from 'framer-motion';
import { 
  ShieldCheck,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  TrendingUp,
  TrendingDown,
  Activity,
  Database,
  FileText,
  BarChart3,
  LineChart,
  PieChart,
  Eye,
  Settings,
  Search,
  Filter,
  Download,
  Upload,
  RefreshCw,
  Play,
  Pause,
  Square,
  Calendar,
  Users,
  Package,
  Tag,
  Hash,
  Layers,
  Grid,
  List,
  MoreHorizontal,
  ExternalLink,
  Info,
  Plus,
  Minus,
  Edit,
  Trash2,
  Copy,
  Bookmark,
  Star,
  Award,
  Lightbulb,
  Workflow,
  GitBranch,
  Code,
  Terminal,
  Archive,
  FolderOpen,
  Link,
  Unlink,
  MousePointer,
  Gauge,
  Timer,
  HardDrive,
  MemoryStick,
  Cpu,
  Brain,
  Shield,
  Lock,
  Unlock,
  Key,
  Globe,
  Network,
  Server,
  Cloud,
  Wifi,
  Smartphone,
  Laptop,
  Tablet,
  Watch,
  Construction,
  Rocket,
  FlaskConical,
  Bug,
  CheckCheck,
  AlertCircle,
  Target,
  Crosshair,
  Move,
  RotateCw,
  Repeat,
  Shuffle,
  Split,
  Merge,
  Route,
  Navigation,
  MapPin,
  Compass,
  TrendingFlat,
  ArrowRight,
  ArrowLeft,
  ArrowUp,
  ArrowDown,
  Share,
  Zap,
  Monitor,
  BarChart as BarChartIcon,
  PieChart as Pie,
  Radar,
  Scatter as ScatterIcon,
  Percent,
  Hash as HashIcon,
  Slash,
  Equal,
  NotEqual,
  MoreVertical,
  Expand,
  Minimize,
  Maximize,
  Command,
  Save,
  Undo,
  Redo,
  Cut,
  PaintBucket,
  Brush,
  Eraser,
  Ruler,
  Scissors,
  Paperclip,
  Pin,
  Pushpin,
  Anchor,
  Flag,
  Bell,
  BellOff,
  Volume2,
  VolumeX,
  Mic,
  MicOff,
  TreePine,
  Waves,
  Radio,
  Tv,
  Image,
  Film,
  Music,
  Headphones,
  Speaker
} from 'lucide-react';
import { 
  LineChart as RechartsLineChart, 
  Line, 
  AreaChart, 
  Area, 
  BarChart, 
  Bar, 
  ScatterChart,
  Scatter,
  PieChart as RechartsPieChart,
  Cell,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis
} from 'recharts';

// Types for Data Quality Monitoring
interface DataQualityRule {
  id: string;
  name: string;
  category: 'completeness' | 'accuracy' | 'consistency' | 'validity' | 'uniqueness' | 'timeliness';
  description: string;
  condition: string;
  threshold: number;
  severity: 'critical' | 'high' | 'medium' | 'low';
  enabled: boolean;
  created: Date;
  lastRun: Date;
  violations: number;
  successRate: number;
  dataSource: string;
  field: string;
  operator: string;
  value: string | number;
  tags: string[];
}

interface DataProfile {
  id: string;
  dataSource: string;
  table: string;
  field: string;
  dataType: 'string' | 'number' | 'date' | 'boolean' | 'object';
  totalRecords: number;
  nullCount: number;
  uniqueCount: number;
  duplicateCount: number;
  minValue: any;
  maxValue: any;
  avgValue: number;
  median: number;
  standardDeviation: number;
  valueDistribution: ValueDistribution[];
  patterns: PatternAnalysis[];
  anomalies: AnomalyDetection[];
  qualityScore: number;
  lastProfiled: Date;
  trendData: TrendPoint[];
}

interface ValueDistribution {
  value: string;
  count: number;
  percentage: number;
}

interface PatternAnalysis {
  pattern: string;
  matches: number;
  percentage: number;
  examples: string[];
}

interface AnomalyDetection {
  type: 'outlier' | 'pattern_break' | 'sudden_change' | 'missing_data';
  description: string;
  severity: 'high' | 'medium' | 'low';
  affectedRecords: number;
  timestamp: Date;
  confidence: number;
}

interface TrendPoint {
  timestamp: Date;
  qualityScore: number;
  nullRate: number;
  uniquenessRate: number;
  completenessRate: number;
}

interface QualityAlert {
  id: string;
  ruleId: string;
  ruleName: string;
  severity: 'critical' | 'high' | 'medium' | 'low';
  status: 'active' | 'acknowledged' | 'resolved' | 'suppressed';
  message: string;
  dataSource: string;
  field: string;
  violationCount: number;
  firstOccurrence: Date;
  lastOccurrence: Date;
  assignee?: string;
  resolution?: string;
  metadata: Record<string, any>;
}

interface DataLineage {
  id: string;
  dataSource: string;
  transformations: TransformationStep[];
  dependencies: DataDependency[];
  qualityImpact: QualityImpact[];
  lastUpdated: Date;
}

interface TransformationStep {
  id: string;
  type: 'extract' | 'transform' | 'load' | 'validate' | 'cleanse';
  name: string;
  description: string;
  inputSchema: any;
  outputSchema: any;
  rules: string[];
  qualityChecks: string[];
  performance: {
    duration: number;
    recordsProcessed: number;
    errorRate: number;
  };
}

interface DataDependency {
  id: string;
  sourceSystem: string;
  targetSystem: string;
  dataFlow: string;
  frequency: string;
  lastSync: Date;
  status: 'healthy' | 'degraded' | 'failed';
}

interface QualityImpact {
  system: string;
  impactLevel: 'high' | 'medium' | 'low';
  affectedMetrics: string[];
  mitigationActions: string[];
}

interface QualityReport {
  id: string;
  name: string;
  type: 'scheduled' | 'ad_hoc' | 'triggered';
  period: 'daily' | 'weekly' | 'monthly' | 'quarterly';
  scope: string[];
  metrics: QualityMetric[];
  trends: TrendAnalysis[];
  recommendations: string[];
  generatedDate: Date;
  recipients: string[];
}

interface QualityMetric {
  name: string;
  value: number;
  unit: string;
  target: number;
  status: 'good' | 'warning' | 'critical';
  trend: 'up' | 'down' | 'stable';
  changePercent: number;
}

interface TrendAnalysis {
  metric: string;
  period: string;
  trend: 'improving' | 'declining' | 'stable';
  rate: number;
  significance: 'high' | 'medium' | 'low';
}

// Mock data generators
const generateMockQualityRules = (): DataQualityRule[] => {
  const categories: Array<'completeness' | 'accuracy' | 'consistency' | 'validity' | 'uniqueness' | 'timeliness'> = 
    ['completeness', 'accuracy', 'consistency', 'validity', 'uniqueness', 'timeliness'];
  const severities: Array<'critical' | 'high' | 'medium' | 'low'> = ['critical', 'high', 'medium', 'low'];
  const dataSources = ['Shopify', 'WooCommerce', 'Magento', 'BigCommerce', 'Custom API'];
  
  return Array.from({ length: 25 }, (_, i) => ({
    id: `rule-${i + 1}`,
    name: `${categories[i % categories.length]} Check ${i + 1}`,
    category: categories[i % categories.length],
    description: `Validates ${categories[i % categories.length]} for data integrity`,
    condition: `field IS NOT NULL AND field != ''`,
    threshold: 85 + Math.random() * 15,
    severity: severities[i % severities.length],
    enabled: Math.random() > 0.2,
    created: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000),
    lastRun: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000),
    violations: Math.floor(Math.random() * 1000),
    successRate: 85 + Math.random() * 15,
    dataSource: dataSources[i % dataSources.length],
    field: ['email', 'phone', 'address', 'order_id', 'product_id'][i % 5],
    operator: ['IS_NOT_NULL', 'MATCHES_PATTERN', 'IN_RANGE', 'UNIQUE'][i % 4],
    value: ['', '^[\\w-]+@[\\w-]+\\.[a-z]{2,}$', '0-1000', ''][i % 4],
    tags: ['validation', 'automated', 'critical'].slice(0, Math.floor(Math.random() * 3) + 1)
  }));
};

const generateMockDataProfiles = (): DataProfile[] => {
  const dataSources = ['orders', 'customers', 'products', 'inventory', 'transactions'];
  const fields = ['id', 'email', 'price', 'quantity', 'created_at', 'status'];
  const dataTypes: Array<'string' | 'number' | 'date' | 'boolean' | 'object'> = 
    ['string', 'number', 'date', 'boolean', 'object'];
  
  return Array.from({ length: 15 }, (_, i) => ({
    id: `profile-${i + 1}`,
    dataSource: dataSources[i % dataSources.length],
    table: dataSources[i % dataSources.length],
    field: fields[i % fields.length],
    dataType: dataTypes[i % dataTypes.length],
    totalRecords: Math.floor(Math.random() * 100000) + 10000,
    nullCount: Math.floor(Math.random() * 1000),
    uniqueCount: Math.floor(Math.random() * 50000) + 5000,
    duplicateCount: Math.floor(Math.random() * 500),
    minValue: i % 2 === 0 ? Math.floor(Math.random() * 100) : new Date(2020, 0, 1).toISOString(),
    maxValue: i % 2 === 0 ? Math.floor(Math.random() * 10000) + 1000 : new Date().toISOString(),
    avgValue: Math.random() * 1000,
    median: Math.random() * 800,
    standardDeviation: Math.random() * 200,
    valueDistribution: [],
    patterns: [],
    anomalies: [],
    qualityScore: 75 + Math.random() * 25,
    lastProfiled: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000),
    trendData: Array.from({ length: 30 }, (_, j) => ({
      timestamp: new Date(Date.now() - (29 - j) * 24 * 60 * 60 * 1000),
      qualityScore: 70 + Math.random() * 30,
      nullRate: Math.random() * 10,
      uniquenessRate: 85 + Math.random() * 15,
      completenessRate: 90 + Math.random() * 10
    }))
  }));
};

const generateMockQualityAlerts = (): QualityAlert[] => {
  const severities: Array<'critical' | 'high' | 'medium' | 'low'> = ['critical', 'high', 'medium', 'low'];
  const statuses: Array<'active' | 'acknowledged' | 'resolved' | 'suppressed'> = 
    ['active', 'acknowledged', 'resolved', 'suppressed'];
  
  return Array.from({ length: 12 }, (_, i) => ({
    id: `alert-${i + 1}`,
    ruleId: `rule-${i + 1}`,
    ruleName: `Data Quality Rule ${i + 1}`,
    severity: severities[i % severities.length],
    status: statuses[i % statuses.length],
    message: `Quality threshold violation detected in ${['orders', 'customers', 'products'][i % 3]} data`,
    dataSource: ['Shopify', 'WooCommerce', 'Magento'][i % 3],
    field: ['email', 'phone', 'address', 'order_id'][i % 4],
    violationCount: Math.floor(Math.random() * 500) + 10,
    firstOccurrence: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000),
    lastOccurrence: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000),
    assignee: i % 3 === 0 ? `User ${i + 1}` : undefined,
    resolution: i % 4 === 0 ? 'Data cleansing pipeline deployed' : undefined,
    metadata: {}
  }));
};

// Performance data for charts
const qualityTrendData = Array.from({ length: 30 }, (_, i) => ({
  date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toLocaleDateString(),
  overall: 85 + Math.random() * 15,
  completeness: 90 + Math.random() * 10,
  accuracy: 80 + Math.random() * 20,
  consistency: 85 + Math.random() * 15,
  validity: 88 + Math.random() * 12,
  uniqueness: 92 + Math.random() * 8,
  timeliness: 87 + Math.random() * 13
}));

const ruleViolationData = Array.from({ length: 12 }, (_, i) => ({
  month: new Date(2024, i, 1).toLocaleDateString('en', { month: 'short' }),
  critical: Math.floor(Math.random() * 50) + 10,
  high: Math.floor(Math.random() * 100) + 20,
  medium: Math.floor(Math.random() * 200) + 50,
  low: Math.floor(Math.random() * 300) + 100
}));

const qualityScoreDistribution = [
  { name: 'Excellent (90-100%)', value: 45, color: '#10b981' },
  { name: 'Good (80-89%)', value: 30, color: '#3b82f6' },
  { name: 'Fair (70-79%)', value: 20, color: '#f59e0b' },
  { name: 'Poor (<70%)', value: 5, color: '#ef4444' }
];

const dataSourceQuality = Array.from({ length: 8 }, (_, i) => ({
  source: ['Shopify', 'WooCommerce', 'Magento', 'BigCommerce', 'Custom API', 'Stripe', 'PayPal', 'Amazon'][i],
  completeness: 85 + Math.random() * 15,
  accuracy: 80 + Math.random() * 20,
  consistency: 88 + Math.random() * 12,
  validity: 92 + Math.random() * 8,
  uniqueness: 90 + Math.random() * 10,
  timeliness: 85 + Math.random() * 15
}));

const DataQualityMonitoring: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedRule, setSelectedRule] = useState<string | null>(null);
  const [selectedProfile, setSelectedProfile] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterSeverity, setFilterSeverity] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');

  const qualityRules = useMemo(() => generateMockQualityRules(), []);
  const dataProfiles = useMemo(() => generateMockDataProfiles(), []);
  const qualityAlerts = useMemo(() => generateMockQualityAlerts(), []);

  const filteredRules = useMemo(() => {
    return qualityRules.filter(rule => {
      const matchesSearch = rule.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           rule.description.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesSeverity = filterSeverity === 'all' || rule.severity === filterSeverity;
      const matchesStatus = filterStatus === 'all' || 
                           (filterStatus === 'enabled' && rule.enabled) ||
                           (filterStatus === 'disabled' && !rule.enabled);
      return matchesSearch && matchesSeverity && matchesStatus;
    });
  }, [qualityRules, searchQuery, filterSeverity, filterStatus]);

  const filteredAlerts = useMemo(() => {
    return qualityAlerts.filter(alert => {
      const matchesSearch = alert.ruleName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           alert.message.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesSeverity = filterSeverity === 'all' || alert.severity === filterSeverity;
      const matchesStatus = filterStatus === 'all' || alert.status === filterStatus;
      return matchesSearch && matchesSeverity && matchesStatus;
    });
  }, [qualityAlerts, searchQuery, filterSeverity, filterStatus]);

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'bg-red-500';
      case 'high': return 'bg-orange-500';
      case 'medium': return 'bg-yellow-500';
      case 'low': return 'bg-blue-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-red-100 text-red-800';
      case 'acknowledged': return 'bg-yellow-100 text-yellow-800';
      case 'resolved': return 'bg-green-100 text-green-800';
      case 'suppressed': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getQualityScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 80) return 'text-blue-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Data Quality Monitoring</h1>
          <p className="text-gray-600 mt-2">Automated validation, profiling, and quality assessment</p>
        </div>
        <div className="flex gap-3">
          <Button className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            New Rule
          </Button>
          <Button variant="outline" className="flex items-center gap-2">
            <Play className="h-4 w-4" />
            Run All Checks
          </Button>
          <Button variant="outline" className="flex items-center gap-2">
            <Download className="h-4 w-4" />
            Export Report
          </Button>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-6 w-full">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="rules" className="flex items-center gap-2">
            <ShieldCheck className="h-4 w-4" />
            Quality Rules
          </TabsTrigger>
          <TabsTrigger value="profiles" className="flex items-center gap-2">
            <Database className="h-4 w-4" />
            Data Profiles
          </TabsTrigger>
          <TabsTrigger value="alerts" className="flex items-center gap-2">
            <AlertTriangle className="h-4 w-4" />
            Alerts
          </TabsTrigger>
          <TabsTrigger value="lineage" className="flex items-center gap-2">
            <Workflow className="h-4 w-4" />
            Data Lineage
          </TabsTrigger>
          <TabsTrigger value="reports" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Reports
          </TabsTrigger>
        </TabsList>

        {/* Overview */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Overall Quality Score</p>
                    <p className="text-2xl font-bold text-green-600">94.2%</p>
                  </div>
                  <ShieldCheck className="h-8 w-8 text-green-500" />
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  <span className="text-green-600">↗ 2.1%</span> from last week
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Active Rules</p>
                    <p className="text-2xl font-bold">187</p>
                  </div>
                  <Settings className="h-8 w-8 text-blue-500" />
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  <span className="text-blue-600">+5</span> rules added today
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Open Alerts</p>
                    <p className="text-2xl font-bold text-orange-600">23</p>
                  </div>
                  <AlertTriangle className="h-8 w-8 text-orange-500" />
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  <span className="text-red-600">+3</span> new since yesterday
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Data Sources</p>
                    <p className="text-2xl font-bold">12</p>
                  </div>
                  <Database className="h-8 w-8 text-purple-500" />
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  <span className="text-green-600">All</span> sources healthy
                </p>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <LineChart className="h-5 w-5" />
                  Quality Score Trends
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <RechartsLineChart data={qualityTrendData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis domain={[60, 100]} />
                      <Tooltip />
                      <Legend />
                      <Line type="monotone" dataKey="overall" stroke="#10b981" strokeWidth={2} name="Overall" />
                      <Line type="monotone" dataKey="completeness" stroke="#3b82f6" name="Completeness" />
                      <Line type="monotone" dataKey="accuracy" stroke="#f59e0b" name="Accuracy" />
                      <Line type="monotone" dataKey="consistency" stroke="#8b5cf6" name="Consistency" />
                    </RechartsLineChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <PieChart className="h-5 w-5" />
                  Quality Score Distribution
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <RechartsPieChart>
                      <Pie
                        data={qualityScoreDistribution}
                        cx="50%"
                        cy="50%"
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                        label={({ name, value }) => `${name}: ${value}%`}
                      >
                        {qualityScoreDistribution.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </RechartsPieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Rule Violations by Severity
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={ruleViolationData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="critical" stackId="a" fill="#ef4444" name="Critical" />
                      <Bar dataKey="high" stackId="a" fill="#f97316" name="High" />
                      <Bar dataKey="medium" stackId="a" fill="#eab308" name="Medium" />
                      <Bar dataKey="low" stackId="a" fill="#3b82f6" name="Low" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Radar className="h-5 w-5" />
                  Data Source Quality Radar
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <RadarChart data={dataSourceQuality.slice(0, 5)}>
                      <PolarGrid />
                      <PolarAngleAxis dataKey="source" />
                      <PolarRadiusAxis domain={[60, 100]} />
                      <Radar
                        name="Completeness"
                        dataKey="completeness"
                        stroke="#10b981"
                        fill="#10b981"
                        fillOpacity={0.3}
                      />
                      <Radar
                        name="Accuracy"
                        dataKey="accuracy"
                        stroke="#3b82f6"
                        fill="#3b82f6"
                        fillOpacity={0.3}
                      />
                      <Tooltip />
                      <Legend />
                    </RadarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Quality Rules */}
        <TabsContent value="rules" className="space-y-6">
          <div className="flex gap-4 items-center">
            <div className="flex-1">
              <Input
                placeholder="Search rules..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="max-w-sm"
              />
            </div>
            <select
              value={filterSeverity}
              onChange={(e) => setFilterSeverity(e.target.value)}
              className="border rounded-md px-3 py-2"
            >
              <option value="all">All Severities</option>
              <option value="critical">Critical</option>
              <option value="high">High</option>
              <option value="medium">Medium</option>
              <option value="low">Low</option>
            </select>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="border rounded-md px-3 py-2"
            >
              <option value="all">All Status</option>
              <option value="enabled">Enabled</option>
              <option value="disabled">Disabled</option>
            </select>
          </div>

          <div className="grid grid-cols-1 gap-4">
            {filteredRules.map((rule) => (
              <motion.div
                key={rule.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
              >
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-4 flex-1">
                        <div className={`w-4 h-4 rounded-full mt-1 ${getSeverityColor(rule.severity)}`} />
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h3 className="font-semibold">{rule.name}</h3>
                            <Badge className={`text-xs ${rule.enabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>
                              {rule.enabled ? 'Enabled' : 'Disabled'}
                            </Badge>
                            <Badge variant="outline" className="text-xs">
                              {rule.category}
                            </Badge>
                          </div>
                          <p className="text-sm text-gray-600 mb-2">{rule.description}</p>
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                            <div>
                              <span className="text-gray-500">Data Source:</span>
                              <span className="ml-1 font-medium">{rule.dataSource}</span>
                            </div>
                            <div>
                              <span className="text-gray-500">Field:</span>
                              <span className="ml-1 font-medium">{rule.field}</span>
                            </div>
                            <div>
                              <span className="text-gray-500">Success Rate:</span>
                              <span className={`ml-1 font-medium ${getQualityScoreColor(rule.successRate)}`}>
                                {rule.successRate.toFixed(1)}%
                              </span>
                            </div>
                            <div>
                              <span className="text-gray-500">Violations:</span>
                              <span className="ml-1 font-medium text-red-600">{rule.violations}</span>
                            </div>
                          </div>
                          <div className="flex gap-1 mt-3">
                            {rule.tags.map(tag => (
                              <Badge key={tag} variant="secondary" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          <Play className="h-3 w-3" />
                        </Button>
                        <Button variant="outline" size="sm">
                          <Edit className="h-3 w-3" />
                        </Button>
                        <Button variant="outline" size="sm">
                          <MoreHorizontal className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </TabsContent>

        {/* Data Profiles */}
        <TabsContent value="profiles" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {dataProfiles.map((profile) => (
              <motion.div
                key={profile.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
              >
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Database className="h-5 w-5" />
                      {profile.table}.{profile.field}
                    </CardTitle>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">{profile.dataType}</Badge>
                      <Badge className={`${getQualityScoreColor(profile.qualityScore)}`}>
                        {profile.qualityScore.toFixed(1)}%
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-500">Total Records</span>
                        <p className="font-medium">{profile.totalRecords.toLocaleString()}</p>
                      </div>
                      <div>
                        <span className="text-gray-500">Unique Values</span>
                        <p className="font-medium">{profile.uniqueCount.toLocaleString()}</p>
                      </div>
                      <div>
                        <span className="text-gray-500">Null Values</span>
                        <p className="font-medium text-orange-600">{profile.nullCount.toLocaleString()}</p>
                      </div>
                      <div>
                        <span className="text-gray-500">Duplicates</span>
                        <p className="font-medium text-red-600">{profile.duplicateCount.toLocaleString()}</p>
                      </div>
                    </div>
                    
                    {profile.dataType === 'number' && (
                      <div className="space-y-2">
                        <h4 className="font-semibold text-sm">Statistical Summary</h4>
                        <div className="grid grid-cols-2 gap-2 text-xs">
                          <div className="flex justify-between">
                            <span className="text-gray-500">Min:</span>
                            <span>{profile.minValue}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-500">Max:</span>
                            <span>{profile.maxValue}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-500">Avg:</span>
                            <span>{profile.avgValue.toFixed(2)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-500">Median:</span>
                            <span>{profile.median.toFixed(2)}</span>
                          </div>
                        </div>
                      </div>
                    )}

                    <div className="space-y-2">
                      <h4 className="font-semibold text-sm">Quality Metrics</h4>
                      <div className="space-y-1">
                        <div className="flex justify-between text-xs">
                          <span>Completeness</span>
                          <span className="font-medium">
                            {((profile.totalRecords - profile.nullCount) / profile.totalRecords * 100).toFixed(1)}%
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-1">
                          <div 
                            className="bg-blue-500 h-1 rounded-full" 
                            style={{ 
                              width: `${(profile.totalRecords - profile.nullCount) / profile.totalRecords * 100}%` 
                            }} 
                          />
                        </div>
                      </div>
                      <div className="space-y-1">
                        <div className="flex justify-between text-xs">
                          <span>Uniqueness</span>
                          <span className="font-medium">
                            {(profile.uniqueCount / profile.totalRecords * 100).toFixed(1)}%
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-1">
                          <div 
                            className="bg-green-500 h-1 rounded-full" 
                            style={{ 
                              width: `${profile.uniqueCount / profile.totalRecords * 100}%` 
                            }} 
                          />
                        </div>
                      </div>
                    </div>

                    <div className="flex gap-2">
                      <Button size="sm" variant="outline" className="flex-1">
                        <Eye className="h-3 w-3 mr-1" />
                        View Details
                      </Button>
                      <Button size="sm" variant="outline">
                        <RefreshCw className="h-3 w-3" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </TabsContent>

        {/* Alerts */}
        <TabsContent value="alerts" className="space-y-6">
          <div className="flex gap-4 items-center">
            <div className="flex-1">
              <Input
                placeholder="Search alerts..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="max-w-sm"
              />
            </div>
            <select
              value={filterSeverity}
              onChange={(e) => setFilterSeverity(e.target.value)}
              className="border rounded-md px-3 py-2"
            >
              <option value="all">All Severities</option>
              <option value="critical">Critical</option>
              <option value="high">High</option>
              <option value="medium">Medium</option>
              <option value="low">Low</option>
            </select>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="border rounded-md px-3 py-2"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="acknowledged">Acknowledged</option>
              <option value="resolved">Resolved</option>
              <option value="suppressed">Suppressed</option>
            </select>
          </div>

          <div className="grid grid-cols-1 gap-4">
            {filteredAlerts.map((alert) => (
              <motion.div
                key={alert.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
              >
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-4 flex-1">
                        <div className={`w-4 h-4 rounded-full mt-1 ${getSeverityColor(alert.severity)}`} />
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h3 className="font-semibold">{alert.ruleName}</h3>
                            <Badge className={getStatusColor(alert.status)}>
                              {alert.status}
                            </Badge>
                            <Badge variant="outline">
                              {alert.severity}
                            </Badge>
                          </div>
                          <p className="text-sm text-gray-600 mb-2">{alert.message}</p>
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                            <div>
                              <span className="text-gray-500">Data Source:</span>
                              <span className="ml-1 font-medium">{alert.dataSource}</span>
                            </div>
                            <div>
                              <span className="text-gray-500">Field:</span>
                              <span className="ml-1 font-medium">{alert.field}</span>
                            </div>
                            <div>
                              <span className="text-gray-500">Violations:</span>
                              <span className="ml-1 font-medium text-red-600">{alert.violationCount}</span>
                            </div>
                            <div>
                              <span className="text-gray-500">First Seen:</span>
                              <span className="ml-1 font-medium">{alert.firstOccurrence.toLocaleDateString()}</span>
                            </div>
                          </div>
                          {alert.assignee && (
                            <div className="mt-2 text-sm">
                              <span className="text-gray-500">Assigned to:</span>
                              <span className="ml-1 font-medium">{alert.assignee}</span>
                            </div>
                          )}
                          {alert.resolution && (
                            <div className="mt-2 p-2 bg-green-50 rounded text-sm">
                              <span className="text-green-700 font-medium">Resolution: </span>
                              <span className="text-green-600">{alert.resolution}</span>
                            </div>
                          )}
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          <CheckCircle className="h-3 w-3" />
                        </Button>
                        <Button variant="outline" size="sm">
                          <Edit className="h-3 w-3" />
                        </Button>
                        <Button variant="outline" size="sm">
                          <MoreHorizontal className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </TabsContent>

        {/* Data Lineage */}
        <TabsContent value="lineage" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Workflow className="h-5 w-5" />
                Data Flow Visualization
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-96 bg-gray-50 border-2 border-dashed border-gray-200 rounded-lg flex items-center justify-center">
                <div className="text-center text-gray-500">
                  <Workflow className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Interactive data lineage visualization</p>
                  <p className="text-sm">Coming soon...</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Network className="h-5 w-5" />
                  System Dependencies
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {['Shopify → Data Lake', 'Data Lake → Analytics DB', 'Analytics DB → Reporting', 'External API → Cache'].map((dep, i) => (
                    <div key={i} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-3 h-3 bg-green-500 rounded-full" />
                        <span className="font-medium">{dep}</span>
                      </div>
                      <Badge variant="outline">Healthy</Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5" />
                  Quality Impact Analysis
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="p-3 border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium">Customer Data Quality</span>
                      <Badge className="bg-red-100 text-red-800">High Impact</Badge>
                    </div>
                    <p className="text-sm text-gray-600">Affects customer segmentation and personalization</p>
                  </div>
                  <div className="p-3 border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium">Product Data Accuracy</span>
                      <Badge className="bg-yellow-100 text-yellow-800">Medium Impact</Badge>
                    </div>
                    <p className="text-sm text-gray-600">Impacts inventory management and recommendations</p>
                  </div>
                  <div className="p-3 border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium">Order Data Timeliness</span>
                      <Badge className="bg-blue-100 text-blue-800">Low Impact</Badge>
                    </div>
                    <p className="text-sm text-gray-600">Minor effect on reporting latency</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Reports */}
        <TabsContent value="reports" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {['Daily Quality Summary', 'Weekly Trend Analysis', 'Monthly Executive Report', 'Data Source Health Check', 'Anomaly Detection Report', 'Compliance Audit Report'].map((report, i) => (
              <motion.div
                key={i}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: i * 0.1 }}
              >
                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div>
                        <h3 className="font-semibold">{report}</h3>
                        <p className="text-sm text-gray-600">Last generated: {new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toLocaleDateString()}</p>
                      </div>
                      <FileText className="h-6 w-6 text-blue-500" />
                    </div>
                    <div className="space-y-2 mb-4">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-500">Format:</span>
                        <span>PDF, Excel</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-500">Recipients:</span>
                        <span>{Math.floor(Math.random() * 10) + 3} users</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-500">Frequency:</span>
                        <span>{['Daily', 'Weekly', 'Monthly'][i % 3]}</span>
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <Button size="sm" className="flex-1">
                        <Download className="h-3 w-3 mr-1" />
                        Download
                      </Button>
                      <Button variant="outline" size="sm">
                        <Settings className="h-3 w-3" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Scheduled Reports
              </CardTitle>
              <Button className="flex items-center gap-2">
                <Plus className="h-4 w-4" />
                New Schedule
              </Button>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  { name: 'Executive Dashboard', schedule: 'Monday 9:00 AM', recipients: 'C-level executives', status: 'Active' },
                  { name: 'Operations Report', schedule: 'Daily 6:00 AM', recipients: 'Operations team', status: 'Active' },
                  { name: 'Data Quality Alerts', schedule: 'Real-time', recipients: 'Data engineers', status: 'Active' },
                  { name: 'Compliance Report', schedule: 'First of month', recipients: 'Compliance team', status: 'Paused' }
                ].map((schedule, i) => (
                  <div key={i} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center gap-4">
                      <div className={`w-3 h-3 rounded-full ${schedule.status === 'Active' ? 'bg-green-500' : 'bg-gray-500'}`} />
                      <div>
                        <h4 className="font-medium">{schedule.name}</h4>
                        <p className="text-sm text-gray-600">{schedule.schedule} • {schedule.recipients}</p>
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <Badge className={schedule.status === 'Active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
                        {schedule.status}
                      </Badge>
                      <Button variant="outline" size="sm">
                        <Edit className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default DataQualityMonitoring;