/**
 * Real-time Streaming
 * Advanced Kafka/Pulsar event processing with stream analytics and monitoring
 */

import React, { useState, useMemo, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { motion } from 'framer-motion';
import { 
  Activity,
  Zap,
  Radio,
  Waves,
  Gauge,
  TrendingUp,
  TrendingDown,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  Timer,
  Play,
  Pause,
  Square,
  SkipForward,
  SkipBack,
  RefreshCw,
  Settings,
  Monitor,
  BarChart3,
  LineChart,
  PieChart,
  Eye,
  Download,
  Upload,
  Share,
  Filter,
  Search,
  Calendar,
  Users,
  Package,
  Tag,
  Hash,
  Layers,
  Grid,
  List,
  MoreHorizontal,
  ExternalLink,
  Info,
  Plus,
  Minus,
  Edit,
  Trash2,
  Copy,
  Bookmark,
  Star,
  Award,
  Lightbulb,
  Workflow,
  GitBranch,
  Code,
  Terminal,
  FileText,
  Archive,
  FolderOpen,
  Link,
  Unlink,
  MousePointer,
  Database,
  Server,
  Cloud,
  Network,
  Globe,
  Shield,
  Lock,
  Unlock,
  Key,
  Wifi,
  Smartphone,
  Laptop,
  Tablet,
  Watch,
  Construction,
  Rocket,
  FlaskConical,
  Bug,
  CheckCheck,
  AlertCircle,
  Target,
  Crosshair,
  Move,
  RotateCw,
  Repeat,
  Shuffle,
  Split,
  Merge,
  Route,
  Navigation,
  Compass,
  MapPin,
  Cpu,
  MemoryStick,
  HardDrive,
  Brain
} from 'lucide-react';
import { 
  LineChart as RechartsLineChart, 
  Line, 
  AreaChart, 
  Area, 
  BarChart, 
  Bar, 
  ScatterChart,
  Scatter,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  Cell,
  PieChart as RechartsPieChart,
  Pie,
  RadialBarChart,
  RadialBar,
  ComposedChart,
  ReferenceLine
} from 'recharts';

// Types
interface StreamingTopic {
  id: string;
  name: string;
  platform: 'kafka' | 'pulsar' | 'kinesis' | 'eventhubs';
  status: 'active' | 'paused' | 'error' | 'creating' | 'deleting';
  partitions: number;
  replicationFactor: number;
  retentionPolicy: {
    size: number; // MB
    time: number; // hours
    strategy: 'size' | 'time' | 'compact';
  };
  configuration: {
    compressionType: 'none' | 'gzip' | 'snappy' | 'lz4' | 'zstd';
    cleanupPolicy: 'delete' | 'compact' | 'delete,compact';
    maxMessageSize: number; // bytes
    batchSize: number;
    lingerMs: number;
    acks: 'all' | '1' | '0';
  };
  metrics: {
    messagesPerSecond: number;
    bytesPerSecond: number;
    producerCount: number;
    consumerCount: number;
    lag: number;
    diskUsage: number; // MB
    networkIO: number; // MB/s
  };
  schema: {
    format: 'json' | 'avro' | 'protobuf' | 'string';
    version: number;
    registry: string;
    evolution: 'backward' | 'forward' | 'full' | 'none';
    definition: any;
  };
  security: {
    encryption: boolean;
    authentication: boolean;
    authorization: boolean;
    acls: ACL[];
  };
  monitoring: {
    alertsEnabled: boolean;
    thresholds: {
      lagAlert: number;
      throughputAlert: number;
      errorRateAlert: number;
    };
  };
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
}

interface ACL {
  principal: string;
  operation: 'read' | 'write' | 'create' | 'delete' | 'alter' | 'describe' | 'all';
  permissionType: 'allow' | 'deny';
  host: string;
}

interface StreamProcessor {
  id: string;
  name: string;
  type: 'kafka_streams' | 'flink' | 'spark' | 'custom';
  status: 'running' | 'stopped' | 'error' | 'starting' | 'stopping';
  description: string;
  inputTopics: string[];
  outputTopics: string[];
  processingLogic: {
    type: 'filter' | 'map' | 'aggregate' | 'join' | 'window' | 'complex';
    configuration: any;
    code?: string;
  };
  windowing: {
    type: 'tumbling' | 'sliding' | 'session' | 'none';
    size: number; // seconds
    advance?: number; // seconds for sliding windows
    grace?: number; // seconds for late arrivals
  };
  stateStore: {
    enabled: boolean;
    type: 'rocks_db' | 'in_memory' | 'external';
    persistence: boolean;
    backup: boolean;
  };
  scaling: {
    instances: number;
    autoScaling: boolean;
    minInstances: number;
    maxInstances: number;
    cpuThreshold: number;
    memoryThreshold: number;
  };
  metrics: {
    recordsProcessed: number;
    recordsPerSecond: number;
    processingLatency: number; // ms
    errorRate: number;
    throughput: number;
    cpuUsage: number;
    memoryUsage: number;
  };
  checkpointing: {
    enabled: boolean;
    interval: number; // seconds
    timeout: number; // seconds
    strategy: 'exactly_once' | 'at_least_once';
  };
  errorHandling: {
    strategy: 'fail' | 'skip' | 'retry' | 'dlq';
    retryPolicy?: {
      maxRetries: number;
      backoffMs: number;
      backoffMultiplier: number;
    };
    deadLetterQueue?: string;
  };
  deployment: {
    environment: 'development' | 'staging' | 'production';
    resources: {
      cpu: number;
      memory: number; // MB
      storage: number; // GB
    };
    replicas: number;
  };
  createdAt: Date;
  updatedAt: Date;
}

interface StreamConsumer {
  id: string;
  name: string;
  groupId: string;
  topicName: string;
  status: 'running' | 'stopped' | 'error' | 'rebalancing';
  partitionAssignment: PartitionAssignment[];
  offsetPosition: 'earliest' | 'latest' | 'specific';
  autoCommit: boolean;
  commitInterval: number; // ms
  maxPollRecords: number;
  sessionTimeout: number; // ms
  heartbeatInterval: number; // ms
  metrics: {
    recordsConsumed: number;
    recordsPerSecond: number;
    bytesConsumed: number;
    avgRecordSize: number;
    lag: number;
    processingTime: number; // ms
    errorCount: number;
    lastCommittedOffset: number;
    assignedPartitions: number;
  };
  processing: {
    batchSize: number;
    parallelism: number;
    backpressure: boolean;
    errorHandling: 'skip' | 'retry' | 'fail';
  };
  configuration: {
    fetchMinBytes: number;
    fetchMaxWaitMs: number;
    maxPartitionFetchBytes: number;
    enableAutoCommit: boolean;
    autoOffsetReset: 'earliest' | 'latest' | 'none';
  };
  createdAt: Date;
  lastSeen: Date;
}

interface PartitionAssignment {
  partition: number;
  currentOffset: number;
  endOffset: number;
  lag: number;
  leader: string;
}

interface StreamAlert {
  id: string;
  type: 'lag' | 'throughput' | 'error_rate' | 'partition_skew' | 'consumer_failure' | 'topic_full';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  topic?: string;
  consumer?: string;
  processor?: string;
  threshold: number;
  currentValue: number;
  timestamp: Date;
  status: 'active' | 'acknowledged' | 'resolved';
  resolvedAt?: Date;
  assignee?: string;
}

interface EventSchema {
  id: string;
  name: string;
  version: number;
  format: 'json' | 'avro' | 'protobuf';
  compatibility: 'backward' | 'forward' | 'full' | 'none';
  definition: any;
  topics: string[];
  validation: {
    enabled: boolean;
    strict: boolean;
    allowNull: boolean;
  };
  evolution: {
    previousVersions: number[];
    migrationRules: any[];
    deprecationDate?: Date;
  };
  metadata: {
    description: string;
    owner: string;
    tags: string[];
    documentation: string;
  };
  usage: {
    producers: number;
    consumers: number;
    messagesPerDay: number;
  };
  createdAt: Date;
  updatedAt: Date;
}

interface StreamingCluster {
  id: string;
  name: string;
  platform: 'kafka' | 'pulsar';
  environment: 'development' | 'staging' | 'production';
  status: 'healthy' | 'degraded' | 'critical' | 'maintenance';
  nodes: ClusterNode[];
  configuration: {
    version: string;
    replicationFactor: number;
    minInsyncReplicas: number;
    uncleanLeaderElection: boolean;
    logRetentionHours: number;
    logSegmentBytes: number;
  };
  monitoring: {
    brokerCount: number;
    topicCount: number;
    partitionCount: number;
    underReplicatedPartitions: number;
    offlinePartitions: number;
    activeControllers: number;
  };
  performance: {
    messagesPerSecond: number;
    bytesInPerSecond: number;
    bytesOutPerSecond: number;
    requestsPerSecond: number;
    networkUtilization: number;
    diskUtilization: number;
  };
  alerts: StreamAlert[];
}

interface ClusterNode {
  id: string;
  host: string;
  port: number;
  role: 'broker' | 'controller' | 'proxy';
  status: 'online' | 'offline' | 'failed';
  leader: boolean;
  partitionsLeading: number;
  partitionsFollowing: number;
  resources: {
    cpuUsage: number;
    memoryUsage: number;
    diskUsage: number;
    networkIn: number;
    networkOut: number;
  };
  uptime: number; // seconds
  lastSeen: Date;
}

// Mock data generators
const generateStreamingTopics = (): StreamingTopic[] => {
  const platforms: StreamingTopic['platform'][] = ['kafka', 'pulsar', 'kinesis'];
  const statuses: StreamingTopic['status'][] = ['active', 'paused', 'error'];

  return Array.from({ length: 20 }, (_, index) => ({
    id: `TOPIC-${String(index + 1).padStart(3, '0')}`,
    name: `${['orders', 'customers', 'products', 'inventory', 'analytics', 'events'][Math.floor(Math.random() * 6)]}-stream-${index + 1}`,
    platform: platforms[Math.floor(Math.random() * platforms.length)],
    status: statuses[Math.floor(Math.random() * statuses.length)],
    partitions: Math.floor(Math.random() * 32) + 1,
    replicationFactor: Math.floor(Math.random() * 3) + 1,
    retentionPolicy: {
      size: Math.floor(Math.random() * 10000) + 1000,
      time: Math.floor(Math.random() * 168) + 24,
      strategy: ['size', 'time', 'compact'][Math.floor(Math.random() * 3)] as any
    },
    configuration: {
      compressionType: ['none', 'gzip', 'snappy', 'lz4'][Math.floor(Math.random() * 4)] as any,
      cleanupPolicy: ['delete', 'compact', 'delete,compact'][Math.floor(Math.random() * 3)] as any,
      maxMessageSize: Math.floor(Math.random() * 10000000) + 1000000,
      batchSize: Math.floor(Math.random() * 100000) + 16384,
      lingerMs: Math.floor(Math.random() * 100) + 5,
      acks: ['all', '1', '0'][Math.floor(Math.random() * 3)] as any
    },
    metrics: {
      messagesPerSecond: Math.floor(Math.random() * 10000) + 100,
      bytesPerSecond: Math.floor(Math.random() * 1000000) + 10000,
      producerCount: Math.floor(Math.random() * 20) + 1,
      consumerCount: Math.floor(Math.random() * 50) + 1,
      lag: Math.floor(Math.random() * 10000),
      diskUsage: Math.floor(Math.random() * 1000) + 100,
      networkIO: Math.floor(Math.random() * 100) + 10
    },
    schema: {
      format: ['json', 'avro', 'protobuf'][Math.floor(Math.random() * 3)] as any,
      version: Math.floor(Math.random() * 10) + 1,
      registry: 'schema-registry.internal:8081',
      evolution: ['backward', 'forward', 'full'][Math.floor(Math.random() * 3)] as any,
      definition: {}
    },
    security: {
      encryption: Math.random() > 0.3,
      authentication: Math.random() > 0.2,
      authorization: Math.random() > 0.4,
      acls: []
    },
    monitoring: {
      alertsEnabled: Math.random() > 0.2,
      thresholds: {
        lagAlert: 10000,
        throughputAlert: 1000000,
        errorRateAlert: 0.05
      }
    },
    tags: ['production', 'critical', 'real-time'].slice(0, Math.floor(Math.random() * 3) + 1),
    createdAt: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000)
  }));
};

const generateStreamProcessors = (): StreamProcessor[] => {
  const types: StreamProcessor['type'][] = ['kafka_streams', 'flink', 'spark', 'custom'];
  const statuses: StreamProcessor['status'][] = ['running', 'stopped', 'error'];

  return Array.from({ length: 12 }, (_, index) => ({
    id: `PROC-${String(index + 1).padStart(3, '0')}`,
    name: `Stream Processor ${index + 1}`,
    type: types[Math.floor(Math.random() * types.length)],
    status: statuses[Math.floor(Math.random() * statuses.length)],
    description: `Real-time data processing for ${['analytics', 'enrichment', 'filtering', 'aggregation'][Math.floor(Math.random() * 4)]}`,
    inputTopics: [`input-topic-${index + 1}`],
    outputTopics: [`output-topic-${index + 1}`],
    processingLogic: {
      type: ['filter', 'map', 'aggregate', 'join'][Math.floor(Math.random() * 4)] as any,
      configuration: {},
      code: 'stream.filter(record => record.value > threshold)'
    },
    windowing: {
      type: ['tumbling', 'sliding', 'session', 'none'][Math.floor(Math.random() * 4)] as any,
      size: Math.floor(Math.random() * 3600) + 60,
      advance: Math.floor(Math.random() * 300) + 30,
      grace: Math.floor(Math.random() * 60) + 10
    },
    stateStore: {
      enabled: Math.random() > 0.3,
      type: ['rocks_db', 'in_memory', 'external'][Math.floor(Math.random() * 3)] as any,
      persistence: Math.random() > 0.5,
      backup: Math.random() > 0.4
    },
    scaling: {
      instances: Math.floor(Math.random() * 10) + 1,
      autoScaling: Math.random() > 0.6,
      minInstances: 1,
      maxInstances: 20,
      cpuThreshold: 70,
      memoryThreshold: 80
    },
    metrics: {
      recordsProcessed: Math.floor(Math.random() * 1000000) + 10000,
      recordsPerSecond: Math.floor(Math.random() * 5000) + 100,
      processingLatency: Math.floor(Math.random() * 1000) + 10,
      errorRate: Math.random() * 0.05,
      throughput: Math.floor(Math.random() * 1000000) + 10000,
      cpuUsage: Math.random() * 100,
      memoryUsage: Math.random() * 100
    },
    checkpointing: {
      enabled: Math.random() > 0.2,
      interval: Math.floor(Math.random() * 300) + 60,
      timeout: Math.floor(Math.random() * 600) + 300,
      strategy: ['exactly_once', 'at_least_once'][Math.floor(Math.random() * 2)] as any
    },
    errorHandling: {
      strategy: ['fail', 'skip', 'retry', 'dlq'][Math.floor(Math.random() * 4)] as any,
      retryPolicy: {
        maxRetries: 3,
        backoffMs: 1000,
        backoffMultiplier: 2
      },
      deadLetterQueue: 'error-topic'
    },
    deployment: {
      environment: ['development', 'staging', 'production'][Math.floor(Math.random() * 3)] as any,
      resources: {
        cpu: Math.floor(Math.random() * 8) + 1,
        memory: Math.floor(Math.random() * 16) + 2,
        storage: Math.floor(Math.random() * 100) + 10
      },
      replicas: Math.floor(Math.random() * 5) + 1
    },
    createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000)
  }));
};

const generateStreamConsumers = (topics: StreamingTopic[]): StreamConsumer[] => {
  return Array.from({ length: 15 }, (_, index) => {
    const topic = topics[Math.floor(Math.random() * topics.length)];
    return {
      id: `CONS-${String(index + 1).padStart(3, '0')}`,
      name: `Consumer ${index + 1}`,
      groupId: `consumer-group-${Math.floor(index / 3) + 1}`,
      topicName: topic.name,
      status: ['running', 'stopped', 'error', 'rebalancing'][Math.floor(Math.random() * 4)] as any,
      partitionAssignment: Array.from({ length: Math.min(topic.partitions, 5) }, (_, i) => ({
        partition: i,
        currentOffset: Math.floor(Math.random() * 100000),
        endOffset: Math.floor(Math.random() * 100000) + 100000,
        lag: Math.floor(Math.random() * 1000),
        leader: `broker-${Math.floor(Math.random() * 3) + 1}`
      })),
      offsetPosition: ['earliest', 'latest', 'specific'][Math.floor(Math.random() * 3)] as any,
      autoCommit: Math.random() > 0.3,
      commitInterval: Math.floor(Math.random() * 5000) + 1000,
      maxPollRecords: Math.floor(Math.random() * 1000) + 100,
      sessionTimeout: Math.floor(Math.random() * 30000) + 10000,
      heartbeatInterval: Math.floor(Math.random() * 5000) + 3000,
      metrics: {
        recordsConsumed: Math.floor(Math.random() * 100000) + 1000,
        recordsPerSecond: Math.floor(Math.random() * 1000) + 50,
        bytesConsumed: Math.floor(Math.random() * 1000000) + 10000,
        avgRecordSize: Math.floor(Math.random() * 10000) + 1000,
        lag: Math.floor(Math.random() * 5000),
        processingTime: Math.floor(Math.random() * 100) + 10,
        errorCount: Math.floor(Math.random() * 100),
        lastCommittedOffset: Math.floor(Math.random() * 100000),
        assignedPartitions: Math.min(topic.partitions, Math.floor(Math.random() * 5) + 1)
      },
      processing: {
        batchSize: Math.floor(Math.random() * 1000) + 100,
        parallelism: Math.floor(Math.random() * 10) + 1,
        backpressure: Math.random() > 0.7,
        errorHandling: ['skip', 'retry', 'fail'][Math.floor(Math.random() * 3)] as any
      },
      configuration: {
        fetchMinBytes: Math.floor(Math.random() * 10000) + 1,
        fetchMaxWaitMs: Math.floor(Math.random() * 1000) + 500,
        maxPartitionFetchBytes: Math.floor(Math.random() * 1000000) + 1048576,
        enableAutoCommit: Math.random() > 0.3,
        autoOffsetReset: ['earliest', 'latest', 'none'][Math.floor(Math.random() * 3)] as any
      },
      createdAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000),
      lastSeen: new Date(Date.now() - Math.random() * 60 * 60 * 1000)
    };
  });
};

const generateStreamAlerts = (): StreamAlert[] => {
  const types: StreamAlert['type'][] = ['lag', 'throughput', 'error_rate', 'partition_skew', 'consumer_failure'];
  const severities: StreamAlert['severity'][] = ['low', 'medium', 'high', 'critical'];

  return Array.from({ length: 8 }, (_, index) => ({
    id: `ALERT-${String(index + 1).padStart(3, '0')}`,
    type: types[Math.floor(Math.random() * types.length)],
    severity: severities[Math.floor(Math.random() * severities.length)],
    title: `Stream Alert ${index + 1}`,
    description: `Alert description for ${types[Math.floor(Math.random() * types.length)]} issue`,
    topic: `topic-${Math.floor(Math.random() * 10) + 1}`,
    threshold: Math.random() * 1000,
    currentValue: Math.random() * 1200,
    timestamp: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000),
    status: ['active', 'acknowledged', 'resolved'][Math.floor(Math.random() * 3)] as any,
    resolvedAt: Math.random() > 0.5 ? new Date() : undefined
  }));
};

// Components
const TopicCard: React.FC<{ topic: StreamingTopic }> = ({ topic }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'paused': return 'bg-yellow-100 text-yellow-800';
      case 'error': return 'bg-red-100 text-red-800';
      case 'creating': return 'bg-blue-100 text-blue-800';
      case 'deleting': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <Radio className="h-3 w-3" />;
      case 'paused': return <Pause className="h-3 w-3" />;
      case 'error': return <XCircle className="h-3 w-3" />;
      case 'creating': return <RefreshCw className="h-3 w-3 animate-spin" />;
      case 'deleting': return <Trash2 className="h-3 w-3" />;
      default: return <Info className="h-3 w-3" />;
    }
  };

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case 'kafka': return <Activity className="h-4 w-4" />;
      case 'pulsar': return <Zap className="h-4 w-4" />;
      case 'kinesis': return <Waves className="h-4 w-4" />;
      default: return <Radio className="h-4 w-4" />;
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className="border rounded-lg p-4 hover:shadow-md transition-shadow"
    >
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-2">
          {getPlatformIcon(topic.platform)}
          <div>
            <h4 className="font-medium">{topic.name}</h4>
            <p className="text-sm text-gray-600 capitalize">{topic.platform}</p>
          </div>
        </div>
        <Badge className={getStatusColor(topic.status)}>
          {getStatusIcon(topic.status)}
          {topic.status}
        </Badge>
      </div>

      <div className="grid grid-cols-2 gap-4 mb-3">
        <div>
          <p className="text-sm text-gray-600">Messages/sec</p>
          <p className="text-lg font-semibold">{topic.metrics.messagesPerSecond.toLocaleString()}</p>
        </div>
        <div>
          <p className="text-sm text-gray-600">Lag</p>
          <p className="text-lg font-semibold">{topic.metrics.lag.toLocaleString()}</p>
        </div>
        <div>
          <p className="text-sm text-gray-600">Partitions</p>
          <p className="text-lg font-semibold">{topic.partitions}</p>
        </div>
        <div>
          <p className="text-sm text-gray-600">Consumers</p>
          <p className="text-lg font-semibold">{topic.metrics.consumerCount}</p>
        </div>
      </div>

      <div className="mb-3">
        <div className="flex items-center justify-between text-sm mb-1">
          <span className="text-gray-600">Disk Usage</span>
          <span>{topic.metrics.diskUsage} MB</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-blue-600 h-2 rounded-full"
            style={{ width: `${Math.min((topic.metrics.diskUsage / topic.retentionPolicy.size) * 100, 100)}%` }}
          />
        </div>
      </div>

      <div className="mb-3">
        <div className="flex items-center gap-2 text-sm">
          {topic.security.encryption && <Lock className="h-3 w-3 text-green-500" />}
          {topic.security.authentication && <Key className="h-3 w-3 text-blue-500" />}
          {topic.monitoring.alertsEnabled && <Bell className="h-3 w-3 text-yellow-500" />}
          <span className="text-gray-600">Schema: {topic.schema.format}</span>
        </div>
      </div>

      <div className="flex items-center justify-between">
        <div className="flex flex-wrap gap-1">
          {topic.tags.slice(0, 2).map((tag, index) => (
            <Badge key={index} variant="outline" className="text-xs">
              {tag}
            </Badge>
          ))}
          {topic.tags.length > 2 && (
            <Badge variant="outline" className="text-xs">
              +{topic.tags.length - 2}
            </Badge>
          )}
        </div>
        <div className="flex items-center gap-2">
          <Button size="sm" variant="outline">
            <Eye className="h-3 w-3 mr-1" />
            Monitor
          </Button>
          <Button size="sm" variant="ghost">
            <Settings className="h-3 w-3" />
          </Button>
        </div>
      </div>
    </motion.div>
  );
};

const ProcessorCard: React.FC<{ processor: StreamProcessor }> = ({ processor }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'bg-green-100 text-green-800';
      case 'stopped': return 'bg-gray-100 text-gray-800';
      case 'error': return 'bg-red-100 text-red-800';
      case 'starting': return 'bg-blue-100 text-blue-800';
      case 'stopping': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running': return <Play className="h-3 w-3" />;
      case 'stopped': return <Square className="h-3 w-3" />;
      case 'error': return <XCircle className="h-3 w-3" />;
      case 'starting': return <RefreshCw className="h-3 w-3 animate-spin" />;
      case 'stopping': return <Pause className="h-3 w-3" />;
      default: return <Info className="h-3 w-3" />;
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'kafka_streams': return <Activity className="h-4 w-4" />;
      case 'flink': return <Zap className="h-4 w-4" />;
      case 'spark': return <Brain className="h-4 w-4" />;
      case 'custom': return <Code className="h-4 w-4" />;
      default: return <Workflow className="h-4 w-4" />;
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="border rounded-lg p-4 hover:shadow-md transition-shadow"
    >
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-2">
          {getTypeIcon(processor.type)}
          <div>
            <h4 className="font-medium">{processor.name}</h4>
            <p className="text-sm text-gray-600">{processor.type.replace('_', ' ')}</p>
          </div>
        </div>
        <Badge className={getStatusColor(processor.status)}>
          {getStatusIcon(processor.status)}
          {processor.status}
        </Badge>
      </div>

      <p className="text-sm text-gray-600 mb-3 line-clamp-2">{processor.description}</p>

      <div className="grid grid-cols-2 gap-4 mb-3">
        <div>
          <p className="text-sm text-gray-600">Records/sec</p>
          <p className="text-lg font-semibold">{processor.metrics.recordsPerSecond.toLocaleString()}</p>
        </div>
        <div>
          <p className="text-sm text-gray-600">Latency</p>
          <p className="text-lg font-semibold">{processor.metrics.processingLatency}ms</p>
        </div>
        <div>
          <p className="text-sm text-gray-600">Error Rate</p>
          <p className="text-lg font-semibold">{(processor.metrics.errorRate * 100).toFixed(2)}%</p>
        </div>
        <div>
          <p className="text-sm text-gray-600">Instances</p>
          <p className="text-lg font-semibold">{processor.scaling.instances}</p>
        </div>
      </div>

      <div className="mb-3">
        <div className="flex items-center justify-between text-sm mb-1">
          <span className="text-gray-600">CPU Usage</span>
          <span>{processor.metrics.cpuUsage.toFixed(1)}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-blue-600 h-2 rounded-full"
            style={{ width: `${processor.metrics.cpuUsage}%` }}
          />
        </div>
      </div>

      <div className="mb-3">
        <p className="text-sm text-gray-600 mb-1">Processing Flow</p>
        <div className="flex items-center gap-2 text-sm">
          <Badge variant="outline" className="text-xs">
            {processor.inputTopics[0]}
          </Badge>
          <ArrowRight className="h-3 w-3 text-gray-400" />
          <Badge variant="outline" className="text-xs">
            {processor.processingLogic.type}
          </Badge>
          <ArrowRight className="h-3 w-3 text-gray-400" />
          <Badge variant="outline" className="text-xs">
            {processor.outputTopics[0]}
          </Badge>
        </div>
      </div>

      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          {processor.stateStore.enabled && <Database className="h-3 w-3 text-purple-500" />}
          {processor.checkpointing.enabled && <CheckCircle className="h-3 w-3 text-green-500" />}
          {processor.scaling.autoScaling && <Target className="h-3 w-3 text-blue-500" />}
        </div>
        <div className="flex items-center gap-2">
          <Button size="sm" variant="outline">
            <Monitor className="h-3 w-3 mr-1" />
            Monitor
          </Button>
          <Button size="sm" variant="ghost">
            <Settings className="h-3 w-3" />
          </Button>
        </div>
      </div>
    </motion.div>
  );
};

const ConsumerCard: React.FC<{ consumer: StreamConsumer }> = ({ consumer }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'bg-green-100 text-green-800';
      case 'stopped': return 'bg-gray-100 text-gray-800';
      case 'error': return 'bg-red-100 text-red-800';
      case 'rebalancing': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running': return <Play className="h-3 w-3" />;
      case 'stopped': return <Square className="h-3 w-3" />;
      case 'error': return <XCircle className="h-3 w-3" />;
      case 'rebalancing': return <RefreshCw className="h-3 w-3 animate-spin" />;
      default: return <Info className="h-3 w-3" />;
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      className="border rounded-lg p-4 hover:shadow-md transition-shadow"
    >
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-2">
          <Package className="h-4 w-4 text-green-600" />
          <div>
            <h4 className="font-medium">{consumer.name}</h4>
            <p className="text-sm text-gray-600">{consumer.groupId}</p>
          </div>
        </div>
        <Badge className={getStatusColor(consumer.status)}>
          {getStatusIcon(consumer.status)}
          {consumer.status}
        </Badge>
      </div>

      <div className="mb-3">
        <p className="text-sm text-gray-600 mb-1">Topic</p>
        <Badge variant="outline">{consumer.topicName}</Badge>
      </div>

      <div className="grid grid-cols-2 gap-4 mb-3">
        <div>
          <p className="text-sm text-gray-600">Records/sec</p>
          <p className="text-lg font-semibold">{consumer.metrics.recordsPerSecond.toLocaleString()}</p>
        </div>
        <div>
          <p className="text-sm text-gray-600">Lag</p>
          <p className="text-lg font-semibold">{consumer.metrics.lag.toLocaleString()}</p>
        </div>
        <div>
          <p className="text-sm text-gray-600">Partitions</p>
          <p className="text-lg font-semibold">{consumer.metrics.assignedPartitions}</p>
        </div>
        <div>
          <p className="text-sm text-gray-600">Processing Time</p>
          <p className="text-lg font-semibold">{consumer.metrics.processingTime}ms</p>
        </div>
      </div>

      <div className="mb-3">
        <p className="text-sm text-gray-600 mb-2">Partition Assignment</p>
        <div className="space-y-1">
          {consumer.partitionAssignment.slice(0, 3).map((assignment, index) => (
            <div key={index} className="flex items-center justify-between text-xs">
              <span>Partition {assignment.partition}</span>
              <span className="text-gray-500">Lag: {assignment.lag}</span>
            </div>
          ))}
          {consumer.partitionAssignment.length > 3 && (
            <div className="text-xs text-gray-500">
              +{consumer.partitionAssignment.length - 3} more partitions
            </div>
          )}
        </div>
      </div>

      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          {consumer.autoCommit && <CheckCircle className="h-3 w-3 text-green-500" />}
          {consumer.processing.backpressure && <AlertTriangle className="h-3 w-3 text-yellow-500" />}
        </div>
        <span className="text-sm text-gray-600">
          Last seen: {consumer.lastSeen.toLocaleTimeString()}
        </span>
      </div>
    </motion.div>
  );
};

const AlertCard: React.FC<{ alert: StreamAlert }> = ({ alert }) => {
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-blue-100 text-blue-800 border-blue-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical': return <AlertTriangle className="h-4 w-4" />;
      case 'high': return <AlertCircle className="h-4 w-4" />;
      case 'medium': return <Info className="h-4 w-4" />;
      case 'low': return <CheckCircle className="h-4 w-4" />;
      default: return <Info className="h-4 w-4" />;
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className="border rounded-lg p-4 hover:shadow-md transition-shadow"
    >
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-2">
          {getSeverityIcon(alert.severity)}
          <div>
            <h4 className="font-medium">{alert.title}</h4>
            <p className="text-sm text-gray-600">{alert.description}</p>
          </div>
        </div>
        <Badge className={getSeverityColor(alert.severity)}>
          {alert.severity.toUpperCase()}
        </Badge>
      </div>

      <div className="grid grid-cols-2 gap-4 mb-3">
        <div>
          <p className="text-sm text-gray-600">Threshold</p>
          <p className="text-sm font-medium">{alert.threshold.toFixed(2)}</p>
        </div>
        <div>
          <p className="text-sm text-gray-600">Current Value</p>
          <p className="text-sm font-medium">{alert.currentValue.toFixed(2)}</p>
        </div>
      </div>

      {alert.topic && (
        <div className="mb-3">
          <p className="text-sm text-gray-600 mb-1">Affected Topic</p>
          <Badge variant="outline">{alert.topic}</Badge>
        </div>
      )}

      <div className="flex items-center justify-between">
        <span className="text-sm text-gray-600">
          {alert.timestamp.toLocaleTimeString()}
        </span>
        <Badge variant={alert.status === 'active' ? 'destructive' : 'secondary'}>
          {alert.status}
        </Badge>
      </div>
    </motion.div>
  );
};

const ArrowRight: React.FC = () => <span>→</span>;
const Bell: React.FC = () => <AlertTriangle className="h-3 w-3" />;

export const RealtimeStreaming: React.FC = () => {
  const [selectedPlatform, setSelectedPlatform] = useState<string>('all');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'name' | 'throughput' | 'lag' | 'consumers'>('throughput');

  const topics = useMemo(() => generateStreamingTopics(), []);
  const processors = useMemo(() => generateStreamProcessors(), []);
  const consumers = useMemo(() => generateStreamConsumers(topics), [topics]);
  const alerts = useMemo(() => generateStreamAlerts(), []);

  const filteredTopics = useMemo(() => {
    return topics
      .filter(topic => selectedPlatform === 'all' || topic.platform === selectedPlatform)
      .filter(topic => selectedStatus === 'all' || topic.status === selectedStatus)
      .filter(topic => topic.name.toLowerCase().includes(searchQuery.toLowerCase()))
      .sort((a, b) => {
        switch (sortBy) {
          case 'name': return a.name.localeCompare(b.name);
          case 'throughput': return b.metrics.messagesPerSecond - a.metrics.messagesPerSecond;
          case 'lag': return b.metrics.lag - a.metrics.lag;
          case 'consumers': return b.metrics.consumerCount - a.metrics.consumerCount;
          default: return 0;
        }
      });
  }, [topics, selectedPlatform, selectedStatus, searchQuery, sortBy]);

  const streamingMetrics = useMemo(() => {
    const totalTopics = topics.length;
    const activeTopics = topics.filter(t => t.status === 'active').length;
    const totalProcessors = processors.length;
    const runningProcessors = processors.filter(p => p.status === 'running').length;
    const totalThroughput = topics.reduce((sum, t) => sum + t.metrics.messagesPerSecond, 0);
    const totalLag = consumers.reduce((sum, c) => sum + c.metrics.lag, 0);
    const avgProcessingLatency = processors.reduce((sum, p) => sum + p.metrics.processingLatency, 0) / processors.length;
    const activeAlerts = alerts.filter(a => a.status === 'active').length;

    return {
      totalTopics,
      activeTopics,
      totalProcessors,
      runningProcessors,
      totalThroughput,
      totalLag,
      avgProcessingLatency,
      activeAlerts
    };
  }, [topics, processors, consumers, alerts]);

  const throughputData = useMemo(() => {
    return Array.from({ length: 24 }, (_, i) => ({
      hour: i,
      messages: Math.floor(Math.random() * 100000) + 50000,
      bytes: Math.floor(Math.random() * 1000000) + 500000,
      lag: Math.floor(Math.random() * 10000) + 1000,
      errors: Math.floor(Math.random() * 100) + 10
    }));
  }, []);

  const platformData = useMemo(() => {
    const platformDistribution = topics.reduce((acc, topic) => {
      acc[topic.platform] = (acc[topic.platform] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(platformDistribution).map(([platform, count]) => ({
      name: platform.toUpperCase(),
      value: count,
      percentage: ((count / topics.length) * 100).toFixed(1)
    }));
  }, [topics]);

  const topicPerformanceData = useMemo(() => {
    return topics
      .sort((a, b) => b.metrics.messagesPerSecond - a.metrics.messagesPerSecond)
      .slice(0, 10)
      .map(topic => ({
        name: topic.name.substring(0, 15),
        throughput: topic.metrics.messagesPerSecond,
        lag: topic.metrics.lag,
        consumers: topic.metrics.consumerCount
      }));
  }, [topics]);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Activity className="h-6 w-6" />
            Real-time Streaming
          </h2>
          <p className="text-gray-600">Advanced Kafka/Pulsar event processing and stream analytics</p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Plus className="h-4 w-4 mr-2" />
            Create Topic
          </Button>
          <Button size="sm">
            <Monitor className="h-4 w-4 mr-2" />
            Monitor All
          </Button>
        </div>
      </div>

      <Tabs defaultValue="topics" className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="topics">Topics</TabsTrigger>
          <TabsTrigger value="processors">Processors</TabsTrigger>
          <TabsTrigger value="consumers">Consumers</TabsTrigger>
          <TabsTrigger value="monitoring">Monitoring</TabsTrigger>
          <TabsTrigger value="alerts">Alerts</TabsTrigger>
        </TabsList>

        <TabsContent value="topics">
          <div className="space-y-6">
            {/* Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Radio className="h-4 w-4 text-blue-600" />
                    <div>
                      <p className="text-sm text-gray-600">Total Topics</p>
                      <p className="text-2xl font-bold">{streamingMetrics.totalTopics}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Activity className="h-4 w-4 text-green-600" />
                    <div>
                      <p className="text-sm text-gray-600">Active Topics</p>
                      <p className="text-2xl font-bold">{streamingMetrics.activeTopics}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Gauge className="h-4 w-4 text-purple-600" />
                    <div>
                      <p className="text-sm text-gray-600">Total Throughput</p>
                      <p className="text-2xl font-bold">{(streamingMetrics.totalThroughput / 1000).toFixed(0)}K/s</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-red-600" />
                    <div>
                      <p className="text-sm text-gray-600">Total Lag</p>
                      <p className="text-2xl font-bold">{(streamingMetrics.totalLag / 1000).toFixed(0)}K</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Filters */}
            <div className="flex items-center gap-4">
              <div className="flex-1">
                <Input
                  placeholder="Search topics..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full"
                />
              </div>
              <select
                value={selectedPlatform}
                onChange={(e) => setSelectedPlatform(e.target.value)}
                className="px-3 py-2 border rounded-md"
              >
                <option value="all">All Platforms</option>
                <option value="kafka">Kafka</option>
                <option value="pulsar">Pulsar</option>
                <option value="kinesis">Kinesis</option>
              </select>
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="px-3 py-2 border rounded-md"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="paused">Paused</option>
                <option value="error">Error</option>
              </select>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="px-3 py-2 border rounded-md"
              >
                <option value="throughput">Sort by Throughput</option>
                <option value="name">Sort by Name</option>
                <option value="lag">Sort by Lag</option>
                <option value="consumers">Sort by Consumers</option>
              </select>
            </div>

            {/* Charts */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Platform Distribution</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <RechartsPieChart>
                      <Pie
                        data={platformData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percentage }) => `${name}: ${percentage}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {platformData.map((entry, index) => (
                          <Cell
                            key={`cell-${index}`}
                            fill={['#8884d8', '#82ca9d', '#ffc658'][index % 3]}
                          />
                        ))}
                      </Pie>
                      <Tooltip />
                    </RechartsPieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Throughput Trends</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <ComposedChart data={throughputData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="hour" />
                      <YAxis yAxisId="left" />
                      <YAxis yAxisId="right" orientation="right" />
                      <Tooltip />
                      <Legend />
                      <Area yAxisId="left" type="monotone" dataKey="messages" stroke="#8884d8" fill="#8884d8" name="Messages/hour" />
                      <Line yAxisId="right" type="monotone" dataKey="lag" stroke="#ff7300" name="Lag" />
                    </ComposedChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>

            {/* Topics Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredTopics.map((topic) => (
                <TopicCard key={topic.id} topic={topic} />
              ))}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="processors">
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Stream Processors</h3>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Create Processor
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {processors.map((processor) => (
                <ProcessorCard key={processor.id} processor={processor} />
              ))}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="consumers">
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Stream Consumers</h3>
              <Button size="sm">
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {consumers.map((consumer) => (
                <ConsumerCard key={consumer.id} consumer={consumer} />
              ))}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="monitoring">
          <div className="space-y-6">
            <h3 className="text-lg font-semibold">Stream Monitoring</h3>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Real-time Throughput</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <AreaChart data={throughputData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="hour" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Area type="monotone" dataKey="messages" stroke="#8884d8" fill="#8884d8" name="Messages" />
                      <Area type="monotone" dataKey="bytes" stroke="#82ca9d" fill="#82ca9d" name="Bytes" />
                    </AreaChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Consumer Lag</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <LineChart data={throughputData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="hour" />
                      <YAxis />
                      <Tooltip />
                      <Line type="monotone" dataKey="lag" stroke="#ff7300" name="Consumer Lag" />
                    </LineChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Top Performing Topics</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={400}>
                  <BarChart data={topicPerformanceData} layout="horizontal">
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis type="number" />
                    <YAxis dataKey="name" type="category" width={120} />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="throughput" fill="#8884d8" name="Messages/sec" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Real-time Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Cpu className="h-4 w-4 text-blue-600" />
                    <div>
                      <p className="text-sm text-gray-600">Avg Processing Latency</p>
                      <p className="text-2xl font-bold">{streamingMetrics.avgProcessingLatency.toFixed(0)}ms</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Workflow className="h-4 w-4 text-green-600" />
                    <div>
                      <p className="text-sm text-gray-600">Running Processors</p>
                      <p className="text-2xl font-bold">{streamingMetrics.runningProcessors}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <AlertTriangle className="h-4 w-4 text-red-600" />
                    <div>
                      <p className="text-sm text-gray-600">Active Alerts</p>
                      <p className="text-2xl font-bold">{streamingMetrics.activeAlerts}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="alerts">
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Stream Alerts</h3>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Create Alert
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {alerts.map((alert) => (
                <AlertCard key={alert.id} alert={alert} />
              ))}
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default RealtimeStreaming;