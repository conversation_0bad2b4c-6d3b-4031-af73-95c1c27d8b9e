/**
 * Data Pipeline Orchestration
 * Advanced ETL/ELT workflow management with visual pipeline builder and monitoring
 */

import React, { useState, useMemo, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { motion } from 'framer-motion';
import { 
  Workflow,
  GitBranch,
  Play,
  Pause,
  Square,
  SkipForward,
  SkipBack,
  RefreshCw,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  Timer,
  Zap,
  Activity,
  TrendingUp,
  TrendingDown,
  Database,
  Server,
  Cloud,
  Network,
  Settings,
  Monitor,
  BarChart3,
  LineChart,
  PieChart,
  Eye,
  Download,
  Upload,
  Share,
  Filter,
  Search,
  Calendar,
  Users,
  Package,
  Tag,
  Hash,
  Layers,
  Grid,
  List,
  MoreHorizontal,
  ExternalLink,
  Info,
  Plus,
  Minus,
  Edit,
  Trash2,
  Copy,
  Bookmark,
  Star,
  Award,
  Lightbulb,
  Code,
  Terminal,
  FileText,
  Archive,
  FolderOpen,
  Link,
  Unlink,
  MousePointer,
  Gauge,
  HardDrive,
  MemoryStick,
  Cpu,
  Brain,
  Shield,
  Lock,
  Unlock,
  Key,
  Globe,
  Wifi,
  Smartphone,
  Laptop,
  Tablet,
  Watch,
  Construction,
  Rocket,
  FlaskConical,
  Bug,
  CheckCheck,
  AlertCircle,
  Target,
  Crosshair,
  Move,
  RotateCw,
  Repeat,
  Shuffle,
  SplitSquareHorizontal,
  Merge,
  Filter as FilterIcon,
  MapPin,
  Route,
  Waypoints
} from 'lucide-react';
import { 
  LineChart as RechartsLineChart, 
  Line, 
  AreaChart, 
  Area, 
  BarChart, 
  Bar, 
  ScatterChart,
  Scatter,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  Cell,
  PieChart as RechartsPieChart,
  Pie,
  RadialBarChart,
  RadialBar,
  ComposedChart,
  Sankey
} from 'recharts';

// Types
interface Pipeline {
  id: string;
  name: string;
  description: string;
  type: 'etl' | 'elt' | 'streaming' | 'batch' | 'real_time';
  status: 'draft' | 'active' | 'paused' | 'error' | 'completed';
  schedule: {
    type: 'manual' | 'cron' | 'event' | 'continuous';
    expression?: string;
    timezone: string;
    nextRun?: Date;
  };
  source: DataSource;
  destination: DataDestination;
  transformations: Transformation[];
  monitoring: {
    lastRun: Date;
    duration: number;
    recordsProcessed: number;
    errorCount: number;
    successRate: number;
    avgProcessingTime: number;
  };
  configuration: {
    parallelism: number;
    retryPolicy: {
      maxRetries: number;
      backoffStrategy: 'linear' | 'exponential';
      delay: number;
    };
    errorHandling: {
      onError: 'stop' | 'continue' | 'skip';
      quarantineErrors: boolean;
      alertThreshold: number;
    };
    performance: {
      batchSize: number;
      memoryLimit: number;
      timeout: number;
    };
  };
  dependencies: string[];
  tags: string[];
  version: number;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
}

interface DataSource {
  id: string;
  type: 'database' | 'file' | 'api' | 'stream' | 'webhook' | 'queue';
  name: string;
  connection: {
    host?: string;
    port?: number;
    database?: string;
    username?: string;
    password?: string;
    url?: string;
    apiKey?: string;
    headers?: Record<string, string>;
    query?: string;
    path?: string;
  };
  schema: DataSchema;
  incremental: {
    enabled: boolean;
    column?: string;
    strategy: 'timestamp' | 'sequence' | 'checksum';
    lastValue?: any;
  };
  validation: {
    enabled: boolean;
    rules: ValidationRule[];
  };
}

interface DataDestination {
  id: string;
  type: 'database' | 'warehouse' | 'lake' | 'api' | 'file' | 'stream';
  name: string;
  connection: {
    host?: string;
    port?: number;
    database?: string;
    table?: string;
    username?: string;
    password?: string;
    url?: string;
    apiKey?: string;
    path?: string;
    format?: 'json' | 'csv' | 'parquet' | 'avro';
  };
  schema: DataSchema;
  writeMode: 'append' | 'overwrite' | 'upsert' | 'merge';
  partitioning: {
    enabled: boolean;
    columns: string[];
    strategy: 'hash' | 'range' | 'time';
  };
}

interface DataSchema {
  fields: SchemaField[];
  primaryKey: string[];
  indexes: string[];
  constraints: Constraint[];
}

interface SchemaField {
  name: string;
  type: 'string' | 'integer' | 'float' | 'boolean' | 'date' | 'datetime' | 'json' | 'array';
  nullable: boolean;
  defaultValue?: any;
  description?: string;
}

interface ValidationRule {
  field: string;
  type: 'required' | 'range' | 'pattern' | 'unique' | 'custom';
  parameters: any;
  message: string;
}

interface Constraint {
  type: 'unique' | 'check' | 'foreign_key';
  columns: string[];
  parameters: any;
}

interface Transformation {
  id: string;
  name: string;
  type: 'filter' | 'map' | 'aggregate' | 'join' | 'union' | 'split' | 'custom' | 'sql';
  description: string;
  configuration: any;
  inputSchema: DataSchema;
  outputSchema: DataSchema;
  order: number;
  enabled: boolean;
}

interface PipelineRun {
  id: string;
  pipelineId: string;
  status: 'running' | 'completed' | 'failed' | 'cancelled';
  startTime: Date;
  endTime?: Date;
  duration: number;
  trigger: 'manual' | 'scheduled' | 'event';
  statistics: {
    recordsRead: number;
    recordsWritten: number;
    recordsFiltered: number;
    recordsErrored: number;
    bytesProcessed: number;
  };
  stages: PipelineStage[];
  logs: PipelineLog[];
  error?: string;
}

interface PipelineStage {
  id: string;
  name: string;
  type: 'extract' | 'transform' | 'load' | 'validate';
  status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped';
  startTime?: Date;
  endTime?: Date;
  duration: number;
  recordsProcessed: number;
  error?: string;
  metrics: Record<string, number>;
}

interface PipelineLog {
  id: string;
  timestamp: Date;
  level: 'debug' | 'info' | 'warn' | 'error';
  stage?: string;
  message: string;
  metadata: Record<string, any>;
}

interface PipelineTemplate {
  id: string;
  name: string;
  description: string;
  category: 'ecommerce' | 'analytics' | 'ml' | 'reporting' | 'integration';
  useCase: string;
  configuration: Partial<Pipeline>;
  popularity: number;
  tags: string[];
}

interface ResourceUsage {
  cpu: number;
  memory: number;
  storage: number;
  network: number;
  timestamp: Date;
}

// Mock data generators
const generatePipelines = (): Pipeline[] => {
  const types: Pipeline['type'][] = ['etl', 'elt', 'streaming', 'batch'];
  const statuses: Pipeline['status'][] = ['active', 'paused', 'error', 'draft'];

  return Array.from({ length: 15 }, (_, index) => ({
    id: `PIPE-${String(index + 1).padStart(3, '0')}`,
    name: `Data Pipeline ${index + 1}`,
    description: `Automated data processing pipeline for ${['orders', 'customers', 'products', 'inventory', 'analytics'][Math.floor(Math.random() * 5)]}`,
    type: types[Math.floor(Math.random() * types.length)],
    status: statuses[Math.floor(Math.random() * statuses.length)],
    schedule: {
      type: ['cron', 'event', 'continuous'][Math.floor(Math.random() * 3)] as any,
      expression: '0 */6 * * *',
      timezone: 'UTC',
      nextRun: new Date(Date.now() + Math.random() * 24 * 60 * 60 * 1000)
    },
    source: {
      id: `SRC-${index + 1}`,
      type: ['database', 'api', 'file'][Math.floor(Math.random() * 3)] as any,
      name: `Source ${index + 1}`,
      connection: {
        host: 'source.example.com',
        port: 5432,
        database: 'ecommerce',
        username: 'readonly'
      },
      schema: {
        fields: [
          { name: 'id', type: 'integer', nullable: false },
          { name: 'created_at', type: 'datetime', nullable: false },
          { name: 'data', type: 'json', nullable: true }
        ],
        primaryKey: ['id'],
        indexes: ['created_at'],
        constraints: []
      },
      incremental: {
        enabled: true,
        column: 'updated_at',
        strategy: 'timestamp',
        lastValue: new Date()
      },
      validation: {
        enabled: true,
        rules: [
          {
            field: 'id',
            type: 'required',
            parameters: {},
            message: 'ID is required'
          }
        ]
      }
    },
    destination: {
      id: `DEST-${index + 1}`,
      type: ['warehouse', 'database', 'lake'][Math.floor(Math.random() * 3)] as any,
      name: `Destination ${index + 1}`,
      connection: {
        host: 'warehouse.example.com',
        port: 5432,
        database: 'analytics',
        table: `table_${index + 1}`
      },
      schema: {
        fields: [
          { name: 'id', type: 'integer', nullable: false },
          { name: 'processed_at', type: 'datetime', nullable: false },
          { name: 'enriched_data', type: 'json', nullable: true }
        ],
        primaryKey: ['id'],
        indexes: ['processed_at'],
        constraints: []
      },
      writeMode: ['append', 'upsert', 'overwrite'][Math.floor(Math.random() * 3)] as any,
      partitioning: {
        enabled: true,
        columns: ['date'],
        strategy: 'time'
      }
    },
    transformations: [
      {
        id: `TRANS-${index + 1}-1`,
        name: 'Data Cleansing',
        type: 'filter',
        description: 'Remove invalid records',
        configuration: {
          conditions: [
            { field: 'id', operator: 'not_null' },
            { field: 'created_at', operator: 'not_null' }
          ]
        },
        inputSchema: {
          fields: [],
          primaryKey: [],
          indexes: [],
          constraints: []
        },
        outputSchema: {
          fields: [],
          primaryKey: [],
          indexes: [],
          constraints: []
        },
        order: 1,
        enabled: true
      },
      {
        id: `TRANS-${index + 1}-2`,
        name: 'Data Enrichment',
        type: 'map',
        description: 'Add calculated fields',
        configuration: {
          mappings: [
            { source: 'created_at', target: 'date', transformation: 'date_format' },
            { source: 'data', target: 'enriched_data', transformation: 'json_flatten' }
          ]
        },
        inputSchema: {
          fields: [],
          primaryKey: [],
          indexes: [],
          constraints: []
        },
        outputSchema: {
          fields: [],
          primaryKey: [],
          indexes: [],
          constraints: []
        },
        order: 2,
        enabled: true
      }
    ],
    monitoring: {
      lastRun: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000),
      duration: Math.floor(Math.random() * 3600) + 300,
      recordsProcessed: Math.floor(Math.random() * 100000) + 1000,
      errorCount: Math.floor(Math.random() * 100),
      successRate: 0.95 + Math.random() * 0.05,
      avgProcessingTime: Math.floor(Math.random() * 1800) + 600
    },
    configuration: {
      parallelism: Math.floor(Math.random() * 8) + 1,
      retryPolicy: {
        maxRetries: 3,
        backoffStrategy: 'exponential',
        delay: 5000
      },
      errorHandling: {
        onError: 'continue',
        quarantineErrors: true,
        alertThreshold: 10
      },
      performance: {
        batchSize: Math.floor(Math.random() * 10000) + 1000,
        memoryLimit: Math.floor(Math.random() * 8) + 2,
        timeout: Math.floor(Math.random() * 3600) + 1800
      }
    },
    dependencies: [],
    tags: ['automated', 'production', 'critical'].slice(0, Math.floor(Math.random() * 3) + 1),
    version: Math.floor(Math.random() * 10) + 1,
    createdAt: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000),
    createdBy: `user${Math.floor(Math.random() * 10) + 1}@example.com`
  }));
};

const generatePipelineRuns = (pipelines: Pipeline[]): PipelineRun[] => {
  const runs: PipelineRun[] = [];
  
  pipelines.forEach(pipeline => {
    const numRuns = Math.floor(Math.random() * 10) + 5;
    for (let i = 0; i < numRuns; i++) {
      const startTime = new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000);
      const duration = Math.floor(Math.random() * 3600) + 300;
      const endTime = new Date(startTime.getTime() + duration * 1000);
      
      runs.push({
        id: `RUN-${pipeline.id}-${String(i + 1).padStart(3, '0')}`,
        pipelineId: pipeline.id,
        status: ['completed', 'failed', 'running'][Math.floor(Math.random() * 3)] as any,
        startTime,
        endTime: Math.random() > 0.1 ? endTime : undefined,
        duration,
        trigger: ['scheduled', 'manual', 'event'][Math.floor(Math.random() * 3)] as any,
        statistics: {
          recordsRead: Math.floor(Math.random() * 100000) + 1000,
          recordsWritten: Math.floor(Math.random() * 95000) + 950,
          recordsFiltered: Math.floor(Math.random() * 5000) + 50,
          recordsErrored: Math.floor(Math.random() * 100) + 5,
          bytesProcessed: Math.floor(Math.random() * 1000000000) + 100000000
        },
        stages: [
          {
            id: `STAGE-${pipeline.id}-1`,
            name: 'Extract',
            type: 'extract',
            status: 'completed',
            startTime,
            endTime: new Date(startTime.getTime() + duration * 0.3 * 1000),
            duration: duration * 0.3,
            recordsProcessed: Math.floor(Math.random() * 100000) + 1000,
            metrics: {
              throughput: Math.floor(Math.random() * 1000) + 100
            }
          },
          {
            id: `STAGE-${pipeline.id}-2`,
            name: 'Transform',
            type: 'transform',
            status: 'completed',
            startTime: new Date(startTime.getTime() + duration * 0.3 * 1000),
            endTime: new Date(startTime.getTime() + duration * 0.8 * 1000),
            duration: duration * 0.5,
            recordsProcessed: Math.floor(Math.random() * 95000) + 950,
            metrics: {
              throughput: Math.floor(Math.random() * 800) + 80
            }
          },
          {
            id: `STAGE-${pipeline.id}-3`,
            name: 'Load',
            type: 'load',
            status: 'completed',
            startTime: new Date(startTime.getTime() + duration * 0.8 * 1000),
            endTime,
            duration: duration * 0.2,
            recordsProcessed: Math.floor(Math.random() * 95000) + 950,
            metrics: {
              throughput: Math.floor(Math.random() * 1200) + 120
            }
          }
        ],
        logs: [
          {
            id: `LOG-${pipeline.id}-1`,
            timestamp: startTime,
            level: 'info',
            stage: 'extract',
            message: 'Starting data extraction',
            metadata: { records: 10000 }
          },
          {
            id: `LOG-${pipeline.id}-2`,
            timestamp: endTime,
            level: 'info',
            stage: 'load',
            message: 'Pipeline completed successfully',
            metadata: { duration }
          }
        ],
        error: Math.random() > 0.8 ? 'Connection timeout' : undefined
      });
    }
  });
  
  return runs.sort((a, b) => b.startTime.getTime() - a.startTime.getTime());
};

const generateTemplates = (): PipelineTemplate[] => {
  return [
    {
      id: 'TEMPLATE-001',
      name: 'E-commerce Order Processing',
      description: 'Extract, transform, and load order data from e-commerce platforms',
      category: 'ecommerce',
      useCase: 'Process orders from multiple channels into a unified analytics warehouse',
      configuration: {
        type: 'etl',
        schedule: {
          type: 'cron',
          expression: '0 */1 * * *',
          timezone: 'UTC'
        }
      },
      popularity: 0.9,
      tags: ['orders', 'ecommerce', 'real-time']
    },
    {
      id: 'TEMPLATE-002',
      name: 'Customer Data Integration',
      description: 'Consolidate customer data from multiple sources',
      category: 'integration',
      useCase: 'Create a unified customer view by merging data from CRM, support, and transaction systems',
      configuration: {
        type: 'elt',
        schedule: {
          type: 'cron',
          expression: '0 2 * * *',
          timezone: 'UTC'
        }
      },
      popularity: 0.8,
      tags: ['customers', 'cdp', 'integration']
    },
    {
      id: 'TEMPLATE-003',
      name: 'Real-time Analytics Pipeline',
      description: 'Stream processing for real-time analytics and dashboards',
      category: 'analytics',
      useCase: 'Process streaming data for real-time dashboards and alerts',
      configuration: {
        type: 'streaming',
        schedule: {
          type: 'continuous',
          timezone: 'UTC'
        }
      },
      popularity: 0.85,
      tags: ['streaming', 'real-time', 'analytics']
    }
  ];
};

// Components
const PipelineCard: React.FC<{ pipeline: Pipeline }> = ({ pipeline }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'paused': return 'bg-yellow-100 text-yellow-800';
      case 'error': return 'bg-red-100 text-red-800';
      case 'draft': return 'bg-gray-100 text-gray-800';
      case 'completed': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <Play className="h-3 w-3" />;
      case 'paused': return <Pause className="h-3 w-3" />;
      case 'error': return <XCircle className="h-3 w-3" />;
      case 'draft': return <Edit className="h-3 w-3" />;
      case 'completed': return <CheckCircle className="h-3 w-3" />;
      default: return <Info className="h-3 w-3" />;
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'etl': return <GitBranch className="h-4 w-4" />;
      case 'elt': return <Database className="h-4 w-4" />;
      case 'streaming': return <Activity className="h-4 w-4" />;
      case 'batch': return <Package className="h-4 w-4" />;
      default: return <Workflow className="h-4 w-4" />;
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className="border rounded-lg p-4 hover:shadow-md transition-shadow"
    >
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-2">
          {getTypeIcon(pipeline.type)}
          <div>
            <h4 className="font-medium">{pipeline.name}</h4>
            <p className="text-sm text-gray-600">v{pipeline.version}</p>
          </div>
        </div>
        <Badge className={getStatusColor(pipeline.status)}>
          {getStatusIcon(pipeline.status)}
          {pipeline.status}
        </Badge>
      </div>

      <p className="text-sm text-gray-600 mb-3 line-clamp-2">{pipeline.description}</p>

      <div className="grid grid-cols-2 gap-4 mb-3">
        <div>
          <p className="text-sm text-gray-600">Success Rate</p>
          <p className="text-lg font-semibold">{(pipeline.monitoring.successRate * 100).toFixed(1)}%</p>
        </div>
        <div>
          <p className="text-sm text-gray-600">Avg Duration</p>
          <p className="text-lg font-semibold">{Math.floor(pipeline.monitoring.avgProcessingTime / 60)}m</p>
        </div>
        <div>
          <p className="text-sm text-gray-600">Records/Run</p>
          <p className="text-lg font-semibold">{(pipeline.monitoring.recordsProcessed / 1000).toFixed(0)}K</p>
        </div>
        <div>
          <p className="text-sm text-gray-600">Errors</p>
          <p className="text-lg font-semibold">{pipeline.monitoring.errorCount}</p>
        </div>
      </div>

      <div className="mb-3">
        <p className="text-sm text-gray-600 mb-1">Source → Destination</p>
        <div className="flex items-center gap-2 text-sm">
          <Badge variant="outline" className="text-xs">
            {pipeline.source.type}
          </Badge>
          <ArrowRight className="h-3 w-3 text-gray-400" />
          <Badge variant="outline" className="text-xs">
            {pipeline.destination.type}
          </Badge>
        </div>
      </div>

      <div className="mb-3">
        <p className="text-sm text-gray-600 mb-1">Schedule</p>
        <div className="flex items-center gap-2">
          <Clock className="h-3 w-3 text-gray-400" />
          <span className="text-sm">{pipeline.schedule.type}</span>
          {pipeline.schedule.nextRun && (
            <span className="text-xs text-gray-500">
              Next: {pipeline.schedule.nextRun.toLocaleTimeString()}
            </span>
          )}
        </div>
      </div>

      <div className="flex items-center justify-between">
        <div className="flex flex-wrap gap-1">
          {pipeline.tags.slice(0, 2).map((tag, index) => (
            <Badge key={index} variant="outline" className="text-xs">
              {tag}
            </Badge>
          ))}
          {pipeline.tags.length > 2 && (
            <Badge variant="outline" className="text-xs">
              +{pipeline.tags.length - 2}
            </Badge>
          )}
        </div>
        <div className="flex items-center gap-2">
          <Button size="sm" variant="outline">
            <Eye className="h-3 w-3 mr-1" />
            View
          </Button>
          <Button size="sm" variant="ghost">
            <Play className="h-3 w-3" />
          </Button>
        </div>
      </div>
    </motion.div>
  );
};

const RunCard: React.FC<{ run: PipelineRun }> = ({ run }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'running': return 'bg-blue-100 text-blue-800';
      case 'failed': return 'bg-red-100 text-red-800';
      case 'cancelled': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="h-3 w-3" />;
      case 'running': return <RefreshCw className="h-3 w-3 animate-spin" />;
      case 'failed': return <XCircle className="h-3 w-3" />;
      case 'cancelled': return <Square className="h-3 w-3" />;
      default: return <Info className="h-3 w-3" />;
    }
  };

  const getTriggerIcon = (trigger: string) => {
    switch (trigger) {
      case 'manual': return <MousePointer className="h-3 w-3" />;
      case 'scheduled': return <Clock className="h-3 w-3" />;
      case 'event': return <Zap className="h-3 w-3" />;
      default: return <Info className="h-3 w-3" />;
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="border rounded-lg p-4 hover:shadow-md transition-shadow"
    >
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-2">
          <Timer className="h-4 w-4 text-blue-600" />
          <div>
            <h4 className="font-medium">{run.pipelineId}</h4>
            <p className="text-sm text-gray-600">{run.id}</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="text-xs">
            {getTriggerIcon(run.trigger)}
            {run.trigger}
          </Badge>
          <Badge className={getStatusColor(run.status)}>
            {getStatusIcon(run.status)}
            {run.status}
          </Badge>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4 mb-3">
        <div>
          <p className="text-sm text-gray-600">Duration</p>
          <p className="text-lg font-semibold">{Math.floor(run.duration / 60)}m {run.duration % 60}s</p>
        </div>
        <div>
          <p className="text-sm text-gray-600">Records Processed</p>
          <p className="text-lg font-semibold">{run.statistics.recordsWritten.toLocaleString()}</p>
        </div>
      </div>

      <div className="mb-3">
        <p className="text-sm text-gray-600 mb-2">Stages Progress</p>
        <div className="space-y-1">
          {run.stages.map((stage, index) => (
            <div key={stage.id} className="flex items-center gap-2">
              <div className="w-2 h-2 rounded-full bg-green-500" />
              <span className="text-xs text-gray-600">{stage.name}</span>
              <span className="text-xs text-gray-500 ml-auto">
                {Math.floor(stage.duration)}s
              </span>
            </div>
          ))}
        </div>
      </div>

      {run.error && (
        <div className="mb-3 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-800">
          <div className="flex items-center gap-2">
            <AlertTriangle className="h-3 w-3" />
            <span className="font-medium">Error:</span>
          </div>
          <p className="mt-1">{run.error}</p>
        </div>
      )}

      <div className="flex items-center justify-between">
        <span className="text-sm text-gray-600">
          Started: {run.startTime.toLocaleTimeString()}
        </span>
        <div className="flex items-center gap-2">
          <Button size="sm" variant="outline">
            <FileText className="h-3 w-3 mr-1" />
            Logs
          </Button>
          <Button size="sm" variant="ghost">
            <RefreshCw className="h-3 w-3" />
          </Button>
        </div>
      </div>
    </motion.div>
  );
};

const TemplateCard: React.FC<{ template: PipelineTemplate }> = ({ template }) => {
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'ecommerce': return <ShoppingCart className="h-4 w-4" />;
      case 'analytics': return <BarChart3 className="h-4 w-4" />;
      case 'integration': return <Link className="h-4 w-4" />;
      case 'ml': return <Brain className="h-4 w-4" />;
      default: return <Package className="h-4 w-4" />;
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className="border rounded-lg p-4 hover:shadow-md transition-shadow"
    >
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
            {getCategoryIcon(template.category)}
          </div>
          <div>
            <h4 className="font-medium">{template.name}</h4>
            <p className="text-sm text-gray-600 capitalize">{template.category}</p>
          </div>
        </div>
        <div className="flex items-center gap-1">
          <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
          <span className="text-sm font-medium">{(template.popularity * 5).toFixed(1)}</span>
        </div>
      </div>

      <p className="text-sm text-gray-600 mb-3">{template.description}</p>

      <div className="mb-3 p-2 bg-blue-50 border border-blue-200 rounded text-sm text-blue-800">
        <p className="font-medium">Use Case:</p>
        <p>{template.useCase}</p>
      </div>

      <div className="mb-3">
        <p className="text-sm text-gray-600 mb-1">Features</p>
        <div className="flex flex-wrap gap-1">
          {template.tags.slice(0, 3).map((tag, index) => (
            <Badge key={index} variant="outline" className="text-xs">
              {tag}
            </Badge>
          ))}
          {template.tags.length > 3 && (
            <Badge variant="outline" className="text-xs">
              +{template.tags.length - 3} more
            </Badge>
          )}
        </div>
      </div>

      <div className="flex items-center justify-between">
        <Badge variant="secondary" className="capitalize">
          {template.configuration.type || 'ETL'}
        </Badge>
        <Button size="sm">
          <Download className="h-3 w-3 mr-1" />
          Use Template
        </Button>
      </div>
    </motion.div>
  );
};

const ArrowRight: React.FC = () => <span>→</span>;

export const DataPipelineOrchestration: React.FC = () => {
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [selectedType, setSelectedType] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'name' | 'lastRun' | 'duration' | 'success'>('lastRun');

  const pipelines = useMemo(() => generatePipelines(), []);
  const runs = useMemo(() => generatePipelineRuns(pipelines), [pipelines]);
  const templates = useMemo(() => generateTemplates(), []);

  const filteredPipelines = useMemo(() => {
    return pipelines
      .filter(pipeline => selectedStatus === 'all' || pipeline.status === selectedStatus)
      .filter(pipeline => selectedType === 'all' || pipeline.type === selectedType)
      .filter(pipeline => pipeline.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         pipeline.description.toLowerCase().includes(searchQuery.toLowerCase()))
      .sort((a, b) => {
        switch (sortBy) {
          case 'name': return a.name.localeCompare(b.name);
          case 'lastRun': return b.monitoring.lastRun.getTime() - a.monitoring.lastRun.getTime();
          case 'duration': return a.monitoring.avgProcessingTime - b.monitoring.avgProcessingTime;
          case 'success': return b.monitoring.successRate - a.monitoring.successRate;
          default: return 0;
        }
      });
  }, [pipelines, selectedStatus, selectedType, searchQuery, sortBy]);

  const orchestrationMetrics = useMemo(() => {
    const totalPipelines = pipelines.length;
    const activePipelines = pipelines.filter(p => p.status === 'active').length;
    const totalRuns = runs.length;
    const successfulRuns = runs.filter(r => r.status === 'completed').length;
    const failedRuns = runs.filter(r => r.status === 'failed').length;
    const avgDuration = runs.reduce((sum, r) => sum + r.duration, 0) / runs.length;
    const totalRecordsProcessed = runs.reduce((sum, r) => sum + r.statistics.recordsWritten, 0);

    return {
      totalPipelines,
      activePipelines,
      totalRuns,
      successfulRuns,
      failedRuns,
      successRate: totalRuns > 0 ? (successfulRuns / totalRuns) : 0,
      avgDuration,
      totalRecordsProcessed
    };
  }, [pipelines, runs]);

  const performanceData = useMemo(() => {
    return Array.from({ length: 24 }, (_, i) => ({
      hour: i,
      pipelines_running: Math.floor(Math.random() * 15) + 5,
      records_processed: Math.floor(Math.random() * 100000) + 50000,
      success_rate: 0.85 + Math.random() * 0.1,
      avg_duration: Math.floor(Math.random() * 1800) + 600
    }));
  }, []);

  const pipelineTypeData = useMemo(() => {
    const typeDistribution = pipelines.reduce((acc, pipeline) => {
      acc[pipeline.type] = (acc[pipeline.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(typeDistribution).map(([type, count]) => ({
      name: type.toUpperCase(),
      value: count,
      percentage: ((count / pipelines.length) * 100).toFixed(1)
    }));
  }, [pipelines]);

  const recentRuns = useMemo(() => {
    return runs.slice(0, 10);
  }, [runs]);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Workflow className="h-6 w-6" />
            Data Pipeline Orchestration
          </h2>
          <p className="text-gray-600">Advanced ETL/ELT workflow management and monitoring</p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Plus className="h-4 w-4 mr-2" />
            Create Pipeline
          </Button>
          <Button size="sm">
            <Rocket className="h-4 w-4 mr-2" />
            Run All
          </Button>
        </div>
      </div>

      <Tabs defaultValue="pipelines" className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="pipelines">Pipelines</TabsTrigger>
          <TabsTrigger value="runs">Runs</TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
          <TabsTrigger value="monitoring">Monitoring</TabsTrigger>
          <TabsTrigger value="builder">Pipeline Builder</TabsTrigger>
        </TabsList>

        <TabsContent value="pipelines">
          <div className="space-y-6">
            {/* Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Workflow className="h-4 w-4 text-blue-600" />
                    <div>
                      <p className="text-sm text-gray-600">Total Pipelines</p>
                      <p className="text-2xl font-bold">{orchestrationMetrics.totalPipelines}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Play className="h-4 w-4 text-green-600" />
                    <div>
                      <p className="text-sm text-gray-600">Active</p>
                      <p className="text-2xl font-bold">{orchestrationMetrics.activePipelines}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <div>
                      <p className="text-sm text-gray-600">Success Rate</p>
                      <p className="text-2xl font-bold">{(orchestrationMetrics.successRate * 100).toFixed(1)}%</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Database className="h-4 w-4 text-purple-600" />
                    <div>
                      <p className="text-sm text-gray-600">Records Processed</p>
                      <p className="text-2xl font-bold">{(orchestrationMetrics.totalRecordsProcessed / 1000000).toFixed(1)}M</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Filters */}
            <div className="flex items-center gap-4">
              <div className="flex-1">
                <Input
                  placeholder="Search pipelines..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full"
                />
              </div>
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="px-3 py-2 border rounded-md"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="paused">Paused</option>
                <option value="error">Error</option>
                <option value="draft">Draft</option>
              </select>
              <select
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                className="px-3 py-2 border rounded-md"
              >
                <option value="all">All Types</option>
                <option value="etl">ETL</option>
                <option value="elt">ELT</option>
                <option value="streaming">Streaming</option>
                <option value="batch">Batch</option>
              </select>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="px-3 py-2 border rounded-md"
              >
                <option value="lastRun">Sort by Last Run</option>
                <option value="name">Sort by Name</option>
                <option value="duration">Sort by Duration</option>
                <option value="success">Sort by Success Rate</option>
              </select>
            </div>

            {/* Charts */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Pipeline Type Distribution</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <RechartsPieChart>
                      <Pie
                        data={pipelineTypeData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percentage }) => `${name}: ${percentage}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {pipelineTypeData.map((entry, index) => (
                          <Cell
                            key={`cell-${index}`}
                            fill={['#8884d8', '#82ca9d', '#ffc658', '#ff7300'][index % 4]}
                          />
                        ))}
                      </Pie>
                      <Tooltip />
                    </RechartsPieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Performance Trends</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <ComposedChart data={performanceData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="hour" />
                      <YAxis yAxisId="left" />
                      <YAxis yAxisId="right" orientation="right" />
                      <Tooltip />
                      <Legend />
                      <Bar yAxisId="left" dataKey="pipelines_running" fill="#8884d8" name="Running Pipelines" />
                      <Line yAxisId="right" type="monotone" dataKey="success_rate" stroke="#82ca9d" name="Success Rate" />
                    </ComposedChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>

            {/* Pipelines Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredPipelines.map((pipeline) => (
                <PipelineCard key={pipeline.id} pipeline={pipeline} />
              ))}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="runs">
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Recent Pipeline Runs</h3>
              <Button size="sm">
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {recentRuns.map((run) => (
                <RunCard key={run.id} run={run} />
              ))}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="templates">
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Pipeline Templates</h3>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Create Template
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {templates.map((template) => (
                <TemplateCard key={template.id} template={template} />
              ))}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="monitoring">
          <div className="space-y-6">
            <h3 className="text-lg font-semibold">Pipeline Monitoring</h3>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Execution Performance</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <AreaChart data={performanceData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="hour" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Area type="monotone" dataKey="records_processed" stroke="#8884d8" fill="#8884d8" name="Records Processed" />
                    </AreaChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Duration Trends</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <LineChart data={performanceData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="hour" />
                      <YAxis />
                      <Tooltip />
                      <Line type="monotone" dataKey="avg_duration" stroke="#82ca9d" name="Avg Duration (s)" />
                    </LineChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>

            {/* Resource Usage */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Cpu className="h-4 w-4 text-blue-600" />
                    <div>
                      <p className="text-sm text-gray-600">CPU Usage</p>
                      <p className="text-2xl font-bold">67%</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <MemoryStick className="h-4 w-4 text-green-600" />
                    <div>
                      <p className="text-sm text-gray-600">Memory Usage</p>
                      <p className="text-2xl font-bold">4.2GB</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <HardDrive className="h-4 w-4 text-purple-600" />
                    <div>
                      <p className="text-sm text-gray-600">Storage Used</p>
                      <p className="text-2xl font-bold">156GB</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="builder">
          <div className="space-y-6">
            <h3 className="text-lg font-semibold">Visual Pipeline Builder</h3>

            <Card>
              <CardContent className="p-6">
                <div className="text-center">
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-8">
                    <Workflow className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-lg font-medium mb-2">Drag & Drop Pipeline Builder</p>
                    <p className="text-gray-600 mb-4">Create complex data pipelines with our visual interface</p>
                    <div className="flex justify-center gap-4">
                      <Button>
                        <Plus className="h-4 w-4 mr-2" />
                        Start Building
                      </Button>
                      <Button variant="outline">
                        <Download className="h-4 w-4 mr-2" />
                        Import Pipeline
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Pipeline Components */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Sources</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {['Database', 'File System', 'API Endpoint', 'Message Queue', 'Stream'].map((source, index) => (
                      <div key={index} className="flex items-center gap-2 p-2 border rounded cursor-pointer hover:bg-gray-50">
                        <Database className="h-4 w-4 text-blue-600" />
                        <span className="text-sm">{source}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Transformations</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {['Filter', 'Map', 'Aggregate', 'Join', 'Split'].map((transform, index) => (
                      <div key={index} className="flex items-center gap-2 p-2 border rounded cursor-pointer hover:bg-gray-50">
                        <RotateCw className="h-4 w-4 text-green-600" />
                        <span className="text-sm">{transform}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Destinations</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {['Data Warehouse', 'Data Lake', 'Database', 'File Export', 'API'].map((dest, index) => (
                      <div key={index} className="flex items-center gap-2 p-2 border rounded cursor-pointer hover:bg-gray-50">
                        <Server className="h-4 w-4 text-purple-600" />
                        <span className="text-sm">{dest}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default DataPipelineOrchestration;