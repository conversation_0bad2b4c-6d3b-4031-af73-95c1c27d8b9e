/**
 * Webhook Management System
 * Bi-directional webhook management with advanced delivery, retry, and monitoring capabilities
 */

import React, { useState, useMemo, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { motion } from 'framer-motion';
import { 
  Webhook,
  Send,
  Inbox,
  ArrowLeftRight,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  Timer,
  Zap,
  Activity,
  TrendingUp,
  TrendingDown,
  Play,
  Pause,
  Square,
  SkipForward,
  SkipBack,
  RefreshCw,
  Settings,
  Monitor,
  BarChart3,
  LineChart,
  PieChart,
  Eye,
  Download,
  Upload,
  Share,
  Filter,
  Search,
  Calendar,
  Users,
  Package,
  Tag,
  Hash,
  Layers,
  Grid,
  List,
  MoreHorizontal,
  ExternalLink,
  Info,
  Plus,
  Minus,
  Edit,
  Trash2,
  Copy,
  Bookmark,
  Star,
  Award,
  Lightbulb,
  Code,
  Terminal,
  FileText,
  Archive,
  FolderOpen,
  Link,
  Unlink,
  MousePointer,
  Database,
  Server,
  Cloud,
  Network,
  Globe,
  Shield,
  Lock,
  Unlock,
  Key,
  Wifi,
  Smartphone,
  Laptop,
  Tablet,
  Watch,
  Construction,
  Rocket,
  FlaskConical,
  Bug,
  CheckCheck,
  AlertCircle,
  Target,
  Crosshair,
  Move,
  RotateCw,
  Repeat,
  Shuffle,
  Split,
  Merge,
  Route,
  Navigation,
  Compass,
  MapPin,
  Bell,
  BellOff,
  Volume2,
  VolumeX,
  MessageSquare,
  MessageCircle,
  Mail,
  Phone,
  Headphones,
  Radio
} from 'lucide-react';
import { 
  LineChart as RechartsLineChart, 
  Line, 
  AreaChart, 
  Area, 
  BarChart, 
  Bar, 
  ScatterChart,
  Scatter,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  Cell,
  PieChart as RechartsPieChart,
  Pie,
  RadialBarChart,
  RadialBar,
  ComposedChart
} from 'recharts';

// Types
interface WebhookEndpoint {
  id: string;
  name: string;
  description: string;
  url: string;
  method: 'POST' | 'PUT' | 'PATCH';
  status: 'active' | 'inactive' | 'suspended' | 'error';
  direction: 'outbound' | 'inbound' | 'bidirectional';
  events: string[];
  headers: Record<string, string>;
  authentication: {
    type: 'none' | 'api_key' | 'bearer' | 'basic' | 'signature';
    config: Record<string, any>;
  };
  security: {
    secretKey: string;
    signatureHeader: string;
    algorithm: 'sha256' | 'sha1' | 'md5';
    ipWhitelist: string[];
    verifySSL: boolean;
  };
  retryPolicy: {
    enabled: boolean;
    maxRetries: number;
    backoffStrategy: 'linear' | 'exponential' | 'fixed';
    initialDelay: number; // seconds
    maxDelay: number; // seconds
    multiplier: number;
  };
  filters: WebhookFilter[];
  transformation: {
    enabled: boolean;
    template: string;
    mapping: Record<string, string>;
  };
  rateLimiting: {
    enabled: boolean;
    requests: number;
    window: number; // seconds
    burst: number;
  };
  monitoring: {
    healthCheckEnabled: boolean;
    healthCheckInterval: number; // minutes
    alertsEnabled: boolean;
    alertThresholds: {
      failureRate: number;
      latency: number; // ms
      errorCount: number;
    };
  };
  metrics: {
    totalDeliveries: number;
    successfulDeliveries: number;
    failedDeliveries: number;
    avgResponseTime: number; // ms
    lastDelivery: Date;
    deliveryRate: number; // per minute
    errorRate: number;
    uptime: number;
  };
  tags: string[];
  environment: 'development' | 'staging' | 'production';
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
}

interface WebhookFilter {
  id: string;
  name: string;
  type: 'event' | 'data' | 'header' | 'custom';
  condition: 'equals' | 'contains' | 'starts_with' | 'ends_with' | 'regex' | 'exists';
  field: string;
  value: any;
  enabled: boolean;
}

interface WebhookDelivery {
  id: string;
  webhookId: string;
  event: string;
  status: 'pending' | 'success' | 'failed' | 'retrying';
  attempt: number;
  maxAttempts: number;
  httpStatus?: number;
  responseTime: number; // ms
  requestPayload: any;
  responsePayload?: any;
  headers: Record<string, string>;
  error?: string;
  timestamp: Date;
  nextRetry?: Date;
  retryHistory: RetryAttempt[];
}

interface RetryAttempt {
  attempt: number;
  timestamp: Date;
  httpStatus?: number;
  responseTime: number;
  error?: string;
}

interface WebhookEvent {
  id: string;
  name: string;
  description: string;
  category: 'order' | 'customer' | 'product' | 'inventory' | 'payment' | 'shipping' | 'system';
  payloadSchema: any;
  frequency: 'high' | 'medium' | 'low';
  enabled: boolean;
  endpoints: string[];
  metrics: {
    triggersPerDay: number;
    successRate: number;
    avgProcessingTime: number;
  };
  examples: EventExample[];
}

interface EventExample {
  name: string;
  description: string;
  payload: any;
}

interface WebhookSubscription {
  id: string;
  endpointId: string;
  events: string[];
  filters: WebhookFilter[];
  status: 'active' | 'paused' | 'suspended';
  deliveryConfig: {
    batchSize: number;
    batchWindow: number; // seconds
    maxBatchDelay: number; // seconds
    compression: boolean;
  };
  createdAt: Date;
  updatedAt: Date;
}

interface WebhookTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  useCase: string;
  configuration: Partial<WebhookEndpoint>;
  popularity: number;
  tags: string[];
}

interface WebhookLog {
  id: string;
  webhookId: string;
  level: 'debug' | 'info' | 'warn' | 'error';
  message: string;
  timestamp: Date;
  metadata: Record<string, any>;
}

interface WebhookAlert {
  id: string;
  webhookId: string;
  type: 'delivery_failure' | 'high_latency' | 'rate_limit' | 'endpoint_down' | 'security_breach';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  threshold: number;
  currentValue: number;
  timestamp: Date;
  status: 'active' | 'acknowledged' | 'resolved';
  resolvedAt?: Date;
  assignee?: string;
}

// Mock data generators
const generateWebhookEndpoints = (): WebhookEndpoint[] => {
  const directions: WebhookEndpoint['direction'][] = ['outbound', 'inbound', 'bidirectional'];
  const statuses: WebhookEndpoint['status'][] = ['active', 'inactive', 'suspended', 'error'];
  const environments: WebhookEndpoint['environment'][] = ['production', 'staging', 'development'];

  return Array.from({ length: 18 }, (_, index) => ({
    id: `WH-${String(index + 1).padStart(3, '0')}`,
    name: `Webhook ${index + 1}`,
    description: `${directions[Math.floor(Math.random() * directions.length)]} webhook for ${['orders', 'customers', 'products', 'payments'][Math.floor(Math.random() * 4)]}`,
    url: `https://api.partner${index + 1}.com/webhooks/receive`,
    method: ['POST', 'PUT', 'PATCH'][Math.floor(Math.random() * 3)] as any,
    status: statuses[Math.floor(Math.random() * statuses.length)],
    direction: directions[Math.floor(Math.random() * directions.length)],
    events: ['order.created', 'order.updated', 'order.cancelled', 'customer.created', 'product.updated'].slice(0, Math.floor(Math.random() * 4) + 1),
    headers: {
      'Content-Type': 'application/json',
      'User-Agent': 'EcommerceAnalytics/1.0',
      'X-Webhook-Source': 'analytics-platform'
    },
    authentication: {
      type: ['none', 'api_key', 'bearer', 'signature'][Math.floor(Math.random() * 4)] as any,
      config: {
        key: 'webhook-secret-key',
        header: 'X-API-Key'
      }
    },
    security: {
      secretKey: `secret_${Math.random().toString(36).substring(2, 15)}`,
      signatureHeader: 'X-Webhook-Signature',
      algorithm: ['sha256', 'sha1'][Math.floor(Math.random() * 2)] as any,
      ipWhitelist: ['***********/24', '10.0.0.0/8'],
      verifySSL: Math.random() > 0.2
    },
    retryPolicy: {
      enabled: Math.random() > 0.2,
      maxRetries: Math.floor(Math.random() * 5) + 3,
      backoffStrategy: ['exponential', 'linear', 'fixed'][Math.floor(Math.random() * 3)] as any,
      initialDelay: Math.floor(Math.random() * 10) + 5,
      maxDelay: Math.floor(Math.random() * 300) + 300,
      multiplier: 1.5 + Math.random()
    },
    filters: [
      {
        id: `FILTER-${index + 1}`,
        name: 'Event Filter',
        type: 'event',
        condition: 'equals',
        field: 'event_type',
        value: 'order.created',
        enabled: true
      }
    ],
    transformation: {
      enabled: Math.random() > 0.4,
      template: '{"event": "{{event}}", "data": {{data}}, "timestamp": "{{timestamp}}"}',
      mapping: {
        'order.id': 'orderId',
        'customer.email': 'customerEmail'
      }
    },
    rateLimiting: {
      enabled: Math.random() > 0.3,
      requests: Math.floor(Math.random() * 1000) + 100,
      window: 60,
      burst: Math.floor(Math.random() * 100) + 50
    },
    monitoring: {
      healthCheckEnabled: Math.random() > 0.3,
      healthCheckInterval: Math.floor(Math.random() * 30) + 5,
      alertsEnabled: Math.random() > 0.2,
      alertThresholds: {
        failureRate: 0.1,
        latency: 5000,
        errorCount: 10
      }
    },
    metrics: {
      totalDeliveries: Math.floor(Math.random() * 100000) + 1000,
      successfulDeliveries: Math.floor(Math.random() * 95000) + 950,
      failedDeliveries: Math.floor(Math.random() * 5000) + 50,
      avgResponseTime: Math.floor(Math.random() * 2000) + 100,
      lastDelivery: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000),
      deliveryRate: Math.floor(Math.random() * 100) + 10,
      errorRate: Math.random() * 0.1,
      uptime: 0.95 + Math.random() * 0.05
    },
    tags: ['production', 'critical', 'real-time'].slice(0, Math.floor(Math.random() * 3) + 1),
    environment: environments[Math.floor(Math.random() * environments.length)],
    createdAt: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000),
    createdBy: `user${Math.floor(Math.random() * 10) + 1}@example.com`
  }));
};

const generateWebhookDeliveries = (endpoints: WebhookEndpoint[]): WebhookDelivery[] => {
  const deliveries: WebhookDelivery[] = [];
  
  endpoints.forEach(endpoint => {
    const numDeliveries = Math.floor(Math.random() * 20) + 10;
    for (let i = 0; i < numDeliveries; i++) {
      const status = ['success', 'failed', 'pending', 'retrying'][Math.floor(Math.random() * 4)] as any;
      const timestamp = new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000);
      
      deliveries.push({
        id: `DEL-${endpoint.id}-${String(i + 1).padStart(3, '0')}`,
        webhookId: endpoint.id,
        event: endpoint.events[Math.floor(Math.random() * endpoint.events.length)],
        status,
        attempt: status === 'retrying' ? Math.floor(Math.random() * 3) + 1 : 1,
        maxAttempts: endpoint.retryPolicy.maxRetries,
        httpStatus: status === 'success' ? 200 : status === 'failed' ? Math.floor(Math.random() * 400) + 400 : undefined,
        responseTime: Math.floor(Math.random() * 5000) + 100,
        requestPayload: {
          event: endpoint.events[0],
          data: { id: 'test-123', timestamp: timestamp.toISOString() },
          metadata: { source: 'analytics-platform' }
        },
        responsePayload: status === 'success' ? { received: true } : undefined,
        headers: endpoint.headers,
        error: status === 'failed' ? 'Connection timeout' : undefined,
        timestamp,
        nextRetry: status === 'retrying' ? new Date(Date.now() + Math.random() * 3600 * 1000) : undefined,
        retryHistory: status === 'retrying' ? [
          {
            attempt: 1,
            timestamp: new Date(timestamp.getTime() - 60000),
            httpStatus: 500,
            responseTime: 5000,
            error: 'Internal server error'
          }
        ] : []
      });
    }
  });
  
  return deliveries.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
};

const generateWebhookEvents = (): WebhookEvent[] => {
  const categories: WebhookEvent['category'][] = ['order', 'customer', 'product', 'inventory', 'payment', 'shipping'];
  
  return categories.flatMap(category => 
    ['created', 'updated', 'deleted'].map((action, index) => ({
      id: `EVENT-${category.toUpperCase()}-${action.toUpperCase()}`,
      name: `${category}.${action}`,
      description: `Triggered when a ${category} is ${action}`,
      category,
      payloadSchema: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          event: { type: 'string' },
          data: { type: 'object' },
          timestamp: { type: 'string' }
        }
      },
      frequency: ['high', 'medium', 'low'][Math.floor(Math.random() * 3)] as any,
      enabled: Math.random() > 0.2,
      endpoints: [],
      metrics: {
        triggersPerDay: Math.floor(Math.random() * 10000) + 100,
        successRate: 0.9 + Math.random() * 0.1,
        avgProcessingTime: Math.floor(Math.random() * 100) + 10
      },
      examples: [
        {
          name: 'Basic Example',
          description: `Example ${category} ${action} event`,
          payload: {
            id: 'evt_123',
            event: `${category}.${action}`,
            data: { id: `${category}_456`, name: `Sample ${category}` },
            timestamp: new Date().toISOString()
          }
        }
      ]
    }))
  );
};

const generateWebhookTemplates = (): WebhookTemplate[] => {
  return [
    {
      id: 'TEMPLATE-001',
      name: 'Order Processing Webhook',
      description: 'Real-time order notifications for fulfillment systems',
      category: 'ecommerce',
      useCase: 'Send order events to external fulfillment and inventory systems',
      configuration: {
        events: ['order.created', 'order.updated', 'order.cancelled'],
        retryPolicy: {
          enabled: true,
          maxRetries: 3,
          backoffStrategy: 'exponential',
          initialDelay: 5,
          maxDelay: 300,
          multiplier: 2
        }
      },
      popularity: 0.9,
      tags: ['orders', 'fulfillment', 'real-time']
    },
    {
      id: 'TEMPLATE-002',
      name: 'Customer Data Sync',
      description: 'Bi-directional customer data synchronization',
      category: 'integration',
      useCase: 'Keep customer data synchronized between CRM and e-commerce systems',
      configuration: {
        direction: 'bidirectional',
        events: ['customer.created', 'customer.updated'],
        transformation: {
          enabled: true,
          template: '{"customer_id": "{{data.id}}", "email": "{{data.email}}", "updated_at": "{{timestamp}}"}'
        }
      },
      popularity: 0.8,
      tags: ['customers', 'crm', 'sync']
    },
    {
      id: 'TEMPLATE-003',
      name: 'Payment Processing',
      description: 'Secure payment event notifications',
      category: 'payments',
      useCase: 'Handle payment confirmations and failures with high security',
      configuration: {
        events: ['payment.succeeded', 'payment.failed'],
        security: {
          algorithm: 'sha256',
          verifySSL: true
        },
        authentication: {
          type: 'signature'
        }
      },
      popularity: 0.85,
      tags: ['payments', 'security', 'notifications']
    }
  ];
};

const generateWebhookAlerts = (): WebhookAlert[] => {
  const types: WebhookAlert['type'][] = ['delivery_failure', 'high_latency', 'rate_limit', 'endpoint_down'];
  const severities: WebhookAlert['severity'][] = ['low', 'medium', 'high', 'critical'];

  return Array.from({ length: 6 }, (_, index) => ({
    id: `ALERT-${String(index + 1).padStart(3, '0')}`,
    webhookId: `WH-${String(Math.floor(Math.random() * 18) + 1).padStart(3, '0')}`,
    type: types[Math.floor(Math.random() * types.length)],
    severity: severities[Math.floor(Math.random() * severities.length)],
    title: `Webhook Alert ${index + 1}`,
    description: `Alert description for ${types[Math.floor(Math.random() * types.length)]} issue`,
    threshold: Math.random() * 100,
    currentValue: Math.random() * 120,
    timestamp: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000),
    status: ['active', 'acknowledged', 'resolved'][Math.floor(Math.random() * 3)] as any,
    resolvedAt: Math.random() > 0.5 ? new Date() : undefined
  }));
};

// Components
const WebhookCard: React.FC<{ webhook: WebhookEndpoint }> = ({ webhook }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      case 'suspended': return 'bg-yellow-100 text-yellow-800';
      case 'error': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle className="h-3 w-3" />;
      case 'inactive': return <Pause className="h-3 w-3" />;
      case 'suspended': return <Clock className="h-3 w-3" />;
      case 'error': return <XCircle className="h-3 w-3" />;
      default: return <Info className="h-3 w-3" />;
    }
  };

  const getDirectionIcon = (direction: string) => {
    switch (direction) {
      case 'outbound': return <Send className="h-4 w-4" />;
      case 'inbound': return <Inbox className="h-4 w-4" />;
      case 'bidirectional': return <ArrowLeftRight className="h-4 w-4" />;
      default: return <Webhook className="h-4 w-4" />;
    }
  };

  const successRate = webhook.metrics.totalDeliveries > 0 ? 
    (webhook.metrics.successfulDeliveries / webhook.metrics.totalDeliveries) * 100 : 0;

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className="border rounded-lg p-4 hover:shadow-md transition-shadow"
    >
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-2">
          {getDirectionIcon(webhook.direction)}
          <div>
            <h4 className="font-medium">{webhook.name}</h4>
            <p className="text-sm text-gray-600 capitalize">{webhook.direction}</p>
          </div>
        </div>
        <Badge className={getStatusColor(webhook.status)}>
          {getStatusIcon(webhook.status)}
          {webhook.status}
        </Badge>
      </div>

      <p className="text-sm text-gray-600 mb-3 line-clamp-2">{webhook.description}</p>

      <div className="grid grid-cols-2 gap-4 mb-3">
        <div>
          <p className="text-sm text-gray-600">Success Rate</p>
          <p className="text-lg font-semibold">{successRate.toFixed(1)}%</p>
        </div>
        <div>
          <p className="text-sm text-gray-600">Avg Response</p>
          <p className="text-lg font-semibold">{webhook.metrics.avgResponseTime}ms</p>
        </div>
        <div>
          <p className="text-sm text-gray-600">Deliveries/min</p>
          <p className="text-lg font-semibold">{webhook.metrics.deliveryRate}</p>
        </div>
        <div>
          <p className="text-sm text-gray-600">Uptime</p>
          <p className="text-lg font-semibold">{(webhook.metrics.uptime * 100).toFixed(1)}%</p>
        </div>
      </div>

      <div className="mb-3">
        <p className="text-sm text-gray-600 mb-1">Events</p>
        <div className="flex flex-wrap gap-1">
          {webhook.events.slice(0, 3).map((event, index) => (
            <Badge key={index} variant="outline" className="text-xs">
              {event}
            </Badge>
          ))}
          {webhook.events.length > 3 && (
            <Badge variant="outline" className="text-xs">
              +{webhook.events.length - 3} more
            </Badge>
          )}
        </div>
      </div>

      <div className="mb-3">
        <div className="flex items-center gap-2 text-sm">
          {webhook.security.verifySSL && <Lock className="h-3 w-3 text-green-500" />}
          {webhook.authentication.type !== 'none' && <Key className="h-3 w-3 text-blue-500" />}
          {webhook.retryPolicy.enabled && <RefreshCw className="h-3 w-3 text-purple-500" />}
          {webhook.monitoring.alertsEnabled && <Bell className="h-3 w-3 text-yellow-500" />}
        </div>
      </div>

      <div className="flex items-center justify-between">
        <Badge variant="secondary" className="capitalize">
          {webhook.environment}
        </Badge>
        <div className="flex items-center gap-2">
          <Button size="sm" variant="outline">
            <Eye className="h-3 w-3 mr-1" />
            Monitor
          </Button>
          <Button size="sm" variant="ghost">
            <Settings className="h-3 w-3" />
          </Button>
        </div>
      </div>
    </motion.div>
  );
};

const DeliveryCard: React.FC<{ delivery: WebhookDelivery }> = ({ delivery }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'bg-green-100 text-green-800';
      case 'failed': return 'bg-red-100 text-red-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'retrying': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success': return <CheckCircle className="h-3 w-3" />;
      case 'failed': return <XCircle className="h-3 w-3" />;
      case 'pending': return <Clock className="h-3 w-3" />;
      case 'retrying': return <RefreshCw className="h-3 w-3 animate-spin" />;
      default: return <Info className="h-3 w-3" />;
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="border rounded-lg p-4 hover:shadow-md transition-shadow"
    >
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-2">
          <Send className="h-4 w-4 text-blue-600" />
          <div>
            <h4 className="font-medium">{delivery.event}</h4>
            <p className="text-sm text-gray-600">{delivery.id}</p>
          </div>
        </div>
        <Badge className={getStatusColor(delivery.status)}>
          {getStatusIcon(delivery.status)}
          {delivery.status}
        </Badge>
      </div>

      <div className="grid grid-cols-2 gap-4 mb-3">
        <div>
          <p className="text-sm text-gray-600">Attempt</p>
          <p className="text-lg font-semibold">{delivery.attempt}/{delivery.maxAttempts}</p>
        </div>
        <div>
          <p className="text-sm text-gray-600">Response Time</p>
          <p className="text-lg font-semibold">{delivery.responseTime}ms</p>
        </div>
      </div>

      {delivery.httpStatus && (
        <div className="mb-3">
          <p className="text-sm text-gray-600 mb-1">HTTP Status</p>
          <Badge variant={delivery.httpStatus === 200 ? 'default' : 'destructive'}>
            {delivery.httpStatus}
          </Badge>
        </div>
      )}

      {delivery.error && (
        <div className="mb-3 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-800">
          <div className="flex items-center gap-2">
            <AlertTriangle className="h-3 w-3" />
            <span className="font-medium">Error:</span>
          </div>
          <p className="mt-1">{delivery.error}</p>
        </div>
      )}

      {delivery.nextRetry && (
        <div className="mb-3 p-2 bg-blue-50 border border-blue-200 rounded text-sm text-blue-800">
          <div className="flex items-center gap-2">
            <Timer className="h-3 w-3" />
            <span className="font-medium">Next retry:</span>
            <span>{delivery.nextRetry.toLocaleTimeString()}</span>
          </div>
        </div>
      )}

      <div className="flex items-center justify-between">
        <span className="text-sm text-gray-600">
          {delivery.timestamp.toLocaleTimeString()}
        </span>
        <div className="flex items-center gap-2">
          <Button size="sm" variant="outline">
            <FileText className="h-3 w-3 mr-1" />
            Details
          </Button>
          {delivery.status === 'failed' && (
            <Button size="sm" variant="ghost">
              <RefreshCw className="h-3 w-3" />
            </Button>
          )}
        </div>
      </div>
    </motion.div>
  );
};

const EventCard: React.FC<{ event: WebhookEvent }> = ({ event }) => {
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'order': return <Package className="h-4 w-4" />;
      case 'customer': return <Users className="h-4 w-4" />;
      case 'product': return <Tag className="h-4 w-4" />;
      case 'inventory': return <Archive className="h-4 w-4" />;
      case 'payment': return <CreditCard className="h-4 w-4" />;
      case 'shipping': return <Truck className="h-4 w-4" />;
      default: return <Bell className="h-4 w-4" />;
    }
  };

  const getFrequencyColor = (frequency: string) => {
    switch (frequency) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      className="border rounded-lg p-4 hover:shadow-md transition-shadow"
    >
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-2">
          {getCategoryIcon(event.category)}
          <div>
            <h4 className="font-medium">{event.name}</h4>
            <p className="text-sm text-gray-600 capitalize">{event.category}</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Badge className={getFrequencyColor(event.frequency)}>
            {event.frequency}
          </Badge>
          <Badge variant={event.enabled ? 'default' : 'secondary'}>
            {event.enabled ? 'Enabled' : 'Disabled'}
          </Badge>
        </div>
      </div>

      <p className="text-sm text-gray-600 mb-3">{event.description}</p>

      <div className="grid grid-cols-2 gap-4 mb-3">
        <div>
          <p className="text-sm text-gray-600">Triggers/Day</p>
          <p className="text-lg font-semibold">{event.metrics.triggersPerDay.toLocaleString()}</p>
        </div>
        <div>
          <p className="text-sm text-gray-600">Success Rate</p>
          <p className="text-lg font-semibold">{(event.metrics.successRate * 100).toFixed(1)}%</p>
        </div>
      </div>

      <div className="mb-3">
        <p className="text-sm text-gray-600 mb-1">Processing Time</p>
        <p className="text-sm font-medium">{event.metrics.avgProcessingTime}ms avg</p>
      </div>

      <div className="flex items-center justify-between">
        <span className="text-sm text-gray-600">
          {event.endpoints.length} endpoints
        </span>
        <div className="flex items-center gap-2">
          <Button size="sm" variant="outline">
            <Code className="h-3 w-3 mr-1" />
            Schema
          </Button>
          <Button size="sm" variant="ghost">
            <Settings className="h-3 w-3" />
          </Button>
        </div>
      </div>
    </motion.div>
  );
};

const TemplateCard: React.FC<{ template: WebhookTemplate }> = ({ template }) => {
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'ecommerce': return <ShoppingCart className="h-4 w-4" />;
      case 'integration': return <Link className="h-4 w-4" />;
      case 'payments': return <CreditCard className="h-4 w-4" />;
      default: return <Webhook className="h-4 w-4" />;
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className="border rounded-lg p-4 hover:shadow-md transition-shadow"
    >
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
            {getCategoryIcon(template.category)}
          </div>
          <div>
            <h4 className="font-medium">{template.name}</h4>
            <p className="text-sm text-gray-600 capitalize">{template.category}</p>
          </div>
        </div>
        <div className="flex items-center gap-1">
          <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
          <span className="text-sm font-medium">{(template.popularity * 5).toFixed(1)}</span>
        </div>
      </div>

      <p className="text-sm text-gray-600 mb-3">{template.description}</p>

      <div className="mb-3 p-2 bg-blue-50 border border-blue-200 rounded text-sm text-blue-800">
        <p className="font-medium">Use Case:</p>
        <p>{template.useCase}</p>
      </div>

      <div className="mb-3">
        <p className="text-sm text-gray-600 mb-1">Features</p>
        <div className="flex flex-wrap gap-1">
          {template.tags.slice(0, 3).map((tag, index) => (
            <Badge key={index} variant="outline" className="text-xs">
              {tag}
            </Badge>
          ))}
          {template.tags.length > 3 && (
            <Badge variant="outline" className="text-xs">
              +{template.tags.length - 3} more
            </Badge>
          )}
        </div>
      </div>

      <div className="flex items-center justify-between">
        <Badge variant="secondary" className="capitalize">
          {template.category}
        </Badge>
        <Button size="sm">
          <Download className="h-3 w-3 mr-1" />
          Use Template
        </Button>
      </div>
    </motion.div>
  );
};

const AlertCard: React.FC<{ alert: WebhookAlert }> = ({ alert }) => {
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-blue-100 text-blue-800 border-blue-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical': return <AlertTriangle className="h-4 w-4" />;
      case 'high': return <AlertCircle className="h-4 w-4" />;
      case 'medium': return <Info className="h-4 w-4" />;
      case 'low': return <CheckCircle className="h-4 w-4" />;
      default: return <Info className="h-4 w-4" />;
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className="border rounded-lg p-4 hover:shadow-md transition-shadow"
    >
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-2">
          {getSeverityIcon(alert.severity)}
          <div>
            <h4 className="font-medium">{alert.title}</h4>
            <p className="text-sm text-gray-600">{alert.description}</p>
          </div>
        </div>
        <Badge className={getSeverityColor(alert.severity)}>
          {alert.severity.toUpperCase()}
        </Badge>
      </div>

      <div className="grid grid-cols-2 gap-4 mb-3">
        <div>
          <p className="text-sm text-gray-600">Threshold</p>
          <p className="text-sm font-medium">{alert.threshold.toFixed(2)}</p>
        </div>
        <div>
          <p className="text-sm text-gray-600">Current Value</p>
          <p className="text-sm font-medium">{alert.currentValue.toFixed(2)}</p>
        </div>
      </div>

      <div className="mb-3">
        <p className="text-sm text-gray-600 mb-1">Webhook</p>
        <Badge variant="outline">{alert.webhookId}</Badge>
      </div>

      <div className="flex items-center justify-between">
        <span className="text-sm text-gray-600">
          {alert.timestamp.toLocaleTimeString()}
        </span>
        <Badge variant={alert.status === 'active' ? 'destructive' : 'secondary'}>
          {alert.status}
        </Badge>
      </div>
    </motion.div>
  );
};

// Import missing icons
const ShoppingCart = Package;
const CreditCard = Package;
const Truck = Package;

export const WebhookManagement: React.FC = () => {
  const [selectedDirection, setSelectedDirection] = useState<string>('all');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [selectedEnvironment, setSelectedEnvironment] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'name' | 'deliveries' | 'success_rate' | 'response_time'>('deliveries');

  const webhooks = useMemo(() => generateWebhookEndpoints(), []);
  const deliveries = useMemo(() => generateWebhookDeliveries(webhooks), [webhooks]);
  const events = useMemo(() => generateWebhookEvents(), []);
  const templates = useMemo(() => generateWebhookTemplates(), []);
  const alerts = useMemo(() => generateWebhookAlerts(), []);

  const filteredWebhooks = useMemo(() => {
    return webhooks
      .filter(webhook => selectedDirection === 'all' || webhook.direction === selectedDirection)
      .filter(webhook => selectedStatus === 'all' || webhook.status === selectedStatus)
      .filter(webhook => selectedEnvironment === 'all' || webhook.environment === selectedEnvironment)
      .filter(webhook => webhook.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                        webhook.description.toLowerCase().includes(searchQuery.toLowerCase()))
      .sort((a, b) => {
        switch (sortBy) {
          case 'name': return a.name.localeCompare(b.name);
          case 'deliveries': return b.metrics.totalDeliveries - a.metrics.totalDeliveries;
          case 'success_rate': 
            const aRate = a.metrics.totalDeliveries > 0 ? a.metrics.successfulDeliveries / a.metrics.totalDeliveries : 0;
            const bRate = b.metrics.totalDeliveries > 0 ? b.metrics.successfulDeliveries / b.metrics.totalDeliveries : 0;
            return bRate - aRate;
          case 'response_time': return a.metrics.avgResponseTime - b.metrics.avgResponseTime;
          default: return 0;
        }
      });
  }, [webhooks, selectedDirection, selectedStatus, selectedEnvironment, searchQuery, sortBy]);

  const webhookMetrics = useMemo(() => {
    const totalWebhooks = webhooks.length;
    const activeWebhooks = webhooks.filter(w => w.status === 'active').length;
    const totalDeliveries = webhooks.reduce((sum, w) => sum + w.metrics.totalDeliveries, 0);
    const successfulDeliveries = webhooks.reduce((sum, w) => sum + w.metrics.successfulDeliveries, 0);
    const failedDeliveries = webhooks.reduce((sum, w) => sum + w.metrics.failedDeliveries, 0);
    const avgResponseTime = webhooks.reduce((sum, w) => sum + w.metrics.avgResponseTime, 0) / webhooks.length;
    const totalEvents = events.filter(e => e.enabled).length;
    const activeAlerts = alerts.filter(a => a.status === 'active').length;

    return {
      totalWebhooks,
      activeWebhooks,
      totalDeliveries,
      successfulDeliveries,
      failedDeliveries,
      successRate: totalDeliveries > 0 ? (successfulDeliveries / totalDeliveries) : 0,
      avgResponseTime,
      totalEvents,
      activeAlerts
    };
  }, [webhooks, events, alerts]);

  const deliveryTrendData = useMemo(() => {
    return Array.from({ length: 24 }, (_, i) => ({
      hour: i,
      deliveries: Math.floor(Math.random() * 1000) + 500,
      success: Math.floor(Math.random() * 950) + 475,
      failed: Math.floor(Math.random() * 50) + 25,
      avg_response_time: Math.floor(Math.random() * 2000) + 100
    }));
  }, []);

  const directionData = useMemo(() => {
    const directionDistribution = webhooks.reduce((acc, webhook) => {
      acc[webhook.direction] = (acc[webhook.direction] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(directionDistribution).map(([direction, count]) => ({
      name: direction.charAt(0).toUpperCase() + direction.slice(1),
      value: count,
      percentage: ((count / webhooks.length) * 100).toFixed(1)
    }));
  }, [webhooks]);

  const recentDeliveries = useMemo(() => {
    return deliveries.slice(0, 10);
  }, [deliveries]);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Webhook className="h-6 w-6" />
            Webhook Management
          </h2>
          <p className="text-gray-600">Bi-directional webhook management with advanced delivery and monitoring</p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Plus className="h-4 w-4 mr-2" />
            Create Webhook
          </Button>
          <Button size="sm">
            <Monitor className="h-4 w-4 mr-2" />
            Monitor All
          </Button>
        </div>
      </div>

      <Tabs defaultValue="webhooks" className="w-full">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="webhooks">Webhooks</TabsTrigger>
          <TabsTrigger value="deliveries">Deliveries</TabsTrigger>
          <TabsTrigger value="events">Events</TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
          <TabsTrigger value="monitoring">Monitoring</TabsTrigger>
          <TabsTrigger value="alerts">Alerts</TabsTrigger>
        </TabsList>

        <TabsContent value="webhooks">
          <div className="space-y-6">
            {/* Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Webhook className="h-4 w-4 text-blue-600" />
                    <div>
                      <p className="text-sm text-gray-600">Total Webhooks</p>
                      <p className="text-2xl font-bold">{webhookMetrics.totalWebhooks}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <div>
                      <p className="text-sm text-gray-600">Active</p>
                      <p className="text-2xl font-bold">{webhookMetrics.activeWebhooks}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Send className="h-4 w-4 text-purple-600" />
                    <div>
                      <p className="text-sm text-gray-600">Total Deliveries</p>
                      <p className="text-2xl font-bold">{(webhookMetrics.totalDeliveries / 1000).toFixed(0)}K</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Target className="h-4 w-4 text-green-600" />
                    <div>
                      <p className="text-sm text-gray-600">Success Rate</p>
                      <p className="text-2xl font-bold">{(webhookMetrics.successRate * 100).toFixed(1)}%</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Filters */}
            <div className="flex items-center gap-4">
              <div className="flex-1">
                <Input
                  placeholder="Search webhooks..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full"
                />
              </div>
              <select
                value={selectedDirection}
                onChange={(e) => setSelectedDirection(e.target.value)}
                className="px-3 py-2 border rounded-md"
              >
                <option value="all">All Directions</option>
                <option value="outbound">Outbound</option>
                <option value="inbound">Inbound</option>
                <option value="bidirectional">Bidirectional</option>
              </select>
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="px-3 py-2 border rounded-md"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="suspended">Suspended</option>
                <option value="error">Error</option>
              </select>
              <select
                value={selectedEnvironment}
                onChange={(e) => setSelectedEnvironment(e.target.value)}
                className="px-3 py-2 border rounded-md"
              >
                <option value="all">All Environments</option>
                <option value="production">Production</option>
                <option value="staging">Staging</option>
                <option value="development">Development</option>
              </select>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="px-3 py-2 border rounded-md"
              >
                <option value="deliveries">Sort by Deliveries</option>
                <option value="name">Sort by Name</option>
                <option value="success_rate">Sort by Success Rate</option>
                <option value="response_time">Sort by Response Time</option>
              </select>
            </div>

            {/* Charts */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Direction Distribution</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <RechartsPieChart>
                      <Pie
                        data={directionData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percentage }) => `${name}: ${percentage}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {directionData.map((entry, index) => (
                          <Cell
                            key={`cell-${index}`}
                            fill={['#8884d8', '#82ca9d', '#ffc658'][index % 3]}
                          />
                        ))}
                      </Pie>
                      <Tooltip />
                    </RechartsPieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Delivery Trends</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <ComposedChart data={deliveryTrendData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="hour" />
                      <YAxis yAxisId="left" />
                      <YAxis yAxisId="right" orientation="right" />
                      <Tooltip />
                      <Legend />
                      <Bar yAxisId="left" dataKey="success" fill="#82ca9d" name="Successful" />
                      <Bar yAxisId="left" dataKey="failed" fill="#ff7300" name="Failed" />
                      <Line yAxisId="right" type="monotone" dataKey="avg_response_time" stroke="#8884d8" name="Avg Response Time (ms)" />
                    </ComposedChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>

            {/* Webhooks Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredWebhooks.map((webhook) => (
                <WebhookCard key={webhook.id} webhook={webhook} />
              ))}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="deliveries">
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Recent Deliveries</h3>
              <Button size="sm">
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {recentDeliveries.map((delivery) => (
                <DeliveryCard key={delivery.id} delivery={delivery} />
              ))}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="events">
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Webhook Events</h3>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Create Event
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {events.map((event) => (
                <EventCard key={event.id} event={event} />
              ))}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="templates">
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Webhook Templates</h3>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Create Template
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {templates.map((template) => (
                <TemplateCard key={template.id} template={template} />
              ))}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="monitoring">
          <div className="space-y-6">
            <h3 className="text-lg font-semibold">Webhook Monitoring</h3>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Delivery Performance</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <AreaChart data={deliveryTrendData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="hour" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Area type="monotone" dataKey="deliveries" stroke="#8884d8" fill="#8884d8" name="Total Deliveries" />
                    </AreaChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Response Time Distribution</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <LineChart data={deliveryTrendData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="hour" />
                      <YAxis />
                      <Tooltip />
                      <Line type="monotone" dataKey="avg_response_time" stroke="#82ca9d" name="Avg Response Time (ms)" />
                    </LineChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>

            {/* Real-time Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Timer className="h-4 w-4 text-blue-600" />
                    <div>
                      <p className="text-sm text-gray-600">Avg Response Time</p>
                      <p className="text-2xl font-bold">{webhookMetrics.avgResponseTime.toFixed(0)}ms</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Bell className="h-4 w-4 text-green-600" />
                    <div>
                      <p className="text-sm text-gray-600">Active Events</p>
                      <p className="text-2xl font-bold">{webhookMetrics.totalEvents}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <AlertTriangle className="h-4 w-4 text-red-600" />
                    <div>
                      <p className="text-sm text-gray-600">Active Alerts</p>
                      <p className="text-2xl font-bold">{webhookMetrics.activeAlerts}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="alerts">
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Webhook Alerts</h3>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Create Alert
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {alerts.map((alert) => (
                <AlertCard key={alert.id} alert={alert} />
              ))}
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default WebhookManagement;