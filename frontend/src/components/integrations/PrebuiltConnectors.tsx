/**
 * Pre-built Connectors
 * 100+ e-commerce platform integrations with automated configuration and management
 */

import React, { useState, useMemo, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { motion } from 'framer-motion';
import { 
  Package,
  Plug,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  Zap,
  Settings,
  Download,
  Upload,
  Share,
  RefreshCw,
  Play,
  Pause,
  Square,
  Filter,
  Search,
  Star,
  Heart,
  Eye,
  Users,
  Activity,
  TrendingUp,
  TrendingDown,
  BarChart3,
  LineChart,
  PieChart,
  Globe,
  Shield,
  Lock,
  Unlock,
  Key,
  Database,
  Server,
  Cloud,
  Network,
  Wifi,
  Smartphone,
  Laptop,
  Tablet,
  Monitor,
  ShoppingCart,
  CreditCard,
  Truck,
  Tag,
  Hash,
  Plus,
  Minus,
  Edit,
  Trash2,
  Copy,
  ExternalLink,
  Link,
  Unlink,
  Info,
  HelpCircle,
  CheckCheck,
  AlertCircle,
  Workflow,
  GitBranch,
  Code,
  Terminal,
  FileText,
  Archive,
  FolderOpen,
  Layers,
  Grid,
  List,
  MoreHorizontal,
  Calendar,
  Timer,
  Gauge,
  Award,
  Flag,
  Building2,
  Store,
  MapPin
} from 'lucide-react';
import { 
  LineChart as RechartsLineChart, 
  Line, 
  AreaChart, 
  Area, 
  BarChart, 
  Bar, 
  ScatterChart,
  Scatter,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  Cell,
  PieChart as RechartsPieChart,
  Pie,
  RadialBarChart,
  RadialBar,
  TreeMap
} from 'recharts';

// Types
interface Connector {
  id: string;
  name: string;
  displayName: string;
  description: string;
  category: 'ecommerce' | 'payment' | 'shipping' | 'marketing' | 'analytics' | 'erp' | 'crm' | 'inventory';
  platform: string;
  version: string;
  logo: string;
  popularity: number;
  rating: number;
  totalInstalls: number;
  status: 'available' | 'installed' | 'configured' | 'active' | 'error' | 'deprecated';
  tier: 'free' | 'basic' | 'premium' | 'enterprise';
  pricing: {
    free: boolean;
    startingPrice?: number;
    currency: string;
    billingCycle: 'monthly' | 'yearly' | 'usage';
  };
  capabilities: {
    dataSync: string[];
    realTime: boolean;
    webhooks: boolean;
    bulkOperations: boolean;
    customFields: boolean;
    scheduling: boolean;
  };
  authentication: {
    type: 'api_key' | 'oauth2' | 'basic' | 'custom';
    fields: AuthField[];
  };
  configuration: {
    required: ConfigField[];
    optional: ConfigField[];
    mappings: FieldMapping[];
  };
  metrics: {
    lastSync: Date;
    totalRecords: number;
    errorRate: number;
    avgSyncTime: number;
    dataFreshness: number; // minutes
  };
  support: {
    documentation: string;
    community: boolean;
    priority: boolean;
    sla: string;
  };
  compliance: {
    gdpr: boolean;
    soc2: boolean;
    hipaa: boolean;
    pci: boolean;
  };
  regions: string[];
  lastUpdated: Date;
  vendor: {
    name: string;
    website: string;
    support: string;
    verified: boolean;
  };
}

interface AuthField {
  name: string;
  label: string;
  type: 'text' | 'password' | 'url' | 'select';
  required: boolean;
  description: string;
  placeholder?: string;
  options?: string[];
  validation?: {
    pattern?: string;
    minLength?: number;
    maxLength?: number;
  };
}

interface ConfigField {
  name: string;
  label: string;
  type: 'text' | 'number' | 'boolean' | 'select' | 'multiselect' | 'json';
  required: boolean;
  description: string;
  defaultValue?: any;
  options?: { label: string; value: any }[];
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
  };
}

interface FieldMapping {
  sourceField: string;
  targetField: string;
  transformation?: 'uppercase' | 'lowercase' | 'date_format' | 'currency' | 'custom';
  required: boolean;
  description: string;
}

interface ConnectorInstallation {
  id: string;
  connectorId: string;
  name: string;
  status: 'installing' | 'configuring' | 'testing' | 'active' | 'paused' | 'error';
  progress: number;
  configuration: Record<string, any>;
  lastSync: Date;
  nextSync: Date;
  syncFrequency: number; // minutes
  metrics: {
    totalSyncs: number;
    successfulSyncs: number;
    failedSyncs: number;
    avgDuration: number;
    lastError?: string;
  };
  dataFlow: {
    inbound: {
      enabled: boolean;
      entities: string[];
      lastSync: Date;
      recordCount: number;
    };
    outbound: {
      enabled: boolean;
      entities: string[];
      lastSync: Date;
      recordCount: number;
    };
  };
  alerts: ConnectorAlert[];
  logs: ConnectorLog[];
}

interface ConnectorAlert {
  id: string;
  type: 'sync_failure' | 'auth_error' | 'rate_limit' | 'data_quality' | 'performance';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  timestamp: Date;
  resolved: boolean;
  resolvedAt?: Date;
}

interface ConnectorLog {
  id: string;
  timestamp: Date;
  level: 'debug' | 'info' | 'warn' | 'error';
  message: string;
  metadata: Record<string, any>;
}

interface ConnectorTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  connectors: string[];
  configuration: Record<string, any>;
  popularity: number;
  useCase: string;
}

interface ConnectorMarketplace {
  featured: Connector[];
  trending: Connector[];
  categories: {
    name: string;
    count: number;
    connectors: Connector[];
  }[];
  totalConnectors: number;
}

// Mock data generators
const generateConnectors = (): Connector[] => {
  const ecommercePlatforms = [
    'Shopify', 'WooCommerce', 'Magento', 'BigCommerce', 'Prestashop', 
    'Opencart', 'Squarespace', 'Wix', 'Volusion', 'Salesforce Commerce'
  ];
  
  const paymentProviders = [
    'Stripe', 'PayPal', 'Square', 'Adyen', 'Braintree',
    'Authorize.net', 'Klarna', 'Afterpay', 'Mollie', 'Razorpay'
  ];
  
  const shippingCarriers = [
    'FedEx', 'UPS', 'DHL', 'USPS', 'Amazon Logistics',
    'Canada Post', 'Royal Mail', 'Australia Post', 'La Poste', 'Deutsche Post'
  ];
  
  const marketingTools = [
    'Mailchimp', 'Klaviyo', 'SendGrid', 'Constant Contact', 'Campaign Monitor',
    'HubSpot', 'Marketo', 'Salesforce Marketing', 'ActiveCampaign', 'ConvertKit'
  ];
  
  const analyticsTools = [
    'Google Analytics', 'Adobe Analytics', 'Mixpanel', 'Amplitude', 'Segment',
    'Hotjar', 'Crazy Egg', 'Kissmetrics', 'Heap', 'Fullstory'
  ];

  const allPlatforms = [
    ...ecommercePlatforms.map(name => ({ name, category: 'ecommerce' as const })),
    ...paymentProviders.map(name => ({ name, category: 'payment' as const })),
    ...shippingCarriers.map(name => ({ name, category: 'shipping' as const })),
    ...marketingTools.map(name => ({ name, category: 'marketing' as const })),
    ...analyticsTools.map(name => ({ name, category: 'analytics' as const }))
  ];

  return allPlatforms.map((platform, index) => ({
    id: `CONN-${String(index + 1).padStart(3, '0')}`,
    name: platform.name.toLowerCase().replace(/\s+/g, '_'),
    displayName: platform.name,
    description: `Official ${platform.name} integration for seamless data synchronization`,
    category: platform.category,
    platform: platform.name,
    version: `v${Math.floor(Math.random() * 3) + 1}.${Math.floor(Math.random() * 10)}.${Math.floor(Math.random() * 10)}`,
    logo: `/logos/${platform.name.toLowerCase()}.png`,
    popularity: Math.random(),
    rating: 3.5 + Math.random() * 1.5,
    totalInstalls: Math.floor(Math.random() * 100000) + 1000,
    status: ['available', 'installed', 'configured', 'active'][Math.floor(Math.random() * 4)] as any,
    tier: ['free', 'basic', 'premium', 'enterprise'][Math.floor(Math.random() * 4)] as any,
    pricing: {
      free: Math.random() > 0.6,
      startingPrice: Math.random() > 0.3 ? Math.floor(Math.random() * 100) + 10 : undefined,
      currency: 'USD',
      billingCycle: 'monthly'
    },
    capabilities: {
      dataSync: ['orders', 'customers', 'products', 'inventory'].slice(0, Math.floor(Math.random() * 4) + 1),
      realTime: Math.random() > 0.4,
      webhooks: Math.random() > 0.3,
      bulkOperations: Math.random() > 0.5,
      customFields: Math.random() > 0.6,
      scheduling: Math.random() > 0.7
    },
    authentication: {
      type: ['api_key', 'oauth2', 'basic'][Math.floor(Math.random() * 3)] as any,
      fields: [
        {
          name: 'api_key',
          label: 'API Key',
          type: 'password',
          required: true,
          description: 'Your API key from the platform dashboard'
        },
        {
          name: 'store_url',
          label: 'Store URL',
          type: 'url',
          required: true,
          description: 'Your store URL'
        }
      ]
    },
    configuration: {
      required: [
        {
          name: 'sync_frequency',
          label: 'Sync Frequency',
          type: 'select',
          required: true,
          description: 'How often to sync data',
          options: [
            { label: 'Every 15 minutes', value: 15 },
            { label: 'Every hour', value: 60 },
            { label: 'Every 6 hours', value: 360 }
          ]
        }
      ],
      optional: [
        {
          name: 'enable_webhooks',
          label: 'Enable Webhooks',
          type: 'boolean',
          required: false,
          description: 'Enable real-time data updates via webhooks',
          defaultValue: true
        }
      ],
      mappings: [
        {
          sourceField: 'order_id',
          targetField: 'external_order_id',
          required: true,
          description: 'Maps platform order ID to our system'
        }
      ]
    },
    metrics: {
      lastSync: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000),
      totalRecords: Math.floor(Math.random() * 100000) + 1000,
      errorRate: Math.random() * 0.05,
      avgSyncTime: Math.floor(Math.random() * 300) + 30,
      dataFreshness: Math.floor(Math.random() * 60) + 5
    },
    support: {
      documentation: `https://docs.${platform.name.toLowerCase()}.com`,
      community: Math.random() > 0.3,
      priority: Math.random() > 0.7,
      sla: Math.random() > 0.5 ? '24/7' : 'Business Hours'
    },
    compliance: {
      gdpr: Math.random() > 0.2,
      soc2: Math.random() > 0.4,
      hipaa: Math.random() > 0.8,
      pci: platform.category === 'payment' ? Math.random() > 0.1 : Math.random() > 0.7
    },
    regions: ['US', 'EU', 'APAC'].slice(0, Math.floor(Math.random() * 3) + 1),
    lastUpdated: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
    vendor: {
      name: `${platform.name} Inc.`,
      website: `https://www.${platform.name.toLowerCase()}.com`,
      support: `support@${platform.name.toLowerCase()}.com`,
      verified: Math.random() > 0.2
    }
  }));
};

const generateInstallations = (connectors: Connector[]): ConnectorInstallation[] => {
  const installedConnectors = connectors.filter(c => ['installed', 'configured', 'active'].includes(c.status));
  
  return installedConnectors.slice(0, 12).map((connector) => ({
    id: `INST-${connector.id}`,
    connectorId: connector.id,
    name: `${connector.displayName} Integration`,
    status: ['active', 'paused', 'error', 'configuring'][Math.floor(Math.random() * 4)] as any,
    progress: Math.random() * 100,
    configuration: {
      api_key: '••••••••••••1234',
      store_url: `https://store.${connector.name}.com`,
      sync_frequency: 60,
      enable_webhooks: true
    },
    lastSync: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000),
    nextSync: new Date(Date.now() + Math.random() * 60 * 60 * 1000),
    syncFrequency: 60,
    metrics: {
      totalSyncs: Math.floor(Math.random() * 1000) + 100,
      successfulSyncs: Math.floor(Math.random() * 950) + 95,
      failedSyncs: Math.floor(Math.random() * 50) + 5,
      avgDuration: Math.floor(Math.random() * 180) + 30,
      lastError: Math.random() > 0.7 ? 'Rate limit exceeded' : undefined
    },
    dataFlow: {
      inbound: {
        enabled: true,
        entities: ['orders', 'customers', 'products'],
        lastSync: new Date(Date.now() - Math.random() * 60 * 60 * 1000),
        recordCount: Math.floor(Math.random() * 10000) + 1000
      },
      outbound: {
        enabled: Math.random() > 0.5,
        entities: ['inventory', 'pricing'],
        lastSync: new Date(Date.now() - Math.random() * 60 * 60 * 1000),
        recordCount: Math.floor(Math.random() * 5000) + 500
      }
    },
    alerts: [],
    logs: []
  }));
};

const generateTemplates = (): ConnectorTemplate[] => {
  return [
    {
      id: 'TEMP-001',
      name: 'E-commerce Starter Pack',
      description: 'Essential integrations for new e-commerce businesses',
      category: 'starter',
      connectors: ['shopify', 'stripe', 'mailchimp', 'google_analytics'],
      configuration: {
        sync_frequency: 60,
        enable_webhooks: true,
        auto_mapping: true
      },
      popularity: 0.9,
      useCase: 'Perfect for startups and small businesses getting started'
    },
    {
      id: 'TEMP-002',
      name: 'Enterprise Suite',
      description: 'Comprehensive integration suite for large organizations',
      category: 'enterprise',
      connectors: ['magento', 'salesforce', 'hubspot', 'fedex', 'adobe_analytics'],
      configuration: {
        sync_frequency: 15,
        enable_webhooks: true,
        priority_support: true
      },
      popularity: 0.7,
      useCase: 'Designed for enterprise-level operations with high volume'
    },
    {
      id: 'TEMP-003',
      name: 'Multi-channel Retail',
      description: 'Perfect for businesses selling across multiple channels',
      category: 'multichannel',
      connectors: ['shopify', 'woocommerce', 'amazon', 'ebay', 'facebook_shop'],
      configuration: {
        sync_frequency: 30,
        inventory_sync: true,
        unified_analytics: true
      },
      popularity: 0.8,
      useCase: 'Ideal for retailers managing multiple sales channels'
    }
  ];
};

// Components
const ConnectorCard: React.FC<{ connector: Connector }> = ({ connector }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'configured': return 'bg-blue-100 text-blue-800';
      case 'installed': return 'bg-yellow-100 text-yellow-800';
      case 'available': return 'bg-gray-100 text-gray-800';
      case 'error': return 'bg-red-100 text-red-800';
      case 'deprecated': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle className="h-3 w-3" />;
      case 'configured': return <Settings className="h-3 w-3" />;
      case 'installed': return <Package className="h-3 w-3" />;
      case 'available': return <Download className="h-3 w-3" />;
      case 'error': return <XCircle className="h-3 w-3" />;
      case 'deprecated': return <AlertTriangle className="h-3 w-3" />;
      default: return <Info className="h-3 w-3" />;
    }
  };

  const getTierColor = (tier: string) => {
    switch (tier) {
      case 'free': return 'bg-green-100 text-green-800';
      case 'basic': return 'bg-blue-100 text-blue-800';
      case 'premium': return 'bg-purple-100 text-purple-800';
      case 'enterprise': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className="border rounded-lg p-4 hover:shadow-md transition-shadow"
    >
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
            <Package className="h-5 w-5 text-gray-600" />
          </div>
          <div>
            <h4 className="font-medium">{connector.displayName}</h4>
            <p className="text-sm text-gray-600">{connector.version}</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Badge className={getTierColor(connector.tier)}>
            {connector.tier}
          </Badge>
          <Badge className={getStatusColor(connector.status)}>
            {getStatusIcon(connector.status)}
            {connector.status}
          </Badge>
        </div>
      </div>

      <p className="text-sm text-gray-600 mb-3 line-clamp-2">{connector.description}</p>

      <div className="grid grid-cols-2 gap-4 mb-3">
        <div>
          <p className="text-sm text-gray-600">Rating</p>
          <div className="flex items-center gap-1">
            <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
            <span className="text-sm font-medium">{connector.rating.toFixed(1)}</span>
          </div>
        </div>
        <div>
          <p className="text-sm text-gray-600">Installs</p>
          <p className="text-sm font-medium">{(connector.totalInstalls / 1000).toFixed(0)}K</p>
        </div>
      </div>

      <div className="mb-3">
        <p className="text-sm text-gray-600 mb-1">Capabilities</p>
        <div className="flex flex-wrap gap-1">
          {connector.capabilities.dataSync.slice(0, 3).map((capability, index) => (
            <Badge key={index} variant="outline" className="text-xs">
              {capability}
            </Badge>
          ))}
          {connector.capabilities.dataSync.length > 3 && (
            <Badge variant="outline" className="text-xs">
              +{connector.capabilities.dataSync.length - 3}
            </Badge>
          )}
        </div>
      </div>

      <div className="mb-3">
        <div className="flex items-center gap-2 text-sm">
          {connector.capabilities.realTime && <Zap className="h-3 w-3 text-yellow-500" />}
          {connector.capabilities.webhooks && <Webhook className="h-3 w-3 text-blue-500" />}
          {connector.vendor.verified && <CheckCheck className="h-3 w-3 text-green-500" />}
          {connector.compliance.gdpr && <Shield className="h-3 w-3 text-purple-500" />}
        </div>
      </div>

      <div className="flex items-center justify-between">
        <div className="text-sm text-gray-600">
          {connector.pricing.free ? 'Free' : `$${connector.pricing.startingPrice}/mo`}
        </div>
        <div className="flex items-center gap-2">
          {connector.status === 'available' && (
            <Button size="sm" variant="outline">
              <Download className="h-3 w-3 mr-1" />
              Install
            </Button>
          )}
          {connector.status === 'active' && (
            <Button size="sm" variant="outline">
              <Settings className="h-3 w-3 mr-1" />
              Configure
            </Button>
          )}
          <Button size="sm" variant="ghost">
            <Eye className="h-3 w-3" />
          </Button>
        </div>
      </div>
    </motion.div>
  );
};

const InstallationCard: React.FC<{ installation: ConnectorInstallation }> = ({ installation }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'paused': return 'bg-yellow-100 text-yellow-800';
      case 'error': return 'bg-red-100 text-red-800';
      case 'configuring': return 'bg-blue-100 text-blue-800';
      case 'installing': return 'bg-purple-100 text-purple-800';
      case 'testing': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const successRate = installation.metrics.totalSyncs > 0 ? 
    (installation.metrics.successfulSyncs / installation.metrics.totalSyncs) * 100 : 0;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="border rounded-lg p-4 hover:shadow-md transition-shadow"
    >
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
            <Plug className="h-5 w-5 text-gray-600" />
          </div>
          <div>
            <h4 className="font-medium">{installation.name}</h4>
            <p className="text-sm text-gray-600">
              Last sync: {installation.lastSync.toLocaleTimeString()}
            </p>
          </div>
        </div>
        <Badge className={getStatusColor(installation.status)}>
          {installation.status}
        </Badge>
      </div>

      <div className="grid grid-cols-2 gap-4 mb-3">
        <div>
          <p className="text-sm text-gray-600">Success Rate</p>
          <p className="text-lg font-semibold">{successRate.toFixed(1)}%</p>
        </div>
        <div>
          <p className="text-sm text-gray-600">Avg Duration</p>
          <p className="text-lg font-semibold">{installation.metrics.avgDuration}s</p>
        </div>
      </div>

      <div className="mb-3">
        <div className="flex items-center justify-between text-sm mb-1">
          <span className="text-gray-600">Sync Progress</span>
          <span>{installation.progress.toFixed(0)}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${installation.progress}%` }}
          />
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4 mb-3">
        <div>
          <p className="text-sm text-gray-600">Inbound Records</p>
          <p className="text-sm font-medium">{installation.dataFlow.inbound.recordCount.toLocaleString()}</p>
        </div>
        <div>
          <p className="text-sm text-gray-600">Outbound Records</p>
          <p className="text-sm font-medium">{installation.dataFlow.outbound.recordCount.toLocaleString()}</p>
        </div>
      </div>

      {installation.metrics.lastError && (
        <div className="mb-3 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-800">
          <div className="flex items-center gap-2">
            <AlertTriangle className="h-3 w-3" />
            <span className="font-medium">Last Error:</span>
          </div>
          <p className="mt-1">{installation.metrics.lastError}</p>
        </div>
      )}

      <div className="flex items-center justify-between">
        <span className="text-sm text-gray-600">
          Next sync: {installation.nextSync.toLocaleTimeString()}
        </span>
        <div className="flex items-center gap-2">
          <Button size="sm" variant="outline">
            <RefreshCw className="h-3 w-3 mr-1" />
            Sync Now
          </Button>
          <Button size="sm" variant="ghost">
            <Settings className="h-3 w-3" />
          </Button>
        </div>
      </div>
    </motion.div>
  );
};

const TemplateCard: React.FC<{ template: ConnectorTemplate }> = ({ template }) => {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className="border rounded-lg p-4 hover:shadow-md transition-shadow"
    >
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
            <Workflow className="h-5 w-5 text-white" />
          </div>
          <div>
            <h4 className="font-medium">{template.name}</h4>
            <p className="text-sm text-gray-600">{template.connectors.length} connectors</p>
          </div>
        </div>
        <div className="flex items-center gap-1">
          <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
          <span className="text-sm font-medium">{(template.popularity * 5).toFixed(1)}</span>
        </div>
      </div>

      <p className="text-sm text-gray-600 mb-3">{template.description}</p>

      <div className="mb-3">
        <p className="text-sm text-gray-600 mb-1">Included Connectors</p>
        <div className="flex flex-wrap gap-1">
          {template.connectors.slice(0, 3).map((connector, index) => (
            <Badge key={index} variant="outline" className="text-xs capitalize">
              {connector.replace('_', ' ')}
            </Badge>
          ))}
          {template.connectors.length > 3 && (
            <Badge variant="outline" className="text-xs">
              +{template.connectors.length - 3} more
            </Badge>
          )}
        </div>
      </div>

      <div className="mb-3 p-2 bg-blue-50 border border-blue-200 rounded text-sm text-blue-800">
        <p className="font-medium">Use Case:</p>
        <p>{template.useCase}</p>
      </div>

      <div className="flex items-center justify-between">
        <Badge variant="secondary" className="capitalize">
          {template.category}
        </Badge>
        <Button size="sm">
          <Download className="h-3 w-3 mr-1" />
          Install Template
        </Button>
      </div>
    </motion.div>
  );
};

const Webhook: React.FC = () => <Network className="h-4 w-4" />;

export const PrebuiltConnectors: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedTier, setSelectedTier] = useState<string>('all');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'name' | 'popularity' | 'rating' | 'installs'>('popularity');

  const connectors = useMemo(() => generateConnectors(), []);
  const installations = useMemo(() => generateInstallations(connectors), [connectors]);
  const templates = useMemo(() => generateTemplates(), []);

  const filteredConnectors = useMemo(() => {
    return connectors
      .filter(conn => selectedCategory === 'all' || conn.category === selectedCategory)
      .filter(conn => selectedTier === 'all' || conn.tier === selectedTier)
      .filter(conn => selectedStatus === 'all' || conn.status === selectedStatus)
      .filter(conn => conn.displayName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                     conn.description.toLowerCase().includes(searchQuery.toLowerCase()))
      .sort((a, b) => {
        switch (sortBy) {
          case 'name': return a.displayName.localeCompare(b.displayName);
          case 'popularity': return b.popularity - a.popularity;
          case 'rating': return b.rating - a.rating;
          case 'installs': return b.totalInstalls - a.totalInstalls;
          default: return 0;
        }
      });
  }, [connectors, selectedCategory, selectedTier, selectedStatus, searchQuery, sortBy]);

  const marketplaceMetrics = useMemo(() => {
    const categoryDistribution = connectors.reduce((acc, conn) => {
      acc[conn.category] = (acc[conn.category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const totalInstalls = connectors.reduce((sum, conn) => sum + conn.totalInstalls, 0);
    const avgRating = connectors.reduce((sum, conn) => sum + conn.rating, 0) / connectors.length;
    const activeInstallations = installations.filter(inst => inst.status === 'active').length;

    return {
      totalConnectors: connectors.length,
      categoryDistribution,
      totalInstalls,
      avgRating,
      activeInstallations,
      verifiedVendors: connectors.filter(conn => conn.vendor.verified).length
    };
  }, [connectors, installations]);

  const installationTrendData = useMemo(() => {
    return Array.from({ length: 30 }, (_, i) => ({
      day: i + 1,
      installs: Math.floor(Math.random() * 100) + 50,
      active: Math.floor(Math.random() * 80) + 40,
      errors: Math.floor(Math.random() * 10) + 2
    }));
  }, []);

  const categoryData = useMemo(() => {
    return Object.entries(marketplaceMetrics.categoryDistribution).map(([category, count]) => ({
      name: category,
      value: count,
      percentage: ((count / marketplaceMetrics.totalConnectors) * 100).toFixed(1)
    }));
  }, [marketplaceMetrics]);

  const popularConnectorsData = useMemo(() => {
    return connectors
      .sort((a, b) => b.totalInstalls - a.totalInstalls)
      .slice(0, 10)
      .map(conn => ({
        name: conn.displayName,
        installs: conn.totalInstalls,
        rating: conn.rating,
        category: conn.category
      }));
  }, [connectors]);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Package className="h-6 w-6" />
            Pre-built Connectors
          </h2>
          <p className="text-gray-600">100+ verified integrations for seamless data synchronization</p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Upload className="h-4 w-4 mr-2" />
            Upload Connector
          </Button>
          <Button size="sm">
            <Plus className="h-4 w-4 mr-2" />
            Request Integration
          </Button>
        </div>
      </div>

      <Tabs defaultValue="marketplace" className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="marketplace">Marketplace</TabsTrigger>
          <TabsTrigger value="installed">Installed</TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="management">Management</TabsTrigger>
        </TabsList>

        <TabsContent value="marketplace">
          <div className="space-y-6">
            {/* Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Package className="h-4 w-4 text-blue-600" />
                    <div>
                      <p className="text-sm text-gray-600">Total Connectors</p>
                      <p className="text-2xl font-bold">{marketplaceMetrics.totalConnectors}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Download className="h-4 w-4 text-green-600" />
                    <div>
                      <p className="text-sm text-gray-600">Total Installs</p>
                      <p className="text-2xl font-bold">{(marketplaceMetrics.totalInstalls / 1000000).toFixed(1)}M</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Star className="h-4 w-4 text-yellow-600" />
                    <div>
                      <p className="text-sm text-gray-600">Avg Rating</p>
                      <p className="text-2xl font-bold">{marketplaceMetrics.avgRating.toFixed(1)}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <CheckCheck className="h-4 w-4 text-purple-600" />
                    <div>
                      <p className="text-sm text-gray-600">Verified Vendors</p>
                      <p className="text-2xl font-bold">{marketplaceMetrics.verifiedVendors}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Filters */}
            <div className="flex items-center gap-4">
              <div className="flex-1">
                <Input
                  placeholder="Search connectors..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full"
                />
              </div>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-3 py-2 border rounded-md"
              >
                <option value="all">All Categories</option>
                <option value="ecommerce">E-commerce</option>
                <option value="payment">Payment</option>
                <option value="shipping">Shipping</option>
                <option value="marketing">Marketing</option>
                <option value="analytics">Analytics</option>
              </select>
              <select
                value={selectedTier}
                onChange={(e) => setSelectedTier(e.target.value)}
                className="px-3 py-2 border rounded-md"
              >
                <option value="all">All Tiers</option>
                <option value="free">Free</option>
                <option value="basic">Basic</option>
                <option value="premium">Premium</option>
                <option value="enterprise">Enterprise</option>
              </select>
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="px-3 py-2 border rounded-md"
              >
                <option value="all">All Status</option>
                <option value="available">Available</option>
                <option value="installed">Installed</option>
                <option value="active">Active</option>
              </select>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="px-3 py-2 border rounded-md"
              >
                <option value="popularity">Sort by Popularity</option>
                <option value="name">Sort by Name</option>
                <option value="rating">Sort by Rating</option>
                <option value="installs">Sort by Installs</option>
              </select>
            </div>

            {/* Charts */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Category Distribution</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <RechartsPieChart>
                      <Pie
                        data={categoryData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percentage }) => `${name}: ${percentage}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {categoryData.map((entry, index) => (
                          <Cell
                            key={`cell-${index}`}
                            fill={['#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#8dd1e1'][index % 5]}
                          />
                        ))}
                      </Pie>
                      <Tooltip />
                    </RechartsPieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Popular Connectors</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={popularConnectorsData.slice(0, 8)} layout="horizontal">
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis type="number" />
                      <YAxis dataKey="name" type="category" width={80} />
                      <Tooltip />
                      <Bar dataKey="installs" fill="#8884d8" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>

            {/* Connectors Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredConnectors.map((connector) => (
                <ConnectorCard key={connector.id} connector={connector} />
              ))}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="installed">
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Active Integrations</h3>
              <Button size="sm">
                <RefreshCw className="h-4 w-4 mr-2" />
                Sync All
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {installations.map((installation) => (
                <InstallationCard key={installation.id} installation={installation} />
              ))}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="templates">
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Integration Templates</h3>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Create Template
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {templates.map((template) => (
                <TemplateCard key={template.id} template={template} />
              ))}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="analytics">
          <div className="space-y-6">
            <h3 className="text-lg font-semibold">Integration Analytics</h3>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Installation Trends</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <AreaChart data={installationTrendData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="day" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Area type="monotone" dataKey="installs" stackId="1" stroke="#8884d8" fill="#8884d8" name="New Installs" />
                      <Area type="monotone" dataKey="active" stackId="1" stroke="#82ca9d" fill="#82ca9d" name="Active" />
                    </AreaChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Error Rates</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <LineChart data={installationTrendData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="day" />
                      <YAxis />
                      <Tooltip />
                      <Line type="monotone" dataKey="errors" stroke="#ef4444" name="Errors" />
                    </LineChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>

            {/* Performance Table */}
            <Card>
              <CardHeader>
                <CardTitle>Integration Performance</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left p-2">Integration</th>
                        <th className="text-left p-2">Status</th>
                        <th className="text-left p-2">Success Rate</th>
                        <th className="text-left p-2">Avg Duration</th>
                        <th className="text-left p-2">Records Synced</th>
                        <th className="text-left p-2">Last Error</th>
                      </tr>
                    </thead>
                    <tbody>
                      {installations.slice(0, 8).map((installation) => (
                        <motion.tr
                          key={installation.id}
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          className="border-b hover:bg-gray-50"
                        >
                          <td className="p-2 font-medium">{installation.name}</td>
                          <td className="p-2">
                            <Badge className={getStatusColor(installation.status)}>
                              {installation.status}
                            </Badge>
                          </td>
                          <td className="p-2">
                            {((installation.metrics.successfulSyncs / installation.metrics.totalSyncs) * 100).toFixed(1)}%
                          </td>
                          <td className="p-2">{installation.metrics.avgDuration}s</td>
                          <td className="p-2">
                            {(installation.dataFlow.inbound.recordCount + installation.dataFlow.outbound.recordCount).toLocaleString()}
                          </td>
                          <td className="p-2 text-sm text-red-600">
                            {installation.metrics.lastError || '-'}
                          </td>
                        </motion.tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="management">
          <div className="space-y-6">
            <h3 className="text-lg font-semibold">Integration Management</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Bulk Operations</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <Button className="w-full justify-start">
                      <RefreshCw className="h-4 w-4 mr-2" />
                      Sync All Integrations
                    </Button>
                    <Button className="w-full justify-start" variant="outline">
                      <Pause className="h-4 w-4 mr-2" />
                      Pause All Integrations
                    </Button>
                    <Button className="w-full justify-start" variant="outline">
                      <Settings className="h-4 w-4 mr-2" />
                      Update All Configurations
                    </Button>
                    <Button className="w-full justify-start" variant="outline">
                      <Download className="h-4 w-4 mr-2" />
                      Export Integration Data
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>System Health</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">API Gateway</span>
                      <Badge className="bg-green-100 text-green-800">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Healthy
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Data Pipeline</span>
                      <Badge className="bg-green-100 text-green-800">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Healthy
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Queue Processing</span>
                      <Badge className="bg-yellow-100 text-yellow-800">
                        <AlertTriangle className="h-3 w-3 mr-1" />
                        Warning
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Error Handling</span>
                      <Badge className="bg-green-100 text-green-800">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Healthy
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Global Settings */}
            <Card>
              <CardHeader>
                <CardTitle>Global Settings</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-medium mb-3">Default Sync Settings</h4>
                    <div className="space-y-3">
                      <div>
                        <label className="text-sm text-gray-600">Default Sync Frequency</label>
                        <select className="w-full mt-1 px-3 py-2 border rounded-md">
                          <option value="15">Every 15 minutes</option>
                          <option value="60">Every hour</option>
                          <option value="360">Every 6 hours</option>
                          <option value="1440">Daily</option>
                        </select>
                      </div>
                      <div className="flex items-center gap-2">
                        <input type="checkbox" id="auto-retry" defaultChecked />
                        <label htmlFor="auto-retry" className="text-sm">Auto-retry failed syncs</label>
                      </div>
                      <div className="flex items-center gap-2">
                        <input type="checkbox" id="webhook-fallback" defaultChecked />
                        <label htmlFor="webhook-fallback" className="text-sm">Webhook fallback enabled</label>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h4 className="font-medium mb-3">Notification Settings</h4>
                    <div className="space-y-3">
                      <div className="flex items-center gap-2">
                        <input type="checkbox" id="sync-alerts" defaultChecked />
                        <label htmlFor="sync-alerts" className="text-sm">Sync failure alerts</label>
                      </div>
                      <div className="flex items-center gap-2">
                        <input type="checkbox" id="performance-alerts" defaultChecked />
                        <label htmlFor="performance-alerts" className="text-sm">Performance alerts</label>
                      </div>
                      <div className="flex items-center gap-2">
                        <input type="checkbox" id="weekly-reports" defaultChecked />
                        <label htmlFor="weekly-reports" className="text-sm">Weekly summary reports</label>
                      </div>
                      <div>
                        <label className="text-sm text-gray-600">Alert Email</label>
                        <Input 
                          type="email" 
                          placeholder="<EMAIL>"
                          className="mt-1"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

function getStatusColor(status: string): string {
  switch (status) {
    case 'active': return 'bg-green-100 text-green-800';
    case 'paused': return 'bg-yellow-100 text-yellow-800';
    case 'error': return 'bg-red-100 text-red-800';
    case 'configuring': return 'bg-blue-100 text-blue-800';
    default: return 'bg-gray-100 text-gray-800';
  }
}

export default PrebuiltConnectors;