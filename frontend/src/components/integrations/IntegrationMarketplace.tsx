/**
 * Integration Marketplace
 * Community-driven marketplace for custom connectors and integrations
 */

import React, { useState, useMemo, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { motion } from 'framer-motion';
import { 
  Store,
  Download,
  Upload,
  Heart,
  Eye,
  Users,
  TrendingUp,
  Verified,
  BarChart3,
  LineChart,
  PieChart,
  FileText,
  MessageCircle,
  Plug,
  Palette,
  Crown,
  Star as StarIcon,
  Package as PackageIcon,
  Grid,
  List,
  Bookmark,
  Workflow
} from 'lucide-react';
import { 
  LineChart as RechartsLineChart, 
  Line, 
  AreaChart, 
  Area, 
  BarChart, 
  Bar, 
  Scatter<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON> as Recharts<PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>s, 
  CartesianGrid, 
  Toolt<PERSON>, 
  Legend, 
  ResponsiveContainer,
  Cell
} from 'recharts';

// Types for Integration Marketplace
interface MarketplaceItem {
  id: string;
  name: string;
  description: string;
  category: string;
  subcategory: string;
  type: 'connector' | 'template' | 'workflow' | 'component' | 'theme';
  version: string;
  author: User;
  maintainers: User[];
  pricing: {
    type: 'free' | 'paid' | 'freemium' | 'subscription';
    price?: number;
    currency?: string;
    billingCycle?: 'monthly' | 'yearly' | 'one-time';
  };
  stats: {
    downloads: number;
    installations: number;
    rating: number;
    reviews: number;
    likes: number;
    stars: number;
    forks: number;
    watchers: number;
  };
  metadata: {
    tags: string[];
    platforms: string[];
    languages: string[];
    frameworks: string[];
    dependencies: string[];
    compatibility: string[];
    size: number;
    lastUpdated: Date;
    created: Date;
    verified: boolean;
    featured: boolean;
    trending: boolean;
    popular: boolean;
    recommended: boolean;
  };
  content: {
    readme: string;
    documentation: string;
    changelog: string;
    license: string;
    repository: string;
    demo: string;
    screenshots: string[];
    videos: string[];
    thumbnails: string[];
  };
  support: {
    email?: string;
    website?: string;
    documentation?: string;
    community?: string;
    issues?: string;
    discussions?: string;
    support?: string;
  };
  security: {
    scanned: boolean;
    lastScan: Date;
    vulnerabilities: SecurityVulnerability[];
    permissions: string[];
    dataAccess: string[];
  };
}

interface User {
  id: string;
  username: string;
  displayName: string;
  email: string;
  avatar: string;
  bio: string;
  location: string;
  website: string;
  social: {
    github?: string;
    twitter?: string;
    linkedin?: string;
  };
  stats: {
    followers: number;
    following: number;
    repositories: number;
    contributions: number;
    reputation: number;
  };
  badges: Badge[];
  verified: boolean;
  pro: boolean;
  joinDate: Date;
}

interface SecurityVulnerability {
  id: string;
  type: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  cve?: string;
  score: number;
  fixed: boolean;
  fixedVersion?: string;
  reportedDate: Date;
}

interface Review {
  id: string;
  itemId: string;
  user: User;
  rating: number;
  title: string;
  content: string;
  pros: string[];
  cons: string[];
  verified: boolean;
  helpful: number;
  reportCount: number;
  createdDate: Date;
  updatedDate?: Date;
  version: string;
}

interface Collection {
  id: string;
  name: string;
  description: string;
  author: User;
  items: string[];
  public: boolean;
  featured: boolean;
  tags: string[];
  createdDate: Date;
  updatedDate: Date;
  followers: number;
  likes: number;
}

interface Publisher {
  id: string;
  name: string;
  type: 'individual' | 'organization' | 'company';
  displayName: string;
  description: string;
  avatar: string;
  website: string;
  email: string;
  verified: boolean;
  partnered: boolean;
  items: string[];
  followers: number;
  totalDownloads: number;
  averageRating: number;
  joinDate: Date;
  social: {
    github?: string;
    twitter?: string;
    linkedin?: string;
    facebook?: string;
  };
}

interface AnalyticsData {
  downloads: number;
  installations: number;
  revenue: number;
  users: number;
  reviews: number;
  rating: number;
  period: string;
  change: number;
  trend: 'up' | 'down' | 'stable';
}

// Mock data generators
const generateMockUsers = (): User[] => {
  return Array.from({ length: 50 }, (_, i) => ({
    id: `user-${i + 1}`,
    username: `developer${i + 1}`,
    displayName: `Developer ${i + 1}`,
    email: `dev${i + 1}@example.com`,
    avatar: `/api/placeholder/40/40`,
    bio: `Passionate developer building amazing integrations`,
    location: ['New York', 'San Francisco', 'London', 'Berlin', 'Tokyo'][i % 5],
    website: `https://dev${i + 1}.dev`,
    social: {
      github: `dev${i + 1}`,
      twitter: `dev${i + 1}`,
      linkedin: `dev${i + 1}`
    },
    stats: {
      followers: Math.floor(Math.random() * 10000),
      following: Math.floor(Math.random() * 1000),
      repositories: Math.floor(Math.random() * 100),
      contributions: Math.floor(Math.random() * 5000),
      reputation: Math.floor(Math.random() * 10000)
    },
    badges: [],
    verified: Math.random() > 0.7,
    pro: Math.random() > 0.8,
    joinDate: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000)
  }));
};

const generateMockMarketplaceItems = (users: User[]): MarketplaceItem[] => {
  const categories = ['E-commerce', 'Analytics', 'CRM', 'Marketing', 'Payment', 'Shipping', 'Inventory', 'Social'];
  const types: Array<'connector' | 'template' | 'workflow' | 'component' | 'theme'> = 
    ['connector', 'template', 'workflow', 'component', 'theme'];
  const pricingTypes: Array<'free' | 'paid' | 'freemium' | 'subscription'> = 
    ['free', 'paid', 'freemium', 'subscription'];

  return Array.from({ length: 100 }, (_, i) => ({
    id: `item-${i + 1}`,
    name: `${categories[i % categories.length]} ${types[i % types.length]} ${i + 1}`,
    description: `Professional ${types[i % types.length]} for ${categories[i % categories.length].toLowerCase()} integration`,
    category: categories[i % categories.length],
    subcategory: ['API', 'Webhook', 'Database', 'File', 'Real-time'][i % 5],
    type: types[i % types.length],
    version: `${Math.floor(Math.random() * 3) + 1}.${Math.floor(Math.random() * 10)}.${Math.floor(Math.random() * 10)}`,
    author: users[i % users.length],
    maintainers: users.slice(i % 10, (i % 10) + 3),
    pricing: {
      type: pricingTypes[i % pricingTypes.length],
      price: pricingTypes[i % pricingTypes.length] === 'free' ? undefined : Math.floor(Math.random() * 100) + 10,
      currency: 'USD',
      billingCycle: ['monthly', 'yearly', 'one-time'][Math.floor(Math.random() * 3)] as any
    },
    stats: {
      downloads: Math.floor(Math.random() * 100000),
      installations: Math.floor(Math.random() * 50000),
      rating: 3.5 + Math.random() * 1.5,
      reviews: Math.floor(Math.random() * 1000),
      likes: Math.floor(Math.random() * 5000),
      stars: Math.floor(Math.random() * 2000),
      forks: Math.floor(Math.random() * 500),
      watchers: Math.floor(Math.random() * 1000)
    },
    metadata: {
      tags: ['integration', 'automation', 'api', 'webhook'].slice(0, Math.floor(Math.random() * 4) + 1),
      platforms: ['Web', 'Mobile', 'Desktop', 'Server'].slice(0, Math.floor(Math.random() * 4) + 1),
      languages: ['JavaScript', 'Python', 'Java', 'Go'].slice(0, Math.floor(Math.random() * 4) + 1),
      frameworks: ['React', 'Vue', 'Angular', 'Node.js'].slice(0, Math.floor(Math.random() * 4) + 1),
      dependencies: [],
      compatibility: ['v1.0+', 'v2.0+'][Math.floor(Math.random() * 2)],
      size: Math.floor(Math.random() * 10000) + 100,
      lastUpdated: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
      created: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000),
      verified: Math.random() > 0.6,
      featured: Math.random() > 0.9,
      trending: Math.random() > 0.8,
      popular: Math.random() > 0.7,
      recommended: Math.random() > 0.8
    },
    content: {
      readme: `# ${categories[i % categories.length]} Integration\n\nDetailed documentation...`,
      documentation: `Documentation for ${categories[i % categories.length]} integration`,
      changelog: `## v1.0.0\n- Initial release`,
      license: 'MIT',
      repository: `https://github.com/user/repo${i + 1}`,
      demo: `https://demo.example.com/item${i + 1}`,
      screenshots: [`/api/placeholder/600/400`],
      videos: [`/api/placeholder/video/600/400`],
      thumbnails: [`/api/placeholder/200/150`]
    },
    support: {
      email: '<EMAIL>',
      website: 'https://example.com',
      documentation: 'https://docs.example.com',
      community: 'https://community.example.com',
      issues: 'https://github.com/user/repo/issues',
      discussions: 'https://github.com/user/repo/discussions'
    },
    security: {
      scanned: Math.random() > 0.2,
      lastScan: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000),
      vulnerabilities: [],
      permissions: ['read', 'write'],
      dataAccess: ['orders', 'customers']
    }
  }));
};

const generateMockReviews = (items: MarketplaceItem[], users: User[]): Review[] => {
  return Array.from({ length: 200 }, (_, i) => ({
    id: `review-${i + 1}`,
    itemId: items[i % items.length].id,
    user: users[i % users.length],
    rating: Math.floor(Math.random() * 5) + 1,
    title: `Great ${items[i % items.length].type}!`,
    content: `This ${items[i % items.length].type} works perfectly for our needs. Highly recommended!`,
    pros: ['Easy to use', 'Well documented', 'Great support'],
    cons: ['Could use more features'],
    verified: Math.random() > 0.3,
    helpful: Math.floor(Math.random() * 50),
    reportCount: 0,
    createdDate: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000),
    version: items[i % items.length].version
  }));
};

// Performance data for charts
const marketplaceAnalyticsData = Array.from({ length: 12 }, (_, i) => ({
  month: new Date(2024, i, 1).toLocaleDateString('en', { month: 'short' }),
  downloads: Math.floor(Math.random() * 50000) + 10000,
  revenue: Math.floor(Math.random() * 100000) + 20000,
  newItems: Math.floor(Math.random() * 100) + 50,
  users: Math.floor(Math.random() * 10000) + 5000
}));

const categoryDistribution = [
  { name: 'E-commerce', value: 35, color: '#3b82f6' },
  { name: 'Analytics', value: 20, color: '#10b981' },
  { name: 'CRM', value: 15, color: '#f59e0b' },
  { name: 'Marketing', value: 12, color: '#ef4444' },
  { name: 'Payment', value: 10, color: '#8b5cf6' },
  { name: 'Other', value: 8, color: '#6b7280' }
];

const trendingItemsData = Array.from({ length: 10 }, (_, i) => ({
  name: `Integration ${i + 1}`,
  downloads: Math.floor(Math.random() * 5000) + 1000,
  change: Math.floor(Math.random() * 200) - 100
}));

const IntegrationMarketplace: React.FC = () => {
  const [activeTab, setActiveTab] = useState('browse');
  const [selectedItem, setSelectedItem] = useState<string | null>(null);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedType, setSelectedType] = useState('all');
  const [selectedPricing, setSelectedPricing] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState('popular');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  const users = useMemo(() => generateMockUsers(), []);
  const items = useMemo(() => generateMockMarketplaceItems(users), [users]);
  const reviews = useMemo(() => generateMockReviews(items, users), [items, users]);

  const filteredItems = useMemo(() => {
    return items.filter(item => {
      const matchesSearch = item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           item.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           item.metadata.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
      const matchesCategory = selectedCategory === 'all' || item.category === selectedCategory;
      const matchesType = selectedType === 'all' || item.type === selectedType;
      const matchesPricing = selectedPricing === 'all' || item.pricing.type === selectedPricing;
      return matchesSearch && matchesCategory && matchesType && matchesPricing;
    });
  }, [items, searchQuery, selectedCategory, selectedType, selectedPricing]);

  const sortedItems = useMemo(() => {
    const sorted = [...filteredItems];
    switch (sortBy) {
      case 'popular':
        return sorted.sort((a, b) => b.stats.downloads - a.stats.downloads);
      case 'rating':
        return sorted.sort((a, b) => b.stats.rating - a.stats.rating);
      case 'newest':
        return sorted.sort((a, b) => b.metadata.created.getTime() - a.metadata.created.getTime());
      case 'updated':
        return sorted.sort((a, b) => b.metadata.lastUpdated.getTime() - a.metadata.lastUpdated.getTime());
      case 'name':
        return sorted.sort((a, b) => a.name.localeCompare(b.name));
      default:
        return sorted;
    }
  }, [filteredItems, sortBy]);

  const getPricingColor = (type: string) => {
    switch (type) {
      case 'free': return 'bg-green-100 text-green-800';
      case 'paid': return 'bg-blue-100 text-blue-800';
      case 'freemium': return 'bg-purple-100 text-purple-800';
      case 'subscription': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'connector': return <Plug className="h-4 w-4" />;
      case 'template': return <FileText className="h-4 w-4" />;
      case 'workflow': return <Workflow className="h-4 w-4" />;
      case 'component': return <PackageIcon className="h-4 w-4" />;
      case 'theme': return <Palette className="h-4 w-4" />;
      default: return <PackageIcon className="h-4 w-4" />;
    }
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <StarIcon
        key={i}
        className={`h-3 w-3 ${i < Math.floor(rating) ? 'text-yellow-500 fill-current' : 'text-gray-300'}`}
      />
    ));
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Integration Marketplace</h1>
          <p className="text-gray-600 mt-2">Discover and share community-built integrations</p>
        </div>
        <div className="flex gap-3">
          <Button className="flex items-center gap-2">
            <Upload className="h-4 w-4" />
            Publish Item
          </Button>
          <Button variant="outline" className="flex items-center gap-2">
            <Store className="h-4 w-4" />
            My Items
          </Button>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-6 w-full">
          <TabsTrigger value="browse" className="flex items-center gap-2">
            <Store className="h-4 w-4" />
            Browse
          </TabsTrigger>
          <TabsTrigger value="featured" className="flex items-center gap-2">
            <Crown className="h-4 w-4" />
            Featured
          </TabsTrigger>
          <TabsTrigger value="trending" className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Trending
          </TabsTrigger>
          <TabsTrigger value="collections" className="flex items-center gap-2">
            <Bookmark className="h-4 w-4" />
            Collections
          </TabsTrigger>
          <TabsTrigger value="publishers" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Publishers
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Analytics
          </TabsTrigger>
        </TabsList>

        {/* Browse */}
        <TabsContent value="browse" className="space-y-6">
          {/* Filters */}
          <div className="flex flex-wrap gap-4 items-center">
            <div className="flex-1 min-w-[300px]">
              <Input
                placeholder="Search integrations..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full"
              />
            </div>
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="border rounded-md px-3 py-2"
            >
              <option value="all">All Categories</option>
              <option value="E-commerce">E-commerce</option>
              <option value="Analytics">Analytics</option>
              <option value="CRM">CRM</option>
              <option value="Marketing">Marketing</option>
              <option value="Payment">Payment</option>
            </select>
            <select
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value)}
              className="border rounded-md px-3 py-2"
            >
              <option value="all">All Types</option>
              <option value="connector">Connectors</option>
              <option value="template">Templates</option>
              <option value="workflow">Workflows</option>
              <option value="component">Components</option>
              <option value="theme">Themes</option>
            </select>
            <select
              value={selectedPricing}
              onChange={(e) => setSelectedPricing(e.target.value)}
              className="border rounded-md px-3 py-2"
            >
              <option value="all">All Pricing</option>
              <option value="free">Free</option>
              <option value="paid">Paid</option>
              <option value="freemium">Freemium</option>
              <option value="subscription">Subscription</option>
            </select>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="border rounded-md px-3 py-2"
            >
              <option value="popular">Most Popular</option>
              <option value="rating">Highest Rated</option>
              <option value="newest">Newest</option>
              <option value="updated">Recently Updated</option>
              <option value="name">Name A-Z</option>
            </select>
            <div className="flex gap-2">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('grid')}
              >
                <Grid className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('list')}
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Results */}
          <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' : 'space-y-4'}>
            {sortedItems.map((item) => (
              <motion.div
                key={item.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className={viewMode === 'grid' ? 'h-full' : ''}
              >
                <Card className={viewMode === 'grid' ? 'h-full flex flex-col' : ''}>
                  {viewMode === 'grid' && (
                    <div className="relative">
                      <img 
                        src={item.content.thumbnails[0]} 
                        alt={item.name}
                        className="w-full h-40 object-cover rounded-t-lg"
                      />
                      <div className="absolute top-2 left-2 flex gap-2">
                        {item.metadata.verified && (
                          <Badge className="bg-blue-100 text-blue-800">
                            <Verified className="h-3 w-3 mr-1" />
                            Verified
                          </Badge>
                        )}
                        {item.metadata.featured && (
                          <Badge className="bg-yellow-100 text-yellow-800">
                            <Crown className="h-3 w-3 mr-1" />
                            Featured
                          </Badge>
                        )}
                      </div>
                      <div className="absolute top-2 right-2">
                        <Badge className={getPricingColor(item.pricing.type)}>
                          {item.pricing.type === 'free' ? 'Free' : 
                           item.pricing.type === 'paid' ? `$${item.pricing.price}` :
                           item.pricing.type === 'freemium' ? 'Freemium' : 'Subscription'}
                        </Badge>
                      </div>
                    </div>
                  )}
                  
                  <CardContent className={`p-4 ${viewMode === 'grid' ? 'flex-1 flex flex-col' : ''}`}>
                    <div className={`space-y-3 ${viewMode === 'grid' ? 'flex-1' : ''}`}>
                      <div className="flex items-start justify-between">
                        <div className={`${viewMode === 'list' ? 'flex items-center gap-4 flex-1' : ''}`}>
                          {viewMode === 'list' && (
                            <img 
                              src={item.content.thumbnails[0]} 
                              alt={item.name}
                              className="w-16 h-16 object-cover rounded-lg"
                            />
                          )}
                          <div className={viewMode === 'list' ? 'flex-1' : ''}>
                            <div className="flex items-center gap-2 mb-1">
                              {getTypeIcon(item.type)}
                              <h3 className="font-semibold">{item.name}</h3>
                              {viewMode === 'list' && item.metadata.verified && (
                                <Verified className="h-4 w-4 text-blue-500" />
                              )}
                            </div>
                            <p className={`text-sm text-gray-600 ${viewMode === 'list' ? 'mb-2' : 'mb-3'}`}>
                              {item.description}
                            </p>
                            {viewMode === 'list' && (
                              <div className="flex items-center gap-4 text-sm text-gray-500">
                                <div className="flex items-center gap-1">
                                  <Download className="h-3 w-3" />
                                  {item.stats.downloads.toLocaleString()}
                                </div>
                                <div className="flex items-center gap-1">
                                  {renderStars(item.stats.rating)}
                                  <span className="ml-1">{item.stats.rating.toFixed(1)}</span>
                                </div>
                                <Badge variant="outline" className="text-xs">
                                  {item.category}
                                </Badge>
                              </div>
                            )}
                          </div>
                        </div>
                        {viewMode === 'list' && (
                          <div className="flex items-center gap-2">
                            <Badge className={getPricingColor(item.pricing.type)}>
                              {item.pricing.type === 'free' ? 'Free' : 
                               item.pricing.type === 'paid' ? `$${item.pricing.price}` :
                               item.pricing.type === 'freemium' ? 'Freemium' : 'Subscription'}
                            </Badge>
                            <Button size="sm">
                              <Download className="h-3 w-3 mr-1" />
                              Install
                            </Button>
                          </div>
                        )}
                      </div>

                      {viewMode === 'grid' && (
                        <>
                          <div className="flex items-center gap-4 text-sm text-gray-500">
                            <div className="flex items-center gap-1">
                              <Download className="h-3 w-3" />
                              {item.stats.downloads.toLocaleString()}
                            </div>
                            <div className="flex items-center gap-1">
                              {renderStars(item.stats.rating)}
                              <span className="ml-1">{item.stats.rating.toFixed(1)}</span>
                            </div>
                          </div>
                          
                          <div className="flex items-center gap-2">
                            <div className="flex items-center gap-1">
                              <img 
                                src={item.author.avatar} 
                                alt={item.author.displayName}
                                className="w-5 h-5 rounded-full"
                              />
                              <span className="text-sm text-gray-600">{item.author.displayName}</span>
                            </div>
                            {item.author.verified && (
                              <Verified className="h-3 w-3 text-blue-500" />
                            )}
                          </div>

                          <div className="flex flex-wrap gap-1">
                            <Badge variant="outline" className="text-xs">{item.category}</Badge>
                            {item.metadata.tags.slice(0, 2).map(tag => (
                              <Badge key={tag} variant="secondary" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                          </div>
                        </>
                      )}
                    </div>

                    {viewMode === 'grid' && (
                      <div className="flex gap-2 mt-4">
                        <Button size="sm" className="flex-1">
                          <Download className="h-3 w-3 mr-1" />
                          Install
                        </Button>
                        <Button variant="outline" size="sm">
                          <Eye className="h-3 w-3" />
                        </Button>
                        <Button variant="outline" size="sm">
                          <Heart className="h-3 w-3" />
                        </Button>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </TabsContent>

        {/* Featured */}
        <TabsContent value="featured" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {items.filter(item => item.metadata.featured).map((item) => (
              <motion.div
                key={item.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="h-full"
              >
                <Card className="h-full flex flex-col relative overflow-hidden">
                  <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-yellow-400 to-orange-500" />
                  <div className="relative">
                    <img 
                      src={item.content.thumbnails[0]} 
                      alt={item.name}
                      className="w-full h-48 object-cover"
                    />
                    <div className="absolute top-3 left-3">
                      <Badge className="bg-yellow-100 text-yellow-800">
                        <Crown className="h-3 w-3 mr-1" />
                        Featured
                      </Badge>
                    </div>
                    <div className="absolute top-3 right-3">
                      <Badge className={getPricingColor(item.pricing.type)}>
                        {item.pricing.type === 'free' ? 'Free' : `$${item.pricing.price}`}
                      </Badge>
                    </div>
                  </div>
                  <CardContent className="p-4 flex-1 flex flex-col">
                    <div className="space-y-3 flex-1">
                      <div>
                        <h3 className="font-semibold text-lg">{item.name}</h3>
                        <p className="text-sm text-gray-600 mt-1">{item.description}</p>
                      </div>
                      <div className="flex items-center gap-4 text-sm text-gray-500">
                        <div className="flex items-center gap-1">
                          <Download className="h-3 w-3" />
                          {item.stats.downloads.toLocaleString()}
                        </div>
                        <div className="flex items-center gap-1">
                          {renderStars(item.stats.rating)}
                          <span className="ml-1">{item.stats.rating.toFixed(1)}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <MessageCircle className="h-3 w-3" />
                          {item.stats.reviews}
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <img 
                          src={item.author.avatar} 
                          alt={item.author.displayName}
                          className="w-6 h-6 rounded-full"
                        />
                        <span className="text-sm text-gray-600">{item.author.displayName}</span>
                        {item.author.verified && (
                          <Verified className="h-4 w-4 text-blue-500" />
                        )}
                      </div>
                    </div>
                    <div className="flex gap-2 mt-4">
                      <Button size="sm" className="flex-1">
                        <Download className="h-3 w-3 mr-1" />
                        Install
                      </Button>
                      <Button variant="outline" size="sm">
                        <Eye className="h-3 w-3" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </TabsContent>

        {/* Trending */}
        <TabsContent value="trending" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="h-5 w-5" />
                    Trending This Week
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {items.filter(item => item.metadata.trending).slice(0, 10).map((item, index) => (
                      <div key={item.id} className="flex items-center gap-4 p-3 border rounded-lg">
                        <div className="flex items-center justify-center w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-full font-bold text-sm">
                          {index + 1}
                        </div>
                        <img 
                          src={item.content.thumbnails[0]} 
                          alt={item.name}
                          className="w-12 h-12 object-cover rounded-lg"
                        />
                        <div className="flex-1">
                          <h4 className="font-medium">{item.name}</h4>
                          <div className="flex items-center gap-4 text-sm text-gray-500">
                            <div className="flex items-center gap-1">
                              <Download className="h-3 w-3" />
                              {item.stats.downloads.toLocaleString()}
                            </div>
                            <div className="flex items-center gap-1">
                              <TrendingUp className="h-3 w-3 text-green-500" />
                              +{Math.floor(Math.random() * 50) + 10}%
                            </div>
                          </div>
                        </div>
                        <Button size="sm" variant="outline">
                          <Eye className="h-3 w-3" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BarChart3 className="h-5 w-5" />
                    Download Trends
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-48">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart data={trendingItemsData.slice(0, 6)}>
                        <XAxis dataKey="name" fontSize={10} />
                        <YAxis fontSize={10} />
                        <Tooltip />
                        <Bar dataKey="downloads" fill="#3b82f6" />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <StarIcon className="h-5 w-5" />
                    Rising Stars
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {items.slice(0, 5).map((item) => (
                      <div key={item.id} className="flex items-center gap-3">
                        <img 
                          src={item.content.thumbnails[0]} 
                          alt={item.name}
                          className="w-8 h-8 object-cover rounded"
                        />
                        <div className="flex-1">
                          <div className="font-medium text-sm">{item.name}</div>
                          <div className="text-xs text-gray-500">{item.category}</div>
                        </div>
                        <div className="flex items-center gap-1 text-xs text-yellow-600">
                          <StarIcon className="h-3 w-3 fill-current" />
                          {item.stats.rating.toFixed(1)}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        {/* Collections */}
        <TabsContent value="collections" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {['Essential E-commerce', 'Analytics Toolkit', 'Marketing Suite', 'Developer Favorites', 'Enterprise Ready', 'Community Picks'].map((collection, i) => (
              <motion.div
                key={i}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: i * 0.1 }}
              >
                <Card>
                  <div className="relative">
                    <div className="grid grid-cols-2 gap-1 h-32 overflow-hidden rounded-t-lg">
                      {items.slice(i * 4, (i * 4) + 4).map((item, j) => (
                        <img 
                          key={j}
                          src={item.content.thumbnails[0]} 
                          alt={item.name}
                          className="w-full h-full object-cover"
                        />
                      ))}
                    </div>
                    <div className="absolute top-2 right-2">
                      <Badge className="bg-black bg-opacity-75 text-white">
                        {Math.floor(Math.random() * 20) + 5} items
                      </Badge>
                    </div>
                  </div>
                  <CardContent className="p-4">
                    <div className="space-y-3">
                      <div>
                        <h3 className="font-semibold">{collection}</h3>
                        <p className="text-sm text-gray-600">Curated collection of top integrations</p>
                      </div>
                      <div className="flex items-center gap-4 text-sm text-gray-500">
                        <div className="flex items-center gap-1">
                          <Users className="h-3 w-3" />
                          {Math.floor(Math.random() * 1000) + 100} followers
                        </div>
                        <div className="flex items-center gap-1">
                          <Heart className="h-3 w-3" />
                          {Math.floor(Math.random() * 500) + 50} likes
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <img 
                          src={users[i % users.length].avatar} 
                          alt={users[i % users.length].displayName}
                          className="w-5 h-5 rounded-full"
                        />
                        <span className="text-sm text-gray-600">
                          by {users[i % users.length].displayName}
                        </span>
                      </div>
                      <Button size="sm" className="w-full">
                        <Eye className="h-3 w-3 mr-1" />
                        View Collection
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </TabsContent>

        {/* Publishers */}
        <TabsContent value="publishers" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {users.slice(0, 12).map((user) => (
              <motion.div
                key={user.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
              >
                <Card>
                  <CardContent className="p-6 text-center">
                    <div className="space-y-4">
                      <div>
                        <img 
                          src={user.avatar} 
                          alt={user.displayName}
                          className="w-16 h-16 rounded-full mx-auto mb-3"
                        />
                        <div className="flex items-center justify-center gap-2">
                          <h3 className="font-semibold">{user.displayName}</h3>
                          {user.verified && (
                            <Verified className="h-4 w-4 text-blue-500" />
                          )}
                          {user.pro && (
                            <Crown className="h-4 w-4 text-yellow-500" />
                          )}
                        </div>
                        <p className="text-sm text-gray-600">@{user.username}</p>
                      </div>
                      <p className="text-sm text-gray-600">{user.bio}</p>
                      <div className="grid grid-cols-3 gap-4 text-center">
                        <div>
                          <div className="font-semibold text-sm">{Math.floor(Math.random() * 50) + 5}</div>
                          <div className="text-xs text-gray-500">Items</div>
                        </div>
                        <div>
                          <div className="font-semibold text-sm">{user.stats.followers.toLocaleString()}</div>
                          <div className="text-xs text-gray-500">Followers</div>
                        </div>
                        <div>
                          <div className="font-semibold text-sm">{(3.5 + Math.random() * 1.5).toFixed(1)}</div>
                          <div className="text-xs text-gray-500">Rating</div>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button size="sm" className="flex-1">
                          <Users className="h-3 w-3 mr-1" />
                          Follow
                        </Button>
                        <Button variant="outline" size="sm">
                          <Eye className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </TabsContent>

        {/* Analytics */}
        <TabsContent value="analytics" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Items</p>
                    <p className="text-2xl font-bold">2,847</p>
                  </div>
                  <PackageIcon className="h-8 w-8 text-blue-500" />
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  <span className="text-green-600">↗ 12%</span> from last month
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Downloads</p>
                    <p className="text-2xl font-bold">1.2M</p>
                  </div>
                  <Download className="h-8 w-8 text-green-500" />
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  <span className="text-green-600">↗ 8%</span> from last week
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Active Publishers</p>
                    <p className="text-2xl font-bold">156</p>
                  </div>
                  <Users className="h-8 w-8 text-purple-500" />
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  <span className="text-green-600">+3</span> new this week
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Revenue</p>
                    <p className="text-2xl font-bold">$45.2K</p>
                  </div>
                  <TrendingUp className="h-8 w-8 text-orange-500" />
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  <span className="text-green-600">↗ 15%</span> from last month
                </p>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Marketplace Growth
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart data={marketplaceAnalyticsData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Area type="monotone" dataKey="downloads" stackId="1" stroke="#3b82f6" fill="#3b82f6" fillOpacity={0.6} name="Downloads" />
                      <Area type="monotone" dataKey="newItems" stackId="2" stroke="#10b981" fill="#10b981" fillOpacity={0.6} name="New Items" />
                    </AreaChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <PieChart className="h-5 w-5" />
                  Category Distribution
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <RechartsPieChart>
                      <Pie
                        data={categoryDistribution}
                        cx="50%"
                        cy="50%"
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                        label={({ name, value }) => `${name}: ${value}%`}
                      >
                        {categoryDistribution.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </RechartsPieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <LineChart className="h-5 w-5" />
                Revenue & User Growth
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <RechartsLineChart data={marketplaceAnalyticsData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Line type="monotone" dataKey="revenue" stroke="#10b981" strokeWidth={2} name="Revenue ($)" />
                    <Line type="monotone" dataKey="users" stroke="#8b5cf6" strokeWidth={2} name="Active Users" />
                  </RechartsLineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default IntegrationMarketplace;