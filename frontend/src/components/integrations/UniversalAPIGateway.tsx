/**
 * Universal API Gateway
 * Centralized integration management platform with advanced routing, security, and monitoring
 */

import React, { useState, useMemo, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { motion } from 'framer-motion';
import { 
  Network,
  Globe,
  Shield,
  Zap,
  Activity,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  Database,
  Server,
  Cloud,
  Lock,
  Unlock,
  Settings,
  Monitor,
  BarChart3,
  LineChart,
  PieChart,
  Target,
  Eye,
  Download,
  Upload,
  Share,
  RefreshCw,
  Play,
  Pause,
  Square,
  Filter,
  Search,
  Calendar,
  Users,
  Package,
  Tag,
  Hash,
  Layers,
  Grid,
  List,
  MoreHorizontal,
  ExternalLink,
  Info,
  Plus,
  Minus,
  Edit,
  Trash2,
  Copy,
  Bookmark,
  Star,
  Award,
  Lightbulb,
  Workflow,
  GitBranch,
  Code,
  Terminal,
  Gauge,
  Timer,
  HardDrive,
  MemoryStick,
  Wifi,
  WifiOff,
  Power,
  PowerOff,
  Thermometer,
  Wrench,
  Bug,
  CheckCheck,
  AlertCircle,
  Construction,
  Rocket,
  FlaskConical,
  Router,
  Cpu,
  Brain,
  Key,
  FileText,
  Archive,
  FolderOpen,
  Link,
  Unlink,
  MousePointer,
  Fingerprint,
  Smartphone,
  Laptop,
  Tablet,
  Watch
} from 'lucide-react';
import { 
  LineChart as RechartsLineChart, 
  Line, 
  AreaChart, 
  Area, 
  BarChart, 
  Bar, 
  ScatterChart,
  Scatter,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  Cell,
  PieChart as RechartsPieChart,
  Pie,
  RadialBarChart,
  RadialBar,
  ComposedChart,
  Treemap
} from 'recharts';

// Types
interface APIEndpoint {
  id: string;
  name: string;
  path: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  description: string;
  category: 'analytics' | 'orders' | 'customers' | 'products' | 'inventory' | 'webhooks' | 'auth';
  version: string;
  status: 'active' | 'deprecated' | 'beta' | 'maintenance';
  authentication: {
    type: 'api_key' | 'oauth2' | 'jwt' | 'basic' | 'bearer';
    required: boolean;
    scopes?: string[];
  };
  rateLimit: {
    requests: number;
    window: number; // seconds
    burst?: number;
  };
  caching: {
    enabled: boolean;
    ttl: number; // seconds
    strategy: 'memory' | 'redis' | 'cdn';
  };
  monitoring: {
    uptime: number;
    avgResponseTime: number;
    errorRate: number;
    requestCount: number;
    lastChecked: Date;
  };
  documentation: {
    summary: string;
    parameters: Parameter[];
    responses: Response[];
    examples: Example[];
  };
  routing: {
    upstream: string;
    loadBalancing: 'round_robin' | 'least_connections' | 'weighted' | 'ip_hash';
    healthCheck: {
      enabled: boolean;
      path: string;
      interval: number;
      timeout: number;
    };
    retries: {
      count: number;
      backoff: 'linear' | 'exponential';
      delay: number;
    };
  };
  security: {
    cors: {
      enabled: boolean;
      origins: string[];
      methods: string[];
      headers: string[];
    };
    rateLimiting: {
      enabled: boolean;
      strategy: 'fixed_window' | 'sliding_window' | 'token_bucket';
    };
    validation: {
      enabled: boolean;
      schema: any;
    };
    encryption: {
      enabled: boolean;
      algorithm: string;
    };
  };
  analytics: {
    requests24h: number;
    uniqueClients: number;
    averagePayloadSize: number;
    topUserAgents: { name: string; count: number }[];
    topIPs: { ip: string; count: number }[];
    errorBreakdown: { code: number; count: number }[];
  };
}

interface Parameter {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  required: boolean;
  description: string;
  example?: any;
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    enum?: string[];
  };
}

interface Response {
  code: number;
  description: string;
  schema: any;
  example: any;
}

interface Example {
  name: string;
  description: string;
  request: {
    headers?: Record<string, string>;
    body?: any;
    query?: Record<string, string>;
  };
  response: {
    status: number;
    headers?: Record<string, string>;
    body: any;
  };
}

interface APIGatewayMetrics {
  totalRequests: number;
  requestsPerSecond: number;
  averageLatency: number;
  errorRate: number;
  activeConnections: number;
  bandwidthUsage: number;
  cacheHitRate: number;
  uptime: number;
}

interface IntegrationClient {
  id: string;
  name: string;
  type: 'web_app' | 'mobile_app' | 'service' | 'webhook' | 'third_party';
  apiKey: string;
  status: 'active' | 'inactive' | 'suspended' | 'pending';
  permissions: string[];
  quotas: {
    requests: {
      limit: number;
      used: number;
      reset: Date;
    };
    bandwidth: {
      limit: number; // MB
      used: number;
    };
  };
  usage: {
    totalRequests: number;
    lastRequest: Date;
    topEndpoints: { endpoint: string; count: number }[];
    errorRate: number;
  };
  metadata: {
    createdAt: Date;
    lastActivity: Date;
    version: string;
    userAgent?: string;
    ipAddress?: string;
    country?: string;
  };
}

interface RouteRule {
  id: string;
  name: string;
  pattern: string;
  priority: number;
  conditions: {
    method?: string[];
    headers?: Record<string, string>;
    query?: Record<string, string>;
    ip?: string[];
    userAgent?: string;
  };
  actions: {
    route: {
      upstream: string;
      rewrite?: string;
      headers?: Record<string, string>;
    };
    rateLimit?: {
      requests: number;
      window: number;
    };
    cache?: {
      ttl: number;
      key?: string;
    };
    transform?: {
      request?: any;
      response?: any;
    };
  };
  status: 'active' | 'inactive';
  metrics: {
    matches: number;
    lastMatched: Date;
  };
}

interface SecurityPolicy {
  id: string;
  name: string;
  description: string;
  type: 'authentication' | 'authorization' | 'encryption' | 'validation' | 'rate_limiting';
  enabled: boolean;
  configuration: any;
  applicableEndpoints: string[];
  metrics: {
    blocked: number;
    allowed: number;
    lastTriggered: Date;
  };
}

// Mock data generators
const generateAPIEndpoints = (): APIEndpoint[] => {
  const categories: APIEndpoint['category'][] = ['analytics', 'orders', 'customers', 'products', 'inventory'];
  const methods: APIEndpoint['method'][] = ['GET', 'POST', 'PUT', 'DELETE'];
  const statuses: APIEndpoint['status'][] = ['active', 'beta', 'deprecated'];
  const authTypes: APIEndpoint['authentication']['type'][] = ['api_key', 'oauth2', 'jwt', 'bearer'];

  return Array.from({ length: 25 }, (_, index) => {
    const category = categories[Math.floor(Math.random() * categories.length)];
    const method = methods[Math.floor(Math.random() * methods.length)];
    const status = statuses[Math.floor(Math.random() * statuses.length)];
    const authType = authTypes[Math.floor(Math.random() * authTypes.length)];

    return {
      id: `EP-${String(index + 1).padStart(3, '0')}`,
      name: `${category.charAt(0).toUpperCase() + category.slice(1)} API`,
      path: `/api/v1/${category}/${index + 1}`,
      method,
      description: `API endpoint for ${category} operations`,
      category,
      version: `v1.${Math.floor(Math.random() * 10)}`,
      status,
      authentication: {
        type: authType,
        required: Math.random() > 0.2,
        scopes: authType === 'oauth2' ? ['read', 'write'] : undefined
      },
      rateLimit: {
        requests: Math.floor(Math.random() * 1000) + 100,
        window: 3600,
        burst: Math.floor(Math.random() * 50) + 10
      },
      caching: {
        enabled: Math.random() > 0.3,
        ttl: Math.floor(Math.random() * 3600) + 300,
        strategy: ['memory', 'redis', 'cdn'][Math.floor(Math.random() * 3)] as any
      },
      monitoring: {
        uptime: 0.95 + Math.random() * 0.05,
        avgResponseTime: 50 + Math.random() * 200,
        errorRate: Math.random() * 0.05,
        requestCount: Math.floor(Math.random() * 100000) + 10000,
        lastChecked: new Date()
      },
      documentation: {
        summary: `Comprehensive ${category} management endpoint`,
        parameters: generateParameters(),
        responses: generateResponses(),
        examples: generateExamples()
      },
      routing: {
        upstream: `https://${category}-service.internal:8080`,
        loadBalancing: ['round_robin', 'least_connections', 'weighted'][Math.floor(Math.random() * 3)] as any,
        healthCheck: {
          enabled: true,
          path: '/health',
          interval: 30,
          timeout: 5
        },
        retries: {
          count: 3,
          backoff: 'exponential',
          delay: 1000
        }
      },
      security: {
        cors: {
          enabled: true,
          origins: ['https://app.example.com', 'https://dashboard.example.com'],
          methods: ['GET', 'POST', 'PUT', 'DELETE'],
          headers: ['Content-Type', 'Authorization']
        },
        rateLimiting: {
          enabled: true,
          strategy: 'sliding_window'
        },
        validation: {
          enabled: true,
          schema: {}
        },
        encryption: {
          enabled: true,
          algorithm: 'AES-256-GCM'
        }
      },
      analytics: {
        requests24h: Math.floor(Math.random() * 50000) + 5000,
        uniqueClients: Math.floor(Math.random() * 1000) + 100,
        averagePayloadSize: Math.floor(Math.random() * 1000) + 100,
        topUserAgents: [
          { name: 'Mozilla/5.0', count: Math.floor(Math.random() * 1000) + 100 },
          { name: 'Chrome/91.0', count: Math.floor(Math.random() * 800) + 80 }
        ],
        topIPs: [
          { ip: '*************', count: Math.floor(Math.random() * 500) + 50 },
          { ip: '*********', count: Math.floor(Math.random() * 400) + 40 }
        ],
        errorBreakdown: [
          { code: 400, count: Math.floor(Math.random() * 100) + 10 },
          { code: 401, count: Math.floor(Math.random() * 50) + 5 },
          { code: 500, count: Math.floor(Math.random() * 20) + 2 }
        ]
      }
    };
  });
};

const generateParameters = (): Parameter[] => {
  return [
    {
      name: 'id',
      type: 'string',
      required: true,
      description: 'Unique identifier',
      example: 'abc123'
    },
    {
      name: 'limit',
      type: 'number',
      required: false,
      description: 'Number of results to return',
      example: 10,
      validation: { min: 1, max: 100 }
    },
    {
      name: 'filter',
      type: 'object',
      required: false,
      description: 'Filter criteria',
      example: { status: 'active' }
    }
  ];
};

const generateResponses = (): Response[] => {
  return [
    {
      code: 200,
      description: 'Success',
      schema: { type: 'object' },
      example: { success: true, data: {} }
    },
    {
      code: 400,
      description: 'Bad Request',
      schema: { type: 'object' },
      example: { error: 'Invalid parameters' }
    },
    {
      code: 401,
      description: 'Unauthorized',
      schema: { type: 'object' },
      example: { error: 'Authentication required' }
    }
  ];
};

const generateExamples = (): Example[] => {
  return [
    {
      name: 'Basic Request',
      description: 'Simple API call example',
      request: {
        headers: { 'Authorization': 'Bearer token123' },
        query: { limit: '10' }
      },
      response: {
        status: 200,
        body: { success: true, data: [] }
      }
    }
  ];
};

const generateIntegrationClients = (): IntegrationClient[] => {
  const types: IntegrationClient['type'][] = ['web_app', 'mobile_app', 'service', 'webhook'];
  const statuses: IntegrationClient['status'][] = ['active', 'inactive', 'suspended'];

  return Array.from({ length: 15 }, (_, index) => ({
    id: `CLIENT-${String(index + 1).padStart(3, '0')}`,
    name: `Client ${index + 1}`,
    type: types[Math.floor(Math.random() * types.length)],
    apiKey: `ak_${Math.random().toString(36).substring(2, 15)}`,
    status: statuses[Math.floor(Math.random() * statuses.length)],
    permissions: ['read:analytics', 'write:orders', 'read:customers'].slice(0, Math.floor(Math.random() * 3) + 1),
    quotas: {
      requests: {
        limit: Math.floor(Math.random() * 10000) + 1000,
        used: Math.floor(Math.random() * 5000),
        reset: new Date(Date.now() + 24 * 60 * 60 * 1000)
      },
      bandwidth: {
        limit: Math.floor(Math.random() * 1000) + 100,
        used: Math.floor(Math.random() * 500)
      }
    },
    usage: {
      totalRequests: Math.floor(Math.random() * 100000) + 10000,
      lastRequest: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000),
      topEndpoints: [
        { endpoint: '/api/v1/analytics', count: Math.floor(Math.random() * 1000) + 100 },
        { endpoint: '/api/v1/orders', count: Math.floor(Math.random() * 800) + 80 }
      ],
      errorRate: Math.random() * 0.05
    },
    metadata: {
      createdAt: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000),
      lastActivity: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000),
      version: `v1.${Math.floor(Math.random() * 10)}`,
      userAgent: 'MyApp/1.0',
      ipAddress: `192.168.1.${Math.floor(Math.random() * 255)}`,
      country: ['US', 'UK', 'CA', 'DE', 'FR'][Math.floor(Math.random() * 5)]
    }
  }));
};

const generateRouteRules = (): RouteRule[] => {
  return Array.from({ length: 10 }, (_, index) => ({
    id: `RULE-${String(index + 1).padStart(3, '0')}`,
    name: `Route Rule ${index + 1}`,
    pattern: `/api/v1/route${index + 1}/*`,
    priority: Math.floor(Math.random() * 100) + 1,
    conditions: {
      method: ['GET', 'POST'].slice(0, Math.floor(Math.random() * 2) + 1),
      headers: { 'X-Client-Type': 'mobile' }
    },
    actions: {
      route: {
        upstream: `https://service${index + 1}.internal:8080`,
        rewrite: `/v1/service${index + 1}`,
        headers: { 'X-Forwarded-For': '$remote_addr' }
      },
      rateLimit: {
        requests: Math.floor(Math.random() * 1000) + 100,
        window: 3600
      }
    },
    status: Math.random() > 0.2 ? 'active' : 'inactive',
    metrics: {
      matches: Math.floor(Math.random() * 10000) + 1000,
      lastMatched: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000)
    }
  }));
};

const generateSecurityPolicies = (): SecurityPolicy[] => {
  const types: SecurityPolicy['type'][] = ['authentication', 'authorization', 'encryption', 'validation', 'rate_limiting'];

  return Array.from({ length: 8 }, (_, index) => ({
    id: `POLICY-${String(index + 1).padStart(3, '0')}`,
    name: `Security Policy ${index + 1}`,
    description: `${types[index % types.length]} security policy`,
    type: types[index % types.length],
    enabled: Math.random() > 0.2,
    configuration: {
      strict: true,
      threshold: Math.floor(Math.random() * 100) + 10
    },
    applicableEndpoints: [`/api/v1/endpoint${index + 1}`, `/api/v1/endpoint${index + 2}`],
    metrics: {
      blocked: Math.floor(Math.random() * 1000) + 100,
      allowed: Math.floor(Math.random() * 10000) + 1000,
      lastTriggered: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000)
    }
  }));
};

// Components
const EndpointCard: React.FC<{ endpoint: APIEndpoint }> = ({ endpoint }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'beta': return 'bg-blue-100 text-blue-800';
      case 'deprecated': return 'bg-red-100 text-red-800';
      case 'maintenance': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getMethodColor = (method: string) => {
    switch (method) {
      case 'GET': return 'bg-blue-100 text-blue-800';
      case 'POST': return 'bg-green-100 text-green-800';
      case 'PUT': return 'bg-yellow-100 text-yellow-800';
      case 'DELETE': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className="border rounded-lg p-4 hover:shadow-md transition-shadow"
    >
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-2">
          <Network className="h-4 w-4 text-blue-600" />
          <div>
            <h4 className="font-medium">{endpoint.name}</h4>
            <p className="text-sm text-gray-600">{endpoint.path}</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Badge className={getMethodColor(endpoint.method)}>
            {endpoint.method}
          </Badge>
          <Badge className={getStatusColor(endpoint.status)}>
            {endpoint.status}
          </Badge>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4 mb-3">
        <div>
          <p className="text-sm text-gray-600">Uptime</p>
          <p className="text-lg font-semibold">{(endpoint.monitoring.uptime * 100).toFixed(1)}%</p>
        </div>
        <div>
          <p className="text-sm text-gray-600">Avg Response</p>
          <p className="text-lg font-semibold">{endpoint.monitoring.avgResponseTime.toFixed(0)}ms</p>
        </div>
        <div>
          <p className="text-sm text-gray-600">Requests/24h</p>
          <p className="text-lg font-semibold">{endpoint.analytics.requests24h.toLocaleString()}</p>
        </div>
        <div>
          <p className="text-sm text-gray-600">Error Rate</p>
          <p className="text-lg font-semibold">{(endpoint.monitoring.errorRate * 100).toFixed(2)}%</p>
        </div>
      </div>

      <div className="mb-3">
        <div className="flex items-center justify-between text-sm mb-1">
          <span className="text-gray-600">Rate Limit Usage</span>
          <span>{Math.floor(Math.random() * endpoint.rateLimit.requests)}/{endpoint.rateLimit.requests}</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-blue-600 h-2 rounded-full"
            style={{ width: `${Math.random() * 80 + 10}%` }}
          />
        </div>
      </div>

      <div className="flex items-center justify-between text-sm">
        <div className="flex items-center gap-2">
          {endpoint.authentication.required && <Lock className="h-3 w-3" />}
          {endpoint.caching.enabled && <Database className="h-3 w-3" />}
          <span className="text-gray-600">{endpoint.version}</span>
        </div>
        <span className="text-gray-600">{endpoint.analytics.uniqueClients} clients</span>
      </div>
    </motion.div>
  );
};

const ClientCard: React.FC<{ client: IntegrationClient }> = ({ client }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      case 'suspended': return 'bg-red-100 text-red-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'web_app': return <Globe className="h-4 w-4" />;
      case 'mobile_app': return <Smartphone className="h-4 w-4" />;
      case 'service': return <Server className="h-4 w-4" />;
      case 'webhook': return <Webhook className="h-4 w-4" />;
      default: return <Package className="h-4 w-4" />;
    }
  };

  const quotaUsagePercent = (client.quotas.requests.used / client.quotas.requests.limit) * 100;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="border rounded-lg p-4 hover:shadow-md transition-shadow"
    >
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-2">
          {getTypeIcon(client.type)}
          <div>
            <h4 className="font-medium">{client.name}</h4>
            <p className="text-sm text-gray-600 font-mono">{client.apiKey.substring(0, 12)}...</p>
          </div>
        </div>
        <Badge className={getStatusColor(client.status)}>
          {client.status}
        </Badge>
      </div>

      <div className="grid grid-cols-2 gap-4 mb-3">
        <div>
          <p className="text-sm text-gray-600">Total Requests</p>
          <p className="text-lg font-semibold">{client.usage.totalRequests.toLocaleString()}</p>
        </div>
        <div>
          <p className="text-sm text-gray-600">Error Rate</p>
          <p className="text-lg font-semibold">{(client.usage.errorRate * 100).toFixed(2)}%</p>
        </div>
      </div>

      <div className="mb-3">
        <div className="flex items-center justify-between text-sm mb-1">
          <span className="text-gray-600">Quota Usage</span>
          <span>{client.quotas.requests.used.toLocaleString()}/{client.quotas.requests.limit.toLocaleString()}</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className={`h-2 rounded-full ${quotaUsagePercent > 90 ? 'bg-red-600' : quotaUsagePercent > 70 ? 'bg-yellow-600' : 'bg-green-600'}`}
            style={{ width: `${Math.min(quotaUsagePercent, 100)}%` }}
          />
        </div>
      </div>

      <div className="mb-3">
        <p className="text-sm text-gray-600 mb-1">Permissions</p>
        <div className="flex flex-wrap gap-1">
          {client.permissions.map((permission, index) => (
            <Badge key={index} variant="outline" className="text-xs">
              {permission}
            </Badge>
          ))}
        </div>
      </div>

      <div className="flex items-center justify-between text-sm">
        <span className="text-gray-600">Last activity: {client.usage.lastRequest.toLocaleDateString()}</span>
        <span className="text-gray-600">{client.metadata.country}</span>
      </div>
    </motion.div>
  );
};

const RouteRuleCard: React.FC<{ rule: RouteRule }> = ({ rule }) => {
  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      className="border rounded-lg p-4 hover:shadow-md transition-shadow"
    >
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-2">
          <Router className="h-4 w-4 text-purple-600" />
          <div>
            <h4 className="font-medium">{rule.name}</h4>
            <p className="text-sm text-gray-600 font-mono">{rule.pattern}</p>
          </div>
        </div>
        <Badge variant={rule.status === 'active' ? 'default' : 'secondary'}>
          {rule.status}
        </Badge>
      </div>

      <div className="grid grid-cols-2 gap-4 mb-3">
        <div>
          <p className="text-sm text-gray-600">Priority</p>
          <p className="text-lg font-semibold">{rule.priority}</p>
        </div>
        <div>
          <p className="text-sm text-gray-600">Matches</p>
          <p className="text-lg font-semibold">{rule.metrics.matches.toLocaleString()}</p>
        </div>
      </div>

      <div className="mb-3">
        <p className="text-sm text-gray-600 mb-1">Upstream</p>
        <p className="text-sm font-mono bg-gray-50 p-2 rounded">{rule.actions.route.upstream}</p>
      </div>

      <div className="flex items-center justify-between text-sm">
        <span className="text-gray-600">
          Methods: {rule.conditions.method?.join(', ') || 'ALL'}
        </span>
        <span className="text-gray-600">
          Last match: {rule.metrics.lastMatched.toLocaleDateString()}
        </span>
      </div>
    </motion.div>
  );
};

const Webhook: React.FC = () => <Network className="h-4 w-4" />;

export const UniversalAPIGateway: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'name' | 'requests' | 'latency' | 'uptime'>('name');

  const endpoints = useMemo(() => generateAPIEndpoints(), []);
  const clients = useMemo(() => generateIntegrationClients(), []);
  const routeRules = useMemo(() => generateRouteRules(), []);
  const securityPolicies = useMemo(() => generateSecurityPolicies(), []);

  const filteredEndpoints = useMemo(() => {
    return endpoints
      .filter(ep => selectedCategory === 'all' || ep.category === selectedCategory)
      .filter(ep => selectedStatus === 'all' || ep.status === selectedStatus)
      .filter(ep => ep.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                   ep.path.toLowerCase().includes(searchQuery.toLowerCase()))
      .sort((a, b) => {
        switch (sortBy) {
          case 'name': return a.name.localeCompare(b.name);
          case 'requests': return b.analytics.requests24h - a.analytics.requests24h;
          case 'latency': return a.monitoring.avgResponseTime - b.monitoring.avgResponseTime;
          case 'uptime': return b.monitoring.uptime - a.monitoring.uptime;
          default: return 0;
        }
      });
  }, [endpoints, selectedCategory, selectedStatus, searchQuery, sortBy]);

  const gatewayMetrics = useMemo(() => {
    const totalRequests = endpoints.reduce((sum, ep) => sum + ep.analytics.requests24h, 0);
    const avgLatency = endpoints.reduce((sum, ep) => sum + ep.monitoring.avgResponseTime, 0) / endpoints.length;
    const errorRate = endpoints.reduce((sum, ep) => sum + ep.monitoring.errorRate, 0) / endpoints.length;
    const avgUptime = endpoints.reduce((sum, ep) => sum + ep.monitoring.uptime, 0) / endpoints.length;

    return {
      totalRequests,
      requestsPerSecond: totalRequests / (24 * 60 * 60),
      averageLatency: avgLatency,
      errorRate,
      activeConnections: Math.floor(Math.random() * 1000) + 500,
      bandwidthUsage: Math.floor(Math.random() * 100) + 50, // GB
      cacheHitRate: 0.85 + Math.random() * 0.1,
      uptime: avgUptime
    };
  }, [endpoints]);

  const requestTrendData = useMemo(() => {
    return Array.from({ length: 24 }, (_, i) => ({
      hour: i,
      requests: Math.floor(Math.random() * 5000) + 2000,
      latency: 50 + Math.random() * 100,
      errors: Math.floor(Math.random() * 100) + 10,
      cache_hits: Math.floor(Math.random() * 3000) + 1000
    }));
  }, []);

  const endpointDistributionData = useMemo(() => {
    const categories = endpoints.reduce((acc, ep) => {
      acc[ep.category] = (acc[ep.category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(categories).map(([category, count]) => ({
      name: category,
      value: count,
      percentage: ((count / endpoints.length) * 100).toFixed(1)
    }));
  }, [endpoints]);

  const topEndpointsData = useMemo(() => {
    return endpoints
      .sort((a, b) => b.analytics.requests24h - a.analytics.requests24h)
      .slice(0, 10)
      .map(ep => ({
        name: ep.name,
        requests: ep.analytics.requests24h,
        latency: ep.monitoring.avgResponseTime,
        uptime: ep.monitoring.uptime * 100
      }));
  }, [endpoints]);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Network className="h-6 w-6" />
            Universal API Gateway
          </h2>
          <p className="text-gray-600">Centralized integration management with advanced routing and security</p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Plus className="h-4 w-4 mr-2" />
            Add Endpoint
          </Button>
          <Button size="sm">
            <Settings className="h-4 w-4 mr-2" />
            Configure
          </Button>
        </div>
      </div>

      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="endpoints">Endpoints</TabsTrigger>
          <TabsTrigger value="clients">Clients</TabsTrigger>
          <TabsTrigger value="routing">Routing</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
          <TabsTrigger value="monitoring">Monitoring</TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <div className="space-y-6">
            {/* Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Activity className="h-4 w-4 text-blue-600" />
                    <div>
                      <p className="text-sm text-gray-600">Total Requests</p>
                      <p className="text-2xl font-bold">{gatewayMetrics.totalRequests.toLocaleString()}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Zap className="h-4 w-4 text-green-600" />
                    <div>
                      <p className="text-sm text-gray-600">Avg Latency</p>
                      <p className="text-2xl font-bold">{gatewayMetrics.averageLatency.toFixed(0)}ms</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <div>
                      <p className="text-sm text-gray-600">Uptime</p>
                      <p className="text-2xl font-bold">{(gatewayMetrics.uptime * 100).toFixed(1)}%</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <AlertTriangle className="h-4 w-4 text-red-600" />
                    <div>
                      <p className="text-sm text-gray-600">Error Rate</p>
                      <p className="text-2xl font-bold">{(gatewayMetrics.errorRate * 100).toFixed(2)}%</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Charts */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Request Volume & Latency</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <ComposedChart data={requestTrendData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="hour" />
                      <YAxis yAxisId="left" />
                      <YAxis yAxisId="right" orientation="right" />
                      <Tooltip />
                      <Legend />
                      <Bar yAxisId="left" dataKey="requests" fill="#8884d8" name="Requests" />
                      <Line yAxisId="right" type="monotone" dataKey="latency" stroke="#ff7300" name="Latency (ms)" />
                    </ComposedChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Endpoint Distribution</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <RechartsPieChart>
                      <Pie
                        data={endpointDistributionData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percentage }) => `${name}: ${percentage}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {endpointDistributionData.map((entry, index) => (
                          <Cell
                            key={`cell-${index}`}
                            fill={['#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#8dd1e1'][index % 5]}
                          />
                        ))}
                      </Pie>
                      <Tooltip />
                    </RechartsPieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>

            {/* Top Endpoints */}
            <Card>
              <CardHeader>
                <CardTitle>Top Performing Endpoints</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left p-2">Endpoint</th>
                        <th className="text-left p-2">Requests</th>
                        <th className="text-left p-2">Latency</th>
                        <th className="text-left p-2">Uptime</th>
                      </tr>
                    </thead>
                    <tbody>
                      {topEndpointsData.map((endpoint, index) => (
                        <motion.tr
                          key={index}
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          transition={{ delay: index * 0.1 }}
                          className="border-b hover:bg-gray-50"
                        >
                          <td className="p-2 font-medium">{endpoint.name}</td>
                          <td className="p-2">{endpoint.requests.toLocaleString()}</td>
                          <td className="p-2">{endpoint.latency.toFixed(0)}ms</td>
                          <td className="p-2">{endpoint.uptime.toFixed(1)}%</td>
                        </motion.tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="endpoints">
          <div className="space-y-6">
            {/* Filters */}
            <div className="flex items-center gap-4">
              <div className="flex-1">
                <Input
                  placeholder="Search endpoints..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full"
                />
              </div>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-3 py-2 border rounded-md"
              >
                <option value="all">All Categories</option>
                <option value="analytics">Analytics</option>
                <option value="orders">Orders</option>
                <option value="customers">Customers</option>
                <option value="products">Products</option>
                <option value="inventory">Inventory</option>
              </select>
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="px-3 py-2 border rounded-md"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="beta">Beta</option>
                <option value="deprecated">Deprecated</option>
              </select>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="px-3 py-2 border rounded-md"
              >
                <option value="name">Sort by Name</option>
                <option value="requests">Sort by Requests</option>
                <option value="latency">Sort by Latency</option>
                <option value="uptime">Sort by Uptime</option>
              </select>
            </div>

            {/* Endpoints Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredEndpoints.map((endpoint) => (
                <EndpointCard key={endpoint.id} endpoint={endpoint} />
              ))}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="clients">
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Integration Clients</h3>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Client
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {clients.map((client) => (
                <ClientCard key={client.id} client={client} />
              ))}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="routing">
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Routing Rules</h3>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Rule
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {routeRules.map((rule) => (
                <RouteRuleCard key={rule.id} rule={rule} />
              ))}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="security">
          <div className="space-y-6">
            <h3 className="text-lg font-semibold">Security Policies</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {securityPolicies.map((policy) => (
                <motion.div
                  key={policy.id}
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                >
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Shield className="h-5 w-5" />
                          {policy.name}
                        </div>
                        <Badge variant={policy.enabled ? 'default' : 'secondary'}>
                          {policy.enabled ? 'Enabled' : 'Disabled'}
                        </Badge>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-gray-600 mb-4">{policy.description}</p>
                      
                      <div className="grid grid-cols-2 gap-4 mb-4">
                        <div>
                          <p className="text-sm text-gray-600">Blocked</p>
                          <p className="text-lg font-semibold text-red-600">{policy.metrics.blocked.toLocaleString()}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">Allowed</p>
                          <p className="text-lg font-semibold text-green-600">{policy.metrics.allowed.toLocaleString()}</p>
                        </div>
                      </div>

                      <div className="mb-3">
                        <p className="text-sm text-gray-600 mb-1">Applicable Endpoints</p>
                        <div className="flex flex-wrap gap-1">
                          {policy.applicableEndpoints.slice(0, 3).map((endpoint, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {endpoint}
                            </Badge>
                          ))}
                          {policy.applicableEndpoints.length > 3 && (
                            <Badge variant="outline" className="text-xs">
                              +{policy.applicableEndpoints.length - 3} more
                            </Badge>
                          )}
                        </div>
                      </div>

                      <div className="text-sm text-gray-600">
                        Last triggered: {policy.metrics.lastTriggered.toLocaleTimeString()}
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="monitoring">
          <div className="space-y-6">
            <h3 className="text-lg font-semibold">Real-time Monitoring</h3>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Cache Performance</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <AreaChart data={requestTrendData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="hour" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Area type="monotone" dataKey="cache_hits" stroke="#82ca9d" fill="#82ca9d" name="Cache Hits" />
                      <Area type="monotone" dataKey="requests" stroke="#8884d8" fill="#8884d8" name="Total Requests" />
                    </AreaChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Error Trends</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <LineChart data={requestTrendData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="hour" />
                      <YAxis />
                      <Tooltip />
                      <Line type="monotone" dataKey="errors" stroke="#ef4444" name="Errors" />
                    </LineChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>

            {/* Real-time Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Network className="h-4 w-4 text-blue-600" />
                    <div>
                      <p className="text-sm text-gray-600">Active Connections</p>
                      <p className="text-2xl font-bold">{gatewayMetrics.activeConnections}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <HardDrive className="h-4 w-4 text-green-600" />
                    <div>
                      <p className="text-sm text-gray-600">Bandwidth Usage</p>
                      <p className="text-2xl font-bold">{gatewayMetrics.bandwidthUsage} GB</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Database className="h-4 w-4 text-purple-600" />
                    <div>
                      <p className="text-sm text-gray-600">Cache Hit Rate</p>
                      <p className="text-2xl font-bold">{(gatewayMetrics.cacheHitRate * 100).toFixed(1)}%</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default UniversalAPIGateway;