/**
 * Data Storytelling Platform
 * Narrative analytics, interactive data stories, and guided insights
 */

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Activity,
  BarChart3,
  Bookmark,
  BookOpen,
  Brain,
  Clock,
  Crown,
  Edit,
  Eye,
  FileText,
  Lightbulb,
  MoreHorizontal,
  PieChart,
  Play,
  PlayCircle,
  Plus,
  Presentation,
  Save,
  Settings,
  Share,
  Star,
  Target,
  ThumbsUp,
  TrendingUp,
  Upload,
  Video,
  Volume2,
  Zap
} from 'lucide-react';
import { 
  <PERSON><PERSON>hart as RechartsLine<PERSON>hart, 
  Line,
  AreaChart, 
  BarChart, 
  Bar, 
  <PERSON><PERSON> as Recha<PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>, 
  <PERSON><PERSON>s, 
  CartesianG<PERSON>, 
  <PERSON><PERSON><PERSON>, 
  <PERSON>, 
  ResponsiveContainer,
  Composed<PERSON><PERSON>,
  <PERSON>att<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>
} from 'recharts';

// Types for Data Storytelling Platform
interface DataStory {
  id: string;
  title: string;
  subtitle?: string;
  description: string;
  author: {
    id: string;
    name: string;
    avatar: string;
    title: string;
  };
  category: 'performance' | 'trends' | 'insights' | 'forecast' | 'comparison' | 'deep_dive';
  audience: 'executives' | 'analysts' | 'stakeholders' | 'public' | 'team';
  format: 'interactive' | 'presentation' | 'article' | 'video' | 'audio';
  chapters: StoryChapter[];
  duration: number; // in minutes
  complexity: 'beginner' | 'intermediate' | 'advanced';
  tags: string[];
  views: number;
  likes: number;
  shares: number;
  bookmarks: number;
  comments: number;
  publishedAt: Date;
  updatedAt: Date;
  version: string;
  isPublished: boolean;
  isPromoted: boolean;
  analyticsData: AnalyticsSnapshot;
}

interface StoryChapter {
  id: string;
  title: string;
  content: StoryContent[];
  duration: number;
  order: number;
  insights: string[];
  interactiveElements: InteractiveElement[];
}

interface StoryContent {
  id: string;
  type: 'text' | 'chart' | 'image' | 'video' | 'audio' | 'interactive' | 'insight' | 'data_point';
  content: any;
  order: number;
  animation?: AnimationSettings;
  narration?: NarrationSettings;
}

interface InteractiveElement {
  id: string;
  type: 'filter' | 'drill_down' | 'comparison' | 'scenario' | 'what_if';
  config: any;
  position: { x: number; y: number };
}

interface AnimationSettings {
  type: 'fade' | 'slide' | 'zoom' | 'highlight' | 'typewriter';
  duration: number;
  delay: number;
  easing: string;
}

interface NarrationSettings {
  hasAudio: boolean;
  transcript: string;
  timing: number[];
  voice: 'male' | 'female' | 'neutral';
  speed: number;
}

interface AnalyticsSnapshot {
  revenue: number;
  growth: number;
  conversions: number;
  engagement: number;
  trends: TrendData[];
  insights: InsightData[];
}

interface TrendData {
  metric: string;
  value: number;
  change: number;
  period: string;
  forecast?: number;
}

interface InsightData {
  id: string;
  title: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  confidence: number;
  actionable: boolean;
  category: string;
}

interface StoryTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedTime: number;
  components: TemplateComponent[];
  preview: string;
  tags: string[];
  usage: number;
  rating: number;
}

interface TemplateComponent {
  id: string;
  type: string;
  config: any;
  position: { x: number; y: number; width: number; height: number };
}

const DataStorytellingPlatform: React.FC = () => {
  const [activeTab, setActiveTab] = useState('stories');
  const [selectedStory, setSelectedStory] = useState<DataStory | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedAudience, setSelectedAudience] = useState<string>('all');
  const [selectedFormat, setSelectedFormat] = useState<string>('all');
  const [isCreating, setIsCreating] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<StoryTemplate | null>(null);

  // Mock data for demonstration
  const sampleStories: DataStory[] = [
    {
      id: '1',
      title: 'Q3 Revenue Growth Story',
      subtitle: 'Uncovering the drivers behind our 35% growth',
      description: 'An interactive exploration of our Q3 performance, highlighting key growth drivers, customer segments, and market opportunities.',
      author: {
        id: 'author1',
        name: 'Sarah Chen',
        avatar: '/avatars/sarah.jpg',
        title: 'Senior Data Analyst'
      },
      category: 'performance',
      audience: 'executives',
      format: 'interactive',
      chapters: [],
      duration: 15,
      complexity: 'intermediate',
      tags: ['revenue', 'growth', 'quarterly', 'executive'],
      views: 1247,
      likes: 89,
      shares: 23,
      bookmarks: 45,
      comments: 12,
      publishedAt: new Date('2024-10-15'),
      updatedAt: new Date('2024-10-20'),
      version: '1.2',
      isPublished: true,
      isPromoted: true,
      analyticsData: {
        revenue: 2450000,
        growth: 35.2,
        conversions: 1250,
        engagement: 78.5,
        trends: [],
        insights: []
      }
    },
    {
      id: '2',
      title: 'Customer Journey Analytics',
      subtitle: 'From awareness to advocacy - mapping the complete experience',
      description: 'A comprehensive analysis of customer touchpoints, conversion funnels, and optimization opportunities across the entire journey.',
      author: {
        id: 'author2',
        name: 'Michael Rodriguez',
        avatar: '/avatars/michael.jpg',
        title: 'Customer Analytics Lead'
      },
      category: 'insights',
      audience: 'analysts',
      format: 'presentation',
      chapters: [],
      duration: 25,
      complexity: 'advanced',
      tags: ['customer-journey', 'conversion', 'funnel', 'optimization'],
      views: 892,
      likes: 156,
      shares: 67,
      bookmarks: 89,
      comments: 34,
      publishedAt: new Date('2024-10-18'),
      updatedAt: new Date('2024-10-18'),
      version: '1.0',
      isPublished: true,
      isPromoted: false,
      analyticsData: {
        revenue: 1980000,
        growth: 28.7,
        conversions: 2340,
        engagement: 82.1,
        trends: [],
        insights: []
      }
    },
    {
      id: '3',
      title: 'Market Trend Predictions',
      subtitle: 'What the data tells us about next quarter',
      description: 'Using advanced analytics and machine learning to forecast market trends and identify emerging opportunities.',
      author: {
        id: 'author3',
        name: 'Dr. Lisa Park',
        avatar: '/avatars/lisa.jpg',
        title: 'Chief Data Scientist'
      },
      category: 'forecast',
      audience: 'stakeholders',
      format: 'article',
      chapters: [],
      duration: 20,
      complexity: 'advanced',
      tags: ['forecasting', 'ml', 'trends', 'predictions'],
      views: 2103,
      likes: 234,
      shares: 89,
      bookmarks: 156,
      comments: 67,
      publishedAt: new Date('2024-10-22'),
      updatedAt: new Date('2024-10-23'),
      version: '1.1',
      isPublished: true,
      isPromoted: true,
      analyticsData: {
        revenue: 3200000,
        growth: 42.1,
        conversions: 1890,
        engagement: 91.3,
        trends: [],
        insights: []
      }
    }
  ];

  const storyTemplates: StoryTemplate[] = [
    {
      id: 'template1',
      name: 'Executive Summary',
      description: 'High-level overview with key metrics and insights for executive audiences',
      category: 'Performance',
      difficulty: 'beginner',
      estimatedTime: 10,
      components: [],
      preview: '/templates/executive-summary.png',
      tags: ['executive', 'summary', 'kpi'],
      usage: 234,
      rating: 4.8
    },
    {
      id: 'template2',
      name: 'Deep Dive Analysis',
      description: 'Comprehensive analytical story with detailed breakdowns and insights',
      category: 'Analysis',
      difficulty: 'advanced',
      estimatedTime: 45,
      components: [],
      preview: '/templates/deep-dive.png',
      tags: ['analysis', 'detailed', 'insights'],
      usage: 156,
      rating: 4.6
    },
    {
      id: 'template3',
      name: 'Trend Forecast',
      description: 'Predictive analysis template with forecasting and scenario modeling',
      category: 'Forecasting',
      difficulty: 'intermediate',
      estimatedTime: 30,
      components: [],
      preview: '/templates/forecast.png',
      tags: ['forecast', 'trends', 'predictions'],
      usage: 189,
      rating: 4.7
    }
  ];

  // Filter stories based on search and filters
  const filteredStories = useMemo(() => {
    return sampleStories.filter(story => {
      const matchesSearch = !searchQuery || 
        story.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        story.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        story.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
      
      const matchesCategory = selectedCategory === 'all' || story.category === selectedCategory;
      const matchesAudience = selectedAudience === 'all' || story.audience === selectedAudience;
      const matchesFormat = selectedFormat === 'all' || story.format === selectedFormat;
      
      return matchesSearch && matchesCategory && matchesAudience && matchesFormat;
    });
  }, [sampleStories, searchQuery, selectedCategory, selectedAudience, selectedFormat]);

  // Get category colors
  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'performance': return 'bg-blue-100 text-blue-800';
      case 'trends': return 'bg-green-100 text-green-800';
      case 'insights': return 'bg-purple-100 text-purple-800';
      case 'forecast': return 'bg-orange-100 text-orange-800';
      case 'comparison': return 'bg-pink-100 text-pink-800';
      case 'deep_dive': return 'bg-indigo-100 text-indigo-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // Get format icon
  const getFormatIcon = (format: string) => {
    switch (format) {
      case 'interactive': return <Play className="h-4 w-4" />;
      case 'presentation': return <Presentation className="h-4 w-4" />;
      case 'article': return <FileText className="h-4 w-4" />;
      case 'video': return <Video className="h-4 w-4" />;
      case 'audio': return <Volume2 className="h-4 w-4" />;
      default: return <BookOpen className="h-4 w-4" />;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 p-6">
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold text-gray-900">Data Storytelling Platform</h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Transform your data into compelling narratives that drive action and insight
          </p>
          <div className="flex justify-center gap-4">
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Create New Story
            </Button>
            <Button variant="outline" className="flex items-center gap-2">
              <Upload className="h-4 w-4" />
              Import Data
            </Button>
          </div>
        </div>

        {/* Main Content */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-5 w-full">
            <TabsTrigger value="stories" className="flex items-center gap-2">
              <BookOpen className="h-4 w-4" />
              Stories
            </TabsTrigger>
            <TabsTrigger value="create" className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Create
            </TabsTrigger>
            <TabsTrigger value="templates" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Templates
            </TabsTrigger>
            <TabsTrigger value="insights" className="flex items-center gap-2">
              <Lightbulb className="h-4 w-4" />
              AI Insights
            </TabsTrigger>
            <TabsTrigger value="analytics" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Analytics
            </TabsTrigger>
          </TabsList>

          {/* Stories Tab */}
          <TabsContent value="stories" className="space-y-6">
            {/* Search and Filters */}
            <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
              <div className="flex-1 max-w-md">
                <Input
                  placeholder="Search stories, tags, or authors..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full"
                />
              </div>
              <div className="flex gap-2">
                <select 
                  value={selectedCategory} 
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="px-3 py-2 border rounded-md"
                >
                  <option value="all">All Categories</option>
                  <option value="performance">Performance</option>
                  <option value="trends">Trends</option>
                  <option value="insights">Insights</option>
                  <option value="forecast">Forecast</option>
                  <option value="comparison">Comparison</option>
                  <option value="deep_dive">Deep Dive</option>
                </select>
                <select 
                  value={selectedAudience} 
                  onChange={(e) => setSelectedAudience(e.target.value)}
                  className="px-3 py-2 border rounded-md"
                >
                  <option value="all">All Audiences</option>
                  <option value="executives">Executives</option>
                  <option value="analysts">Analysts</option>
                  <option value="stakeholders">Stakeholders</option>
                  <option value="public">Public</option>
                  <option value="team">Team</option>
                </select>
              </div>
            </div>

            {/* Featured Story */}
            {filteredStories.length > 0 && (
              <div className="mb-8">
                <h2 className="text-2xl font-semibold mb-4 flex items-center gap-2">
                  <Crown className="h-6 w-6 text-yellow-500" />
                  Featured Story
                </h2>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  <Card className="h-full">
                    <CardHeader className="pb-4">
                      <div className="flex items-start justify-between">
                        <div className="flex items-center gap-3">
                          <BookOpen className="h-12 w-12 mx-auto mb-4" />
                          <div>
                            <CardTitle className="text-2xl mb-2">{filteredStories[0].title}</CardTitle>
                            <p className="text-gray-600 mb-4">{filteredStories[0].subtitle}</p>
                          </div>
                        </div>
                        <Badge className="bg-yellow-100 text-yellow-800">
                          <Crown className="h-3 w-3 mr-1" />
                          Featured
                        </Badge>
                      </div>
                      <p className="text-gray-700 mb-4">{filteredStories[0].description}</p>
                    </CardHeader>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center gap-4 text-sm text-gray-500">
                          <div className="flex items-center gap-1">
                            <Eye className="h-3 w-3" />
                            {filteredStories[0].views}
                          </div>
                          <div className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            {filteredStories[0].duration}m
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge className={getCategoryColor(filteredStories[0].category)}>
                            {filteredStories[0].category}
                          </Badge>
                          {filteredStories[0].isPromoted && (
                            <Badge className="bg-red-100 text-red-800">
                              <TrendingUp className="h-3 w-3 mr-1" />
                              Trending
                            </Badge>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center justify-between">
                        <Button size="sm" className="flex-1">
                          <PlayCircle className="h-3 w-3 mr-1" />
                          View Story
                        </Button>
                        <Button variant="outline" size="sm">
                          <Bookmark className="h-3 w-3" />
                        </Button>
                        <Button variant="outline" size="sm">
                          <Share className="h-3 w-3" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              </div>
            )}

            {/* Stories Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredStories.slice(1).map((story, index) => (
                <motion.div
                  key={story.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                >
                  <Card className="h-full flex flex-col">
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center gap-2">
                          {getFormatIcon(story.format)}
                          <CardTitle className="text-lg">{story.title}</CardTitle>
                        </div>
                        <Badge className={getCategoryColor(story.category)}>
                          {story.category}
                        </Badge>
                      </div>
                      {story.isPromoted && (
                        <Badge className="bg-red-100 text-red-800">
                          <TrendingUp className="h-3 w-3 mr-1" />
                          Trending
                        </Badge>
                      )}
                      <p className="text-sm text-gray-600">{story.subtitle}</p>
                    </CardHeader>
                    <CardContent className="p-4 flex-1 flex flex-col">
                      <p className="text-sm text-gray-700 mb-4 flex-1">{story.description}</p>
                      <div className="flex items-center justify-between mb-4 text-xs text-gray-500">
                        <div className="flex items-center gap-3">
                          <div className="flex items-center gap-1">
                            <Eye className="h-3 w-3" />
                            {story.views}
                          </div>
                          <div className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            {story.duration}m
                          </div>
                          <div className="flex items-center gap-1">
                            <ThumbsUp className="h-3 w-3" />
                            {story.likes}
                          </div>
                        </div>
                      </div>
                      <div className="mb-4">
                        <div className="flex flex-wrap gap-1">
                          {story.tags.slice(0, 3).map((tag) => (
                            <Badge key={tag} variant="secondary" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button size="sm" className="flex-1">
                          <PlayCircle className="h-3 w-3 mr-1" />
                          View
                        </Button>
                        <Button variant="outline" size="sm">
                          <MoreHorizontal className="h-3 w-3" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>

            {filteredStories.length === 0 && (
              <div className="text-center py-12">
                <BookOpen className="h-16 w-16 mx-auto text-gray-400 mb-4" />
                <h3 className="text-xl font-medium text-gray-900 mb-2">No stories found</h3>
                <p className="text-gray-600 mb-4">Try adjusting your search or filters</p>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Your First Story
                </Button>
              </div>
            )}
          </TabsContent>

          {/* Create Tab */}
          <TabsContent value="create" className="space-y-6">
            <div className="text-center space-y-4">
              <h2 className="text-3xl font-bold">Create a New Data Story</h2>
              <p className="text-gray-600 max-w-2xl mx-auto">
                Choose from our templates or start from scratch to create compelling data narratives
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <Card className="cursor-pointer hover:shadow-lg transition-shadow">
                <CardContent className="p-6 text-center">
                  <Plus className="h-12 w-12 mx-auto mb-4 text-blue-500" />
                  <h3 className="text-lg font-medium mb-2">Start from Scratch</h3>
                  <p className="text-sm text-gray-600 mb-4">
                    Build your story from the ground up with our intuitive editor
                  </p>
                  <Button className="w-full">Start Creating</Button>
                </CardContent>
              </Card>

              <Card className="cursor-pointer hover:shadow-lg transition-shadow">
                <CardContent className="p-6 text-center">
                  <FileText className="h-12 w-12 mx-auto mb-4 text-green-500" />
                  <h3 className="text-lg font-medium mb-2">Use Template</h3>
                  <p className="text-sm text-gray-600 mb-4">
                    Start with a proven template and customize to your needs
                  </p>
                  <Button variant="outline" className="w-full">Browse Templates</Button>
                </CardContent>
              </Card>

              <Card className="cursor-pointer hover:shadow-lg transition-shadow">
                <CardContent className="p-6 text-center">
                  <Zap className="h-12 w-12 mx-auto mb-4 text-purple-500" />
                  <h3 className="text-lg font-medium mb-2">AI Assistant</h3>
                  <p className="text-sm text-gray-600 mb-4">
                    Let AI help you create a story based on your data
                  </p>
                  <Button variant="outline" className="w-full">Try AI Assistant</Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Templates Tab */}
          <TabsContent value="templates" className="space-y-6">
            <div className="text-center space-y-4">
              <h2 className="text-3xl font-bold">Story Templates</h2>
              <p className="text-gray-600 max-w-2xl mx-auto">
                Professional templates to get you started quickly
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {storyTemplates.map((template) => (
                <Card key={template.id} className="cursor-pointer hover:shadow-lg transition-shadow">
                  <CardContent className="p-6">
                    <div className="aspect-video bg-gray-100 rounded-lg mb-4 flex items-center justify-center">
                      <FileText className="h-12 w-12 text-gray-400" />
                    </div>
                    <h3 className="text-lg font-medium mb-2">{template.name}</h3>
                    <p className="text-sm text-gray-600 mb-4">{template.description}</p>
                    <div className="flex items-center justify-between mb-4">
                      <Badge variant="secondary">{template.category}</Badge>
                      <div className="flex items-center gap-1 text-sm text-gray-500">
                        <Clock className="h-3 w-3" />
                        {template.estimatedTime}m
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-1">
                        <Star className="h-4 w-4 text-yellow-400 fill-current" />
                        <span className="text-sm">{template.rating}</span>
                      </div>
                      <Button size="sm">Use Template</Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* AI Insights Tab */}
          <TabsContent value="insights" className="space-y-6">
            <div className="text-center space-y-4">
              <h2 className="text-3xl font-bold">AI-Powered Insights</h2>
              <p className="text-gray-600 max-w-2xl mx-auto">
                Discover hidden patterns and generate story ideas with artificial intelligence
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Brain className="h-5 w-5" />
                    Story Suggestions
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="p-4 bg-blue-50 rounded-lg">
                    <h4 className="font-medium mb-2">Revenue Anomaly Detected</h4>
                    <p className="text-sm text-gray-600 mb-3">
                      Unusual spike in revenue from mobile users in the last week
                    </p>
                    <Button size="sm">Create Story</Button>
                  </div>
                  <div className="p-4 bg-green-50 rounded-lg">
                    <h4 className="font-medium mb-2">Customer Behavior Pattern</h4>
                    <p className="text-sm text-gray-600 mb-3">
                      New user segment showing high engagement with product features
                    </p>
                    <Button size="sm">Create Story</Button>
                  </div>
                  <div className="p-4 bg-purple-50 rounded-lg">
                    <h4 className="font-medium mb-2">Seasonal Trend Forecast</h4>
                    <p className="text-sm text-gray-600 mb-3">
                      Predicted 40% increase in holiday season based on historical data
                    </p>
                    <Button size="sm">Create Story</Button>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Target className="h-5 w-5" />
                    Performance Insights
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div>
                        <p className="font-medium">Conversion Rate</p>
                        <p className="text-sm text-gray-600">Up 15% from last month</p>
                      </div>
                      <Badge className="bg-green-100 text-green-800">Positive</Badge>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div>
                        <p className="font-medium">User Engagement</p>
                        <p className="text-sm text-gray-600">Session time increased 22%</p>
                      </div>
                      <Badge className="bg-green-100 text-green-800">Positive</Badge>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div>
                        <p className="font-medium">Churn Rate</p>
                        <p className="text-sm text-gray-600">Slight increase in new users</p>
                      </div>
                      <Badge className="bg-yellow-100 text-yellow-800">Watch</Badge>
                    </div>
                  </div>
                  <Button className="w-full">View Full Analysis</Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Analytics Tab */}
          <TabsContent value="analytics" className="space-y-6">
            <div className="text-center space-y-4">
              <h2 className="text-3xl font-bold">Story Analytics</h2>
              <p className="text-gray-600 max-w-2xl mx-auto">
                Track engagement and performance of your data stories
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-sm font-medium text-gray-600">Total Views</h3>
                    <Eye className="h-4 w-4 text-gray-400" />
                  </div>
                  <p className="text-2xl font-bold">4,242</p>
                  <p className="text-sm text-green-600">+12% from last month</p>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-sm font-medium text-gray-600">Engagement Rate</h3>
                    <Activity className="h-4 w-4 text-gray-400" />
                  </div>
                  <p className="text-2xl font-bold">68%</p>
                  <p className="text-sm text-green-600">+5% from last month</p>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-sm font-medium text-gray-600">Stories Created</h3>
                    <BookOpen className="h-4 w-4 text-gray-400" />
                  </div>
                  <p className="text-2xl font-bold">23</p>
                  <p className="text-sm text-blue-600">+3 this month</p>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-sm font-medium text-gray-600">Avg. Time Spent</h3>
                    <Clock className="h-4 w-4 text-gray-400" />
                  </div>
                  <p className="text-2xl font-bold">8.5m</p>
                  <p className="text-sm text-green-600">+1.2m from last month</p>
                </CardContent>
              </Card>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Story Performance</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64 flex items-center justify-center text-gray-500">
                    <ResponsiveContainer width="100%" height="100%">
                      <AreaChart data={[
                        { name: 'Jan', views: 400, engagement: 240 },
                        { name: 'Feb', views: 300, engagement: 139 },
                        { name: 'Mar', views: 200, engagement: 980 },
                        { name: 'Apr', views: 278, engagement: 390 },
                        { name: 'May', views: 189, engagement: 480 },
                        { name: 'Jun', views: 239, engagement: 380 }
                      ]}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="name" />
                        <YAxis />
                        <Tooltip />
                        <Legend />
                        <defs>
                          <linearGradient id="colorViews" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="5%" stopColor="#3B82F6" stopOpacity={0.8}/>
                            <stop offset="95%" stopColor="#3B82F6" stopOpacity={0}/>
                          </linearGradient>
                          <linearGradient id="colorEngagement" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="5%" stopColor="#10B981" stopOpacity={0.8}/>
                            <stop offset="95%" stopColor="#10B981" stopOpacity={0}/>
                          </linearGradient>
                        </defs>
                        <Bar dataKey="views" stackId="a" fill="url(#colorViews)" />
                        <Bar dataKey="engagement" stackId="a" fill="url(#colorEngagement)" />
                      </AreaChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Audience Breakdown</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64 flex items-center justify-center">
                    <ResponsiveContainer width="100%" height="100%">
                      <RechartsPieChart>
                        <Pie
                          data={[
                            { name: 'Executives', value: 35, fill: '#3B82F6' },
                            { name: 'Analysts', value: 28, fill: '#10B981' },
                            { name: 'Stakeholders', value: 22, fill: '#F59E0B' },
                            { name: 'Team', value: 15, fill: '#EF4444' }
                          ]}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="value"
                          label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                        >
                          {[
                            { name: 'Executives', value: 35, fill: '#3B82F6' },
                            { name: 'Analysts', value: 28, fill: '#10B981' },
                            { name: 'Stakeholders', value: 22, fill: '#F59E0B' },
                            { name: 'Team', value: 15, fill: '#EF4444' }
                          ].map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.fill} />
                          ))}
                        </Pie>
                        <Tooltip />
                      </RechartsPieChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default DataStorytellingPlatform;