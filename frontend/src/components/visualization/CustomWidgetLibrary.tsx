/**
 * Custom Widget Library
 * Advanced widget templates with real-time data visualization and marketplace integration
 */

import React, { useState, useMemo, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { motion } from 'framer-motion';
import { 
  Package,
  Plus,
  Download,
  Star,
  Eye,
  Code,
  Settings,
  Share,
  Search,
  Filter,
  Grid,
  List,
  BarChart3,
  LineChart,
  PieChart,
  Gauge,
  Hash,
  FileText,
  Image,
  Video,
  MapPin,
  Clock,
  TrendingUp,
  Activity,
  Users,
  ShoppingCart,
  DollarSign,
  Crown,
  Award,
  Zap,
  Palette,
  Layers,
  Monitor,
  Smartphone,
  Tablet,
  Edit,
  Trash2,
  Copy,
  Upload,
  ExternalLink,
  Info,
  HelpCircle,
  BookOpen,
  GitBranch,
  Terminal,
  Play,
  Pause,
  RefreshCw,
  Save,
  MoreHorizontal
} from 'lucide-react';
import { 
  LineChart as RechartsLineChart, 
  Line, 
  AreaChart, 
  Area, 
  BarChart, 
  Bar, 
  ScatterChart,
  Scatter,
  PieChart as RechartsPieChart,
  Cell,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  ComposedChart,
  Treemap,
  FunnelChart,
  Funnel
} from 'recharts';

// Types for Custom Widget Library
interface WidgetTemplate {
  id: string;
  name: string;
  type: 'chart' | 'metric' | 'table' | 'text' | 'image' | 'video' | 'map' | 'gauge' | 'counter' | 'custom';
  category: string;
  subcategory: string;
  description: string;
  version: string;
  author: {
    id: string;
    name: string;
    avatar: string;
    verified: boolean;
  };
  pricing: {
    type: 'free' | 'paid' | 'premium';
    price?: number;
    currency?: string;
  };
  stats: {
    downloads: number;
    rating: number;
    reviews: number;
    likes: number;
    forks: number;
  };
  metadata: {
    tags: string[];
    compatibility: string[];
    dependencies: string[];
    size: number;
    created: Date;
    updated: Date;
    featured: boolean;
    trending: boolean;
    verified: boolean;
  };
  configuration: {
    properties: WidgetProperty[];
    dataBinding: DataBinding[];
    styling: StylingOptions;
    interactions: InteractionOptions;
    performance: PerformanceOptions;
  };
  code: {
    component: string;
    styles: string;
    config: string;
    demo: string;
  };
  preview: {
    thumbnail: string;
    screenshots: string[];
    demo: string;
    video?: string;
  };
  documentation: {
    readme: string;
    changelog: string;
    examples: CodeExample[];
    api: APIDocumentation[];
  };
}

interface WidgetProperty {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'color' | 'select' | 'array' | 'object';
  label: string;
  description: string;
  defaultValue: any;
  required: boolean;
  options?: any[];
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    custom?: string;
  };
}

interface DataBinding {
  name: string;
  type: 'metric' | 'dimension' | 'measure' | 'filter';
  label: string;
  description: string;
  required: boolean;
  multiple: boolean;
  dataTypes: string[];
  aggregations?: string[];
}

interface StylingOptions {
  themes: string[];
  customCSS: boolean;
  colorSchemes: string[];
  typography: boolean;
  spacing: boolean;
  borders: boolean;
  shadows: boolean;
  animations: boolean;
}

interface InteractionOptions {
  clickable: boolean;
  hoverable: boolean;
  draggable: boolean;
  resizable: boolean;
  selectable: boolean;
  filters: boolean;
  drilldown: boolean;
  tooltips: boolean;
}

interface PerformanceOptions {
  lazy: boolean;
  virtualization: boolean;
  caching: boolean;
  streaming: boolean;
  compression: boolean;
  debouncing: boolean;
  throttling: boolean;
}

interface CodeExample {
  title: string;
  description: string;
  code: string;
  language: string;
  category: string;
}

interface APIDocumentation {
  name: string;
  type: 'prop' | 'method' | 'event';
  description: string;
  parameters?: Parameter[];
  returns?: string;
  examples: string[];
}

interface Parameter {
  name: string;
  type: string;
  description: string;
  required: boolean;
  defaultValue?: any;
}

interface WidgetCollection {
  id: string;
  name: string;
  description: string;
  author: string;
  widgets: string[];
  public: boolean;
  featured: boolean;
  stats: {
    followers: number;
    downloads: number;
    rating: number;
  };
  created: Date;
  updated: Date;
}

interface WidgetAnalytics {
  widgetId: string;
  metrics: {
    downloads: number;
    installations: number;
    activeUsers: number;
    performance: {
      loadTime: number;
      renderTime: number;
      memoryUsage: number;
      errorRate: number;
    };
    usage: {
      dailyActive: number;
      weeklyActive: number;
      monthlyActive: number;
      retention: number;
    };
  };
  timeline: TimelineMetric[];
}

interface TimelineMetric {
  date: Date;
  downloads: number;
  activeUsers: number;
  performance: number;
  errors: number;
}

// Mock data generators
const generateMockWidgetTemplates = (): WidgetTemplate[] => {
  const categories = ['Charts', 'Metrics', 'Tables', 'Media', 'Maps', 'Gauges', 'Counters', 'Custom'];
  const subcategories = ['Line Charts', 'Bar Charts', 'Pie Charts', 'KPI Cards', 'Data Tables', 'Maps', 'Gauges'];
  const types: Array<'chart' | 'metric' | 'table' | 'text' | 'image' | 'video' | 'map' | 'gauge' | 'counter' | 'custom'> = 
    ['chart', 'metric', 'table', 'text', 'gauge', 'counter', 'custom'];

  return Array.from({ length: 48 }, (_, i) => ({
    id: `widget-template-${i + 1}`,
    name: `${categories[i % categories.length]} Widget ${i + 1}`,
    type: types[i % types.length],
    category: categories[i % categories.length],
    subcategory: subcategories[i % subcategories.length],
    description: `Professional ${types[i % types.length]} widget with advanced customization options`,
    version: `${Math.floor(Math.random() * 3) + 1}.${Math.floor(Math.random() * 10)}.${Math.floor(Math.random() * 10)}`,
    author: {
      id: `author-${i % 10 + 1}`,
      name: `Developer ${i % 10 + 1}`,
      avatar: `/api/placeholder/32/32`,
      verified: Math.random() > 0.3
    },
    pricing: {
      type: ['free', 'paid', 'premium'][Math.floor(Math.random() * 3)] as any,
      price: Math.random() > 0.5 ? Math.floor(Math.random() * 50) + 10 : undefined,
      currency: 'USD'
    },
    stats: {
      downloads: Math.floor(Math.random() * 50000),
      rating: 3.5 + Math.random() * 1.5,
      reviews: Math.floor(Math.random() * 1000),
      likes: Math.floor(Math.random() * 5000),
      forks: Math.floor(Math.random() * 500)
    },
    metadata: {
      tags: ['responsive', 'customizable', 'interactive', 'real-time'].slice(0, Math.floor(Math.random() * 4) + 1),
      compatibility: ['React', 'Vue', 'Angular'].slice(0, Math.floor(Math.random() * 3) + 1),
      dependencies: ['recharts', 'lodash', 'moment'].slice(0, Math.floor(Math.random() * 3)),
      size: Math.floor(Math.random() * 500) + 50,
      created: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000),
      updated: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
      featured: Math.random() > 0.8,
      trending: Math.random() > 0.7,
      verified: Math.random() > 0.4
    },
    configuration: {
      properties: [
        {
          name: 'title',
          type: 'string',
          label: 'Widget Title',
          description: 'The title displayed at the top of the widget',
          defaultValue: 'Sample Widget',
          required: true
        },
        {
          name: 'color',
          type: 'color',
          label: 'Primary Color',
          description: 'The primary color scheme for the widget',
          defaultValue: '#3b82f6',
          required: false
        }
      ],
      dataBinding: [
        {
          name: 'data',
          type: 'metric',
          label: 'Data Source',
          description: 'The data source for the widget',
          required: true,
          multiple: false,
          dataTypes: ['number', 'string', 'date']
        }
      ],
      styling: {
        themes: ['light', 'dark', 'auto'],
        customCSS: true,
        colorSchemes: ['blue', 'green', 'purple', 'red'],
        typography: true,
        spacing: true,
        borders: true,
        shadows: true,
        animations: true
      },
      interactions: {
        clickable: true,
        hoverable: true,
        draggable: false,
        resizable: true,
        selectable: true,
        filters: true,
        drilldown: false,
        tooltips: true
      },
      performance: {
        lazy: true,
        virtualization: false,
        caching: true,
        streaming: false,
        compression: false,
        debouncing: true,
        throttling: true
      }
    },
    code: {
      component: `// Widget Component Code\nimport React from 'react';\n\nconst Widget = () => {\n  return <div>Widget Content</div>;\n};\n\nexport default Widget;`,
      styles: `/* Widget Styles */\n.widget {\n  background: white;\n  border-radius: 8px;\n  padding: 16px;\n}`,
      config: `// Widget Configuration\nexport const config = {\n  name: 'Sample Widget',\n  version: '1.0.0'\n};`,
      demo: `// Demo Code\nimport Widget from './Widget';\n\nconst Demo = () => {\n  return <Widget data={sampleData} />;\n};`
    },
    preview: {
      thumbnail: `/api/placeholder/200/150`,
      screenshots: [`/api/placeholder/400/300`],
      demo: `/api/placeholder/600/400`,
      video: Math.random() > 0.7 ? `/api/placeholder/video/600/400` : undefined
    },
    documentation: {
      readme: `# Widget Documentation\n\nThis is a sample widget with advanced features.`,
      changelog: `## v1.0.0\n- Initial release`,
      examples: [
        {
          title: 'Basic Usage',
          description: 'How to use the widget in its simplest form',
          code: '<Widget data={data} />',
          language: 'jsx',
          category: 'basic'
        }
      ],
      api: [
        {
          name: 'data',
          type: 'prop',
          description: 'The data to display in the widget',
          parameters: [
            {
              name: 'data',
              type: 'Array<Object>',
              description: 'Array of data objects',
              required: true
            }
          ],
          examples: ['<Widget data={[{name: "A", value: 100}]} />']
        }
      ]
    }
  }));
};

const generateMockCollections = (): WidgetCollection[] => {
  return Array.from({ length: 12 }, (_, i) => ({
    id: `collection-${i + 1}`,
    name: `Collection ${i + 1}`,
    description: `Curated collection of widgets for ${['analytics', 'e-commerce', 'marketing'][i % 3]}`,
    author: `Author ${i + 1}`,
    widgets: Array.from({ length: Math.floor(Math.random() * 10) + 5 }, (_, j) => `widget-${j + 1}`),
    public: Math.random() > 0.3,
    featured: Math.random() > 0.7,
    stats: {
      followers: Math.floor(Math.random() * 1000),
      downloads: Math.floor(Math.random() * 10000),
      rating: 3.5 + Math.random() * 1.5
    },
    created: new Date(Date.now() - Math.random() * 180 * 24 * 60 * 60 * 1000),
    updated: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000)
  }));
};

// Sample data for analytics
const analyticsData = Array.from({ length: 30 }, (_, i) => ({
  date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toLocaleDateString(),
  downloads: Math.floor(Math.random() * 1000) + 100,
  activeUsers: Math.floor(Math.random() * 500) + 50,
  performance: 80 + Math.random() * 20,
  errors: Math.floor(Math.random() * 20)
}));

const categoryStats = [
  { name: 'Charts', count: 156, color: '#3b82f6' },
  { name: 'Metrics', count: 89, color: '#10b981' },
  { name: 'Tables', count: 67, color: '#f59e0b' },
  { name: 'Maps', count: 34, color: '#ef4444' },
  { name: 'Custom', count: 23, color: '#8b5cf6' }
];

const CustomWidgetLibrary: React.FC = () => {
  const [activeTab, setActiveTab] = useState('browse');
  const [selectedWidget, setSelectedWidget] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterCategory, setFilterCategory] = useState('all');
  const [filterType, setFilterType] = useState('all');
  const [filterPricing, setFilterPricing] = useState('all');
  const [sortBy, setSortBy] = useState('popular');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  const widgets = useMemo(() => generateMockWidgetTemplates(), []);
  const collections = useMemo(() => generateMockCollections(), []);

  const filteredWidgets = useMemo(() => {
    return widgets.filter(widget => {
      const matchesSearch = widget.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           widget.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           widget.metadata.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
      const matchesCategory = filterCategory === 'all' || widget.category === filterCategory;
      const matchesType = filterType === 'all' || widget.type === filterType;
      const matchesPricing = filterPricing === 'all' || widget.pricing.type === filterPricing;
      return matchesSearch && matchesCategory && matchesType && matchesPricing;
    });
  }, [widgets, searchQuery, filterCategory, filterType, filterPricing]);

  const sortedWidgets = useMemo(() => {
    const sorted = [...filteredWidgets];
    switch (sortBy) {
      case 'popular':
        return sorted.sort((a, b) => b.stats.downloads - a.stats.downloads);
      case 'rating':
        return sorted.sort((a, b) => b.stats.rating - a.stats.rating);
      case 'newest':
        return sorted.sort((a, b) => b.metadata.created.getTime() - a.metadata.created.getTime());
      case 'updated':
        return sorted.sort((a, b) => b.metadata.updated.getTime() - a.metadata.updated.getTime());
      case 'name':
        return sorted.sort((a, b) => a.name.localeCompare(b.name));
      default:
        return sorted;
    }
  }, [filteredWidgets, sortBy]);

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'chart': return <BarChart3 className="h-4 w-4" />;
      case 'metric': return <Gauge className="h-4 w-4" />;
      case 'table': return <Grid className="h-4 w-4" />;
      case 'text': return <FileText className="h-4 w-4" />;
      case 'image': return <Image className="h-4 w-4" />;
      case 'video': return <Video className="h-4 w-4" />;
      case 'map': return <MapPin className="h-4 w-4" />;
      case 'gauge': return <Gauge className="h-4 w-4" />;
      case 'counter': return <Hash className="h-4 w-4" />;
      case 'custom': return <Code className="h-4 w-4" />;
      default: return <Package className="h-4 w-4" />;
    }
  };

  const getPricingColor = (type: string) => {
    switch (type) {
      case 'free': return 'bg-green-100 text-green-800';
      case 'paid': return 'bg-blue-100 text-blue-800';
      case 'premium': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-3 w-3 ${i < Math.floor(rating) ? 'text-yellow-500 fill-current' : 'text-gray-300'}`}
      />
    ));
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Custom Widget Library</h1>
          <p className="text-gray-600 mt-2">Advanced widget templates with real-time data visualization</p>
        </div>
        <div className="flex gap-3">
          <Button className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            Create Widget
          </Button>
          <Button variant="outline" className="flex items-center gap-2">
            <Upload className="h-4 w-4" />
            Import
          </Button>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-6 w-full">
          <TabsTrigger value="browse" className="flex items-center gap-2">
            <Package className="h-4 w-4" />
            Browse
          </TabsTrigger>
          <TabsTrigger value="featured" className="flex items-center gap-2">
            <Crown className="h-4 w-4" />
            Featured
          </TabsTrigger>
          <TabsTrigger value="collections" className="flex items-center gap-2">
            <Layers className="h-4 w-4" />
            Collections
          </TabsTrigger>
          <TabsTrigger value="builder" className="flex items-center gap-2">
            <Code className="h-4 w-4" />
            Builder
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Analytics
          </TabsTrigger>
          <TabsTrigger value="marketplace" className="flex items-center gap-2">
            <Award className="h-4 w-4" />
            Marketplace
          </TabsTrigger>
        </TabsList>

        {/* Browse Widgets */}
        <TabsContent value="browse" className="space-y-6">
          {/* Filters and Search */}
          <div className="flex flex-wrap gap-4 items-center">
            <div className="flex-1 min-w-[300px]">
              <Input
                placeholder="Search widgets..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full"
              />
            </div>
            <select
              value={filterCategory}
              onChange={(e) => setFilterCategory(e.target.value)}
              className="border rounded-md px-3 py-2"
            >
              <option value="all">All Categories</option>
              <option value="Charts">Charts</option>
              <option value="Metrics">Metrics</option>
              <option value="Tables">Tables</option>
              <option value="Media">Media</option>
              <option value="Maps">Maps</option>
              <option value="Custom">Custom</option>
            </select>
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              className="border rounded-md px-3 py-2"
            >
              <option value="all">All Types</option>
              <option value="chart">Charts</option>
              <option value="metric">Metrics</option>
              <option value="table">Tables</option>
              <option value="gauge">Gauges</option>
              <option value="counter">Counters</option>
            </select>
            <select
              value={filterPricing}
              onChange={(e) => setFilterPricing(e.target.value)}
              className="border rounded-md px-3 py-2"
            >
              <option value="all">All Pricing</option>
              <option value="free">Free</option>
              <option value="paid">Paid</option>
              <option value="premium">Premium</option>
            </select>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="border rounded-md px-3 py-2"
            >
              <option value="popular">Most Popular</option>
              <option value="rating">Highest Rated</option>
              <option value="newest">Newest</option>
              <option value="updated">Recently Updated</option>
              <option value="name">Name A-Z</option>
            </select>
            <div className="flex gap-2">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('grid')}
              >
                <Grid className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('list')}
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Widget Grid/List */}
          <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6' : 'space-y-4'}>
            {sortedWidgets.map((widget) => (
              <motion.div
                key={widget.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className={viewMode === 'grid' ? 'h-full' : ''}
              >
                <Card className={viewMode === 'grid' ? 'h-full flex flex-col' : ''}>
                  {viewMode === 'grid' && (
                    <div className="relative">
                      <img 
                        src={widget.preview.thumbnail} 
                        alt={widget.name}
                        className="w-full h-40 object-cover rounded-t-lg"
                      />
                      <div className="absolute top-2 left-2 flex gap-2">
                        {widget.metadata.featured && (
                          <Badge className="bg-yellow-100 text-yellow-800">
                            <Crown className="h-3 w-3 mr-1" />
                            Featured
                          </Badge>
                        )}
                        {widget.metadata.verified && (
                          <Badge className="bg-blue-100 text-blue-800">
                            Verified
                          </Badge>
                        )}
                      </div>
                      <div className="absolute top-2 right-2">
                        <Badge className={getPricingColor(widget.pricing.type)}>
                          {widget.pricing.type === 'free' ? 'Free' : 
                           widget.pricing.price ? `$${widget.pricing.price}` : 'Premium'}
                        </Badge>
                      </div>
                    </div>
                  )}
                  
                  <CardContent className={`p-4 ${viewMode === 'grid' ? 'flex-1 flex flex-col' : ''}`}>
                    <div className={`space-y-3 ${viewMode === 'grid' ? 'flex-1' : ''}`}>
                      <div className="flex items-start justify-between">
                        <div className={`${viewMode === 'list' ? 'flex items-center gap-4 flex-1' : ''}`}>
                          {viewMode === 'list' && (
                            <img 
                              src={widget.preview.thumbnail} 
                              alt={widget.name}
                              className="w-16 h-16 object-cover rounded-lg"
                            />
                          )}
                          <div className={viewMode === 'list' ? 'flex-1' : ''}>
                            <div className="flex items-center gap-2 mb-1">
                              {getTypeIcon(widget.type)}
                              <h3 className="font-semibold">{widget.name}</h3>
                              <Badge variant="outline" className="text-xs">
                                v{widget.version}
                              </Badge>
                            </div>
                            <p className={`text-sm text-gray-600 ${viewMode === 'list' ? 'mb-2' : 'mb-3'}`}>
                              {widget.description}
                            </p>
                            {viewMode === 'list' && (
                              <div className="flex items-center gap-4 text-sm text-gray-500">
                                <div className="flex items-center gap-1">
                                  <Download className="h-3 w-3" />
                                  {widget.stats.downloads.toLocaleString()}
                                </div>
                                <div className="flex items-center gap-1">
                                  {renderStars(widget.stats.rating)}
                                  <span className="ml-1">{widget.stats.rating.toFixed(1)}</span>
                                </div>
                                <Badge variant="outline" className="text-xs">
                                  {widget.category}
                                </Badge>
                              </div>
                            )}
                          </div>
                        </div>
                        {viewMode === 'list' && (
                          <div className="flex items-center gap-2">
                            <Badge className={getPricingColor(widget.pricing.type)}>
                              {widget.pricing.type === 'free' ? 'Free' : 
                               widget.pricing.price ? `$${widget.pricing.price}` : 'Premium'}
                            </Badge>
                            <Button size="sm">
                              <Download className="h-3 w-3 mr-1" />
                              Install
                            </Button>
                          </div>
                        )}
                      </div>

                      {viewMode === 'grid' && (
                        <>
                          <div className="flex items-center gap-4 text-sm text-gray-500">
                            <div className="flex items-center gap-1">
                              <Download className="h-3 w-3" />
                              {widget.stats.downloads.toLocaleString()}
                            </div>
                            <div className="flex items-center gap-1">
                              {renderStars(widget.stats.rating)}
                              <span className="ml-1">{widget.stats.rating.toFixed(1)}</span>
                            </div>
                          </div>
                          
                          <div className="flex items-center gap-2">
                            <img 
                              src={widget.author.avatar} 
                              alt={widget.author.name}
                              className="w-5 h-5 rounded-full"
                            />
                            <span className="text-sm text-gray-600">{widget.author.name}</span>
                            {widget.author.verified && (
                              <Badge variant="outline" className="text-xs">Verified</Badge>
                            )}
                          </div>

                          <div className="flex flex-wrap gap-1">
                            <Badge variant="outline" className="text-xs">{widget.category}</Badge>
                            {widget.metadata.tags.slice(0, 2).map(tag => (
                              <Badge key={tag} variant="secondary" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                          </div>
                        </>
                      )}
                    </div>

                    {viewMode === 'grid' && (
                      <div className="flex gap-2 mt-4">
                        <Button size="sm" className="flex-1">
                          <Download className="h-3 w-3 mr-1" />
                          Install
                        </Button>
                        <Button variant="outline" size="sm">
                          <Eye className="h-3 w-3" />
                        </Button>
                        <Button variant="outline" size="sm">
                          <Code className="h-3 w-3" />
                        </Button>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </TabsContent>

        {/* Featured Widgets */}
        <TabsContent value="featured" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {widgets.filter(widget => widget.metadata.featured).map((widget) => (
              <motion.div
                key={widget.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="h-full"
              >
                <Card className="h-full flex flex-col relative overflow-hidden">
                  <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-yellow-400 to-orange-500" />
                  <div className="relative">
                    <img 
                      src={widget.preview.thumbnail} 
                      alt={widget.name}
                      className="w-full h-48 object-cover"
                    />
                    <div className="absolute top-3 left-3">
                      <Badge className="bg-yellow-100 text-yellow-800">
                        <Crown className="h-3 w-3 mr-1" />
                        Featured
                      </Badge>
                    </div>
                    <div className="absolute top-3 right-3">
                      <Badge className={getPricingColor(widget.pricing.type)}>
                        {widget.pricing.type === 'free' ? 'Free' : `$${widget.pricing.price}`}
                      </Badge>
                    </div>
                  </div>
                  <CardContent className="p-4 flex-1 flex flex-col">
                    <div className="space-y-3 flex-1">
                      <div>
                        <h3 className="font-semibold text-lg">{widget.name}</h3>
                        <p className="text-sm text-gray-600 mt-1">{widget.description}</p>
                      </div>
                      <div className="flex items-center gap-4 text-sm text-gray-500">
                        <div className="flex items-center gap-1">
                          <Download className="h-3 w-3" />
                          {widget.stats.downloads.toLocaleString()}
                        </div>
                        <div className="flex items-center gap-1">
                          {renderStars(widget.stats.rating)}
                          <span className="ml-1">{widget.stats.rating.toFixed(1)}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Users className="h-3 w-3" />
                          {widget.stats.reviews}
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <img 
                          src={widget.author.avatar} 
                          alt={widget.author.name}
                          className="w-6 h-6 rounded-full"
                        />
                        <span className="text-sm text-gray-600">{widget.author.name}</span>
                        {widget.author.verified && (
                          <Badge variant="outline" className="text-xs">Verified</Badge>
                        )}
                      </div>
                    </div>
                    <div className="flex gap-2 mt-4">
                      <Button size="sm" className="flex-1">
                        <Download className="h-3 w-3 mr-1" />
                        Install
                      </Button>
                      <Button variant="outline" size="sm">
                        <Eye className="h-3 w-3" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </TabsContent>

        {/* Collections */}
        <TabsContent value="collections" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {collections.map((collection) => (
              <motion.div
                key={collection.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
              >
                <Card>
                  <div className="relative">
                    <div className="grid grid-cols-2 gap-1 h-32 overflow-hidden rounded-t-lg">
                      {Array.from({ length: 4 }, (_, i) => (
                        <img 
                          key={i}
                          src={widgets[i % widgets.length].preview.thumbnail} 
                          alt="Widget preview"
                          className="w-full h-full object-cover"
                        />
                      ))}
                    </div>
                    <div className="absolute top-2 right-2">
                      <Badge className="bg-black bg-opacity-75 text-white">
                        {collection.widgets.length} widgets
                      </Badge>
                    </div>
                  </div>
                  <CardContent className="p-4">
                    <div className="space-y-3">
                      <div>
                        <h3 className="font-semibold">{collection.name}</h3>
                        <p className="text-sm text-gray-600">{collection.description}</p>
                      </div>
                      <div className="flex items-center gap-4 text-sm text-gray-500">
                        <div className="flex items-center gap-1">
                          <Users className="h-3 w-3" />
                          {collection.stats.followers} followers
                        </div>
                        <div className="flex items-center gap-1">
                          <Download className="h-3 w-3" />
                          {collection.stats.downloads.toLocaleString()}
                        </div>
                        <div className="flex items-center gap-1">
                          {renderStars(collection.stats.rating)}
                          <span className="ml-1">{collection.stats.rating.toFixed(1)}</span>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-gray-600">by {collection.author}</span>
                        {collection.featured && (
                          <Badge variant="outline" className="text-xs">Featured</Badge>
                        )}
                      </div>
                      <Button size="sm" className="w-full">
                        <Eye className="h-3 w-3 mr-1" />
                        View Collection
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </TabsContent>

        {/* Widget Builder */}
        <TabsContent value="builder" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Code className="h-5 w-5" />
                  Widget Code Editor
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">Widget Name</label>
                    <Input placeholder="Enter widget name" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">Description</label>
                    <Input placeholder="Enter widget description" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">Category</label>
                    <select className="w-full border rounded-md px-3 py-2">
                      <option>Charts</option>
                      <option>Metrics</option>
                      <option>Tables</option>
                      <option>Custom</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">Component Code</label>
                    <div className="bg-gray-900 text-gray-100 p-4 rounded-lg font-mono text-sm h-64 overflow-auto">
                      <pre>{`import React from 'react';

const CustomWidget = ({ data, config }) => {
  return (
    <div className="widget-container">
      <h3>{config.title}</h3>
      <div className="widget-content">
        {/* Widget implementation */}
      </div>
    </div>
  );
};

export default CustomWidget;`}</pre>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button className="flex-1">
                      <Save className="h-4 w-4 mr-1" />
                      Save Widget
                    </Button>
                    <Button variant="outline">
                      <Play className="h-4 w-4 mr-1" />
                      Test
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Eye className="h-5 w-5" />
                  Widget Preview
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="border-2 border-dashed border-gray-200 rounded-lg h-64 flex items-center justify-center">
                    <div className="text-center text-gray-500">
                      <Monitor className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>Widget preview will appear here</p>
                    </div>
                  </div>
                  
                  <div className="space-y-3">
                    <h4 className="font-medium">Configuration Options</h4>
                    <div className="space-y-2">
                      <div>
                        <label className="block text-sm mb-1">Title</label>
                        <Input placeholder="Widget Title" />
                      </div>
                      <div>
                        <label className="block text-sm mb-1">Color Scheme</label>
                        <select className="w-full border rounded-md px-3 py-2">
                          <option>Blue</option>
                          <option>Green</option>
                          <option>Purple</option>
                          <option>Red</option>
                        </select>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Show Animation</span>
                        <Button variant="outline" size="sm">
                          <Zap className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BookOpen className="h-5 w-5" />
                Widget Templates
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {['Basic Chart', 'KPI Metric', 'Data Table', 'Progress Gauge', 'Number Counter', 'Custom Widget'].map((template, i) => (
                  <Card key={i} className="p-4 cursor-pointer hover:shadow-md transition-shadow">
                    <div className="text-center">
                      {getTypeIcon(['chart', 'metric', 'table', 'gauge', 'counter', 'custom'][i])}
                      <h4 className="font-medium mt-2">{template}</h4>
                      <p className="text-sm text-gray-600 mt-1">Start with this template</p>
                      <Button size="sm" className="mt-3 w-full">
                        Use Template
                      </Button>
                    </div>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Analytics */}
        <TabsContent value="analytics" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Widgets</p>
                    <p className="text-2xl font-bold">369</p>
                  </div>
                  <Package className="h-8 w-8 text-blue-500" />
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  <span className="text-green-600">↗ 12%</span> from last month
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Downloads</p>
                    <p className="text-2xl font-bold">2.4M</p>
                  </div>
                  <Download className="h-8 w-8 text-green-500" />
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  <span className="text-green-600">↗ 8%</span> from last week
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Active Users</p>
                    <p className="text-2xl font-bold">15.2K</p>
                  </div>
                  <Users className="h-8 w-8 text-purple-500" />
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  <span className="text-green-600">↗ 5%</span> from yesterday
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Avg Rating</p>
                    <p className="text-2xl font-bold">4.6</p>
                  </div>
                  <Star className="h-8 w-8 text-yellow-500" />
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  <span className="text-green-600">↗ 0.1</span> improvement
                </p>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Download Trends
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <RechartsLineChart data={analyticsData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Line type="monotone" dataKey="downloads" stroke="#3b82f6" strokeWidth={2} name="Downloads" />
                      <Line type="monotone" dataKey="activeUsers" stroke="#10b981" strokeWidth={2} name="Active Users" />
                    </RechartsLineChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <PieChart className="h-5 w-5" />
                  Category Distribution
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <RechartsPieChart>
                      <Pie
                        data={categoryStats}
                        cx="50%"
                        cy="50%"
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="count"
                        label={({ name, count }) => `${name}: ${count}`}
                      >
                        {categoryStats.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </RechartsPieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Performance Metrics
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={analyticsData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Area type="monotone" dataKey="performance" stackId="1" stroke="#8b5cf6" fill="#8b5cf6" fillOpacity={0.6} name="Performance Score" />
                    <Area type="monotone" dataKey="errors" stackId="2" stroke="#ef4444" fill="#ef4444" fillOpacity={0.6} name="Error Count" />
                  </AreaChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Marketplace */}
        <TabsContent value="marketplace" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Crown className="h-5 w-5" />
                  Premium Widgets
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="text-sm text-gray-600">Access exclusive premium widgets with advanced features</p>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Advanced Charts</span>
                      <Badge>Pro</Badge>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Real-time Dashboards</span>
                      <Badge>Pro</Badge>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Custom Themes</span>
                      <Badge>Pro</Badge>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Priority Support</span>
                      <Badge>Pro</Badge>
                    </div>
                  </div>
                  <Button className="w-full">
                    <Crown className="h-4 w-4 mr-2" />
                    Upgrade to Pro
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Award className="h-5 w-5" />
                  Widget Rewards
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="text-sm text-gray-600">Earn rewards for creating and sharing widgets</p>
                  <div className="space-y-3">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                        <Award className="h-4 w-4 text-yellow-600" />
                      </div>
                      <div>
                        <div className="font-medium text-sm">Widget Creator</div>
                        <div className="text-xs text-gray-500">Create 5 widgets</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <Star className="h-4 w-4 text-blue-600" />
                      </div>
                      <div>
                        <div className="font-medium text-sm">Popular Widget</div>
                        <div className="text-xs text-gray-500">1000+ downloads</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <Users className="h-4 w-4 text-green-600" />
                      </div>
                      <div>
                        <div className="font-medium text-sm">Community Hero</div>
                        <div className="text-xs text-gray-500">Help other developers</div>
                      </div>
                    </div>
                  </div>
                  <Button variant="outline" className="w-full">
                    View All Rewards
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="h-5 w-5" />
                  Quick Actions
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <Button variant="outline" className="w-full justify-start">
                    <Plus className="h-4 w-4 mr-2" />
                    Create New Widget
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <Upload className="h-4 w-4 mr-2" />
                    Import Widget
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <Share className="h-4 w-4 mr-2" />
                    Share Collection
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <Settings className="h-4 w-4 mr-2" />
                    Marketplace Settings
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <HelpCircle className="h-4 w-4 mr-2" />
                    Help & Support
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Trending This Week
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {widgets.filter(w => w.metadata.trending).slice(0, 5).map((widget, index) => (
                    <div key={widget.id} className="flex items-center gap-4">
                      <div className="flex items-center justify-center w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-full font-bold text-sm">
                        {index + 1}
                      </div>
                      <img 
                        src={widget.preview.thumbnail} 
                        alt={widget.name}
                        className="w-12 h-12 object-cover rounded-lg"
                      />
                      <div className="flex-1">
                        <h4 className="font-medium text-sm">{widget.name}</h4>
                        <div className="flex items-center gap-2 text-xs text-gray-500">
                          <span>{widget.stats.downloads.toLocaleString()} downloads</span>
                          <span>•</span>
                          <div className="flex items-center gap-1">
                            {renderStars(widget.stats.rating)}
                          </div>
                        </div>
                      </div>
                      <Button size="sm" variant="outline">
                        <Eye className="h-3 w-3" />
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  Recently Updated
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {widgets.slice(0, 5).map((widget) => (
                    <div key={widget.id} className="flex items-center gap-4">
                      <img 
                        src={widget.preview.thumbnail} 
                        alt={widget.name}
                        className="w-12 h-12 object-cover rounded-lg"
                      />
                      <div className="flex-1">
                        <h4 className="font-medium text-sm">{widget.name}</h4>
                        <div className="flex items-center gap-2 text-xs text-gray-500">
                          <span>v{widget.version}</span>
                          <span>•</span>
                          <span>{widget.metadata.updated.toLocaleDateString()}</span>
                        </div>
                      </div>
                      <div className="flex gap-1">
                        <Button size="sm" variant="outline">
                          <Download className="h-3 w-3" />
                        </Button>
                        <Button size="sm" variant="outline">
                          <MoreHorizontal className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default CustomWidgetLibrary;