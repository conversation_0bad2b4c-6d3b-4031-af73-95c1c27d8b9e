/**
 * Interactive Report Builder
 * Guided report creation with AI-powered insights and automated layouts
 */

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  FileText,
  Layout,
  Layers,
  Wand2,
  Brain,
  Eye,
  Settings,
  Save,
  Share,
  Download,
  Edit,
  Plus,
  Minus,
  Copy,
  Trash,
  MoreHorizontal,
  ChevronUp,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  ArrowUp,
  ArrowDown,
  ArrowLeft,
  ArrowRight,
  Grid,
  List,
  BarChart3,
  LineChart,
  PieChart,
  TrendingUp,
  Target,
  Users,
  DollarSign,
  ShoppingCart,
  Package,
  Activity,
  Clock,
  Calendar,
  Filter,
  Search,
  RefreshCw,
  ExternalLink,
  Lightbulb,
  AlertTriangle,
  CheckCircle,
  Info,
  Star,
  Flag,
  Tag,
  Hash,
  Percent,
  Globe,
  MapPin,
  Building2,
  Home,
  Office,
  Store,
  Factory,
  Warehouse,
  Smartphone,
  Monitor,
  Tablet,
  Laptop,
  Watch,
  Camera,
  Mic,
  Speaker,
  Headphones,
  Volume2,
  VolumeX,
  Play,
  Pause,
  Stop,
  SkipForward,
  SkipBack,
  FastForward,
  Rewind,
  Repeat,
  Shuffle,
  Mail,
  Phone,
  MessageCircle,
  Send,
  Inbox,
  Archive,
  Folder,
  File,
  Image,
  Video,
  Music,
  Code,
  Terminal,
  GitBranch,
  GitCommit,
  GitMerge,
  GitPullRequest,
  Sidebar,
  PanelLeft,
  PanelRight,
  Menu,
  X,
  Upload,
  Link,
  Unlink,
  Scissors,
  PaintBucket,
  Palette,
  Brush,
  Eraser,
  Ruler,
  Move,
  RotateCcw,
  RotateCw,
  FlipHorizontal,
  FlipVertical,
  Crop,
  Maximize,
  Minimize,
  Square,
  Circle,
  Triangle,
  Hexagon,
  Diamond,
  Heart,
  Sparkles,
  Flame,
  Snowflake,
  Sun,
  Moon,
  CloudRain,
  Thermometer,
  Wind,
  Umbrella,
  Mountain,
  Tree,
  Flower,
  Leaf
} from 'lucide-react';
import { 
  LineChart as RechartsLineChart, 
  Line, 
  AreaChart, 
  Area, 
  BarChart, 
  Bar, 
  PieChart as RechartsPieChart,
  Cell,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  ComposedChart,
  ScatterChart,
  Scatter,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar
} from 'recharts';

// Types for Interactive Report Builder
interface Report {
  id: string;
  title: string;
  description: string;
  type: 'dashboard' | 'analytical' | 'executive' | 'operational' | 'compliance' | 'custom';
  template: ReportTemplate;
  status: 'draft' | 'in_progress' | 'review' | 'published' | 'archived';
  visibility: 'public' | 'private' | 'team' | 'organization';
  sections: ReportSection[];
  layout: ReportLayout;
  styling: ReportStyling;
  data_sources: DataSource[];
  filters: ReportFilter[];
  parameters: ReportParameter[];
  schedules: ReportSchedule[];
  distribution: ReportDistribution;
  automation: ReportAutomation;
  insights: AIInsight[];
  comments: ReportComment[];
  version: number;
  created_by: string;
  created_at: Date;
  updated_at: Date;
  last_generated: Date;
  next_generation: Date;
  metadata: {
    tags: string[];
    category: string;
    priority: 'high' | 'medium' | 'low';
    audience: string[];
    business_context: string;
    objectives: string[];
    success_metrics: string[];
    data_quality_score: number;
    compliance_requirements: string[];
    security_classification: 'public' | 'internal' | 'confidential' | 'restricted';
  };
}

interface ReportTemplate {
  id: string;
  name: string;
  description: string;
  category: 'business' | 'financial' | 'operational' | 'marketing' | 'sales' | 'custom';
  preview_url: string;
  sections: TemplateSection[];
  layout: TemplateLayout;
  suggested_data_sources: string[];
  recommended_visualizations: string[];
  best_practices: string[];
  use_cases: string[];
  complexity: 'simple' | 'intermediate' | 'advanced';
  estimated_time: string;
  required_permissions: string[];
}

interface TemplateSection {
  id: string;
  name: string;
  description: string;
  type: 'summary' | 'analysis' | 'visualization' | 'table' | 'text' | 'image' | 'custom';
  position: { x: number; y: number; width: number; height: number };
  required: boolean;
  suggested_content: string[];
  visualization_types: string[];
  data_requirements: DataRequirement[];
}

interface TemplateLayout {
  format: 'A4' | 'letter' | 'legal' | 'tabloid' | 'custom';
  orientation: 'portrait' | 'landscape';
  margins: { top: number; right: number; bottom: number; left: number };
  header: LayoutElement;
  footer: LayoutElement;
  sidebar: LayoutElement;
  grid: GridSettings;
  responsive: boolean;
  breakpoints: ResponsiveBreakpoint[];
}

interface LayoutElement {
  enabled: boolean;
  height: number;
  content: string;
  styling: ElementStyling;
  data_binding?: DataBinding;
}

interface GridSettings {
  enabled: boolean;
  columns: number;
  rows: number;
  gap: number;
  snap_to_grid: boolean;
  show_grid: boolean;
}

interface ResponsiveBreakpoint {
  name: string;
  min_width: number;
  max_width: number;
  layout_adjustments: LayoutAdjustment[];
}

interface LayoutAdjustment {
  element_id: string;
  property: string;
  value: any;
}

interface ReportSection {
  id: string;
  title: string;
  description: string;
  type: 'summary' | 'analysis' | 'visualization' | 'table' | 'text' | 'image' | 'custom';
  order: number;
  content: SectionContent;
  layout: SectionLayout;
  styling: SectionStyling;
  data_binding: DataBinding;
  filters: SectionFilter[];
  interactions: SectionInteraction[];
  conditional_display: ConditionalDisplay;
  ai_insights: SectionAIInsights;
  status: 'draft' | 'complete' | 'needs_review' | 'approved';
}

interface SectionContent {
  type: 'visualization' | 'text' | 'table' | 'image' | 'metric' | 'list' | 'custom';
  data: any;
  configuration: ContentConfiguration;
  formatting: ContentFormatting;
  annotations: Annotation[];
  cross_references: CrossReference[];
}

interface ContentConfiguration {
  chart_type?: string;
  aggregation?: string;
  grouping?: string[];
  sorting?: SortConfiguration[];
  limits?: LimitConfiguration;
  calculations?: CalculationConfiguration[];
  formatting_rules?: FormattingRule[];
}

interface SortConfiguration {
  field: string;
  direction: 'asc' | 'desc';
  priority: number;
}

interface LimitConfiguration {
  max_rows?: number;
  max_columns?: number;
  pagination?: boolean;
  page_size?: number;
}

interface CalculationConfiguration {
  name: string;
  formula: string;
  display_name: string;
  format: string;
  description: string;
}

interface FormattingRule {
  condition: string;
  format: FormatStyle;
  priority: number;
}

interface FormatStyle {
  background_color?: string;
  text_color?: string;
  font_weight?: string;
  font_style?: string;
  border?: string;
  icon?: string;
}

interface ContentFormatting {
  number_format: string;
  date_format: string;
  currency: string;
  decimal_places: number;
  thousands_separator: boolean;
  null_value_display: string;
  color_scheme: string;
  theme: string;
}

interface Annotation {
  id: string;
  type: 'callout' | 'highlight' | 'note' | 'warning' | 'insight';
  position: { x: number; y: number };
  content: string;
  styling: AnnotationStyling;
  triggers: AnnotationTrigger[];
}

interface AnnotationStyling {
  background_color: string;
  text_color: string;
  border_color: string;
  font_size: string;
  opacity: number;
  shadow: boolean;
}

interface AnnotationTrigger {
  event: 'hover' | 'click' | 'load' | 'data_change';
  condition?: string;
}

interface CrossReference {
  id: string;
  target_section: string;
  target_element?: string;
  type: 'link' | 'drill_down' | 'filter' | 'highlight';
  label: string;
  description?: string;
}

interface SectionLayout {
  position: { x: number; y: number; width: number; height: number };
  alignment: 'left' | 'center' | 'right' | 'justify';
  padding: { top: number; right: number; bottom: number; left: number };
  margin: { top: number; right: number; bottom: number; left: number };
  border: BorderStyle;
  background: BackgroundStyle;
  responsive: ResponsiveSettings;
}

interface BorderStyle {
  width: number;
  style: 'solid' | 'dashed' | 'dotted' | 'none';
  color: string;
  radius: number;
}

interface BackgroundStyle {
  color?: string;
  image?: string;
  gradient?: GradientStyle;
  opacity: number;
}

interface GradientStyle {
  type: 'linear' | 'radial';
  direction: number;
  stops: GradientStop[];
}

interface GradientStop {
  position: number;
  color: string;
}

interface ResponsiveSettings {
  breakpoints: ResponsiveBreakpoint[];
  auto_resize: boolean;
  maintain_aspect_ratio: boolean;
}

interface SectionStyling {
  font_family: string;
  font_size: string;
  font_weight: string;
  line_height: number;
  text_color: string;
  heading_styles: HeadingStyle[];
  link_styles: LinkStyle;
  table_styles: TableStyle;
  chart_styles: ChartStyle;
}

interface HeadingStyle {
  level: number;
  font_size: string;
  font_weight: string;
  color: string;
  margin: { top: number; bottom: number };
}

interface LinkStyle {
  color: string;
  hover_color: string;
  underline: boolean;
  target: '_self' | '_blank';
}

interface TableStyle {
  header_background: string;
  header_text_color: string;
  row_alternating: boolean;
  border_style: BorderStyle;
  cell_padding: number;
  hover_effect: boolean;
}

interface ChartStyle {
  color_palette: string[];
  background_color: string;
  grid_color: string;
  axis_color: string;
  legend_position: 'top' | 'bottom' | 'left' | 'right' | 'none';
  animation: boolean;
  tooltip_style: TooltipStyle;
}

interface TooltipStyle {
  background_color: string;
  text_color: string;
  border_color: string;
  font_size: string;
  opacity: number;
}

interface DataBinding {
  source_id: string;
  query: string;
  parameters: Record<string, any>;
  refresh_interval: number;
  cache_duration: number;
  fallback_data?: any;
  error_handling: ErrorHandling;
}

interface ErrorHandling {
  on_error: 'show_message' | 'show_fallback' | 'hide_section' | 'show_placeholder';
  error_message: string;
  retry_attempts: number;
  retry_delay: number;
}

interface SectionFilter {
  id: string;
  field: string;
  operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains' | 'in' | 'between';
  value: any;
  label: string;
  user_configurable: boolean;
  default_value: any;
}

interface SectionInteraction {
  type: 'click' | 'hover' | 'double_click' | 'right_click' | 'drag' | 'scroll';
  action: 'drill_down' | 'filter' | 'sort' | 'navigate' | 'export' | 'custom';
  target: string;
  parameters: Record<string, any>;
  confirmation_required: boolean;
  confirmation_message?: string;
}

interface ConditionalDisplay {
  enabled: boolean;
  conditions: DisplayCondition[];
  default_visibility: boolean;
}

interface DisplayCondition {
  field: string;
  operator: string;
  value: any;
  logic: 'and' | 'or';
}

interface SectionAIInsights {
  enabled: boolean;
  insight_types: InsightType[];
  confidence_threshold: number;
  max_insights: number;
  display_style: 'inline' | 'sidebar' | 'tooltip' | 'popup';
  auto_highlight: boolean;
}

interface InsightType {
  type: 'trend' | 'anomaly' | 'correlation' | 'prediction' | 'recommendation' | 'explanation';
  enabled: boolean;
  priority: number;
  configuration: Record<string, any>;
}

interface ReportLayout {
  format: 'A4' | 'letter' | 'legal' | 'tabloid' | 'custom';
  orientation: 'portrait' | 'landscape';
  dimensions: { width: number; height: number };
  margins: { top: number; right: number; bottom: number; left: number };
  header: LayoutElement;
  footer: LayoutElement;
  page_numbers: PageNumbering;
  table_of_contents: TableOfContents;
  watermark: Watermark;
}

interface PageNumbering {
  enabled: boolean;
  format: 'numeric' | 'roman' | 'alphabetic';
  position: 'header' | 'footer';
  alignment: 'left' | 'center' | 'right';
  start_page: number;
  prefix?: string;
  suffix?: string;
}

interface TableOfContents {
  enabled: boolean;
  position: 'beginning' | 'end' | 'custom';
  depth: number;
  include_page_numbers: boolean;
  styling: TOCStyling;
}

interface TOCStyling {
  font_size: string;
  indent_size: number;
  line_spacing: number;
  dot_leader: boolean;
}

interface Watermark {
  enabled: boolean;
  text?: string;
  image?: string;
  opacity: number;
  position: 'center' | 'top_left' | 'top_right' | 'bottom_left' | 'bottom_right';
  rotation: number;
  size: number;
}

interface ReportStyling {
  theme: 'light' | 'dark' | 'custom';
  color_scheme: string;
  font_family: string;
  primary_color: string;
  secondary_color: string;
  accent_color: string;
  background_color: string;
  text_color: string;
  border_color: string;
  custom_css?: string;
  brand_elements: BrandElement[];
}

interface BrandElement {
  type: 'logo' | 'color' | 'font' | 'pattern';
  value: string;
  position?: string;
  size?: string;
}

interface DataSource {
  id: string;
  name: string;
  type: 'database' | 'api' | 'file' | 'stream' | 'external';
  connection: ConnectionConfig;
  schema: DataSchema;
  refresh_rate: number;
  last_updated: Date;
  status: 'connected' | 'disconnected' | 'error' | 'syncing';
  permissions: DataPermission[];
}

interface ConnectionConfig {
  endpoint: string;
  authentication: AuthConfig;
  headers?: Record<string, string>;
  timeout: number;
  retry_policy: RetryPolicy;
}

interface AuthConfig {
  type: 'none' | 'basic' | 'bearer' | 'oauth' | 'api_key';
  credentials: Record<string, string>;
  token_refresh?: TokenRefresh;
}

interface TokenRefresh {
  enabled: boolean;
  refresh_endpoint: string;
  refresh_interval: number;
  auto_retry: boolean;
}

interface RetryPolicy {
  max_attempts: number;
  delay: number;
  backoff_multiplier: number;
  max_delay: number;
}

interface DataSchema {
  fields: DataField[];
  relationships: DataRelationship[];
  constraints: DataConstraint[];
}

interface DataField {
  name: string;
  type: 'string' | 'number' | 'date' | 'boolean' | 'array' | 'object';
  description?: string;
  required: boolean;
  default_value?: any;
  format?: string;
  validation: FieldValidation[];
}

interface FieldValidation {
  type: 'min' | 'max' | 'pattern' | 'enum' | 'custom';
  value: any;
  message: string;
}

interface DataRelationship {
  source_field: string;
  target_source: string;
  target_field: string;
  type: 'one_to_one' | 'one_to_many' | 'many_to_many';
}

interface DataConstraint {
  type: 'unique' | 'not_null' | 'check' | 'foreign_key';
  fields: string[];
  condition?: string;
}

interface DataPermission {
  user_id: string;
  role: string;
  permissions: string[];
  restrictions: DataRestriction[];
}

interface DataRestriction {
  type: 'row_level' | 'column_level' | 'time_based' | 'condition_based';
  condition: string;
  description: string;
}

interface ReportFilter {
  id: string;
  name: string;
  type: 'text' | 'number' | 'date' | 'select' | 'multi_select' | 'range' | 'boolean';
  field: string;
  operator: string;
  default_value: any;
  options?: FilterOption[];
  validation: FilterValidation;
  dependencies: FilterDependency[];
  ui_config: FilterUIConfig;
}

interface FilterOption {
  label: string;
  value: any;
  description?: string;
  group?: string;
}

interface FilterValidation {
  required: boolean;
  min_value?: any;
  max_value?: any;
  pattern?: string;
  custom_validation?: string;
}

interface FilterDependency {
  target_filter: string;
  condition: string;
  action: 'show' | 'hide' | 'enable' | 'disable' | 'update_options';
}

interface FilterUIConfig {
  placeholder: string;
  help_text?: string;
  width: 'small' | 'medium' | 'large' | 'full';
  position: number;
  collapsible: boolean;
  search_enabled: boolean;
  multi_column: boolean;
}

interface ReportParameter {
  id: string;
  name: string;
  type: string;
  value: any;
  description: string;
  scope: 'global' | 'section' | 'visualization';
  editable: boolean;
  data_type: 'string' | 'number' | 'date' | 'boolean' | 'array' | 'object';
}

interface ReportSchedule {
  id: string;
  name: string;
  frequency: 'manual' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'annually';
  time: string;
  timezone: string;
  days_of_week?: number[];
  days_of_month?: number[];
  enabled: boolean;
  last_run?: Date;
  next_run?: Date;
  recipients: ScheduleRecipient[];
  format: 'pdf' | 'excel' | 'csv' | 'html' | 'json';
  delivery_method: 'email' | 'webhook' | 'ftp' | 'sftp' | 's3' | 'api';
  delivery_config: DeliveryConfig;
}

interface ScheduleRecipient {
  type: 'user' | 'group' | 'email' | 'webhook';
  identifier: string;
  preferences: RecipientPreferences;
}

interface RecipientPreferences {
  format?: string;
  delivery_method?: string;
  notification_settings: NotificationSettings;
  personalization: PersonalizationSettings;
}

interface NotificationSettings {
  on_success: boolean;
  on_failure: boolean;
  on_data_change: boolean;
  digest_frequency: 'immediate' | 'daily' | 'weekly';
}

interface PersonalizationSettings {
  language: string;
  timezone: string;
  custom_filters: Record<string, any>;
  preferred_sections: string[];
}

interface DeliveryConfig {
  endpoint?: string;
  credentials?: Record<string, string>;
  headers?: Record<string, string>;
  file_naming: FileNaming;
  compression: CompressionSettings;
  encryption: EncryptionSettings;
}

interface FileNaming {
  template: string;
  include_timestamp: boolean;
  include_parameters: boolean;
  sanitize_names: boolean;
}

interface CompressionSettings {
  enabled: boolean;
  format: 'zip' | 'gzip' | '7z';
  level: number;
}

interface EncryptionSettings {
  enabled: boolean;
  algorithm: string;
  key_source: 'generated' | 'provided' | 'kms';
  key_value?: string;
}

interface ReportDistribution {
  channels: DistributionChannel[];
  access_control: AccessControl;
  sharing_settings: SharingSettings;
  embedding: EmbeddingSettings;
}

interface DistributionChannel {
  type: 'email' | 'slack' | 'teams' | 'webhook' | 'portal' | 'public_url';
  configuration: ChannelConfig;
  recipients: string[];
  schedule?: string;
  enabled: boolean;
}

interface ChannelConfig {
  endpoint?: string;
  credentials?: Record<string, string>;
  template?: string;
  custom_settings: Record<string, any>;
}

interface AccessControl {
  public_access: boolean;
  password_protection: boolean;
  ip_restrictions: string[];
  time_restrictions: TimeRestriction[];
  user_permissions: UserPermission[];
  role_permissions: RolePermission[];
}

interface TimeRestriction {
  start_time: string;
  end_time: string;
  days_of_week: number[];
  timezone: string;
}

interface UserPermission {
  user_id: string;
  permissions: string[];
  restrictions: string[];
  expiration?: Date;
}

interface RolePermission {
  role: string;
  permissions: string[];
  restrictions: string[];
}

interface SharingSettings {
  allow_public_sharing: boolean;
  allow_external_sharing: boolean;
  require_authentication: boolean;
  link_expiration: number;
  download_permissions: string[];
  print_permissions: string[];
  copy_permissions: string[];
}

interface EmbeddingSettings {
  enabled: boolean;
  domains: string[];
  iframe_options: IFrameOptions;
  api_access: APIAccess;
  customization: EmbeddingCustomization;
}

interface IFrameOptions {
  width: string;
  height: string;
  border: boolean;
  scrolling: boolean;
  responsive: boolean;
}

interface APIAccess {
  enabled: boolean;
  rate_limiting: RateLimiting;
  authentication_required: boolean;
  cors_settings: CORSSettings;
}

interface RateLimiting {
  requests_per_minute: number;
  requests_per_hour: number;
  requests_per_day: number;
}

interface CORSSettings {
  allowed_origins: string[];
  allowed_methods: string[];
  allowed_headers: string[];
  credentials: boolean;
}

interface EmbeddingCustomization {
  theme: string;
  hide_navigation: boolean;
  hide_filters: boolean;
  hide_export: boolean;
  custom_css: string;
  white_labeling: WhiteLabeling;
}

interface WhiteLabeling {
  remove_branding: boolean;
  custom_logo: string;
  custom_colors: Record<string, string>;
  custom_fonts: string[];
}

interface ReportAutomation {
  triggers: AutomationTrigger[];
  actions: AutomationAction[];
  workflows: AutomationWorkflow[];
  monitoring: AutomationMonitoring;
}

interface AutomationTrigger {
  id: string;
  type: 'schedule' | 'data_change' | 'threshold' | 'event' | 'manual';
  condition: string;
  parameters: Record<string, any>;
  enabled: boolean;
}

interface AutomationAction {
  id: string;
  type: 'generate' | 'distribute' | 'alert' | 'update' | 'archive' | 'custom';
  configuration: ActionConfiguration;
  retry_policy: RetryPolicy;
  timeout: number;
}

interface ActionConfiguration {
  target: string;
  parameters: Record<string, any>;
  conditions: ActionCondition[];
  error_handling: ErrorHandling;
}

interface ActionCondition {
  field: string;
  operator: string;
  value: any;
  action_on_true: string;
  action_on_false: string;
}

interface AutomationWorkflow {
  id: string;
  name: string;
  description: string;
  triggers: string[];
  actions: WorkflowAction[];
  error_handling: WorkflowErrorHandling;
  monitoring: WorkflowMonitoring;
}

interface WorkflowAction {
  action_id: string;
  depends_on: string[];
  condition?: string;
  retry_policy?: RetryPolicy;
  timeout?: number;
}

interface WorkflowErrorHandling {
  on_error: 'stop' | 'continue' | 'retry' | 'fallback';
  fallback_action?: string;
  notification_settings: NotificationSettings;
}

interface WorkflowMonitoring {
  track_performance: boolean;
  log_level: 'debug' | 'info' | 'warn' | 'error';
  alert_on_failure: boolean;
  alert_on_long_runtime: boolean;
  max_runtime: number;
}

interface AutomationMonitoring {
  enabled: boolean;
  metrics: MonitoringMetric[];
  alerts: MonitoringAlert[];
  dashboards: string[];
}

interface MonitoringMetric {
  name: string;
  type: 'counter' | 'gauge' | 'histogram' | 'timer';
  labels: Record<string, string>;
  retention: number;
}

interface MonitoringAlert {
  name: string;
  condition: string;
  severity: 'info' | 'warning' | 'critical';
  recipients: string[];
  cooldown: number;
}

interface AIInsight {
  id: string;
  type: 'trend' | 'anomaly' | 'correlation' | 'prediction' | 'recommendation' | 'explanation';
  title: string;
  description: string;
  confidence: number;
  relevance: number;
  data_points: DataPoint[];
  supporting_evidence: Evidence[];
  recommendations: AIRecommendation[];
  visualization_suggestions: VisualizationSuggestion[];
  generated_at: Date;
  expires_at?: Date;
  feedback: InsightFeedback[];
}

interface DataPoint {
  metric: string;
  value: number;
  timestamp: Date;
  context: Record<string, any>;
}

interface Evidence {
  type: 'statistical' | 'visual' | 'comparative' | 'historical';
  description: string;
  strength: 'weak' | 'moderate' | 'strong';
  data: any;
}

interface AIRecommendation {
  action: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  impact: 'low' | 'medium' | 'high';
  effort: 'low' | 'medium' | 'high';
  timeline: string;
  success_criteria: string[];
  risks: string[];
}

interface VisualizationSuggestion {
  chart_type: string;
  rationale: string;
  configuration: Record<string, any>;
  expected_insight: string;
}

interface InsightFeedback {
  user_id: string;
  rating: number;
  useful: boolean;
  comments?: string;
  timestamp: Date;
}

interface ReportComment {
  id: string;
  author: string;
  content: string;
  timestamp: Date;
  type: 'general' | 'section' | 'data' | 'suggestion' | 'issue';
  target?: string;
  status: 'open' | 'resolved' | 'dismissed';
  replies: ReportComment[];
  attachments: string[];
  mentions: string[];
  reactions: CommentReaction[];
}

interface CommentReaction {
  user_id: string;
  emoji: string;
  timestamp: Date;
}

interface DataRequirement {
  field: string;
  type: string;
  required: boolean;
  description: string;
  example_values: any[];
}

interface ElementStyling {
  font_family: string;
  font_size: string;
  font_weight: string;
  color: string;
  background_color: string;
  border: BorderStyle;
  padding: { top: number; right: number; bottom: number; left: number };
  alignment: 'left' | 'center' | 'right';
}

// Mock data generators
const generateReportTemplates = () => {
  return [
    {
      id: 'template-executive',
      name: 'Executive Summary',
      description: 'High-level overview for executives with key metrics and insights',
      category: 'business',
      complexity: 'simple',
      estimated_time: '15 minutes',
      preview_url: '/templates/executive-preview.png'
    },
    {
      id: 'template-sales',
      name: 'Sales Performance',
      description: 'Comprehensive sales analytics with pipeline and conversion metrics',
      category: 'sales',
      complexity: 'intermediate',
      estimated_time: '30 minutes',
      preview_url: '/templates/sales-preview.png'
    },
    {
      id: 'template-marketing',
      name: 'Marketing Analytics',
      description: 'Marketing campaign performance and ROI analysis',
      category: 'marketing',
      complexity: 'intermediate',
      estimated_time: '25 minutes',
      preview_url: '/templates/marketing-preview.png'
    },
    {
      id: 'template-financial',
      name: 'Financial Report',
      description: 'Detailed financial analysis with P&L and cash flow statements',
      category: 'financial',
      complexity: 'advanced',
      estimated_time: '45 minutes',
      preview_url: '/templates/financial-preview.png'
    },
    {
      id: 'template-operational',
      name: 'Operational Dashboard',
      description: 'Real-time operational metrics and performance indicators',
      category: 'operational',
      complexity: 'intermediate',
      estimated_time: '20 minutes',
      preview_url: '/templates/operational-preview.png'
    },
    {
      id: 'template-custom',
      name: 'Custom Report',
      description: 'Start from scratch with a blank template',
      category: 'custom',
      complexity: 'advanced',
      estimated_time: '60+ minutes',
      preview_url: '/templates/custom-preview.png'
    }
  ];
};

const generateReports = (): Report[] => {
  const statuses: Array<'draft' | 'in_progress' | 'review' | 'published' | 'archived'> = 
    ['draft', 'in_progress', 'review', 'published', 'archived'];
  const types: Array<'dashboard' | 'analytical' | 'executive' | 'operational' | 'compliance' | 'custom'> = 
    ['dashboard', 'analytical', 'executive', 'operational', 'compliance', 'custom'];

  return Array.from({ length: 15 }, (_, i) => ({
    id: `report-${i + 1}`,
    title: [
      'Q4 Executive Summary',
      'Sales Performance Analysis',
      'Marketing ROI Report',
      'Customer Satisfaction Dashboard',
      'Financial Performance Review',
      'Operational Efficiency Metrics',
      'Product Analytics Overview',
      'Supply Chain Dashboard',
      'Employee Engagement Report',
      'Competitive Analysis',
      'Market Research Insights',
      'Compliance Audit Report',
      'Risk Assessment Dashboard',
      'Innovation Pipeline Review',
      'Strategic Planning Report'
    ][i],
    description: `Comprehensive analysis and insights for ${['executive leadership', 'sales team', 'marketing department', 'customer success', 'finance team', 'operations', 'product team', 'supply chain', 'HR department', 'strategy team', 'research team', 'compliance team', 'risk management', 'innovation team', 'planning committee'][i]}`,
    type: types[i % types.length],
    template: {
      id: `template-${i + 1}`,
      name: 'Business Template',
      description: 'Standard business report template',
      category: 'business',
      preview_url: '/templates/business.png',
      sections: [],
      layout: {
        format: 'A4',
        orientation: 'portrait',
        margins: { top: 20, right: 20, bottom: 20, left: 20 },
        header: {
          enabled: true,
          height: 60,
          content: 'Company Header',
          styling: {
            font_family: 'Arial',
            font_size: '14px',
            font_weight: 'bold',
            color: '#000000',
            background_color: '#ffffff',
            border: { width: 0, style: 'none', color: '#000000', radius: 0 },
            padding: { top: 10, right: 10, bottom: 10, left: 10 },
            alignment: 'center'
          }
        },
        footer: {
          enabled: true,
          height: 40,
          content: 'Page Footer',
          styling: {
            font_family: 'Arial',
            font_size: '12px',
            font_weight: 'normal',
            color: '#666666',
            background_color: '#ffffff',
            border: { width: 0, style: 'none', color: '#000000', radius: 0 },
            padding: { top: 5, right: 10, bottom: 5, left: 10 },
            alignment: 'center'
          }
        },
        sidebar: {
          enabled: false,
          height: 0,
          content: '',
          styling: {
            font_family: 'Arial',
            font_size: '12px',
            font_weight: 'normal',
            color: '#000000',
            background_color: '#ffffff',
            border: { width: 0, style: 'none', color: '#000000', radius: 0 },
            padding: { top: 0, right: 0, bottom: 0, left: 0 },
            alignment: 'left'
          }
        },
        grid: {
          enabled: true,
          columns: 12,
          rows: 20,
          gap: 10,
          snap_to_grid: true,
          show_grid: false
        },
        responsive: true,
        breakpoints: []
      },
      suggested_data_sources: ['Analytics Database', 'CRM System'],
      recommended_visualizations: ['Bar Chart', 'Line Chart', 'KPI Cards'],
      best_practices: ['Use consistent formatting', 'Include data sources'],
      use_cases: ['Monthly reporting', 'Executive presentations'],
      complexity: 'intermediate',
      estimated_time: '30 minutes',
      required_permissions: ['read_analytics']
    },
    status: statuses[i % statuses.length],
    visibility: ['public', 'private', 'team', 'organization'][Math.floor(Math.random() * 4)] as any,
    sections: [],
    layout: {
      format: 'A4',
      orientation: 'portrait',
      dimensions: { width: 210, height: 297 },
      margins: { top: 20, right: 20, bottom: 20, left: 20 },
      header: {
        enabled: true,
        height: 60,
        content: 'Report Header',
        styling: {
          font_family: 'Arial',
          font_size: '16px',
          font_weight: 'bold',
          color: '#000000',
          background_color: '#f8f9fa',
          border: { width: 1, style: 'solid', color: '#dee2e6', radius: 4 },
          padding: { top: 15, right: 20, bottom: 15, left: 20 },
          alignment: 'center'
        }
      },
      footer: {
        enabled: true,
        height: 40,
        content: 'Generated on {{date}}',
        styling: {
          font_family: 'Arial',
          font_size: '10px',
          font_weight: 'normal',
          color: '#6c757d',
          background_color: '#ffffff',
          border: { width: 0, style: 'none', color: '#000000', radius: 0 },
          padding: { top: 10, right: 20, bottom: 10, left: 20 },
          alignment: 'center'
        }
      },
      page_numbers: {
        enabled: true,
        format: 'numeric',
        position: 'footer',
        alignment: 'right',
        start_page: 1,
        prefix: 'Page ',
        suffix: ''
      },
      table_of_contents: {
        enabled: false,
        position: 'beginning',
        depth: 3,
        include_page_numbers: true,
        styling: {
          font_size: '12px',
          indent_size: 20,
          line_spacing: 1.5,
          dot_leader: true
        }
      },
      watermark: {
        enabled: false,
        text: 'CONFIDENTIAL',
        opacity: 0.1,
        position: 'center',
        rotation: -45,
        size: 72
      }
    },
    styling: {
      theme: 'light',
      color_scheme: 'blue',
      font_family: 'Inter, sans-serif',
      primary_color: '#3b82f6',
      secondary_color: '#6b7280',
      accent_color: '#10b981',
      background_color: '#ffffff',
      text_color: '#1f2937',
      border_color: '#e5e7eb',
      brand_elements: []
    },
    data_sources: [],
    filters: [],
    parameters: [],
    schedules: [],
    distribution: {
      channels: [],
      access_control: {
        public_access: false,
        password_protection: false,
        ip_restrictions: [],
        time_restrictions: [],
        user_permissions: [],
        role_permissions: []
      },
      sharing_settings: {
        allow_public_sharing: true,
        allow_external_sharing: false,
        require_authentication: true,
        link_expiration: 30,
        download_permissions: ['pdf', 'excel'],
        print_permissions: ['all'],
        copy_permissions: ['data']
      },
      embedding: {
        enabled: false,
        domains: [],
        iframe_options: {
          width: '100%',
          height: '600px',
          border: false,
          scrolling: true,
          responsive: true
        },
        api_access: {
          enabled: false,
          rate_limiting: {
            requests_per_minute: 60,
            requests_per_hour: 1000,
            requests_per_day: 10000
          },
          authentication_required: true,
          cors_settings: {
            allowed_origins: ['*'],
            allowed_methods: ['GET', 'POST'],
            allowed_headers: ['Content-Type', 'Authorization'],
            credentials: false
          }
        },
        customization: {
          theme: 'default',
          hide_navigation: false,
          hide_filters: false,
          hide_export: false,
          custom_css: '',
          white_labeling: {
            remove_branding: false,
            custom_logo: '',
            custom_colors: {},
            custom_fonts: []
          }
        }
      }
    },
    automation: {
      triggers: [],
      actions: [],
      workflows: [],
      monitoring: {
        enabled: false,
        metrics: [],
        alerts: [],
        dashboards: []
      }
    },
    insights: [],
    comments: [],
    version: 1,
    created_by: ['John Smith', 'Sarah Johnson', 'Mike Chen', 'Lisa Wilson'][Math.floor(Math.random() * 4)],
    created_at: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
    updated_at: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000),
    last_generated: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000),
    next_generation: new Date(Date.now() + Math.random() * 7 * 24 * 60 * 60 * 1000),
    metadata: {
      tags: ['quarterly', 'executive', 'performance'].slice(0, Math.floor(Math.random() * 3) + 1),
      category: ['Business', 'Financial', 'Operational', 'Strategic'][Math.floor(Math.random() * 4)],
      priority: ['high', 'medium', 'low'][Math.floor(Math.random() * 3)] as any,
      audience: ['executives', 'management', 'stakeholders'].slice(0, Math.floor(Math.random() * 2) + 1),
      business_context: 'Quarterly performance review and strategic planning',
      objectives: ['Track KPI performance', 'Identify trends', 'Support decision making'],
      success_metrics: ['Report adoption rate', 'Decision impact', 'Time to insight'],
      data_quality_score: 0.85 + Math.random() * 0.15,
      compliance_requirements: ['GDPR', 'SOX'].slice(0, Math.floor(Math.random() * 2) + 1),
      security_classification: ['public', 'internal', 'confidential'][Math.floor(Math.random() * 3)] as any
    }
  }));
};

const generateAIInsights = () => {
  return [
    {
      type: 'trend',
      title: 'Revenue Growing 15% QoQ',
      description: 'Strong revenue growth trend detected across all product lines',
      confidence: 0.92,
      relevance: 0.88
    },
    {
      type: 'anomaly',
      title: 'Unusual Traffic Spike on Mobile',
      description: 'Mobile traffic increased 40% above normal patterns',
      confidence: 0.85,
      relevance: 0.75
    },
    {
      type: 'correlation',
      title: 'Marketing Spend vs Conversions',
      description: 'Strong correlation found between social media spend and conversions',
      confidence: 0.78,
      relevance: 0.82
    },
    {
      type: 'prediction',
      title: 'Q1 Revenue Forecast',
      description: 'Predicted 23% revenue increase in Q1 based on current trends',
      confidence: 0.71,
      relevance: 0.90
    },
    {
      type: 'recommendation',
      title: 'Optimize Checkout Flow',
      description: 'Recommendation to simplify checkout process to reduce abandonment',
      confidence: 0.83,
      relevance: 0.79
    }
  ];
};

const STATUS_COLORS = {
  draft: '#6b7280',
  in_progress: '#3b82f6',
  review: '#f59e0b',
  published: '#10b981',
  archived: '#9ca3af'
};

const InteractiveReportBuilder: React.FC = () => {
  const [activeTab, setActiveTab] = useState('builder');
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [currentStep, setCurrentStep] = useState(1);
  const [reportTitle, setReportTitle] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [filterType, setFilterType] = useState<string>('all');

  const templates = useMemo(() => generateReportTemplates(), []);
  const reports = useMemo(() => generateReports(), []);
  const aiInsights = useMemo(() => generateAIInsights(), []);

  const filteredReports = useMemo(() => {
    return reports.filter(report => {
      const matchesSearch = report.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        report.description.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesStatus = filterStatus === 'all' || report.status === filterStatus;
      const matchesType = filterType === 'all' || report.type === filterType;
      return matchesSearch && matchesStatus && matchesType;
    });
  }, [reports, searchTerm, filterStatus, filterType]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'draft': return <Edit className="w-4 h-4" />;
      case 'in_progress': return <Clock className="w-4 h-4" />;
      case 'review': return <Eye className="w-4 h-4" />;
      case 'published': return <CheckCircle className="w-4 h-4" />;
      case 'archived': return <Archive className="w-4 h-4" />;
      default: return <FileText className="w-4 h-4" />;
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'dashboard': return <Layout className="w-4 h-4" />;
      case 'analytical': return <BarChart3 className="w-4 h-4" />;
      case 'executive': return <Target className="w-4 h-4" />;
      case 'operational': return <Activity className="w-4 h-4" />;
      case 'compliance': return <CheckCircle className="w-4 h-4" />;
      case 'custom': return <Wand2 className="w-4 h-4" />;
      default: return <FileText className="w-4 h-4" />;
    }
  };

  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'simple': return 'bg-green-100 text-green-800';
      case 'intermediate': return 'bg-yellow-100 text-yellow-800';
      case 'advanced': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const StepIndicator = ({ step, title, completed }: { step: number; title: string; completed: boolean }) => (
    <div className="flex items-center space-x-3">
      <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
        completed ? 'bg-green-500 text-white' :
        currentStep === step ? 'bg-blue-500 text-white' :
        'bg-gray-200 text-gray-600'
      }`}>
        {completed ? <CheckCircle className="w-4 h-4" /> : step}
      </div>
      <span className={`font-medium ${
        completed || currentStep === step ? 'text-gray-900' : 'text-gray-500'
      }`}>
        {title}
      </span>
    </div>
  );

  return (
    <div className="p-6 max-w-7xl mx-auto space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Interactive Report Builder</h1>
          <p className="text-gray-600 mt-2">Create comprehensive reports with AI-powered insights and guided layouts</p>
        </div>
        <div className="flex items-center space-x-4">
          <Button variant="outline" className="flex items-center space-x-2">
            <Settings className="w-4 h-4" />
            <span>Settings</span>
          </Button>
          <Button className="flex items-center space-x-2">
            <Plus className="w-4 h-4" />
            <span>New Report</span>
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="builder">Report Builder</TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
          <TabsTrigger value="reports">My Reports</TabsTrigger>
          <TabsTrigger value="insights">AI Insights</TabsTrigger>
          <TabsTrigger value="automation">Automation</TabsTrigger>
        </TabsList>

        <TabsContent value="builder" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Wand2 className="w-5 h-5" />
                <span>Guided Report Creation</span>
              </CardTitle>
              <div className="flex items-center justify-between mt-4">
                <div className="flex items-center space-x-8">
                  <StepIndicator step={1} title="Choose Template" completed={false} />
                  <div className="w-8 h-0.5 bg-gray-300" />
                  <StepIndicator step={2} title="Configure Data" completed={false} />
                  <div className="w-8 h-0.5 bg-gray-300" />
                  <StepIndicator step={3} title="Design Layout" completed={false} />
                  <div className="w-8 h-0.5 bg-gray-300" />
                  <StepIndicator step={4} title="Review & Publish" completed={false} />
                </div>
                <div className="text-sm text-gray-500">
                  Step {currentStep} of 4
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {currentStep === 1 && (
                <div className="space-y-6">
                  <div>
                    <h3 className="font-medium text-gray-900 mb-4">Choose a Template</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      {templates.map((template) => (
                        <motion.div
                          key={template.id}
                          whileHover={{ scale: 1.02 }}
                          className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                            selectedTemplate === template.id 
                              ? 'border-blue-500 bg-blue-50' 
                              : 'border-gray-200 hover:border-gray-300'
                          }`}
                          onClick={() => setSelectedTemplate(template.id)}
                        >
                          <div className="aspect-video bg-gray-100 rounded-lg mb-3 flex items-center justify-center">
                            <FileText className="w-8 h-8 text-gray-400" />
                          </div>
                          <h4 className="font-medium text-gray-900">{template.name}</h4>
                          <p className="text-sm text-gray-600 mt-1">{template.description}</p>
                          <div className="flex items-center justify-between mt-3">
                            <Badge className={getComplexityColor(template.complexity)}>
                              {template.complexity}
                            </Badge>
                            <span className="text-xs text-gray-500">{template.estimated_time}</span>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </div>
                  
                  {selectedTemplate && (
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="border border-blue-200 rounded-lg p-4 bg-blue-50"
                    >
                      <h4 className="font-medium text-gray-900 mb-2">Report Details</h4>
                      <div className="space-y-3">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Report Title
                          </label>
                          <Input
                            value={reportTitle}
                            onChange={(e) => setReportTitle(e.target.value)}
                            placeholder="Enter report title..."
                            className="w-full"
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="text-sm text-gray-600">
                            Template: {templates.find(t => t.id === selectedTemplate)?.name}
                          </div>
                          <Button
                            onClick={() => setCurrentStep(2)}
                            disabled={!reportTitle.trim()}
                          >
                            Next: Configure Data
                            <ChevronRight className="w-4 h-4 ml-2" />
                          </Button>
                        </div>
                      </div>
                    </motion.div>
                  )}
                </div>
              )}

              {currentStep === 2 && (
                <div className="space-y-6">
                  <div className="flex items-center justify-between">
                    <h3 className="font-medium text-gray-900">Configure Data Sources</h3>
                    <Button variant="outline" onClick={() => setCurrentStep(1)}>
                      <ChevronLeft className="w-4 h-4 mr-2" />
                      Back
                    </Button>
                  </div>
                  
                  <div className="text-center py-8">
                    <Database className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                    <h4 className="text-lg font-medium text-gray-900 mb-2">Data Source Configuration</h4>
                    <p className="text-gray-600 mb-4">Connect and configure your data sources for the report</p>
                    <div className="flex items-center justify-center space-x-4">
                      <Button variant="outline">
                        <Plus className="w-4 h-4 mr-2" />
                        Add Data Source
                      </Button>
                      <Button onClick={() => setCurrentStep(3)}>
                        Next: Design Layout
                        <ChevronRight className="w-4 h-4 ml-2" />
                      </Button>
                    </div>
                  </div>
                </div>
              )}

              {currentStep === 3 && (
                <div className="space-y-6">
                  <div className="flex items-center justify-between">
                    <h3 className="font-medium text-gray-900">Design Layout</h3>
                    <Button variant="outline" onClick={() => setCurrentStep(2)}>
                      <ChevronLeft className="w-4 h-4 mr-2" />
                      Back
                    </Button>
                  </div>
                  
                  <div className="text-center py-8">
                    <Layout className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                    <h4 className="text-lg font-medium text-gray-900 mb-2">Layout Designer</h4>
                    <p className="text-gray-600 mb-4">Customize the layout and styling of your report</p>
                    <div className="flex items-center justify-center space-x-4">
                      <Button variant="outline">
                        <Palette className="w-4 h-4 mr-2" />
                        Customize Styling
                      </Button>
                      <Button onClick={() => setCurrentStep(4)}>
                        Next: Review & Publish
                        <ChevronRight className="w-4 h-4 ml-2" />
                      </Button>
                    </div>
                  </div>
                </div>
              )}

              {currentStep === 4 && (
                <div className="space-y-6">
                  <div className="flex items-center justify-between">
                    <h3 className="font-medium text-gray-900">Review & Publish</h3>
                    <Button variant="outline" onClick={() => setCurrentStep(3)}>
                      <ChevronLeft className="w-4 h-4 mr-2" />
                      Back
                    </Button>
                  </div>
                  
                  <div className="text-center py-8">
                    <CheckCircle className="w-16 h-16 text-green-400 mx-auto mb-4" />
                    <h4 className="text-lg font-medium text-gray-900 mb-2">Ready to Publish</h4>
                    <p className="text-gray-600 mb-4">Review your report and publish when ready</p>
                    <div className="flex items-center justify-center space-x-4">
                      <Button variant="outline">
                        <Eye className="w-4 h-4 mr-2" />
                        Preview Report
                      </Button>
                      <Button className="bg-green-600 hover:bg-green-700">
                        <CheckCircle className="w-4 h-4 mr-2" />
                        Publish Report
                      </Button>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="templates" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Report Templates</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {templates.map((template) => (
                  <motion.div
                    key={template.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow"
                  >
                    <div className="aspect-video bg-gray-100 rounded-lg mb-4 flex items-center justify-center">
                      <FileText className="w-12 h-12 text-gray-400" />
                    </div>
                    <h3 className="font-semibold text-gray-900 mb-2">{template.name}</h3>
                    <p className="text-sm text-gray-600 mb-4">{template.description}</p>
                    <div className="flex items-center justify-between mb-4">
                      <Badge className={getComplexityColor(template.complexity)}>
                        {template.complexity}
                      </Badge>
                      <span className="text-xs text-gray-500">{template.estimated_time}</span>
                    </div>
                    <div className="flex space-x-2">
                      <Button size="sm" className="flex-1">
                        <Eye className="w-3 h-3 mr-1" />
                        Preview
                      </Button>
                      <Button size="sm" variant="outline" className="flex-1">
                        <Plus className="w-3 h-3 mr-1" />
                        Use Template
                      </Button>
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reports" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>My Reports</CardTitle>
              <div className="flex items-center space-x-4 mt-4">
                <div className="flex-1">
                  <Input
                    placeholder="Search reports..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="max-w-sm"
                  />
                </div>
                <select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  className="border border-gray-300 rounded-md px-3 py-2"
                >
                  <option value="all">All Statuses</option>
                  <option value="draft">Draft</option>
                  <option value="in_progress">In Progress</option>
                  <option value="review">Review</option>
                  <option value="published">Published</option>
                  <option value="archived">Archived</option>
                </select>
                <select
                  value={filterType}
                  onChange={(e) => setFilterType(e.target.value)}
                  className="border border-gray-300 rounded-md px-3 py-2"
                >
                  <option value="all">All Types</option>
                  <option value="dashboard">Dashboard</option>
                  <option value="analytical">Analytical</option>
                  <option value="executive">Executive</option>
                  <option value="operational">Operational</option>
                  <option value="compliance">Compliance</option>
                  <option value="custom">Custom</option>
                </select>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredReports.map((report) => (
                  <motion.div
                    key={report.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center space-x-2">
                          <Badge 
                            style={{ backgroundColor: STATUS_COLORS[report.status] }}
                            className="text-white"
                          >
                            {getStatusIcon(report.status)}
                            <span className="ml-1">{report.status.replace('_', ' ').toUpperCase()}</span>
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            {getTypeIcon(report.type)}
                            <span className="ml-1">{report.type}</span>
                          </Badge>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-xs text-gray-500">
                          Updated {report.updated_at.toLocaleDateString()}
                        </span>
                        <Button variant="outline" size="sm">
                          <MoreHorizontal className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                    <div className="mt-2">
                      <h4 className="font-medium text-gray-900">{report.title}</h4>
                      <p className="text-sm text-gray-600 mt-1">{report.description}</p>
                    </div>
                    <div className="mt-3 flex items-center justify-between">
                      <div className="flex items-center space-x-4 text-sm text-gray-600">
                        <span>Created by {report.created_by}</span>
                        <span>•</span>
                        <span>Version {report.version}</span>
                        <span>•</span>
                        <span className="capitalize">{report.visibility}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button variant="outline" size="sm">
                          <Edit className="w-4 h-4 mr-1" />
                          Edit
                        </Button>
                        <Button variant="outline" size="sm">
                          <Eye className="w-4 h-4 mr-1" />
                          View
                        </Button>
                        <Button variant="outline" size="sm">
                          <Share className="w-4 h-4 mr-1" />
                          Share
                        </Button>
                      </div>
                    </div>
                    <div className="mt-2 flex items-center space-x-1">
                      {report.metadata.tags.map((tag) => (
                        <Badge key={tag} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="insights" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Brain className="w-5 h-5" />
                <span>AI-Powered Insights</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {aiInsights.map((insight, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    delay={index * 0.1}
                    className="border border-gray-200 rounded-lg p-6"
                  >
                    <div className="flex items-center justify-between mb-3">
                      <Badge 
                        variant={insight.type === 'recommendation' ? 'default' : 'secondary'}
                        className="capitalize"
                      >
                        {insight.type === 'trend' && <TrendingUp className="w-3 h-3 mr-1" />}
                        {insight.type === 'anomaly' && <AlertTriangle className="w-3 h-3 mr-1" />}
                        {insight.type === 'correlation' && <Activity className="w-3 h-3 mr-1" />}
                        {insight.type === 'prediction' && <Target className="w-3 h-3 mr-1" />}
                        {insight.type === 'recommendation' && <Lightbulb className="w-3 h-3 mr-1" />}
                        {insight.type}
                      </Badge>
                      <div className="flex items-center space-x-1">
                        <span className="text-xs text-gray-500">
                          {(insight.confidence * 100).toFixed(0)}% confidence
                        </span>
                        <div className={`w-2 h-2 rounded-full ${
                          insight.confidence >= 0.8 ? 'bg-green-500' :
                          insight.confidence >= 0.6 ? 'bg-yellow-500' : 'bg-red-500'
                        }`} />
                      </div>
                    </div>
                    <h4 className="font-medium text-gray-900 mb-2">{insight.title}</h4>
                    <p className="text-sm text-gray-600 mb-4">{insight.description}</p>
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-gray-500">
                        Relevance: {(insight.relevance * 100).toFixed(0)}%
                      </span>
                      <Button variant="outline" size="sm">
                        <Plus className="w-3 h-3 mr-1" />
                        Add to Report
                      </Button>
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="automation" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Report Automation</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Activity className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Automation Center</h3>
                <p className="text-gray-600 mb-4">
                  Set up automated report generation, scheduling, and distribution
                </p>
                <Button>
                  <Plus className="w-4 h-4 mr-2" />
                  Create Automation
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default InteractiveReportBuilder;