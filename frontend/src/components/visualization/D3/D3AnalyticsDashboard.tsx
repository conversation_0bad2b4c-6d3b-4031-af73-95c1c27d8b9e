import React, { useEffect, useRef, useState, useMemo } from 'react';
import * as d3 from 'd3';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';

// Types for D3 analytics
interface D3DataPoint {
  id: string;
  value: number;
  category: string;
  label: string;
  date?: Date;
  metadata?: Record<string, any>;
  color?: string;
}

interface D3ChartData {
  type: 'line' | 'bar' | 'pie' | 'scatter' | 'area' | 'heatmap';
  data: D3DataPoint[];
  title: string;
  xAxis?: string;
  yAxis?: string;
  width?: number;
  height?: number;
}

interface D3AnalyticsDashboardProps {
  charts: D3ChartData[];
  realTimeData?: boolean;
  interactive?: boolean;
  onDataInteraction?: (chart: D3ChartData, point: D3DataPoint) => void;
}

// Line Chart Component
const D3LineChart: React.FC<{
  data: D3DataPoint[];
  width: number;
  height: number;
  title: string;
  onPointClick?: (point: D3DataPoint) => void;
}> = ({ data, width, height, title, onPointClick }) => {
  const svgRef = useRef<SVGSVGElement>(null);

  useEffect(() => {
    if (!svgRef.current || !data.length) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove();

    const margin = { top: 20, right: 30, bottom: 40, left: 50 };
    const innerWidth = width - margin.left - margin.right;
    const innerHeight = height - margin.top - margin.bottom;

    const g = svg
      .append('g')
      .attr('transform', `translate(${margin.left},${margin.top})`);

    // Scales
    const xScale = d3
      .scaleTime()
      .domain(d3.extent(data, d => d.date || new Date()) as [Date, Date])
      .range([0, innerWidth]);

    const yScale = d3
      .scaleLinear()
      .domain(d3.extent(data, d => d.value) as [number, number])
      .nice()
      .range([innerHeight, 0]);

    // Line generator
    const line = d3
      .line<D3DataPoint>()
      .x(d => xScale(d.date || new Date()))
      .y(d => yScale(d.value))
      .curve(d3.curveMonotoneX);

    // Add axes
    g.append('g')
      .attr('transform', `translate(0,${innerHeight})`)
      .call(d3.axisBottom(xScale).tickFormat(d3.timeFormat('%m/%d')));

    g.append('g')
      .call(d3.axisLeft(yScale));

    // Add line
    g.append('path')
      .datum(data)
      .attr('fill', 'none')
      .attr('stroke', '#3b82f6')
      .attr('stroke-width', 2)
      .attr('d', line);

    // Add points
    g.selectAll('.dot')
      .data(data)
      .enter()
      .append('circle')
      .attr('class', 'dot')
      .attr('cx', d => xScale(d.date || new Date()))
      .attr('cy', d => yScale(d.value))
      .attr('r', 4)
      .attr('fill', '#3b82f6')
      .style('cursor', 'pointer')
      .on('click', (event, d) => onPointClick?.(d))
      .on('mouseover', function(event, d) {
        d3.select(this).attr('r', 6);
        
        // Tooltip
        const tooltip = d3.select('body')
          .append('div')
          .attr('class', 'tooltip')
          .style('position', 'absolute')
          .style('background', 'rgba(0, 0, 0, 0.8)')
          .style('color', 'white')
          .style('padding', '8px')
          .style('border-radius', '4px')
          .style('font-size', '12px')
          .style('pointer-events', 'none')
          .style('opacity', 0);

        tooltip.transition().duration(200).style('opacity', 1);
        tooltip.html(`${d.label}: ${d.value}`)
          .style('left', (event.pageX + 10) + 'px')
          .style('top', (event.pageY - 10) + 'px');
      })
      .on('mouseout', function() {
        d3.select(this).attr('r', 4);
        d3.selectAll('.tooltip').remove();
      });

  }, [data, width, height, onPointClick]);

  return (
    <div className="w-full">
      <h3 className="text-lg font-semibold mb-2">{title}</h3>
      <svg ref={svgRef} width={width} height={height} />
    </div>
  );
};

// Bar Chart Component
const D3BarChart: React.FC<{
  data: D3DataPoint[];
  width: number;
  height: number;
  title: string;
  onBarClick?: (point: D3DataPoint) => void;
}> = ({ data, width, height, title, onBarClick }) => {
  const svgRef = useRef<SVGSVGElement>(null);

  useEffect(() => {
    if (!svgRef.current || !data.length) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove();

    const margin = { top: 20, right: 30, bottom: 40, left: 50 };
    const innerWidth = width - margin.left - margin.right;
    const innerHeight = height - margin.top - margin.bottom;

    const g = svg
      .append('g')
      .attr('transform', `translate(${margin.left},${margin.top})`);

    // Scales
    const xScale = d3
      .scaleBand()
      .domain(data.map(d => d.label))
      .range([0, innerWidth])
      .padding(0.1);

    const yScale = d3
      .scaleLinear()
      .domain([0, d3.max(data, d => d.value) || 0])
      .nice()
      .range([innerHeight, 0]);

    // Add axes
    g.append('g')
      .attr('transform', `translate(0,${innerHeight})`)
      .call(d3.axisBottom(xScale));

    g.append('g')
      .call(d3.axisLeft(yScale));

    // Add bars
    g.selectAll('.bar')
      .data(data)
      .enter()
      .append('rect')
      .attr('class', 'bar')
      .attr('x', d => xScale(d.label) || 0)
      .attr('width', xScale.bandwidth())
      .attr('y', d => yScale(d.value))
      .attr('height', d => innerHeight - yScale(d.value))
      .attr('fill', d => d.color || '#3b82f6')
      .style('cursor', 'pointer')
      .on('click', (event, d) => onBarClick?.(d))
      .on('mouseover', function(event, d) {
        d3.select(this).attr('opacity', 0.8);
        
        // Tooltip
        const tooltip = d3.select('body')
          .append('div')
          .attr('class', 'tooltip')
          .style('position', 'absolute')
          .style('background', 'rgba(0, 0, 0, 0.8)')
          .style('color', 'white')
          .style('padding', '8px')
          .style('border-radius', '4px')
          .style('font-size', '12px')
          .style('pointer-events', 'none')
          .style('opacity', 0);

        tooltip.transition().duration(200).style('opacity', 1);
        tooltip.html(`${d.label}: ${d.value}`)
          .style('left', (event.pageX + 10) + 'px')
          .style('top', (event.pageY - 10) + 'px');
      })
      .on('mouseout', function() {
        d3.select(this).attr('opacity', 1);
        d3.selectAll('.tooltip').remove();
      });

  }, [data, width, height, onBarClick]);

  return (
    <div className="w-full">
      <h3 className="text-lg font-semibold mb-2">{title}</h3>
      <svg ref={svgRef} width={width} height={height} />
    </div>
  );
};

// Pie Chart Component
const D3PieChart: React.FC<{
  data: D3DataPoint[];
  width: number;
  height: number;
  title: string;
  onSliceClick?: (point: D3DataPoint) => void;
}> = ({ data, width, height, title, onSliceClick }) => {
  const svgRef = useRef<SVGSVGElement>(null);

  useEffect(() => {
    if (!svgRef.current || !data.length) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove();

    const radius = Math.min(width, height) / 2 - 20;
    const g = svg
      .append('g')
      .attr('transform', `translate(${width / 2},${height / 2})`);

    const color = d3.scaleOrdinal(d3.schemeCategory10);

    const pie = d3.pie<D3DataPoint>()
      .value(d => d.value)
      .sort(null);

    const arc = d3.arc<d3.PieArcDatum<D3DataPoint>>()
      .innerRadius(0)
      .outerRadius(radius);

    const arcs = g.selectAll('.arc')
      .data(pie(data))
      .enter()
      .append('g')
      .attr('class', 'arc');

    arcs.append('path')
      .attr('d', arc)
      .attr('fill', (d, i) => d.data.color || color(i.toString()))
      .style('cursor', 'pointer')
      .on('click', (event, d) => onSliceClick?.(d.data))
      .on('mouseover', function(event, d) {
        d3.select(this).attr('opacity', 0.8);
        
        // Tooltip
        const tooltip = d3.select('body')
          .append('div')
          .attr('class', 'tooltip')
          .style('position', 'absolute')
          .style('background', 'rgba(0, 0, 0, 0.8)')
          .style('color', 'white')
          .style('padding', '8px')
          .style('border-radius', '4px')
          .style('font-size', '12px')
          .style('pointer-events', 'none')
          .style('opacity', 0);

        tooltip.transition().duration(200).style('opacity', 1);
        tooltip.html(`${d.data.label}: ${d.data.value}`)
          .style('left', (event.pageX + 10) + 'px')
          .style('top', (event.pageY - 10) + 'px');
      })
      .on('mouseout', function() {
        d3.select(this).attr('opacity', 1);
        d3.selectAll('.tooltip').remove();
      });

    arcs.append('text')
      .attr('transform', d => `translate(${arc.centroid(d)})`)
      .attr('dy', '0.35em')
      .style('text-anchor', 'middle')
      .style('font-size', '12px')
      .text(d => d.data.label);

  }, [data, width, height, onSliceClick]);

  return (
    <div className="w-full">
      <h3 className="text-lg font-semibold mb-2">{title}</h3>
      <svg ref={svgRef} width={width} height={height} />
    </div>
  );
};

// Main D3 Analytics Dashboard Component
const D3AnalyticsDashboard: React.FC<D3AnalyticsDashboardProps> = ({
  charts,
  realTimeData = false,
  interactive = true,
  onDataInteraction
}) => {
  const [selectedChart, setSelectedChart] = useState<string | null>(null);

  const handleDataInteraction = (chart: D3ChartData, point: D3DataPoint) => {
    if (interactive && onDataInteraction) {
      onDataInteraction(chart, point);
    }
  };

  const renderChart = (chart: D3ChartData) => {
    const width = chart.width || 400;
    const height = chart.height || 300;

    const commonProps = {
      data: chart.data,
      width,
      height,
      title: chart.title,
    };

    switch (chart.type) {
      case 'line':
        return (
          <D3LineChart
            {...commonProps}
            onPointClick={(point) => handleDataInteraction(chart, point)}
          />
        );
      case 'bar':
        return (
          <D3BarChart
            {...commonProps}
            onBarClick={(point) => handleDataInteraction(chart, point)}
          />
        );
      case 'pie':
        return (
          <D3PieChart
            {...commonProps}
            onSliceClick={(point) => handleDataInteraction(chart, point)}
          />
        );
      default:
        return <div>Unsupported chart type: {chart.type}</div>;
    }
  };

  return (
    <div className="w-full space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Analytics Dashboard</h2>
        <div className="flex items-center space-x-2">
          {realTimeData && (
            <Badge variant="outline" className="text-green-600">
              Real-time
            </Badge>
          )}
          {interactive && (
            <Badge variant="outline" className="text-blue-600">
              Interactive
            </Badge>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {charts.map((chart, index) => (
          <Card key={index} className="p-4">
            <CardContent>
              {renderChart(chart)}
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default D3AnalyticsDashboard;
