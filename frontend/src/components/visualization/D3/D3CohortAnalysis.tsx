import React, { useEffect, useRef, useState } from 'react';
import * as d3 from 'd3';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

// Types for cohort analysis
interface CohortData {
  cohortMonth: string;
  period: number;
  customers: number;
  revenue: number;
  retentionRate: number;
}

interface D3CohortAnalysisProps {
  data: CohortData[];
  metric?: 'customers' | 'revenue' | 'retentionRate';
  onCellClick?: (cohort: CohortData) => void;
}

// Cohort Heatmap Component
const D3CohortHeatmap: React.FC<{
  data: CohortData[];
  metric: 'customers' | 'revenue' | 'retentionRate';
  width: number;
  height: number;
  onCellClick?: (cohort: CohortData) => void;
}> = ({ data, metric, width, height, onCellClick }) => {
  const svgRef = useRef<SVGSVGElement>(null);

  useEffect(() => {
    if (!svgRef.current || !data.length) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove();

    const margin = { top: 60, right: 20, bottom: 40, left: 100 };
    const innerWidth = width - margin.left - margin.right;
    const innerHeight = height - margin.top - margin.bottom;

    const g = svg
      .append('g')
      .attr('transform', `translate(${margin.left},${margin.top})`);

    // Prepare data matrix
    const cohorts = Array.from(new Set(data.map(d => d.cohortMonth))).sort();
    const maxPeriod = d3.max(data, d => d.period) || 0;
    const periods = Array.from({ length: maxPeriod + 1 }, (_, i) => i);

    // Create matrix
    const matrix: (CohortData | null)[][] = cohorts.map(cohort => 
      periods.map(period => 
        data.find(d => d.cohortMonth === cohort && d.period === period) || null
      )
    );

    // Scales
    const xScale = d3.scaleBand()
      .domain(periods.map(String))
      .range([0, innerWidth])
      .padding(0.05);

    const yScale = d3.scaleBand()
      .domain(cohorts)
      .range([0, innerHeight])
      .padding(0.05);

    // Color scale based on metric
    let colorScale: d3.ScaleSequential<string>;
    let maxValue: number;

    switch (metric) {
      case 'customers':
        maxValue = d3.max(data, d => d.customers) || 0;
        colorScale = d3.scaleSequential(d3.interpolateBlues)
          .domain([0, maxValue]);
        break;
      case 'revenue':
        maxValue = d3.max(data, d => d.revenue) || 0;
        colorScale = d3.scaleSequential(d3.interpolateGreens)
          .domain([0, maxValue]);
        break;
      case 'retentionRate':
        colorScale = d3.scaleSequential(d3.interpolateReds)
          .domain([0, 1]);
        break;
      default:
        maxValue = d3.max(data, d => d.customers) || 0;
        colorScale = d3.scaleSequential(d3.interpolateBlues)
          .domain([0, maxValue]);
    }

    // Add cells
    matrix.forEach((cohortData, cohortIndex) => {
      cohortData.forEach((cellData, periodIndex) => {
        if (!cellData) return;

        let cellValue: number;
        let displayValue: string;

        switch (metric) {
          case 'customers':
            cellValue = cellData.customers;
            displayValue = cellValue.toString();
            break;
          case 'revenue':
            cellValue = cellData.revenue;
            displayValue = `$${(cellValue / 1000).toFixed(0)}k`;
            break;
          case 'retentionRate':
            cellValue = cellData.retentionRate;
            displayValue = `${(cellValue * 100).toFixed(0)}%`;
            break;
          default:
            cellValue = cellData.customers;
            displayValue = cellValue.toString();
        }

        const cell = g.append('g')
          .attr('class', 'cohort-cell');

        // Cell rectangle
        cell.append('rect')
          .attr('x', xScale(periodIndex.toString()) || 0)
          .attr('y', yScale(cohorts[cohortIndex]) || 0)
          .attr('width', xScale.bandwidth())
          .attr('height', yScale.bandwidth())
          .attr('fill', colorScale(cellValue))
          .attr('stroke', '#fff')
          .attr('stroke-width', 1)
          .style('cursor', 'pointer')
          .on('click', () => onCellClick?.(cellData))
          .on('mouseover', function(event) {
            d3.select(this).attr('stroke-width', 3).attr('stroke', '#333');
            
            // Tooltip
            const tooltip = d3.select('body')
              .append('div')
              .attr('class', 'tooltip')
              .style('position', 'absolute')
              .style('background', 'rgba(0, 0, 0, 0.8)')
              .style('color', 'white')
              .style('padding', '8px')
              .style('border-radius', '4px')
              .style('font-size', '12px')
              .style('pointer-events', 'none')
              .style('opacity', 0);

            tooltip.transition().duration(200).style('opacity', 1);
            tooltip.html(`
              Cohort: ${cellData.cohortMonth}<br/>
              Period: ${cellData.period}<br/>
              Customers: ${cellData.customers}<br/>
              Revenue: $${cellData.revenue.toLocaleString()}<br/>
              Retention: ${(cellData.retentionRate * 100).toFixed(1)}%
            `)
              .style('left', (event.pageX + 10) + 'px')
              .style('top', (event.pageY - 10) + 'px');
          })
          .on('mouseout', function() {
            d3.select(this).attr('stroke-width', 1).attr('stroke', '#fff');
            d3.selectAll('.tooltip').remove();
          });

        // Cell text
        if (xScale.bandwidth() > 40 && yScale.bandwidth() > 20) {
          cell.append('text')
            .attr('x', (xScale(periodIndex.toString()) || 0) + xScale.bandwidth() / 2)
            .attr('y', (yScale(cohorts[cohortIndex]) || 0) + yScale.bandwidth() / 2)
            .attr('dy', '0.35em')
            .attr('text-anchor', 'middle')
            .style('font-size', '10px')
            .style('font-weight', 'bold')
            .style('fill', cellValue > maxValue * 0.5 ? 'white' : 'black')
            .text(displayValue);
        }
      });
    });

    // Add axes
    g.append('g')
      .attr('transform', `translate(0,${innerHeight})`)
      .call(d3.axisBottom(xScale))
      .append('text')
      .attr('x', innerWidth / 2)
      .attr('y', 35)
      .attr('text-anchor', 'middle')
      .style('font-size', '12px')
      .style('font-weight', 'bold')
      .text('Period (Months)');

    g.append('g')
      .call(d3.axisLeft(yScale))
      .append('text')
      .attr('transform', 'rotate(-90)')
      .attr('y', -80)
      .attr('x', -innerHeight / 2)
      .attr('text-anchor', 'middle')
      .style('font-size', '12px')
      .style('font-weight', 'bold')
      .text('Cohort Month');

    // Add title
    g.append('text')
      .attr('x', innerWidth / 2)
      .attr('y', -30)
      .attr('text-anchor', 'middle')
      .style('font-size', '14px')
      .style('font-weight', 'bold')
      .text(`Cohort Analysis - ${metric.charAt(0).toUpperCase() + metric.slice(1)}`);

    // Add color legend
    const legendWidth = 200;
    const legendHeight = 10;
    const legend = g.append('g')
      .attr('class', 'legend')
      .attr('transform', `translate(${innerWidth - legendWidth}, -50)`);

    const legendScale = d3.scaleLinear()
      .domain(colorScale.domain())
      .range([0, legendWidth]);

    const legendAxis = d3.axisBottom(legendScale)
      .ticks(5)
      .tickFormat(metric === 'retentionRate' ? d3.format('.0%') : d3.format('.0s'));

    // Legend gradient
    const defs = svg.append('defs');
    const gradient = defs.append('linearGradient')
      .attr('id', 'legend-gradient')
      .attr('x1', '0%')
      .attr('x2', '100%')
      .attr('y1', '0%')
      .attr('y2', '0%');

    gradient.selectAll('stop')
      .data(d3.range(0, 1.1, 0.1))
      .enter()
      .append('stop')
      .attr('offset', d => `${d * 100}%`)
      .attr('stop-color', d => colorScale(d * (colorScale.domain()[1] - colorScale.domain()[0]) + colorScale.domain()[0]));

    legend.append('rect')
      .attr('width', legendWidth)
      .attr('height', legendHeight)
      .style('fill', 'url(#legend-gradient)');

    legend.append('g')
      .attr('transform', `translate(0, ${legendHeight})`)
      .call(legendAxis);

  }, [data, metric, width, height, onCellClick]);

  return <svg ref={svgRef} width={width} height={height} />;
};

// Cohort Retention Curve
const D3RetentionCurve: React.FC<{
  data: CohortData[];
  width: number;
  height: number;
}> = ({ data, width, height }) => {
  const svgRef = useRef<SVGSVGElement>(null);

  useEffect(() => {
    if (!svgRef.current || !data.length) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove();

    const margin = { top: 20, right: 30, bottom: 40, left: 50 };
    const innerWidth = width - margin.left - margin.right;
    const innerHeight = height - margin.top - margin.bottom;

    const g = svg
      .append('g')
      .attr('transform', `translate(${margin.left},${margin.top})`);

    // Group data by cohort
    const cohortGroups = d3.group(data, d => d.cohortMonth);
    const cohorts = Array.from(cohortGroups.keys()).sort();

    // Scales
    const xScale = d3.scaleLinear()
      .domain([0, d3.max(data, d => d.period) || 0])
      .range([0, innerWidth]);

    const yScale = d3.scaleLinear()
      .domain([0, 1])
      .range([innerHeight, 0]);

    const colorScale = d3.scaleOrdinal(d3.schemeCategory10)
      .domain(cohorts);

    // Line generator
    const line = d3.line<CohortData>()
      .x(d => xScale(d.period))
      .y(d => yScale(d.retentionRate))
      .curve(d3.curveMonotoneX);

    // Add axes
    g.append('g')
      .attr('transform', `translate(0,${innerHeight})`)
      .call(d3.axisBottom(xScale));

    g.append('g')
      .call(d3.axisLeft(yScale).tickFormat(d3.format('.0%')));

    // Add lines for each cohort
    cohorts.forEach(cohort => {
      const cohortData = cohortGroups.get(cohort) || [];
      
      g.append('path')
        .datum(cohortData)
        .attr('fill', 'none')
        .attr('stroke', colorScale(cohort) as string)
        .attr('stroke-width', 2)
        .attr('d', line);

      // Add points
      g.selectAll(`.point-${cohort}`)
        .data(cohortData)
        .enter()
        .append('circle')
        .attr('class', `point-${cohort}`)
        .attr('cx', d => xScale(d.period))
        .attr('cy', d => yScale(d.retentionRate))
        .attr('r', 3)
        .attr('fill', colorScale(cohort) as string);
    });

    // Add legend
    const legend = g.append('g')
      .attr('class', 'legend')
      .attr('transform', `translate(${innerWidth - 100}, 20)`);

    const legendItems = legend.selectAll('.legend-item')
      .data(cohorts.slice(0, 5)) // Show only first 5 cohorts
      .enter()
      .append('g')
      .attr('class', 'legend-item')
      .attr('transform', (d, i) => `translate(0, ${i * 20})`);

    legendItems.append('line')
      .attr('x1', 0)
      .attr('x2', 15)
      .attr('stroke', d => colorScale(d) as string)
      .attr('stroke-width', 2);

    legendItems.append('text')
      .attr('x', 20)
      .attr('y', 5)
      .style('font-size', '10px')
      .text(d => d);

  }, [data, width, height]);

  return <svg ref={svgRef} width={width} height={height} />;
};

// Main Cohort Analysis Component
const D3CohortAnalysis: React.FC<D3CohortAnalysisProps> = ({
  data,
  metric = 'retentionRate',
  onCellClick
}) => {
  const [selectedMetric, setSelectedMetric] = useState(metric);
  const [viewMode, setViewMode] = useState<'heatmap' | 'curve'>('heatmap');

  const metrics = [
    { id: 'customers', name: 'Customers', description: 'Number of customers' },
    { id: 'revenue', name: 'Revenue', description: 'Revenue generated' },
    { id: 'retentionRate', name: 'Retention Rate', description: 'Customer retention percentage' }
  ];

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Cohort Analysis</CardTitle>
          <div className="flex space-x-2">
            <button
              onClick={() => setViewMode('heatmap')}
              className={`px-3 py-1 rounded ${viewMode === 'heatmap' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
            >
              Heatmap
            </button>
            <button
              onClick={() => setViewMode('curve')}
              className={`px-3 py-1 rounded ${viewMode === 'curve' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
            >
              Retention Curve
            </button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {viewMode === 'heatmap' && (
          <div className="space-y-4">
            <div className="flex space-x-2">
              {metrics.map(m => (
                <button
                  key={m.id}
                  onClick={() => setSelectedMetric(m.id as any)}
                  className={`px-3 py-2 rounded text-sm ${
                    selectedMetric === m.id 
                      ? 'bg-blue-500 text-white' 
                      : 'bg-gray-100 hover:bg-gray-200'
                  }`}
                >
                  <div className="font-medium">{m.name}</div>
                  <div className="text-xs opacity-75">{m.description}</div>
                </button>
              ))}
            </div>
            <D3CohortHeatmap
              data={data}
              metric={selectedMetric}
              width={800}
              height={400}
              onCellClick={onCellClick}
            />
          </div>
        )}
        
        {viewMode === 'curve' && (
          <D3RetentionCurve
            data={data}
            width={800}
            height={400}
          />
        )}
      </CardContent>
    </Card>
  );
};

export default D3CohortAnalysis;
