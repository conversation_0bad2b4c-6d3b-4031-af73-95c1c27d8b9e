import React, { useEffect, useRef, useState } from 'react';
import * as d3 from 'd3';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

// Types for customer journey
interface JourneyNode {
  id: string;
  name: string;
  type: 'touchpoint' | 'conversion' | 'exit';
  value: number;
  stage: 'awareness' | 'interest' | 'consideration' | 'intent' | 'purchase' | 'retention';
  x?: number;
  y?: number;
}

interface JourneyLink {
  source: string;
  target: string;
  value: number;
  type: 'flow' | 'conversion' | 'dropout';
}

interface CustomerJourneyData {
  nodes: JourneyNode[];
  links: JourneyLink[];
}

interface D3CustomerJourneyProps {
  data: CustomerJourneyData;
  width?: number;
  height?: number;
  onNodeClick?: (node: JourneyNode) => void;
  onLinkClick?: (link: JourneyLink) => void;
}

// Sankey Diagram for Customer Journey Flow
const D3SankeyJourney: React.FC<{
  data: CustomerJourneyData;
  width: number;
  height: number;
  onNodeClick?: (node: JourneyNode) => void;
}> = ({ data, width, height, onNodeClick }) => {
  const svgRef = useRef<SVGSVGElement>(null);

  useEffect(() => {
    if (!svgRef.current || !data.nodes.length) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove();

    const margin = { top: 20, right: 20, bottom: 20, left: 20 };
    const innerWidth = width - margin.left - margin.right;
    const innerHeight = height - margin.top - margin.bottom;

    const g = svg
      .append('g')
      .attr('transform', `translate(${margin.left},${margin.top})`);

    // Create a simple flow layout (simplified Sankey)
    const stages = ['awareness', 'interest', 'consideration', 'intent', 'purchase', 'retention'];
    const stageWidth = innerWidth / stages.length;
    
    // Position nodes by stage
    const nodesByStage = d3.group(data.nodes, d => d.stage);
    
    data.nodes.forEach((node, i) => {
      const stageIndex = stages.indexOf(node.stage);
      const nodesInStage = nodesByStage.get(node.stage) || [];
      const nodeIndex = nodesInStage.indexOf(node);
      
      node.x = stageIndex * stageWidth + stageWidth / 2;
      node.y = (nodeIndex + 1) * (innerHeight / (nodesInStage.length + 1));
    });

    // Color scale for stages
    const colorScale = d3.scaleOrdinal()
      .domain(stages)
      .range(['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3']);

    // Draw links
    const linkGenerator = d3.linkHorizontal<any, JourneyNode>()
      .x(d => d.x || 0)
      .y(d => d.y || 0);

    g.selectAll('.link')
      .data(data.links)
      .enter()
      .append('path')
      .attr('class', 'link')
      .attr('d', d => {
        const source = data.nodes.find(n => n.id === d.source);
        const target = data.nodes.find(n => n.id === d.target);
        if (!source || !target) return '';
        return linkGenerator({ source, target });
      })
      .attr('stroke', '#999')
      .attr('stroke-width', d => Math.max(1, d.value / 10))
      .attr('fill', 'none')
      .attr('opacity', 0.6);

    // Draw nodes
    g.selectAll('.node')
      .data(data.nodes)
      .enter()
      .append('circle')
      .attr('class', 'node')
      .attr('cx', d => d.x || 0)
      .attr('cy', d => d.y || 0)
      .attr('r', d => Math.max(8, Math.sqrt(d.value) * 2))
      .attr('fill', d => colorScale(d.stage) as string)
      .attr('stroke', '#fff')
      .attr('stroke-width', 2)
      .style('cursor', 'pointer')
      .on('click', (event, d) => onNodeClick?.(d))
      .on('mouseover', function(event, d) {
        d3.select(this).attr('stroke-width', 4);
        
        // Tooltip
        const tooltip = d3.select('body')
          .append('div')
          .attr('class', 'tooltip')
          .style('position', 'absolute')
          .style('background', 'rgba(0, 0, 0, 0.8)')
          .style('color', 'white')
          .style('padding', '8px')
          .style('border-radius', '4px')
          .style('font-size', '12px')
          .style('pointer-events', 'none')
          .style('opacity', 0);

        tooltip.transition().duration(200).style('opacity', 1);
        tooltip.html(`${d.name}<br/>Stage: ${d.stage}<br/>Value: ${d.value}`)
          .style('left', (event.pageX + 10) + 'px')
          .style('top', (event.pageY - 10) + 'px');
      })
      .on('mouseout', function() {
        d3.select(this).attr('stroke-width', 2);
        d3.selectAll('.tooltip').remove();
      });

    // Add node labels
    g.selectAll('.node-label')
      .data(data.nodes)
      .enter()
      .append('text')
      .attr('class', 'node-label')
      .attr('x', d => d.x || 0)
      .attr('y', d => (d.y || 0) - 15)
      .attr('text-anchor', 'middle')
      .style('font-size', '10px')
      .style('font-weight', 'bold')
      .text(d => d.name);

    // Add stage labels
    g.selectAll('.stage-label')
      .data(stages)
      .enter()
      .append('text')
      .attr('class', 'stage-label')
      .attr('x', (d, i) => i * stageWidth + stageWidth / 2)
      .attr('y', 15)
      .attr('text-anchor', 'middle')
      .style('font-size', '12px')
      .style('font-weight', 'bold')
      .style('text-transform', 'capitalize')
      .text(d => d);

  }, [data, width, height, onNodeClick]);

  return <svg ref={svgRef} width={width} height={height} />;
};

// Funnel Chart for Conversion Analysis
const D3ConversionFunnel: React.FC<{
  data: JourneyNode[];
  width: number;
  height: number;
  onStageClick?: (node: JourneyNode) => void;
}> = ({ data, width, height, onStageClick }) => {
  const svgRef = useRef<SVGSVGElement>(null);

  useEffect(() => {
    if (!svgRef.current || !data.length) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove();

    const margin = { top: 20, right: 20, bottom: 40, left: 20 };
    const innerWidth = width - margin.left - margin.right;
    const innerHeight = height - margin.top - margin.bottom;

    const g = svg
      .append('g')
      .attr('transform', `translate(${margin.left},${margin.top})`);

    // Sort data by stage order
    const stageOrder = ['awareness', 'interest', 'consideration', 'intent', 'purchase', 'retention'];
    const sortedData = data.sort((a, b) => stageOrder.indexOf(a.stage) - stageOrder.indexOf(b.stage));

    const maxValue = d3.max(sortedData, d => d.value) || 0;
    const stageHeight = innerHeight / sortedData.length;

    // Color scale
    const colorScale = d3.scaleSequential(d3.interpolateBlues)
      .domain([0, maxValue]);

    // Draw funnel segments
    sortedData.forEach((d, i) => {
      const segmentWidth = (d.value / maxValue) * innerWidth;
      const x = (innerWidth - segmentWidth) / 2;
      const y = i * stageHeight;

      // Funnel segment
      g.append('rect')
        .attr('x', x)
        .attr('y', y + 5)
        .attr('width', segmentWidth)
        .attr('height', stageHeight - 10)
        .attr('fill', colorScale(d.value))
        .attr('stroke', '#fff')
        .attr('stroke-width', 2)
        .style('cursor', 'pointer')
        .on('click', () => onStageClick?.(d))
        .on('mouseover', function(event) {
          d3.select(this).attr('opacity', 0.8);
          
          // Tooltip
          const tooltip = d3.select('body')
            .append('div')
            .attr('class', 'tooltip')
            .style('position', 'absolute')
            .style('background', 'rgba(0, 0, 0, 0.8)')
            .style('color', 'white')
            .style('padding', '8px')
            .style('border-radius', '4px')
            .style('font-size', '12px')
            .style('pointer-events', 'none')
            .style('opacity', 0);

          tooltip.transition().duration(200).style('opacity', 1);
          tooltip.html(`${d.name}<br/>Value: ${d.value}<br/>Conversion: ${((d.value / maxValue) * 100).toFixed(1)}%`)
            .style('left', (event.pageX + 10) + 'px')
            .style('top', (event.pageY - 10) + 'px');
        })
        .on('mouseout', function() {
          d3.select(this).attr('opacity', 1);
          d3.selectAll('.tooltip').remove();
        });

      // Stage label
      g.append('text')
        .attr('x', 10)
        .attr('y', y + stageHeight / 2)
        .attr('dy', '0.35em')
        .style('font-size', '12px')
        .style('font-weight', 'bold')
        .style('text-transform', 'capitalize')
        .text(d.stage);

      // Value label
      g.append('text')
        .attr('x', innerWidth - 10)
        .attr('y', y + stageHeight / 2)
        .attr('dy', '0.35em')
        .attr('text-anchor', 'end')
        .style('font-size', '12px')
        .style('font-weight', 'bold')
        .text(d.value.toLocaleString());
    });

  }, [data, width, height, onStageClick]);

  return <svg ref={svgRef} width={width} height={height} />;
};

// Main Customer Journey Component
const D3CustomerJourney: React.FC<D3CustomerJourneyProps> = ({
  data,
  width = 800,
  height = 400,
  onNodeClick,
  onLinkClick
}) => {
  const [viewMode, setViewMode] = useState<'flow' | 'funnel'>('flow');

  // Aggregate data for funnel view
  const funnelData = React.useMemo(() => {
    const stageGroups = d3.group(data.nodes, d => d.stage);
    return Array.from(stageGroups, ([stage, nodes]) => ({
      id: stage,
      name: stage,
      type: 'touchpoint' as const,
      value: d3.sum(nodes, d => d.value),
      stage: stage as any
    }));
  }, [data]);

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Customer Journey Analysis</CardTitle>
          <div className="flex space-x-2">
            <button
              onClick={() => setViewMode('flow')}
              className={`px-3 py-1 rounded ${viewMode === 'flow' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
            >
              Journey Flow
            </button>
            <button
              onClick={() => setViewMode('funnel')}
              className={`px-3 py-1 rounded ${viewMode === 'funnel' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
            >
              Conversion Funnel
            </button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {viewMode === 'flow' ? (
          <D3SankeyJourney
            data={data}
            width={width}
            height={height}
            onNodeClick={onNodeClick}
          />
        ) : (
          <D3ConversionFunnel
            data={funnelData}
            width={width}
            height={height}
            onStageClick={onNodeClick}
          />
        )}
      </CardContent>
    </Card>
  );
};

export default D3CustomerJourney;
