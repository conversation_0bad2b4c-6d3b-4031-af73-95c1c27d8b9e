import React, { useEffect, useRef, useState } from 'react';
import * as d3 from 'd3';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

// Types for attribution analysis
interface AttributionData {
  channel: string;
  model: string;
  attributedValue: number;
  conversions: number;
  weight: number;
  touchpoints: number;
}

interface AttributionComparison {
  channel: string;
  lastClick: number;
  firstClick: number;
  linear: number;
  timeDecay: number;
  positionBased: number;
}

interface D3AttributionAnalysisProps {
  attributionData: AttributionData[];
  comparisonData: AttributionComparison[];
  selectedModel?: string;
  onModelChange?: (model: string) => void;
  onChannelClick?: (channel: string) => void;
}

// Attribution Model Comparison Chart
const D3AttributionComparison: React.FC<{
  data: AttributionComparison[];
  width: number;
  height: number;
  onChannelClick?: (channel: string) => void;
}> = ({ data, width, height, onChannelClick }) => {
  const svgRef = useRef<SVGSVGElement>(null);

  useEffect(() => {
    if (!svgRef.current || !data.length) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove();

    const margin = { top: 20, right: 80, bottom: 60, left: 60 };
    const innerWidth = width - margin.left - margin.right;
    const innerHeight = height - margin.top - margin.bottom;

    const g = svg
      .append('g')
      .attr('transform', `translate(${margin.left},${margin.top})`);

    // Prepare data for stacked bar chart
    const models = ['lastClick', 'firstClick', 'linear', 'timeDecay', 'positionBased'];
    const modelLabels = ['Last Click', 'First Click', 'Linear', 'Time Decay', 'Position Based'];
    
    const stackedData = d3.stack<AttributionComparison>()
      .keys(models)
      (data);

    // Scales
    const xScale = d3.scaleBand()
      .domain(data.map(d => d.channel))
      .range([0, innerWidth])
      .padding(0.1);

    const yScale = d3.scaleLinear()
      .domain([0, d3.max(data, d => d.lastClick + d.firstClick + d.linear + d.timeDecay + d.positionBased) || 0])
      .nice()
      .range([innerHeight, 0]);

    const colorScale = d3.scaleOrdinal()
      .domain(models)
      .range(['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57']);

    // Add axes
    g.append('g')
      .attr('transform', `translate(0,${innerHeight})`)
      .call(d3.axisBottom(xScale))
      .selectAll('text')
      .style('text-anchor', 'end')
      .attr('dx', '-.8em')
      .attr('dy', '.15em')
      .attr('transform', 'rotate(-45)');

    g.append('g')
      .call(d3.axisLeft(yScale));

    // Add bars
    g.selectAll('.model-group')
      .data(stackedData)
      .enter()
      .append('g')
      .attr('class', 'model-group')
      .attr('fill', (d, i) => colorScale(models[i]) as string)
      .selectAll('rect')
      .data(d => d)
      .enter()
      .append('rect')
      .attr('x', d => xScale(d.data.channel) || 0)
      .attr('y', d => yScale(d[1]))
      .attr('height', d => yScale(d[0]) - yScale(d[1]))
      .attr('width', xScale.bandwidth())
      .style('cursor', 'pointer')
      .on('click', (event, d) => onChannelClick?.(d.data.channel))
      .on('mouseover', function(event, d) {
        d3.select(this).attr('opacity', 0.8);
        
        // Tooltip
        const tooltip = d3.select('body')
          .append('div')
          .attr('class', 'tooltip')
          .style('position', 'absolute')
          .style('background', 'rgba(0, 0, 0, 0.8)')
          .style('color', 'white')
          .style('padding', '8px')
          .style('border-radius', '4px')
          .style('font-size', '12px')
          .style('pointer-events', 'none')
          .style('opacity', 0);

        tooltip.transition().duration(200).style('opacity', 1);
        tooltip.html(`${d.data.channel}<br/>Value: ${(d[1] - d[0]).toFixed(0)}`)
          .style('left', (event.pageX + 10) + 'px')
          .style('top', (event.pageY - 10) + 'px');
      })
      .on('mouseout', function() {
        d3.select(this).attr('opacity', 1);
        d3.selectAll('.tooltip').remove();
      });

    // Add legend
    const legend = g.append('g')
      .attr('class', 'legend')
      .attr('transform', `translate(${innerWidth + 10}, 20)`);

    const legendItems = legend.selectAll('.legend-item')
      .data(modelLabels)
      .enter()
      .append('g')
      .attr('class', 'legend-item')
      .attr('transform', (d, i) => `translate(0, ${i * 20})`);

    legendItems.append('rect')
      .attr('width', 15)
      .attr('height', 15)
      .attr('fill', (d, i) => colorScale(models[i]) as string);

    legendItems.append('text')
      .attr('x', 20)
      .attr('y', 12)
      .style('font-size', '12px')
      .text(d => d);

  }, [data, width, height, onChannelClick]);

  return <svg ref={svgRef} width={width} height={height} />;
};

// Attribution Weight Visualization
const D3AttributionWeights: React.FC<{
  data: AttributionData[];
  width: number;
  height: number;
  selectedModel: string;
}> = ({ data, width, height, selectedModel }) => {
  const svgRef = useRef<SVGSVGElement>(null);

  useEffect(() => {
    if (!svgRef.current || !data.length) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove();

    const margin = { top: 20, right: 20, bottom: 40, left: 60 };
    const innerWidth = width - margin.left - margin.right;
    const innerHeight = height - margin.top - margin.bottom;

    const g = svg
      .append('g')
      .attr('transform', `translate(${margin.left},${margin.top})`);

    // Filter data by selected model
    const modelData = data.filter(d => d.model === selectedModel);

    // Scales
    const xScale = d3.scaleBand()
      .domain(modelData.map(d => d.channel))
      .range([0, innerWidth])
      .padding(0.1);

    const yScale = d3.scaleLinear()
      .domain([0, d3.max(modelData, d => d.weight) || 1])
      .nice()
      .range([innerHeight, 0]);

    const colorScale = d3.scaleSequential(d3.interpolateBlues)
      .domain([0, d3.max(modelData, d => d.weight) || 1]);

    // Add axes
    g.append('g')
      .attr('transform', `translate(0,${innerHeight})`)
      .call(d3.axisBottom(xScale));

    g.append('g')
      .call(d3.axisLeft(yScale).tickFormat(d3.format('.0%')));

    // Add bars
    g.selectAll('.weight-bar')
      .data(modelData)
      .enter()
      .append('rect')
      .attr('class', 'weight-bar')
      .attr('x', d => xScale(d.channel) || 0)
      .attr('width', xScale.bandwidth())
      .attr('y', d => yScale(d.weight))
      .attr('height', d => innerHeight - yScale(d.weight))
      .attr('fill', d => colorScale(d.weight))
      .on('mouseover', function(event, d) {
        d3.select(this).attr('opacity', 0.8);
        
        // Tooltip
        const tooltip = d3.select('body')
          .append('div')
          .attr('class', 'tooltip')
          .style('position', 'absolute')
          .style('background', 'rgba(0, 0, 0, 0.8)')
          .style('color', 'white')
          .style('padding', '8px')
          .style('border-radius', '4px')
          .style('font-size', '12px')
          .style('pointer-events', 'none')
          .style('opacity', 0);

        tooltip.transition().duration(200).style('opacity', 1);
        tooltip.html(`${d.channel}<br/>Weight: ${(d.weight * 100).toFixed(1)}%<br/>Value: $${d.attributedValue.toLocaleString()}`)
          .style('left', (event.pageX + 10) + 'px')
          .style('top', (event.pageY - 10) + 'px');
      })
      .on('mouseout', function() {
        d3.select(this).attr('opacity', 1);
        d3.selectAll('.tooltip').remove();
      });

    // Add value labels on bars
    g.selectAll('.value-label')
      .data(modelData)
      .enter()
      .append('text')
      .attr('class', 'value-label')
      .attr('x', d => (xScale(d.channel) || 0) + xScale.bandwidth() / 2)
      .attr('y', d => yScale(d.weight) - 5)
      .attr('text-anchor', 'middle')
      .style('font-size', '10px')
      .style('font-weight', 'bold')
      .text(d => `$${(d.attributedValue / 1000).toFixed(0)}k`);

  }, [data, width, height, selectedModel]);

  return <svg ref={svgRef} width={width} height={height} />;
};

// Main Attribution Analysis Component
const D3AttributionAnalysis: React.FC<D3AttributionAnalysisProps> = ({
  attributionData,
  comparisonData,
  selectedModel = 'lastClick',
  onModelChange,
  onChannelClick
}) => {
  const [activeTab, setActiveTab] = useState<'comparison' | 'weights'>('comparison');

  const models = [
    { id: 'lastClick', name: 'Last Click', description: 'Full credit to last touchpoint' },
    { id: 'firstClick', name: 'First Click', description: 'Full credit to first touchpoint' },
    { id: 'linear', name: 'Linear', description: 'Equal credit to all touchpoints' },
    { id: 'timeDecay', name: 'Time Decay', description: 'More credit to recent touchpoints' },
    { id: 'positionBased', name: 'Position Based', description: '40% first, 40% last, 20% middle' }
  ];

  return (
    <div className="w-full space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Attribution Model Analysis</CardTitle>
            <div className="flex space-x-2">
              <button
                onClick={() => setActiveTab('comparison')}
                className={`px-3 py-1 rounded ${activeTab === 'comparison' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
              >
                Model Comparison
              </button>
              <button
                onClick={() => setActiveTab('weights')}
                className={`px-3 py-1 rounded ${activeTab === 'weights' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
              >
                Attribution Weights
              </button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {activeTab === 'comparison' ? (
            <D3AttributionComparison
              data={comparisonData}
              width={800}
              height={400}
              onChannelClick={onChannelClick}
            />
          ) : (
            <div className="space-y-4">
              <div className="flex flex-wrap gap-2">
                {models.map(model => (
                  <button
                    key={model.id}
                    onClick={() => onModelChange?.(model.id)}
                    className={`px-3 py-2 rounded-lg text-sm ${
                      selectedModel === model.id 
                        ? 'bg-blue-500 text-white' 
                        : 'bg-gray-100 hover:bg-gray-200'
                    }`}
                  >
                    <div className="font-medium">{model.name}</div>
                    <div className="text-xs opacity-75">{model.description}</div>
                  </button>
                ))}
              </div>
              <D3AttributionWeights
                data={attributionData}
                width={800}
                height={400}
                selectedModel={selectedModel}
              />
            </div>
          )}
        </CardContent>
      </Card>

      {/* Attribution Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Attribution Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {attributionData
              .filter(d => d.model === selectedModel)
              .slice(0, 3)
              .map((item, index) => (
                <div key={index} className="p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium">{item.channel}</h4>
                    <Badge variant="outline">
                      {(item.weight * 100).toFixed(1)}%
                    </Badge>
                  </div>
                  <div className="space-y-1 text-sm text-gray-600">
                    <div>Attributed Value: ${item.attributedValue.toLocaleString()}</div>
                    <div>Conversions: {item.conversions}</div>
                    <div>Touchpoints: {item.touchpoints}</div>
                  </div>
                </div>
              ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default D3AttributionAnalysis;
