import React, { useEffect, useRef, useState, useCallback } from 'react';
import * as d3 from 'd3';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

// Types for real-time metrics
interface RealTimeMetric {
  timestamp: Date;
  value: number;
  metric: string;
  change?: number;
}

interface MetricStream {
  id: string;
  name: string;
  data: RealTimeMetric[];
  color: string;
  unit: string;
  format: 'number' | 'currency' | 'percentage';
}

interface D3RealTimeMetricsProps {
  streams: MetricStream[];
  updateInterval?: number;
  maxDataPoints?: number;
  onMetricClick?: (metric: MetricStream) => void;
}

// Real-time Line Chart
const D3RealTimeChart: React.FC<{
  stream: MetricStream;
  width: number;
  height: number;
  maxDataPoints: number;
}> = ({ stream, width, height, maxDataPoints }) => {
  const svgRef = useRef<SVGSVGElement>(null);

  useEffect(() => {
    if (!svgRef.current || !stream.data.length) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove();

    const margin = { top: 20, right: 30, bottom: 30, left: 50 };
    const innerWidth = width - margin.left - margin.right;
    const innerHeight = height - margin.top - margin.bottom;

    const g = svg
      .append('g')
      .attr('transform', `translate(${margin.left},${margin.top})`);

    // Keep only the latest data points
    const recentData = stream.data.slice(-maxDataPoints);

    // Scales
    const xScale = d3.scaleTime()
      .domain(d3.extent(recentData, d => d.timestamp) as [Date, Date])
      .range([0, innerWidth]);

    const yScale = d3.scaleLinear()
      .domain(d3.extent(recentData, d => d.value) as [number, number])
      .nice()
      .range([innerHeight, 0]);

    // Line generator
    const line = d3.line<RealTimeMetric>()
      .x(d => xScale(d.timestamp))
      .y(d => yScale(d.value))
      .curve(d3.curveMonotoneX);

    // Area generator for gradient fill
    const area = d3.area<RealTimeMetric>()
      .x(d => xScale(d.timestamp))
      .y0(innerHeight)
      .y1(d => yScale(d.value))
      .curve(d3.curveMonotoneX);

    // Add gradient
    const defs = svg.append('defs');
    const gradient = defs.append('linearGradient')
      .attr('id', `gradient-${stream.id}`)
      .attr('gradientUnits', 'userSpaceOnUse')
      .attr('x1', 0).attr('y1', 0)
      .attr('x2', 0).attr('y2', innerHeight);

    gradient.append('stop')
      .attr('offset', '0%')
      .attr('stop-color', stream.color)
      .attr('stop-opacity', 0.3);

    gradient.append('stop')
      .attr('offset', '100%')
      .attr('stop-color', stream.color)
      .attr('stop-opacity', 0);

    // Add area
    g.append('path')
      .datum(recentData)
      .attr('fill', `url(#gradient-${stream.id})`)
      .attr('d', area);

    // Add line
    g.append('path')
      .datum(recentData)
      .attr('fill', 'none')
      .attr('stroke', stream.color)
      .attr('stroke-width', 2)
      .attr('d', line);

    // Add dots for recent points
    g.selectAll('.dot')
      .data(recentData.slice(-5)) // Show only last 5 points
      .enter()
      .append('circle')
      .attr('class', 'dot')
      .attr('cx', d => xScale(d.timestamp))
      .attr('cy', d => yScale(d.value))
      .attr('r', 3)
      .attr('fill', stream.color);

    // Add axes
    g.append('g')
      .attr('transform', `translate(0,${innerHeight})`)
      .call(d3.axisBottom(xScale).tickFormat(d3.timeFormat('%H:%M')));

    g.append('g')
      .call(d3.axisLeft(yScale).ticks(5));

  }, [stream, width, height, maxDataPoints]);

  return <svg ref={svgRef} width={width} height={height} />;
};

// Metric Card with Sparkline
const D3MetricCard: React.FC<{
  stream: MetricStream;
  onMetricClick?: (metric: MetricStream) => void;
}> = ({ stream, onMetricClick }) => {
  const sparklineRef = useRef<SVGSVGElement>(null);

  useEffect(() => {
    if (!sparklineRef.current || !stream.data.length) return;

    const svg = d3.select(sparklineRef.current);
    svg.selectAll('*').remove();

    const width = 100;
    const height = 30;
    const recentData = stream.data.slice(-20);

    const xScale = d3.scaleLinear()
      .domain([0, recentData.length - 1])
      .range([0, width]);

    const yScale = d3.scaleLinear()
      .domain(d3.extent(recentData, d => d.value) as [number, number])
      .range([height, 0]);

    const line = d3.line<RealTimeMetric>()
      .x((d, i) => xScale(i))
      .y(d => yScale(d.value))
      .curve(d3.curveMonotoneX);

    svg.append('path')
      .datum(recentData)
      .attr('fill', 'none')
      .attr('stroke', stream.color)
      .attr('stroke-width', 1.5)
      .attr('d', line);

  }, [stream]);

  const formatValue = (value: number) => {
    switch (stream.format) {
      case 'currency':
        return `$${value.toLocaleString()}`;
      case 'percentage':
        return `${value.toFixed(1)}%`;
      default:
        return value.toLocaleString();
    }
  };

  const latestValue = stream.data[stream.data.length - 1];
  const previousValue = stream.data[stream.data.length - 2];
  const change = latestValue && previousValue 
    ? ((latestValue.value - previousValue.value) / previousValue.value) * 100 
    : 0;

  return (
    <Card 
      className="cursor-pointer hover:shadow-lg transition-shadow"
      onClick={() => onMetricClick?.(stream)}
    >
      <CardContent className="p-4">
        <div className="flex items-center justify-between mb-2">
          <h4 className="text-sm font-medium text-gray-600">{stream.name}</h4>
          <Badge 
            variant={change >= 0 ? "default" : "destructive"}
            className="text-xs"
          >
            {change >= 0 ? '+' : ''}{change.toFixed(1)}%
          </Badge>
        </div>
        
        <div className="flex items-center justify-between">
          <div>
            <div className="text-2xl font-bold">
              {latestValue ? formatValue(latestValue.value) : '--'}
            </div>
            <div className="text-xs text-gray-500">
              {latestValue ? latestValue.timestamp.toLocaleTimeString() : '--'}
            </div>
          </div>
          
          <svg ref={sparklineRef} width={100} height={30} />
        </div>
      </CardContent>
    </Card>
  );
};

// Multi-stream Real-time Chart
const D3MultiStreamChart: React.FC<{
  streams: MetricStream[];
  width: number;
  height: number;
  maxDataPoints: number;
}> = ({ streams, width, height, maxDataPoints }) => {
  const svgRef = useRef<SVGSVGElement>(null);

  useEffect(() => {
    if (!svgRef.current || !streams.length) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove();

    const margin = { top: 20, right: 80, bottom: 30, left: 50 };
    const innerWidth = width - margin.left - margin.right;
    const innerHeight = height - margin.top - margin.bottom;

    const g = svg
      .append('g')
      .attr('transform', `translate(${margin.left},${margin.top})`);

    // Combine all data points for time domain
    const allData = streams.flatMap(stream => stream.data.slice(-maxDataPoints));
    const timeExtent = d3.extent(allData, d => d.timestamp) as [Date, Date];

    // Scales
    const xScale = d3.scaleTime()
      .domain(timeExtent)
      .range([0, innerWidth]);

    // Create separate y-scales for each stream (normalized)
    const yScales = new Map();
    streams.forEach(stream => {
      const extent = d3.extent(stream.data.slice(-maxDataPoints), d => d.value) as [number, number];
      yScales.set(stream.id, d3.scaleLinear()
        .domain(extent)
        .range([innerHeight, 0]));
    });

    // Line generator
    const line = d3.line<RealTimeMetric>()
      .x(d => xScale(d.timestamp))
      .curve(d3.curveMonotoneX);

    // Draw lines for each stream
    streams.forEach(stream => {
      const yScale = yScales.get(stream.id);
      const recentData = stream.data.slice(-maxDataPoints);

      line.y(d => yScale(d.value));

      g.append('path')
        .datum(recentData)
        .attr('fill', 'none')
        .attr('stroke', stream.color)
        .attr('stroke-width', 2)
        .attr('d', line);

      // Add dots for latest points
      g.selectAll(`.dot-${stream.id}`)
        .data([recentData[recentData.length - 1]].filter(Boolean))
        .enter()
        .append('circle')
        .attr('class', `dot-${stream.id}`)
        .attr('cx', d => xScale(d.timestamp))
        .attr('cy', d => yScale(d.value))
        .attr('r', 4)
        .attr('fill', stream.color);
    });

    // Add x-axis
    g.append('g')
      .attr('transform', `translate(0,${innerHeight})`)
      .call(d3.axisBottom(xScale).tickFormat(d3.timeFormat('%H:%M')));

    // Add legend
    const legend = g.append('g')
      .attr('class', 'legend')
      .attr('transform', `translate(${innerWidth + 10}, 20)`);

    const legendItems = legend.selectAll('.legend-item')
      .data(streams)
      .enter()
      .append('g')
      .attr('class', 'legend-item')
      .attr('transform', (d, i) => `translate(0, ${i * 20})`);

    legendItems.append('line')
      .attr('x1', 0)
      .attr('x2', 15)
      .attr('stroke', d => d.color)
      .attr('stroke-width', 2);

    legendItems.append('text')
      .attr('x', 20)
      .attr('y', 5)
      .style('font-size', '12px')
      .text(d => d.name);

  }, [streams, width, height, maxDataPoints]);

  return <svg ref={svgRef} width={width} height={height} />;
};

// Main Real-time Metrics Component
const D3RealTimeMetrics: React.FC<D3RealTimeMetricsProps> = ({
  streams,
  updateInterval = 5000,
  maxDataPoints = 50,
  onMetricClick
}) => {
  const [isLive, setIsLive] = useState(true);
  const [selectedStream, setSelectedStream] = useState<string | null>(null);

  // Simulate real-time data updates
  useEffect(() => {
    if (!isLive) return;

    const interval = setInterval(() => {
      // This would typically come from WebSocket or API polling
      // For demo purposes, we'll simulate data updates
      console.log('Real-time data update simulation');
    }, updateInterval);

    return () => clearInterval(interval);
  }, [isLive, updateInterval]);

  const handleMetricClick = useCallback((metric: MetricStream) => {
    setSelectedStream(selectedStream === metric.id ? null : metric.id);
    onMetricClick?.(metric);
  }, [selectedStream, onMetricClick]);

  return (
    <div className="w-full space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Real-time Metrics</h2>
        <div className="flex items-center space-x-2">
          <Badge variant={isLive ? "default" : "secondary"}>
            {isLive ? 'Live' : 'Paused'}
          </Badge>
          <button
            onClick={() => setIsLive(!isLive)}
            className="px-3 py-1 rounded bg-blue-500 text-white hover:bg-blue-600"
          >
            {isLive ? 'Pause' : 'Resume'}
          </button>
        </div>
      </div>

      {/* Metric Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {streams.map(stream => (
          <D3MetricCard
            key={stream.id}
            stream={stream}
            onMetricClick={handleMetricClick}
          />
        ))}
      </div>

      {/* Detailed Charts */}
      {selectedStream ? (
        <Card>
          <CardHeader>
            <CardTitle>
              {streams.find(s => s.id === selectedStream)?.name} - Detailed View
            </CardTitle>
          </CardHeader>
          <CardContent>
            <D3RealTimeChart
              stream={streams.find(s => s.id === selectedStream)!}
              width={800}
              height={300}
              maxDataPoints={maxDataPoints}
            />
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>All Metrics Overview</CardTitle>
          </CardHeader>
          <CardContent>
            <D3MultiStreamChart
              streams={streams}
              width={800}
              height={300}
              maxDataPoints={maxDataPoints}
            />
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default D3RealTimeMetrics;
