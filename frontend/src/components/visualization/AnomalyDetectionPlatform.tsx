/**
 * Anomaly Detection Platform
 * AI-powered anomaly detection with explanations and intelligent alerts
 */

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  AlertTriangle,
  CheckCircle,
  Info,
  Zap,
  Brain,
  Eye,
  Settings,
  TrendingUp,
  TrendingDown,
  Activity,
  Target,
  Search,
  Filter,
  BarChart3,
  LineChart,
  PieChart,
  Bell,
  Clock,
  Calendar,
  Users,
  DollarSign,
  Package,
  ShoppingCart,
  Plus,
  Minus,
  RefreshCw,
  Download,
  Share,
  Edit,
  MoreHorizontal,
  ExternalLink,
  Lightbulb,
  Gauge,
  Shield,
  Database,
  Server,
  Cloud,
  Home,
  Building2,
  MapPin,
  Globe,
  Mail,
  Phone,
  MessageCircle,
  File,
  FileText,
  Image,
  Video,
  Music,
  Layers,
  Grid,
  List,
  Layout,
  Menu,
  X,
  ChevronUp,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  ArrowUp,
  ArrowDown,
  ArrowLeft,
  ArrowRight,
  Upload,
  Link,
  Copy,
  Move,
  Maximize,
  Minimize,
  Square,
  Circle,
  Triangle,
  Heart,
  Sparkles,
  Sun,
  Moon
} from 'lucide-react';
import { 
  LineChart as RechartsLineChart, 
  Line, 
  AreaChart, 
  Area, 
  BarChart, 
  Bar, 
  PieChart as RechartsPieChart,
  Cell,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  ScatterChart,
  Scatter,
  ComposedChart,
  ReferenceLine,
  ReferenceArea
} from 'recharts';

// Types for Anomaly Detection Platform
interface AnomalyDetection {
  id: string;
  title: string;
  type: 'statistical' | 'ml_based' | 'rule_based' | 'hybrid' | 'deep_learning';
  severity: 'critical' | 'high' | 'medium' | 'low' | 'info';
  status: 'active' | 'resolved' | 'investigating' | 'false_positive' | 'acknowledged';
  confidence: number;
  anomalyScore: number;
  detectedAt: Date;
  resolvedAt?: Date;
  dataSource: string;
  metrics: AnomalyMetric[];
  explanation: AnomalyExplanation;
  impact: AnomalyImpact;
  recommendations: string[];
  relatedAnomalies: string[];
  alerts: AnomalyAlert[];
  investigation: Investigation;
  metadata: {
    algorithm: string;
    modelVersion: string;
    dataPoints: number;
    timeWindow: string;
    threshold: number;
    baseline: number;
    deviation: number;
    zScore: number;
    pValue: number;
    tags: string[];
    category: string;
    priority: number;
  };
}

interface AnomalyMetric {
  id: string;
  name: string;
  value: number;
  expectedValue: number;
  deviation: number;
  unit: string;
  threshold: {
    upper: number;
    lower: number;
    critical: number;
  };
  trend: 'increasing' | 'decreasing' | 'stable' | 'volatile';
  dataType: 'numeric' | 'categorical' | 'time_series' | 'text';
  source: string;
  timestamp: Date;
}

interface AnomalyExplanation {
  summary: string;
  details: string;
  rootCauses: RootCause[];
  contributing_factors: ContributingFactor[];
  evidence: Evidence[];
  confidence: number;
  methodology: string;
  assumptions: string[];
  limitations: string[];
  next_steps: string[];
}

interface RootCause {
  id: string;
  description: string;
  probability: number;
  evidence: string[];
  impact: 'high' | 'medium' | 'low';
  actionable: boolean;
  recommendation: string;
}

interface ContributingFactor {
  id: string;
  name: string;
  influence: number;
  description: string;
  correlation: number;
  timeDelay: number;
  confidence: number;
}

interface Evidence {
  id: string;
  type: 'data_pattern' | 'statistical_test' | 'visual_inspection' | 'domain_knowledge' | 'correlation' | 'trend_analysis';
  description: string;
  strength: 'strong' | 'moderate' | 'weak';
  source: string;
  timestamp: Date;
  data: any;
}

interface AnomalyImpact {
  revenue: {
    estimated_loss: number;
    potential_loss: number;
    currency: string;
    confidence: number;
  };
  operations: {
    affected_processes: string[];
    downtime_risk: number;
    resource_impact: string;
    customer_impact: string;
  };
  compliance: {
    risk_level: 'high' | 'medium' | 'low';
    regulations_affected: string[];
    reporting_required: boolean;
    deadline: Date;
  };
  reputation: {
    risk_score: number;
    potential_customers_affected: number;
    media_attention_risk: 'high' | 'medium' | 'low';
    social_media_sentiment: number;
  };
}

interface AnomalyAlert {
  id: string;
  type: 'email' | 'sms' | 'push' | 'webhook' | 'slack' | 'teams' | 'dashboard';
  recipients: string[];
  sent_at: Date;
  status: 'sent' | 'delivered' | 'failed' | 'pending';
  urgency: 'immediate' | 'high' | 'normal' | 'low';
  escalation_level: number;
  message: string;
  actions_taken: string[];
}

interface Investigation {
  id: string;
  status: 'open' | 'in_progress' | 'closed' | 'escalated';
  assigned_to: string;
  created_at: Date;
  updated_at: Date;
  notes: InvestigationNote[];
  findings: Finding[];
  actions: Action[];
  timeline: TimelineEvent[];
  stakeholders: string[];
  priority: 'critical' | 'high' | 'medium' | 'low';
  estimated_resolution: Date;
  actual_resolution?: Date;
}

interface InvestigationNote {
  id: string;
  author: string;
  content: string;
  timestamp: Date;
  type: 'observation' | 'hypothesis' | 'action' | 'conclusion';
  attachments: string[];
}

interface Finding {
  id: string;
  description: string;
  evidence: string[];
  confidence: number;
  impact: 'high' | 'medium' | 'low';
  actionable: boolean;
  recommendations: string[];
}

interface Action {
  id: string;
  description: string;
  type: 'immediate' | 'short_term' | 'long_term' | 'preventive';
  status: 'planned' | 'in_progress' | 'completed' | 'cancelled';
  assigned_to: string;
  due_date: Date;
  completed_date?: Date;
  effort_estimate: string;
  dependencies: string[];
  success_criteria: string[];
}

interface TimelineEvent {
  id: string;
  timestamp: Date;
  event: string;
  details: string;
  type: 'detection' | 'alert' | 'investigation' | 'action' | 'resolution';
  actor: string;
  impact: 'high' | 'medium' | 'low';
}

interface AnomalyPattern {
  id: string;
  name: string;
  description: string;
  pattern_type: 'seasonal' | 'trending' | 'cyclical' | 'irregular' | 'outlier' | 'shift' | 'drift';
  frequency: number;
  duration: string;
  characteristics: string[];
  examples: string[];
  detection_methods: string[];
  false_positive_rate: number;
  historical_occurrences: number;
}

interface AnomalyModel {
  id: string;
  name: string;
  type: 'isolation_forest' | 'one_class_svm' | 'local_outlier_factor' | 'dbscan' | 'autoencoder' | 'lstm' | 'prophet' | 'arima';
  version: string;
  accuracy: number;
  precision: number;
  recall: number;
  f1_score: number;
  false_positive_rate: number;
  false_negative_rate: number;
  training_data: {
    size: number;
    period: string;
    features: string[];
    quality_score: number;
  };
  performance: {
    latency: number;
    throughput: number;
    memory_usage: number;
    cpu_usage: number;
  };
  hyperparameters: Record<string, any>;
  last_trained: Date;
  next_training: Date;
  status: 'active' | 'training' | 'deprecated' | 'experimental';
}

interface AnomalyConfig {
  id: string;
  name: string;
  data_sources: string[];
  metrics: string[];
  detection_algorithms: string[];
  thresholds: Record<string, number>;
  alert_rules: AlertRule[];
  notification_settings: NotificationSettings;
  investigation_workflow: WorkflowStep[];
  auto_resolution: AutoResolution;
  reporting: ReportingConfig;
  integration: IntegrationConfig;
}

interface AlertRule {
  id: string;
  name: string;
  condition: string;
  severity_mapping: Record<string, string>;
  cooldown_period: number;
  escalation_rules: EscalationRule[];
  suppression_rules: SuppressionRule[];
  correlation_rules: CorrelationRule[];
}

interface EscalationRule {
  level: number;
  delay: number;
  recipients: string[];
  actions: string[];
  condition: string;
}

interface SuppressionRule {
  condition: string;
  duration: number;
  reason: string;
}

interface CorrelationRule {
  related_metrics: string[];
  correlation_threshold: number;
  time_window: number;
  action: string;
}

interface NotificationSettings {
  channels: NotificationChannel[];
  templates: NotificationTemplate[];
  frequency_limits: FrequencyLimit[];
  escalation_delays: number[];
  business_hours: BusinessHours;
}

interface NotificationChannel {
  type: string;
  endpoint: string;
  credentials: Record<string, string>;
  enabled: boolean;
  priority: number;
  rate_limit: number;
}

interface NotificationTemplate {
  id: string;
  channel: string;
  severity: string;
  template: string;
  variables: string[];
}

interface FrequencyLimit {
  channel: string;
  max_per_hour: number;
  max_per_day: number;
  burst_limit: number;
}

interface BusinessHours {
  timezone: string;
  weekdays: string[];
  start_time: string;
  end_time: string;
  holidays: Date[];
}

interface WorkflowStep {
  id: string;
  name: string;
  type: 'manual' | 'automated' | 'approval' | 'notification';
  condition: string;
  actions: string[];
  timeout: number;
  retry_count: number;
  escalation_action: string;
}

interface AutoResolution {
  enabled: boolean;
  conditions: string[];
  delay: number;
  verification_steps: string[];
  rollback_conditions: string[];
}

interface ReportingConfig {
  enabled: boolean;
  frequency: 'daily' | 'weekly' | 'monthly';
  recipients: string[];
  format: 'pdf' | 'html' | 'csv' | 'json';
  sections: string[];
  filters: Record<string, any>;
}

interface IntegrationConfig {
  ticketing_system: {
    enabled: boolean;
    system: string;
    endpoint: string;
    credentials: Record<string, string>;
    auto_create_tickets: boolean;
    ticket_template: string;
  };
  monitoring_systems: {
    system: string;
    endpoint: string;
    credentials: Record<string, string>;
    metrics_to_sync: string[];
    sync_frequency: number;
  }[];
  data_sources: {
    source: string;
    connection: Record<string, string>;
    refresh_rate: number;
    data_quality_checks: string[];
  }[];
}

// Mock data generators
const generateAnomalies = (): AnomalyDetection[] => {
  const severities: Array<'critical' | 'high' | 'medium' | 'low' | 'info'> = ['critical', 'high', 'medium', 'low', 'info'];
  const types: Array<'statistical' | 'ml_based' | 'rule_based' | 'hybrid' | 'deep_learning'> = ['statistical', 'ml_based', 'rule_based', 'hybrid', 'deep_learning'];
  const statuses: Array<'active' | 'resolved' | 'investigating' | 'false_positive' | 'acknowledged'> = ['active', 'resolved', 'investigating', 'false_positive', 'acknowledged'];
  
  return Array.from({ length: 25 }, (_, i) => ({
    id: `anomaly-${i + 1}`,
    title: [
      'Unusual Spike in Cart Abandonment Rate',
      'Abnormal Revenue Drop in Premium Segment',
      'Unexpected Traffic Pattern from Mobile Users',
      'Irregular Payment Processing Delays',
      'Anomalous User Engagement Metrics',
      'Suspicious Login Activity Detected',
      'Abnormal Inventory Turnover Rate',
      'Unusual Customer Support Ticket Volume',
      'Irregular API Response Times',
      'Unexpected Social Media Sentiment Shift',
      'Abnormal Email Open Rates',
      'Unusual Geographic Traffic Distribution',
      'Irregular Product Return Patterns',
      'Unexpected Seasonal Demand Variation',
      'Abnormal Server Resource Utilization',
      'Unusual Competitor Pricing Changes',
      'Irregular Marketing Campaign Performance',
      'Unexpected Customer Lifetime Value Drop',
      'Abnormal Database Query Performance',
      'Unusual Third-Party Integration Failures',
      'Irregular Mobile App Crash Rates',
      'Unexpected Supply Chain Disruptions',
      'Abnormal Website Load Times',
      'Unusual Customer Churn Patterns',
      'Irregular Payment Gateway Failures'
    ][i],
    type: types[i % types.length],
    severity: severities[i % severities.length],
    status: statuses[i % statuses.length],
    confidence: 0.65 + Math.random() * 0.35,
    anomalyScore: Math.random() * 100,
    detectedAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000),
    resolvedAt: Math.random() > 0.6 ? new Date(Date.now() - Math.random() * 2 * 24 * 60 * 60 * 1000) : undefined,
    dataSource: ['E-commerce Platform', 'Payment Gateway', 'Analytics System', 'CRM System', 'Marketing Platform', 'Mobile App'][i % 6],
    metrics: [],
    explanation: {
      summary: 'AI-detected anomaly requiring immediate attention',
      details: 'Detailed analysis reveals significant deviation from expected patterns',
      rootCauses: [],
      contributing_factors: [],
      evidence: [],
      confidence: 0.85,
      methodology: 'Machine Learning Statistical Analysis',
      assumptions: [],
      limitations: [],
      next_steps: []
    },
    impact: {
      revenue: {
        estimated_loss: Math.random() * 50000,
        potential_loss: Math.random() * 100000,
        currency: 'USD',
        confidence: 0.75
      },
      operations: {
        affected_processes: ['Order Processing', 'Customer Service'],
        downtime_risk: Math.random() * 100,
        resource_impact: 'High priority investigation required',
        customer_impact: 'Potential customer experience degradation'
      },
      compliance: {
        risk_level: ['high', 'medium', 'low'][Math.floor(Math.random() * 3)] as 'high' | 'medium' | 'low',
        regulations_affected: ['GDPR', 'PCI-DSS'],
        reporting_required: Math.random() > 0.5,
        deadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
      },
      reputation: {
        risk_score: Math.random() * 100,
        potential_customers_affected: Math.floor(Math.random() * 10000),
        media_attention_risk: ['high', 'medium', 'low'][Math.floor(Math.random() * 3)] as 'high' | 'medium' | 'low',
        social_media_sentiment: -0.5 + Math.random()
      }
    },
    recommendations: [
      'Immediate investigation required',
      'Monitor related metrics closely',
      'Implement temporary mitigation measures',
      'Schedule emergency team meeting'
    ],
    relatedAnomalies: [],
    alerts: [],
    investigation: {
      id: `investigation-${i + 1}`,
      status: 'open',
      assigned_to: 'Data Science Team',
      created_at: new Date(),
      updated_at: new Date(),
      notes: [],
      findings: [],
      actions: [],
      timeline: [],
      stakeholders: [],
      priority: 'high',
      estimated_resolution: new Date(Date.now() + 24 * 60 * 60 * 1000)
    },
    metadata: {
      algorithm: ['Isolation Forest', 'LSTM', 'One-Class SVM', 'Local Outlier Factor', 'DBSCAN'][i % 5],
      modelVersion: `v${Math.floor(Math.random() * 5) + 1}.${Math.floor(Math.random() * 10)}`,
      dataPoints: Math.floor(Math.random() * 10000) + 1000,
      timeWindow: ['1h', '6h', '24h', '7d'][Math.floor(Math.random() * 4)],
      threshold: Math.random() * 0.1 + 0.05,
      baseline: Math.random() * 1000 + 500,
      deviation: Math.random() * 500 + 100,
      zScore: Math.random() * 5 + 2,
      pValue: Math.random() * 0.05,
      tags: ['revenue', 'conversion', 'traffic', 'performance'].slice(0, Math.floor(Math.random() * 4) + 1),
      category: ['Business Metrics', 'Technical Performance', 'User Behavior', 'Security'][i % 4],
      priority: Math.floor(Math.random() * 5) + 1
    }
  }));
};

const generateTimeSeriesData = () => {
  const data = [];
  const now = new Date();
  
  for (let i = 29; i >= 0; i--) {
    const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
    const baseValue = 1000 + Math.sin(i * 0.2) * 200;
    const noise = (Math.random() - 0.5) * 100;
    const anomaly = i === 5 || i === 12 || i === 23 ? (Math.random() - 0.5) * 400 : 0;
    
    data.push({
      date: date.toISOString().split('T')[0],
      value: Math.max(0, Math.round(baseValue + noise + anomaly)),
      expected: Math.max(0, Math.round(baseValue)),
      upperBound: Math.max(0, Math.round(baseValue + 150)),
      lowerBound: Math.max(0, Math.round(baseValue - 150)),
      isAnomaly: Math.abs(anomaly) > 200
    });
  }
  
  return data;
};

const generateAnomalyDistribution = () => {
  return [
    { severity: 'Critical', count: 3, percentage: 12 },
    { severity: 'High', count: 8, percentage: 32 },
    { severity: 'Medium', count: 10, percentage: 40 },
    { severity: 'Low', count: 4, percentage: 16 }
  ];
};

const generateModelPerformance = () => {
  return [
    { model: 'Isolation Forest', accuracy: 94.2, precision: 91.8, recall: 89.5, f1Score: 90.6 },
    { model: 'LSTM Autoencoder', accuracy: 92.8, precision: 88.4, recall: 93.2, f1Score: 90.7 },
    { model: 'One-Class SVM', accuracy: 89.6, precision: 85.3, recall: 87.9, f1Score: 86.6 },
    { model: 'Local Outlier Factor', accuracy: 87.4, precision: 82.1, recall: 85.6, f1Score: 83.8 },
    { model: 'DBSCAN', accuracy: 85.9, precision: 79.8, recall: 82.4, f1Score: 81.1 }
  ];
};

const SEVERITY_COLORS = {
  critical: '#dc2626',
  high: '#ea580c',
  medium: '#d97706',
  low: '#65a30d',
  info: '#2563eb'
};

const AnomalyDetectionPlatform: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedAnomaly, setSelectedAnomaly] = useState<AnomalyDetection | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterSeverity, setFilterSeverity] = useState<string>('all');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [sortBy, setSortBy] = useState<'detectedAt' | 'severity' | 'confidence' | 'anomalyScore'>('detectedAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  const anomalies = useMemo(() => generateAnomalies(), []);
  const timeSeriesData = useMemo(() => generateTimeSeriesData(), []);
  const distributionData = useMemo(() => generateAnomalyDistribution(), []);
  const modelPerformance = useMemo(() => generateModelPerformance(), []);

  const filteredAnomalies = useMemo(() => {
    return anomalies
      .filter(anomaly => {
        const matchesSearch = anomaly.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
          anomaly.dataSource.toLowerCase().includes(searchTerm.toLowerCase());
        const matchesSeverity = filterSeverity === 'all' || anomaly.severity === filterSeverity;
        const matchesStatus = filterStatus === 'all' || anomaly.status === filterStatus;
        return matchesSearch && matchesSeverity && matchesStatus;
      })
      .sort((a, b) => {
        const aValue = a[sortBy];
        const bValue = b[sortBy];
        const multiplier = sortOrder === 'asc' ? 1 : -1;
        
        if (aValue < bValue) return -1 * multiplier;
        if (aValue > bValue) return 1 * multiplier;
        return 0;
      });
  }, [anomalies, searchTerm, filterSeverity, filterStatus, sortBy, sortOrder]);

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical': return <AlertTriangle className="w-4 h-4" />;
      case 'high': return <AlertCircle className="w-4 h-4" />;
      case 'medium': return <Info className="w-4 h-4" />;
      case 'low': return <CheckCircle className="w-4 h-4" />;
      case 'info': return <Eye className="w-4 h-4" />;
      default: return <Info className="w-4 h-4" />;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <Bell className="w-4 h-4" />;
      case 'resolved': return <CheckCircle className="w-4 h-4" />;
      case 'investigating': return <Search className="w-4 h-4" />;
      case 'false_positive': return <XCircle className="w-4 h-4" />;
      case 'acknowledged': return <Eye className="w-4 h-4" />;
      default: return <Info className="w-4 h-4" />;
    }
  };

  return (
    <div className="p-6 max-w-7xl mx-auto space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Anomaly Detection Platform</h1>
          <p className="text-gray-600 mt-2">AI-powered anomaly detection with intelligent explanations and automated alerts</p>
        </div>
        <div className="flex items-center space-x-4">
          <Button variant="outline" className="flex items-center space-x-2">
            <Settings className="w-4 h-4" />
            <span>Configure Detection</span>
          </Button>
          <Button className="flex items-center space-x-2">
            <Plus className="w-4 h-4" />
            <span>Create Alert Rule</span>
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="anomalies">Active Anomalies</TabsTrigger>
          <TabsTrigger value="detection">Detection Models</TabsTrigger>
          <TabsTrigger value="investigation">Investigation</TabsTrigger>
          <TabsTrigger value="alerts">Alert Management</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Active Anomalies</p>
                    <p className="text-3xl font-bold text-gray-900">
                      {anomalies.filter(a => a.status === 'active').length}
                    </p>
                  </div>
                  <div className="h-12 w-12 bg-red-100 rounded-lg flex items-center justify-center">
                    <AlertTriangle className="h-6 w-6 text-red-600" />
                  </div>
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  +{Math.floor(Math.random() * 10) + 1} from last hour
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Detection Accuracy</p>
                    <p className="text-3xl font-bold text-gray-900">94.2%</p>
                  </div>
                  <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <Target className="h-6 w-6 text-green-600" />
                  </div>
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  +2.1% from last week
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">False Positives</p>
                    <p className="text-3xl font-bold text-gray-900">5.8%</p>
                  </div>
                  <div className="h-12 w-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                    <XCircle className="h-6 w-6 text-yellow-600" />
                  </div>
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  -1.2% from last week
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Mean Time to Resolution</p>
                    <p className="text-3xl font-bold text-gray-900">2.4h</p>
                  </div>
                  <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Clock className="h-6 w-6 text-blue-600" />
                  </div>
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  -15 min from last week
                </p>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <LineChart className="w-5 h-5" />
                  <span>Anomaly Detection Timeline</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart data={timeSeriesData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Area
                        type="monotone"
                        dataKey="upperBound"
                        stackId="1"
                        stroke="transparent"
                        fill="#f3f4f6"
                      />
                      <Area
                        type="monotone"
                        dataKey="lowerBound"
                        stackId="1"
                        stroke="transparent"
                        fill="#ffffff"
                      />
                      <Line
                        type="monotone"
                        dataKey="expected"
                        stroke="#6b7280"
                        strokeDasharray="5 5"
                      />
                      <Line
                        type="monotone"
                        dataKey="value"
                        stroke="#3b82f6"
                        strokeWidth={2}
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <PieChart className="w-5 h-5" />
                  <span>Anomaly Severity Distribution</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <RechartsPieChart>
                      <Pie
                        data={distributionData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ severity, percentage }) => `${severity}: ${percentage}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="count"
                      >
                        {distributionData.map((entry, index) => (
                          <Cell 
                            key={`cell-${index}`} 
                            fill={SEVERITY_COLORS[entry.severity.toLowerCase() as keyof typeof SEVERITY_COLORS]} 
                          />
                        ))}
                      </Pie>
                      <Tooltip />
                    </RechartsPieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Brain className="w-5 h-5" />
                <span>Recent Critical Anomalies</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {anomalies.filter(a => a.severity === 'critical').slice(0, 5).map((anomaly) => (
                  <motion.div
                    key={anomaly.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="flex items-center justify-between p-4 border border-red-200 rounded-lg bg-red-50"
                  >
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-2">
                        {getSeverityIcon(anomaly.severity)}
                        <Badge 
                          variant="destructive"
                          className="text-xs"
                        >
                          {anomaly.severity.toUpperCase()}
                        </Badge>
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900">{anomaly.title}</h4>
                        <p className="text-sm text-gray-600">
                          Detected {anomaly.detectedAt.toLocaleDateString()} • {anomaly.dataSource}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-4">
                      <div className="text-right">
                        <p className="text-sm font-medium text-gray-900">
                          Confidence: {(anomaly.confidence * 100).toFixed(1)}%
                        </p>
                        <p className="text-xs text-gray-600">
                          Score: {anomaly.anomalyScore.toFixed(1)}
                        </p>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setSelectedAnomaly(anomaly)}
                      >
                        Investigate
                      </Button>
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="anomalies" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Active Anomalies</CardTitle>
              <div className="flex items-center space-x-4 mt-4">
                <div className="flex-1">
                  <Input
                    placeholder="Search anomalies..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="max-w-sm"
                  />
                </div>
                <select
                  value={filterSeverity}
                  onChange={(e) => setFilterSeverity(e.target.value)}
                  className="border border-gray-300 rounded-md px-3 py-2"
                >
                  <option value="all">All Severities</option>
                  <option value="critical">Critical</option>
                  <option value="high">High</option>
                  <option value="medium">Medium</option>
                  <option value="low">Low</option>
                  <option value="info">Info</option>
                </select>
                <select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  className="border border-gray-300 rounded-md px-3 py-2"
                >
                  <option value="all">All Statuses</option>
                  <option value="active">Active</option>
                  <option value="resolved">Resolved</option>
                  <option value="investigating">Investigating</option>
                  <option value="false_positive">False Positive</option>
                  <option value="acknowledged">Acknowledged</option>
                </select>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredAnomalies.map((anomaly) => (
                  <motion.div
                    key={anomaly.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center space-x-2">
                          {getSeverityIcon(anomaly.severity)}
                          <Badge 
                            style={{ 
                              backgroundColor: SEVERITY_COLORS[anomaly.severity],
                              color: 'white'
                            }}
                            className="text-xs"
                          >
                            {anomaly.severity.toUpperCase()}
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            {getStatusIcon(anomaly.status)}
                            <span className="ml-1">{anomaly.status.replace('_', ' ').toUpperCase()}</span>
                          </Badge>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-xs text-gray-500">
                          {anomaly.detectedAt.toLocaleDateString()}
                        </span>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setSelectedAnomaly(anomaly)}
                        >
                          <Eye className="w-4 h-4 mr-1" />
                          Details
                        </Button>
                      </div>
                    </div>
                    <div className="mt-2">
                      <h4 className="font-medium text-gray-900">{anomaly.title}</h4>
                      <p className="text-sm text-gray-600 mt-1">
                        Data Source: {anomaly.dataSource} • 
                        Algorithm: {anomaly.metadata.algorithm} • 
                        Confidence: {(anomaly.confidence * 100).toFixed(1)}%
                      </p>
                    </div>
                    <div className="mt-3 flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="text-sm">
                          <span className="text-gray-600">Score:</span>
                          <span className="font-medium ml-1">{anomaly.anomalyScore.toFixed(1)}</span>
                        </div>
                        <div className="text-sm">
                          <span className="text-gray-600">Z-Score:</span>
                          <span className="font-medium ml-1">{anomaly.metadata.zScore.toFixed(2)}</span>
                        </div>
                        <div className="text-sm">
                          <span className="text-gray-600">Impact:</span>
                          <span className="font-medium ml-1">
                            ${anomaly.impact.revenue.estimated_loss.toLocaleString()}
                          </span>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        {anomaly.metadata.tags.map((tag) => (
                          <Badge key={tag} variant="secondary" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="detection" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Brain className="w-5 h-5" />
                <span>Detection Model Performance</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={modelPerformance}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="model" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="accuracy" fill="#3b82f6" name="Accuracy" />
                    <Bar dataKey="precision" fill="#10b981" name="Precision" />
                    <Bar dataKey="recall" fill="#f59e0b" name="Recall" />
                    <Bar dataKey="f1Score" fill="#ef4444" name="F1 Score" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Active Models</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {modelPerformance.map((model, index) => (
                    <div key={model.model} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium text-gray-900">{model.model}</h4>
                        <Badge variant="outline" className="text-xs">
                          {index === 0 ? 'Primary' : 'Secondary'}
                        </Badge>
                      </div>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-gray-600">Accuracy:</span>
                          <span className="font-medium ml-1">{model.accuracy}%</span>
                        </div>
                        <div>
                          <span className="text-gray-600">Precision:</span>
                          <span className="font-medium ml-1">{model.precision}%</span>
                        </div>
                        <div>
                          <span className="text-gray-600">Recall:</span>
                          <span className="font-medium ml-1">{model.recall}%</span>
                        </div>
                        <div>
                          <span className="text-gray-600">F1 Score:</span>
                          <span className="font-medium ml-1">{model.f1Score}%</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Model Configuration</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="border border-gray-200 rounded-lg p-4">
                    <h4 className="font-medium text-gray-900 mb-2">Training Configuration</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Training Data Size:</span>
                        <span className="font-medium">2.5M records</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Training Period:</span>
                        <span className="font-medium">Last 90 days</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Update Frequency:</span>
                        <span className="font-medium">Daily</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Next Update:</span>
                        <span className="font-medium">In 6 hours</span>
                      </div>
                    </div>
                  </div>

                  <div className="border border-gray-200 rounded-lg p-4">
                    <h4 className="font-medium text-gray-900 mb-2">Detection Thresholds</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Critical:</span>
                        <span className="font-medium">Z-Score &gt; 3.5</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">High:</span>
                        <span className="font-medium">Z-Score &gt; 2.5</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Medium:</span>
                        <span className="font-medium">Z-Score &gt; 1.5</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Low:</span>
                        <span className="font-medium">Z-Score &gt; 1.0</span>
                      </div>
                    </div>
                  </div>

                  <div className="flex space-x-2">
                    <Button variant="outline" className="flex-1">
                      <Settings className="w-4 h-4 mr-2" />
                      Configure
                    </Button>
                    <Button variant="outline" className="flex-1">
                      <RefreshCw className="w-4 h-4 mr-2" />
                      Retrain
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="investigation" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Anomaly Investigation Workflow</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Search className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Investigation Tools</h3>
                <p className="text-gray-600 mb-4">
                  Advanced investigation capabilities with AI-powered root cause analysis
                </p>
                <Button>
                  <Plus className="w-4 h-4 mr-2" />
                  Start Investigation
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="alerts" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Alert Rules & Notifications</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Bell className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Alert Management</h3>
                <p className="text-gray-600 mb-4">
                  Configure intelligent alert rules and notification channels
                </p>
                <Button>
                  <Plus className="w-4 h-4 mr-2" />
                  Create Alert Rule
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Detection Analytics & Insights</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <BarChart3 className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Analytics Dashboard</h3>
                <p className="text-gray-600 mb-4">
                  Comprehensive analytics on detection performance and trends
                </p>
                <Button>
                  <Eye className="w-4 h-4 mr-2" />
                  View Analytics
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <AnimatePresence>
        {selectedAnomaly && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
            onClick={() => setSelectedAnomaly(null)}
          >
            <motion.div
              initial={{ scale: 0.95, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.95, opacity: 0 }}
              className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-2">
                      {getSeverityIcon(selectedAnomaly.severity)}
                      <Badge 
                        style={{ 
                          backgroundColor: SEVERITY_COLORS[selectedAnomaly.severity],
                          color: 'white'
                        }}
                      >
                        {selectedAnomaly.severity.toUpperCase()}
                      </Badge>
                    </div>
                    <h2 className="text-xl font-bold text-gray-900">
                      {selectedAnomaly.title}
                    </h2>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setSelectedAnomaly(null)}
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <h3 className="font-medium text-gray-900 mb-2">Detection Details</h3>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Detected:</span>
                          <span className="font-medium">
                            {selectedAnomaly.detectedAt.toLocaleString()}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Data Source:</span>
                          <span className="font-medium">{selectedAnomaly.dataSource}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Algorithm:</span>
                          <span className="font-medium">{selectedAnomaly.metadata.algorithm}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Confidence:</span>
                          <span className="font-medium">
                            {(selectedAnomaly.confidence * 100).toFixed(1)}%
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Anomaly Score:</span>
                          <span className="font-medium">
                            {selectedAnomaly.anomalyScore.toFixed(1)}
                          </span>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h3 className="font-medium text-gray-900 mb-2">Statistical Analysis</h3>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Z-Score:</span>
                          <span className="font-medium">{selectedAnomaly.metadata.zScore.toFixed(2)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">P-Value:</span>
                          <span className="font-medium">{selectedAnomaly.metadata.pValue.toFixed(4)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Baseline:</span>
                          <span className="font-medium">{selectedAnomaly.metadata.baseline.toFixed(0)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Deviation:</span>
                          <span className="font-medium">{selectedAnomaly.metadata.deviation.toFixed(0)}</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <h3 className="font-medium text-gray-900 mb-2">Business Impact</h3>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Estimated Loss:</span>
                          <span className="font-medium text-red-600">
                            ${selectedAnomaly.impact.revenue.estimated_loss.toLocaleString()}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Potential Loss:</span>
                          <span className="font-medium text-red-600">
                            ${selectedAnomaly.impact.revenue.potential_loss.toLocaleString()}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Customers Affected:</span>
                          <span className="font-medium">
                            {selectedAnomaly.impact.reputation.potential_customers_affected.toLocaleString()}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Compliance Risk:</span>
                          <span className="font-medium capitalize">
                            {selectedAnomaly.impact.compliance.risk_level}
                          </span>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h3 className="font-medium text-gray-900 mb-2">AI Explanation</h3>
                      <div className="bg-blue-50 p-4 rounded-lg">
                        <p className="text-sm text-gray-700">
                          {selectedAnomaly.explanation.summary}
                        </p>
                        <p className="text-sm text-gray-600 mt-2">
                          {selectedAnomaly.explanation.details}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="mt-6">
                  <h3 className="font-medium text-gray-900 mb-3">Recommended Actions</h3>
                  <div className="space-y-2">
                    {selectedAnomaly.recommendations.map((recommendation, index) => (
                      <div key={index} className="flex items-start space-x-2">
                        <Lightbulb className="w-4 h-4 text-yellow-500 mt-0.5" />
                        <span className="text-sm text-gray-700">{recommendation}</span>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="mt-6 flex justify-end space-x-3">
                  <Button variant="outline">
                    <Download className="w-4 h-4 mr-2" />
                    Export Report
                  </Button>
                  <Button variant="outline">
                    <Share className="w-4 h-4 mr-2" />
                    Share
                  </Button>
                  <Button>
                    <Search className="w-4 h-4 mr-2" />
                    Start Investigation
                  </Button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default AnomalyDetectionPlatform;