/**
 * Executive Reporting
 * Automated executive summaries, board-ready presentations, and KPI scorecards
 */

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { motion } from 'framer-motion';
import { 
  Crown,
  FileText,
  Presentation,
  Target,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Users,
  ShoppingCart,
  Package,
  Calendar,
  Clock,
  Download,
  Share,
  Edit,
  Eye,
  Settings,
  Send,
  BarChart3,
  LineChart,
  PieChart,
  Gauge,
  Award,
  AlertTriangle,
  CheckCircle,
  Info,
  Lightbulb,
  Zap,
  Brain,
  Activity,
  RefreshCw,
  Filter,
  Search,
  Star,
  Bookmark,
  MoreHorizontal,
  ExternalLink,
  Mail,
  Printer,
  Image,
  Video,
  PlayCircle,
  ArrowUp,
  ArrowDown,
  <PERSON><PERSON>,
  <PERSON>h,
  Globe,
  MapPin
} from 'lucide-react';
import { 
  <PERSON><PERSON><PERSON> as RechartsLineChart, 
  Line, 
  AreaChart, 
  Area, 
  BarChart, 
  Bar, 
  Pie<PERSON>hart as RechartsPieChart,
  Cell,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  ComposedChart,
  ReferenceLine
} from 'recharts';

// Types for Executive Reporting
interface ExecutiveReport {
  id: string;
  title: string;
  type: 'weekly' | 'monthly' | 'quarterly' | 'annual' | 'custom';
  period: {
    start: Date;
    end: Date;
    label: string;
  };
  status: 'draft' | 'pending_review' | 'approved' | 'published';
  priority: 'high' | 'medium' | 'low';
  audience: 'ceo' | 'board' | 'executives' | 'stakeholders' | 'investors';
  sections: ReportSection[];
  summary: ExecutiveSummary;
  kpis: KPIMetric[];
  insights: ExecutiveInsight[];
  recommendations: Recommendation[];
  metadata: {
    created: Date;
    updated: Date;
    author: string;
    reviewer?: string;
    approver?: string;
    version: number;
    template: string;
    autoGenerated: boolean;
  };
  distribution: {
    recipients: string[];
    scheduled: boolean;
    frequency?: string;
    nextSend?: Date;
    lastSent?: Date;
  };
  presentation: {
    slides: PresentationSlide[];
    theme: string;
    format: 'pptx' | 'pdf' | 'html' | 'video';
  };
}

interface ReportSection {
  id: string;
  title: string;
  type: 'summary' | 'metrics' | 'trends' | 'insights' | 'forecast' | 'risks' | 'opportunities';
  order: number;
  content: string;
  visualizations: Visualization[];
  data: any[];
  autoGenerated: boolean;
}

interface ExecutiveSummary {
  keyHighlights: string[];
  performance: {
    revenue: PerformanceMetric;
    growth: PerformanceMetric;
    profitability: PerformanceMetric;
    efficiency: PerformanceMetric;
  };
  marketPosition: {
    ranking: number;
    marketShare: number;
    competitiveAdvantage: string[];
  };
  risks: RiskAssessment[];
  opportunities: OpportunityAssessment[];
  strategicInitiatives: Initiative[];
}

interface PerformanceMetric {
  current: number;
  previous: number;
  change: number;
  changePercent: number;
  target: number;
  status: 'exceeds' | 'meets' | 'below' | 'critical';
  trend: 'improving' | 'stable' | 'declining';
}

interface KPIMetric {
  id: string;
  name: string;
  category: string;
  value: number;
  previousValue: number;
  target: number;
  unit: string;
  format: 'currency' | 'percentage' | 'number' | 'ratio';
  change: number;
  changePercent: number;
  status: 'green' | 'yellow' | 'red';
  importance: 'critical' | 'high' | 'medium' | 'low';
  description: string;
  calculation: string;
  benchmark: {
    industry: number;
    competitors: number[];
    historical: number[];
  };
}

interface ExecutiveInsight {
  id: string;
  title: string;
  category: 'performance' | 'market' | 'customer' | 'operational' | 'financial';
  impact: 'high' | 'medium' | 'low';
  urgency: 'immediate' | 'short_term' | 'medium_term' | 'long_term';
  description: string;
  implications: string[];
  dataPoints: DataPoint[];
  confidence: number;
  source: string;
}

interface DataPoint {
  metric: string;
  value: number;
  context: string;
  significance: string;
}

interface Recommendation {
  id: string;
  title: string;
  description: string;
  priority: 'critical' | 'high' | 'medium' | 'low';
  category: 'strategic' | 'operational' | 'financial' | 'technical' | 'market';
  impact: {
    revenue: number;
    cost: number;
    efficiency: number;
    timeline: string;
  };
  implementation: {
    effort: 'low' | 'medium' | 'high';
    cost: number;
    timeline: string;
    resources: string[];
    risks: string[];
  };
  status: 'proposed' | 'approved' | 'in_progress' | 'completed' | 'rejected';
}

interface RiskAssessment {
  id: string;
  title: string;
  description: string;
  category: 'market' | 'operational' | 'financial' | 'regulatory' | 'technology';
  probability: number;
  impact: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
  mitigation: string[];
  owner: string;
  status: 'identified' | 'mitigating' | 'mitigated' | 'accepted';
}

interface OpportunityAssessment {
  id: string;
  title: string;
  description: string;
  category: 'market' | 'product' | 'technology' | 'partnership' | 'expansion';
  potential: number;
  feasibility: number;
  priority: 'high' | 'medium' | 'low';
  timeline: string;
  requirements: string[];
  status: 'identified' | 'evaluating' | 'pursuing' | 'realized';
}

interface Initiative {
  id: string;
  name: string;
  description: string;
  status: 'planning' | 'in_progress' | 'completed' | 'on_hold';
  progress: number;
  budget: number;
  spent: number;
  roi: number;
  timeline: {
    start: Date;
    end: Date;
    milestones: Milestone[];
  };
  owner: string;
  team: string[];
}

interface Milestone {
  id: string;
  title: string;
  date: Date;
  status: 'pending' | 'completed' | 'delayed';
  description: string;
}

interface Visualization {
  id: string;
  type: 'line' | 'bar' | 'pie' | 'area' | 'gauge' | 'table' | 'heatmap';
  title: string;
  data: any[];
  config: any;
  insights: string[];
}

interface PresentationSlide {
  id: string;
  title: string;
  type: 'title' | 'content' | 'chart' | 'metrics' | 'summary';
  content: string;
  visualizations: string[];
  notes: string;
  order: number;
}

// Mock data generators
const generateMockReports = (): ExecutiveReport[] => {
  const types: Array<'weekly' | 'monthly' | 'quarterly' | 'annual' | 'custom'> = 
    ['weekly', 'monthly', 'quarterly', 'annual', 'custom'];
  const audiences: Array<'ceo' | 'board' | 'executives' | 'stakeholders' | 'investors'> = 
    ['ceo', 'board', 'executives', 'stakeholders', 'investors'];

  return Array.from({ length: 8 }, (_, i) => ({
    id: `report-${i + 1}`,
    title: `${types[i % types.length].charAt(0).toUpperCase() + types[i % types.length].slice(1)} Executive Report`,
    type: types[i % types.length],
    period: {
      start: new Date(2024, 0, 1),
      end: new Date(2024, 11, 31),
      label: 'Q4 2024'
    },
    status: ['draft', 'pending_review', 'approved', 'published'][i % 4] as any,
    priority: ['high', 'medium', 'low'][i % 3] as any,
    audience: audiences[i % audiences.length],
    sections: [],
    summary: {
      keyHighlights: [
        'Revenue increased 23% YoY to $4.2M',
        'Customer acquisition up 34% this quarter',
        'New product line launched successfully'
      ],
      performance: {
        revenue: {
          current: 4200000,
          previous: 3400000,
          change: 800000,
          changePercent: 23.5,
          target: 4000000,
          status: 'exceeds',
          trend: 'improving'
        },
        growth: {
          current: 23.5,
          previous: 18.2,
          change: 5.3,
          changePercent: 29.1,
          target: 20.0,
          status: 'exceeds',
          trend: 'improving'
        },
        profitability: {
          current: 18.5,
          previous: 16.8,
          change: 1.7,
          changePercent: 10.1,
          target: 18.0,
          status: 'meets',
          trend: 'improving'
        },
        efficiency: {
          current: 85.2,
          previous: 82.1,
          change: 3.1,
          changePercent: 3.8,
          target: 85.0,
          status: 'meets',
          trend: 'improving'
        }
      },
      marketPosition: {
        ranking: 3,
        marketShare: 12.4,
        competitiveAdvantage: ['Technology leadership', 'Customer service', 'Product innovation']
      },
      risks: [
        {
          id: 'risk-1',
          title: 'Market Competition',
          description: 'Increasing competition from new entrants',
          category: 'market',
          probability: 75,
          impact: 80,
          severity: 'high',
          mitigation: ['Strengthen product differentiation', 'Enhance customer loyalty programs'],
          owner: 'CMO',
          status: 'mitigating'
        }
      ],
      opportunities: [
        {
          id: 'opp-1',
          title: 'International Expansion',
          description: 'Opportunity to expand into European markets',
          category: 'expansion',
          potential: 90,
          feasibility: 70,
          priority: 'high',
          timeline: '6-12 months',
          requirements: ['Regulatory approval', 'Local partnerships', 'Marketing investment'],
          status: 'evaluating'
        }
      ],
      strategicInitiatives: [
        {
          id: 'init-1',
          name: 'Digital Transformation',
          description: 'Modernize technology infrastructure and processes',
          status: 'in_progress',
          progress: 65,
          budget: 2000000,
          spent: 1300000,
          roi: 145,
          timeline: {
            start: new Date(2024, 0, 1),
            end: new Date(2024, 11, 31),
            milestones: []
          },
          owner: 'CTO',
          team: ['Engineering', 'Operations', 'IT']
        }
      ]
    },
    kpis: [],
    insights: [],
    recommendations: [],
    metadata: {
      created: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
      updated: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000),
      author: 'Executive Assistant AI',
      version: Math.floor(Math.random() * 5) + 1,
      template: 'Executive Summary Template',
      autoGenerated: true
    },
    distribution: {
      recipients: ['<EMAIL>', '<EMAIL>'],
      scheduled: true,
      frequency: 'monthly',
      nextSend: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
      lastSent: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
    },
    presentation: {
      slides: [],
      theme: 'corporate',
      format: 'pptx'
    }
  }));
};

const generateMockKPIs = (): KPIMetric[] => {
  const categories = ['Revenue', 'Growth', 'Efficiency', 'Customer', 'Market'];
  const formats: Array<'currency' | 'percentage' | 'number' | 'ratio'> = 
    ['currency', 'percentage', 'number', 'ratio'];

  return Array.from({ length: 12 }, (_, i) => ({
    id: `kpi-${i + 1}`,
    name: `${categories[i % categories.length]} KPI ${i + 1}`,
    category: categories[i % categories.length],
    value: Math.floor(Math.random() * 1000000) + 100000,
    previousValue: Math.floor(Math.random() * 900000) + 90000,
    target: Math.floor(Math.random() * 1100000) + 110000,
    unit: formats[i % formats.length] === 'currency' ? '$' : formats[i % formats.length] === 'percentage' ? '%' : '',
    format: formats[i % formats.length],
    change: Math.floor(Math.random() * 200000) - 100000,
    changePercent: Math.floor(Math.random() * 100) - 50,
    status: ['green', 'yellow', 'red'][Math.floor(Math.random() * 3)] as any,
    importance: ['critical', 'high', 'medium', 'low'][Math.floor(Math.random() * 4)] as any,
    description: `Key performance indicator for ${categories[i % categories.length].toLowerCase()} measurement`,
    calculation: 'Calculated based on historical data and trends',
    benchmark: {
      industry: Math.floor(Math.random() * 1000000) + 100000,
      competitors: Array.from({ length: 3 }, () => Math.floor(Math.random() * 1000000) + 100000),
      historical: Array.from({ length: 12 }, () => Math.floor(Math.random() * 1000000) + 100000)
    }
  }));
};

// Sample data for charts
const performanceData = Array.from({ length: 12 }, (_, i) => ({
  month: new Date(2024, i, 1).toLocaleDateString('en', { month: 'short' }),
  revenue: Math.floor(Math.random() * 500000) + 300000,
  target: 400000,
  growth: Math.floor(Math.random() * 30) + 10,
  customers: Math.floor(Math.random() * 5000) + 2000
}));

const kpiTrendData = Array.from({ length: 30 }, (_, i) => ({
  date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toLocaleDateString(),
  performance: 70 + Math.random() * 30,
  efficiency: 80 + Math.random() * 20,
  satisfaction: 75 + Math.random() * 25
}));

const regionData = [
  { region: 'North America', revenue: 2400000, growth: 12.5, color: '#3b82f6' },
  { region: 'Europe', revenue: 1800000, growth: 18.3, color: '#10b981' },
  { region: 'Asia Pacific', revenue: 1200000, growth: 25.7, color: '#f59e0b' },
  { region: 'Latin America', revenue: 600000, growth: 15.2, color: '#ef4444' }
];

const ExecutiveReporting: React.FC = () => {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [selectedReport, setSelectedReport] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');

  const reports = useMemo(() => generateMockReports(), []);
  const kpis = useMemo(() => generateMockKPIs(), []);

  const filteredReports = useMemo(() => {
    return reports.filter(report => {
      const matchesSearch = report.title.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesStatus = filterStatus === 'all' || report.status === filterStatus;
      return matchesSearch && matchesStatus;
    });
  }, [reports, searchQuery, filterStatus]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published': return 'bg-green-100 text-green-800';
      case 'approved': return 'bg-blue-100 text-blue-800';
      case 'pending_review': return 'bg-yellow-100 text-yellow-800';
      case 'draft': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getKPIStatusColor = (status: string) => {
    switch (status) {
      case 'green': return 'text-green-600';
      case 'yellow': return 'text-yellow-600';
      case 'red': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const formatValue = (value: number, format: string, unit: string) => {
    if (format === 'currency') {
      return `$${(value / 1000000).toFixed(1)}M`;
    } else if (format === 'percentage') {
      return `${value.toFixed(1)}%`;
    } else if (format === 'number') {
      return value.toLocaleString();
    }
    return `${value}${unit}`;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Executive Reporting</h1>
          <p className="text-gray-600 mt-2">Automated executive summaries and board-ready presentations</p>
        </div>
        <div className="flex gap-3">
          <Button className="flex items-center gap-2">
            <Crown className="h-4 w-4" />
            Generate Report
          </Button>
          <Button variant="outline" className="flex items-center gap-2">
            <Presentation className="h-4 w-4" />
            Create Presentation
          </Button>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-5 w-full">
          <TabsTrigger value="dashboard" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Dashboard
          </TabsTrigger>
          <TabsTrigger value="reports" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Reports
          </TabsTrigger>
          <TabsTrigger value="kpis" className="flex items-center gap-2">
            <Target className="h-4 w-4" />
            KPI Scorecards
          </TabsTrigger>
          <TabsTrigger value="presentations" className="flex items-center gap-2">
            <Presentation className="h-4 w-4" />
            Presentations
          </TabsTrigger>
          <TabsTrigger value="insights" className="flex items-center gap-2">
            <Lightbulb className="h-4 w-4" />
            Insights
          </TabsTrigger>
        </TabsList>

        {/* Executive Dashboard */}
        <TabsContent value="dashboard" className="space-y-6">
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                    <p className="text-2xl font-bold">$4.2M</p>
                  </div>
                  <DollarSign className="h-8 w-8 text-green-500" />
                </div>
                <div className="flex items-center gap-2 mt-2">
                  <ArrowUp className="h-4 w-4 text-green-600" />
                  <span className="text-sm text-green-600">+23.5%</span>
                  <span className="text-sm text-gray-500">vs last period</span>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Growth Rate</p>
                    <p className="text-2xl font-bold">23.5%</p>
                  </div>
                  <TrendingUp className="h-8 w-8 text-blue-500" />
                </div>
                <div className="flex items-center gap-2 mt-2">
                  <ArrowUp className="h-4 w-4 text-green-600" />
                  <span className="text-sm text-green-600">+5.3%</span>
                  <span className="text-sm text-gray-500">vs target</span>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Active Customers</p>
                    <p className="text-2xl font-bold">45.2K</p>
                  </div>
                  <Users className="h-8 w-8 text-purple-500" />
                </div>
                <div className="flex items-center gap-2 mt-2">
                  <ArrowUp className="h-4 w-4 text-green-600" />
                  <span className="text-sm text-green-600">+34%</span>
                  <span className="text-sm text-gray-500">new acquisitions</span>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Market Position</p>
                    <p className="text-2xl font-bold">#3</p>
                  </div>
                  <Award className="h-8 w-8 text-orange-500" />
                </div>
                <div className="flex items-center gap-2 mt-2">
                  <span className="text-sm text-blue-600">12.4%</span>
                  <span className="text-sm text-gray-500">market share</span>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Performance Chart */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <LineChart className="h-5 w-5" />
                  Revenue Performance
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <ComposedChart data={performanceData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="revenue" fill="#3b82f6" name="Revenue" />
                      <Line type="monotone" dataKey="target" stroke="#ef4444" strokeDasharray="5 5" name="Target" />
                    </ComposedChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <PieChart className="h-5 w-5" />
                  Revenue by Region
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <RechartsPieChart>
                      <Pie
                        data={regionData}
                        cx="50%"
                        cy="50%"
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="revenue"
                        label={({ region, revenue }) => `${region}: $${(revenue / 1000000).toFixed(1)}M`}
                      >
                        {regionData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </RechartsPieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Executive Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="h-5 w-5" />
                AI-Generated Executive Summary
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div>
                  <h4 className="font-semibold mb-3">Key Highlights</h4>
                  <ul className="space-y-2">
                    <li className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span>Revenue increased 23% YoY to $4.2M, exceeding target by 5%</span>
                    </li>
                    <li className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span>Customer acquisition up 34% this quarter with improved retention</span>
                    </li>
                    <li className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span>New product line launched successfully, contributing 12% of revenue</span>
                    </li>
                    <li className="flex items-center gap-2">
                      <AlertTriangle className="h-4 w-4 text-yellow-500" />
                      <span>Increased competition requires strategic response</span>
                    </li>
                  </ul>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold mb-3">Strategic Opportunities</h4>
                    <ul className="space-y-2">
                      <li className="flex items-center gap-2">
                        <Lightbulb className="h-4 w-4 text-blue-500" />
                        <span>International expansion into European markets</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <Lightbulb className="h-4 w-4 text-blue-500" />
                        <span>AI-powered product recommendations</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <Lightbulb className="h-4 w-4 text-blue-500" />
                        <span>Strategic partnerships with industry leaders</span>
                      </li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-3">Risk Assessment</h4>
                    <ul className="space-y-2">
                      <li className="flex items-center gap-2">
                        <AlertTriangle className="h-4 w-4 text-red-500" />
                        <span>Increased market competition</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <AlertTriangle className="h-4 w-4 text-yellow-500" />
                        <span>Supply chain dependencies</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <AlertTriangle className="h-4 w-4 text-yellow-500" />
                        <span>Regulatory changes in key markets</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Reports */}
        <TabsContent value="reports" className="space-y-6">
          {/* Filters */}
          <div className="flex gap-4 items-center">
            <div className="flex-1">
              <Input
                placeholder="Search reports..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="max-w-sm"
              />
            </div>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="border rounded-md px-3 py-2"
            >
              <option value="all">All Status</option>
              <option value="published">Published</option>
              <option value="approved">Approved</option>
              <option value="pending_review">Pending Review</option>
              <option value="draft">Draft</option>
            </select>
          </div>

          {/* Reports Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredReports.map((report) => (
              <motion.div
                key={report.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
              >
                <Card className="h-full flex flex-col">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div>
                        <CardTitle className="text-lg">{report.title}</CardTitle>
                        <p className="text-sm text-gray-600 mt-1">{report.period.label}</p>
                      </div>
                      <Badge className={getStatusColor(report.status)}>
                        {report.status.replace('_', ' ')}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="flex-1 flex flex-col">
                    <div className="space-y-3 flex-1">
                      <div className="flex items-center gap-4 text-sm">
                        <div className="flex items-center gap-1">
                          <Users className="h-3 w-3" />
                          <span className="capitalize">{report.audience}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          <span className="capitalize">{report.type}</span>
                        </div>
                        <Badge variant="outline" className={
                          report.priority === 'high' ? 'border-red-500 text-red-700' :
                          report.priority === 'medium' ? 'border-yellow-500 text-yellow-700' :
                          'border-green-500 text-green-700'
                        }>
                          {report.priority}
                        </Badge>
                      </div>

                      <div className="space-y-2">
                        <h4 className="font-medium text-sm">Key Highlights:</h4>
                        <ul className="text-sm space-y-1">
                          {report.summary.keyHighlights.slice(0, 3).map((highlight, i) => (
                            <li key={i} className="flex items-center gap-2">
                              <div className="w-1 h-1 bg-blue-500 rounded-full" />
                              {highlight}
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-gray-500">Revenue:</span>
                          <div className="font-medium text-green-600">
                            ${(report.summary.performance.revenue.current / 1000000).toFixed(1)}M
                          </div>
                        </div>
                        <div>
                          <span className="text-gray-500">Growth:</span>
                          <div className="font-medium text-blue-600">
                            +{report.summary.performance.growth.changePercent.toFixed(1)}%
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="flex gap-2 mt-4">
                      <Button size="sm" className="flex-1">
                        <Eye className="h-3 w-3 mr-1" />
                        View
                      </Button>
                      <Button variant="outline" size="sm">
                        <Download className="h-3 w-3" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <Share className="h-3 w-3" />
                      </Button>
                    </div>

                    <div className="flex justify-between text-xs text-gray-500 mt-3">
                      <span>v{report.metadata.version}</span>
                      <span>{report.metadata.updated.toLocaleDateString()}</span>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </TabsContent>

        {/* KPI Scorecards */}
        <TabsContent value="kpis" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {kpis.map((kpi) => (
              <motion.div
                key={kpi.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
              >
                <Card>
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div>
                        <CardTitle className="text-lg">{kpi.name}</CardTitle>
                        <p className="text-sm text-gray-600">{kpi.category}</p>
                      </div>
                      <Badge variant="outline" className={
                        kpi.importance === 'critical' ? 'border-red-500 text-red-700' :
                        kpi.importance === 'high' ? 'border-orange-500 text-orange-700' :
                        kpi.importance === 'medium' ? 'border-yellow-500 text-yellow-700' :
                        'border-green-500 text-green-700'
                      }>
                        {kpi.importance}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="text-center">
                        <div className={`text-3xl font-bold ${getKPIStatusColor(kpi.status)}`}>
                          {formatValue(kpi.value, kpi.format, kpi.unit)}
                        </div>
                        <div className="text-sm text-gray-600">Current Value</div>
                      </div>

                      <div className="flex items-center justify-center gap-2">
                        {kpi.changePercent > 0 ? (
                          <ArrowUp className="h-4 w-4 text-green-600" />
                        ) : (
                          <ArrowDown className="h-4 w-4 text-red-600" />
                        )}
                        <span className={`text-sm font-medium ${
                          kpi.changePercent > 0 ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {kpi.changePercent > 0 ? '+' : ''}{kpi.changePercent.toFixed(1)}%
                        </span>
                        <span className="text-sm text-gray-500">vs previous</span>
                      </div>

                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-500">Target:</span>
                          <span className="font-medium">
                            {formatValue(kpi.target, kpi.format, kpi.unit)}
                          </span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-500">Previous:</span>
                          <span className="font-medium">
                            {formatValue(kpi.previousValue, kpi.format, kpi.unit)}
                          </span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-500">Industry:</span>
                          <span className="font-medium">
                            {formatValue(kpi.benchmark.industry, kpi.format, kpi.unit)}
                          </span>
                        </div>
                      </div>

                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className={`h-2 rounded-full ${
                            kpi.status === 'green' ? 'bg-green-500' :
                            kpi.status === 'yellow' ? 'bg-yellow-500' : 'bg-red-500'
                          }`}
                          style={{ 
                            width: `${Math.min(100, (kpi.value / kpi.target) * 100)}%` 
                          }}
                        />
                      </div>

                      <p className="text-xs text-gray-600">{kpi.description}</p>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                KPI Performance Trends
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <RechartsLineChart data={kpiTrendData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis domain={[60, 100]} />
                    <Tooltip />
                    <Legend />
                    <Line type="monotone" dataKey="performance" stroke="#3b82f6" strokeWidth={2} name="Performance" />
                    <Line type="monotone" dataKey="efficiency" stroke="#10b981" strokeWidth={2} name="Efficiency" />
                    <Line type="monotone" dataKey="satisfaction" stroke="#f59e0b" strokeWidth={2} name="Satisfaction" />
                    <ReferenceLine y={80} stroke="#ef4444" strokeDasharray="5 5" label="Target" />
                  </RechartsLineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Presentations */}
        <TabsContent value="presentations" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {['Q4 Board Presentation', 'Investor Update', 'Strategic Review', 'Performance Summary', 'Market Analysis', 'Growth Strategy'].map((title, i) => (
              <motion.div
                key={i}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: i * 0.1 }}
              >
                <Card>
                  <div className="relative">
                    <div className="h-40 bg-gradient-to-br from-blue-500 to-purple-600 rounded-t-lg flex items-center justify-center">
                      <Presentation className="h-12 w-12 text-white" />
                    </div>
                    <div className="absolute top-2 right-2">
                      <Badge className="bg-white text-blue-600">
                        {Math.floor(Math.random() * 20) + 10} slides
                      </Badge>
                    </div>
                  </div>
                  <CardContent className="p-4">
                    <h3 className="font-semibold mb-2">{title}</h3>
                    <p className="text-sm text-gray-600 mb-3">
                      Auto-generated presentation for executive audience
                    </p>
                    <div className="flex items-center gap-2 text-xs text-gray-500 mb-4">
                      <Calendar className="h-3 w-3" />
                      <span>Updated {Math.floor(Math.random() * 7) + 1} days ago</span>
                    </div>
                    <div className="flex gap-2">
                      <Button size="sm" className="flex-1">
                        <PlayCircle className="h-3 w-3 mr-1" />
                        Present
                      </Button>
                      <Button variant="outline" size="sm">
                        <Edit className="h-3 w-3" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <Download className="h-3 w-3" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </TabsContent>

        {/* Insights */}
        <TabsContent value="insights" className="space-y-6">
          <div className="grid grid-cols-1 gap-6">
            {[
              {
                title: 'Revenue Growth Acceleration',
                category: 'financial',
                impact: 'high',
                description: 'Revenue growth has accelerated 23% beyond projections due to successful product launches and market expansion.',
                implications: ['Exceeding annual targets', 'Increased market confidence', 'Opportunity for strategic investments'],
                confidence: 94
              },
              {
                title: 'Customer Acquisition Efficiency',
                category: 'customer',
                impact: 'medium',
                description: 'Customer acquisition costs have decreased 15% while conversion rates improved by 28%.',
                implications: ['More efficient marketing spend', 'Better targeting strategies', 'Improved customer quality'],
                confidence: 87
              },
              {
                title: 'Market Position Strengthening',
                category: 'market',
                impact: 'high',
                description: 'Market share increased to 12.4%, moving from 4th to 3rd position in the competitive landscape.',
                implications: ['Enhanced competitive advantage', 'Increased bargaining power', 'Brand recognition growth'],
                confidence: 91
              }
            ].map((insight, i) => (
              <motion.div
                key={i}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: i * 0.1 }}
              >
                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-start gap-4">
                        <div className={`p-3 rounded-lg ${
                          insight.impact === 'high' ? 'bg-red-100' :
                          insight.impact === 'medium' ? 'bg-yellow-100' : 'bg-green-100'
                        }`}>
                          <Lightbulb className={`h-6 w-6 ${
                            insight.impact === 'high' ? 'text-red-600' :
                            insight.impact === 'medium' ? 'text-yellow-600' : 'text-green-600'
                          }`} />
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold mb-2">{insight.title}</h3>
                          <p className="text-gray-600 mb-3">{insight.description}</p>
                          <div className="flex gap-2">
                            <Badge className={
                              insight.impact === 'high' ? 'bg-red-100 text-red-800' :
                              insight.impact === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-green-100 text-green-800'
                            }>
                              {insight.impact} impact
                            </Badge>
                            <Badge variant="outline">
                              {insight.category}
                            </Badge>
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-2xl font-bold text-blue-600">{insight.confidence}%</div>
                        <div className="text-sm text-gray-500">Confidence</div>
                      </div>
                    </div>

                    <div>
                      <h4 className="font-medium mb-2">Strategic Implications:</h4>
                      <ul className="space-y-1">
                        {insight.implications.map((implication, j) => (
                          <li key={j} className="flex items-center gap-2 text-sm">
                            <CheckCircle className="h-4 w-4 text-green-500" />
                            {implication}
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div className="flex justify-between items-center mt-4 pt-4 border-t">
                      <div className="text-sm text-gray-500">
                        Generated by AI Analytics • Last updated 2 hours ago
                      </div>
                      <div className="flex gap-2">
                        <Button size="sm" variant="outline">
                          <Bookmark className="h-3 w-3" />
                        </Button>
                        <Button size="sm" variant="outline">
                          <Share className="h-3 w-3" />
                        </Button>
                        <Button size="sm">
                          <ExternalLink className="h-3 w-3 mr-1" />
                          Explore
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ExecutiveReporting;