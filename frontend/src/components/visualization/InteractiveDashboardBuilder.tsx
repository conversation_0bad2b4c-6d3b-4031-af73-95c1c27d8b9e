/**
 * Interactive Dashboard Builder
 * Drag-and-drop dashboard builder with custom widget library and real-time data visualization
 */

import React, { useState, useMemo, useCallback, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  LayoutDashboard,
  Grid,
  Eye,
  Save,
  Share,
  Download,
  Copy,
  Trash2,
  Edit,
  Move,
  Lock,
  Unlock,
  RefreshCw,
  MoreHorizontal,
  ZoomIn,
  ZoomOut,
  AlignLeft,
  AlignCenter,
  AlignRight,
  BringToFront,
  SendToBack,
  Group,
  Ungroup,
  Target,
  BarChart3,
  Gauge,
  TrendingUp,
  Users,
  Package,
  MapPin,
  FileText,
  Image,
  Video,
  Hash,
  Crown,
  Star,
  MousePointer,
  XCircle,
  Zap
} from 'lucide-react';
import { 
  LineChart as RechartsLineChart, 
  Line, 
  AreaChart, 
  Area,
  BarChart, 
  Bar,
  ScatterChart,
  PieChart as RechartsPieChart,
  Cell,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  ComposedChart,
  FunnelChart,
  Funnel,
  LabelList
} from 'recharts';

// Types for Dashboard Builder
interface DashboardWidget {
  id: string;
  type: 'chart' | 'metric' | 'table' | 'text' | 'image' | 'video' | 'map' | 'gauge' | 'counter';
  title: string;
  description?: string;
  position: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  configuration: {
    dataSource?: string;
    metrics?: string[];
    dimensions?: string[];
    filters?: Filter[];
    aggregation?: string;
    timeRange?: string;
    refreshInterval?: number;
    chartType?: string;
    colorScheme?: string;
    showLegend?: boolean;
    showTooltip?: boolean;
    showGrid?: boolean;
    animation?: boolean;
    theme?: string;
    fontSize?: number;
    fontFamily?: string;
    textAlign?: 'left' | 'center' | 'right';
    backgroundColor?: string;
    borderColor?: string;
    borderWidth?: number;
    borderRadius?: number;
    padding?: number;
    margin?: number;
    customCSS?: string;
    customJS?: string;
  };
  data?: any[];
  lastUpdated?: Date;
  status: 'loading' | 'ready' | 'error' | 'empty';
  error?: string;
  locked: boolean;
  visible: boolean;
  layer: number;
}

interface DashboardLayout {
  id: string;
  name: string;
  description?: string;
  widgets: DashboardWidget[];
  settings: {
    gridSize: number;
    snapToGrid: boolean;
    backgroundColor: string;
    backgroundImage?: string;
    theme: 'light' | 'dark' | 'auto';
    responsive: boolean;
    autoRefresh: boolean;
    refreshInterval: number;
    allowEditing: boolean;
    showGrid: boolean;
    showRuler: boolean;
    showGuides: boolean;
  };
  permissions: {
    owner: string;
    editors: string[];
    viewers: string[];
    public: boolean;
  };
  metadata: {
    created: Date;
    updated: Date;
    version: number;
    tags: string[];
    category: string;
    featured: boolean;
  };
}

interface Filter {
  field: string;
  operator: 'equals' | 'not_equals' | 'contains' | 'not_contains' | 'greater_than' | 'less_than' | 'between' | 'in' | 'not_in';
  value: any;
  enabled: boolean;
}

interface WidgetTemplate {
  id: string;
  name: string;
  type: string;
  category: string;
  description: string;
  icon: string;
  preview: string;
  defaultConfig: any;
  requiredFields: string[];
  customizable: string[];
  premium: boolean;
  rating: number;
  downloads: number;
}

interface DashboardTheme {
  id: string;
  name: string;
  colors: {
    primary: string;
    secondary: string;
    background: string;
    surface: string;
    text: string;
    border: string;
    accent: string;
  };
  typography: {
    fontFamily: string;
    fontSize: {
      small: number;
      medium: number;
      large: number;
    };
  };
  spacing: {
    small: number;
    medium: number;
    large: number;
  };
  borderRadius: number;
  shadows: boolean;
  animations: boolean;
}

// Mock data generators
const generateMockWidgets = (): DashboardWidget[] => {
  const types: Array<'chart' | 'metric' | 'table' | 'text' | 'image' | 'video' | 'map' | 'gauge' | 'counter'> = 
    ['chart', 'metric', 'table', 'text', 'gauge', 'counter'];
  
  return Array.from({ length: 8 }, (_, i) => ({
    id: `widget-${i + 1}`,
    type: types[i % types.length],
    title: `${types[i % types.length]} Widget ${i + 1}`,
    description: `Sample ${types[i % types.length]} widget for dashboard`,
    position: {
      x: (i % 4) * 300,
      y: Math.floor(i / 4) * 200,
      width: 280,
      height: 180
    },
    configuration: {
      dataSource: 'sales_data',
      chartType: ['line', 'bar', 'pie', 'area'][i % 4],
      colorScheme: 'blue',
      showLegend: true,
      showTooltip: true,
      animation: true,
      refreshInterval: 60000
    },
    data: Array.from({ length: 10 }, (_, j) => ({
      name: `Item ${j + 1}`,
      value: Math.floor(Math.random() * 1000) + 100,
      category: ['A', 'B', 'C'][j % 3]
    })),
    lastUpdated: new Date(),
    status: 'ready',
    locked: false,
    visible: true,
    layer: 1
  }));
};

const generateMockWidgetTemplates = (): WidgetTemplate[] => {
  const categories = ['Charts', 'Metrics', 'Tables', 'Media', 'Maps', 'Gauges', 'Counters', 'Text'];
  const types = ['line_chart', 'bar_chart', 'pie_chart', 'area_chart', 'metric_card', 'data_table', 'gauge', 'counter'];

  return Array.from({ length: 24 }, (_, i) => ({
    id: `template-${i + 1}`,
    name: `${types[i % types.length].replace('_', ' ')} Template`,
    type: types[i % types.length],
    category: categories[i % categories.length],
    description: `Professional ${types[i % types.length].replace('_', ' ')} widget template`,
    icon: 'BarChart3',
    preview: `/api/placeholder/200/150`,
    defaultConfig: {
      colorScheme: 'blue',
      showLegend: true,
      animation: true
    },
    requiredFields: ['dataSource'],
    customizable: ['colors', 'legend', 'animation'],
    premium: Math.random() > 0.7,
    rating: 3.5 + Math.random() * 1.5,
    downloads: Math.floor(Math.random() * 10000)
  }));
};

const generateMockDashboards = (): DashboardLayout[] => {
  return Array.from({ length: 6 }, (_, i) => ({
    id: `dashboard-${i + 1}`,
    name: `Dashboard ${i + 1}`,
    description: `Sample dashboard layout ${i + 1}`,
    widgets: generateMockWidgets(),
    settings: {
      gridSize: 20,
      snapToGrid: true,
      backgroundColor: '#ffffff',
      theme: 'light' as const,
      responsive: true,
      autoRefresh: true,
      refreshInterval: 300000,
      allowEditing: true,
      showGrid: true,
      showRuler: false,
      showGuides: false
    },
    permissions: {
      owner: 'user-1',
      editors: ['user-2', 'user-3'],
      viewers: ['user-4', 'user-5'],
      public: i % 2 === 0
    },
    metadata: {
      created: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
      updated: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000),
      version: Math.floor(Math.random() * 10) + 1,
      tags: ['analytics', 'sales', 'performance'].slice(0, Math.floor(Math.random() * 3) + 1),
      category: ['Sales', 'Marketing', 'Operations', 'Finance'][i % 4],
      featured: Math.random() > 0.7
    }
  }));
};

// Sample data for charts
const sampleLineData = Array.from({ length: 12 }, (_, i) => ({
  month: new Date(2024, i, 1).toLocaleDateString('en', { month: 'short' }),
  sales: Math.floor(Math.random() * 50000) + 10000,
  orders: Math.floor(Math.random() * 1000) + 200,
  customers: Math.floor(Math.random() * 500) + 100
}));

const samplePieData = [
  { name: 'Desktop', value: 45, color: '#3b82f6' },
  { name: 'Mobile', value: 35, color: '#10b981' },
  { name: 'Tablet', value: 20, color: '#f59e0b' }
];

const sampleBarData = Array.from({ length: 7 }, (_, i) => ({
  day: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'][i],
  revenue: Math.floor(Math.random() * 10000) + 2000,
  visitors: Math.floor(Math.random() * 2000) + 500
}));

const InteractiveDashboardBuilder: React.FC = () => {
  const [activeTab, setActiveTab] = useState('builder');
  const [selectedWidget, setSelectedWidget] = useState<string | null>(null);
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);
  const [dashboards, setDashboards] = useState<DashboardLayout[]>(() => generateMockDashboards());
  const [currentDashboard, setCurrentDashboard] = useState<DashboardLayout>(dashboards[0]);
  const [widgetTemplates] = useState<WidgetTemplate[]>(() => generateMockWidgetTemplates());
  const [isEditing, setIsEditing] = useState(true);
  const [showGrid, setShowGrid] = useState(true);
  const [snapToGrid, setSnapToGrid] = useState(true);
  const [zoom, setZoom] = useState(100);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterCategory, setFilterCategory] = useState('all');

  const canvasRef = useRef<HTMLDivElement>(null);

  const filteredTemplates = useMemo(() => {
    return widgetTemplates.filter(template => {
      const matchesSearch = template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           template.description.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesCategory = filterCategory === 'all' || template.category === filterCategory;
      return matchesSearch && matchesCategory;
    });
  }, [widgetTemplates, searchQuery, filterCategory]);

  const getWidgetIcon = (type: string) => {
    switch (type) {
      case 'chart': return <BarChart3 className="h-4 w-4" />;
      case 'metric': return <Gauge className="h-4 w-4" />;
      case 'table': return <Grid className="h-4 w-4" />;
      case 'text': return <FileText className="h-4 w-4" />;
      case 'image': return <Image className="h-4 w-4" />;
      case 'video': return <Video className="h-4 w-4" />;
      case 'map': return <MapPin className="h-4 w-4" />;
      case 'gauge': return <Gauge className="h-4 w-4" />;
      case 'counter': return <Hash className="h-4 w-4" />;
      default: return <Package className="h-4 w-4" />;
    }
  };

  const renderWidget = (widget: DashboardWidget) => {
    const { type, configuration, data } = widget;

    switch (type) {
      case 'chart':
        if (configuration.chartType === 'line') {
          return (
            <ResponsiveContainer width="100%" height="100%">
              <RechartsLineChart data={sampleLineData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" fontSize={10} />
                <YAxis fontSize={10} />
                <Tooltip />
                <Legend />
                <Line type="monotone" dataKey="sales" stroke="#3b82f6" strokeWidth={2} />
              </RechartsLineChart>
            </ResponsiveContainer>
          );
        } else if (configuration.chartType === 'bar') {
          return (
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={sampleBarData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="day" fontSize={10} />
                <YAxis fontSize={10} />
                <Tooltip />
                <Bar dataKey="revenue" fill="#3b82f6" />
              </BarChart>
            </ResponsiveContainer>
          );
        } else if (configuration.chartType === 'pie') {
          return (
            <ResponsiveContainer width="100%" height="100%">
              <RechartsPieChart>
                <Pie
                  data={samplePieData}
                  cx="50%"
                  cy="50%"
                  outerRadius={60}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {samplePieData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </RechartsPieChart>
            </ResponsiveContainer>
          );
        } else {
          return (
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart data={sampleLineData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" fontSize={10} />
                <YAxis fontSize={10} />
                <Tooltip />
                <Area type="monotone" dataKey="sales" stroke="#3b82f6" fill="#3b82f6" fillOpacity={0.6} />
              </AreaChart>
            </ResponsiveContainer>
          );
        }

      case 'metric':
        return (
          <div className="flex flex-col items-center justify-center h-full">
            <div className="text-3xl font-bold text-blue-600">$47.2K</div>
            <div className="text-sm text-gray-600">Total Revenue</div>
            <div className="flex items-center gap-1 text-sm text-green-600 mt-2">
              <TrendingUp className="h-3 w-3" />
              +12.5%
            </div>
          </div>
        );

      case 'gauge':
        return (
          <div className="flex flex-col items-center justify-center h-full">
            <div className="relative w-24 h-24">
              <svg className="w-full h-full transform -rotate-90" viewBox="0 0 100 100">
                <circle
                  cx="50"
                  cy="50"
                  r="40"
                  fill="none"
                  stroke="#e5e7eb"
                  strokeWidth="8"
                />
                <circle
                  cx="50"
                  cy="50"
                  r="40"
                  fill="none"
                  stroke="#3b82f6"
                  strokeWidth="8"
                  strokeDasharray={`${75 * 2.51} ${100 * 2.51}`}
                  strokeLinecap="round"
                />
              </svg>
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-lg font-bold">75%</span>
              </div>
            </div>
            <div className="text-sm text-gray-600 mt-2">Performance</div>
          </div>
        );

      case 'counter':
        return (
          <div className="flex flex-col items-center justify-center h-full">
            <div className="text-4xl font-bold text-purple-600">1,247</div>
            <div className="text-sm text-gray-600">Active Users</div>
            <div className="flex items-center gap-1 text-sm text-green-600 mt-2">
              <Users className="h-3 w-3" />
              Online Now
            </div>
          </div>
        );

      case 'table':
        return (
          <div className="p-2 h-full overflow-auto">
            <table className="w-full text-xs">
              <thead>
                <tr className="border-b">
                  <th className="text-left p-1">Product</th>
                  <th className="text-left p-1">Sales</th>
                  <th className="text-left p-1">Revenue</th>
                </tr>
              </thead>
              <tbody>
                {Array.from({ length: 5 }, (_, i) => (
                  <tr key={i} className="border-b">
                    <td className="p-1">Product {i + 1}</td>
                    <td className="p-1">{Math.floor(Math.random() * 1000)}</td>
                    <td className="p-1">${Math.floor(Math.random() * 10000)}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        );

      case 'text':
        return (
          <div className="p-4 h-full flex items-center justify-center">
            <div className="text-center">
              <h3 className="font-semibold text-lg mb-2">Welcome to Dashboard</h3>
              <p className="text-sm text-gray-600">
                This is a sample text widget. You can customize the content, styling, and layout.
              </p>
            </div>
          </div>
        );

      default:
        return (
          <div className="flex items-center justify-center h-full text-gray-400">
            <div className="text-center">
              {getWidgetIcon(type)}
              <div className="text-xs mt-1">{type}</div>
            </div>
          </div>
        );
    }
  };

  const addWidget = (template: WidgetTemplate) => {
    const newWidget: DashboardWidget = {
      id: `widget-${Date.now()}`,
      type: template.type as any,
      title: template.name,
      position: {
        x: 50,
        y: 50,
        width: 280,
        height: 200
      },
      configuration: template.defaultConfig,
      status: 'ready',
      locked: false,
      visible: true,
      layer: 1
    };

    setCurrentDashboard(prev => ({
      ...prev,
      widgets: [...prev.widgets, newWidget]
    }));
  };

  const removeWidget = (widgetId: string) => {
    setCurrentDashboard(prev => ({
      ...prev,
      widgets: prev.widgets.filter(w => w.id !== widgetId)
    }));
    setSelectedWidget(null);
  };

  const duplicateWidget = (widgetId: string) => {
    const widget = currentDashboard.widgets.find(w => w.id === widgetId);
    if (widget) {
      const newWidget: DashboardWidget = {
        ...widget,
        id: `widget-${Date.now()}`,
        title: `${widget.title} (Copy)`,
        position: {
          ...widget.position,
          x: widget.position.x + 20,
          y: widget.position.y + 20
        }
      };

      setCurrentDashboard(prev => ({
        ...prev,
        widgets: [...prev.widgets, newWidget]
      }));
    }
  };

  return (
    <div className="h-screen flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b bg-white">
        <div className="flex items-center gap-4">
          <h1 className="text-xl font-bold">Dashboard Builder</h1>
          <div className="flex items-center gap-2">
            <select
              value={currentDashboard.id}
              onChange={(e) => {
                const dashboard = dashboards.find(d => d.id === e.target.value);
                if (dashboard) setCurrentDashboard(dashboard);
              }}
              className="border rounded-md px-3 py-1"
            >
              {dashboards.map(dashboard => (
                <option key={dashboard.id} value={dashboard.id}>
                  {dashboard.name}
                </option>
              ))}
            </select>
            <Button
              variant={isEditing ? 'default' : 'outline'}
              size="sm"
              onClick={() => setIsEditing(!isEditing)}
            >
              {isEditing ? <Edit className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              {isEditing ? 'Edit' : 'View'}
            </Button>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-2 text-sm">
            <span>Zoom:</span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setZoom(Math.max(25, zoom - 25))}
              disabled={zoom <= 25}
            >
              <ZoomOut className="h-3 w-3" />
            </Button>
            <span className="min-w-[50px] text-center">{zoom}%</span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setZoom(Math.min(200, zoom + 25))}
              disabled={zoom >= 200}
            >
              <ZoomIn className="h-3 w-3" />
            </Button>
          </div>
          <Button variant="outline" size="sm">
            <Save className="h-4 w-4 mr-1" />
            Save
          </Button>
          <Button variant="outline" size="sm">
            <Share className="h-4 w-4 mr-1" />
            Share
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-1" />
            Export
          </Button>
        </div>
      </div>

      <div className="flex-1 flex">
        {/* Sidebar */}
        {isEditing && (
          <div className="w-80 border-r bg-gray-50 flex flex-col">
            <Tabs defaultValue="widgets" className="flex-1 flex flex-col">
              <TabsList className="grid grid-cols-3 m-2">
                <TabsTrigger value="widgets">Widgets</TabsTrigger>
                <TabsTrigger value="layouts">Layouts</TabsTrigger>
                <TabsTrigger value="settings">Settings</TabsTrigger>
              </TabsList>

              {/* Widget Library */}
              <TabsContent value="widgets" className="flex-1 flex flex-col p-2 space-y-4">
                <div className="space-y-2">
                  <Input
                    placeholder="Search widgets..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                  <select
                    value={filterCategory}
                    onChange={(e) => setFilterCategory(e.target.value)}
                    className="w-full border rounded-md px-3 py-2"
                  >
                    <option value="all">All Categories</option>
                    <option value="Charts">Charts</option>
                    <option value="Metrics">Metrics</option>
                    <option value="Tables">Tables</option>
                    <option value="Media">Media</option>
                  </select>
                </div>

                <div className="flex-1 overflow-auto space-y-2">
                  {filteredTemplates.map((template) => (
                    <motion.div
                      key={template.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      whileHover={{ scale: 1.02 }}
                      className="cursor-pointer"
                      onClick={() => addWidget(template)}
                    >
                      <Card className="p-3 hover:shadow-md transition-shadow">
                        <div className="flex items-start gap-3">
                          <img 
                            src={template.preview} 
                            alt={template.name}
                            className="w-12 h-12 object-cover rounded"
                          />
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2">
                              <h4 className="font-medium text-sm truncate">{template.name}</h4>
                              {template.premium && (
                                <Crown className="h-3 w-3 text-yellow-500" />
                              )}
                            </div>
                            <p className="text-xs text-gray-600 line-clamp-2">{template.description}</p>
                            <div className="flex items-center gap-2 mt-1">
                              <div className="flex items-center gap-1">
                                <Star className="h-3 w-3 text-yellow-500" />
                                <span className="text-xs">{template.rating.toFixed(1)}</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <Download className="h-3 w-3 text-gray-400" />
                                <span className="text-xs">{template.downloads.toLocaleString()}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </Card>
                    </motion.div>
                  ))}
                </div>
              </TabsContent>

              {/* Layouts */}
              <TabsContent value="layouts" className="flex-1 p-2 space-y-4">
                <div className="space-y-2">
                  {dashboards.map((dashboard) => (
                    <Card 
                      key={dashboard.id} 
                      className={`p-3 cursor-pointer transition-colors ${
                        dashboard.id === currentDashboard.id ? 'ring-2 ring-blue-500' : 'hover:bg-gray-50'
                      }`}
                      onClick={() => setCurrentDashboard(dashboard)}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium text-sm">{dashboard.name}</h4>
                          <p className="text-xs text-gray-600">{dashboard.widgets.length} widgets</p>
                          <div className="flex gap-1 mt-1">
                            {dashboard.metadata.tags.map(tag => (
                              <Badge key={tag} variant="secondary" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                          </div>
                        </div>
                        <div className="flex gap-1">
                          <Button variant="outline" size="sm">
                            <Copy className="h-3 w-3" />
                          </Button>
                          <Button variant="outline" size="sm">
                            <MoreHorizontal className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              </TabsContent>

              {/* Settings */}
              <TabsContent value="settings" className="flex-1 p-2 space-y-4">
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-sm mb-2">Canvas Settings</h4>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Show Grid</span>
                        <Button
                          variant={showGrid ? 'default' : 'outline'}
                          size="sm"
                          onClick={() => setShowGrid(!showGrid)}
                        >
                          <Grid className="h-3 w-3" />
                        </Button>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Snap to Grid</span>
                        <Button
                          variant={snapToGrid ? 'default' : 'outline'}
                          size="sm"
                          onClick={() => setSnapToGrid(!snapToGrid)}
                        >
                          <Target className="h-3 w-3" />
                        </Button>
                      </div>
                      <div>
                        <label className="block text-sm mb-1">Grid Size</label>
                        <Input
                          type="number"
                          value={currentDashboard.settings.gridSize}
                          onChange={(e) => setCurrentDashboard(prev => ({
                            ...prev,
                            settings: { ...prev.settings, gridSize: parseInt(e.target.value) || 20 }
                          }))}
                          min="10"
                          max="50"
                        />
                      </div>
                      <div>
                        <label className="block text-sm mb-1">Background Color</label>
                        <Input
                          type="color"
                          value={currentDashboard.settings.backgroundColor}
                          onChange={(e) => setCurrentDashboard(prev => ({
                            ...prev,
                            settings: { ...prev.settings, backgroundColor: e.target.value }
                          }))}
                        />
                      </div>
                      <div>
                        <label className="block text-sm mb-1">Theme</label>
                        <select
                          value={currentDashboard.settings.theme}
                          onChange={(e) => setCurrentDashboard(prev => ({
                            ...prev,
                            settings: { ...prev.settings, theme: e.target.value as any }
                          }))}
                          className="w-full border rounded-md px-3 py-2"
                        >
                          <option value="light">Light</option>
                          <option value="dark">Dark</option>
                          <option value="auto">Auto</option>
                        </select>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium text-sm mb-2">Auto Refresh</h4>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Enable Auto Refresh</span>
                        <Button
                          variant={currentDashboard.settings.autoRefresh ? 'default' : 'outline'}
                          size="sm"
                          onClick={() => setCurrentDashboard(prev => ({
                            ...prev,
                            settings: { ...prev.settings, autoRefresh: !prev.settings.autoRefresh }
                          }))}
                        >
                          <RefreshCw className="h-3 w-3" />
                        </Button>
                      </div>
                      <div>
                        <label className="block text-sm mb-1">Interval (seconds)</label>
                        <Input
                          type="number"
                          value={currentDashboard.settings.refreshInterval / 1000}
                          onChange={(e) => setCurrentDashboard(prev => ({
                            ...prev,
                            settings: { ...prev.settings, refreshInterval: (parseInt(e.target.value) || 60) * 1000 }
                          }))}
                          min="10"
                          max="3600"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        )}

        {/* Canvas */}
        <div className="flex-1 flex flex-col">
          {/* Toolbar */}
          {isEditing && (
            <div className="flex items-center gap-2 p-2 border-b bg-white">
              <Button variant="outline" size="sm">
                <Move className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="sm">
                <MousePointer className="h-4 w-4" />
              </Button>
              <div className="w-px h-6 bg-gray-300" />
              <Button variant="outline" size="sm">
                <AlignLeft className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="sm">
                <AlignCenter className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="sm">
                <AlignRight className="h-4 w-4" />
              </Button>
              <div className="w-px h-6 bg-gray-300" />
              <Button variant="outline" size="sm">
                <BringToFront className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="sm">
                <SendToBack className="h-4 w-4" />
              </Button>
              <div className="w-px h-6 bg-gray-300" />
              <Button variant="outline" size="sm">
                <Group className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="sm">
                <Ungroup className="h-4 w-4" />
              </Button>
            </div>
          )}

          {/* Dashboard Canvas */}
          <div 
            className="flex-1 overflow-auto relative"
            style={{ 
              backgroundColor: currentDashboard.settings.backgroundColor,
              backgroundImage: showGrid ? `
                linear-gradient(to right, #e5e7eb 1px, transparent 1px),
                linear-gradient(to bottom, #e5e7eb 1px, transparent 1px)
              ` : undefined,
              backgroundSize: showGrid ? `${currentDashboard.settings.gridSize}px ${currentDashboard.settings.gridSize}px` : undefined
            }}
          >
            <div 
              ref={canvasRef}
              className="relative min-h-full"
              style={{ 
                transform: `scale(${zoom / 100})`,
                transformOrigin: 'top left',
                width: `${10000 / (zoom / 100)}px`,
                height: `${6000 / (zoom / 100)}px`
              }}
            >
              {currentDashboard.widgets.map((widget) => (
                <motion.div
                  key={widget.id}
                  className={`absolute border-2 rounded-lg overflow-hidden cursor-pointer ${
                    selectedWidget === widget.id 
                      ? 'border-blue-500 shadow-lg' 
                      : isEditing 
                        ? 'border-gray-300 hover:border-gray-400' 
                        : 'border-transparent'
                  }`}
                  style={{
                    left: widget.position.x,
                    top: widget.position.y,
                    width: widget.position.width,
                    height: widget.position.height,
                    zIndex: widget.layer,
                    backgroundColor: 'white'
                  }}
                  onClick={(e) => {
                    e.stopPropagation();
                    if (isEditing) {
                      setSelectedWidget(widget.id);
                    }
                  }}
                  whileHover={isEditing ? { scale: 1.02 } : {}}
                  transition={{ type: "spring", stiffness: 300, damping: 30 }}
                >
                  {/* Widget Header */}
                  {isEditing && (
                    <div className="absolute top-0 left-0 right-0 bg-gray-50 border-b p-1 flex items-center justify-between text-xs">
                      <div className="flex items-center gap-1">
                        {getWidgetIcon(widget.type)}
                        <span className="font-medium truncate">{widget.title}</span>
                      </div>
                      <div className="flex gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-5 w-5 p-0"
                          onClick={(e) => {
                            e.stopPropagation();
                            duplicateWidget(widget.id);
                          }}
                        >
                          <Copy className="h-3 w-3" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-5 w-5 p-0"
                          onClick={(e) => {
                            e.stopPropagation();
                            removeWidget(widget.id);
                          }}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  )}

                  {/* Widget Content */}
                  <div 
                    className={`h-full ${isEditing ? 'pt-7' : ''}`}
                    style={{ pointerEvents: isEditing ? 'none' : 'auto' }}
                  >
                    {renderWidget(widget)}
                  </div>

                  {/* Resize Handles */}
                  {isEditing && selectedWidget === widget.id && (
                    <>
                      <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-blue-500 border border-white rounded cursor-se-resize" />
                      <div className="absolute -bottom-1 left-1/2 w-3 h-3 bg-blue-500 border border-white rounded cursor-s-resize transform -translate-x-1/2" />
                      <div className="absolute top-1/2 -right-1 w-3 h-3 bg-blue-500 border border-white rounded cursor-e-resize transform -translate-y-1/2" />
                    </>
                  )}
                </motion.div>
              ))}

              {/* Drop Zone Indicator */}
              {isEditing && currentDashboard.widgets.length === 0 && (
                <div className="absolute inset-0 flex items-center justify-center text-gray-400">
                  <div className="text-center">
                    <LayoutDashboard className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p className="text-lg font-medium">Start Building Your Dashboard</p>
                    <p className="text-sm">Drag widgets from the sidebar to get started</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Properties Panel */}
        {isEditing && selectedWidget && (
          <div className="w-80 border-l bg-gray-50 p-4 space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="font-medium">Widget Properties</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSelectedWidget(null)}
              >
                <XCircle className="h-4 w-4" />
              </Button>
            </div>

            {(() => {
              const widget = currentDashboard.widgets.find(w => w.id === selectedWidget);
              if (!widget) return null;

              return (
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">Title</label>
                    <Input
                      value={widget.title}
                      onChange={(e) => {
                        setCurrentDashboard(prev => ({
                          ...prev,
                          widgets: prev.widgets.map(w => 
                            w.id === selectedWidget 
                              ? { ...w, title: e.target.value }
                              : w
                          )
                        }));
                      }}
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <label className="block text-sm font-medium mb-1">Width</label>
                      <Input
                        type="number"
                        value={widget.position.width}
                        onChange={(e) => {
                          setCurrentDashboard(prev => ({
                            ...prev,
                            widgets: prev.widgets.map(w => 
                              w.id === selectedWidget 
                                ? { ...w, position: { ...w.position, width: parseInt(e.target.value) || 280 } }
                                : w
                            )
                          }));
                        }}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">Height</label>
                      <Input
                        type="number"
                        value={widget.position.height}
                        onChange={(e) => {
                          setCurrentDashboard(prev => ({
                            ...prev,
                            widgets: prev.widgets.map(w => 
                              w.id === selectedWidget 
                                ? { ...w, position: { ...w.position, height: parseInt(e.target.value) || 200 } }
                                : w
                            )
                          }));
                        }}
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <label className="block text-sm font-medium mb-1">X Position</label>
                      <Input
                        type="number"
                        value={widget.position.x}
                        onChange={(e) => {
                          setCurrentDashboard(prev => ({
                            ...prev,
                            widgets: prev.widgets.map(w => 
                              w.id === selectedWidget 
                                ? { ...w, position: { ...w.position, x: parseInt(e.target.value) || 0 } }
                                : w
                            )
                          }));
                        }}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">Y Position</label>
                      <Input
                        type="number"
                        value={widget.position.y}
                        onChange={(e) => {
                          setCurrentDashboard(prev => ({
                            ...prev,
                            widgets: prev.widgets.map(w => 
                              w.id === selectedWidget 
                                ? { ...w, position: { ...w.position, y: parseInt(e.target.value) || 0 } }
                                : w
                            )
                          }));
                        }}
                      />
                    </div>
                  </div>

                  {widget.type === 'chart' && (
                    <div>
                      <label className="block text-sm font-medium mb-1">Chart Type</label>
                      <select
                        value={widget.configuration.chartType || 'line'}
                        onChange={(e) => {
                          setCurrentDashboard(prev => ({
                            ...prev,
                            widgets: prev.widgets.map(w => 
                              w.id === selectedWidget 
                                ? { ...w, configuration: { ...w.configuration, chartType: e.target.value } }
                                : w
                            )
                          }));
                        }}
                        className="w-full border rounded-md px-3 py-2"
                      >
                        <option value="line">Line Chart</option>
                        <option value="bar">Bar Chart</option>
                        <option value="pie">Pie Chart</option>
                        <option value="area">Area Chart</option>
                      </select>
                    </div>
                  )}

                  <div>
                    <label className="block text-sm font-medium mb-1">Color Scheme</label>
                    <select
                      value={widget.configuration.colorScheme || 'blue'}
                      onChange={(e) => {
                        setCurrentDashboard(prev => ({
                          ...prev,
                          widgets: prev.widgets.map(w => 
                            w.id === selectedWidget 
                              ? { ...w, configuration: { ...w.configuration, colorScheme: e.target.value } }
                              : w
                          )
                        }));
                      }}
                      className="w-full border rounded-md px-3 py-2"
                    >
                      <option value="blue">Blue</option>
                      <option value="green">Green</option>
                      <option value="purple">Purple</option>
                      <option value="red">Red</option>
                      <option value="orange">Orange</option>
                    </select>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Show Legend</span>
                      <Button
                        variant={widget.configuration.showLegend ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => {
                          setCurrentDashboard(prev => ({
                            ...prev,
                            widgets: prev.widgets.map(w => 
                              w.id === selectedWidget 
                                ? { ...w, configuration: { ...w.configuration, showLegend: !w.configuration.showLegend } }
                                : w
                            )
                          }));
                        }}
                      >
                        <Eye className="h-3 w-3" />
                      </Button>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Animation</span>
                      <Button
                        variant={widget.configuration.animation ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => {
                          setCurrentDashboard(prev => ({
                            ...prev,
                            widgets: prev.widgets.map(w => 
                              w.id === selectedWidget 
                                ? { ...w, configuration: { ...w.configuration, animation: !w.configuration.animation } }
                                : w
                            )
                          }));
                        }}
                      >
                        <Zap className="h-3 w-3" />
                      </Button>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Lock Widget</span>
                      <Button
                        variant={widget.locked ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => {
                          setCurrentDashboard(prev => ({
                            ...prev,
                            widgets: prev.widgets.map(w => 
                              w.id === selectedWidget 
                                ? { ...w, locked: !w.locked }
                                : w
                            )
                          }));
                        }}
                      >
                        {widget.locked ? <Lock className="h-3 w-3" /> : <Unlock className="h-3 w-3" />}
                      </Button>
                    </div>
                  </div>
                </div>
              );
            })()}
          </div>
        )}
      </div>
    </div>
  );
};

export default InteractiveDashboardBuilder;