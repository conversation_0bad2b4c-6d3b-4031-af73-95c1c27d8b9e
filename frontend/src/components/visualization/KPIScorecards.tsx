/**
 * KPI Scorecards & Performance Tracking
 * Real-time KPI monitoring, performance scorecards, and goal tracking
 */

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Target,
  TrendingUp,
  TrendingDown,
  Gauge,
  Award,
  AlertTriangle,
  CheckCircle,
  Clock,
  Calendar,
  Users,
  DollarSign,
  ShoppingCart,
  Package,
  Activity,
  BarChart3,
  LineChart,
  PieChart,
  Star,
  Flag,
  Zap,
  Eye,
  Settings,
  Filter,
  Search,
  RefreshCw,
  Download,
  Share,
  Edit,
  Plus,
  Minus,
  ArrowUp,
  ArrowDown,
  MoreHorizontal,
  ExternalLink,
  Bookmark,
  Bell,
  BellRing,
  BellOff,
  Info,
  Lightbulb,
  Brain,
  Crown,
  Trophy,
  Medal,
  Percent,
  Hash,
  Globe,
  MapPin,
  Building2,
  Store,
  Factory,
  Warehouse,
  Home,
  Office,
  Smartphone,
  Monitor,
  Tablet,
  Laptop,
  Watch,
  Camera,
  Mic,
  Speaker,
  Headphones,
  Volume2,
  VolumeX,
  Play,
  Pause,
  Stop,
  SkipForward,
  SkipBack,
  FastForward,
  Rewind,
  Repeat,
  Shuffle,
  Mail,
  Phone,
  MessageCircle,
  Send,
  Inbox,
  Archive,
  Trash,
  Folder,
  File,
  FileText,
  Image,
  Video,
  Music,
  Code,
  Terminal,
  GitBranch,
  GitCommit,
  GitMerge,
  GitPullRequest,
  Layers,
  Grid,
  List,
  Layout,
  Sidebar,
  PanelLeft,
  PanelRight,
  Menu,
  X,
  ChevronUp,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  ArrowLeft,
  ArrowRight,
  Upload,
  Link,
  Unlink,
  Copy,
  Cut,
  Paste,
  Scissors,
  PaintBucket,
  Palette,
  Brush,
  Eraser,
  Ruler,
  Move,
  RotateCcw,
  RotateCw,
  FlipHorizontal,
  FlipVertical,
  Crop,
  Maximize,
  Minimize,
  Square,
  Circle,
  Triangle,
  Hexagon,
  Octagon,
  Pentagon,
  Diamond,
  Heart,
  Sparkles,
  Flame,
  Snowflake,
  Sun,
  Moon,
  CloudRain,
  CloudSnow,
  Thermometer,
  Wind,
  Umbrella,
  Sunset,
  Sunrise,
  Mountain,
  Tree,
  Flower,
  Leaf,
  Bug,
  Fish,
  Bird,
  Cat,
  Dog,
  Rabbit,
  Bear,
  Lion,
  Tiger,
  Elephant,
  Horse,
  Cow,
  Pig,
  Sheep,
  Chicken,
  Egg
} from 'lucide-react';
import { 
  LineChart as RechartsLineChart, 
  Line, 
  AreaChart, 
  Area, 
  BarChart, 
  Bar, 
  PieChart as RechartsPieChart,
  Cell,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  RadialBarChart,
  RadialBar,
  CircularProgressbar,
  buildStyles
} from 'recharts';

// Types for KPI Scorecards & Performance Tracking
interface KPIMetric {
  id: string;
  name: string;
  description: string;
  category: 'revenue' | 'growth' | 'efficiency' | 'quality' | 'customer' | 'operational' | 'financial' | 'marketing' | 'sales' | 'support';
  currentValue: number;
  targetValue: number;
  previousValue: number;
  unit: string;
  format: 'number' | 'currency' | 'percentage' | 'ratio' | 'time' | 'decimal';
  trend: 'up' | 'down' | 'stable';
  trendPercentage: number;
  status: 'on_track' | 'at_risk' | 'off_track' | 'exceeded' | 'critical';
  priority: 'critical' | 'high' | 'medium' | 'low';
  frequency: 'real_time' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'annually';
  dataSource: string;
  lastUpdated: Date;
  historicalData: HistoricalDataPoint[];
  thresholds: KPIThreshold;
  owner: string;
  department: string;
  tags: string[];
  metadata: {
    formula: string;
    calculation_method: string;
    data_quality_score: number;
    confidence_level: number;
    sample_size: number;
    benchmark_value?: number;
    industry_average?: number;
    competitor_average?: number;
    seasonal_adjustment: boolean;
    forecasted_value?: number;
    forecast_confidence?: number;
  };
}

interface HistoricalDataPoint {
  date: Date;
  value: number;
  target: number;
  forecast?: number;
  actual?: number;
}

interface KPIThreshold {
  excellent: { min: number; max: number; color: string };
  good: { min: number; max: number; color: string };
  fair: { min: number; max: number; color: string };
  poor: { min: number; max: number; color: string };
  critical: { min: number; max: number; color: string };
}

interface Scorecard {
  id: string;
  name: string;
  description: string;
  type: 'balanced' | 'custom' | 'department' | 'executive' | 'operational' | 'strategic';
  perspective: 'financial' | 'customer' | 'internal' | 'learning' | 'mixed';
  owner: string;
  department: string;
  visibility: 'public' | 'department' | 'executive' | 'private';
  status: 'active' | 'draft' | 'archived';
  kpis: string[];
  layout: ScorecardLayout;
  settings: ScorecardSettings;
  created_at: Date;
  updated_at: Date;
  performance_score: number;
  risk_score: number;
  trend_score: number;
  benchmark_comparison: BenchmarkComparison;
  alerts: ScorecardAlert[];
  comments: ScorecardComment[];
  attachments: string[];
  sharing: SharingSettings;
}

interface ScorecardLayout {
  grid_size: { rows: number; cols: number };
  widgets: ScorecardWidget[];
  theme: 'light' | 'dark' | 'auto';
  color_scheme: string;
  auto_refresh: boolean;
  refresh_interval: number;
}

interface ScorecardWidget {
  id: string;
  type: 'kpi_card' | 'chart' | 'gauge' | 'table' | 'text' | 'image' | 'embed';
  position: { x: number; y: number; width: number; height: number };
  config: WidgetConfig;
  data_binding: DataBinding;
  styling: WidgetStyling;
  interactions: WidgetInteraction[];
}

interface WidgetConfig {
  title: string;
  subtitle?: string;
  show_trend: boolean;
  show_target: boolean;
  show_benchmark: boolean;
  show_forecast: boolean;
  time_period: string;
  aggregation: 'sum' | 'avg' | 'min' | 'max' | 'count' | 'distinct';
  filters: FilterConfig[];
  drill_down: DrillDownConfig;
}

interface DataBinding {
  kpi_id?: string;
  data_source: string;
  query: string;
  parameters: Record<string, any>;
  refresh_rate: number;
  cache_duration: number;
}

interface WidgetStyling {
  background_color: string;
  text_color: string;
  border_color: string;
  font_size: string;
  font_weight: string;
  alignment: 'left' | 'center' | 'right';
  padding: string;
  margin: string;
  border_radius: string;
  shadow: string;
}

interface WidgetInteraction {
  type: 'click' | 'hover' | 'double_click' | 'right_click';
  action: 'drill_down' | 'filter' | 'navigate' | 'export' | 'alert' | 'custom';
  target: string;
  parameters: Record<string, any>;
}

interface FilterConfig {
  field: string;
  operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains' | 'in' | 'between';
  value: any;
  label: string;
}

interface DrillDownConfig {
  enabled: boolean;
  target_scorecard?: string;
  target_dashboard?: string;
  target_report?: string;
  parameters: Record<string, any>;
}

interface ScorecardSettings {
  auto_alerts: boolean;
  alert_thresholds: AlertThreshold[];
  notification_channels: string[];
  export_formats: string[];
  data_retention: number;
  access_permissions: AccessPermission[];
  audit_trail: boolean;
  version_control: boolean;
}

interface AlertThreshold {
  kpi_id: string;
  condition: string;
  threshold_value: number;
  severity: 'info' | 'warning' | 'critical';
  action: 'notify' | 'escalate' | 'auto_fix' | 'log';
  recipients: string[];
  cooldown_period: number;
}

interface AccessPermission {
  user_id: string;
  role: 'viewer' | 'editor' | 'admin' | 'owner';
  permissions: string[];
  restrictions: string[];
}

interface BenchmarkComparison {
  internal_benchmark: number;
  industry_benchmark: number;
  competitor_benchmark: number;
  historical_benchmark: number;
  performance_vs_internal: number;
  performance_vs_industry: number;
  performance_vs_competitor: number;
  performance_vs_historical: number;
  ranking: {
    internal: number;
    industry: number;
    market: number;
  };
}

interface ScorecardAlert {
  id: string;
  type: 'threshold' | 'trend' | 'anomaly' | 'target' | 'benchmark';
  severity: 'info' | 'warning' | 'critical';
  message: string;
  kpi_id: string;
  triggered_at: Date;
  resolved_at?: Date;
  status: 'active' | 'acknowledged' | 'resolved' | 'dismissed';
  actions_taken: string[];
  escalation_level: number;
}

interface ScorecardComment {
  id: string;
  author: string;
  content: string;
  timestamp: Date;
  type: 'insight' | 'concern' | 'recommendation' | 'question' | 'update';
  kpi_id?: string;
  mentions: string[];
  attachments: string[];
  reactions: CommentReaction[];
}

interface CommentReaction {
  user_id: string;
  emoji: string;
  timestamp: Date;
}

interface SharingSettings {
  public_url?: string;
  password_protected: boolean;
  expiration_date?: Date;
  download_enabled: boolean;
  print_enabled: boolean;
  export_formats: string[];
  watermark: boolean;
  embedding_enabled: boolean;
  embedding_domains: string[];
}

interface Goal {
  id: string;
  name: string;
  description: string;
  type: 'individual' | 'team' | 'department' | 'company';
  category: 'revenue' | 'growth' | 'efficiency' | 'quality' | 'innovation' | 'customer' | 'employee';
  status: 'draft' | 'active' | 'completed' | 'cancelled' | 'overdue';
  priority: 'critical' | 'high' | 'medium' | 'low';
  owner: string;
  stakeholders: string[];
  start_date: Date;
  end_date: Date;
  target_value: number;
  current_value: number;
  unit: string;
  progress_percentage: number;
  kpi_metrics: string[];
  milestones: Milestone[];
  dependencies: GoalDependency[];
  success_criteria: SuccessCriteria[];
  risk_factors: RiskFactor[];
  action_items: ActionItem[];
  updates: GoalUpdate[];
  attachments: string[];
  tags: string[];
}

interface Milestone {
  id: string;
  name: string;
  description: string;
  due_date: Date;
  status: 'pending' | 'in_progress' | 'completed' | 'overdue';
  completion_percentage: number;
  dependencies: string[];
  deliverables: string[];
  success_criteria: string[];
  assigned_to: string[];
}

interface GoalDependency {
  goal_id: string;
  type: 'blocks' | 'enables' | 'influences' | 'supports';
  description: string;
  impact_level: 'high' | 'medium' | 'low';
}

interface SuccessCriteria {
  id: string;
  description: string;
  measurement: string;
  target_value: number;
  current_value: number;
  status: 'not_met' | 'partially_met' | 'met' | 'exceeded';
  weight: number;
}

interface RiskFactor {
  id: string;
  description: string;
  probability: number;
  impact: number;
  risk_score: number;
  mitigation_plan: string;
  status: 'identified' | 'monitoring' | 'mitigating' | 'resolved';
  owner: string;
}

interface ActionItem {
  id: string;
  description: string;
  assigned_to: string;
  due_date: Date;
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  priority: 'high' | 'medium' | 'low';
  estimated_effort: string;
  actual_effort?: string;
  dependencies: string[];
  deliverables: string[];
}

interface GoalUpdate {
  id: string;
  author: string;
  content: string;
  timestamp: Date;
  type: 'progress' | 'milestone' | 'risk' | 'blocker' | 'achievement';
  attachments: string[];
  metrics_snapshot: Record<string, number>;
}

interface PerformanceReview {
  id: string;
  period: {
    start: Date;
    end: Date;
    type: 'weekly' | 'monthly' | 'quarterly' | 'annually';
  };
  entity: {
    type: 'individual' | 'team' | 'department' | 'company';
    id: string;
    name: string;
  };
  scorecard_id: string;
  overall_score: number;
  category_scores: Record<string, number>;
  kpi_performance: KPIPerformance[];
  goal_achievement: GoalAchievement[];
  insights: PerformanceInsight[];
  recommendations: PerformanceRecommendation[];
  benchmarks: PerformanceBenchmark[];
  trends: PerformanceTrend[];
  risk_assessment: RiskAssessment;
  next_period_forecast: Forecast;
  action_plan: ActionPlan;
  stakeholder_feedback: StakeholderFeedback[];
  created_by: string;
  created_at: Date;
  approved_by?: string;
  approved_at?: Date;
  status: 'draft' | 'pending_approval' | 'approved' | 'published';
}

interface KPIPerformance {
  kpi_id: string;
  score: number;
  achievement_rate: number;
  trend: 'improving' | 'declining' | 'stable';
  benchmark_comparison: number;
  variance_from_target: number;
  variance_explanation: string;
  action_items: string[];
}

interface GoalAchievement {
  goal_id: string;
  achievement_rate: number;
  status: 'exceeded' | 'achieved' | 'partially_achieved' | 'not_achieved';
  variance_from_target: number;
  contributing_factors: string[];
  lessons_learned: string[];
}

interface PerformanceInsight {
  type: 'trend' | 'correlation' | 'anomaly' | 'pattern' | 'opportunity' | 'risk';
  title: string;
  description: string;
  confidence: number;
  impact: 'high' | 'medium' | 'low';
  supporting_data: any;
  recommendations: string[];
}

interface PerformanceRecommendation {
  type: 'improve' | 'maintain' | 'investigate' | 'escalate' | 'celebrate';
  priority: 'critical' | 'high' | 'medium' | 'low';
  description: string;
  expected_impact: string;
  effort_required: 'low' | 'medium' | 'high';
  timeline: string;
  responsible_party: string;
  success_metrics: string[];
}

interface PerformanceBenchmark {
  type: 'internal' | 'industry' | 'competitor' | 'best_practice';
  category: string;
  our_performance: number;
  benchmark_value: number;
  percentile_ranking: number;
  gap_analysis: string;
  improvement_potential: number;
}

interface PerformanceTrend {
  metric: string;
  direction: 'upward' | 'downward' | 'stable' | 'volatile';
  velocity: number;
  acceleration: number;
  seasonality: boolean;
  forecast: number[];
  confidence_interval: { lower: number; upper: number }[];
}

interface RiskAssessment {
  overall_risk_score: number;
  risk_categories: RiskCategory[];
  mitigation_strategies: MitigationStrategy[];
  contingency_plans: ContingencyPlan[];
}

interface RiskCategory {
  name: string;
  score: number;
  probability: number;
  impact: number;
  specific_risks: string[];
  indicators: string[];
}

interface MitigationStrategy {
  risk: string;
  strategy: string;
  implementation_timeline: string;
  responsible_party: string;
  success_metrics: string[];
  cost: number;
  effectiveness: number;
}

interface ContingencyPlan {
  scenario: string;
  trigger_conditions: string[];
  response_actions: string[];
  responsible_parties: string[];
  resource_requirements: string[];
  timeline: string;
}

interface Forecast {
  period: string;
  predicted_performance: Record<string, number>;
  confidence_levels: Record<string, number>;
  assumptions: string[];
  risk_factors: string[];
  scenario_analysis: ScenarioAnalysis[];
}

interface ScenarioAnalysis {
  name: string;
  probability: number;
  outcomes: Record<string, number>;
  implications: string[];
  recommended_actions: string[];
}

interface ActionPlan {
  objectives: ActionObjective[];
  initiatives: Initiative[];
  resource_allocation: ResourceAllocation[];
  timeline: PlanTimeline[];
  success_metrics: string[];
  review_schedule: ReviewSchedule[];
}

interface ActionObjective {
  id: string;
  description: string;
  priority: 'critical' | 'high' | 'medium' | 'low';
  target_completion: Date;
  success_criteria: string[];
  dependencies: string[];
  assigned_to: string[];
}

interface Initiative {
  id: string;
  name: string;
  description: string;
  type: 'improvement' | 'innovation' | 'maintenance' | 'expansion' | 'optimization';
  budget: number;
  timeline: string;
  expected_impact: string;
  kpis_affected: string[];
  stakeholders: string[];
}

interface ResourceAllocation {
  resource_type: 'human' | 'financial' | 'technical' | 'physical';
  description: string;
  amount: number;
  unit: string;
  allocation_period: string;
  cost: number;
  availability: number;
}

interface PlanTimeline {
  phase: string;
  start_date: Date;
  end_date: Date;
  deliverables: string[];
  milestones: string[];
  dependencies: string[];
}

interface ReviewSchedule {
  frequency: 'weekly' | 'monthly' | 'quarterly';
  participants: string[];
  agenda_items: string[];
  success_criteria: string[];
  escalation_criteria: string[];
}

interface StakeholderFeedback {
  stakeholder: string;
  role: string;
  feedback: string;
  sentiment: 'positive' | 'neutral' | 'negative';
  priority_areas: string[];
  suggestions: string[];
  timestamp: Date;
}

// Mock data generators
const generateKPIMetrics = (): KPIMetric[] => {
  const categories: Array<'revenue' | 'growth' | 'efficiency' | 'quality' | 'customer' | 'operational' | 'financial' | 'marketing' | 'sales' | 'support'> = 
    ['revenue', 'growth', 'efficiency', 'quality', 'customer', 'operational', 'financial', 'marketing', 'sales', 'support'];
  
  const kpiNames = [
    { name: 'Monthly Recurring Revenue', category: 'revenue', unit: 'USD', format: 'currency' as const },
    { name: 'Customer Acquisition Cost', category: 'marketing', unit: 'USD', format: 'currency' as const },
    { name: 'Customer Lifetime Value', category: 'customer', unit: 'USD', format: 'currency' as const },
    { name: 'Conversion Rate', category: 'sales', unit: '%', format: 'percentage' as const },
    { name: 'Churn Rate', category: 'customer', unit: '%', format: 'percentage' as const },
    { name: 'Average Order Value', category: 'sales', unit: 'USD', format: 'currency' as const },
    { name: 'Net Promoter Score', category: 'customer', unit: 'points', format: 'number' as const },
    { name: 'Revenue Growth Rate', category: 'growth', unit: '%', format: 'percentage' as const },
    { name: 'Gross Margin', category: 'financial', unit: '%', format: 'percentage' as const },
    { name: 'Customer Support Satisfaction', category: 'support', unit: '%', format: 'percentage' as const },
    { name: 'Website Traffic', category: 'marketing', unit: 'visitors', format: 'number' as const },
    { name: 'Lead Generation Rate', category: 'marketing', unit: 'leads/month', format: 'number' as const },
    { name: 'Sales Cycle Length', category: 'sales', unit: 'days', format: 'number' as const },
    { name: 'Inventory Turnover', category: 'operational', unit: 'times/year', format: 'decimal' as const },
    { name: 'Employee Satisfaction', category: 'operational', unit: '%', format: 'percentage' as const },
    { name: 'Return on Investment', category: 'financial', unit: '%', format: 'percentage' as const },
    { name: 'Market Share', category: 'growth', unit: '%', format: 'percentage' as const },
    { name: 'Cost per Lead', category: 'marketing', unit: 'USD', format: 'currency' as const },
    { name: 'Customer Retention Rate', category: 'customer', unit: '%', format: 'percentage' as const },
    { name: 'Product Quality Score', category: 'quality', unit: 'points', format: 'number' as const }
  ];

  return Array.from({ length: 20 }, (_, i) => {
    const kpi = kpiNames[i];
    const currentValue = Math.random() * 1000 + 100;
    const targetValue = currentValue * (0.9 + Math.random() * 0.2);
    const previousValue = currentValue * (0.85 + Math.random() * 0.3);
    
    const historicalData = Array.from({ length: 30 }, (_, j) => ({
      date: new Date(Date.now() - (29 - j) * 24 * 60 * 60 * 1000),
      value: Math.max(0, currentValue + (Math.random() - 0.5) * currentValue * 0.3),
      target: targetValue,
      forecast: j > 25 ? currentValue * (1 + Math.random() * 0.1) : undefined
    }));

    return {
      id: `kpi-${i + 1}`,
      name: kpi.name,
      description: `Track and monitor ${kpi.name.toLowerCase()} performance`,
      category: kpi.category,
      currentValue,
      targetValue,
      previousValue,
      unit: kpi.unit,
      format: kpi.format,
      trend: currentValue > previousValue ? 'up' : currentValue < previousValue ? 'down' : 'stable',
      trendPercentage: Math.abs((currentValue - previousValue) / previousValue * 100),
      status: currentValue >= targetValue * 0.95 ? 'on_track' : 
              currentValue >= targetValue * 0.8 ? 'at_risk' :
              currentValue >= targetValue * 0.6 ? 'off_track' :
              currentValue > targetValue ? 'exceeded' : 'critical',
      priority: ['critical', 'high', 'medium', 'low'][Math.floor(Math.random() * 4)] as any,
      frequency: ['daily', 'weekly', 'monthly'][Math.floor(Math.random() * 3)] as any,
      dataSource: ['CRM System', 'Analytics Platform', 'Financial System', 'Support System'][Math.floor(Math.random() * 4)],
      lastUpdated: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000),
      historicalData,
      thresholds: {
        excellent: { min: targetValue * 1.1, max: Infinity, color: '#10b981' },
        good: { min: targetValue * 0.95, max: targetValue * 1.1, color: '#3b82f6' },
        fair: { min: targetValue * 0.8, max: targetValue * 0.95, color: '#f59e0b' },
        poor: { min: targetValue * 0.6, max: targetValue * 0.8, color: '#ef4444' },
        critical: { min: 0, max: targetValue * 0.6, color: '#dc2626' }
      },
      owner: ['John Smith', 'Sarah Johnson', 'Mike Chen', 'Lisa Wilson'][Math.floor(Math.random() * 4)],
      department: ['Sales', 'Marketing', 'Operations', 'Finance', 'Customer Success'][Math.floor(Math.random() * 5)],
      tags: ['revenue', 'growth', 'efficiency', 'quality'].slice(0, Math.floor(Math.random() * 3) + 1),
      metadata: {
        formula: 'Sum of all recurring revenue / month',
        calculation_method: 'Automated calculation from CRM data',
        data_quality_score: 0.85 + Math.random() * 0.15,
        confidence_level: 0.90 + Math.random() * 0.1,
        sample_size: Math.floor(Math.random() * 10000) + 1000,
        benchmark_value: targetValue * (0.9 + Math.random() * 0.2),
        industry_average: targetValue * (0.8 + Math.random() * 0.4),
        seasonal_adjustment: Math.random() > 0.5,
        forecasted_value: currentValue * (1 + Math.random() * 0.2),
        forecast_confidence: 0.75 + Math.random() * 0.2
      }
    };
  });
};

const generateScorecards = (): Scorecard[] => {
  return Array.from({ length: 8 }, (_, i) => ({
    id: `scorecard-${i + 1}`,
    name: [
      'Executive Dashboard',
      'Sales Performance',
      'Marketing Analytics',
      'Customer Success Metrics',
      'Financial Overview',
      'Operational Excellence',
      'Product Analytics',
      'Support Quality'
    ][i],
    description: `Comprehensive performance tracking for ${['executive team', 'sales department', 'marketing team', 'customer success', 'finance', 'operations', 'product team', 'support team'][i]}`,
    type: ['executive', 'department', 'department', 'department', 'department', 'operational', 'department', 'department'][i] as any,
    perspective: ['mixed', 'financial', 'customer', 'customer', 'financial', 'internal', 'internal', 'customer'][i] as any,
    owner: ['CEO', 'VP Sales', 'CMO', 'VP Customer Success', 'CFO', 'COO', 'VP Product', 'VP Support'][i],
    department: ['Executive', 'Sales', 'Marketing', 'Customer Success', 'Finance', 'Operations', 'Product', 'Support'][i],
    visibility: ['executive', 'department', 'department', 'department', 'executive', 'public', 'department', 'department'][i] as any,
    status: 'active',
    kpis: [`kpi-${i * 2 + 1}`, `kpi-${i * 2 + 2}`, `kpi-${i * 2 + 3}`],
    layout: {
      grid_size: { rows: 4, cols: 3 },
      widgets: [],
      theme: 'light',
      color_scheme: 'blue',
      auto_refresh: true,
      refresh_interval: 300000
    },
    settings: {
      auto_alerts: true,
      alert_thresholds: [],
      notification_channels: ['email', 'slack'],
      export_formats: ['pdf', 'excel', 'csv'],
      data_retention: 365,
      access_permissions: [],
      audit_trail: true,
      version_control: true
    },
    created_at: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
    updated_at: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000),
    performance_score: 70 + Math.random() * 30,
    risk_score: Math.random() * 50,
    trend_score: -10 + Math.random() * 20,
    benchmark_comparison: {
      internal_benchmark: 75,
      industry_benchmark: 70,
      competitor_benchmark: 68,
      historical_benchmark: 72,
      performance_vs_internal: Math.random() * 20 - 10,
      performance_vs_industry: Math.random() * 20 - 10,
      performance_vs_competitor: Math.random() * 20 - 10,
      performance_vs_historical: Math.random() * 20 - 10,
      ranking: {
        internal: Math.floor(Math.random() * 10) + 1,
        industry: Math.floor(Math.random() * 100) + 1,
        market: Math.floor(Math.random() * 500) + 1
      }
    },
    alerts: [],
    comments: [],
    attachments: [],
    sharing: {
      password_protected: false,
      download_enabled: true,
      print_enabled: true,
      export_formats: ['pdf', 'excel'],
      watermark: true,
      embedding_enabled: false,
      embedding_domains: []
    }
  }));
};

const generateGoals = (): Goal[] => {
  const goalTypes: Array<'individual' | 'team' | 'department' | 'company'> = ['individual', 'team', 'department', 'company'];
  const categories: Array<'revenue' | 'growth' | 'efficiency' | 'quality' | 'innovation' | 'customer' | 'employee'> = 
    ['revenue', 'growth', 'efficiency', 'quality', 'innovation', 'customer', 'employee'];

  return Array.from({ length: 12 }, (_, i) => {
    const startDate = new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000);
    const endDate = new Date(startDate.getTime() + (30 + Math.random() * 330) * 24 * 60 * 60 * 1000);
    const targetValue = 1000 + Math.random() * 9000;
    const currentValue = targetValue * (0.3 + Math.random() * 0.7);

    return {
      id: `goal-${i + 1}`,
      name: [
        'Increase Monthly Revenue by 25%',
        'Reduce Customer Churn to <5%',
        'Launch New Product Line',
        'Improve Customer Satisfaction Score',
        'Expand Market Share by 15%',
        'Optimize Supply Chain Efficiency',
        'Enhance Employee Engagement',
        'Implement AI-Powered Analytics',
        'Achieve Carbon Neutrality',
        'Streamline Order Processing',
        'Develop Strategic Partnerships',
        'Build Mobile Application'
      ][i],
      description: `Strategic initiative to achieve significant business improvement in ${categories[i % categories.length]}`,
      type: goalTypes[i % goalTypes.length],
      category: categories[i % categories.length],
      status: Math.random() > 0.8 ? 'completed' : Math.random() > 0.6 ? 'active' : Math.random() > 0.4 ? 'overdue' : 'active',
      priority: ['critical', 'high', 'medium', 'low'][Math.floor(Math.random() * 4)] as any,
      owner: ['John Smith', 'Sarah Johnson', 'Mike Chen', 'Lisa Wilson', 'David Brown', 'Emma Davis'][Math.floor(Math.random() * 6)],
      stakeholders: ['CEO', 'CFO', 'CTO', 'VP Sales', 'VP Marketing'].slice(0, Math.floor(Math.random() * 3) + 2),
      start_date: startDate,
      end_date: endDate,
      target_value: targetValue,
      current_value: currentValue,
      unit: ['%', 'USD', 'points', 'units', 'customers'][Math.floor(Math.random() * 5)],
      progress_percentage: (currentValue / targetValue) * 100,
      kpi_metrics: [`kpi-${i + 1}`, `kpi-${i + 2}`],
      milestones: Array.from({ length: Math.floor(Math.random() * 5) + 2 }, (_, j) => ({
        id: `milestone-${i}-${j}`,
        name: `Milestone ${j + 1}`,
        description: `Key deliverable for goal achievement`,
        due_date: new Date(startDate.getTime() + (j + 1) * (endDate.getTime() - startDate.getTime()) / 5),
        status: j < 2 ? 'completed' : j === 2 ? 'in_progress' : 'pending',
        completion_percentage: j < 2 ? 100 : j === 2 ? 60 : 0,
        dependencies: [],
        deliverables: [`Deliverable ${j + 1}A`, `Deliverable ${j + 1}B`],
        success_criteria: [`Criteria ${j + 1}`],
        assigned_to: ['Team Lead', 'Project Manager']
      })),
      dependencies: [],
      success_criteria: Array.from({ length: 3 }, (_, j) => ({
        id: `criteria-${i}-${j}`,
        description: `Success criteria ${j + 1}`,
        measurement: 'Quantitative measurement method',
        target_value: 100,
        current_value: 30 + Math.random() * 70,
        status: Math.random() > 0.5 ? 'met' : Math.random() > 0.25 ? 'partially_met' : 'not_met',
        weight: 0.33
      })),
      risk_factors: [],
      action_items: [],
      updates: [],
      attachments: [],
      tags: ['strategic', 'high-priority', 'q4-goal'].slice(0, Math.floor(Math.random() * 3) + 1)
    };
  });
};

const STATUS_COLORS = {
  on_track: '#10b981',
  at_risk: '#f59e0b',
  off_track: '#ef4444',
  exceeded: '#3b82f6',
  critical: '#dc2626'
};

const TREND_COLORS = {
  up: '#10b981',
  down: '#ef4444',
  stable: '#6b7280'
};

const KPIScorecards: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedScorecard, setSelectedScorecard] = useState<string>('scorecard-1');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState<string>('all');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list' | 'cards'>('cards');

  const kpiMetrics = useMemo(() => generateKPIMetrics(), []);
  const scorecards = useMemo(() => generateScorecards(), []);
  const goals = useMemo(() => generateGoals(), []);

  const filteredKPIs = useMemo(() => {
    return kpiMetrics.filter(kpi => {
      const matchesSearch = kpi.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        kpi.description.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesCategory = filterCategory === 'all' || kpi.category === filterCategory;
      const matchesStatus = filterStatus === 'all' || kpi.status === filterStatus;
      return matchesSearch && matchesCategory && matchesStatus;
    });
  }, [kpiMetrics, searchTerm, filterCategory, filterStatus]);

  const formatValue = (value: number, format: string, unit: string) => {
    switch (format) {
      case 'currency':
        return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(value);
      case 'percentage':
        return `${value.toFixed(1)}%`;
      case 'decimal':
        return value.toFixed(2);
      default:
        return `${Math.round(value)} ${unit}`;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'on_track': return <CheckCircle className="w-4 h-4" />;
      case 'exceeded': return <Award className="w-4 h-4" />;
      case 'at_risk': return <AlertTriangle className="w-4 h-4" />;
      case 'off_track': return <AlertTriangle className="w-4 h-4" />;
      case 'critical': return <AlertTriangle className="w-4 h-4" />;
      default: return <Info className="w-4 h-4" />;
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <TrendingUp className="w-4 h-4" />;
      case 'down': return <TrendingDown className="w-4 h-4" />;
      case 'stable': return <Activity className="w-4 h-4" />;
      default: return <Activity className="w-4 h-4" />;
    }
  };

  return (
    <div className="p-6 max-w-7xl mx-auto space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">KPI Scorecards & Performance Tracking</h1>
          <p className="text-gray-600 mt-2">Real-time performance monitoring, goal tracking, and strategic insights</p>
        </div>
        <div className="flex items-center space-x-4">
          <Button variant="outline" className="flex items-center space-x-2">
            <Settings className="w-4 h-4" />
            <span>Configure KPIs</span>
          </Button>
          <Button className="flex items-center space-x-2">
            <Plus className="w-4 h-4" />
            <span>Create Scorecard</span>
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="scorecards">Scorecards</TabsTrigger>
          <TabsTrigger value="kpis">KPI Metrics</TabsTrigger>
          <TabsTrigger value="goals">Goals & Targets</TabsTrigger>
          <TabsTrigger value="performance">Performance Review</TabsTrigger>
          <TabsTrigger value="benchmarks">Benchmarks</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Active KPIs</p>
                    <p className="text-3xl font-bold text-gray-900">{kpiMetrics.length}</p>
                  </div>
                  <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Target className="h-6 w-6 text-blue-600" />
                  </div>
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  {kpiMetrics.filter(k => k.status === 'on_track').length} on track
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Performance Score</p>
                    <p className="text-3xl font-bold text-gray-900">87.2%</p>
                  </div>
                  <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <Gauge className="h-6 w-6 text-green-600" />
                  </div>
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  +3.2% from last month
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Goals Achieved</p>
                    <p className="text-3xl font-bold text-gray-900">
                      {goals.filter(g => g.status === 'completed').length}
                    </p>
                  </div>
                  <div className="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <Award className="h-6 w-6 text-purple-600" />
                  </div>
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  {goals.filter(g => g.status === 'active').length} in progress
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">At Risk KPIs</p>
                    <p className="text-3xl font-bold text-gray-900">
                      {kpiMetrics.filter(k => k.status === 'at_risk' || k.status === 'off_track').length}
                    </p>
                  </div>
                  <div className="h-12 w-12 bg-red-100 rounded-lg flex items-center justify-center">
                    <AlertTriangle className="h-6 w-6 text-red-600" />
                  </div>
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  Requires immediate attention
                </p>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <BarChart3 className="w-5 h-5" />
                  <span>KPI Performance Distribution</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={[
                          { name: 'On Track', value: kpiMetrics.filter(k => k.status === 'on_track').length, fill: STATUS_COLORS.on_track },
                          { name: 'Exceeded', value: kpiMetrics.filter(k => k.status === 'exceeded').length, fill: STATUS_COLORS.exceeded },
                          { name: 'At Risk', value: kpiMetrics.filter(k => k.status === 'at_risk').length, fill: STATUS_COLORS.at_risk },
                          { name: 'Off Track', value: kpiMetrics.filter(k => k.status === 'off_track').length, fill: STATUS_COLORS.off_track },
                          { name: 'Critical', value: kpiMetrics.filter(k => k.status === 'critical').length, fill: STATUS_COLORS.critical }
                        ]}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, value }) => `${name}: ${value}`}
                        outerRadius={80}
                        dataKey="value"
                      />
                      <Tooltip />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <LineChart className="w-5 h-5" />
                  <span>Performance Trends</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart data={kpiMetrics.slice(0, 5).map(kpi => kpi.historicalData.slice(-7)).flat()}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Area type="monotone" dataKey="value" stackId="1" stroke="#3b82f6" fill="#3b82f6" />
                      <Area type="monotone" dataKey="target" stackId="2" stroke="#10b981" fill="none" strokeDasharray="5 5" />
                    </AreaChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Crown className="w-5 h-5" />
                <span>Top Performing KPIs</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {kpiMetrics
                  .filter(kpi => kpi.status === 'exceeded' || kpi.status === 'on_track')
                  .slice(0, 6)
                  .map((kpi) => (
                    <motion.div
                      key={kpi.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="border border-gray-200 rounded-lg p-4"
                    >
                      <div className="flex items-center justify-between mb-2">
                        <Badge 
                          style={{ backgroundColor: STATUS_COLORS[kpi.status] }}
                          className="text-white"
                        >
                          {getStatusIcon(kpi.status)}
                          <span className="ml-1">{kpi.status.replace('_', ' ').toUpperCase()}</span>
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          {kpi.category}
                        </Badge>
                      </div>
                      <h4 className="font-medium text-gray-900 mb-1">{kpi.name}</h4>
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-600">Current:</span>
                        <span className="font-medium">{formatValue(kpi.currentValue, kpi.format, kpi.unit)}</span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-600">Target:</span>
                        <span className="font-medium">{formatValue(kpi.targetValue, kpi.format, kpi.unit)}</span>
                      </div>
                      <div className="flex items-center justify-between text-sm mt-2">
                        <div className="flex items-center space-x-1">
                          {getTrendIcon(kpi.trend)}
                          <span style={{ color: TREND_COLORS[kpi.trend] }}>
                            {kpi.trendPercentage.toFixed(1)}%
                          </span>
                        </div>
                        <span className="text-xs text-gray-500">vs last period</span>
                      </div>
                    </motion.div>
                  ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="scorecards" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <Card className="lg:col-span-1">
              <CardHeader>
                <CardTitle>Available Scorecards</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {scorecards.map((scorecard) => (
                    <motion.div
                      key={scorecard.id}
                      whileHover={{ scale: 1.02 }}
                      className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                        selectedScorecard === scorecard.id 
                          ? 'border-blue-500 bg-blue-50' 
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => setSelectedScorecard(scorecard.id)}
                    >
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium text-gray-900">{scorecard.name}</h4>
                        <Badge variant="outline" className="text-xs">
                          {scorecard.type}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600 mt-1">{scorecard.description}</p>
                      <div className="flex items-center justify-between mt-2">
                        <span className="text-xs text-gray-500">
                          Score: {scorecard.performance_score.toFixed(1)}%
                        </span>
                        <div className="flex items-center space-x-1">
                          <div className={`w-2 h-2 rounded-full ${
                            scorecard.performance_score >= 80 ? 'bg-green-500' :
                            scorecard.performance_score >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                          }`} />
                          <span className="text-xs text-gray-500">{scorecard.status}</span>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>{scorecards.find(s => s.id === selectedScorecard)?.name || 'Scorecard'}</span>
                  <div className="flex items-center space-x-2">
                    <Button variant="outline" size="sm">
                      <Edit className="w-4 h-4 mr-2" />
                      Edit
                    </Button>
                    <Button variant="outline" size="sm">
                      <Share className="w-4 h-4 mr-2" />
                      Share
                    </Button>
                    <Button variant="outline" size="sm">
                      <Download className="w-4 h-4 mr-2" />
                      Export
                    </Button>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {kpiMetrics.slice(0, 6).map((kpi) => (
                    <Card key={kpi.id} className="p-4">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-medium text-gray-900 text-sm">{kpi.name}</h4>
                        <Badge 
                          style={{ backgroundColor: STATUS_COLORS[kpi.status] }}
                          className="text-white text-xs"
                        >
                          {getStatusIcon(kpi.status)}
                        </Badge>
                      </div>
                      
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-2xl font-bold text-gray-900">
                            {formatValue(kpi.currentValue, kpi.format, kpi.unit)}
                          </span>
                          <div className="flex items-center space-x-1">
                            {getTrendIcon(kpi.trend)}
                            <span 
                              className="text-sm font-medium"
                              style={{ color: TREND_COLORS[kpi.trend] }}
                            >
                              {kpi.trend === 'up' ? '+' : kpi.trend === 'down' ? '-' : ''}
                              {kpi.trendPercentage.toFixed(1)}%
                            </span>
                          </div>
                        </div>
                        
                        <div className="flex items-center justify-between text-sm text-gray-600">
                          <span>Target: {formatValue(kpi.targetValue, kpi.format, kpi.unit)}</span>
                          <span>
                            {((kpi.currentValue / kpi.targetValue) * 100).toFixed(0)}% of target
                          </span>
                        </div>
                        
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div 
                            className="h-2 rounded-full transition-all duration-300"
                            style={{ 
                              width: `${Math.min(100, (kpi.currentValue / kpi.targetValue) * 100)}%`,
                              backgroundColor: STATUS_COLORS[kpi.status]
                            }}
                          />
                        </div>
                        
                        <div className="text-xs text-gray-500">
                          Updated {kpi.lastUpdated.toLocaleDateString()}
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="kpis" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>KPI Metrics</CardTitle>
              <div className="flex items-center space-x-4 mt-4">
                <div className="flex-1">
                  <Input
                    placeholder="Search KPIs..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="max-w-sm"
                  />
                </div>
                <select
                  value={filterCategory}
                  onChange={(e) => setFilterCategory(e.target.value)}
                  className="border border-gray-300 rounded-md px-3 py-2"
                >
                  <option value="all">All Categories</option>
                  <option value="revenue">Revenue</option>
                  <option value="growth">Growth</option>
                  <option value="efficiency">Efficiency</option>
                  <option value="quality">Quality</option>
                  <option value="customer">Customer</option>
                  <option value="operational">Operational</option>
                  <option value="financial">Financial</option>
                  <option value="marketing">Marketing</option>
                  <option value="sales">Sales</option>
                  <option value="support">Support</option>
                </select>
                <select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  className="border border-gray-300 rounded-md px-3 py-2"
                >
                  <option value="all">All Statuses</option>
                  <option value="on_track">On Track</option>
                  <option value="exceeded">Exceeded</option>
                  <option value="at_risk">At Risk</option>
                  <option value="off_track">Off Track</option>
                  <option value="critical">Critical</option>
                </select>
                <div className="flex items-center space-x-1">
                  <Button
                    variant={viewMode === 'cards' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setViewMode('cards')}
                  >
                    <Grid className="w-4 h-4" />
                  </Button>
                  <Button
                    variant={viewMode === 'list' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setViewMode('list')}
                  >
                    <List className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {viewMode === 'cards' ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {filteredKPIs.map((kpi) => (
                    <motion.div
                      key={kpi.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-shadow"
                    >
                      <div className="flex items-center justify-between mb-4">
                        <Badge 
                          style={{ backgroundColor: STATUS_COLORS[kpi.status] }}
                          className="text-white"
                        >
                          {getStatusIcon(kpi.status)}
                          <span className="ml-1">{kpi.status.replace('_', ' ').toUpperCase()}</span>
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          {kpi.category}
                        </Badge>
                      </div>

                      <h3 className="font-semibold text-gray-900 mb-2">{kpi.name}</h3>
                      <p className="text-sm text-gray-600 mb-4">{kpi.description}</p>

                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <span className="text-3xl font-bold text-gray-900">
                            {formatValue(kpi.currentValue, kpi.format, kpi.unit)}
                          </span>
                          <div className="flex items-center space-x-1">
                            {getTrendIcon(kpi.trend)}
                            <span 
                              className="text-sm font-medium"
                              style={{ color: TREND_COLORS[kpi.trend] }}
                            >
                              {kpi.trend === 'up' ? '+' : kpi.trend === 'down' ? '-' : ''}
                              {kpi.trendPercentage.toFixed(1)}%
                            </span>
                          </div>
                        </div>

                        <div className="flex items-center justify-between text-sm text-gray-600">
                          <span>Target: {formatValue(kpi.targetValue, kpi.format, kpi.unit)}</span>
                          <span>
                            {((kpi.currentValue / kpi.targetValue) * 100).toFixed(0)}% achieved
                          </span>
                        </div>

                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div 
                            className="h-2 rounded-full transition-all duration-300"
                            style={{ 
                              width: `${Math.min(100, (kpi.currentValue / kpi.targetValue) * 100)}%`,
                              backgroundColor: STATUS_COLORS[kpi.status]
                            }}
                          />
                        </div>

                        <div className="flex items-center justify-between text-xs text-gray-500">
                          <span>Owner: {kpi.owner}</span>
                          <span>Updated: {kpi.lastUpdated.toLocaleDateString()}</span>
                        </div>

                        <div className="flex items-center justify-between text-xs">
                          <Badge variant="secondary">{kpi.frequency}</Badge>
                          <Badge variant="outline">{kpi.priority} priority</Badge>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredKPIs.map((kpi) => (
                    <motion.div
                      key={kpi.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <div className="flex items-center space-x-2">
                            <Badge 
                              style={{ backgroundColor: STATUS_COLORS[kpi.status] }}
                              className="text-white"
                            >
                              {getStatusIcon(kpi.status)}
                            </Badge>
                            <Badge variant="outline" className="text-xs">
                              {kpi.category}
                            </Badge>
                          </div>
                          <div>
                            <h4 className="font-medium text-gray-900">{kpi.name}</h4>
                            <p className="text-sm text-gray-600">{kpi.description}</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-6 text-sm">
                          <div className="text-right">
                            <p className="font-semibold text-gray-900">
                              {formatValue(kpi.currentValue, kpi.format, kpi.unit)}
                            </p>
                            <p className="text-gray-600">
                              Target: {formatValue(kpi.targetValue, kpi.format, kpi.unit)}
                            </p>
                          </div>
                          <div className="flex items-center space-x-1">
                            {getTrendIcon(kpi.trend)}
                            <span 
                              className="font-medium"
                              style={{ color: TREND_COLORS[kpi.trend] }}
                            >
                              {kpi.trend === 'up' ? '+' : kpi.trend === 'down' ? '-' : ''}
                              {kpi.trendPercentage.toFixed(1)}%
                            </span>
                          </div>
                          <div className="text-right">
                            <p className="text-gray-900 font-medium">
                              {((kpi.currentValue / kpi.targetValue) * 100).toFixed(0)}%
                            </p>
                            <p className="text-xs text-gray-500">of target</p>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="goals" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Goals & Targets</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {goals.map((goal) => (
                  <motion.div
                    key={goal.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="border border-gray-200 rounded-lg p-6"
                  >
                    <div className="flex items-center justify-between mb-4">
                      <Badge 
                        variant={goal.status === 'completed' ? 'default' : 
                                goal.status === 'active' ? 'secondary' : 'destructive'}
                      >
                        {goal.status === 'completed' ? <CheckCircle className="w-3 h-3 mr-1" /> :
                         goal.status === 'active' ? <Clock className="w-3 h-3 mr-1" /> :
                         <AlertTriangle className="w-3 h-3 mr-1" />}
                        {goal.status.replace('_', ' ').toUpperCase()}
                      </Badge>
                      <Badge variant="outline" className="text-xs">
                        {goal.type}
                      </Badge>
                    </div>

                    <h3 className="font-semibold text-gray-900 mb-2">{goal.name}</h3>
                    <p className="text-sm text-gray-600 mb-4">{goal.description}</p>

                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-lg font-semibold text-gray-900">
                          {formatValue(goal.current_value, 'number', goal.unit)}
                        </span>
                        <span className="text-sm text-gray-600">
                          Target: {formatValue(goal.target_value, 'number', goal.unit)}
                        </span>
                      </div>

                      <div className="w-full bg-gray-200 rounded-full h-3">
                        <div 
                          className={`h-3 rounded-full transition-all duration-300 ${
                            goal.progress_percentage >= 100 ? 'bg-green-500' :
                            goal.progress_percentage >= 75 ? 'bg-blue-500' :
                            goal.progress_percentage >= 50 ? 'bg-yellow-500' : 'bg-red-500'
                          }`}
                          style={{ width: `${Math.min(100, goal.progress_percentage)}%` }}
                        />
                      </div>

                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-600">
                          {goal.progress_percentage.toFixed(1)}% complete
                        </span>
                        <span className="text-gray-600">
                          Due: {goal.end_date.toLocaleDateString()}
                        </span>
                      </div>

                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <span>Owner: {goal.owner}</span>
                        <Badge variant="secondary" className="text-xs">
                          {goal.priority} priority
                        </Badge>
                      </div>

                      <div className="flex items-center space-x-1">
                        {goal.tags.map((tag) => (
                          <Badge key={tag} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Performance Review</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Trophy className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Performance Analytics</h3>
                <p className="text-gray-600 mb-4">
                  Comprehensive performance reviews and trend analysis
                </p>
                <Button>
                  <Plus className="w-4 h-4 mr-2" />
                  Generate Review
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="benchmarks" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Benchmark Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Star className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Industry Benchmarks</h3>
                <p className="text-gray-600 mb-4">
                  Compare performance against industry standards and competitors
                </p>
                <Button>
                  <Eye className="w-4 h-4 mr-2" />
                  View Benchmarks
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default KPIScorecards;