/**
 * Autonomous Anomaly Resolution System
 * AI-driven detection, analysis, and automated resolution of system anomalies
 */

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  AlertTriangle,
  Shield,
  Brain,
  Zap,
  Activity,
  CheckCircle,
  XCircle,
  Clock,
  TrendingUp,
  TrendingDown,
  Eye,
  Settings,
  RefreshCw,
  Play,
  Pause,
  StopCircle,
  Search,
  Filter,
  Target,
  Cpu,
  Server,
  Database,
  Network,
  MemoryStick,
  HardDrive,
  Gauge,
  BarChart3,
  LineChart,
  PieChart,
  Users,
  Calendar,
  DollarSign,
  Star,
  Flag,
  Lightbulb,
  Edit,
  Trash,
  Plus,
  Minus,
  Download,
  Upload,
  Share,
  ExternalLink,
  MoreHorizontal,
  ChevronUp,
  ChevronDown,
  ArrowUp,
  ArrowDown,
  ArrowRight,
  GitBranch,
  Building2,
  Globe,
  MapPin,
  Layers,
  Grid3x3,
  Workflow,
  Link2,
  Lock,
  Key,
  FileText,
  Mail,
  Phone,
  MessageCircle,
  Bell,
  BellRing,
  Volume2,
  Mic,
  Speaker,
  Camera,
  Monitor,
  Smartphone,
  Laptop,
  Watch
} from 'lucide-react';
import { LineChart as RechartsLineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area, BarChart as RechartsBarChart, Bar } from 'recharts';

// TypeScript interfaces for anomaly resolution
interface SystemAnomaly {
  id: string;
  title: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  category: 'performance' | 'security' | 'availability' | 'data_quality' | 'cost' | 'user_experience';
  status: 'detected' | 'analyzing' | 'resolving' | 'resolved' | 'escalated';
  confidence: number;
  affectedSystems: string[];
  impact: {
    users: number;
    revenue: number;
    performance: number;
    availability: number;
  };
  aiAnalysis: {
    rootCause: string;
    recommendation: string;
    estimatedResolutionTime: string;
    automatedResolution: boolean;
    successProbability: number;
  };
  timeline: {
    detected: string;
    analyzed: string;
    resolutionStarted: string;
    resolved: string;
  };
  metrics: {
    before: Record<string, number>;
    current: Record<string, number>;
    target: Record<string, number>;
  };
  createdAt: string;
}

interface ResolutionAction {
  id: string;
  anomalyId: string;
  action: string;
  type: 'automatic' | 'manual' | 'hybrid';
  status: 'pending' | 'executing' | 'completed' | 'failed' | 'rolled_back';
  executor: 'ai_agent' | 'human' | 'system';
  priority: 'low' | 'medium' | 'high' | 'critical';
  estimatedDuration: string;
  actualDuration: string;
  dependencies: string[];
  rollbackPlan: string;
  successRate: number;
  riskLevel: 'low' | 'medium' | 'high';
  createdAt: string;
  executedAt: string;
}

interface AnomalyPattern {
  id: string;
  pattern: string;
  frequency: number;
  category: string;
  severity: string;
  typicalResolution: string;
  aiModel: string;
  accuracy: number;
  lastUpdated: string;
  isActive: boolean;
}

interface ResolutionPlaybook {
  id: string;
  name: string;
  category: string;
  triggers: string[];
  actions: string[];
  estimatedTime: string;
  successRate: number;
  automated: boolean;
  riskLevel: 'low' | 'medium' | 'high';
  lastUsed: string;
  timesUsed: number;
}

interface AnomalyMetric {
  timestamp: string;
  detectedAnomalies: number;
  resolvedAnomalies: number;
  avgResolutionTime: number;
  automationRate: number;
  successRate: number;
  falsePositives: number;
  criticalAnomalies: number;
}

// Mock data generation functions
const generateAnomalies = (): SystemAnomaly[] => {
  const severities = ['low', 'medium', 'high', 'critical'] as const;
  const categories = ['performance', 'security', 'availability', 'data_quality', 'cost', 'user_experience'] as const;
  const statuses = ['detected', 'analyzing', 'resolving', 'resolved', 'escalated'] as const;
  const systems = ['web-server', 'database', 'cache', 'api-gateway', 'cdn', 'auth-service', 'payment-service', 'analytics'];

  return Array.from({ length: 16 }, (_, i) => ({
    id: `anomaly-${i + 1}`,
    title: [
      'Unusual CPU spike in web servers',
      'Database query performance degradation',
      'Elevated error rates in payment API',
      'Memory leak detected in analytics service',
      'Abnormal network traffic patterns',
      'Authentication service latency increase',
      'CDN cache hit rate dropping',
      'Storage capacity approaching limits',
      'User session timeout anomalies',
      'Cost optimization opportunities detected',
      'Security breach indicators found',
      'Data quality issues in ETL pipeline',
      'Service mesh connectivity problems',
      'Container orchestration inefficiencies',
      'Load balancer health check failures',
      'Monitoring system false positives'
    ][i],
    description: [
      'CPU utilization increased by 300% in the last hour across multiple web server instances',
      'Query execution time increased by 150% for common operations',
      'Payment processing API showing 5x normal error rate',
      'Memory usage growing linearly, indicating potential leak',
      'Traffic patterns don\'t match historical data or expected load',
      'Authentication response times increased from 50ms to 250ms',
      'CDN cache effectiveness dropped from 95% to 60%',
      'Storage utilization at 85% and growing at 2%/hour',
      'User sessions timing out 3x faster than normal',
      'Infrastructure costs increased 40% without proportional usage',
      'Multiple failed login attempts from suspicious IP ranges',
      'Data validation errors increased 10x in ETL processes',
      'Service-to-service communication latency spikes detected',
      'Pod restart rates 5x higher than baseline',
      'Load balancer failing to route traffic to healthy instances',
      'Alert fatigue from 300% increase in false positive alerts'
    ][i],
    severity: severities[i % severities.length],
    category: categories[i % categories.length],
    status: statuses[i % statuses.length],
    confidence: Math.random() * 30 + 70,
    affectedSystems: systems.slice(0, Math.floor(Math.random() * 4) + 1),
    impact: {
      users: Math.floor(Math.random() * 10000),
      revenue: Math.floor(Math.random() * 50000),
      performance: Math.floor(Math.random() * 50 + 50),
      availability: Math.floor(Math.random() * 20 + 80)
    },
    aiAnalysis: {
      rootCause: [
        'Traffic surge from viral social media campaign',
        'Inefficient database indexes causing full table scans',
        'Rate limiting misconfiguration in API gateway',
        'Memory not being properly garbage collected',
        'DDoS attack or bot traffic detected',
        'Database connection pool exhaustion',
        'CDN origin server performance degradation',
        'Log retention policy not being enforced',
        'Session management configuration error',
        'Auto-scaling policies not properly tuned',
        'Coordinated brute force attack detected',
        'Data source schema changes breaking validation',
        'Network partition causing service isolation',
        'Resource limits too restrictive for current load',
        'Health check endpoints returning false negatives',
        'Threshold values set too low for normal variance'
      ][i],
      recommendation: [
        'Scale web servers horizontally and optimize caching',
        'Rebuild database indexes and optimize queries',
        'Update rate limiting rules and monitor API usage',
        'Restart affected services and investigate memory usage',
        'Implement DDoS protection and traffic filtering',
        'Increase connection pool size and optimize queries',
        'Investigate origin server performance and scale',
        'Implement automated log cleanup and monitoring',
        'Review session timeout configuration and scaling',
        'Adjust auto-scaling thresholds and policies',
        'Block suspicious IPs and enhance security monitoring',
        'Update data validation rules and add error handling',
        'Investigate network connectivity and add redundancy',
        'Increase resource limits and optimize allocation',
        'Update health check logic and add monitoring',
        'Adjust alert thresholds and add statistical filtering'
      ][i],
      estimatedResolutionTime: ['5 minutes', '15 minutes', '30 minutes', '1 hour', '2 hours'][i % 5],
      automatedResolution: Math.random() > 0.4,
      successProbability: Math.random() * 30 + 70
    },
    timeline: {
      detected: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000).toISOString(),
      analyzed: new Date(Date.now() - Math.random() * 20 * 60 * 60 * 1000).toISOString(),
      resolutionStarted: new Date(Date.now() - Math.random() * 15 * 60 * 60 * 1000).toISOString(),
      resolved: Math.random() > 0.5 ? new Date(Date.now() - Math.random() * 10 * 60 * 60 * 1000).toISOString() : ''
    },
    metrics: {
      before: {
        cpu: Math.random() * 50 + 20,
        memory: Math.random() * 40 + 30,
        latency: Math.random() * 100 + 50,
        errorRate: Math.random() * 2 + 0.5,
        throughput: Math.random() * 500 + 200
      },
      current: {
        cpu: Math.random() * 80 + 40,
        memory: Math.random() * 70 + 50,
        latency: Math.random() * 200 + 100,
        errorRate: Math.random() * 10 + 2,
        throughput: Math.random() * 300 + 100
      },
      target: {
        cpu: Math.random() * 30 + 30,
        memory: Math.random() * 30 + 40,
        latency: Math.random() * 50 + 25,
        errorRate: Math.random() * 1 + 0.1,
        throughput: Math.random() * 600 + 400
      }
    },
    createdAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString()
  }));
};

const generateResolutionActions = (): ResolutionAction[] => {
  const types = ['automatic', 'manual', 'hybrid'] as const;
  const statuses = ['pending', 'executing', 'completed', 'failed', 'rolled_back'] as const;
  const executors = ['ai_agent', 'human', 'system'] as const;
  const priorities = ['low', 'medium', 'high', 'critical'] as const;
  const risks = ['low', 'medium', 'high'] as const;

  return Array.from({ length: 20 }, (_, i) => ({
    id: `action-${i + 1}`,
    anomalyId: `anomaly-${(i % 16) + 1}`,
    action: [
      'Scale web server instances',
      'Rebuild database indexes',
      'Update API rate limits',
      'Restart analytics service',
      'Enable DDoS protection',
      'Increase DB connection pool',
      'Scale CDN origin servers',
      'Clean up old log files',
      'Restart session service',
      'Adjust auto-scaling thresholds',
      'Block suspicious IP addresses',
      'Update data validation rules',
      'Restart network components',
      'Increase resource limits',
      'Update health check logic',
      'Adjust alert thresholds',
      'Implement caching layer',
      'Optimize database queries',
      'Add monitoring alerts',
      'Deploy security patches'
    ][i],
    type: types[i % types.length],
    status: statuses[i % statuses.length],
    executor: executors[i % executors.length],
    priority: priorities[i % priorities.length],
    estimatedDuration: ['2 minutes', '5 minutes', '15 minutes', '30 minutes', '1 hour'][i % 5],
    actualDuration: ['1 minute', '3 minutes', '12 minutes', '25 minutes', '45 minutes'][i % 5],
    dependencies: i % 3 === 0 ? [`action-${i}`] : [],
    rollbackPlan: [
      'Scale down instances to original count',
      'Restore original database indexes',
      'Revert to previous rate limit settings',
      'No rollback needed for service restart',
      'Disable DDoS protection if issues arise',
      'Reduce connection pool to previous size',
      'Scale down CDN servers if performance degrades',
      'Restore logs from backup if needed',
      'Revert session configuration',
      'Restore previous auto-scaling settings',
      'Remove IP blocks if false positives',
      'Revert to previous validation rules',
      'Restart network with previous config',
      'Reduce resource limits if instability',
      'Revert health check configuration',
      'Restore original alert thresholds',
      'Remove caching layer if issues',
      'Revert database query changes',
      'Remove monitoring alerts if noisy',
      'Rollback security patches if problems'
    ][i],
    successRate: Math.random() * 30 + 70,
    riskLevel: risks[i % risks.length],
    createdAt: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000).toISOString(),
    executedAt: new Date(Date.now() - Math.random() * 20 * 60 * 60 * 1000).toISOString()
  }));
};

const generateAnomalyPatterns = (): AnomalyPattern[] => {
  return Array.from({ length: 12 }, (_, i) => ({
    id: `pattern-${i + 1}`,
    pattern: [
      'CPU spike followed by memory increase',
      'Database lock timeouts during peak hours',
      'API error rate correlation with user load',
      'Memory leak pattern in long-running processes',
      'Network latency spikes during deployments',
      'Authentication failures during traffic surges',
      'Cache miss rate increases before outages',
      'Storage I/O bottlenecks during backups',
      'Session timeout clusters during maintenance',
      'Cost anomalies after auto-scaling events',
      'Security events correlated with geographic regions',
      'Data quality issues after schema changes'
    ][i],
    frequency: Math.floor(Math.random() * 50) + 10,
    category: ['performance', 'security', 'availability', 'data_quality', 'cost', 'user_experience'][i % 6],
    severity: ['low', 'medium', 'high', 'critical'][i % 4],
    typicalResolution: [
      'Horizontal scaling + caching optimization',
      'Query optimization + connection pooling',
      'Rate limiting + load balancing adjustment',
      'Service restart + memory profiling',
      'Gradual deployment + network monitoring',
      'Connection scaling + rate limiting',
      'Cache warming + origin optimization',
      'I/O scheduling + backup optimization',
      'Session scaling + timeout adjustment',
      'Policy tuning + cost monitoring',
      'IP blocking + security enhancement',
      'Validation update + error handling'
    ][i],
    aiModel: ['RandomForest', 'XGBoost', 'LSTM', 'Transformer'][i % 4],
    accuracy: Math.random() * 20 + 80,
    lastUpdated: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
    isActive: Math.random() > 0.2
  }));
};

const generateResolutionPlaybooks = (): ResolutionPlaybook[] => {
  return Array.from({ length: 10 }, (_, i) => ({
    id: `playbook-${i + 1}`,
    name: [
      'High Traffic Load Response',
      'Database Performance Recovery',
      'API Service Restoration',
      'Memory Leak Mitigation',
      'Security Incident Response',
      'Network Connectivity Recovery',
      'Storage Capacity Management',
      'Session Management Recovery',
      'Cost Optimization Response',
      'Data Quality Incident Response'
    ][i],
    category: ['performance', 'availability', 'security', 'cost', 'data_quality'][i % 5],
    triggers: [
      ['CPU > 80%', 'Response time > 2s', 'Error rate > 5%'],
      ['Query time > 1s', 'Connection pool > 90%', 'Lock timeouts > 10'],
      ['API errors > 5%', 'Response time > 500ms', 'Rate limit hit'],
      ['Memory growth > 10%/hour', 'GC frequency > 2x', 'OOM warnings'],
      ['Failed logins > 100/min', 'Suspicious IPs detected', 'Privilege escalation'],
      ['Packet loss > 1%', 'Latency > 200ms', 'Connection failures'],
      ['Storage > 85%', 'I/O wait > 20%', 'Disk errors detected'],
      ['Session timeouts > 3x normal', 'Auth failures > 5%', 'Load > 80%'],
      ['Cost increase > 20%', 'Efficiency < 60%', 'Unused resources > 30%'],
      ['Validation errors > 5%', 'Data loss detected', 'Schema conflicts']
    ][i],
    actions: [
      ['Scale web servers', 'Enable caching', 'Update load balancer'],
      ['Optimize queries', 'Increase connections', 'Rebuild indexes'],
      ['Restart API service', 'Update rate limits', 'Check dependencies'],
      ['Restart service', 'Profile memory', 'Update configurations'],
      ['Block IPs', 'Reset credentials', 'Enable monitoring'],
      ['Restart network', 'Check routing', 'Update configurations'],
      ['Clean old files', 'Add storage', 'Optimize I/O'],
      ['Restart session service', 'Update timeouts', 'Scale horizontally'],
      ['Right-size resources', 'Enable spot instances', 'Optimize scheduling'],
      ['Fix validation rules', 'Restore from backup', 'Update schema']
    ][i],
    estimatedTime: ['15 minutes', '30 minutes', '45 minutes', '1 hour', '2 hours'][i % 5],
    successRate: Math.random() * 20 + 80,
    automated: Math.random() > 0.3,
    riskLevel: ['low', 'medium', 'high'][i % 3] as 'low' | 'medium' | 'high',
    lastUsed: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
    timesUsed: Math.floor(Math.random() * 50) + 5
  }));
};

const generateAnomalyMetrics = (): AnomalyMetric[] => {
  return Array.from({ length: 24 }, (_, i) => ({
    timestamp: new Date(Date.now() - (23 - i) * 60 * 60 * 1000).toISOString().split('T')[1].split(':')[0] + ':00',
    detectedAnomalies: Math.floor(Math.random() * 20) + 5,
    resolvedAnomalies: Math.floor(Math.random() * 15) + 3,
    avgResolutionTime: Math.random() * 30 + 10,
    automationRate: Math.random() * 30 + 60,
    successRate: Math.random() * 20 + 80,
    falsePositives: Math.floor(Math.random() * 5) + 1,
    criticalAnomalies: Math.floor(Math.random() * 3) + 1
  }));
};

const AutonomousAnomalyResolution: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [severityFilter, setSeverityFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [activeTab, setActiveTab] = useState('anomalies');

  // Generate mock data
  const anomalies = useMemo(() => generateAnomalies(), []);
  const resolutionActions = useMemo(() => generateResolutionActions(), []);
  const anomalyPatterns = useMemo(() => generateAnomalyPatterns(), []);
  const resolutionPlaybooks = useMemo(() => generateResolutionPlaybooks(), []);
  const anomalyMetrics = useMemo(() => generateAnomalyMetrics(), []);

  // Filter and search logic
  const filteredAnomalies = useMemo(() => {
    return anomalies.filter(anomaly => {
      const matchesSearch = anomaly.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          anomaly.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          anomaly.category.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesSeverity = severityFilter === 'all' || anomaly.severity === severityFilter;
      const matchesStatus = statusFilter === 'all' || anomaly.status === statusFilter;
      const matchesCategory = categoryFilter === 'all' || anomaly.category === categoryFilter;
      return matchesSearch && matchesSeverity && matchesStatus && matchesCategory;
    });
  }, [anomalies, searchTerm, severityFilter, statusFilter, categoryFilter]);

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'bg-red-500';
      case 'high': return 'bg-orange-500';
      case 'medium': return 'bg-yellow-500';
      case 'low': return 'bg-blue-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'resolved': return 'text-green-600';
      case 'resolving': return 'text-blue-600';
      case 'analyzing': return 'text-yellow-600';
      case 'detected': return 'text-orange-600';
      case 'escalated': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'performance': return <Gauge className="h-4 w-4" />;
      case 'security': return <Shield className="h-4 w-4" />;
      case 'availability': return <Activity className="h-4 w-4" />;
      case 'data_quality': return <Database className="h-4 w-4" />;
      case 'cost': return <DollarSign className="h-4 w-4" />;
      case 'user_experience': return <Users className="h-4 w-4" />;
      default: return <AlertTriangle className="h-4 w-4" />;
    }
  };

  return (
    <div className="p-6 space-y-6 bg-gray-50 min-h-screen">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Autonomous Anomaly Resolution</h1>
          <p className="text-gray-600 mt-2">AI-driven detection, analysis, and automated resolution of system anomalies</p>
        </div>
        <div className="flex gap-3">
          <Button variant="outline" className="flex items-center gap-2">
            <RefreshCw className="h-4 w-4" />
            Refresh
          </Button>
          <Button className="flex items-center gap-2">
            <Brain className="h-4 w-4" />
            AI Analysis
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="anomalies">Anomalies</TabsTrigger>
          <TabsTrigger value="actions">Resolution Actions</TabsTrigger>
          <TabsTrigger value="patterns">Patterns</TabsTrigger>
          <TabsTrigger value="playbooks">Playbooks</TabsTrigger>
          <TabsTrigger value="metrics">Metrics</TabsTrigger>
          <TabsTrigger value="overview">Overview</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Active Anomalies</p>
                    <p className="text-3xl font-bold text-red-600">
                      {anomalies.filter(a => a.status !== 'resolved').length}
                    </p>
                  </div>
                  <AlertTriangle className="h-8 w-8 text-red-500" />
                </div>
                <div className="mt-4">
                  <Badge variant="secondary">
                    {anomalies.filter(a => a.severity === 'critical' && a.status !== 'resolved').length} Critical
                  </Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Resolution Rate</p>
                    <p className="text-3xl font-bold text-green-600">
                      {Math.floor((anomalies.filter(a => a.status === 'resolved').length / anomalies.length) * 100)}%
                    </p>
                  </div>
                  <CheckCircle className="h-8 w-8 text-green-500" />
                </div>
                <div className="mt-4">
                  <Badge variant="secondary">
                    {anomalies.filter(a => a.status === 'resolved').length} Resolved
                  </Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Automation Rate</p>
                    <p className="text-3xl font-bold text-blue-600">
                      {Math.floor(resolutionActions.filter(a => a.type === 'automatic').length / resolutionActions.length * 100)}%
                    </p>
                  </div>
                  <Brain className="h-8 w-8 text-blue-500" />
                </div>
                <div className="mt-4">
                  <Badge variant="secondary">
                    {resolutionActions.filter(a => a.type === 'automatic').length} Auto Actions
                  </Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Avg Resolution Time</p>
                    <p className="text-3xl font-bold text-purple-600">
                      {Math.floor(anomalyMetrics.reduce((sum, m) => sum + m.avgResolutionTime, 0) / anomalyMetrics.length)}m
                    </p>
                  </div>
                  <Clock className="h-8 w-8 text-purple-500" />
                </div>
                <div className="mt-4">
                  <Badge variant="secondary" className="flex items-center gap-1">
                    <TrendingDown className="h-3 w-3" />
                    Improving
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Anomaly Detection & Resolution
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={anomalyMetrics}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="timestamp" />
                    <YAxis />
                    <Tooltip />
                    <Area type="monotone" dataKey="detectedAnomalies" stackId="1" stroke="#ff7300" fill="#ff7300" />
                    <Area type="monotone" dataKey="resolvedAnomalies" stackId="1" stroke="#00ff00" fill="#00ff00" />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <LineChart className="h-5 w-5" />
                  Resolution Performance
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <RechartsLineChart data={anomalyMetrics}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="timestamp" />
                    <YAxis />
                    <Tooltip />
                    <Line type="monotone" dataKey="avgResolutionTime" stroke="#8884d8" strokeWidth={2} />
                    <Line type="monotone" dataKey="automationRate" stroke="#82ca9d" strokeWidth={2} />
                    <Line type="monotone" dataKey="successRate" stroke="#ffc658" strokeWidth={2} />
                  </RechartsLineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="anomalies" className="space-y-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search anomalies..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex gap-2">
              <select
                value={severityFilter}
                onChange={(e) => setSeverityFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md"
              >
                <option value="all">All Severity</option>
                <option value="critical">Critical</option>
                <option value="high">High</option>
                <option value="medium">Medium</option>
                <option value="low">Low</option>
              </select>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md"
              >
                <option value="all">All Status</option>
                <option value="detected">Detected</option>
                <option value="analyzing">Analyzing</option>
                <option value="resolving">Resolving</option>
                <option value="resolved">Resolved</option>
                <option value="escalated">Escalated</option>
              </select>
              <select
                value={categoryFilter}
                onChange={(e) => setCategoryFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md"
              >
                <option value="all">All Categories</option>
                <option value="performance">Performance</option>
                <option value="security">Security</option>
                <option value="availability">Availability</option>
                <option value="data_quality">Data Quality</option>
                <option value="cost">Cost</option>
                <option value="user_experience">User Experience</option>
              </select>
            </div>
          </div>

          <div className="grid gap-4">
            <AnimatePresence>
              {filteredAnomalies.map((anomaly) => (
                <motion.div
                  key={anomaly.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.2 }}
                >
                  <Card className="hover:shadow-lg transition-shadow">
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <div className={`w-3 h-3 rounded-full ${getSeverityColor(anomaly.severity)}`} />
                            {getCategoryIcon(anomaly.category)}
                            <h3 className="text-lg font-semibold">{anomaly.title}</h3>
                            <Badge variant="outline" className={getStatusColor(anomaly.status)}>
                              {anomaly.status}
                            </Badge>
                            <Badge variant="outline">
                              {Math.floor(anomaly.confidence)}% confidence
                            </Badge>
                            {anomaly.aiAnalysis.automatedResolution && (
                              <Badge variant="outline" className="flex items-center gap-1">
                                <Brain className="h-3 w-3" />
                                Auto-Resolve
                              </Badge>
                            )}
                          </div>
                          <p className="text-gray-600 mb-3">{anomaly.description}</p>
                          
                          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4 text-sm">
                            <div>
                              <p className="text-gray-600">Affected Users</p>
                              <p className="font-semibold">{anomaly.impact.users.toLocaleString()}</p>
                            </div>
                            <div>
                              <p className="text-gray-600">Revenue Impact</p>
                              <p className="font-semibold text-red-600">${anomaly.impact.revenue.toLocaleString()}</p>
                            </div>
                            <div>
                              <p className="text-gray-600">Performance</p>
                              <p className="font-semibold">{anomaly.impact.performance}%</p>
                            </div>
                            <div>
                              <p className="text-gray-600">Availability</p>
                              <p className="font-semibold">{anomaly.impact.availability}%</p>
                            </div>
                          </div>

                          <div className="bg-gray-50 p-4 rounded-lg mb-4">
                            <div className="flex items-start gap-2">
                              <Brain className="h-5 w-5 text-purple-500 mt-0.5" />
                              <div className="flex-1">
                                <p className="font-semibold text-purple-700 mb-1">AI Analysis</p>
                                <p className="text-sm text-gray-700 mb-2">
                                  <strong>Root Cause:</strong> {anomaly.aiAnalysis.rootCause}
                                </p>
                                <p className="text-sm text-gray-700">
                                  <strong>Recommendation:</strong> {anomaly.aiAnalysis.recommendation}
                                </p>
                                <div className="flex items-center gap-4 mt-2 text-xs text-gray-600">
                                  <span>ETA: {anomaly.aiAnalysis.estimatedResolutionTime}</span>
                                  <span>Success: {Math.floor(anomaly.aiAnalysis.successProbability)}%</span>
                                </div>
                              </div>
                            </div>
                          </div>

                          <div className="flex flex-wrap gap-2 mb-4">
                            {anomaly.affectedSystems.map((system) => (
                              <Badge key={system} variant="secondary">
                                {system}
                              </Badge>
                            ))}
                          </div>
                        </div>

                        <div className="flex flex-col gap-2">
                          {anomaly.status === 'detected' && (
                            <Button size="sm" className="flex items-center gap-1">
                              <Play className="h-4 w-4" />
                              Resolve
                            </Button>
                          )}
                          {anomaly.status === 'resolving' && (
                            <Button size="sm" variant="outline" className="flex items-center gap-1">
                              <Pause className="h-4 w-4" />
                              Pause
                            </Button>
                          )}
                          <Button size="sm" variant="outline" className="flex items-center gap-1">
                            <Eye className="h-4 w-4" />
                            Details
                          </Button>
                          <Button size="sm" variant="outline">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        </TabsContent>

        <TabsContent value="actions" className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold">Resolution Actions</h2>
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              New Action
            </Button>
          </div>

          <div className="grid gap-4">
            {resolutionActions.map((action) => (
              <Card key={action.id}>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="text-lg font-semibold">{action.action}</h3>
                        <Badge variant={
                          action.status === 'completed' ? 'default' :
                          action.status === 'executing' ? 'secondary' :
                          action.status === 'failed' ? 'destructive' :
                          'outline'
                        }>
                          {action.status}
                        </Badge>
                        <Badge variant="outline" className={
                          action.type === 'automatic' ? 'border-blue-500 text-blue-700' :
                          action.type === 'manual' ? 'border-orange-500 text-orange-700' :
                          'border-purple-500 text-purple-700'
                        }>
                          {action.type}
                        </Badge>
                        <Badge variant="outline" className={
                          action.priority === 'critical' ? 'border-red-500 text-red-700' :
                          action.priority === 'high' ? 'border-orange-500 text-orange-700' :
                          action.priority === 'medium' ? 'border-yellow-500 text-yellow-700' :
                          'border-gray-500 text-gray-700'
                        }>
                          {action.priority}
                        </Badge>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-5 gap-4 text-sm">
                        <div>
                          <p className="text-gray-600">Executor</p>
                          <p className="font-medium">{action.executor.replace('_', ' ')}</p>
                        </div>
                        <div>
                          <p className="text-gray-600">Duration</p>
                          <p className="font-medium">{action.actualDuration || action.estimatedDuration}</p>
                        </div>
                        <div>
                          <p className="text-gray-600">Success Rate</p>
                          <p className="font-medium">{Math.floor(action.successRate)}%</p>
                        </div>
                        <div>
                          <p className="text-gray-600">Risk Level</p>
                          <p className={`font-medium ${
                            action.riskLevel === 'high' ? 'text-red-600' :
                            action.riskLevel === 'medium' ? 'text-yellow-600' :
                            'text-green-600'
                          }`}>
                            {action.riskLevel}
                          </p>
                        </div>
                        <div>
                          <p className="text-gray-600">Anomaly</p>
                          <p className="font-medium">{action.anomalyId}</p>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {action.status === 'pending' && (
                        <Button size="sm" className="flex items-center gap-1">
                          <Play className="h-4 w-4" />
                          Execute
                        </Button>
                      )}
                      {action.status === 'executing' && (
                        <Button size="sm" variant="destructive" className="flex items-center gap-1">
                          <StopCircle className="h-4 w-4" />
                          Stop
                        </Button>
                      )}
                      <Button variant="outline" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="patterns" className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold">Anomaly Patterns</h2>
            <Button className="flex items-center gap-2">
              <Brain className="h-4 w-4" />
              Learn New Pattern
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {anomalyPatterns.map((pattern) => (
              <Card key={pattern.id} className="hover:shadow-lg transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">{pattern.pattern}</CardTitle>
                    <Badge variant={pattern.isActive ? "default" : "secondary"}>
                      {pattern.isActive ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-gray-600">Frequency</p>
                      <p className="font-semibold">{pattern.frequency}x</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Accuracy</p>
                      <p className="font-semibold">{Math.floor(pattern.accuracy)}%</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Category</p>
                      <p className="font-semibold capitalize">{pattern.category}</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Severity</p>
                      <p className="font-semibold capitalize">{pattern.severity}</p>
                    </div>
                  </div>
                  
                  <div className="pt-2 border-t">
                    <p className="text-sm text-gray-600 mb-1">Typical Resolution:</p>
                    <p className="text-sm font-medium">{pattern.typicalResolution}</p>
                  </div>

                  <div className="pt-2 border-t">
                    <div className="flex items-center gap-2 text-sm">
                      <Badge variant="outline">{pattern.aiModel}</Badge>
                      <span className="text-gray-600">
                        Updated {new Date(pattern.lastUpdated).toLocaleDateString()}
                      </span>
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <Button variant="outline" size="sm" className="flex-1">
                      <Edit className="h-4 w-4 mr-1" />
                      Edit
                    </Button>
                    <Button variant="outline" size="sm" className="flex-1">
                      <Eye className="h-4 w-4 mr-1" />
                      View
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="playbooks" className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold">Resolution Playbooks</h2>
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Create Playbook
            </Button>
          </div>

          <div className="grid gap-4">
            {resolutionPlaybooks.map((playbook) => (
              <Card key={playbook.id}>
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="text-lg font-semibold">{playbook.name}</h3>
                        <Badge variant="outline" className="capitalize">
                          {playbook.category}
                        </Badge>
                        {playbook.automated && (
                          <Badge variant="outline" className="flex items-center gap-1">
                            <Brain className="h-3 w-3" />
                            Automated
                          </Badge>
                        )}
                        <Badge variant="outline" className={
                          playbook.riskLevel === 'high' ? 'border-red-500 text-red-700' :
                          playbook.riskLevel === 'medium' ? 'border-yellow-500 text-yellow-700' :
                          'border-green-500 text-green-700'
                        }>
                          {playbook.riskLevel} risk
                        </Badge>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4 text-sm">
                        <div>
                          <p className="text-gray-600">Success Rate</p>
                          <p className="font-semibold text-green-600">{Math.floor(playbook.successRate)}%</p>
                        </div>
                        <div>
                          <p className="text-gray-600">Est. Time</p>
                          <p className="font-semibold">{playbook.estimatedTime}</p>
                        </div>
                        <div>
                          <p className="text-gray-600">Times Used</p>
                          <p className="font-semibold">{playbook.timesUsed}</p>
                        </div>
                        <div>
                          <p className="text-gray-600">Last Used</p>
                          <p className="font-semibold">{new Date(playbook.lastUsed).toLocaleDateString()}</p>
                        </div>
                      </div>

                      <div className="mb-4">
                        <p className="text-sm font-medium text-gray-700 mb-2">Triggers:</p>
                        <div className="flex flex-wrap gap-2">
                          {playbook.triggers.map((trigger, idx) => (
                            <Badge key={idx} variant="secondary">
                              {trigger}
                            </Badge>
                          ))}
                        </div>
                      </div>

                      <div>
                        <p className="text-sm font-medium text-gray-700 mb-2">Actions:</p>
                        <div className="flex flex-wrap gap-2">
                          {playbook.actions.map((action, idx) => (
                            <Badge key={idx} variant="outline">
                              {action}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>

                    <div className="flex flex-col gap-2">
                      <Button size="sm" className="flex items-center gap-1">
                        <Play className="h-4 w-4" />
                        Execute
                      </Button>
                      <Button size="sm" variant="outline" className="flex items-center gap-1">
                        <Edit className="h-4 w-4" />
                        Edit
                      </Button>
                      <Button size="sm" variant="outline">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="metrics" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <LineChart className="h-5 w-5" />
                  Detection & Resolution Trends
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <RechartsLineChart data={anomalyMetrics}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="timestamp" />
                    <YAxis />
                    <Tooltip />
                    <Line type="monotone" dataKey="detectedAnomalies" stroke="#ff7300" strokeWidth={2} />
                    <Line type="monotone" dataKey="resolvedAnomalies" stroke="#00ff00" strokeWidth={2} />
                    <Line type="monotone" dataKey="criticalAnomalies" stroke="#ff0000" strokeWidth={2} />
                  </RechartsLineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Performance Metrics
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <RechartsBarChart data={anomalyMetrics}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="timestamp" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="avgResolutionTime" fill="#8884d8" />
                    <Bar dataKey="automationRate" fill="#82ca9d" />
                    <Bar dataKey="successRate" fill="#ffc658" />
                  </RechartsBarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <PieChart className="h-5 w-5" />
                Anomaly Distribution
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
                {['performance', 'security', 'availability', 'data_quality', 'cost', 'user_experience'].map((category) => {
                  const count = anomalies.filter(a => a.category === category).length;
                  const percentage = Math.floor((count / anomalies.length) * 100);
                  return (
                    <div key={category} className="text-center">
                      <div className="flex items-center justify-center mb-2">
                        {getCategoryIcon(category)}
                      </div>
                      <p className="text-lg font-semibold">{count}</p>
                      <p className="text-sm text-gray-600 capitalize">{category.replace('_', ' ')}</p>
                      <p className="text-xs text-gray-500">{percentage}%</p>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AutonomousAnomalyResolution;