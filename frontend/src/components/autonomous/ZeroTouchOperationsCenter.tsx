/**
 * Zero-Touch Analytics Operations Center
 * Fully autonomous operations management with AI-driven decision making
 */

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Brain,
  Cpu,
  Activity,
  Shield,
  Zap,
  Eye,
  Settings,
  RefreshCw,
  CheckCircle,
  AlertTriangle,
  Clock,
  Target,
  TrendingUp,
  TrendingDown,
  Play,
  Pause,
  Stop,
  Search,
  Filter,
  BarChart3,
  LineChart,
  PieChart,
  Gauge,
  Server,
  Database,
  Network,
  Cloud,
  Monitor,
  Terminal,
  GitBranch,
  Layers,
  Grid3x3,
  Workflow,
  Users,
  Calendar,
  DollarSign,
  Star,
  Flag,
  Lightbulb,
  Rocket,
  Award,
  Crown,
  Building2,
  Globe,
  MapPin,
  Edit,
  Trash,
  Plus,
  Minus,
  Download,
  Upload,
  Share,
  ExternalLink,
  MoreHorizontal,
  ChevronUp,
  ChevronDown,
  ArrowUp,
  ArrowDown,
  ArrowRight,
  ArrowLeft,
  Link2,
  Lock,
  Key,
  FileText,
  File,
  Folder,
  Archive,
  Mail,
  Phone,
  MessageCircle,
  Bell,
  BellRing,
  Volume2,
  Mic,
  Speaker,
  Camera,
  Smartphone,
  Laptop,
  Watch,
  Tv,
  Radio,
  Home,
  Office,
  Factory,
  Warehouse
} from 'lucide-react';
import { LineChart as RechartsLineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area, BarChart as RechartsBarChart, Bar, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar } from 'recharts';

// TypeScript interfaces for zero-touch operations
interface OperationsWorkflow {
  id: string;
  name: string;
  category: 'monitoring' | 'deployment' | 'scaling' | 'optimization' | 'security' | 'maintenance' | 'backup' | 'recovery';
  status: 'running' | 'paused' | 'completed' | 'failed' | 'scheduled';
  automationLevel: 'fully_automated' | 'human_approval' | 'manual_trigger' | 'supervised';
  priority: 'low' | 'medium' | 'high' | 'critical';
  schedule: {
    type: 'continuous' | 'interval' | 'event_driven' | 'scheduled';
    frequency: string;
    nextRun: string;
  };
  aiAgent: {
    name: string;
    confidence: number;
    decisionsMade: number;
    successRate: number;
  };
  metrics: {
    executionTime: number;
    successCount: number;
    failureCount: number;
    lastExecution: string;
  };
  dependencies: string[];
  actions: string[];
  createdAt: string;
}

interface AIDecision {
  id: string;
  workflowId: string;
  decision: string;
  reasoning: string;
  confidence: number;
  impact: 'low' | 'medium' | 'high' | 'critical';
  category: 'operational' | 'strategic' | 'tactical' | 'emergency';
  status: 'pending' | 'approved' | 'executed' | 'rolled_back';
  humanOverride: boolean;
  data: Record<string, any>;
  outcome: {
    success: boolean;
    metrics: Record<string, number>;
    feedback: string;
  };
  createdAt: string;
  executedAt: string;
}

interface OperationsAgent {
  id: string;
  name: string;
  type: 'monitoring' | 'analysis' | 'optimization' | 'security' | 'maintenance';
  status: 'active' | 'idle' | 'processing' | 'error' | 'updating';
  specialization: string[];
  currentTask: string;
  workload: number;
  performance: {
    accuracy: number;
    speed: number;
    reliability: number;
    efficiency: number;
  };
  capabilities: string[];
  recentActions: string[];
  lastUpdate: string;
}

interface SystemHealth {
  component: string;
  status: 'healthy' | 'warning' | 'critical' | 'unknown';
  score: number;
  metrics: Record<string, number>;
  lastCheck: string;
  autoHealing: boolean;
}

interface AutomationMetric {
  timestamp: string;
  workflowsExecuted: number;
  decisionsAutomated: number;
  humanInterventions: number;
  successRate: number;
  timeToResolution: number;
  costSavings: number;
  efficiencyGain: number;
}

interface OperationalInsight {
  id: string;
  insight: string;
  category: 'performance' | 'cost' | 'security' | 'reliability' | 'efficiency';
  severity: 'info' | 'warning' | 'critical';
  recommendation: string;
  estimatedImpact: string;
  confidence: number;
  aiGenerated: boolean;
  actionable: boolean;
  createdAt: string;
}

// Mock data generation functions
const generateOperationsWorkflows = (): OperationsWorkflow[] => {
  const categories = ['monitoring', 'deployment', 'scaling', 'optimization', 'security', 'maintenance', 'backup', 'recovery'] as const;
  const statuses = ['running', 'paused', 'completed', 'failed', 'scheduled'] as const;
  const automationLevels = ['fully_automated', 'human_approval', 'manual_trigger', 'supervised'] as const;
  const priorities = ['low', 'medium', 'high', 'critical'] as const;
  const scheduleTypes = ['continuous', 'interval', 'event_driven', 'scheduled'] as const;

  return Array.from({ length: 20 }, (_, i) => ({
    id: `workflow-${i + 1}`,
    name: [
      'Real-time Performance Monitoring',
      'Automated Deployment Pipeline',
      'Dynamic Resource Scaling',
      'Cost Optimization Engine',
      'Security Threat Detection',
      'Database Maintenance Automation',
      'Automated Backup Management',
      'Disaster Recovery Orchestration',
      'Log Analysis & Alerting',
      'Configuration Drift Detection',
      'Capacity Planning Automation',
      'Network Performance Optimization',
      'User Experience Monitoring',
      'Compliance Audit Automation',
      'API Health Monitoring',
      'Cache Optimization Engine',
      'SSL Certificate Management',
      'Performance Baseline Maintenance',
      'Anomaly Detection Pipeline',
      'Business Metrics Automation'
    ][i],
    category: categories[i % categories.length],
    status: statuses[i % statuses.length],
    automationLevel: automationLevels[i % automationLevels.length],
    priority: priorities[i % priorities.length],
    schedule: {
      type: scheduleTypes[i % scheduleTypes.length],
      frequency: ['Continuous', 'Every 5 minutes', 'Hourly', 'Daily', 'Weekly'][i % 5],
      nextRun: new Date(Date.now() + Math.random() * 24 * 60 * 60 * 1000).toISOString()
    },
    aiAgent: {
      name: ['OptiMax', 'SecureBot', 'ScaleGenie', 'MonitorAI', 'FixItNow'][i % 5],
      confidence: Math.random() * 30 + 70,
      decisionsMade: Math.floor(Math.random() * 1000) + 100,
      successRate: Math.random() * 20 + 80
    },
    metrics: {
      executionTime: Math.random() * 300 + 30,
      successCount: Math.floor(Math.random() * 500) + 50,
      failureCount: Math.floor(Math.random() * 20) + 1,
      lastExecution: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000).toISOString()
    },
    dependencies: i % 3 === 0 ? [`workflow-${(i + 1) % 20 + 1}`] : [],
    actions: [
      ['Monitor metrics', 'Send alerts', 'Auto-scale'],
      ['Deploy code', 'Run tests', 'Rollback if needed'],
      ['Analyze load', 'Scale resources', 'Optimize cost'],
      ['Monitor costs', 'Right-size resources', 'Suggest savings'],
      ['Scan for threats', 'Block suspicious activity', 'Alert security team'],
      ['Optimize queries', 'Rebuild indexes', 'Clean up logs'],
      ['Create backups', 'Verify integrity', 'Rotate old backups'],
      ['Test recovery', 'Update procedures', 'Monitor RTO/RPO']
    ][i % 8],
    createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString()
  }));
};

const generateAIDecisions = (): AIDecision[] => {
  const impacts = ['low', 'medium', 'high', 'critical'] as const;
  const categories = ['operational', 'strategic', 'tactical', 'emergency'] as const;
  const statuses = ['pending', 'approved', 'executed', 'rolled_back'] as const;

  return Array.from({ length: 24 }, (_, i) => ({
    id: `decision-${i + 1}`,
    workflowId: `workflow-${(i % 20) + 1}`,
    decision: [
      'Scale web servers by 50% due to traffic spike',
      'Enable DDoS protection after suspicious traffic detected',
      'Upgrade database instance for better performance',
      'Implement caching layer to reduce database load',
      'Switch to spot instances to reduce costs by 40%',
      'Migrate workloads to cheaper region',
      'Enable auto-scaling for peak traffic hours',
      'Update security rules after threat detection',
      'Optimize database queries causing performance issues',
      'Increase memory allocation for analytics service',
      'Deploy emergency patch for security vulnerability',
      'Restart services showing memory leaks',
      'Enable additional monitoring for critical services',
      'Implement rate limiting for API endpoints',
      'Scale down resources during low usage period',
      'Update load balancer configuration',
      'Enable compression to reduce bandwidth costs',
      'Migrate to more efficient instance types',
      'Implement circuit breaker for failing service',
      'Enable backup encryption for compliance',
      'Update SSL certificates before expiration',
      'Optimize CDN configuration for better performance',
      'Implement blue-green deployment strategy',
      'Enable chaos engineering for resilience testing'
    ][i],
    reasoning: [
      'Traffic increased 300% above baseline, response times degrading',
      'Detected coordinated attack from multiple IP addresses',
      'Database CPU consistently above 85%, queries timing out',
      'Database connection pool exhausted, high query volume',
      'Current instances underutilized, significant cost savings available',
      'Regional pricing 30% lower, minimal latency impact',
      'Predictable traffic patterns, opportunity for cost optimization',
      'Multiple failed authentication attempts detected',
      'Identified N+1 query problem causing performance bottleneck',
      'Memory usage trending upward, risk of OOM errors',
      'Critical security vulnerability with active exploits detected',
      'Memory usage growing linearly, indicating memory leak',
      'Service critical to business operations, needs enhanced visibility',
      'API showing elevated error rates under high load',
      'Usage dropped 70% from peak, resources underutilized',
      'Uneven traffic distribution causing hotspots',
      'Bandwidth costs increased 40% with minimal benefit',
      'New instance types offer 25% better price/performance',
      'Service showing cascading failures, need fault isolation',
      'Regulatory requirement for data encryption at rest',
      'Certificates expiring in 7 days, automated renewal failed',
      'Cache hit rate dropped 30%, affecting performance',
      'Current deployment strategy causes downtime',
      'Need to validate system resilience under failure conditions'
    ][i],
    confidence: Math.random() * 30 + 70,
    impact: impacts[i % impacts.length],
    category: categories[i % categories.length],
    status: statuses[i % statuses.length],
    humanOverride: Math.random() > 0.8,
    data: {
      metric1: Math.random() * 100,
      metric2: Math.random() * 1000,
      threshold: Math.random() * 50 + 50
    },
    outcome: {
      success: Math.random() > 0.2,
      metrics: {
        improvement: Math.random() * 50 + 10,
        cost: Math.random() * 1000 + 100,
        time: Math.random() * 60 + 5
      },
      feedback: 'Decision executed successfully with positive outcome'
    },
    createdAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
    executedAt: new Date(Date.now() - Math.random() * 5 * 24 * 60 * 60 * 1000).toISOString()
  }));
};

const generateOperationsAgents = (): OperationsAgent[] => {
  const types = ['monitoring', 'analysis', 'optimization', 'security', 'maintenance'] as const;
  const statuses = ['active', 'idle', 'processing', 'error', 'updating'] as const;

  return Array.from({ length: 12 }, (_, i) => ({
    id: `agent-${i + 1}`,
    name: [
      'WatchDog Monitor',
      'DataCrunch Analyzer',
      'OptiMax Optimizer',
      'SecureGuard Agent',
      'FixIt Maintenance Bot',
      'ScaleWise Agent',
      'CostCutter AI',
      'ReliabilityBot',
      'PerformanceGuru',
      'ComplianceChecker',
      'NetworkNinja',
      'BackupMaster'
    ][i],
    type: types[i % types.length],
    status: statuses[i % statuses.length],
    specialization: [
      ['CPU monitoring', 'Memory tracking', 'Disk I/O analysis'],
      ['Log analysis', 'Pattern recognition', 'Anomaly detection'],
      ['Resource optimization', 'Cost analysis', 'Performance tuning'],
      ['Threat detection', 'Vulnerability scanning', 'Access control'],
      ['System updates', 'Database maintenance', 'Log cleanup'],
      ['Auto-scaling', 'Load balancing', 'Capacity planning'],
      ['Cost optimization', 'Resource right-sizing', 'Savings identification'],
      ['Fault tolerance', 'Disaster recovery', 'Health checks'],
      ['Performance monitoring', 'Bottleneck detection', 'Optimization'],
      ['Compliance monitoring', 'Audit trails', 'Policy enforcement'],
      ['Network monitoring', 'Connectivity checks', 'Traffic analysis'],
      ['Backup management', 'Recovery testing', 'Data integrity']
    ][i],
    currentTask: [
      'Monitoring CPU utilization across all instances',
      'Analyzing log patterns for anomalies',
      'Optimizing database query performance',
      'Scanning for security vulnerabilities',
      'Performing scheduled system maintenance',
      'Evaluating auto-scaling thresholds',
      'Identifying cost optimization opportunities',
      'Testing disaster recovery procedures',
      'Monitoring application performance metrics',
      'Checking compliance with security policies',
      'Analyzing network traffic patterns',
      'Verifying backup integrity'
    ][i],
    workload: Math.random() * 100,
    performance: {
      accuracy: Math.random() * 20 + 80,
      speed: Math.random() * 30 + 70,
      reliability: Math.random() * 25 + 75,
      efficiency: Math.random() * 20 + 80
    },
    capabilities: [
      ['Real-time monitoring', 'Alert generation', 'Metric collection'],
      ['Data processing', 'Machine learning', 'Report generation'],
      ['Performance tuning', 'Resource allocation', 'Cost analysis'],
      ['Threat detection', 'Risk assessment', 'Incident response'],
      ['Automated fixes', 'Preventive maintenance', 'System health checks'],
      ['Dynamic scaling', 'Load prediction', 'Resource planning'],
      ['Cost tracking', 'Usage analysis', 'Savings recommendations'],
      ['Fault detection', 'Recovery automation', 'Resilience testing'],
      ['Performance analysis', 'Bottleneck identification', 'Optimization recommendations'],
      ['Policy enforcement', 'Audit generation', 'Compliance reporting'],
      ['Traffic monitoring', 'Connectivity testing', 'Performance optimization'],
      ['Backup scheduling', 'Data verification', 'Recovery testing']
    ][i],
    recentActions: [
      'Detected CPU spike and triggered auto-scaling',
      'Identified anomalous log pattern and created alert',
      'Optimized slow database query, 40% improvement',
      'Blocked suspicious IP after failed login attempts',
      'Completed database index rebuild and cleanup',
      'Adjusted auto-scaling policy based on traffic patterns',
      'Identified underutilized resources, suggested downsizing',
      'Tested backup recovery, verified data integrity',
      'Detected memory leak, restarted affected service',
      'Generated compliance report, all checks passed',
      'Optimized network routing, reduced latency by 15%',
      'Automated backup rotation, cleaned up old files'
    ][i],
    lastUpdate: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000).toISOString()
  }));
};

const generateSystemHealth = (): SystemHealth[] => {
  const statuses = ['healthy', 'warning', 'critical', 'unknown'] as const;
  const components = [
    'Web Servers', 'Database Cluster', 'Cache Layer', 'API Gateway',
    'Load Balancer', 'CDN', 'Message Queue', 'Auth Service',
    'Analytics Pipeline', 'Monitoring System', 'Backup System', 'Security Scanner'
  ];

  return components.map((component, i) => ({
    component,
    status: statuses[i % statuses.length],
    score: Math.random() * 100,
    metrics: {
      cpu: Math.random() * 100,
      memory: Math.random() * 100,
      disk: Math.random() * 100,
      network: Math.random() * 100,
      latency: Math.random() * 200,
      throughput: Math.random() * 1000
    },
    lastCheck: new Date(Date.now() - Math.random() * 60 * 60 * 1000).toISOString(),
    autoHealing: Math.random() > 0.3
  }));
};

const generateAutomationMetrics = (): AutomationMetric[] => {
  return Array.from({ length: 24 }, (_, i) => ({
    timestamp: new Date(Date.now() - (23 - i) * 60 * 60 * 1000).toISOString().split('T')[1].split(':')[0] + ':00',
    workflowsExecuted: Math.floor(Math.random() * 50) + 20,
    decisionsAutomated: Math.floor(Math.random() * 30) + 10,
    humanInterventions: Math.floor(Math.random() * 5) + 1,
    successRate: Math.random() * 20 + 80,
    timeToResolution: Math.random() * 30 + 5,
    costSavings: Math.random() * 1000 + 200,
    efficiencyGain: Math.random() * 30 + 10
  }));
};

const generateOperationalInsights = (): OperationalInsight[] => {
  const categories = ['performance', 'cost', 'security', 'reliability', 'efficiency'] as const;
  const severities = ['info', 'warning', 'critical'] as const;

  return Array.from({ length: 16 }, (_, i) => ({
    id: `insight-${i + 1}`,
    insight: [
      'Database query performance degraded 35% over the last week',
      'Spot instance usage could reduce costs by $2,400/month',
      'Failed login attempts increased 400% from new IP ranges',
      'API error rate correlation with deployment times detected',
      'Cache hit rate optimization could improve response times by 25%',
      'Storage costs growing 15% monthly, cleanup needed',
      'Network latency spikes during peak hours affecting UX',
      'Auto-scaling policies need adjustment for weekend traffic',
      'Security patches pending on 40% of production instances',
      'Database connection pool approaching limits during peak load',
      'CDN effectiveness decreased 20% due to cache configuration',
      'Backup verification failures increasing, investigation needed',
      'Memory usage trending upward across application servers',
      'SSL certificate renewals needed for 5 domains',
      'Load balancer health checks need tuning for accuracy',
      'Monitoring alert fatigue from false positives detected'
    ][i],
    category: categories[i % categories.length],
    severity: severities[i % severities.length],
    recommendation: [
      'Optimize database indexes and review slow query log',
      'Migrate non-critical workloads to spot instances',
      'Implement IP-based rate limiting and geographic blocking',
      'Implement canary deployments with automated rollback',
      'Tune cache TTL settings and implement cache warming',
      'Implement automated log rotation and archival policies',
      'Optimize network routing and consider CDN expansion',
      'Update auto-scaling policies with weekend traffic patterns',
      'Schedule maintenance window for security updates',
      'Increase connection pool size and optimize connection usage',
      'Review and optimize CDN cache headers and rules',
      'Investigate backup infrastructure and update procedures',
      'Profile application memory usage and optimize garbage collection',
      'Automate SSL certificate renewal process',
      'Fine-tune health check intervals and timeout values',
      'Review and adjust monitoring alert thresholds'
    ][i],
    estimatedImpact: [
      '35% query performance improvement',
      '$2,400/month cost savings',
      '90% reduction in security incidents',
      '50% reduction in deployment-related issues',
      '25% improvement in response times',
      '$800/month storage cost reduction',
      '15% improvement in user experience metrics',
      '20% reduction in unnecessary scaling events',
      'Enhanced security posture and compliance',
      '40% improvement in connection efficiency',
      '20% improvement in cache effectiveness',
      '95% improvement in backup reliability',
      '30% reduction in memory-related issues',
      'Elimination of certificate expiration risks',
      '60% reduction in false positive alerts',
      '70% reduction in alert noise'
    ][i],
    confidence: Math.random() * 30 + 70,
    aiGenerated: Math.random() > 0.3,
    actionable: Math.random() > 0.2,
    createdAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString()
  }));
};

const ZeroTouchOperationsCenter: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [activeTab, setActiveTab] = useState('overview');

  // Generate mock data
  const operationsWorkflows = useMemo(() => generateOperationsWorkflows(), []);
  const aiDecisions = useMemo(() => generateAIDecisions(), []);
  const operationsAgents = useMemo(() => generateOperationsAgents(), []);
  const systemHealth = useMemo(() => generateSystemHealth(), []);
  const automationMetrics = useMemo(() => generateAutomationMetrics(), []);
  const operationalInsights = useMemo(() => generateOperationalInsights(), []);

  // Filter and search logic
  const filteredWorkflows = useMemo(() => {
    return operationsWorkflows.filter(workflow => {
      const matchesSearch = workflow.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          workflow.category.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesStatus = statusFilter === 'all' || workflow.status === statusFilter;
      const matchesCategory = categoryFilter === 'all' || workflow.category === categoryFilter;
      return matchesSearch && matchesStatus && matchesCategory;
    });
  }, [operationsWorkflows, searchTerm, statusFilter, categoryFilter]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'bg-green-500';
      case 'paused': return 'bg-yellow-500';
      case 'completed': return 'bg-blue-500';
      case 'failed': return 'bg-red-500';
      case 'scheduled': return 'bg-purple-500';
      default: return 'bg-gray-500';
    }
  };

  const getHealthColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-600';
      case 'warning': return 'text-yellow-600';
      case 'critical': return 'text-red-600';
      case 'unknown': return 'text-gray-600';
      default: return 'text-gray-600';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'monitoring': return <Eye className="h-4 w-4" />;
      case 'deployment': return <Rocket className="h-4 w-4" />;
      case 'scaling': return <TrendingUp className="h-4 w-4" />;
      case 'optimization': return <Zap className="h-4 w-4" />;
      case 'security': return <Shield className="h-4 w-4" />;
      case 'maintenance': return <Settings className="h-4 w-4" />;
      case 'backup': return <Archive className="h-4 w-4" />;
      case 'recovery': return <RefreshCw className="h-4 w-4" />;
      default: return <Activity className="h-4 w-4" />;
    }
  };

  return (
    <div className="p-6 space-y-6 bg-gray-50 min-h-screen">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Zero-Touch Operations Center</h1>
          <p className="text-gray-600 mt-2">Fully autonomous operations management with AI-driven decision making</p>
        </div>
        <div className="flex gap-3">
          <Button variant="outline" className="flex items-center gap-2">
            <RefreshCw className="h-4 w-4" />
            Refresh
          </Button>
          <Button className="flex items-center gap-2">
            <Brain className="h-4 w-4" />
            AI Dashboard
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-7">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="workflows">Workflows</TabsTrigger>
          <TabsTrigger value="decisions">AI Decisions</TabsTrigger>
          <TabsTrigger value="agents">AI Agents</TabsTrigger>
          <TabsTrigger value="health">System Health</TabsTrigger>
          <TabsTrigger value="metrics">Automation Metrics</TabsTrigger>
          <TabsTrigger value="insights">Insights</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Active Workflows</p>
                    <p className="text-3xl font-bold text-green-600">
                      {operationsWorkflows.filter(w => w.status === 'running').length}
                    </p>
                  </div>
                  <Activity className="h-8 w-8 text-green-500" />
                </div>
                <div className="mt-4">
                  <Badge variant="secondary">
                    {operationsWorkflows.filter(w => w.automationLevel === 'fully_automated').length} Automated
                  </Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">AI Decisions</p>
                    <p className="text-3xl font-bold text-blue-600">
                      {aiDecisions.filter(d => d.status === 'executed').length}
                    </p>
                  </div>
                  <Brain className="h-8 w-8 text-blue-500" />
                </div>
                <div className="mt-4">
                  <Badge variant="secondary">
                    {Math.floor(aiDecisions.filter(d => d.outcome.success).length / aiDecisions.length * 100)}% Success
                  </Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Active Agents</p>
                    <p className="text-3xl font-bold text-purple-600">
                      {operationsAgents.filter(a => a.status === 'active' || a.status === 'processing').length}
                    </p>
                  </div>
                  <Cpu className="h-8 w-8 text-purple-500" />
                </div>
                <div className="mt-4">
                  <Badge variant="secondary" className="flex items-center gap-1">
                    <Zap className="h-3 w-3" />
                    High Performance
                  </Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">System Health</p>
                    <p className="text-3xl font-bold text-orange-600">
                      {Math.floor(systemHealth.reduce((sum, h) => sum + h.score, 0) / systemHealth.length)}%
                    </p>
                  </div>
                  <Gauge className="h-8 w-8 text-orange-500" />
                </div>
                <div className="mt-4">
                  <Badge variant="secondary">
                    {systemHealth.filter(h => h.status === 'healthy').length} Healthy
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Automation Performance
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={automationMetrics}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="timestamp" />
                    <YAxis />
                    <Tooltip />
                    <Area type="monotone" dataKey="workflowsExecuted" stackId="1" stroke="#8884d8" fill="#8884d8" />
                    <Area type="monotone" dataKey="decisionsAutomated" stackId="1" stroke="#82ca9d" fill="#82ca9d" />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <LineChart className="h-5 w-5" />
                  Efficiency Metrics
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <RechartsLineChart data={automationMetrics}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="timestamp" />
                    <YAxis />
                    <Tooltip />
                    <Line type="monotone" dataKey="successRate" stroke="#00ff00" strokeWidth={2} />
                    <Line type="monotone" dataKey="efficiencyGain" stroke="#ff7300" strokeWidth={2} />
                    <Line type="monotone" dataKey="timeToResolution" stroke="#8884d8" strokeWidth={2} />
                  </RechartsLineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  Real-time Operations
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {operationsWorkflows.filter(w => w.status === 'running').slice(0, 5).map((workflow) => (
                    <div key={workflow.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center gap-3">
                        {getCategoryIcon(workflow.category)}
                        <div>
                          <p className="font-medium">{workflow.name}</p>
                          <p className="text-sm text-gray-600">{workflow.aiAgent.name} - {Math.floor(workflow.aiAgent.confidence)}% confidence</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className={`w-3 h-3 rounded-full ${getStatusColor(workflow.status)}`} />
                        <Badge variant="outline">{workflow.priority}</Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  System Health Overview
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {systemHealth.slice(0, 6).map((health) => (
                    <div key={health.component} className="flex items-center justify-between">
                      <span className="text-sm font-medium">{health.component}</span>
                      <div className="flex items-center gap-2">
                        <span className="text-sm">{Math.floor(health.score)}%</span>
                        <span className={`text-sm font-medium ${getHealthColor(health.status)}`}>
                          {health.status}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="workflows" className="space-y-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search workflows..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex gap-2">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md"
              >
                <option value="all">All Status</option>
                <option value="running">Running</option>
                <option value="paused">Paused</option>
                <option value="completed">Completed</option>
                <option value="failed">Failed</option>
                <option value="scheduled">Scheduled</option>
              </select>
              <select
                value={categoryFilter}
                onChange={(e) => setCategoryFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md"
              >
                <option value="all">All Categories</option>
                <option value="monitoring">Monitoring</option>
                <option value="deployment">Deployment</option>
                <option value="scaling">Scaling</option>
                <option value="optimization">Optimization</option>
                <option value="security">Security</option>
                <option value="maintenance">Maintenance</option>
                <option value="backup">Backup</option>
                <option value="recovery">Recovery</option>
              </select>
            </div>
          </div>

          <div className="grid gap-4">
            <AnimatePresence>
              {filteredWorkflows.map((workflow) => (
                <motion.div
                  key={workflow.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.2 }}
                >
                  <Card className="hover:shadow-lg transition-shadow">
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <div className={`w-3 h-3 rounded-full ${getStatusColor(workflow.status)}`} />
                            {getCategoryIcon(workflow.category)}
                            <h3 className="text-lg font-semibold">{workflow.name}</h3>
                            <Badge variant="outline" className={
                              workflow.automationLevel === 'fully_automated' ? 'border-green-500 text-green-700' :
                              workflow.automationLevel === 'human_approval' ? 'border-yellow-500 text-yellow-700' :
                              workflow.automationLevel === 'manual_trigger' ? 'border-orange-500 text-orange-700' :
                              'border-blue-500 text-blue-700'
                            }>
                              {workflow.automationLevel.replace('_', ' ')}
                            </Badge>
                            <Badge variant="outline" className={
                              workflow.priority === 'critical' ? 'border-red-500 text-red-700' :
                              workflow.priority === 'high' ? 'border-orange-500 text-orange-700' :
                              workflow.priority === 'medium' ? 'border-yellow-500 text-yellow-700' :
                              'border-gray-500 text-gray-700'
                            }>
                              {workflow.priority}
                            </Badge>
                          </div>
                          
                          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4 text-sm">
                            <div>
                              <p className="text-gray-600">AI Agent</p>
                              <p className="font-medium">{workflow.aiAgent.name}</p>
                              <p className="text-xs text-gray-500">{Math.floor(workflow.aiAgent.confidence)}% confidence</p>
                            </div>
                            <div>
                              <p className="text-gray-600">Schedule</p>
                              <p className="font-medium">{workflow.schedule.frequency}</p>
                              <p className="text-xs text-gray-500">{workflow.schedule.type}</p>
                            </div>
                            <div>
                              <p className="text-gray-600">Success Rate</p>
                              <p className="font-medium text-green-600">
                                {Math.floor(workflow.metrics.successCount / (workflow.metrics.successCount + workflow.metrics.failureCount) * 100)}%
                              </p>
                            </div>
                            <div>
                              <p className="text-gray-600">Execution Time</p>
                              <p className="font-medium">{Math.floor(workflow.metrics.executionTime)}s</p>
                            </div>
                          </div>

                          <div className="mb-4">
                            <p className="text-sm font-medium text-gray-700 mb-2">Actions:</p>
                            <div className="flex flex-wrap gap-2">
                              {workflow.actions.map((action, idx) => (
                                <Badge key={idx} variant="secondary">
                                  {action}
                                </Badge>
                              ))}
                            </div>
                          </div>

                          <div className="flex items-center gap-4 text-sm text-gray-600">
                            <span>Decisions: {workflow.aiAgent.decisionsMade}</span>
                            <span>Success: {Math.floor(workflow.aiAgent.successRate)}%</span>
                            <span>Next: {new Date(workflow.schedule.nextRun).toLocaleString()}</span>
                          </div>
                        </div>

                        <div className="flex flex-col gap-2">
                          {workflow.status === 'paused' && (
                            <Button size="sm" className="flex items-center gap-1">
                              <Play className="h-4 w-4" />
                              Resume
                            </Button>
                          )}
                          {workflow.status === 'running' && (
                            <Button size="sm" variant="outline" className="flex items-center gap-1">
                              <Pause className="h-4 w-4" />
                              Pause
                            </Button>
                          )}
                          <Button size="sm" variant="outline" className="flex items-center gap-1">
                            <Eye className="h-4 w-4" />
                            Details
                          </Button>
                          <Button size="sm" variant="outline">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        </TabsContent>

        <TabsContent value="decisions" className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold">AI Decisions</h2>
            <Button className="flex items-center gap-2">
              <Brain className="h-4 w-4" />
              View AI Reasoning
            </Button>
          </div>

          <div className="grid gap-4">
            {aiDecisions.map((decision) => (
              <Card key={decision.id}>
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="text-lg font-semibold">{decision.decision}</h3>
                        <Badge variant={
                          decision.status === 'executed' ? 'default' :
                          decision.status === 'approved' ? 'secondary' :
                          decision.status === 'rolled_back' ? 'destructive' :
                          'outline'
                        }>
                          {decision.status}
                        </Badge>
                        <Badge variant="outline" className={
                          decision.impact === 'critical' ? 'border-red-500 text-red-700' :
                          decision.impact === 'high' ? 'border-orange-500 text-orange-700' :
                          decision.impact === 'medium' ? 'border-yellow-500 text-yellow-700' :
                          'border-gray-500 text-gray-700'
                        }>
                          {decision.impact} impact
                        </Badge>
                        <Badge variant="outline">{Math.floor(decision.confidence)}% confidence</Badge>
                        {decision.humanOverride && (
                          <Badge variant="outline" className="border-purple-500 text-purple-700">
                            Human Override
                          </Badge>
                        )}
                      </div>
                      
                      <div className="bg-gray-50 p-4 rounded-lg mb-4">
                        <div className="flex items-start gap-2">
                          <Brain className="h-5 w-5 text-blue-500 mt-0.5" />
                          <div className="flex-1">
                            <p className="font-semibold text-blue-700 mb-1">AI Reasoning</p>
                            <p className="text-sm text-gray-700">{decision.reasoning}</p>
                          </div>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <p className="text-gray-600">Category</p>
                          <p className="font-medium capitalize">{decision.category}</p>
                        </div>
                        <div>
                          <p className="text-gray-600">Workflow</p>
                          <p className="font-medium">{decision.workflowId}</p>
                        </div>
                        <div>
                          <p className="text-gray-600">Created</p>
                          <p className="font-medium">{new Date(decision.createdAt).toLocaleDateString()}</p>
                        </div>
                        <div>
                          <p className="text-gray-600">Executed</p>
                          <p className="font-medium">
                            {decision.executedAt ? new Date(decision.executedAt).toLocaleDateString() : 'Pending'}
                          </p>
                        </div>
                      </div>

                      {decision.outcome.success && (
                        <div className="mt-4 p-3 bg-green-50 rounded-lg">
                          <div className="flex items-center gap-2 mb-1">
                            <CheckCircle className="h-4 w-4 text-green-600" />
                            <span className="text-sm font-medium text-green-700">Outcome</span>
                          </div>
                          <p className="text-sm text-green-600">{decision.outcome.feedback}</p>
                          <div className="flex items-center gap-4 mt-2 text-xs text-green-600">
                            <span>Improvement: {Math.floor(decision.outcome.metrics.improvement)}%</span>
                            <span>Cost: ${Math.floor(decision.outcome.metrics.cost)}</span>
                            <span>Time: {Math.floor(decision.outcome.metrics.time)}min</span>
                          </div>
                        </div>
                      )}
                    </div>

                    <div className="flex flex-col gap-2">
                      {decision.status === 'pending' && (
                        <Button size="sm" className="flex items-center gap-1">
                          <CheckCircle className="h-4 w-4" />
                          Approve
                        </Button>
                      )}
                      <Button size="sm" variant="outline" className="flex items-center gap-1">
                        <Eye className="h-4 w-4" />
                        Details
                      </Button>
                      <Button size="sm" variant="outline">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="agents" className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold">AI Operations Agents</h2>
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Deploy Agent
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {operationsAgents.map((agent) => (
              <Card key={agent.id} className="hover:shadow-lg transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">{agent.name}</CardTitle>
                    <Badge variant={
                      agent.status === 'active' ? 'default' :
                      agent.status === 'processing' ? 'secondary' :
                      agent.status === 'error' ? 'destructive' :
                      'outline'
                    }>
                      {agent.status}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="capitalize">{agent.type}</Badge>
                    <Badge variant="outline">{Math.floor(agent.workload)}% load</Badge>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <p className="text-sm font-medium text-gray-700 mb-1">Current Task:</p>
                    <p className="text-sm text-gray-600">{agent.currentTask}</p>
                  </div>

                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-gray-600">Accuracy</p>
                      <p className="font-semibold">{Math.floor(agent.performance.accuracy)}%</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Speed</p>
                      <p className="font-semibold">{Math.floor(agent.performance.speed)}%</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Reliability</p>
                      <p className="font-semibold">{Math.floor(agent.performance.reliability)}%</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Efficiency</p>
                      <p className="font-semibold">{Math.floor(agent.performance.efficiency)}%</p>
                    </div>
                  </div>
                  
                  <div>
                    <p className="text-sm font-medium text-gray-700 mb-2">Specializations:</p>
                    <div className="flex flex-wrap gap-1">
                      {agent.specialization.map((spec, idx) => (
                        <Badge key={idx} variant="secondary" className="text-xs">
                          {spec}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <div>
                    <p className="text-sm font-medium text-gray-700 mb-1">Recent Action:</p>
                    <p className="text-xs text-gray-600">{agent.recentActions[0]}</p>
                  </div>

                  <div className="flex gap-2">
                    <Button variant="outline" size="sm" className="flex-1">
                      <Eye className="h-4 w-4 mr-1" />
                      Monitor
                    </Button>
                    <Button variant="outline" size="sm" className="flex-1">
                      <Settings className="h-4 w-4 mr-1" />
                      Configure
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="health" className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold">System Health Dashboard</h2>
            <Button className="flex items-center gap-2">
              <RefreshCw className="h-4 w-4" />
              Health Check
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {systemHealth.map((health) => (
              <Card key={health.component} className="hover:shadow-lg transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">{health.component}</CardTitle>
                    <div className="flex items-center gap-2">
                      <span className={`text-lg font-bold ${getHealthColor(health.status)}`}>
                        {Math.floor(health.score)}%
                      </span>
                      <Badge variant={
                        health.status === 'healthy' ? 'default' :
                        health.status === 'warning' ? 'secondary' :
                        health.status === 'critical' ? 'destructive' :
                        'outline'
                      }>
                        {health.status}
                      </Badge>
                    </div>
                  </div>
                  {health.autoHealing && (
                    <Badge variant="outline" className="w-fit flex items-center gap-1">
                      <Brain className="h-3 w-3" />
                      Auto-Healing
                    </Badge>
                  )}
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-gray-600">CPU</p>
                      <p className="font-semibold">{Math.floor(health.metrics.cpu)}%</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Memory</p>
                      <p className="font-semibold">{Math.floor(health.metrics.memory)}%</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Disk</p>
                      <p className="font-semibold">{Math.floor(health.metrics.disk)}%</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Network</p>
                      <p className="font-semibold">{Math.floor(health.metrics.network)}%</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Latency</p>
                      <p className="font-semibold">{Math.floor(health.metrics.latency)}ms</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Throughput</p>
                      <p className="font-semibold">{Math.floor(health.metrics.throughput)}/s</p>
                    </div>
                  </div>
                  
                  <div className="pt-2 border-t">
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <Clock className="h-4 w-4" />
                      <span>Last check: {new Date(health.lastCheck).toLocaleTimeString()}</span>
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <Button variant="outline" size="sm" className="flex-1">
                      <Eye className="h-4 w-4 mr-1" />
                      Details
                    </Button>
                    <Button variant="outline" size="sm" className="flex-1">
                      <Settings className="h-4 w-4 mr-1" />
                      Configure
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="metrics" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Automation Volume
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <RechartsBarChart data={automationMetrics}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="timestamp" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="workflowsExecuted" fill="#8884d8" />
                    <Bar dataKey="decisionsAutomated" fill="#82ca9d" />
                  </RechartsBarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <LineChart className="h-5 w-5" />
                  Success & Efficiency
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <RechartsLineChart data={automationMetrics}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="timestamp" />
                    <YAxis />
                    <Tooltip />
                    <Line type="monotone" dataKey="successRate" stroke="#00ff00" strokeWidth={2} />
                    <Line type="monotone" dataKey="efficiencyGain" stroke="#ff7300" strokeWidth={2} />
                  </RechartsLineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                Cost Savings & Resolution Time
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <AreaChart data={automationMetrics}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="timestamp" />
                  <YAxis />
                  <Tooltip />
                  <Area type="monotone" dataKey="costSavings" stroke="#00ff00" fill="#00ff00" fillOpacity={0.6} />
                  <Area type="monotone" dataKey="timeToResolution" stroke="#8884d8" fill="#8884d8" fillOpacity={0.6} />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="insights" className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold">Operational Insights</h2>
            <Button className="flex items-center gap-2">
              <Brain className="h-4 w-4" />
              Generate Insights
            </Button>
          </div>

          <div className="grid gap-4">
            {operationalInsights.map((insight) => (
              <Card key={insight.id}>
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <Badge variant="outline" className="capitalize">
                          {insight.category}
                        </Badge>
                        <Badge variant={
                          insight.severity === 'critical' ? 'destructive' :
                          insight.severity === 'warning' ? 'secondary' :
                          'outline'
                        }>
                          {insight.severity}
                        </Badge>
                        <Badge variant="outline">{Math.floor(insight.confidence)}% confidence</Badge>
                        {insight.aiGenerated && (
                          <Badge variant="outline" className="flex items-center gap-1">
                            <Brain className="h-3 w-3" />
                            AI Generated
                          </Badge>
                        )}
                        {insight.actionable && (
                          <Badge variant="outline" className="border-green-500 text-green-700">
                            Actionable
                          </Badge>
                        )}
                      </div>
                      <h3 className="text-lg font-semibold mb-2">{insight.insight}</h3>
                      <p className="text-gray-600 mb-3">{insight.recommendation}</p>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div>
                          <p className="text-gray-600">Estimated Impact</p>
                          <p className="font-medium text-green-600">{insight.estimatedImpact}</p>
                        </div>
                        <div>
                          <p className="text-gray-600">Category</p>
                          <p className="font-medium capitalize">{insight.category}</p>
                        </div>
                        <div>
                          <p className="text-gray-600">Generated</p>
                          <p className="font-medium">{new Date(insight.createdAt).toLocaleDateString()}</p>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {insight.actionable && (
                        <Button size="sm" className="flex items-center gap-1">
                          <CheckCircle className="h-4 w-4" />
                          Implement
                        </Button>
                      )}
                      <Button variant="outline" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ZeroTouchOperationsCenter;