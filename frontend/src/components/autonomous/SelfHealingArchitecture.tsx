/**
 * Self-Healing System Architecture
 * Autonomous fault detection, diagnosis, and recovery with zero downtime
 */

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Shield,
  AlertTriangle,
  CheckCircle,
  Activity,
  Cpu,
  Server,
  Database,
  Network,
  Cloud,
  Zap,
  RefreshCw,
  Brain,
  Eye,
  Settings,
  Play,
  Pause,
  Stop,
  RotateCcw,
  TrendingUp,
  TrendingDown,
  Clock,
  Calendar,
  Users,
  Target,
  Flag,
  Star,
  Lightbulb,
  Search,
  Filter,
  Download,
  Upload,
  Share,
  Edit,
  Trash,
  Plus,
  Minus,
  MoreHorizontal,
  ChevronUp,
  ChevronDown,
  ArrowUp,
  ArrowDown,
  ArrowRight,
  ArrowLeft,
  ExternalLink,
  Link2,
  Unlink,
  Lock,
  Unlock,
  Key,
  Hash,
  Tag,
  Globe,
  MapPin,
  Building2,
  Home,
  Office,
  Factory,
  Warehouse,
  Monitor,
  Smartphone,
  Tablet,
  Laptop,
  Watch,
  Camera,
  Mic,
  Speaker,
  Headphones,
  Volume2,
  VolumeX,
  Bell,
  BellRing,
  BellOff,
  Mail,
  Phone,
  MessageCircle,
  Send,
  Inbox,
  Archive,
  Folder,
  File,
  FileText,
  Image,
  Video,
  Music,
  Code,
  Terminal,
  GitBranch,
  GitCommit,
  GitMerge,
  Layers,
  Grid,
  List,
  Layout,
  Sidebar,
  PanelLeft,
  PanelRight,
  Menu,
  X,
  Copy,
  Cut,
  Paste,
  Scissors,
  PaintBucket,
  Palette,
  Brush,
  Eraser,
  Ruler,
  Move,
  FlipHorizontal,
  FlipVertical,
  Crop,
  Maximize,
  Minimize,
  Square,
  Circle,
  Triangle,
  Hexagon,
  Diamond,
  Heart,
  Sparkles,
  Flame,
  Snowflake,
  Sun,
  Moon,
  CloudRain,
  CloudSnow,
  Thermometer,
  Wind,
  Umbrella,
  Mountain,
  Tree,
  Flower,
  Leaf,
  Bug,
  Fish,
  Bird,
  Cat,
  Dog,
  Rabbit,
  Bear,
  Lion,
  Tiger,
  Elephant,
  Horse,
  Cow,
  Pig,
  Sheep,
  Chicken
} from 'lucide-react';
import { 
  LineChart as RechartsLineChart, 
  Line, 
  AreaChart, 
  Area, 
  BarChart, 
  Bar, 
  PieChart as RechartsPieChart,
  Cell,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  ComposedChart,
  ScatterChart,
  Scatter,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
  ReferenceLine,
  ReferenceArea
} from 'recharts';

// Types for Self-Healing System Architecture
interface SelfHealingSystem {
  id: string;
  name: string;
  description: string;
  type: 'microservice' | 'database' | 'api_gateway' | 'load_balancer' | 'cache' | 'queue' | 'storage' | 'network' | 'security';
  status: 'healthy' | 'degraded' | 'critical' | 'healing' | 'recovered' | 'offline';
  health_score: number;
  availability: number;
  recovery_time_objective: number; // RTO in seconds
  recovery_point_objective: number; // RPO in seconds
  mean_time_to_recovery: number; // MTTR in seconds
  mean_time_between_failures: number; // MTBF in seconds
  fault_tolerance: FaultTolerance;
  healing_capabilities: HealingCapability[];
  monitoring: SystemMonitoring;
  diagnostics: SystemDiagnostics;
  recovery_strategies: RecoveryStrategy[];
  circuit_breakers: CircuitBreaker[];
  bulkheads: Bulkhead[];
  timeouts: TimeoutConfig[];
  retries: RetryConfig[];
  fallbacks: FallbackMechanism[];
  chaos_engineering: ChaosEngineering;
  dependencies: SystemDependency[];
  sla_requirements: SLARequirement[];
  incidents: Incident[];
  metrics: SystemMetrics;
  alerts: SystemAlert[];
  runbooks: Runbook[];
  created_at: Date;
  updated_at: Date;
  last_health_check: Date;
  next_maintenance: Date;
  metadata: {
    owner: string;
    team: string;
    criticality: 'low' | 'medium' | 'high' | 'critical';
    environment: 'development' | 'staging' | 'production';
    region: string;
    cost_per_hour: number;
    compliance_requirements: string[];
    security_classification: 'public' | 'internal' | 'confidential' | 'restricted';
    tags: string[];
    version: string;
    documentation_url: string;
  };
}

interface FaultTolerance {
  redundancy_level: number;
  failure_modes: FailureMode[];
  tolerance_thresholds: ToleranceThreshold[];
  graceful_degradation: GracefulDegradation;
  isolation_mechanisms: IsolationMechanism[];
  recovery_mechanisms: RecoveryMechanism[];
  backup_systems: BackupSystem[];
  failover_strategies: FailoverStrategy[];
}

interface FailureMode {
  id: string;
  name: string;
  description: string;
  probability: number;
  impact: 'low' | 'medium' | 'high' | 'critical';
  detection_time: number;
  recovery_time: number;
  prevention_strategies: PreventionStrategy[];
  mitigation_strategies: MitigationStrategy[];
  early_warning_indicators: EarlyWarningIndicator[];
}

interface HealingCapability {
  id: string;
  name: string;
  type: 'self_diagnosis' | 'auto_restart' | 'config_rollback' | 'scale_out' | 'failover' | 'circuit_break' | 'load_shed' | 'cache_clear';
  description: string;
  trigger_conditions: TriggerCondition[];
  healing_actions: HealingAction[];
  success_criteria: SuccessCriteria[];
  rollback_conditions: RollbackCondition[];
  confidence_threshold: number;
  max_attempts: number;
  cooldown_period: number;
  learning_enabled: boolean;
  effectiveness_score: number;
  last_used: Date;
  usage_count: number;
  success_rate: number;
}

interface SystemMonitoring {
  health_checks: HealthCheck[];
  metrics: MonitoringMetric[];
  alerts: AlertRule[];
  dashboards: Dashboard[];
  logs: LogConfiguration;
  traces: TracingConfiguration;
  profiles: ProfilingConfiguration;
  anomaly_detection: AnomalyDetection;
  synthetic_monitoring: SyntheticMonitoring;
  real_user_monitoring: RealUserMonitoring;
}

interface SystemDiagnostics {
  diagnostic_tools: DiagnosticTool[];
  root_cause_analysis: RootCauseAnalysis;
  performance_profiling: PerformanceProfiling;
  dependency_analysis: DependencyAnalysis;
  capacity_analysis: CapacityAnalysis;
  security_scanning: SecurityScanning;
  compliance_checking: ComplianceChecking;
  knowledge_base: DiagnosticKnowledgeBase;
}

interface RecoveryStrategy {
  id: string;
  name: string;
  description: string;
  type: 'restart' | 'rollback' | 'failover' | 'scale' | 'repair' | 'replace' | 'reconfigure' | 'evacuate';
  priority: number;
  applicability: StrategyApplicability[];
  preconditions: Precondition[];
  recovery_steps: RecoveryStep[];
  validation_steps: ValidationStep[];
  rollback_steps: RollbackStep[];
  estimated_time: number;
  success_probability: number;
  risk_level: 'low' | 'medium' | 'high';
  resource_requirements: ResourceRequirement[];
  dependencies: string[];
  automation_level: 'manual' | 'semi_automatic' | 'automatic';
  approval_required: boolean;
  impact_assessment: ImpactAssessment;
}

interface CircuitBreaker {
  id: string;
  name: string;
  service: string;
  state: 'closed' | 'open' | 'half_open';
  failure_threshold: number;
  recovery_timeout: number;
  success_threshold: number;
  request_volume_threshold: number;
  metrics: CircuitBreakerMetrics;
  configuration: CircuitBreakerConfig;
  fallback_behavior: FallbackBehavior;
  notifications: NotificationConfig[];
}

interface Bulkhead {
  id: string;
  name: string;
  type: 'thread_pool' | 'connection_pool' | 'semaphore' | 'rate_limiter' | 'resource_partition';
  resource_allocation: ResourceAllocation;
  isolation_level: 'strict' | 'soft' | 'adaptive';
  capacity_limits: CapacityLimits;
  overflow_behavior: OverflowBehavior;
  monitoring: BulkheadMonitoring;
  adaptive_sizing: AdaptiveSizing;
}

interface TimeoutConfig {
  id: string;
  operation: string;
  timeout_duration: number;
  timeout_type: 'connection' | 'read' | 'write' | 'total';
  retry_on_timeout: boolean;
  escalation_behavior: EscalationBehavior;
  adaptive_timeout: AdaptiveTimeout;
}

interface RetryConfig {
  id: string;
  operation: string;
  max_attempts: number;
  retry_strategy: 'fixed' | 'exponential' | 'linear' | 'custom';
  initial_delay: number;
  max_delay: number;
  backoff_multiplier: number;
  jitter: boolean;
  retry_conditions: RetryCondition[];
  stop_conditions: StopCondition[];
  circuit_breaker_integration: boolean;
}

interface FallbackMechanism {
  id: string;
  name: string;
  trigger_conditions: TriggerCondition[];
  fallback_type: 'cached_response' | 'default_value' | 'alternative_service' | 'degraded_functionality' | 'error_response';
  fallback_configuration: FallbackConfiguration;
  quality_of_service: QualityOfService;
  expiration_policy: ExpirationPolicy;
  monitoring: FallbackMonitoring;
}

interface ChaosEngineering {
  enabled: boolean;
  experiments: ChaosExperiment[];
  safety_mechanisms: SafetyMechanism[];
  blast_radius: BlastRadius;
  schedule: ChaosSchedule;
  metrics: ChaosMetrics;
  learnings: ChaosLearning[];
  automation: ChaosAutomation;
}

interface SystemDependency {
  id: string;
  name: string;
  type: 'service' | 'database' | 'cache' | 'queue' | 'external_api' | 'file_system' | 'network';
  criticality: 'essential' | 'important' | 'optional';
  health_status: 'healthy' | 'degraded' | 'unhealthy' | 'unknown';
  dependency_relationship: 'synchronous' | 'asynchronous' | 'eventual_consistency';
  fallback_available: boolean;
  circuit_breaker_enabled: boolean;
  timeout_configuration: TimeoutConfig;
  retry_configuration: RetryConfig;
  monitoring: DependencyMonitoring;
}

interface Incident {
  id: string;
  title: string;
  description: string;
  severity: 'sev1' | 'sev2' | 'sev3' | 'sev4';
  status: 'open' | 'investigating' | 'identified' | 'monitoring' | 'resolved' | 'closed';
  impact: IncidentImpact;
  timeline: IncidentTimeline[];
  root_cause: RootCause;
  resolution: IncidentResolution;
  lessons_learned: LessonLearned[];
  action_items: ActionItem[];
  communications: IncidentCommunication[];
  metrics: IncidentMetrics;
  automation_involvement: AutomationInvolvement;
  human_intervention: HumanIntervention[];
  cost_impact: CostImpact;
  customer_impact: CustomerImpact;
  created_at: Date;
  resolved_at?: Date;
  post_mortem_completed: boolean;
}

interface SystemMetrics {
  availability: AvailabilityMetrics;
  performance: PerformanceMetrics;
  reliability: ReliabilityMetrics;
  scalability: ScalabilityMetrics;
  security: SecurityMetrics;
  cost: CostMetrics;
  user_experience: UserExperienceMetrics;
  business_impact: BusinessImpactMetrics;
  healing_effectiveness: HealingEffectivenessMetrics;
  prediction_accuracy: PredictionAccuracyMetrics;
}

interface SystemAlert {
  id: string;
  name: string;
  description: string;
  severity: 'info' | 'warning' | 'error' | 'critical';
  status: 'active' | 'acknowledged' | 'resolved' | 'suppressed';
  trigger_condition: string;
  threshold_value: number;
  current_value: number;
  trend: 'increasing' | 'decreasing' | 'stable' | 'volatile';
  auto_resolution: AutoResolution;
  escalation_rules: EscalationRule[];
  notification_channels: NotificationChannel[];
  runbook_references: string[];
  similar_incidents: SimilarIncident[];
  prediction: AlertPrediction;
  impact_assessment: AlertImpactAssessment;
  created_at: Date;
  acknowledged_at?: Date;
  resolved_at?: Date;
  last_evaluated: Date;
}

interface Runbook {
  id: string;
  title: string;
  description: string;
  category: 'incident_response' | 'maintenance' | 'troubleshooting' | 'deployment' | 'security' | 'capacity_planning';
  severity_applicability: string[];
  prerequisites: Prerequisite[];
  steps: RunbookStep[];
  automation_level: 'manual' | 'semi_automatic' | 'fully_automatic';
  estimated_duration: number;
  required_skills: string[];
  risk_level: 'low' | 'medium' | 'high';
  approval_required: boolean;
  testing_instructions: TestingInstruction[];
  rollback_instructions: RollbackInstruction[];
  success_criteria: SuccessCriteria[];
  common_issues: CommonIssue[];
  related_runbooks: string[];
  version: string;
  last_updated: Date;
  last_executed: Date;
  success_rate: number;
  average_execution_time: number;
}

// Additional supporting interfaces
interface TriggerCondition {
  metric: string;
  operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains' | 'regex' | 'threshold_breach';
  value: any;
  duration: number;
  confidence: number;
}

interface HealingAction {
  action: string;
  parameters: Record<string, any>;
  timeout: number;
  retry_count: number;
  rollback_on_failure: boolean;
}

interface SuccessCriteria {
  metric: string;
  expected_value: any;
  tolerance: number;
  validation_method: string;
}

interface RollbackCondition {
  condition: string;
  trigger_delay: number;
  automatic: boolean;
}

interface ToleranceThreshold {
  metric: string;
  warning_threshold: number;
  critical_threshold: number;
  recovery_threshold: number;
}

interface GracefulDegradation {
  levels: DegradationLevel[];
  automatic_triggers: AutomaticTrigger[];
  manual_overrides: ManualOverride[];
  recovery_conditions: RecoveryCondition[];
}

interface DegradationLevel {
  level: number;
  description: string;
  features_disabled: string[];
  performance_impact: number;
  user_experience_impact: string;
  business_impact: string;
}

interface PreventionStrategy {
  strategy: string;
  implementation: string;
  effectiveness: number;
  cost: number;
  maintenance_effort: string;
}

interface MitigationStrategy {
  strategy: string;
  implementation: string;
  response_time: number;
  effectiveness: number;
  automation_possible: boolean;
}

interface EarlyWarningIndicator {
  indicator: string;
  threshold: number;
  lead_time: number;
  false_positive_rate: number;
  detection_method: string;
}

// Mock data generators
const generateSelfHealingSystems = (): SelfHealingSystem[] => {
  const types: Array<'microservice' | 'database' | 'api_gateway' | 'load_balancer' | 'cache' | 'queue' | 'storage' | 'network' | 'security'> = 
    ['microservice', 'database', 'api_gateway', 'load_balancer', 'cache', 'queue', 'storage', 'network', 'security'];
  const statuses: Array<'healthy' | 'degraded' | 'critical' | 'healing' | 'recovered' | 'offline'> = 
    ['healthy', 'degraded', 'critical', 'healing', 'recovered', 'offline'];

  return Array.from({ length: 16 }, (_, i) => ({
    id: `system-${i + 1}`,
    name: [
      'User Authentication Service',
      'Product Catalog Database',
      'API Gateway',
      'Load Balancer Cluster',
      'Redis Cache Cluster',
      'Message Queue System',
      'File Storage Service',
      'Network Infrastructure',
      'Security Monitoring',
      'Payment Processing Service',
      'Inventory Management DB',
      'Analytics API Gateway',
      'CDN Load Balancer',
      'Session Cache',
      'Notification Queue',
      'Backup Storage'
    ][i],
    description: `Self-healing ${types[i % types.length]} with autonomous fault detection and recovery`,
    type: types[i % types.length],
    status: statuses[Math.floor(Math.random() * statuses.length)],
    health_score: 60 + Math.random() * 40,
    availability: 95 + Math.random() * 5,
    recovery_time_objective: Math.random() * 300 + 60, // 1-5 minutes
    recovery_point_objective: Math.random() * 60 + 5, // 5-65 seconds
    mean_time_to_recovery: Math.random() * 1800 + 300, // 5-35 minutes
    mean_time_between_failures: Math.random() * 720 + 168, // 1-4 weeks in hours
    fault_tolerance: {
      redundancy_level: Math.floor(Math.random() * 5) + 1,
      failure_modes: [],
      tolerance_thresholds: [],
      graceful_degradation: {} as any,
      isolation_mechanisms: [],
      recovery_mechanisms: [],
      backup_systems: [],
      failover_strategies: []
    },
    healing_capabilities: [],
    monitoring: {} as any,
    diagnostics: {} as any,
    recovery_strategies: [],
    circuit_breakers: [],
    bulkheads: [],
    timeouts: [],
    retries: [],
    fallbacks: [],
    chaos_engineering: {
      enabled: Math.random() > 0.3,
      experiments: [],
      safety_mechanisms: [],
      blast_radius: {} as any,
      schedule: {} as any,
      metrics: {} as any,
      learnings: [],
      automation: {} as any
    },
    dependencies: [],
    sla_requirements: [],
    incidents: [],
    metrics: {} as any,
    alerts: [],
    runbooks: [],
    created_at: new Date(Date.now() - Math.random() * 180 * 24 * 60 * 60 * 1000),
    updated_at: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000),
    last_health_check: new Date(Date.now() - Math.random() * 60 * 60 * 1000),
    next_maintenance: new Date(Date.now() + Math.random() * 30 * 24 * 60 * 60 * 1000),
    metadata: {
      owner: ['Platform Team', 'Data Team', 'Security Team', 'Infrastructure Team'][Math.floor(Math.random() * 4)],
      team: ['Platform Engineering', 'Site Reliability', 'DevOps', 'Infrastructure'][Math.floor(Math.random() * 4)],
      criticality: ['low', 'medium', 'high', 'critical'][Math.floor(Math.random() * 4)] as any,
      environment: ['production', 'staging', 'development'][Math.floor(Math.random() * 3)] as any,
      region: ['us-east-1', 'us-west-2', 'eu-west-1', 'ap-southeast-1'][Math.floor(Math.random() * 4)],
      cost_per_hour: Math.random() * 100 + 10,
      compliance_requirements: ['SOC2', 'ISO27001', 'GDPR', 'HIPAA'].slice(0, Math.floor(Math.random() * 3) + 1),
      security_classification: ['public', 'internal', 'confidential', 'restricted'][Math.floor(Math.random() * 4)] as any,
      tags: ['self-healing', 'production', 'critical', 'monitored'].slice(0, Math.floor(Math.random() * 4) + 1),
      version: `v${Math.floor(Math.random() * 5) + 1}.${Math.floor(Math.random() * 10)}.${Math.floor(Math.random() * 10)}`,
      documentation_url: 'https://docs.company.com/systems'
    }
  }));
};

const generateIncidents = (): Incident[] => {
  const severities: Array<'sev1' | 'sev2' | 'sev3' | 'sev4'> = ['sev1', 'sev2', 'sev3', 'sev4'];
  const statuses: Array<'open' | 'investigating' | 'identified' | 'monitoring' | 'resolved' | 'closed'> = 
    ['open', 'investigating', 'identified', 'monitoring', 'resolved', 'closed'];

  return Array.from({ length: 8 }, (_, i) => ({
    id: `incident-${i + 1}`,
    title: [
      'Database Connection Pool Exhaustion',
      'API Gateway Rate Limiting Issue',
      'Cache Cluster Memory Spike',
      'Load Balancer Health Check Failures',
      'Message Queue Processing Delays',
      'Storage Service Timeout Errors',
      'Authentication Service Latency',
      'Payment Processing Failures'
    ][i],
    description: `Automated incident detection and response for ${['database', 'api gateway', 'cache', 'load balancer', 'queue', 'storage', 'auth service', 'payment system'][i]} issues`,
    severity: severities[Math.floor(Math.random() * severities.length)],
    status: statuses[Math.floor(Math.random() * statuses.length)],
    impact: {} as any,
    timeline: [],
    root_cause: {} as any,
    resolution: {} as any,
    lessons_learned: [],
    action_items: [],
    communications: [],
    metrics: {} as any,
    automation_involvement: {} as any,
    human_intervention: [],
    cost_impact: {} as any,
    customer_impact: {} as any,
    created_at: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000),
    resolved_at: Math.random() > 0.4 ? new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000) : undefined,
    post_mortem_completed: Math.random() > 0.3
  }));
};

const generateHealingMetrics = () => {
  const data = [];
  for (let i = 23; i >= 0; i--) {
    const timestamp = new Date(Date.now() - i * 60 * 60 * 1000);
    data.push({
      timestamp: timestamp.toISOString(),
      healing_events: Math.floor(Math.random() * 20),
      success_rate: 85 + Math.random() * 15,
      detection_time: Math.random() * 30 + 5,
      recovery_time: Math.random() * 300 + 60,
      prevented_incidents: Math.floor(Math.random() * 5),
      system_health: 80 + Math.random() * 20
    });
  }
  return data;
};

const generateHealingActivities = () => {
  return [
    {
      system: 'API Gateway',
      action: 'Auto-scaled replicas from 3 to 6 due to high traffic',
      outcome: 'Response time improved by 45%',
      timestamp: new Date(Date.now() - 15 * 60 * 1000),
      confidence: 0.96,
      type: 'scale_out'
    },
    {
      system: 'Database Cluster',
      action: 'Triggered failover to secondary replica',
      outcome: 'Zero data loss, 30s downtime avoided',
      timestamp: new Date(Date.now() - 35 * 60 * 1000),
      confidence: 0.92,
      type: 'failover'
    },
    {
      system: 'Cache Service',
      action: 'Cleared corrupted cache entries automatically',
      outcome: 'Cache hit rate restored to 95%',
      timestamp: new Date(Date.now() - 50 * 60 * 1000),
      confidence: 0.88,
      type: 'cache_clear'
    },
    {
      system: 'Load Balancer',
      action: 'Removed unhealthy backend from rotation',
      outcome: 'Error rate reduced from 5% to 0.1%',
      timestamp: new Date(Date.now() - 75 * 60 * 1000),
      confidence: 0.94,
      type: 'isolation'
    },
    {
      system: 'Message Queue',
      action: 'Restarted stuck consumer processes',
      outcome: 'Queue lag reduced by 80%',
      timestamp: new Date(Date.now() - 90 * 60 * 1000),
      confidence: 0.91,
      type: 'auto_restart'
    }
  ];
};

const STATUS_COLORS = {
  healthy: '#10b981',
  degraded: '#f59e0b',
  critical: '#ef4444',
  healing: '#3b82f6',
  recovered: '#8b5cf6',
  offline: '#6b7280'
};

const SEVERITY_COLORS = {
  sev1: '#dc2626',
  sev2: '#ea580c',
  sev3: '#d97706',
  sev4: '#65a30d'
};

const SelfHealingArchitecture: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [filterType, setFilterType] = useState<string>('all');
  const [filterCriticality, setFilterCriticality] = useState<string>('all');

  const systems = useMemo(() => generateSelfHealingSystems(), []);
  const incidents = useMemo(() => generateIncidents(), []);
  const healingMetrics = useMemo(() => generateHealingMetrics(), []);
  const healingActivities = useMemo(() => generateHealingActivities(), []);

  const filteredSystems = useMemo(() => {
    return systems.filter(system => {
      const matchesSearch = system.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        system.description.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesStatus = filterStatus === 'all' || system.status === filterStatus;
      const matchesType = filterType === 'all' || system.type === filterType;
      const matchesCriticality = filterCriticality === 'all' || system.metadata.criticality === filterCriticality;
      return matchesSearch && matchesStatus && matchesType && matchesCriticality;
    });
  }, [systems, searchTerm, filterStatus, filterType, filterCriticality]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy': return <CheckCircle className="w-4 h-4" />;
      case 'degraded': return <AlertTriangle className="w-4 h-4" />;
      case 'critical': return <AlertTriangle className="w-4 h-4" />;
      case 'healing': return <RefreshCw className="w-4 h-4" />;
      case 'recovered': return <CheckCircle className="w-4 h-4" />;
      case 'offline': return <X className="w-4 h-4" />;
      default: return <Activity className="w-4 h-4" />;
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'microservice': return <Server className="w-4 h-4" />;
      case 'database': return <Database className="w-4 h-4" />;
      case 'api_gateway': return <Network className="w-4 h-4" />;
      case 'load_balancer': return <Network className="w-4 h-4" />;
      case 'cache': return <Zap className="w-4 h-4" />;
      case 'queue': return <Activity className="w-4 h-4" />;
      case 'storage': return <Archive className="w-4 h-4" />;
      case 'network': return <Globe className="w-4 h-4" />;
      case 'security': return <Shield className="w-4 h-4" />;
      default: return <Server className="w-4 h-4" />;
    }
  };

  const getHealingActionIcon = (type: string) => {
    switch (type) {
      case 'scale_out': return <TrendingUp className="w-4 h-4" />;
      case 'failover': return <RefreshCw className="w-4 h-4" />;
      case 'cache_clear': return <Trash className="w-4 h-4" />;
      case 'isolation': return <Shield className="w-4 h-4" />;
      case 'auto_restart': return <RotateCcw className="w-4 h-4" />;
      default: return <Zap className="w-4 h-4" />;
    }
  };

  const getHealthColor = (score: number) => {
    if (score >= 90) return '#10b981';
    if (score >= 75) return '#3b82f6';
    if (score >= 60) return '#f59e0b';
    return '#ef4444';
  };

  return (
    <div className="p-6 max-w-7xl mx-auto space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Self-Healing System Architecture</h1>
          <p className="text-gray-600 mt-2">Autonomous fault detection, diagnosis, and recovery with zero downtime</p>
        </div>
        <div className="flex items-center space-x-4">
          <Button variant="outline" className="flex items-center space-x-2">
            <Settings className="w-4 h-4" />
            <span>Configure Healing</span>
          </Button>
          <Button className="flex items-center space-x-2">
            <Plus className="w-4 h-4" />
            <span>Add System</span>
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="systems">Systems</TabsTrigger>
          <TabsTrigger value="healing">Healing Events</TabsTrigger>
          <TabsTrigger value="incidents">Incidents</TabsTrigger>
          <TabsTrigger value="chaos">Chaos Engineering</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Healthy Systems</p>
                    <p className="text-3xl font-bold text-gray-900">
                      {systems.filter(s => s.status === 'healthy').length}
                    </p>
                  </div>
                  <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <CheckCircle className="h-6 w-6 text-green-600" />
                  </div>
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  {((systems.filter(s => s.status === 'healthy').length / systems.length) * 100).toFixed(1)}% of total
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Average MTTR</p>
                    <p className="text-3xl font-bold text-gray-900">
                      {Math.round(systems.reduce((sum, s) => sum + s.mean_time_to_recovery, 0) / systems.length / 60)}m
                    </p>
                  </div>
                  <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Clock className="h-6 w-6 text-blue-600" />
                  </div>
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  -15% from last month
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Healing Events</p>
                    <p className="text-3xl font-bold text-gray-900">342</p>
                  </div>
                  <div className="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <Shield className="h-6 w-6 text-purple-600" />
                  </div>
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  18 in the last hour
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Availability</p>
                    <p className="text-3xl font-bold text-gray-900">
                      {(systems.reduce((sum, s) => sum + s.availability, 0) / systems.length).toFixed(2)}%
                    </p>
                  </div>
                  <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <TrendingUp className="h-6 w-6 text-green-600" />
                  </div>
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  +0.15% from target
                </p>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Activity className="w-5 h-5" />
                  <span>Self-Healing Metrics</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <ComposedChart data={healingMetrics}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="timestamp" />
                      <YAxis yAxisId="left" />
                      <YAxis yAxisId="right" orientation="right" />
                      <Tooltip />
                      <Legend />
                      <Area
                        yAxisId="left"
                        type="monotone"
                        dataKey="healing_events"
                        fill="#3b82f6"
                        stroke="#3b82f6"
                        name="Healing Events"
                      />
                      <Line
                        yAxisId="right"
                        type="monotone"
                        dataKey="success_rate"
                        stroke="#10b981"
                        name="Success Rate %"
                      />
                      <Line
                        yAxisId="left"
                        type="monotone"
                        dataKey="detection_time"
                        stroke="#f59e0b"
                        name="Detection Time (s)"
                      />
                    </ComposedChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Shield className="w-5 h-5" />
                  <span>Recent Healing Activities</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {healingActivities.map((activity, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      delay={index * 0.1}
                      className="flex items-start space-x-3 p-3 border border-gray-200 rounded-lg"
                    >
                      <div className="flex items-center space-x-2">
                        {getHealingActionIcon(activity.type)}
                        <Badge variant="outline" className="text-xs">
                          {(activity.confidence * 100).toFixed(0)}%
                        </Badge>
                      </div>
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900 text-sm">{activity.system}</h4>
                        <p className="text-sm text-gray-600 mt-1">{activity.action}</p>
                        <div className="flex items-center justify-between mt-2">
                          <span className="text-xs font-medium text-green-600">{activity.outcome}</span>
                          <span className="text-xs text-gray-500">
                            {activity.timestamp.toLocaleTimeString()}
                          </span>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Server className="w-5 h-5" />
                <span>System Health Overview</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {systems.slice(0, 8).map((system) => (
                  <motion.div
                    key={system.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="border border-gray-200 rounded-lg p-4"
                  >
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-2">
                        {getTypeIcon(system.type)}
                        <Badge 
                          style={{ backgroundColor: STATUS_COLORS[system.status] }}
                          className="text-white text-xs"
                        >
                          {getStatusIcon(system.status)}
                        </Badge>
                      </div>
                      <div 
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: getHealthColor(system.health_score) }}
                      />
                    </div>
                    
                    <h4 className="font-medium text-gray-900 text-sm mb-2">{system.name}</h4>
                    
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-xs">
                        <span className="text-gray-600">Health Score:</span>
                        <span className="font-medium">{system.health_score.toFixed(1)}%</span>
                      </div>
                      <div className="flex items-center justify-between text-xs">
                        <span className="text-gray-600">Availability:</span>
                        <span className="font-medium">{system.availability.toFixed(2)}%</span>
                      </div>
                      <div className="flex items-center justify-between text-xs">
                        <span className="text-gray-600">MTTR:</span>
                        <span className="font-medium">{Math.round(system.mean_time_to_recovery / 60)}m</span>
                      </div>
                    </div>

                    <div className="mt-3">
                      <Badge 
                        variant={system.metadata.criticality === 'critical' ? 'destructive' : 'secondary'}
                        className="text-xs"
                      >
                        {system.metadata.criticality}
                      </Badge>
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="systems" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Self-Healing Systems</CardTitle>
              <div className="flex items-center space-x-4 mt-4">
                <div className="flex-1">
                  <Input
                    placeholder="Search systems..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="max-w-sm"
                  />
                </div>
                <select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  className="border border-gray-300 rounded-md px-3 py-2"
                >
                  <option value="all">All Statuses</option>
                  <option value="healthy">Healthy</option>
                  <option value="degraded">Degraded</option>
                  <option value="critical">Critical</option>
                  <option value="healing">Healing</option>
                  <option value="recovered">Recovered</option>
                  <option value="offline">Offline</option>
                </select>
                <select
                  value={filterType}
                  onChange={(e) => setFilterType(e.target.value)}
                  className="border border-gray-300 rounded-md px-3 py-2"
                >
                  <option value="all">All Types</option>
                  <option value="microservice">Microservice</option>
                  <option value="database">Database</option>
                  <option value="api_gateway">API Gateway</option>
                  <option value="load_balancer">Load Balancer</option>
                  <option value="cache">Cache</option>
                  <option value="queue">Queue</option>
                  <option value="storage">Storage</option>
                  <option value="network">Network</option>
                  <option value="security">Security</option>
                </select>
                <select
                  value={filterCriticality}
                  onChange={(e) => setFilterCriticality(e.target.value)}
                  className="border border-gray-300 rounded-md px-3 py-2"
                >
                  <option value="all">All Criticality</option>
                  <option value="low">Low</option>
                  <option value="medium">Medium</option>
                  <option value="high">High</option>
                  <option value="critical">Critical</option>
                </select>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredSystems.map((system) => (
                  <motion.div
                    key={system.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <Badge 
                          style={{ backgroundColor: STATUS_COLORS[system.status] }}
                          className="text-white"
                        >
                          {getStatusIcon(system.status)}
                          <span className="ml-1">{system.status.toUpperCase()}</span>
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          {getTypeIcon(system.type)}
                          <span className="ml-1">{system.type.replace('_', ' ')}</span>
                        </Badge>
                        <Badge 
                          variant={system.metadata.criticality === 'critical' ? 'destructive' : 'secondary'}
                          className="text-xs"
                        >
                          {system.metadata.criticality}
                        </Badge>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-xs text-gray-500">
                          Last check: {system.last_health_check.toLocaleTimeString()}
                        </span>
                        <Button variant="outline" size="sm">
                          <MoreHorizontal className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>

                    <div className="mb-3">
                      <h3 className="font-semibold text-gray-900">{system.name}</h3>
                      <p className="text-sm text-gray-600 mt-1">{system.description}</p>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-3">
                      <div className="text-center">
                        <div className="flex items-center justify-center mb-1">
                          <div 
                            className="w-3 h-3 rounded-full mr-2"
                            style={{ backgroundColor: getHealthColor(system.health_score) }}
                          />
                          <span className="text-sm font-medium">Health</span>
                        </div>
                        <span className="text-lg font-bold">{system.health_score.toFixed(1)}%</span>
                      </div>
                      <div className="text-center">
                        <div className="text-sm font-medium text-gray-600 mb-1">Availability</div>
                        <span className="text-lg font-bold">{system.availability.toFixed(2)}%</span>
                      </div>
                      <div className="text-center">
                        <div className="text-sm font-medium text-gray-600 mb-1">RTO</div>
                        <span className="text-lg font-bold">{Math.round(system.recovery_time_objective)}s</span>
                      </div>
                      <div className="text-center">
                        <div className="text-sm font-medium text-gray-600 mb-1">MTTR</div>
                        <span className="text-lg font-bold">{Math.round(system.mean_time_to_recovery / 60)}m</span>
                      </div>
                      <div className="text-center">
                        <div className="text-sm font-medium text-gray-600 mb-1">MTBF</div>
                        <span className="text-lg font-bold">{Math.round(system.mean_time_between_failures / 24)}d</span>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4 text-sm text-gray-600">
                        <span>Owner: {system.metadata.owner}</span>
                        <span>•</span>
                        <span>Region: {system.metadata.region}</span>
                        <span>•</span>
                        <span>Version: {system.metadata.version}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button variant="outline" size="sm">
                          <Eye className="w-4 h-4 mr-1" />
                          Monitor
                        </Button>
                        <Button variant="outline" size="sm">
                          <Shield className="w-4 h-4 mr-1" />
                          Heal
                        </Button>
                        <Button variant="outline" size="sm">
                          <Settings className="w-4 h-4 mr-1" />
                          Configure
                        </Button>
                      </div>
                    </div>

                    <div className="mt-3 flex items-center space-x-1">
                      {system.metadata.tags.map((tag) => (
                        <Badge key={tag} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="healing" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <RefreshCw className="w-5 h-5" />
                <span>Healing Events Timeline</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {healingActivities.map((activity, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    delay={index * 0.1}
                    className="border border-gray-200 rounded-lg p-4"
                  >
                    <div className="flex items-start space-x-4">
                      <div className="flex items-center space-x-2">
                        <div 
                          className="w-8 h-8 rounded-full flex items-center justify-center"
                          style={{ backgroundColor: `${STATUS_COLORS.healing}20`, color: STATUS_COLORS.healing }}
                        >
                          {getHealingActionIcon(activity.type)}
                        </div>
                        <Badge variant="outline" className="text-xs">
                          {activity.type.replace('_', ' ')}
                        </Badge>
                      </div>
                      
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium text-gray-900">{activity.system}</h4>
                          <div className="flex items-center space-x-2">
                            <Badge variant="secondary" className="text-xs">
                              {(activity.confidence * 100).toFixed(0)}% confidence
                            </Badge>
                            <span className="text-xs text-gray-500">
                              {activity.timestamp.toLocaleString()}
                            </span>
                          </div>
                        </div>
                        
                        <p className="text-sm text-gray-600 mb-2">{activity.action}</p>
                        <p className="text-sm font-medium text-green-600">{activity.outcome}</p>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="incidents" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <AlertTriangle className="w-5 h-5" />
                <span>Incidents & Responses</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {incidents.map((incident) => (
                  <motion.div
                    key={incident.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="border border-gray-200 rounded-lg p-4"
                  >
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <Badge 
                          style={{ backgroundColor: SEVERITY_COLORS[incident.severity] }}
                          className="text-white"
                        >
                          {incident.severity.toUpperCase()}
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          {incident.status.replace('_', ' ')}
                        </Badge>
                      </div>
                      <span className="text-xs text-gray-500">
                        {incident.created_at.toLocaleDateString()}
                      </span>
                    </div>

                    <h3 className="font-semibold text-gray-900 mb-2">{incident.title}</h3>
                    <p className="text-sm text-gray-600 mb-3">{incident.description}</p>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4 text-sm text-gray-600">
                        <span>Status: {incident.status}</span>
                        {incident.resolved_at && (
                          <>
                            <span>•</span>
                            <span>Resolved: {incident.resolved_at.toLocaleDateString()}</span>
                          </>
                        )}
                        <span>•</span>
                        <span>Post-mortem: {incident.post_mortem_completed ? 'Complete' : 'Pending'}</span>
                      </div>
                      <Button variant="outline" size="sm">
                        <Eye className="w-4 h-4 mr-1" />
                        View Details
                      </Button>
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="chaos" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Zap className="w-5 h-5" />
                <span>Chaos Engineering</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Zap className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Chaos Engineering Platform</h3>
                <p className="text-gray-600 mb-4">
                  Controlled failure injection to test and improve system resilience
                </p>
                <Button>
                  <Play className="w-4 h-4 mr-2" />
                  Run Chaos Experiment
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Activity className="w-5 h-5" />
                <span>Resilience Analytics</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Activity className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Advanced Analytics</h3>
                <p className="text-gray-600 mb-4">
                  Deep insights into system resilience patterns and improvement opportunities
                </p>
                <Button>
                  <Eye className="w-4 h-4 mr-2" />
                  View Analytics
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default SelfHealingArchitecture;