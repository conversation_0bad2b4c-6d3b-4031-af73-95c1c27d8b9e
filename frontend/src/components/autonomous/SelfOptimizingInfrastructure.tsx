/**
 * Self-Optimizing Infrastructure Scaling
 * Autonomous resource management with predictive scaling and cost optimization
 */

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Server,
  Cpu,
  MemoryStick,
  HardDrive,
  Network,
  Cloud,
  Zap,
  TrendingUp,
  TrendingDown,
  Activity,
  Gauge,
  Target,
  Brain,
  Shield,
  DollarSign,
  AlertTriangle,
  CheckCircle,
  Clock,
  Calendar,
  Settings,
  RefreshCw,
  Play,
  Pause,
  Stop,
  BarChart3,
  LineChart,
  PieChart,
  Monitor,
  Database,
  GitBranch,
  Eye,
  Edit,
  Plus,
  Minus,
  Search,
  Filter,
  Download,
  Upload,
  Share,
  ExternalLink,
  MoreHorizontal,
  ChevronUp,
  ChevronDown,
  ArrowUp,
  ArrowDown,
  ArrowRight,
  Layers,
  Grid3x3,
  Workflow,
  Building2,
  Globe,
  MapPin,
  Users,
  Star,
  Flag,
  Lightbulb,
  Rocket,
  Award,
  Crown
} from 'lucide-react';
import { LineChart as RechartsLineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area, BarChart as RechartsBarChart, Bar } from 'recharts';

// TypeScript interfaces for self-optimizing infrastructure
interface InfrastructureNode {
  id: string;
  name: string;
  type: 'compute' | 'storage' | 'network' | 'database' | 'cache' | 'queue' | 'api' | 'cdn';
  region: string;
  provider: string;
  status: 'healthy' | 'warning' | 'critical' | 'scaling' | 'optimizing';
  metrics: {
    cpu: number;
    memory: number;
    storage: number;
    network: number;
    latency: number;
    errorRate: number;
    throughput: number;
  };
  capacity: {
    current: number;
    target: number;
    max: number;
    min: number;
  };
  costOptimization: {
    currentCost: number;
    projectedCost: number;
    savings: number;
    efficiency: number;
  };
  scaling: {
    direction: 'up' | 'down' | 'stable';
    reason: string;
    confidence: number;
    eta: string;
  };
  createdAt: string;
  lastOptimized: string;
}

interface ScalingRule {
  id: string;
  name: string;
  nodeType: string;
  trigger: 'cpu' | 'memory' | 'latency' | 'throughput' | 'cost' | 'demand';
  threshold: number;
  action: 'scale_up' | 'scale_down' | 'optimize' | 'migrate' | 'rebalance';
  priority: 'low' | 'medium' | 'high' | 'critical';
  isActive: boolean;
  aiDriven: boolean;
  lastTriggered: string;
  effectiveness: number;
}

interface CostOptimization {
  id: string;
  nodeId: string;
  optimization: string;
  type: 'rightsizing' | 'spot_instances' | 'reserved_capacity' | 'region_migration' | 'efficiency';
  impact: 'low' | 'medium' | 'high';
  savingsPercentage: number;
  savingsAmount: number;
  implementationTime: string;
  riskLevel: 'low' | 'medium' | 'high';
  aiRecommendation: boolean;
  status: 'pending' | 'implementing' | 'completed' | 'monitoring';
  createdAt: string;
}

interface PerformanceMetric {
  timestamp: string;
  cpu: number;
  memory: number;
  network: number;
  latency: number;
  throughput: number;
  cost: number;
  efficiency: number;
}

interface PredictiveInsight {
  id: string;
  type: 'scaling_prediction' | 'cost_optimization' | 'performance_bottleneck' | 'capacity_planning';
  prediction: string;
  confidence: number;
  timeframe: string;
  impact: 'low' | 'medium' | 'high' | 'critical';
  recommendation: string;
  estimatedBenefit: string;
  createdAt: string;
}

// Mock data generation functions
const generateInfrastructureNodes = (): InfrastructureNode[] => {
  const types = ['compute', 'storage', 'network', 'database', 'cache', 'queue', 'api', 'cdn'] as const;
  const regions = ['us-east-1', 'us-west-2', 'eu-west-1', 'ap-southeast-1', 'ap-northeast-1'];
  const providers = ['AWS', 'Azure', 'GCP', 'DigitalOcean'];
  const statuses = ['healthy', 'warning', 'critical', 'scaling', 'optimizing'] as const;

  return Array.from({ length: 24 }, (_, i) => ({
    id: `node-${i + 1}`,
    name: `${types[i % types.length]}-${Math.floor(i / types.length) + 1}`,
    type: types[i % types.length],
    region: regions[i % regions.length],
    provider: providers[i % providers.length],
    status: statuses[i % statuses.length],
    metrics: {
      cpu: Math.random() * 100,
      memory: Math.random() * 100,
      storage: Math.random() * 100,
      network: Math.random() * 100,
      latency: Math.random() * 200,
      errorRate: Math.random() * 5,
      throughput: Math.random() * 1000
    },
    capacity: {
      current: Math.random() * 80 + 20,
      target: Math.random() * 30 + 70,
      max: 100,
      min: 10
    },
    costOptimization: {
      currentCost: Math.random() * 500 + 100,
      projectedCost: Math.random() * 400 + 80,
      savings: Math.random() * 100 + 20,
      efficiency: Math.random() * 40 + 60
    },
    scaling: {
      direction: ['up', 'down', 'stable'][i % 3] as 'up' | 'down' | 'stable',
      reason: [
        'High CPU utilization detected',
        'Cost optimization opportunity',
        'Predicted traffic increase',
        'Memory pressure threshold reached',
        'Latency degradation observed'
      ][i % 5],
      confidence: Math.random() * 30 + 70,
      eta: `${Math.floor(Math.random() * 30) + 5} minutes`
    },
    createdAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
    lastOptimized: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000).toISOString()
  }));
};

const generateScalingRules = (): ScalingRule[] => {
  const triggers = ['cpu', 'memory', 'latency', 'throughput', 'cost', 'demand'] as const;
  const actions = ['scale_up', 'scale_down', 'optimize', 'migrate', 'rebalance'] as const;
  const priorities = ['low', 'medium', 'high', 'critical'] as const;

  return Array.from({ length: 12 }, (_, i) => ({
    id: `rule-${i + 1}`,
    name: `Auto Scale ${triggers[i % triggers.length]} Rule ${i + 1}`,
    nodeType: ['compute', 'database', 'cache', 'api'][i % 4],
    trigger: triggers[i % triggers.length],
    threshold: Math.random() * 50 + 50,
    action: actions[i % actions.length],
    priority: priorities[i % priorities.length],
    isActive: Math.random() > 0.3,
    aiDriven: Math.random() > 0.4,
    lastTriggered: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
    effectiveness: Math.random() * 30 + 70
  }));
};

const generateCostOptimizations = (): CostOptimization[] => {
  const types = ['rightsizing', 'spot_instances', 'reserved_capacity', 'region_migration', 'efficiency'] as const;
  const impacts = ['low', 'medium', 'high'] as const;
  const risks = ['low', 'medium', 'high'] as const;
  const statuses = ['pending', 'implementing', 'completed', 'monitoring'] as const;

  return Array.from({ length: 16 }, (_, i) => ({
    id: `optimization-${i + 1}`,
    nodeId: `node-${(i % 24) + 1}`,
    optimization: [
      'Resize instance to t3.medium',
      'Switch to spot instances',
      'Purchase reserved capacity',
      'Migrate to cheaper region',
      'Optimize storage tier',
      'Enable auto-scaling',
      'Implement caching layer',
      'Optimize database queries'
    ][i % 8],
    type: types[i % types.length],
    impact: impacts[i % impacts.length],
    savingsPercentage: Math.random() * 40 + 10,
    savingsAmount: Math.random() * 200 + 50,
    implementationTime: `${Math.floor(Math.random() * 24) + 1} hours`,
    riskLevel: risks[i % risks.length],
    aiRecommendation: Math.random() > 0.3,
    status: statuses[i % statuses.length],
    createdAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString()
  }));
};

const generatePerformanceData = (): PerformanceMetric[] => {
  return Array.from({ length: 24 }, (_, i) => ({
    timestamp: new Date(Date.now() - (23 - i) * 60 * 60 * 1000).toISOString().split('T')[1].split(':')[0] + ':00',
    cpu: Math.random() * 40 + 30 + Math.sin(i / 4) * 20,
    memory: Math.random() * 30 + 40 + Math.cos(i / 6) * 15,
    network: Math.random() * 50 + 25 + Math.sin(i / 3) * 25,
    latency: Math.random() * 30 + 50 + Math.cos(i / 5) * 20,
    throughput: Math.random() * 200 + 300 + Math.sin(i / 4) * 100,
    cost: Math.random() * 20 + 80 + Math.cos(i / 8) * 10,
    efficiency: Math.random() * 20 + 70 + Math.sin(i / 6) * 15
  }));
};

const generatePredictiveInsights = (): PredictiveInsight[] => {
  const types = ['scaling_prediction', 'cost_optimization', 'performance_bottleneck', 'capacity_planning'] as const;
  const impacts = ['low', 'medium', 'high', 'critical'] as const;

  return Array.from({ length: 8 }, (_, i) => ({
    id: `insight-${i + 1}`,
    type: types[i % types.length],
    prediction: [
      'Traffic will increase by 150% during Black Friday',
      'Database performance will degrade in 3 days',
      'Storage costs can be reduced by 35%',
      'API latency will spike during peak hours',
      'Memory usage trending upward',
      'Network bandwidth approaching limits',
      'Cache hit rate declining',
      'Scaling trigger threshold suboptimal'
    ][i],
    confidence: Math.random() * 30 + 70,
    timeframe: ['2 hours', '6 hours', '1 day', '3 days', '1 week'][i % 5],
    impact: impacts[i % impacts.length],
    recommendation: [
      'Pre-scale compute instances',
      'Optimize database indexes',
      'Implement storage tiering',
      'Add API rate limiting',
      'Increase memory allocation',
      'Upgrade network capacity',
      'Tune cache configuration',
      'Adjust scaling thresholds'
    ][i],
    estimatedBenefit: `$${Math.floor(Math.random() * 500 + 100)}/month`,
    createdAt: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000).toISOString()
  }));
};

const SelfOptimizingInfrastructure: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedNode, setSelectedNode] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');

  // Generate mock data
  const infrastructureNodes = useMemo(() => generateInfrastructureNodes(), []);
  const scalingRules = useMemo(() => generateScalingRules(), []);
  const costOptimizations = useMemo(() => generateCostOptimizations(), []);
  const performanceData = useMemo(() => generatePerformanceData(), []);
  const predictiveInsights = useMemo(() => generatePredictiveInsights(), []);

  // Filter and search logic
  const filteredNodes = useMemo(() => {
    return infrastructureNodes.filter(node => {
      const matchesSearch = node.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          node.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          node.region.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesStatus = statusFilter === 'all' || node.status === statusFilter;
      const matchesType = typeFilter === 'all' || node.type === typeFilter;
      return matchesSearch && matchesStatus && matchesType;
    });
  }, [infrastructureNodes, searchTerm, statusFilter, typeFilter]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'bg-green-500';
      case 'warning': return 'bg-yellow-500';
      case 'critical': return 'bg-red-500';
      case 'scaling': return 'bg-blue-500';
      case 'optimizing': return 'bg-purple-500';
      default: return 'bg-gray-500';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'compute': return <Cpu className="h-4 w-4" />;
      case 'storage': return <HardDrive className="h-4 w-4" />;
      case 'network': return <Network className="h-4 w-4" />;
      case 'database': return <Database className="h-4 w-4" />;
      case 'cache': return <MemoryStick className="h-4 w-4" />;
      case 'queue': return <GitBranch className="h-4 w-4" />;
      case 'api': return <Zap className="h-4 w-4" />;
      case 'cdn': return <Globe className="h-4 w-4" />;
      default: return <Server className="h-4 w-4" />;
    }
  };

  const getScalingDirection = (direction: string) => {
    switch (direction) {
      case 'up': return <TrendingUp className="h-4 w-4 text-green-500" />;
      case 'down': return <TrendingDown className="h-4 w-4 text-red-500" />;
      default: return <Activity className="h-4 w-4 text-blue-500" />;
    }
  };

  return (
    <div className="p-6 space-y-6 bg-gray-50 min-h-screen">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Self-Optimizing Infrastructure</h1>
          <p className="text-gray-600 mt-2">Autonomous resource management with predictive scaling and cost optimization</p>
        </div>
        <div className="flex gap-3">
          <Button variant="outline" className="flex items-center gap-2">
            <RefreshCw className="h-4 w-4" />
            Refresh
          </Button>
          <Button className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Configure
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="nodes">Infrastructure Nodes</TabsTrigger>
          <TabsTrigger value="scaling">Scaling Rules</TabsTrigger>
          <TabsTrigger value="optimization">Cost Optimization</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="insights">Predictive Insights</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Nodes</p>
                    <p className="text-3xl font-bold text-gray-900">{infrastructureNodes.length}</p>
                  </div>
                  <Server className="h-8 w-8 text-blue-500" />
                </div>
                <div className="mt-4 flex items-center gap-2">
                  <Badge variant="secondary" className="flex items-center gap-1">
                    <CheckCircle className="h-3 w-3" />
                    {infrastructureNodes.filter(n => n.status === 'healthy').length} Healthy
                  </Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Cost Savings</p>
                    <p className="text-3xl font-bold text-green-600">
                      ${Math.floor(costOptimizations.reduce((sum, opt) => sum + opt.savingsAmount, 0))}
                    </p>
                  </div>
                  <DollarSign className="h-8 w-8 text-green-500" />
                </div>
                <div className="mt-4">
                  <Badge variant="secondary">
                    {Math.floor(costOptimizations.reduce((sum, opt) => sum + opt.savingsPercentage, 0) / costOptimizations.length)}% Average
                  </Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Active Scaling</p>
                    <p className="text-3xl font-bold text-blue-600">
                      {infrastructureNodes.filter(n => n.status === 'scaling').length}
                    </p>
                  </div>
                  <TrendingUp className="h-8 w-8 text-blue-500" />
                </div>
                <div className="mt-4">
                  <Badge variant="secondary">
                    {scalingRules.filter(r => r.isActive).length} Rules Active
                  </Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Efficiency Score</p>
                    <p className="text-3xl font-bold text-purple-600">
                      {Math.floor(infrastructureNodes.reduce((sum, n) => sum + n.costOptimization.efficiency, 0) / infrastructureNodes.length)}%
                    </p>
                  </div>
                  <Gauge className="h-8 w-8 text-purple-500" />
                </div>
                <div className="mt-4">
                  <Badge variant="secondary" className="flex items-center gap-1">
                    <Brain className="h-3 w-3" />
                    AI Optimized
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Resource Utilization
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={performanceData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="timestamp" />
                    <YAxis />
                    <Tooltip />
                    <Area type="monotone" dataKey="cpu" stackId="1" stroke="#8884d8" fill="#8884d8" />
                    <Area type="monotone" dataKey="memory" stackId="1" stroke="#82ca9d" fill="#82ca9d" />
                    <Area type="monotone" dataKey="network" stackId="1" stroke="#ffc658" fill="#ffc658" />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <LineChart className="h-5 w-5" />
                  Cost vs Efficiency Trend
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <RechartsLineChart data={performanceData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="timestamp" />
                    <YAxis />
                    <Tooltip />
                    <Line type="monotone" dataKey="cost" stroke="#ff7300" strokeWidth={2} />
                    <Line type="monotone" dataKey="efficiency" stroke="#00ff00" strokeWidth={2} />
                  </RechartsLineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="nodes" className="space-y-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search infrastructure nodes..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex gap-2">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md"
              >
                <option value="all">All Status</option>
                <option value="healthy">Healthy</option>
                <option value="warning">Warning</option>
                <option value="critical">Critical</option>
                <option value="scaling">Scaling</option>
                <option value="optimizing">Optimizing</option>
              </select>
              <select
                value={typeFilter}
                onChange={(e) => setTypeFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md"
              >
                <option value="all">All Types</option>
                <option value="compute">Compute</option>
                <option value="storage">Storage</option>
                <option value="network">Network</option>
                <option value="database">Database</option>
                <option value="cache">Cache</option>
                <option value="queue">Queue</option>
                <option value="api">API</option>
                <option value="cdn">CDN</option>
              </select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <AnimatePresence>
              {filteredNodes.map((node) => (
                <motion.div
                  key={node.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.2 }}
                >
                  <Card className="hover:shadow-lg transition-shadow">
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          {getTypeIcon(node.type)}
                          <CardTitle className="text-lg">{node.name}</CardTitle>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className={`w-3 h-3 rounded-full ${getStatusColor(node.status)}`} />
                          {getScalingDirection(node.scaling.direction)}
                        </div>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <Badge variant="outline">{node.provider}</Badge>
                        <Badge variant="outline">{node.region}</Badge>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <p className="text-gray-600">CPU</p>
                          <p className="font-semibold">{Math.floor(node.metrics.cpu)}%</p>
                        </div>
                        <div>
                          <p className="text-gray-600">Memory</p>
                          <p className="font-semibold">{Math.floor(node.metrics.memory)}%</p>
                        </div>
                        <div>
                          <p className="text-gray-600">Cost</p>
                          <p className="font-semibold">${Math.floor(node.costOptimization.currentCost)}</p>
                        </div>
                        <div>
                          <p className="text-gray-600">Efficiency</p>
                          <p className="font-semibold">{Math.floor(node.costOptimization.efficiency)}%</p>
                        </div>
                      </div>

                      <div className="pt-2 border-t">
                        <div className="flex items-center gap-2 text-sm">
                          <Brain className="h-4 w-4 text-purple-500" />
                          <span className="text-gray-600">{node.scaling.reason}</span>
                        </div>
                        <div className="flex items-center gap-2 text-sm mt-1">
                          <Clock className="h-4 w-4 text-gray-400" />
                          <span className="text-gray-600">ETA: {node.scaling.eta}</span>
                        </div>
                      </div>

                      <div className="flex gap-2">
                        <Button variant="outline" size="sm" className="flex-1">
                          <Eye className="h-4 w-4 mr-1" />
                          Details
                        </Button>
                        <Button variant="outline" size="sm" className="flex-1">
                          <Settings className="h-4 w-4 mr-1" />
                          Configure
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        </TabsContent>

        <TabsContent value="scaling" className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold">Scaling Rules</h2>
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Add Rule
            </Button>
          </div>

          <div className="grid gap-4">
            {scalingRules.map((rule) => (
              <Card key={rule.id}>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3">
                        <h3 className="text-lg font-semibold">{rule.name}</h3>
                        <Badge variant={rule.isActive ? "default" : "secondary"}>
                          {rule.isActive ? "Active" : "Inactive"}
                        </Badge>
                        {rule.aiDriven && (
                          <Badge variant="outline" className="flex items-center gap-1">
                            <Brain className="h-3 w-3" />
                            AI-Driven
                          </Badge>
                        )}
                        <Badge variant="outline" className={
                          rule.priority === 'critical' ? 'border-red-500 text-red-700' :
                          rule.priority === 'high' ? 'border-orange-500 text-orange-700' :
                          rule.priority === 'medium' ? 'border-yellow-500 text-yellow-700' :
                          'border-gray-500 text-gray-700'
                        }>
                          {rule.priority}
                        </Badge>
                      </div>
                      <div className="mt-2 grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <p className="text-gray-600">Node Type</p>
                          <p className="font-medium">{rule.nodeType}</p>
                        </div>
                        <div>
                          <p className="text-gray-600">Trigger</p>
                          <p className="font-medium">{rule.trigger} &gt; {Math.floor(rule.threshold)}%</p>
                        </div>
                        <div>
                          <p className="text-gray-600">Action</p>
                          <p className="font-medium">{rule.action.replace('_', ' ')}</p>
                        </div>
                        <div>
                          <p className="text-gray-600">Effectiveness</p>
                          <p className="font-medium">{Math.floor(rule.effectiveness)}%</p>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm">
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="optimization" className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold">Cost Optimizations</h2>
            <Button className="flex items-center gap-2">
              <Brain className="h-4 w-4" />
              Generate AI Recommendations
            </Button>
          </div>

          <div className="grid gap-4">
            {costOptimizations.map((optimization) => (
              <Card key={optimization.id}>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3">
                        <h3 className="text-lg font-semibold">{optimization.optimization}</h3>
                        <Badge variant={
                          optimization.status === 'completed' ? 'default' :
                          optimization.status === 'implementing' ? 'secondary' :
                          optimization.status === 'monitoring' ? 'outline' :
                          'secondary'
                        }>
                          {optimization.status}
                        </Badge>
                        {optimization.aiRecommendation && (
                          <Badge variant="outline" className="flex items-center gap-1">
                            <Brain className="h-3 w-3" />
                            AI Recommended
                          </Badge>
                        )}
                        <Badge variant="outline" className={
                          optimization.impact === 'high' ? 'border-green-500 text-green-700' :
                          optimization.impact === 'medium' ? 'border-yellow-500 text-yellow-700' :
                          'border-gray-500 text-gray-700'
                        }>
                          {optimization.impact} impact
                        </Badge>
                      </div>
                      <div className="mt-2 grid grid-cols-1 md:grid-cols-5 gap-4 text-sm">
                        <div>
                          <p className="text-gray-600">Type</p>
                          <p className="font-medium">{optimization.type.replace('_', ' ')}</p>
                        </div>
                        <div>
                          <p className="text-gray-600">Savings</p>
                          <p className="font-medium text-green-600">
                            ${Math.floor(optimization.savingsAmount)} ({Math.floor(optimization.savingsPercentage)}%)
                          </p>
                        </div>
                        <div>
                          <p className="text-gray-600">Implementation</p>
                          <p className="font-medium">{optimization.implementationTime}</p>
                        </div>
                        <div>
                          <p className="text-gray-600">Risk Level</p>
                          <p className={`font-medium ${
                            optimization.riskLevel === 'high' ? 'text-red-600' :
                            optimization.riskLevel === 'medium' ? 'text-yellow-600' :
                            'text-green-600'
                          }`}>
                            {optimization.riskLevel}
                          </p>
                        </div>
                        <div>
                          <p className="text-gray-600">Node</p>
                          <p className="font-medium">{optimization.nodeId}</p>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {optimization.status === 'pending' && (
                        <Button size="sm" className="flex items-center gap-1">
                          <Play className="h-4 w-4" />
                          Implement
                        </Button>
                      )}
                      <Button variant="outline" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <LineChart className="h-5 w-5" />
                  Performance Metrics
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <RechartsLineChart data={performanceData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="timestamp" />
                    <YAxis />
                    <Tooltip />
                    <Line type="monotone" dataKey="cpu" stroke="#8884d8" strokeWidth={2} />
                    <Line type="monotone" dataKey="memory" stroke="#82ca9d" strokeWidth={2} />
                    <Line type="monotone" dataKey="latency" stroke="#ffc658" strokeWidth={2} />
                  </RechartsLineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Throughput Analysis
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <RechartsBarChart data={performanceData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="timestamp" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="throughput" fill="#8884d8" />
                  </RechartsBarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <PieChart className="h-5 w-5" />
                Resource Distribution
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                {['compute', 'storage', 'network', 'database'].map((type) => {
                  const nodes = infrastructureNodes.filter(n => n.type === type);
                  const avgUtilization = nodes.reduce((sum, n) => sum + n.metrics.cpu, 0) / nodes.length;
                  return (
                    <div key={type} className="text-center">
                      <div className="flex items-center justify-center mb-2">
                        {getTypeIcon(type)}
                      </div>
                      <p className="text-lg font-semibold">{Math.floor(avgUtilization)}%</p>
                      <p className="text-sm text-gray-600 capitalize">{type}</p>
                      <p className="text-xs text-gray-500">{nodes.length} nodes</p>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="insights" className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold">Predictive Insights</h2>
            <Button className="flex items-center gap-2">
              <Brain className="h-4 w-4" />
              Generate Insights
            </Button>
          </div>

          <div className="grid gap-4">
            {predictiveInsights.map((insight) => (
              <Card key={insight.id}>
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <Badge variant="outline" className="flex items-center gap-1">
                          <Brain className="h-3 w-3" />
                          {insight.type.replace('_', ' ')}
                        </Badge>
                        <Badge variant={
                          insight.impact === 'critical' ? 'destructive' :
                          insight.impact === 'high' ? 'default' :
                          insight.impact === 'medium' ? 'secondary' :
                          'outline'
                        }>
                          {insight.impact} impact
                        </Badge>
                        <Badge variant="outline">{Math.floor(insight.confidence)}% confidence</Badge>
                      </div>
                      <h3 className="text-lg font-semibold mb-2">{insight.prediction}</h3>
                      <p className="text-gray-600 mb-3">{insight.recommendation}</p>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div>
                          <p className="text-gray-600">Timeframe</p>
                          <p className="font-medium">{insight.timeframe}</p>
                        </div>
                        <div>
                          <p className="text-gray-600">Estimated Benefit</p>
                          <p className="font-medium text-green-600">{insight.estimatedBenefit}</p>
                        </div>
                        <div>
                          <p className="text-gray-600">Generated</p>
                          <p className="font-medium">{new Date(insight.createdAt).toLocaleDateString()}</p>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button size="sm" className="flex items-center gap-1">
                        <CheckCircle className="h-4 w-4" />
                        Accept
                      </Button>
                      <Button variant="outline" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default SelfOptimizingInfrastructure;