/**
 * SOC 2 Compliance Dashboard
 * Comprehensive security controls, audit trails, and compliance monitoring
 */

import React, { useState, useMemo, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { motion } from 'framer-motion';
import { 
  Shield, 
  Lock, 
  Eye, 
  FileText, 
  AlertTriangle,
  CheckCircle,
  Clock,
  Users,
  Database,
  Server,
  Key,
  Download,
  Upload,
  Search,
  Filter,
  Calendar,
  Activity,
  Zap,
  Settings,
  UserCheck,
  Globe,
  Fingerprint,
  Monitor,
  HardDrive,
  Network,
  Cpu,
  Wifi,
  ShieldCheck,
  ShieldAlert,
  Target,
  TrendingUp,
  BarChart3,
  <PERSON><PERSON>hart,
  LineChart,
  AlertCircle,
  Info,
  CheckSquare,
  XCircle,
  RefreshCw,
  ExternalLink
} from 'lucide-react';
import { 
  <PERSON><PERSON>hart as RechartsLineChart, 
  Line, 
  AreaChart, 
  Area, 
  BarChart, 
  Bar, 
  Pie<PERSON>hart as RechartsPieChart,
  Pie,
  Cell,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer
} from 'recharts';

// Types
interface ComplianceControl {
  id: string;
  category: 'security' | 'availability' | 'processing_integrity' | 'confidentiality' | 'privacy';
  name: string;
  description: string;
  requirement: string;
  status: 'compliant' | 'non_compliant' | 'in_progress' | 'not_tested';
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  owner: string;
  lastAssessed: Date;
  nextAssessment: Date;
  evidence: string[];
  remediation?: string;
  automatedCheck: boolean;
  frequency: 'continuous' | 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'annually';
}

interface SecurityEvent {
  id: string;
  timestamp: Date;
  type: 'access' | 'authentication' | 'authorization' | 'data_access' | 'system_change' | 'security_incident';
  severity: 'low' | 'medium' | 'high' | 'critical';
  user: string;
  action: string;
  resource: string;
  ipAddress: string;
  userAgent: string;
  outcome: 'success' | 'failure' | 'blocked';
  details: Record<string, any>;
  investigated: boolean;
  incidentId?: string;
}

interface DataProcessingRecord {
  id: string;
  timestamp: Date;
  dataType: string;
  purpose: string;
  dataSubjects: number;
  processingActivity: string;
  legalBasis: string;
  retentionPeriod: string;
  dataLocation: string;
  encryptionStatus: boolean;
  accessLog: string[];
}

interface AuditReport {
  id: string;
  reportType: 'soc2_type1' | 'soc2_type2' | 'internal' | 'penetration_test' | 'vulnerability_scan';
  status: 'scheduled' | 'in_progress' | 'completed' | 'failed';
  auditor: string;
  startDate: Date;
  endDate?: Date;
  scope: string[];
  findings: AuditFinding[];
  score?: number;
  certificationExpiry?: Date;
}

interface AuditFinding {
  id: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  category: string;
  description: string;
  recommendation: string;
  status: 'open' | 'in_progress' | 'resolved' | 'accepted_risk';
  assignee: string;
  dueDate: Date;
}

interface ComplianceMetrics {
  overallScore: number;
  controlsCompliant: number;
  totalControls: number;
  riskScore: number;
  incidentsThisMonth: number;
  securityEventsToday: number;
  certificationStatus: 'valid' | 'expiring' | 'expired' | 'pending';
  lastAuditDate: Date;
  nextAuditDate: Date;
}

interface SOC2DashboardProps {
  onControlUpdate?: (controlId: string, updates: Partial<ComplianceControl>) => void;
  onEventInvestigate?: (eventId: string) => void;
  onAuditSchedule?: (auditType: string) => void;
  className?: string;
}

// Mock data generation
const generateComplianceControls = (): ComplianceControl[] => [
  {
    id: 'cc1.1',
    category: 'security',
    name: 'Security Policies and Procedures',
    description: 'Organization has implemented comprehensive security policies and procedures',
    requirement: 'Document and communicate security policies to all personnel',
    status: 'compliant',
    riskLevel: 'high',
    owner: 'CISO',
    lastAssessed: new Date('2024-06-01'),
    nextAssessment: new Date('2024-12-01'),
    evidence: ['Security Policy v2.1', 'Training Records', 'Acknowledgment Forms'],
    automatedCheck: false,
    frequency: 'annually'
  },
  {
    id: 'cc2.1',
    category: 'security',
    name: 'Multi-Factor Authentication',
    description: 'All user accounts require multi-factor authentication',
    requirement: 'Implement MFA for all user access to systems',
    status: 'compliant',
    riskLevel: 'high',
    owner: 'IT Security',
    lastAssessed: new Date('2024-06-15'),
    nextAssessment: new Date('2024-07-15'),
    evidence: ['MFA Configuration Report', 'User Enrollment Status'],
    automatedCheck: true,
    frequency: 'daily'
  },
  {
    id: 'cc3.1',
    category: 'security',
    name: 'Data Encryption at Rest',
    description: 'All sensitive data is encrypted when stored',
    requirement: 'Encrypt all sensitive data using AES-256 or equivalent',
    status: 'compliant',
    riskLevel: 'critical',
    owner: 'Data Engineering',
    lastAssessed: new Date('2024-06-10'),
    nextAssessment: new Date('2024-09-10'),
    evidence: ['Encryption Key Management Report', 'Database Encryption Status'],
    automatedCheck: true,
    frequency: 'weekly'
  },
  {
    id: 'cc4.1',
    category: 'availability',
    name: 'System Backup and Recovery',
    description: 'Regular backups and tested disaster recovery procedures',
    requirement: 'Perform automated backups and test recovery procedures quarterly',
    status: 'in_progress',
    riskLevel: 'medium',
    owner: 'Infrastructure Team',
    lastAssessed: new Date('2024-05-15'),
    nextAssessment: new Date('2024-08-15'),
    evidence: ['Backup Logs', 'Recovery Test Results'],
    remediation: 'Complete Q2 disaster recovery test by July 31st',
    automatedCheck: true,
    frequency: 'daily'
  },
  {
    id: 'cc5.1',
    category: 'processing_integrity',
    name: 'Data Quality Controls',
    description: 'Automated data validation and integrity checks',
    requirement: 'Implement controls to ensure data processing accuracy',
    status: 'compliant',
    riskLevel: 'medium',
    owner: 'Data Quality Team',
    lastAssessed: new Date('2024-06-20'),
    nextAssessment: new Date('2024-09-20'),
    evidence: ['Data Validation Reports', 'ETL Process Documentation'],
    automatedCheck: true,
    frequency: 'continuous'
  },
  {
    id: 'cc6.1',
    category: 'confidentiality',
    name: 'Access Control Management',
    description: 'Role-based access controls with regular reviews',
    requirement: 'Implement RBAC and conduct quarterly access reviews',
    status: 'non_compliant',
    riskLevel: 'high',
    owner: 'IAM Team',
    lastAssessed: new Date('2024-06-05'),
    nextAssessment: new Date('2024-07-05'),
    evidence: ['Access Control Matrix', 'Quarterly Review Reports'],
    remediation: 'Complete Q2 access review for all privileged accounts',
    automatedCheck: false,
    frequency: 'quarterly'
  },
  {
    id: 'cc7.1',
    category: 'privacy',
    name: 'Data Subject Rights',
    description: 'Process for handling data subject requests (GDPR compliance)',
    requirement: 'Implement procedures for data subject rights requests',
    status: 'compliant',
    riskLevel: 'medium',
    owner: 'Privacy Officer',
    lastAssessed: new Date('2024-06-12'),
    nextAssessment: new Date('2024-12-12'),
    evidence: ['DSR Process Documentation', 'Response Time Metrics'],
    automatedCheck: false,
    frequency: 'monthly'
  }
];

const generateSecurityEvents = (): SecurityEvent[] => [
  {
    id: 'event-1',
    timestamp: new Date(Date.now() - 30 * 60 * 1000),
    type: 'authentication',
    severity: 'medium',
    user: '<EMAIL>',
    action: 'Failed login attempt',
    resource: 'Admin Console',
    ipAddress: '************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
    outcome: 'blocked',
    details: {
      attemptCount: 3,
      lockoutTriggered: true,
      mfaRequired: true
    },
    investigated: false
  },
  {
    id: 'event-2',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
    type: 'data_access',
    severity: 'low',
    user: '<EMAIL>',
    action: 'Customer data export',
    resource: 'Customer Database',
    ipAddress: '*************',
    userAgent: 'Analytics-Dashboard/1.0',
    outcome: 'success',
    details: {
      recordCount: 1250,
      exportFormat: 'CSV',
      purpose: 'Monthly report generation'
    },
    investigated: true
  },
  {
    id: 'event-3',
    timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),
    type: 'system_change',
    severity: 'high',
    user: '<EMAIL>',
    action: 'Database schema modification',
    resource: 'Production Database',
    ipAddress: '*********',
    userAgent: 'PostgreSQL Admin',
    outcome: 'success',
    details: {
      changeType: 'ALTER TABLE',
      approvalTicket: 'CHG-2024-0567',
      rollbackAvailable: true
    },
    investigated: true
  }
];

const generateAuditReports = (): AuditReport[] => [
  {
    id: 'audit-soc2-2024',
    reportType: 'soc2_type2',
    status: 'completed',
    auditor: 'BigFour Auditing LLP',
    startDate: new Date('2024-01-15'),
    endDate: new Date('2024-06-15'),
    scope: ['Security', 'Availability', 'Processing Integrity'],
    score: 92,
    certificationExpiry: new Date('2025-06-15'),
    findings: [
      {
        id: 'finding-1',
        severity: 'medium',
        category: 'Access Control',
        description: 'Quarterly access reviews not completed for Q1 2024',
        recommendation: 'Implement automated quarterly access review process',
        status: 'in_progress',
        assignee: 'IAM Team',
        dueDate: new Date('2024-07-31')
      },
      {
        id: 'finding-2',
        severity: 'low',
        category: 'Documentation',
        description: 'Security policy version control needs improvement',
        recommendation: 'Implement version control system for policy documents',
        status: 'resolved',
        assignee: 'Compliance Team',
        dueDate: new Date('2024-06-30')
      }
    ]
  },
  {
    id: 'pentest-2024-q2',
    reportType: 'penetration_test',
    status: 'completed',
    auditor: 'CyberSec Testing Inc.',
    startDate: new Date('2024-04-01'),
    endDate: new Date('2024-04-15'),
    scope: ['Web Application', 'API Endpoints', 'Network Infrastructure'],
    score: 89,
    findings: [
      {
        id: 'pentest-finding-1',
        severity: 'high',
        category: 'API Security',
        description: 'Rate limiting not implemented on authentication endpoints',
        recommendation: 'Implement rate limiting to prevent brute force attacks',
        status: 'resolved',
        assignee: 'API Team',
        dueDate: new Date('2024-05-15')
      }
    ]
  }
];

const generateComplianceMetrics = (): ComplianceMetrics => ({
  overallScore: 92,
  controlsCompliant: 6,
  totalControls: 7,
  riskScore: 23,
  incidentsThisMonth: 2,
  securityEventsToday: 45,
  certificationStatus: 'valid',
  lastAuditDate: new Date('2024-06-15'),
  nextAuditDate: new Date('2025-01-15')
});

// Compliance control card component
const ComplianceControlCard: React.FC<{
  control: ComplianceControl;
  onUpdate?: (updates: Partial<ComplianceControl>) => void;
}> = ({ control, onUpdate }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'compliant': return 'bg-green-100 text-green-800';
      case 'non_compliant': return 'bg-red-100 text-red-800';
      case 'in_progress': return 'bg-yellow-100 text-yellow-800';
      case 'not_tested': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'low': return 'text-green-600';
      case 'medium': return 'text-yellow-600';
      case 'high': return 'text-orange-600';
      case 'critical': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'security': return <Shield className="w-5 h-5 text-blue-600" />;
      case 'availability': return <Server className="w-5 h-5 text-green-600" />;
      case 'processing_integrity': return <Database className="w-5 h-5 text-purple-600" />;
      case 'confidentiality': return <Lock className="w-5 h-5 text-orange-600" />;
      case 'privacy': return <Eye className="w-5 h-5 text-pink-600" />;
      default: return <FileText className="w-5 h-5 text-gray-600" />;
    }
  };

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3">
            {getCategoryIcon(control.category)}
            <div>
              <CardTitle className="text-lg">{control.name}</CardTitle>
              <p className="text-sm text-gray-600 mt-1">{control.description}</p>
            </div>
          </div>
          <div className="flex flex-col space-y-1">
            <Badge className={getStatusColor(control.status)}>
              {control.status.replace('_', ' ')}
            </Badge>
            {control.automatedCheck && (
              <Badge variant="outline" className="text-xs">
                Automated
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Risk Level:</span>
              <span className={`ml-2 font-medium ${getRiskColor(control.riskLevel)}`}>
                {control.riskLevel.toUpperCase()}
              </span>
            </div>
            <div>
              <span className="text-gray-600">Owner:</span>
              <span className="ml-2 font-medium">{control.owner}</span>
            </div>
            <div>
              <span className="text-gray-600">Last Assessed:</span>
              <span className="ml-2 font-medium text-xs">
                {control.lastAssessed.toLocaleDateString()}
              </span>
            </div>
            <div>
              <span className="text-gray-600">Frequency:</span>
              <span className="ml-2 font-medium capitalize">{control.frequency}</span>
            </div>
          </div>

          <div>
            <h4 className="font-medium text-sm mb-2">Evidence:</h4>
            <div className="space-y-1">
              {control.evidence.map((evidence, index) => (
                <div key={index} className="flex items-center space-x-2 text-xs">
                  <FileText className="w-3 h-3 text-gray-400" />
                  <span>{evidence}</span>
                </div>
              ))}
            </div>
          </div>

          {control.remediation && (
            <div className="p-3 bg-yellow-50 rounded-lg">
              <h4 className="font-medium text-sm mb-1">Remediation Required:</h4>
              <p className="text-xs text-yellow-800">{control.remediation}</p>
            </div>
          )}

          <div className="flex justify-between items-center pt-3 border-t">
            <span className="text-xs text-gray-500">
              Next Assessment: {control.nextAssessment.toLocaleDateString()}
            </span>
            <div className="flex space-x-2">
              <Button variant="outline" size="sm">
                <Eye className="w-4 h-4" />
              </Button>
              <Button variant="outline" size="sm">
                <Settings className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Security events table
const SecurityEventsTable: React.FC<{
  events: SecurityEvent[];
  onInvestigate?: (eventId: string) => void;
}> = ({ events, onInvestigate }) => {
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'low': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'critical': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getOutcomeIcon = (outcome: string) => {
    switch (outcome) {
      case 'success': return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'failure': return <XCircle className="w-4 h-4 text-red-600" />;
      case 'blocked': return <ShieldAlert className="w-4 h-4 text-orange-600" />;
      default: return <AlertCircle className="w-4 h-4 text-gray-600" />;
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Security Events</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b">
                <th className="text-left p-3 font-medium">Timestamp</th>
                <th className="text-left p-3 font-medium">Type</th>
                <th className="text-left p-3 font-medium">User</th>
                <th className="text-left p-3 font-medium">Action</th>
                <th className="text-center p-3 font-medium">Severity</th>
                <th className="text-center p-3 font-medium">Outcome</th>
                <th className="text-center p-3 font-medium">Status</th>
                <th className="text-center p-3 font-medium">Actions</th>
              </tr>
            </thead>
            <tbody>
              {events.map((event, index) => (
                <motion.tr
                  key={event.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="border-b hover:bg-gray-50"
                >
                  <td className="p-3">
                    <div>
                      <div>{event.timestamp.toLocaleDateString()}</div>
                      <div className="text-gray-500 text-xs">
                        {event.timestamp.toLocaleTimeString()}
                      </div>
                    </div>
                  </td>
                  <td className="p-3">
                    <Badge variant="outline" className="capitalize">
                      {event.type.replace('_', ' ')}
                    </Badge>
                  </td>
                  <td className="p-3">
                    <div>
                      <div className="font-medium">{event.user}</div>
                      <div className="text-gray-500 text-xs">{event.ipAddress}</div>
                    </div>
                  </td>
                  <td className="p-3">
                    <div>
                      <div>{event.action}</div>
                      <div className="text-gray-500 text-xs">{event.resource}</div>
                    </div>
                  </td>
                  <td className="p-3 text-center">
                    <Badge className={getSeverityColor(event.severity)}>
                      {event.severity}
                    </Badge>
                  </td>
                  <td className="p-3 text-center">
                    {getOutcomeIcon(event.outcome)}
                  </td>
                  <td className="p-3 text-center">
                    {event.investigated ? (
                      <CheckSquare className="w-4 h-4 text-green-600 mx-auto" />
                    ) : (
                      <AlertTriangle className="w-4 h-4 text-yellow-600 mx-auto" />
                    )}
                  </td>
                  <td className="p-3 text-center">
                    <div className="flex justify-center space-x-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onInvestigate?.(event.id)}
                      >
                        <Eye className="w-4 h-4" />
                      </Button>
                    </div>
                  </td>
                </motion.tr>
              ))}
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  );
};

// Main component
const SOC2Dashboard: React.FC<SOC2DashboardProps> = ({
  onControlUpdate,
  onEventInvestigate,
  onAuditSchedule,
  className = ''
}) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [searchTerm, setSearchTerm] = useState('');
  
  const controls = useMemo(() => generateComplianceControls(), []);
  const securityEvents = useMemo(() => generateSecurityEvents(), []);
  const auditReports = useMemo(() => generateAuditReports(), []);
  const metrics = useMemo(() => generateComplianceMetrics(), []);

  const filteredControls = useMemo(() => {
    if (!searchTerm) return controls;
    return controls.filter(control =>
      control.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      control.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      control.category.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [controls, searchTerm]);

  const complianceByCategory = useMemo(() => {
    const categories = ['security', 'availability', 'processing_integrity', 'confidentiality', 'privacy'];
    return categories.map(category => {
      const categoryControls = controls.filter(c => c.category === category);
      const compliant = categoryControls.filter(c => c.status === 'compliant').length;
      return {
        category: category.replace('_', ' '),
        compliant,
        total: categoryControls.length,
        percentage: categoryControls.length > 0 ? (compliant / categoryControls.length) * 100 : 0
      };
    });
  }, [controls]);

  const handleControlUpdate = useCallback((controlId: string, updates: Partial<ComplianceControl>) => {
    onControlUpdate?.(controlId, updates);
  }, [onControlUpdate]);

  const handleEventInvestigate = useCallback((eventId: string) => {
    onEventInvestigate?.(eventId);
  }, [onEventInvestigate]);

  return (
    <div className={`space-y-6 ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <ShieldCheck className="w-6 h-6 text-green-600" />
          <h2 className="text-2xl font-bold">SOC 2 Compliance</h2>
          <Badge className="bg-green-100 text-green-800">Phase 9</Badge>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            Export Report
          </Button>
          <Button>
            <Calendar className="w-4 h-4 mr-2" />
            Schedule Audit
          </Button>
        </div>
      </div>

      {/* Compliance Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Compliance Score</p>
                <p className="text-3xl font-bold text-green-600">{metrics.overallScore}%</p>
                <p className="text-xs text-green-600 mt-1">SOC 2 Type II</p>
              </div>
              <Target className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Controls Status</p>
                <p className="text-3xl font-bold text-blue-600">
                  {metrics.controlsCompliant}/{metrics.totalControls}
                </p>
                <p className="text-xs text-blue-600 mt-1">Compliant</p>
              </div>
              <CheckSquare className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Security Events</p>
                <p className="text-3xl font-bold text-purple-600">{metrics.securityEventsToday}</p>
                <p className="text-xs text-purple-600 mt-1">Today</p>
              </div>
              <Activity className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Certification</p>
                <p className="text-2xl font-bold text-green-600">Valid</p>
                <p className="text-xs text-gray-600 mt-1">
                  Expires: {new Date('2025-06-15').toLocaleDateString()}
                </p>
              </div>
              <ShieldCheck className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="overview">Compliance Overview</TabsTrigger>
          <TabsTrigger value="controls">Security Controls</TabsTrigger>
          <TabsTrigger value="events">Security Events</TabsTrigger>
          <TabsTrigger value="audits">Audit Reports</TabsTrigger>
          <TabsTrigger value="privacy">Data Privacy</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Compliance by Category Chart */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Compliance by Category</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={complianceByCategory}>
                      <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                      <XAxis dataKey="category" />
                      <YAxis />
                      <Tooltip />
                      <Bar dataKey="percentage" fill="#10b981" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Recent Security Events</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {securityEvents.slice(0, 5).map((event, index) => (
                    <div key={event.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div>
                        <div className="font-medium text-sm">{event.action}</div>
                        <div className="text-xs text-gray-600">{event.user} • {event.timestamp.toLocaleTimeString()}</div>
                      </div>
                      <Badge className={
                        event.severity === 'high' ? 'bg-red-100 text-red-800' :
                        event.severity === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-green-100 text-green-800'
                      }>
                        {event.severity}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Audit Timeline */}
          <Card>
            <CardHeader>
              <CardTitle>Audit Timeline</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {auditReports.map((report, index) => (
                  <div key={report.id} className="flex items-center space-x-4 p-4 border rounded-lg">
                    <div className={`w-3 h-3 rounded-full ${
                      report.status === 'completed' ? 'bg-green-500' :
                      report.status === 'in_progress' ? 'bg-yellow-500' :
                      'bg-blue-500'
                    }`} />
                    <div className="flex-1">
                      <div className="font-medium">{report.reportType.replace('_', ' ').toUpperCase()}</div>
                      <div className="text-sm text-gray-600">
                        {report.auditor} • {report.startDate.toLocaleDateString()}
                        {report.score && ` • Score: ${report.score}%`}
                      </div>
                    </div>
                    <Badge className={
                      report.status === 'completed' ? 'bg-green-100 text-green-800' :
                      report.status === 'in_progress' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-blue-100 text-blue-800'
                    }>
                      {report.status.replace('_', ' ')}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="controls" className="space-y-6">
          <div className="flex items-center space-x-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search controls..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button variant="outline">
              <Filter className="w-4 h-4 mr-2" />
              Filter
            </Button>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {filteredControls.map((control, index) => (
              <motion.div
                key={control.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <ComplianceControlCard
                  control={control}
                  onUpdate={(updates) => handleControlUpdate(control.id, updates)}
                />
              </motion.div>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="events" className="space-y-6">
          <SecurityEventsTable
            events={securityEvents}
            onInvestigate={handleEventInvestigate}
          />
        </TabsContent>

        <TabsContent value="audits" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {auditReports.map((report, index) => (
              <motion.div
                key={report.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="capitalize">
                        {report.reportType.replace('_', ' ')}
                      </CardTitle>
                      <Badge className={
                        report.status === 'completed' ? 'bg-green-100 text-green-800' :
                        report.status === 'in_progress' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-blue-100 text-blue-800'
                      }>
                        {report.status.replace('_', ' ')}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-gray-600">Auditor:</span>
                          <span className="ml-2 font-medium">{report.auditor}</span>
                        </div>
                        <div>
                          <span className="text-gray-600">Start Date:</span>
                          <span className="ml-2 font-medium">{report.startDate.toLocaleDateString()}</span>
                        </div>
                        {report.score && (
                          <div>
                            <span className="text-gray-600">Score:</span>
                            <span className="ml-2 font-medium text-green-600">{report.score}%</span>
                          </div>
                        )}
                        {report.certificationExpiry && (
                          <div>
                            <span className="text-gray-600">Expires:</span>
                            <span className="ml-2 font-medium">{report.certificationExpiry.toLocaleDateString()}</span>
                          </div>
                        )}
                      </div>

                      {report.findings.length > 0 && (
                        <div>
                          <h4 className="font-medium text-sm mb-2">Findings ({report.findings.length}):</h4>
                          <div className="space-y-2">
                            {report.findings.slice(0, 3).map((finding) => (
                              <div key={finding.id} className="p-3 bg-gray-50 rounded-lg">
                                <div className="flex items-center justify-between mb-1">
                                  <span className="text-sm font-medium">{finding.category}</span>
                                  <Badge className={
                                    finding.severity === 'high' ? 'bg-red-100 text-red-800' :
                                    finding.severity === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                                    'bg-green-100 text-green-800'
                                  }>
                                    {finding.severity}
                                  </Badge>
                                </div>
                                <p className="text-xs text-gray-600">{finding.description}</p>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      <div className="pt-3 border-t">
                        <Button variant="outline" className="w-full">
                          <ExternalLink className="w-4 h-4 mr-2" />
                          View Full Report
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="privacy" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Data Processing Activities</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="p-4 border rounded-lg">
                    <h4 className="font-semibold mb-2">Customer Analytics</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>Data Subjects:</span>
                        <span>14,287 customers</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Legal Basis:</span>
                        <span>Legitimate Interest</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Retention:</span>
                        <span>7 years</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Location:</span>
                        <span>EU, US</span>
                      </div>
                    </div>
                  </div>

                  <div className="p-4 border rounded-lg">
                    <h4 className="font-semibold mb-2">Marketing Communications</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>Data Subjects:</span>
                        <span>8,456 subscribers</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Legal Basis:</span>
                        <span>Consent</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Retention:</span>
                        <span>Until withdrawal</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Location:</span>
                        <span>EU</span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Data Subject Rights</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-4 bg-blue-50 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">23</div>
                      <div className="text-sm text-blue-600">Access Requests</div>
                      <div className="text-xs text-gray-600">This month</div>
                    </div>
                    <div className="text-center p-4 bg-green-50 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">8</div>
                      <div className="text-sm text-green-600">Deletion Requests</div>
                      <div className="text-xs text-gray-600">This month</div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Average Response Time:</span>
                      <span className="font-medium">2.3 days</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Compliance Rate:</span>
                      <span className="font-medium text-green-600">98.2%</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>SLA Target:</span>
                      <span className="font-medium">30 days</span>
                    </div>
                  </div>

                  <Button variant="outline" className="w-full">
                    <FileText className="w-4 h-4 mr-2" />
                    Process DSR
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default SOC2Dashboard;
export { type ComplianceControl, type SecurityEvent, type AuditReport, type ComplianceMetrics };