/**
 * Market Basket Analysis & Cross-Sell Optimization
 * Advanced AI-powered product association analysis with intelligent cross-sell recommendations
 */

import React, { useState, useMemo, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { motion } from 'framer-motion';
import { 
  ShoppingCart, 
  Package,
  Target,
  TrendingUp,
  TrendingDown,
  Brain,
  Zap,
  DollarSign,
  Percent,
  ArrowUpRight,
  ArrowDownRight,
  BarChart3,
  LineChart,
  PieChart,
  Activity,
  Eye,
  Settings,
  Download,
  Upload,
  Share,
  Filter,
  Search,
  RefreshCw,
  Play,
  Pause,
  Calendar,
  Clock,
  Users,
  Star,
  Award,
  Lightbulb,
  Database,
  Cpu,
  Network,
  Globe,
  Tag,
  Hash,
  Layers,
  Grid,
  List,
  MoreHorizontal,
  ExternalLink,
  CheckCircle,
  AlertTriangle,
  Info,
  Plus,
  Minus,
  Edit,
  Trash2,
  Copy,
  Heart,
  Bookmark,
  Gift,
  ShoppingBag,
  CreditCard,
  Truck,
  MapPin,
  Building2,
  Home,
  Phone,
  Mail,
  MessageSquare
} from 'lucide-react';
import { 
  LineChart as RechartsLineChart, 
  Line, 
  AreaChart, 
  Area, 
  BarChart, 
  Bar, 
  ScatterChart,
  Scatter,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  Cell,
  PieChart as RechartsPieChart,
  Pie,
  Sankey,
  TreeMap,
  RadialBarChart,
  RadialBar
} from 'recharts';

// Types
interface ProductAssociation {
  productA: Product;
  productB: Product;
  support: number;
  confidence: number;
  lift: number;
  conviction: number;
  kulczynski: number;
  imbalanceRatio: number;
  coOccurrenceCount: number;
  totalTransactions: number;
  ruleStrength: 'weak' | 'moderate' | 'strong' | 'very_strong';
  actionable: boolean;
  revenueImpact: number;
  margin: number;
  seasonality: {
    spring: number;
    summer: number;
    autumn: number;
    winter: number;
  };
  demographics: {
    ageGroups: { [key: string]: number };
    genders: { [key: string]: number };
    locations: { [key: string]: number };
  };
}

interface Product {
  id: string;
  name: string;
  category: string;
  subcategory: string;
  price: number;
  cost: number;
  margin: number;
  brand: string;
  tags: string[];
  popularity: number;
  rating: number;
  reviews: number;
  stock: number;
  image?: string;
}

interface CrossSellRecommendation {
  id: string;
  triggerProduct: Product;
  recommendedProducts: Product[];
  strategy: 'bundle' | 'upgrade' | 'complement' | 'alternative' | 'accessory';
  targeting: {
    customerSegments: string[];
    timing: 'immediate' | 'cart' | 'checkout' | 'post_purchase' | 'follow_up';
    placement: 'product_page' | 'cart' | 'email' | 'homepage' | 'category';
  };
  performance: {
    impressions: number;
    clicks: number;
    conversions: number;
    revenue: number;
    clickThroughRate: number;
    conversionRate: number;
    averageOrderValue: number;
    revenuePerImpression: number;
  };
  abTestResults: {
    enabled: boolean;
    variants: RecommendationVariant[];
    winner: string;
    significance: number;
  };
  constraints: {
    minOrderValue: number;
    maxRecommendations: number;
    excludeCategories: string[];
    inventoryThreshold: number;
  };
  personalization: {
    factorCustomerHistory: boolean;
    factorBrowsingBehavior: boolean;
    factorDemographics: boolean;
    dynamicPricing: boolean;
  };
  expectedLift: {
    revenueIncrease: number;
    conversionIncrease: number;
    basketSizeIncrease: number;
  };
}

interface RecommendationVariant {
  id: string;
  name: string;
  description: string;
  products: Product[];
  presentation: 'list' | 'grid' | 'carousel' | 'popup';
  messaging: string;
  discount: number;
  performance: {
    impressions: number;
    clicks: number;
    conversions: number;
    revenue: number;
  };
}

interface MarketBasket {
  id: string;
  customerId: string;
  products: Product[];
  totalValue: number;
  transactionDate: Date;
  channel: 'online' | 'mobile' | 'store' | 'phone';
  customerSegment: string;
  demographics: {
    age: number;
    gender: string;
    location: string;
    incomeLevel: string;
  };
  context: {
    season: string;
    dayOfWeek: string;
    timeOfDay: string;
    promocode?: string;
    referralSource?: string;
  };
}

interface BundleRecommendation {
  id: string;
  name: string;
  products: Product[];
  bundlePrice: number;
  individualPrice: number;
  discount: number;
  savings: number;
  popularity: number;
  conversionRate: number;
  profitMargin: number;
  targetCustomers: string[];
  seasonality: number;
  inventory: {
    available: boolean;
    constraints: string[];
  };
}

interface CategoryAffinity {
  categoryA: string;
  categoryB: string;
  affinity: number;
  transactions: number;
  avgBasketValue: number;
  topProductPairs: {
    productA: string;
    productB: string;
    frequency: number;
  }[];
}

interface CustomerSegmentAnalysis {
  segment: string;
  totalCustomers: number;
  avgBasketSize: number;
  avgBasketValue: number;
  topCategories: string[];
  crossSellSuccess: number;
  preferredBundles: string[];
  seasonalTrends: {
    [season: string]: {
      basketSize: number;
      categories: string[];
      crossSellRate: number;
    };
  };
}

// Mock data generators
const generateProducts = (): Product[] => {
  const categories = ['Electronics', 'Clothing', 'Home & Garden', 'Sports', 'Books', 'Beauty'];
  const brands = ['Apple', 'Samsung', 'Nike', 'Adidas', 'Amazon', 'Google', 'Sony', 'LG'];
  
  return Array.from({ length: 50 }, (_, index) => ({
    id: `PROD-${String(index + 1).padStart(3, '0')}`,
    name: `Product ${index + 1}`,
    category: categories[Math.floor(Math.random() * categories.length)],
    subcategory: `Subcategory ${Math.floor(Math.random() * 5) + 1}`,
    price: Math.floor(Math.random() * 500) + 20,
    cost: Math.floor(Math.random() * 300) + 10,
    margin: 0.2 + Math.random() * 0.5,
    brand: brands[Math.floor(Math.random() * brands.length)],
    tags: ['popular', 'trending', 'bestseller', 'new'].slice(0, Math.floor(Math.random() * 3) + 1),
    popularity: Math.random(),
    rating: 3.5 + Math.random() * 1.5,
    reviews: Math.floor(Math.random() * 1000) + 10,
    stock: Math.floor(Math.random() * 100) + 10
  }));
};

const generateProductAssociations = (products: Product[]): ProductAssociation[] => {
  const associations: ProductAssociation[] = [];
  
  for (let i = 0; i < 30; i++) {
    const productA = products[Math.floor(Math.random() * products.length)];
    const productB = products[Math.floor(Math.random() * products.length)];
    
    if (productA.id !== productB.id) {
      const support = Math.random() * 0.3 + 0.05;
      const confidence = Math.random() * 0.8 + 0.2;
      const lift = 0.8 + Math.random() * 2.5;
      
      associations.push({
        productA,
        productB,
        support,
        confidence,
        lift,
        conviction: confidence / (1 - confidence),
        kulczynski: Math.random() * 0.8 + 0.2,
        imbalanceRatio: Math.random() * 0.5 + 0.5,
        coOccurrenceCount: Math.floor(Math.random() * 500) + 50,
        totalTransactions: Math.floor(Math.random() * 2000) + 1000,
        ruleStrength: lift > 2 ? 'very_strong' : lift > 1.5 ? 'strong' : lift > 1.2 ? 'moderate' : 'weak',
        actionable: lift > 1.2 && confidence > 0.3,
        revenueImpact: Math.random() * 10000 + 1000,
        margin: Math.random() * 0.4 + 0.1,
        seasonality: {
          spring: Math.random(),
          summer: Math.random(),
          autumn: Math.random(),
          winter: Math.random()
        },
        demographics: {
          ageGroups: {
            '18-25': Math.random(),
            '26-35': Math.random(),
            '36-45': Math.random(),
            '46-55': Math.random(),
            '55+': Math.random()
          },
          genders: {
            'M': Math.random(),
            'F': Math.random(),
            'Other': Math.random()
          },
          locations: {
            'Urban': Math.random(),
            'Suburban': Math.random(),
            'Rural': Math.random()
          }
        }
      });
    }
  }
  
  return associations.sort((a, b) => b.lift - a.lift);
};

const generateCrossSellRecommendations = (products: Product[]): CrossSellRecommendation[] => {
  return Array.from({ length: 15 }, (_, index) => {
    const triggerProduct = products[Math.floor(Math.random() * products.length)];
    const recommendedProducts = products
      .filter(p => p.id !== triggerProduct.id)
      .slice(0, Math.floor(Math.random() * 4) + 2);
    
    return {
      id: `REC-${String(index + 1).padStart(3, '0')}`,
      triggerProduct,
      recommendedProducts,
      strategy: ['bundle', 'upgrade', 'complement', 'alternative', 'accessory'][Math.floor(Math.random() * 5)] as any,
      targeting: {
        customerSegments: ['Premium', 'Standard', 'Budget'].slice(0, Math.floor(Math.random() * 3) + 1),
        timing: ['immediate', 'cart', 'checkout', 'post_purchase', 'follow_up'][Math.floor(Math.random() * 5)] as any,
        placement: ['product_page', 'cart', 'email', 'homepage', 'category'][Math.floor(Math.random() * 5)] as any
      },
      performance: {
        impressions: Math.floor(Math.random() * 10000) + 1000,
        clicks: Math.floor(Math.random() * 1000) + 100,
        conversions: Math.floor(Math.random() * 200) + 20,
        revenue: Math.random() * 50000 + 5000,
        clickThroughRate: 0.05 + Math.random() * 0.15,
        conversionRate: 0.02 + Math.random() * 0.08,
        averageOrderValue: 100 + Math.random() * 300,
        revenuePerImpression: Math.random() * 5 + 1
      },
      abTestResults: {
        enabled: Math.random() > 0.5,
        variants: [],
        winner: '',
        significance: Math.random()
      },
      constraints: {
        minOrderValue: Math.floor(Math.random() * 100) + 25,
        maxRecommendations: Math.floor(Math.random() * 5) + 3,
        excludeCategories: [],
        inventoryThreshold: Math.floor(Math.random() * 20) + 5
      },
      personalization: {
        factorCustomerHistory: Math.random() > 0.5,
        factorBrowsingBehavior: Math.random() > 0.5,
        factorDemographics: Math.random() > 0.5,
        dynamicPricing: Math.random() > 0.5
      },
      expectedLift: {
        revenueIncrease: 0.1 + Math.random() * 0.4,
        conversionIncrease: 0.05 + Math.random() * 0.2,
        basketSizeIncrease: 0.08 + Math.random() * 0.25
      }
    };
  });
};

const generateBundleRecommendations = (products: Product[]): BundleRecommendation[] => {
  return Array.from({ length: 10 }, (_, index) => {
    const bundleProducts = products.slice(0, Math.floor(Math.random() * 4) + 2);
    const individualPrice = bundleProducts.reduce((sum, p) => sum + p.price, 0);
    const discount = 0.1 + Math.random() * 0.3;
    const bundlePrice = individualPrice * (1 - discount);
    
    return {
      id: `BUNDLE-${String(index + 1).padStart(3, '0')}`,
      name: `Bundle ${index + 1}`,
      products: bundleProducts,
      bundlePrice,
      individualPrice,
      discount,
      savings: individualPrice - bundlePrice,
      popularity: Math.random(),
      conversionRate: 0.05 + Math.random() * 0.15,
      profitMargin: 0.2 + Math.random() * 0.4,
      targetCustomers: ['Premium', 'Standard', 'Budget'].slice(0, Math.floor(Math.random() * 3) + 1),
      seasonality: Math.random(),
      inventory: {
        available: Math.random() > 0.2,
        constraints: []
      }
    };
  });
};

const generateCategoryAffinities = (): CategoryAffinity[] => {
  const categories = ['Electronics', 'Clothing', 'Home & Garden', 'Sports', 'Books', 'Beauty'];
  const affinities: CategoryAffinity[] = [];
  
  for (let i = 0; i < categories.length; i++) {
    for (let j = i + 1; j < categories.length; j++) {
      affinities.push({
        categoryA: categories[i],
        categoryB: categories[j],
        affinity: Math.random(),
        transactions: Math.floor(Math.random() * 1000) + 100,
        avgBasketValue: 100 + Math.random() * 300,
        topProductPairs: [
          { productA: 'Product A', productB: 'Product B', frequency: Math.floor(Math.random() * 100) + 10 },
          { productA: 'Product C', productB: 'Product D', frequency: Math.floor(Math.random() * 80) + 8 }
        ]
      });
    }
  }
  
  return affinities.sort((a, b) => b.affinity - a.affinity);
};

// Components
const AssociationCard: React.FC<{ association: ProductAssociation }> = ({ association }) => {
  const getStrengthColor = (strength: string) => {
    switch (strength) {
      case 'very_strong': return 'bg-green-100 text-green-800 border-green-200';
      case 'strong': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'moderate': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="border rounded-lg p-4 hover:shadow-md transition-shadow"
    >
      <div className="flex items-start justify-between mb-3">
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-2">
            <Package className="h-4 w-4 text-blue-600" />
            <span className="font-medium">{association.productA.name}</span>
            <ArrowUpRight className="h-3 w-3 text-gray-400" />
            <span className="font-medium">{association.productB.name}</span>
          </div>
          <div className="flex items-center gap-2">
            <Badge className={getStrengthColor(association.ruleStrength)}>
              {association.ruleStrength.replace('_', ' ').toUpperCase()}
            </Badge>
            {association.actionable && (
              <Badge variant="outline" className="text-green-600">
                Actionable
              </Badge>
            )}
          </div>
        </div>
      </div>

      <div className="grid grid-cols-3 gap-4 mb-3">
        <div>
          <p className="text-sm text-gray-600">Support</p>
          <p className="text-lg font-semibold">{(association.support * 100).toFixed(1)}%</p>
        </div>
        <div>
          <p className="text-sm text-gray-600">Confidence</p>
          <p className="text-lg font-semibold">{(association.confidence * 100).toFixed(1)}%</p>
        </div>
        <div>
          <p className="text-sm text-gray-600">Lift</p>
          <p className="text-lg font-semibold">{association.lift.toFixed(2)}</p>
        </div>
      </div>

      <div className="flex items-center justify-between text-sm">
        <span className="text-gray-600">
          {association.coOccurrenceCount} / {association.totalTransactions} transactions
        </span>
        <span className="text-gray-600">
          Revenue Impact: ${(association.revenueImpact / 1000).toFixed(1)}K
        </span>
      </div>
    </motion.div>
  );
};

const CrossSellCard: React.FC<{ recommendation: CrossSellRecommendation }> = ({ recommendation }) => {
  const getStrategyIcon = (strategy: string) => {
    switch (strategy) {
      case 'bundle': return <Package className="h-4 w-4" />;
      case 'upgrade': return <TrendingUp className="h-4 w-4" />;
      case 'complement': return <Plus className="h-4 w-4" />;
      case 'alternative': return <MoreHorizontal className="h-4 w-4" />;
      default: return <Tag className="h-4 w-4" />;
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className="border rounded-lg p-4 hover:shadow-md transition-shadow"
    >
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-2">
          {getStrategyIcon(recommendation.strategy)}
          <div>
            <h4 className="font-medium">{recommendation.triggerProduct.name}</h4>
            <p className="text-sm text-gray-600 capitalize">{recommendation.strategy} Strategy</p>
          </div>
        </div>
        <Badge variant="outline">{recommendation.targeting.timing}</Badge>
      </div>

      <div className="mb-3">
        <p className="text-sm text-gray-600 mb-1">Recommended Products</p>
        <div className="flex flex-wrap gap-1">
          {recommendation.recommendedProducts.slice(0, 3).map((product, index) => (
            <Badge key={index} variant="secondary" className="text-xs">
              {product.name}
            </Badge>
          ))}
          {recommendation.recommendedProducts.length > 3 && (
            <Badge variant="secondary" className="text-xs">
              +{recommendation.recommendedProducts.length - 3} more
            </Badge>
          )}
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4 mb-3">
        <div>
          <p className="text-sm text-gray-600">Conversion Rate</p>
          <p className="text-lg font-semibold">{(recommendation.performance.conversionRate * 100).toFixed(1)}%</p>
        </div>
        <div>
          <p className="text-sm text-gray-600">Revenue</p>
          <p className="text-lg font-semibold">${(recommendation.performance.revenue / 1000).toFixed(0)}K</p>
        </div>
      </div>

      <div className="flex items-center justify-between text-sm">
        <span className="text-gray-600">
          CTR: {(recommendation.performance.clickThroughRate * 100).toFixed(1)}%
        </span>
        <span className="text-gray-600">
          AOV: ${recommendation.performance.averageOrderValue.toFixed(0)}
        </span>
      </div>
    </motion.div>
  );
};

const BundleCard: React.FC<{ bundle: BundleRecommendation }> = ({ bundle }) => {
  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      className="border rounded-lg p-4 hover:shadow-md transition-shadow"
    >
      <div className="flex items-start justify-between mb-3">
        <div>
          <h4 className="font-medium">{bundle.name}</h4>
          <p className="text-sm text-gray-600">{bundle.products.length} products</p>
        </div>
        <Badge variant={bundle.inventory.available ? "default" : "secondary"}>
          {bundle.inventory.available ? 'Available' : 'Out of Stock'}
        </Badge>
      </div>

      <div className="mb-3">
        <div className="flex items-center gap-2 mb-1">
          <span className="text-2xl font-bold">${bundle.bundlePrice.toFixed(0)}</span>
          <span className="text-lg text-gray-500 line-through">${bundle.individualPrice.toFixed(0)}</span>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="text-green-600">
            {(bundle.discount * 100).toFixed(0)}% OFF
          </Badge>
          <span className="text-sm text-gray-600">
            Save ${bundle.savings.toFixed(0)}
          </span>
        </div>
      </div>

      <div className="mb-3">
        <p className="text-sm text-gray-600 mb-1">Products</p>
        <div className="flex flex-wrap gap-1">
          {bundle.products.map((product, index) => (
            <Badge key={index} variant="secondary" className="text-xs">
              {product.name}
            </Badge>
          ))}
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <p className="text-sm text-gray-600">Conversion</p>
          <p className="text-lg font-semibold">{(bundle.conversionRate * 100).toFixed(1)}%</p>
        </div>
        <div>
          <p className="text-sm text-gray-600">Margin</p>
          <p className="text-lg font-semibold">{(bundle.profitMargin * 100).toFixed(1)}%</p>
        </div>
      </div>
    </motion.div>
  );
};

export const MarketBasketAnalysis: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'lift' | 'confidence' | 'support' | 'revenue'>('lift');
  const [minLift, setMinLift] = useState<number>(1.0);

  const products = useMemo(() => generateProducts(), []);
  const associations = useMemo(() => generateProductAssociations(products), [products]);
  const crossSellRecommendations = useMemo(() => generateCrossSellRecommendations(products), [products]);
  const bundleRecommendations = useMemo(() => generateBundleRecommendations(products), [products]);
  const categoryAffinities = useMemo(() => generateCategoryAffinities(), []);

  const filteredAssociations = useMemo(() => {
    return associations
      .filter(a => a.lift >= minLift)
      .filter(a => selectedCategory === 'all' || 
                  a.productA.category === selectedCategory || 
                  a.productB.category === selectedCategory)
      .filter(a => a.productA.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                  a.productB.name.toLowerCase().includes(searchQuery.toLowerCase()))
      .sort((a, b) => {
        switch (sortBy) {
          case 'lift': return b.lift - a.lift;
          case 'confidence': return b.confidence - a.confidence;
          case 'support': return b.support - a.support;
          case 'revenue': return b.revenueImpact - a.revenueImpact;
          default: return 0;
        }
      });
  }, [associations, selectedCategory, searchQuery, sortBy, minLift]);

  const marketBasketMetrics = useMemo(() => {
    const strongRules = associations.filter(a => a.ruleStrength === 'strong' || a.ruleStrength === 'very_strong');
    const actionableRules = associations.filter(a => a.actionable);
    const totalRevenue = associations.reduce((sum, a) => sum + a.revenueImpact, 0);
    
    return {
      totalRules: associations.length,
      strongRules: strongRules.length,
      actionableRules: actionableRules.length,
      avgLift: associations.reduce((sum, a) => sum + a.lift, 0) / associations.length,
      totalRevenue,
      categories: [...new Set(products.map(p => p.category))].length
    };
  }, [associations, products]);

  const liftDistributionData = useMemo(() => {
    const ranges = [
      { range: '0.8-1.0', min: 0.8, max: 1.0 },
      { range: '1.0-1.2', min: 1.0, max: 1.2 },
      { range: '1.2-1.5', min: 1.2, max: 1.5 },
      { range: '1.5-2.0', min: 1.5, max: 2.0 },
      { range: '2.0+', min: 2.0, max: Infinity }
    ];

    return ranges.map(range => ({
      range: range.range,
      count: associations.filter(a => a.lift >= range.min && a.lift < range.max).length
    }));
  }, [associations]);

  const categoryAffinityData = useMemo(() => {
    return categoryAffinities.slice(0, 10).map(affinity => ({
      name: `${affinity.categoryA} → ${affinity.categoryB}`,
      affinity: affinity.affinity * 100,
      transactions: affinity.transactions,
      value: affinity.avgBasketValue
    }));
  }, [categoryAffinities]);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <ShoppingCart className="h-6 w-6" />
            Market Basket Analysis
          </h2>
          <p className="text-gray-600">AI-powered product association analysis and cross-sell optimization</p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Run Analysis
          </Button>
        </div>
      </div>

      <Tabs defaultValue="associations" className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="associations">Product Associations</TabsTrigger>
          <TabsTrigger value="crosssell">Cross-Sell Engine</TabsTrigger>
          <TabsTrigger value="bundles">Bundle Optimization</TabsTrigger>
          <TabsTrigger value="categories">Category Affinity</TabsTrigger>
          <TabsTrigger value="insights">AI Insights</TabsTrigger>
        </TabsList>

        <TabsContent value="associations">
          <div className="space-y-6">
            {/* Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <BarChart3 className="h-4 w-4 text-blue-600" />
                    <div>
                      <p className="text-sm text-gray-600">Total Rules</p>
                      <p className="text-2xl font-bold">{marketBasketMetrics.totalRules}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Target className="h-4 w-4 text-green-600" />
                    <div>
                      <p className="text-sm text-gray-600">Strong Rules</p>
                      <p className="text-2xl font-bold">{marketBasketMetrics.strongRules}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Zap className="h-4 w-4 text-yellow-600" />
                    <div>
                      <p className="text-sm text-gray-600">Actionable</p>
                      <p className="text-2xl font-bold">{marketBasketMetrics.actionableRules}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <DollarSign className="h-4 w-4 text-purple-600" />
                    <div>
                      <p className="text-sm text-gray-600">Revenue Impact</p>
                      <p className="text-2xl font-bold">${(marketBasketMetrics.totalRevenue / 1000).toFixed(0)}K</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Filters and Search */}
            <div className="flex items-center gap-4">
              <div className="flex-1">
                <Input
                  placeholder="Search products..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full"
                />
              </div>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-3 py-2 border rounded-md"
              >
                <option value="all">All Categories</option>
                {[...new Set(products.map(p => p.category))].map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>
              <div className="flex items-center gap-2">
                <label className="text-sm">Min Lift:</label>
                <Input
                  type="number"
                  value={minLift}
                  onChange={(e) => setMinLift(parseFloat(e.target.value))}
                  className="w-20"
                  min="0"
                  max="5"
                  step="0.1"
                />
              </div>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="px-3 py-2 border rounded-md"
              >
                <option value="lift">Sort by Lift</option>
                <option value="confidence">Sort by Confidence</option>
                <option value="support">Sort by Support</option>
                <option value="revenue">Sort by Revenue</option>
              </select>
            </div>

            {/* Charts */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Lift Distribution</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={liftDistributionData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="range" />
                      <YAxis />
                      <Tooltip />
                      <Bar dataKey="count" fill="#8884d8" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Association Strength</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <RechartsPieChart>
                      <Pie
                        data={[
                          { name: 'Very Strong', value: associations.filter(a => a.ruleStrength === 'very_strong').length },
                          { name: 'Strong', value: associations.filter(a => a.ruleStrength === 'strong').length },
                          { name: 'Moderate', value: associations.filter(a => a.ruleStrength === 'moderate').length },
                          { name: 'Weak', value: associations.filter(a => a.ruleStrength === 'weak').length }
                        ]}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, value }) => `${name}: ${value}`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {[0, 1, 2, 3].map(index => (
                          <Cell
                            key={`cell-${index}`}
                            fill={['#22c55e', '#3b82f6', '#eab308', '#ef4444'][index]}
                          />
                        ))}
                      </Pie>
                      <Tooltip />
                    </RechartsPieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>

            {/* Associations Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredAssociations.map((association, index) => (
                <AssociationCard
                  key={`${association.productA.id}-${association.productB.id}`}
                  association={association}
                />
              ))}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="crosssell">
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Cross-Sell Recommendations</h3>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Create Recommendation
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {crossSellRecommendations.map((recommendation) => (
                <CrossSellCard key={recommendation.id} recommendation={recommendation} />
              ))}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="bundles">
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Bundle Recommendations</h3>
              <Button size="sm">
                <Package className="h-4 w-4 mr-2" />
                Create Bundle
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {bundleRecommendations.map((bundle) => (
                <BundleCard key={bundle.id} bundle={bundle} />
              ))}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="categories">
          <div className="space-y-6">
            <h3 className="text-lg font-semibold">Category Affinity Analysis</h3>

            <Card>
              <CardHeader>
                <CardTitle>Category Cross-Purchase Affinity</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={400}>
                  <BarChart data={categoryAffinityData} layout="horizontal">
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis type="number" />
                    <YAxis dataKey="name" type="category" width={150} />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="affinity" fill="#8884d8" name="Affinity Score" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Category Transaction Volume</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <AreaChart data={categoryAffinityData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip />
                      <Area type="monotone" dataKey="transactions" stroke="#8884d8" fill="#8884d8" />
                    </AreaChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Average Basket Value</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <RechartsLineChart data={categoryAffinityData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip />
                      <Line type="monotone" dataKey="value" stroke="#82ca9d" />
                    </RechartsLineChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="insights">
          <div className="space-y-6">
            <h3 className="text-lg font-semibold">AI-Generated Insights</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Brain className="h-5 w-5" />
                    Key Opportunities
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                      <div className="flex items-start gap-2">
                        <Lightbulb className="h-4 w-4 text-green-600 mt-0.5" />
                        <div>
                          <p className="font-medium text-green-800">High-Value Bundle Opportunity</p>
                          <p className="text-sm text-green-700">Electronics + Accessories bundle shows 2.3x lift with 68% confidence</p>
                        </div>
                      </div>
                    </div>
                    <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                      <div className="flex items-start gap-2">
                        <Target className="h-4 w-4 text-blue-600 mt-0.5" />
                        <div>
                          <p className="font-medium text-blue-800">Cross-Category Potential</p>
                          <p className="text-sm text-blue-700">Sports → Health & Beauty shows untapped 45% conversion potential</p>
                        </div>
                      </div>
                    </div>
                    <div className="p-3 bg-purple-50 border border-purple-200 rounded-lg">
                      <div className="flex items-start gap-2">
                        <TrendingUp className="h-4 w-4 text-purple-600 mt-0.5" />
                        <div>
                          <p className="font-medium text-purple-800">Seasonal Trend</p>
                          <p className="text-sm text-purple-700">Home & Garden associations peak 40% higher in spring season</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <AlertTriangle className="h-5 w-5" />
                    Recommendations
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                      <div className="flex items-start gap-2">
                        <Zap className="h-4 w-4 text-yellow-600 mt-0.5" />
                        <div>
                          <p className="font-medium text-yellow-800">Implement Dynamic Bundling</p>
                          <p className="text-sm text-yellow-700">Deploy AI-powered bundle recommendations for top 20 product pairs</p>
                        </div>
                      </div>
                    </div>
                    <div className="p-3 bg-orange-50 border border-orange-200 rounded-lg">
                      <div className="flex items-start gap-2">
                        <Settings className="h-4 w-4 text-orange-600 mt-0.5" />
                        <div>
                          <p className="font-medium text-orange-800">Optimize Placement</p>
                          <p className="text-sm text-orange-700">Move cross-sell recommendations to checkout for 25% lift</p>
                        </div>
                      </div>
                    </div>
                    <div className="p-3 bg-indigo-50 border border-indigo-200 rounded-lg">
                      <div className="flex items-start gap-2">
                        <Activity className="h-4 w-4 text-indigo-600 mt-0.5" />
                        <div>
                          <p className="font-medium text-indigo-800">A/B Test Timing</p>
                          <p className="text-sm text-indigo-700">Test immediate vs. post-purchase recommendations for higher LTV</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Performance Forecast</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <RechartsLineChart data={Array.from({ length: 12 }, (_, i) => ({
                    month: `Month ${i + 1}`,
                    current: 100 + Math.random() * 50,
                    optimized: 120 + Math.random() * 80,
                    potential: 150 + Math.random() * 100
                  }))}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Line type="monotone" dataKey="current" stroke="#8884d8" name="Current Performance" />
                    <Line type="monotone" dataKey="optimized" stroke="#82ca9d" name="With Optimization" />
                    <Line type="monotone" dataKey="potential" stroke="#ffc658" name="Maximum Potential" />
                  </RechartsLineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default MarketBasketAnalysis;