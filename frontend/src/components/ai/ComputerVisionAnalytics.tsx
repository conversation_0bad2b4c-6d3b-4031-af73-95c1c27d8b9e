/**
 * Computer Vision Analytics Dashboard
 * AI-powered visual analysis, image recognition, and automated visual content intelligence
 */

import React, { useState, useMemo, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Camera,
  Eye,
  Image,
  Scan,
  Target,
  Brain,
  Zap,
  TrendingUp,
  TrendingDown,
  Star,
  Award,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Info,
  Settings,
  Download,
  Upload,
  Share,
  Filter,
  Search,
  RefreshCw,
  Play,
  Pause,
  SkipForward,
  SkipBack,
  Calendar,
  Clock,
  Users,
  Package,
  Tag,
  Hash,
  BarChart3,
  LineChart,
  PieChart,
  Activity,
  Grid,
  List,
  Layers,
  Palette,
  Crop,
  Maximize,
  Minimize,
  RotateCw,
  FlipHorizontal,
  FlipVertical,
  Contrast,
  Brightness4,
  Saturation,
  Hue,
  Database,
  Cpu,
  Network,
  Globe,
  Lock,
  Unlock,
  Shield,
  ShieldCheck,
  FileImage,
  Folder,
  FolderOpen,
  Archive,
  HardDrive,
  Cloud,
  CloudUpload,
  CloudDownload,
  Link,
  ExternalLink,
  Copy,
  Trash2,
  Edit,
  Plus,
  Minus,
  MoreHorizontal,
  ArrowUpRight,
  ArrowDownRight,
  ChevronUp,
  ChevronDown,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { 
  LineChart as RechartsLineChart, 
  Line, 
  AreaChart, 
  Area, 
  BarChart, 
  Bar, 
  ScatterChart,
  Scatter,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  Cell,
  PieChart as RechartsPieChart,
  Pie,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
  TreeMap
} from 'recharts';

// Types
interface ProductImage {
  id: string;
  productId: string;
  productName: string;
  url: string;
  filename: string;
  uploadDate: Date;
  fileSize: number;
  dimensions: {
    width: number;
    height: number;
  };
  format: 'jpg' | 'png' | 'webp' | 'gif' | 'svg';
  primary: boolean;
  tags: string[];
  analysisStatus: 'pending' | 'processing' | 'completed' | 'failed';
  analysis: ImageAnalysis;
}

interface ImageAnalysis {
  id: string;
  confidence: number;
  processedAt: Date;
  processingTime: number; // milliseconds
  classification: {
    category: string;
    subcategory: string;
    confidence: number;
    alternativeCategories: { category: string; confidence: number }[];
  };
  objectDetection: {
    objects: DetectedObject[];
    totalObjects: number;
    primaryObject: string;
  };
  qualityAssessment: {
    overall: number;
    sharpness: number;
    brightness: number;
    contrast: number;
    saturation: number;
    composition: number;
    background: number;
    lighting: number;
    issues: QualityIssue[];
    recommendations: string[];
  };
  colorAnalysis: {
    dominantColors: ColorInfo[];
    colorPalette: string[];
    colorHarmony: number;
    vibrancy: number;
    warmth: number;
  };
  textDetection: {
    hasText: boolean;
    extractedText: string[];
    textRegions: TextRegion[];
    brandDetection: {
      brands: string[];
      confidence: number;
    };
  };
  similarImages: {
    imageId: string;
    similarity: number;
    matchType: 'visual' | 'semantic' | 'color' | 'shape';
  }[];
  seoOptimization: {
    altTextSuggestion: string;
    titleSuggestion: string;
    keywords: string[];
    seoScore: number;
  };
  trends: {
    styleMatch: number;
    seasonalRelevance: number;
    marketTrend: 'rising' | 'stable' | 'declining';
    competitorComparison: number;
  };
}

interface DetectedObject {
  name: string;
  confidence: number;
  boundingBox: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  attributes: {
    color?: string;
    material?: string;
    brand?: string;
    condition?: string;
  };
}

interface QualityIssue {
  type: 'blur' | 'dark' | 'overexposed' | 'low_resolution' | 'poor_composition' | 'background_noise';
  severity: 'low' | 'medium' | 'high';
  description: string;
  suggestion: string;
}

interface ColorInfo {
  hex: string;
  rgb: { r: number; g: number; b: number };
  hsl: { h: number; s: number; l: number };
  percentage: number;
  name: string;
}

interface TextRegion {
  text: string;
  confidence: number;
  boundingBox: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
}

interface VisualSearchResult {
  imageId: string;
  productId: string;
  productName: string;
  similarity: number;
  matchType: 'exact' | 'similar' | 'style' | 'color';
  thumbnail: string;
  price: number;
  category: string;
}

interface ImagePerformanceMetrics {
  imageId: string;
  views: number;
  clicks: number;
  conversions: number;
  clickThroughRate: number;
  conversionRate: number;
  revenue: number;
  engagementScore: number;
  bounceRate: number;
  timeSpent: number;
  socialShares: number;
  saves: number;
}

interface TrendAnalysis {
  category: string;
  trendDirection: 'up' | 'down' | 'stable';
  growthRate: number;
  confidence: number;
  visualElements: {
    colors: string[];
    styles: string[];
    compositions: string[];
  };
  seasonality: {
    spring: number;
    summer: number;
    autumn: number;
    winter: number;
  };
  demographics: {
    ageGroups: { [key: string]: number };
    genders: { [key: string]: number };
  };
}

// Mock data generators
const generateProductImages = (): ProductImage[] => {
  const categories = ['Electronics', 'Clothing', 'Home & Garden', 'Sports', 'Beauty', 'Books'];
  const formats: ('jpg' | 'png' | 'webp' | 'gif')[] = ['jpg', 'png', 'webp', 'gif'];
  
  return Array.from({ length: 30 }, (_, index) => {
    const category = categories[Math.floor(Math.random() * categories.length)];
    return {
      id: `IMG-${String(index + 1).padStart(3, '0')}`,
      productId: `PROD-${String(index + 1).padStart(3, '0')}`,
      productName: `Product ${index + 1}`,
      url: `https://example.com/images/product-${index + 1}.jpg`,
      filename: `product-${index + 1}.jpg`,
      uploadDate: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
      fileSize: Math.floor(Math.random() * 5000000) + 100000, // 100KB - 5MB
      dimensions: {
        width: 800 + Math.floor(Math.random() * 1200),
        height: 600 + Math.floor(Math.random() * 800)
      },
      format: formats[Math.floor(Math.random() * formats.length)],
      primary: Math.random() > 0.7,
      tags: ['product', 'main', 'detail', 'lifestyle'].slice(0, Math.floor(Math.random() * 3) + 1),
      analysisStatus: ['completed', 'processing', 'pending'][Math.floor(Math.random() * 3)] as any,
      analysis: generateImageAnalysis(category)
    };
  });
};

const generateImageAnalysis = (category: string): ImageAnalysis => {
  const qualityScore = 0.6 + Math.random() * 0.4;
  
  return {
    id: `ANALYSIS-${Date.now()}`,
    confidence: 0.85 + Math.random() * 0.1,
    processedAt: new Date(),
    processingTime: Math.floor(Math.random() * 5000) + 500,
    classification: {
      category,
      subcategory: `${category} Subcategory`,
      confidence: 0.9 + Math.random() * 0.1,
      alternativeCategories: [
        { category: 'Alternative 1', confidence: 0.7 + Math.random() * 0.2 },
        { category: 'Alternative 2', confidence: 0.5 + Math.random() * 0.2 }
      ]
    },
    objectDetection: {
      objects: [
        {
          name: 'Main Product',
          confidence: 0.95,
          boundingBox: { x: 100, y: 50, width: 300, height: 400 },
          attributes: {
            color: ['red', 'blue', 'green', 'black', 'white'][Math.floor(Math.random() * 5)],
            material: ['plastic', 'metal', 'fabric', 'wood'][Math.floor(Math.random() * 4)]
          }
        }
      ],
      totalObjects: Math.floor(Math.random() * 5) + 1,
      primaryObject: 'Main Product'
    },
    qualityAssessment: {
      overall: qualityScore,
      sharpness: 0.7 + Math.random() * 0.3,
      brightness: 0.6 + Math.random() * 0.4,
      contrast: 0.6 + Math.random() * 0.4,
      saturation: 0.5 + Math.random() * 0.5,
      composition: 0.7 + Math.random() * 0.3,
      background: 0.8 + Math.random() * 0.2,
      lighting: 0.6 + Math.random() * 0.4,
      issues: qualityScore < 0.7 ? [
        {
          type: 'blur',
          severity: 'medium',
          description: 'Image appears slightly blurred',
          suggestion: 'Use better camera stabilization'
        }
      ] : [],
      recommendations: [
        'Improve lighting setup',
        'Use neutral background',
        'Center the product better'
      ]
    },
    colorAnalysis: {
      dominantColors: [
        {
          hex: '#FF5733',
          rgb: { r: 255, g: 87, b: 51 },
          hsl: { h: 14, s: 100, l: 60 },
          percentage: 35,
          name: 'Red Orange'
        },
        {
          hex: '#33FF57',
          rgb: { r: 51, g: 255, b: 87 },
          hsl: { h: 134, s: 100, l: 60 },
          percentage: 25,
          name: 'Spring Green'
        }
      ],
      colorPalette: ['#FF5733', '#33FF57', '#3357FF', '#F3FF33'],
      colorHarmony: 0.7 + Math.random() * 0.3,
      vibrancy: 0.6 + Math.random() * 0.4,
      warmth: Math.random()
    },
    textDetection: {
      hasText: Math.random() > 0.5,
      extractedText: ['Brand Name', 'Product Title'],
      textRegions: [
        {
          text: 'Brand Name',
          confidence: 0.95,
          boundingBox: { x: 10, y: 10, width: 100, height: 30 }
        }
      ],
      brandDetection: {
        brands: ['Nike', 'Apple', 'Samsung'][Math.random() > 0.5 ? 0 : Math.floor(Math.random() * 3)],
        confidence: 0.8 + Math.random() * 0.2
      }
    },
    similarImages: [
      { imageId: 'IMG-002', similarity: 0.85, matchType: 'visual' },
      { imageId: 'IMG-003', similarity: 0.72, matchType: 'color' }
    ],
    seoOptimization: {
      altTextSuggestion: `High-quality ${category.toLowerCase()} product image`,
      titleSuggestion: `Premium ${category} - Professional Product Photo`,
      keywords: [category.toLowerCase(), 'product', 'high-quality', 'professional'],
      seoScore: 0.7 + Math.random() * 0.3
    },
    trends: {
      styleMatch: 0.6 + Math.random() * 0.4,
      seasonalRelevance: Math.random(),
      marketTrend: ['rising', 'stable', 'declining'][Math.floor(Math.random() * 3)] as any,
      competitorComparison: Math.random()
    }
  };
};

const generatePerformanceMetrics = (): ImagePerformanceMetrics[] => {
  return Array.from({ length: 20 }, (_, index) => ({
    imageId: `IMG-${String(index + 1).padStart(3, '0')}`,
    views: Math.floor(Math.random() * 10000) + 1000,
    clicks: Math.floor(Math.random() * 1000) + 100,
    conversions: Math.floor(Math.random() * 100) + 10,
    clickThroughRate: 0.05 + Math.random() * 0.15,
    conversionRate: 0.01 + Math.random() * 0.05,
    revenue: Math.random() * 5000 + 500,
    engagementScore: 0.5 + Math.random() * 0.5,
    bounceRate: 0.2 + Math.random() * 0.4,
    timeSpent: 30 + Math.random() * 120, // seconds
    socialShares: Math.floor(Math.random() * 50),
    saves: Math.floor(Math.random() * 100)
  }));
};

const generateTrendAnalysis = (): TrendAnalysis[] => {
  const categories = ['Electronics', 'Clothing', 'Home & Garden', 'Sports', 'Beauty'];
  
  return categories.map(category => ({
    category,
    trendDirection: ['up', 'down', 'stable'][Math.floor(Math.random() * 3)] as any,
    growthRate: -0.2 + Math.random() * 0.6, // -20% to +40%
    confidence: 0.7 + Math.random() * 0.3,
    visualElements: {
      colors: ['#FF5733', '#33FF57', '#3357FF'].slice(0, Math.floor(Math.random() * 3) + 1),
      styles: ['minimalist', 'vibrant', 'vintage'].slice(0, Math.floor(Math.random() * 3) + 1),
      compositions: ['centered', 'lifestyle', 'detail'].slice(0, Math.floor(Math.random() * 3) + 1)
    },
    seasonality: {
      spring: Math.random(),
      summer: Math.random(),
      autumn: Math.random(),
      winter: Math.random()
    },
    demographics: {
      ageGroups: {
        '18-25': Math.random(),
        '26-35': Math.random(),
        '36-45': Math.random(),
        '46-55': Math.random(),
        '55+': Math.random()
      },
      genders: {
        'Male': Math.random(),
        'Female': Math.random(),
        'Other': Math.random()
      }
    }
  }));
};

// Components
const ImageCard: React.FC<{ image: ProductImage }> = ({ image }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'processing': return 'bg-yellow-100 text-yellow-800';
      case 'pending': return 'bg-gray-100 text-gray-800';
      case 'failed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="h-3 w-3" />;
      case 'processing': return <RefreshCw className="h-3 w-3 animate-spin" />;
      case 'pending': return <Clock className="h-3 w-3" />;
      case 'failed': return <XCircle className="h-3 w-3" />;
      default: return <Info className="h-3 w-3" />;
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className="border rounded-lg overflow-hidden hover:shadow-md transition-shadow"
    >
      <div className="aspect-square bg-gray-100 relative">
        <div className="absolute inset-0 flex items-center justify-center">
          <Image className="h-12 w-12 text-gray-400" />
        </div>
        {image.primary && (
          <Badge className="absolute top-2 left-2 bg-blue-600">
            Primary
          </Badge>
        )}
        <Badge className={`absolute top-2 right-2 ${getStatusColor(image.analysisStatus)}`}>
          {getStatusIcon(image.analysisStatus)}
          {image.analysisStatus}
        </Badge>
      </div>
      
      <div className="p-4">
        <div className="mb-2">
          <h4 className="font-medium truncate">{image.productName}</h4>
          <p className="text-sm text-gray-600">{image.filename}</p>
        </div>
        
        <div className="grid grid-cols-2 gap-2 mb-3 text-sm">
          <div>
            <span className="text-gray-600">Size:</span>
            <span className="ml-1">{(image.fileSize / 1024 / 1024).toFixed(1)}MB</span>
          </div>
          <div>
            <span className="text-gray-600">Dimensions:</span>
            <span className="ml-1">{image.dimensions.width}×{image.dimensions.height}</span>
          </div>
        </div>

        {image.analysisStatus === 'completed' && (
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">Quality Score</span>
              <span className="font-medium">{(image.analysis.qualityAssessment.overall * 100).toFixed(0)}%</span>
            </div>
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">Category</span>
              <span className="font-medium">{image.analysis.classification.category}</span>
            </div>
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">Objects</span>
              <span className="font-medium">{image.analysis.objectDetection.totalObjects}</span>
            </div>
          </div>
        )}

        <div className="mt-3 flex flex-wrap gap-1">
          {image.tags.map((tag, index) => (
            <Badge key={index} variant="outline" className="text-xs">
              {tag}
            </Badge>
          ))}
        </div>
      </div>
    </motion.div>
  );
};

const QualityAnalysisChart: React.FC<{ images: ProductImage[] }> = ({ images }) => {
  const qualityData = useMemo(() => {
    const completedImages = images.filter(img => img.analysisStatus === 'completed');
    return completedImages.map(img => ({
      name: img.productName.slice(0, 10),
      overall: img.analysis.qualityAssessment.overall * 100,
      sharpness: img.analysis.qualityAssessment.sharpness * 100,
      brightness: img.analysis.qualityAssessment.brightness * 100,
      contrast: img.analysis.qualityAssessment.contrast * 100,
      composition: img.analysis.qualityAssessment.composition * 100
    }));
  }, [images]);

  return (
    <ResponsiveContainer width="100%" height={400}>
      <RadarChart data={qualityData}>
        <PolarGrid />
        <PolarAngleAxis dataKey="name" />
        <PolarRadiusAxis angle={90} domain={[0, 100]} />
        <Radar name="Overall" dataKey="overall" stroke="#8884d8" fill="#8884d8" fillOpacity={0.6} />
        <Radar name="Sharpness" dataKey="sharpness" stroke="#82ca9d" fill="#82ca9d" fillOpacity={0.6} />
        <Radar name="Brightness" dataKey="brightness" stroke="#ffc658" fill="#ffc658" fillOpacity={0.6} />
        <Legend />
      </RadarChart>
    </ResponsiveContainer>
  );
};

export const ComputerVisionAnalytics: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'uploadDate' | 'quality' | 'size' | 'name'>('uploadDate');

  const images = useMemo(() => generateProductImages(), []);
  const performanceMetrics = useMemo(() => generatePerformanceMetrics(), []);
  const trendAnalysis = useMemo(() => generateTrendAnalysis(), []);

  const filteredImages = useMemo(() => {
    return images
      .filter(img => selectedCategory === 'all' || img.analysis.classification.category === selectedCategory)
      .filter(img => selectedStatus === 'all' || img.analysisStatus === selectedStatus)
      .filter(img => img.productName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                    img.filename.toLowerCase().includes(searchQuery.toLowerCase()))
      .sort((a, b) => {
        switch (sortBy) {
          case 'uploadDate':
            return b.uploadDate.getTime() - a.uploadDate.getTime();
          case 'quality':
            return (b.analysis?.qualityAssessment?.overall || 0) - (a.analysis?.qualityAssessment?.overall || 0);
          case 'size':
            return b.fileSize - a.fileSize;
          case 'name':
            return a.productName.localeCompare(b.productName);
          default:
            return 0;
        }
      });
  }, [images, selectedCategory, selectedStatus, searchQuery, sortBy]);

  const analyticsMetrics = useMemo(() => {
    const completedAnalyses = images.filter(img => img.analysisStatus === 'completed');
    const averageQuality = completedAnalyses.length > 0 ? 
      completedAnalyses.reduce((sum, img) => sum + img.analysis.qualityAssessment.overall, 0) / completedAnalyses.length : 0;
    
    const categoryDistribution = images.reduce((acc, img) => {
      const category = img.analysis.classification?.category || 'Unknown';
      acc[category] = (acc[category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalImages: images.length,
      completedAnalyses: completedAnalyses.length,
      averageQuality,
      averageFileSize: images.reduce((sum, img) => sum + img.fileSize, 0) / images.length,
      categoryDistribution,
      processingQueue: images.filter(img => img.analysisStatus === 'processing' || img.analysisStatus === 'pending').length
    };
  }, [images]);

  const qualityDistributionData = useMemo(() => {
    const ranges = [
      { range: '0-20%', min: 0, max: 0.2 },
      { range: '21-40%', min: 0.2, max: 0.4 },
      { range: '41-60%', min: 0.4, max: 0.6 },
      { range: '61-80%', min: 0.6, max: 0.8 },
      { range: '81-100%', min: 0.8, max: 1.0 }
    ];

    return ranges.map(range => {
      const count = images.filter(img => 
        img.analysisStatus === 'completed' &&
        img.analysis.qualityAssessment.overall >= range.min &&
        img.analysis.qualityAssessment.overall < range.max
      ).length;
      
      return {
        range: range.range,
        count
      };
    });
  }, [images]);

  const categoryData = useMemo(() => {
    return Object.entries(analyticsMetrics.categoryDistribution).map(([category, count]) => ({
      name: category,
      value: count,
      percentage: ((count / analyticsMetrics.totalImages) * 100).toFixed(1)
    }));
  }, [analyticsMetrics]);

  const performanceTrendData = useMemo(() => {
    return Array.from({ length: 30 }, (_, i) => ({
      day: i + 1,
      analyzed: Math.floor(Math.random() * 50) + 20,
      quality_score: 60 + Math.random() * 30,
      processing_time: 1000 + Math.random() * 3000
    }));
  }, []);

  return (
    <div className="p-6 space-y-6 bg-gradient-to-br from-purple-50 via-white to-indigo-50 min-h-screen">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-2">
            <Camera className="text-purple-600" />
            Computer Vision Analytics
          </h1>
          <p className="text-gray-600 mt-1">AI-powered visual analysis and image intelligence</p>
        </div>
        <div className="flex items-center gap-4">
          <select
            value="7d"
            onChange={() => {}}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
          >
            <option value="1d">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
            <option value="90d">Last 90 Days</option>
          </select>
          <Button className="bg-purple-600 hover:bg-purple-700">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh Analysis
          </Button>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Card className="bg-gradient-to-r from-purple-500 to-purple-600 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-purple-100">Images Analyzed</p>
                  <p className="text-3xl font-bold">{analyticsMetrics.totalImages.toLocaleString()}</p>
                  <p className="text-purple-100 text-sm">+{Math.floor(Math.random() * 25 + 10)}% vs last period</p>
                </div>
                <Image className="h-12 w-12 text-purple-200" />
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-100">Objects Detected</p>
                  <p className="text-3xl font-bold">{(analyticsMetrics.totalImages * 2.3).toFixed(0)}</p>
                  <p className="text-blue-100 text-sm">Avg. Confidence: {(Math.random() * 15 + 85).toFixed(1)}%</p>
                </div>
                <Target className="h-12 w-12 text-blue-200" />
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <Card className="bg-gradient-to-r from-green-500 to-green-600 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-green-100">Quality Score</p>
                  <p className="text-3xl font-bold">{(analyticsMetrics.averageQuality * 100).toFixed(1)}%</p>
                  <p className="text-green-100 text-sm">Avg. across all images</p>
                </div>
                <Star className="h-12 w-12 text-green-200" />
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          <Card className="bg-gradient-to-r from-orange-500 to-orange-600 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-orange-100">Processing Queue</p>
                  <p className="text-3xl font-bold">{analyticsMetrics.processingQueue}</p>
                  <p className="text-orange-100 text-sm">Processing at {(Math.random() * 500 + 100).toFixed(0)}ms avg</p>
                </div>
                <Brain className="h-12 w-12 text-orange-200" />
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      <Tabs defaultValue="gallery" className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="gallery">Image Gallery</TabsTrigger>
          <TabsTrigger value="analysis">Quality Analysis</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="trends">Visual Trends</TabsTrigger>
          <TabsTrigger value="search">Visual Search</TabsTrigger>
        </TabsList>

        <TabsContent value="gallery">
          <div className="space-y-6">
            {/* Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Image className="h-4 w-4 text-blue-600" />
                    <div>
                      <p className="text-sm text-gray-600">Total Images</p>
                      <p className="text-2xl font-bold">{analyticsMetrics.totalImages}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Brain className="h-4 w-4 text-green-600" />
                    <div>
                      <p className="text-sm text-gray-600">Analyzed</p>
                      <p className="text-2xl font-bold">{analyticsMetrics.completedAnalyses}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Star className="h-4 w-4 text-yellow-600" />
                    <div>
                      <p className="text-sm text-gray-600">Avg Quality</p>
                      <p className="text-2xl font-bold">{(analyticsMetrics.averageQuality * 100).toFixed(0)}%</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <RefreshCw className="h-4 w-4 text-purple-600" />
                    <div>
                      <p className="text-sm text-gray-600">Processing</p>
                      <p className="text-2xl font-bold">{analyticsMetrics.processingQueue}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Filters */}
            <div className="flex items-center gap-4">
              <div className="flex-1">
                <Input
                  placeholder="Search images..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full"
                />
              </div>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-3 py-2 border rounded-md"
              >
                <option value="all">All Categories</option>
                {Object.keys(analyticsMetrics.categoryDistribution).map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="px-3 py-2 border rounded-md"
              >
                <option value="all">All Status</option>
                <option value="completed">Completed</option>
                <option value="processing">Processing</option>
                <option value="pending">Pending</option>
                <option value="failed">Failed</option>
              </select>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="px-3 py-2 border rounded-md"
              >
                <option value="uploadDate">Sort by Upload Date</option>
                <option value="quality">Sort by Quality</option>
                <option value="size">Sort by Size</option>
                <option value="name">Sort by Name</option>
              </select>
            </div>

            {/* Images Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {filteredImages.map((image) => (
                <ImageCard key={image.id} image={image} />
              ))}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="analysis">
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Quality Analysis</h3>
              <Button size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export Report
              </Button>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Quality Score Distribution</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={qualityDistributionData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="range" />
                      <YAxis />
                      <Tooltip />
                      <Bar dataKey="count" fill="#8884d8" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Category Distribution</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <RechartsPieChart>
                      <Pie
                        data={categoryData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percentage }) => `${name}: ${percentage}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {categoryData.map((entry, index) => (
                          <Cell
                            key={`cell-${index}`}
                            fill={['#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#8dd1e1'][index % 5]}
                          />
                        ))}
                      </Pie>
                      <Tooltip />
                    </RechartsPieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Quality Metrics Comparison</CardTitle>
              </CardHeader>
              <CardContent>
                <QualityAnalysisChart images={images} />
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="performance">
          <div className="space-y-6">
            <h3 className="text-lg font-semibold">Performance Analytics</h3>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Processing Performance</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <RechartsLineChart data={performanceTrendData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="day" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Line type="monotone" dataKey="analyzed" stroke="#8884d8" name="Images Analyzed" />
                      <Line type="monotone" dataKey="quality_score" stroke="#82ca9d" name="Avg Quality Score" />
                    </RechartsLineChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Processing Time Trends</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <AreaChart data={performanceTrendData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="day" />
                      <YAxis />
                      <Tooltip />
                      <Area type="monotone" dataKey="processing_time" stroke="#8884d8" fill="#8884d8" />
                    </AreaChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>

            {/* Performance Table */}
            <Card>
              <CardHeader>
                <CardTitle>Top Performing Images</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left p-2">Image ID</th>
                        <th className="text-left p-2">Views</th>
                        <th className="text-left p-2">CTR</th>
                        <th className="text-left p-2">Conversions</th>
                        <th className="text-left p-2">Revenue</th>
                        <th className="text-left p-2">Engagement</th>
                      </tr>
                    </thead>
                    <tbody>
                      {performanceMetrics.slice(0, 10).map((metric) => (
                        <motion.tr
                          key={metric.imageId}
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          className="border-b hover:bg-gray-50"
                        >
                          <td className="p-2 font-medium">{metric.imageId}</td>
                          <td className="p-2">{metric.views.toLocaleString()}</td>
                          <td className="p-2">{(metric.clickThroughRate * 100).toFixed(1)}%</td>
                          <td className="p-2">{metric.conversions}</td>
                          <td className="p-2">${metric.revenue.toFixed(0)}</td>
                          <td className="p-2">
                            <div className="flex items-center gap-1">
                              <div className="w-16 bg-gray-200 rounded-full h-2">
                                <div
                                  className="bg-blue-600 h-2 rounded-full"
                                  style={{ width: `${metric.engagementScore * 100}%` }}
                                />
                              </div>
                              <span className="text-sm">{(metric.engagementScore * 100).toFixed(0)}%</span>
                            </div>
                          </td>
                        </motion.tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="trends">
          <div className="space-y-6">
            <h3 className="text-lg font-semibold">Visual Trends Analysis</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {trendAnalysis.map((trend, index) => (
                <motion.div
                  key={trend.category}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-medium">{trend.category}</h4>
                        <Badge variant={
                          trend.trendDirection === 'up' ? 'default' : 
                          trend.trendDirection === 'down' ? 'destructive' : 'secondary'
                        }>
                          {trend.trendDirection === 'up' && <TrendingUp className="h-3 w-3 mr-1" />}
                          {trend.trendDirection === 'down' && <TrendingDown className="h-3 w-3 mr-1" />}
                          {trend.growthRate > 0 ? '+' : ''}{(trend.growthRate * 100).toFixed(1)}%
                        </Badge>
                      </div>
                      
                      <div className="space-y-3">
                        <div>
                          <p className="text-sm text-gray-600 mb-1">Trending Styles</p>
                          <div className="flex flex-wrap gap-1">
                            {trend.visualElements.styles.map((style, idx) => (
                              <Badge key={idx} variant="outline" className="text-xs">
                                {style}
                              </Badge>
                            ))}
                          </div>
                        </div>
                        
                        <div>
                          <p className="text-sm text-gray-600 mb-1">Popular Colors</p>
                          <div className="flex gap-1">
                            {trend.visualElements.colors.map((color, idx) => (
                              <div
                                key={idx}
                                className="w-6 h-6 rounded border-2 border-gray-200"
                                style={{ backgroundColor: color }}
                                title={color}
                              />
                            ))}
                          </div>
                        </div>
                        
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-gray-600">Confidence</span>
                          <span className="font-medium">{(trend.confidence * 100).toFixed(0)}%</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Seasonal Trends</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={400}>
                  <BarChart data={trendAnalysis}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="category" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="seasonality.spring" fill="#22c55e" name="Spring" />
                    <Bar dataKey="seasonality.summer" fill="#eab308" name="Summer" />
                    <Bar dataKey="seasonality.autumn" fill="#f97316" name="Autumn" />
                    <Bar dataKey="seasonality.winter" fill="#3b82f6" name="Winter" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="search">
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Visual Search</h3>
              <Button size="sm">
                <Upload className="h-4 w-4 mr-2" />
                Upload Search Image
              </Button>
            </div>

            <Card>
              <CardContent className="p-6">
                <div className="text-center">
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-8">
                    <Camera className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-lg font-medium mb-2">Upload an image to find similar products</p>
                    <p className="text-gray-600 mb-4">Drag and drop an image or click to browse</p>
                    <Button>
                      <Upload className="h-4 w-4 mr-2" />
                      Choose Image
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Search Analytics</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">Total Searches</span>
                      <span className="font-semibold">12,456</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">Successful Matches</span>
                      <span className="font-semibold">10,892 (87.4%)</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">Avg Response Time</span>
                      <span className="font-semibold">0.8s</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">Click-through Rate</span>
                      <span className="font-semibold">23.5%</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Popular Search Categories</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {['Electronics', 'Clothing', 'Home & Garden', 'Sports', 'Beauty'].map((category, index) => (
                      <div key={category} className="flex items-center justify-between">
                        <span>{category}</span>
                        <div className="flex items-center gap-2">
                          <div className="w-24 bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-blue-600 h-2 rounded-full"
                              style={{ width: `${(5 - index) * 20}%` }}
                            />
                          </div>
                          <span className="text-sm text-gray-600">{(5 - index) * 20}%</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ComputerVisionAnalytics;