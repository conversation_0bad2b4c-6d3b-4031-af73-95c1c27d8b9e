/**
 * Advanced Churn Prediction with Intervention Strategies
 * AI-powered churn prediction with personalized intervention recommendations and automated retention campaigns
 */

import React, { useState, useMemo, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { motion } from 'framer-motion';
import { 
  AlertTriangle, 
  Users, 
  Target,
  Shield,
  TrendingDown,
  TrendingUp,
  Brain,
  Zap,
  Heart,
  Mail,
  Phone,
  MessageSquare,
  Gift,
  Percent,
  DollarSign,
  Calendar,
  Clock,
  Activity,
  Eye,
  Settings,
  Download,
  Upload,
  Share,
  Filter,
  Search,
  RefreshCw,
  Play,
  Pause,
  CheckCircle,
  XCircle,
  Minus,
  Plus,
  ArrowUpRight,
  ArrowDownRight,
  BarChart3,
  LineChart,
  PieChart,
  Award,
  Star,
  Lightbulb,
  Database,
  Cpu,
  Network,
  Globe,
  Lock,
  Unlock,
  Bell,
  BellOff,
  Flag,
  MapPin,
  CreditCard,
  ShoppingCart,
  Package,
  Truck,
  Home,
  Building2,
  UserX,
  UserCheck,
  UserPlus
} from 'lucide-react';
import { 
  LineChart as RechartsLineChart, 
  Line, 
  AreaChart, 
  Area, 
  BarChart, 
  Bar, 
  ScatterChart,
  Scatter,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  Cell,
  PieChart as RechartsPieChart,
  Pie,
  RadialBarChart,
  RadialBar
} from 'recharts';

// Types
interface ChurnPrediction {
  customerId: string;
  customerEmail: string;
  customerName: string;
  churnProbability: number;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  timeToChurn: number; // days
  confidence: number;
  primaryRiskFactors: string[];
  behavioralSignals: {
    decreasedEngagement: boolean;
    reducedPurchaseFrequency: boolean;
    lowerOrderValue: boolean;
    supportTickets: number;
    negativeReviews: number;
    competitorActivity: boolean;
  };
  customerProfile: {
    segment: string;
    lifetimeValue: number;
    tenure: number;
    lastPurchase: Date;
    avgOrderValue: number;
    totalOrders: number;
    preferredChannels: string[];
    location: string;
    demographics: {
      ageGroup: string;
      gender: string;
      incomeLevel: string;
    };
  };
  predictedChurnDate: Date;
  interventionRecommendations: InterventionStrategy[];
  automatedCampaigns: AutomatedCampaign[];
  modelFeatures: {
    [key: string]: number;
  };
}

interface InterventionStrategy {
  id: string;
  type: 'discount' | 'personalization' | 'engagement' | 'support' | 'loyalty' | 'cross_sell';
  name: string;
  description: string;
  expectedEffectiveness: number;
  cost: number;
  timeframe: string;
  channel: 'email' | 'sms' | 'push' | 'in_app' | 'phone' | 'mail';
  personalization: {
    message: string;
    offer: string;
    timing: string;
  };
  targetMetrics: {
    retentionLift: number;
    revenueImpact: number;
    engagementIncrease: number;
  };
  implementation: {
    automated: boolean;
    approvalRequired: boolean;
    setupTime: string;
  };
  success_probability: number;
  roi_estimate: number;
}

interface AutomatedCampaign {
  id: string;
  name: string;
  trigger: string;
  status: 'active' | 'paused' | 'completed' | 'draft';
  targetSegment: string;
  channel: string;
  message: string;
  offer: string;
  timing: {
    delay: number;
    frequency: string;
    endCondition: string;
  };
  performance: {
    sent: number;
    opened: number;
    clicked: number;
    converted: number;
    retained: number;
  };
  abTestConfig: {
    enabled: boolean;
    variants: string[];
    splitRatio: number[];
    winningVariant: string;
  };
  createdAt: Date;
  lastRun: Date;
}

interface ChurnModel {
  id: string;
  name: string;
  type: 'gradient_boosting' | 'neural_network' | 'random_forest' | 'ensemble';
  version: string;
  accuracy: number;
  precision: number;
  recall: number;
  f1Score: number;
  auc: number;
  trainingDate: Date;
  features: string[];
  hyperparameters: any;
  status: 'active' | 'training' | 'evaluating' | 'deprecated';
  performance_trend: 'improving' | 'stable' | 'declining';
}

interface ChurnCohortAnalysis {
  cohort: string;
  period: number;
  totalCustomers: number;
  churnedCustomers: number;
  churnRate: number;
  retainedRevenue: number;
  interventionsDeployed: number;
  interventionSuccess: number;
}

// Mock data generators
const generateChurnPredictions = (): ChurnPrediction[] => {
  const customers = [
    'Sarah Johnson', 'Michael Chen', 'Emily Rodriguez', 'David Kim', 'Jessica Wang',
    'Robert Taylor', 'Amanda Davis', 'Christopher Lee', 'Maria Garcia', 'James Wilson',
    'Lisa Brown', 'Kevin Martinez', 'Jennifer Anderson', 'Daniel Thompson', 'Rachel Kim'
  ];

  return customers.map((name, index) => {
    const churnProbability = Math.random();
    const riskLevel = churnProbability > 0.7 ? 'critical' : 
                     churnProbability > 0.5 ? 'high' : 
                     churnProbability > 0.3 ? 'medium' : 'low';
    
    return {
      customerId: `CUST-${String(index + 1).padStart(5, '0')}`,
      customerEmail: `${name.toLowerCase().replace(' ', '.')}@email.com`,
      customerName: name,
      churnProbability,
      riskLevel,
      timeToChurn: Math.floor(Math.random() * 90) + 7,
      confidence: 0.85 + Math.random() * 0.1,
      primaryRiskFactors: [
        'Decreased engagement',
        'Support ticket volume',
        'Competitor activity',
        'Price sensitivity'
      ].slice(0, Math.floor(Math.random() * 3) + 1),
      behavioralSignals: {
        decreasedEngagement: Math.random() > 0.5,
        reducedPurchaseFrequency: Math.random() > 0.6,
        lowerOrderValue: Math.random() > 0.4,
        supportTickets: Math.floor(Math.random() * 5),
        negativeReviews: Math.floor(Math.random() * 3),
        competitorActivity: Math.random() > 0.7
      },
      customerProfile: {
        segment: ['Premium', 'Standard', 'Basic', 'Enterprise'][Math.floor(Math.random() * 4)],
        lifetimeValue: 500 + Math.random() * 5000,
        tenure: Math.floor(Math.random() * 1000) + 30,
        lastPurchase: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000),
        avgOrderValue: 50 + Math.random() * 200,
        totalOrders: Math.floor(Math.random() * 50) + 1,
        preferredChannels: ['Email', 'SMS', 'Push'].slice(0, Math.floor(Math.random() * 3) + 1),
        location: ['New York', 'California', 'Texas', 'Florida'][Math.floor(Math.random() * 4)],
        demographics: {
          ageGroup: ['18-25', '26-35', '36-45', '46-55', '55+'][Math.floor(Math.random() * 5)],
          gender: ['M', 'F', 'Other'][Math.floor(Math.random() * 3)],
          incomeLevel: ['Low', 'Medium', 'High'][Math.floor(Math.random() * 3)]
        }
      },
      predictedChurnDate: new Date(Date.now() + (Math.floor(Math.random() * 90) + 7) * 24 * 60 * 60 * 1000),
      interventionRecommendations: generateInterventionStrategies(),
      automatedCampaigns: generateAutomatedCampaigns(),
      modelFeatures: {
        recency: Math.random(),
        frequency: Math.random(),
        monetary: Math.random(),
        engagement: Math.random(),
        support_tickets: Math.random(),
        satisfaction: Math.random()
      }
    };
  });
};

const generateInterventionStrategies = (): InterventionStrategy[] => {
  return [
    {
      id: 'DISCOUNT-001',
      type: 'discount',
      name: 'Personalized Discount Offer',
      description: 'Targeted discount based on purchase history and preferences',
      expectedEffectiveness: 0.72,
      cost: 25,
      timeframe: '7-14 days',
      channel: 'email',
      personalization: {
        message: 'We miss you! Here\'s a special offer just for you.',
        offer: '20% off your favorite products',
        timing: 'Send within 24 hours'
      },
      targetMetrics: {
        retentionLift: 0.35,
        revenueImpact: 125,
        engagementIncrease: 0.45
      },
      implementation: {
        automated: true,
        approvalRequired: false,
        setupTime: '1 hour'
      },
      success_probability: 0.68,
      roi_estimate: 4.2
    },
    {
      id: 'ENGAGEMENT-001',
      type: 'engagement',
      name: 'Re-engagement Campaign',
      description: 'Multi-touch campaign to rebuild customer relationship',
      expectedEffectiveness: 0.58,
      cost: 15,
      timeframe: '14-21 days',
      channel: 'email',
      personalization: {
        message: 'Let\'s reconnect! Here are some updates you might like.',
        offer: 'Exclusive early access to new products',
        timing: 'Send in 3-day intervals'
      },
      targetMetrics: {
        retentionLift: 0.28,
        revenueImpact: 85,
        engagementIncrease: 0.62
      },
      implementation: {
        automated: true,
        approvalRequired: false,
        setupTime: '2 hours'
      },
      success_probability: 0.55,
      roi_estimate: 3.8
    },
    {
      id: 'SUPPORT-001',
      type: 'support',
      name: 'Proactive Support Outreach',
      description: 'Personal check-in to address potential issues',
      expectedEffectiveness: 0.81,
      cost: 45,
      timeframe: '3-5 days',
      channel: 'phone',
      personalization: {
        message: 'We want to ensure you\'re having a great experience.',
        offer: 'Free consultation and priority support',
        timing: 'Call within 48 hours'
      },
      targetMetrics: {
        retentionLift: 0.52,
        revenueImpact: 180,
        engagementIncrease: 0.38
      },
      implementation: {
        automated: false,
        approvalRequired: true,
        setupTime: '4 hours'
      },
      success_probability: 0.74,
      roi_estimate: 5.1
    }
  ];
};

const generateAutomatedCampaigns = (): AutomatedCampaign[] => {
  return [
    {
      id: 'CAMP-001',
      name: 'Win-Back Series',
      trigger: 'Churn probability > 0.7',
      status: 'active',
      targetSegment: 'High-risk customers',
      channel: 'Email + SMS',
      message: 'We miss you! Come back for exclusive offers.',
      offer: '25% off + free shipping',
      timing: {
        delay: 1,
        frequency: 'Every 3 days',
        endCondition: 'Purchase or 30 days'
      },
      performance: {
        sent: 1250,
        opened: 435,
        clicked: 87,
        converted: 23,
        retained: 18
      },
      abTestConfig: {
        enabled: true,
        variants: ['Discount Focus', 'Emotional Appeal', 'Product Recommendation'],
        splitRatio: [0.4, 0.3, 0.3],
        winningVariant: 'Discount Focus'
      },
      createdAt: new Date('2024-01-15'),
      lastRun: new Date('2024-01-20')
    },
    {
      id: 'CAMP-002',
      name: 'Loyalty Boost',
      trigger: 'Engagement score < 0.3',
      status: 'active',
      targetSegment: 'Declining engagement',
      channel: 'Push notification',
      message: 'Unlock exclusive rewards and benefits.',
      offer: 'Double loyalty points for 14 days',
      timing: {
        delay: 2,
        frequency: 'Weekly',
        endCondition: 'Engagement improvement or 60 days'
      },
      performance: {
        sent: 890,
        opened: 312,
        clicked: 124,
        converted: 67,
        retained: 45
      },
      abTestConfig: {
        enabled: false,
        variants: [],
        splitRatio: [],
        winningVariant: ''
      },
      createdAt: new Date('2024-01-10'),
      lastRun: new Date('2024-01-18')
    }
  ];
};

const generateChurnModels = (): ChurnModel[] => {
  return [
    {
      id: 'MODEL-001',
      name: 'XGBoost Ensemble',
      type: 'gradient_boosting',
      version: 'v2.1.3',
      accuracy: 0.924,
      precision: 0.887,
      recall: 0.856,
      f1Score: 0.871,
      auc: 0.945,
      trainingDate: new Date('2024-01-15'),
      features: ['recency', 'frequency', 'monetary', 'engagement', 'support_tickets', 'satisfaction'],
      hyperparameters: {
        n_estimators: 500,
        max_depth: 8,
        learning_rate: 0.1
      },
      status: 'active',
      performance_trend: 'improving'
    },
    {
      id: 'MODEL-002',
      name: 'Neural Network',
      type: 'neural_network',
      version: 'v1.8.2',
      accuracy: 0.911,
      precision: 0.902,
      recall: 0.834,
      f1Score: 0.867,
      auc: 0.938,
      trainingDate: new Date('2024-01-12'),
      features: ['behavioral_features', 'transaction_features', 'engagement_features'],
      hyperparameters: {
        hidden_layers: [256, 128, 64],
        dropout: 0.3,
        learning_rate: 0.001
      },
      status: 'active',
      performance_trend: 'stable'
    }
  ];
};

const generateCohortAnalysis = (): ChurnCohortAnalysis[] => {
  return Array.from({ length: 12 }, (_, i) => ({
    cohort: `2024-${String(i + 1).padStart(2, '0')}`,
    period: i + 1,
    totalCustomers: 1000 + Math.floor(Math.random() * 500),
    churnedCustomers: Math.floor(Math.random() * 200) + 50,
    churnRate: 0.05 + Math.random() * 0.15,
    retainedRevenue: 50000 + Math.random() * 30000,
    interventionsDeployed: Math.floor(Math.random() * 150) + 20,
    interventionSuccess: Math.floor(Math.random() * 80) + 40
  }));
};

// Components
const ChurnPredictionCard: React.FC<{ prediction: ChurnPrediction }> = ({ prediction }) => {
  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default: return 'bg-green-100 text-green-800 border-green-200';
    }
  };

  const getRiskIcon = (risk: string) => {
    switch (risk) {
      case 'critical': return <AlertTriangle className="h-4 w-4" />;
      case 'high': return <TrendingDown className="h-4 w-4" />;
      case 'medium': return <Minus className="h-4 w-4" />;
      default: return <CheckCircle className="h-4 w-4" />;
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="border rounded-lg p-4 hover:shadow-md transition-shadow"
    >
      <div className="flex items-start justify-between mb-3">
        <div>
          <h4 className="font-medium">{prediction.customerName}</h4>
          <p className="text-sm text-gray-600">{prediction.customerEmail}</p>
        </div>
        <Badge className={getRiskColor(prediction.riskLevel)}>
          {getRiskIcon(prediction.riskLevel)}
          {prediction.riskLevel.toUpperCase()}
        </Badge>
      </div>
      
      <div className="grid grid-cols-2 gap-4 mb-3">
        <div>
          <p className="text-sm text-gray-600">Churn Probability</p>
          <p className="text-lg font-semibold">{(prediction.churnProbability * 100).toFixed(1)}%</p>
        </div>
        <div>
          <p className="text-sm text-gray-600">Time to Churn</p>
          <p className="text-lg font-semibold">{prediction.timeToChurn} days</p>
        </div>
      </div>

      <div className="mb-3">
        <p className="text-sm text-gray-600 mb-1">Primary Risk Factors</p>
        <div className="flex flex-wrap gap-1">
          {prediction.primaryRiskFactors.map((factor, index) => (
            <Badge key={index} variant="outline" className="text-xs">
              {factor}
            </Badge>
          ))}
        </div>
      </div>

      <div className="flex items-center justify-between text-sm">
        <span className="text-gray-600">CLV: ${prediction.customerProfile.lifetimeValue.toFixed(0)}</span>
        <span className="text-gray-600">Confidence: {(prediction.confidence * 100).toFixed(1)}%</span>
      </div>
    </motion.div>
  );
};

const InterventionCard: React.FC<{ strategy: InterventionStrategy }> = ({ strategy }) => {
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'discount': return <Percent className="h-4 w-4" />;
      case 'engagement': return <Heart className="h-4 w-4" />;
      case 'support': return <Shield className="h-4 w-4" />;
      case 'loyalty': return <Award className="h-4 w-4" />;
      default: return <Target className="h-4 w-4" />;
    }
  };

  const getChannelIcon = (channel: string) => {
    switch (channel) {
      case 'email': return <Mail className="h-4 w-4" />;
      case 'sms': return <MessageSquare className="h-4 w-4" />;
      case 'phone': return <Phone className="h-4 w-4" />;
      default: return <Bell className="h-4 w-4" />;
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className="border rounded-lg p-4 hover:shadow-md transition-shadow"
    >
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-2">
          {getTypeIcon(strategy.type)}
          <h4 className="font-medium">{strategy.name}</h4>
        </div>
        <div className="flex items-center gap-1">
          {getChannelIcon(strategy.channel)}
          <span className="text-sm text-gray-600 capitalize">{strategy.channel}</span>
        </div>
      </div>

      <p className="text-sm text-gray-600 mb-3">{strategy.description}</p>

      <div className="grid grid-cols-2 gap-4 mb-3">
        <div>
          <p className="text-sm text-gray-600">Effectiveness</p>
          <p className="text-lg font-semibold">{(strategy.expectedEffectiveness * 100).toFixed(1)}%</p>
        </div>
        <div>
          <p className="text-sm text-gray-600">ROI Estimate</p>
          <p className="text-lg font-semibold">{strategy.roi_estimate.toFixed(1)}x</p>
        </div>
      </div>

      <div className="mb-3">
        <p className="text-sm text-gray-600 mb-1">Personalization</p>
        <div className="bg-gray-50 p-2 rounded text-sm">
          <p className="font-medium">{strategy.personalization.message}</p>
          <p className="text-gray-600">{strategy.personalization.offer}</p>
        </div>
      </div>

      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Badge variant={strategy.implementation.automated ? "default" : "secondary"}>
            {strategy.implementation.automated ? 'Automated' : 'Manual'}
          </Badge>
          {strategy.implementation.approvalRequired && (
            <Badge variant="outline">Approval Required</Badge>
          )}
        </div>
        <span className="text-sm text-gray-600">Cost: ${strategy.cost}</span>
      </div>
    </motion.div>
  );
};

const CampaignCard: React.FC<{ campaign: AutomatedCampaign }> = ({ campaign }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'paused': return 'bg-yellow-100 text-yellow-800';
      case 'completed': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const conversionRate = campaign.performance.sent > 0 ? 
    (campaign.performance.converted / campaign.performance.sent * 100) : 0;
  const retentionRate = campaign.performance.converted > 0 ? 
    (campaign.performance.retained / campaign.performance.converted * 100) : 0;

  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      className="border rounded-lg p-4 hover:shadow-md transition-shadow"
    >
      <div className="flex items-start justify-between mb-3">
        <div>
          <h4 className="font-medium">{campaign.name}</h4>
          <p className="text-sm text-gray-600">{campaign.targetSegment}</p>
        </div>
        <Badge className={getStatusColor(campaign.status)}>
          {campaign.status.toUpperCase()}
        </Badge>
      </div>

      <div className="mb-3">
        <p className="text-sm text-gray-600 mb-1">Trigger</p>
        <p className="text-sm font-medium">{campaign.trigger}</p>
      </div>

      <div className="grid grid-cols-2 gap-4 mb-3">
        <div>
          <p className="text-sm text-gray-600">Conversion Rate</p>
          <p className="text-lg font-semibold">{conversionRate.toFixed(1)}%</p>
        </div>
        <div>
          <p className="text-sm text-gray-600">Retention Rate</p>
          <p className="text-lg font-semibold">{retentionRate.toFixed(1)}%</p>
        </div>
      </div>

      <div className="mb-3">
        <p className="text-sm text-gray-600 mb-1">Performance</p>
        <div className="grid grid-cols-4 gap-2 text-center">
          <div>
            <p className="text-xs text-gray-600">Sent</p>
            <p className="font-semibold">{campaign.performance.sent}</p>
          </div>
          <div>
            <p className="text-xs text-gray-600">Opened</p>
            <p className="font-semibold">{campaign.performance.opened}</p>
          </div>
          <div>
            <p className="text-xs text-gray-600">Clicked</p>
            <p className="font-semibold">{campaign.performance.clicked}</p>
          </div>
          <div>
            <p className="text-xs text-gray-600">Retained</p>
            <p className="font-semibold">{campaign.performance.retained}</p>
          </div>
        </div>
      </div>

      {campaign.abTestConfig.enabled && (
        <div className="mb-3">
          <p className="text-sm text-gray-600 mb-1">A/B Test</p>
          <div className="flex items-center gap-2">
            <Badge variant="outline">
              Winner: {campaign.abTestConfig.winningVariant}
            </Badge>
          </div>
        </div>
      )}

      <div className="flex items-center justify-between text-sm">
        <span className="text-gray-600">Channel: {campaign.channel}</span>
        <span className="text-gray-600">
          Last run: {campaign.lastRun.toLocaleDateString()}
        </span>
      </div>
    </motion.div>
  );
};

export const AdvancedChurnPrediction: React.FC = () => {
  const [selectedRiskLevel, setSelectedRiskLevel] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'probability' | 'timeToChurn' | 'clv'>('probability');

  const predictions = useMemo(() => generateChurnPredictions(), []);
  const models = useMemo(() => generateChurnModels(), []);
  const cohortData = useMemo(() => generateCohortAnalysis(), []);

  const filteredPredictions = useMemo(() => {
    return predictions
      .filter(p => selectedRiskLevel === 'all' || p.riskLevel === selectedRiskLevel)
      .filter(p => p.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                  p.customerEmail.toLowerCase().includes(searchQuery.toLowerCase()))
      .sort((a, b) => {
        switch (sortBy) {
          case 'probability':
            return b.churnProbability - a.churnProbability;
          case 'timeToChurn':
            return a.timeToChurn - b.timeToChurn;
          case 'clv':
            return b.customerProfile.lifetimeValue - a.customerProfile.lifetimeValue;
          default:
            return 0;
        }
      });
  }, [predictions, selectedRiskLevel, searchQuery, sortBy]);

  const riskDistribution = useMemo(() => {
    const distribution = { critical: 0, high: 0, medium: 0, low: 0 };
    predictions.forEach(p => distribution[p.riskLevel]++);
    return Object.entries(distribution).map(([level, count]) => ({
      name: level,
      value: count,
      percentage: (count / predictions.length * 100).toFixed(1)
    }));
  }, [predictions]);

  const churnTrendData = useMemo(() => {
    return Array.from({ length: 30 }, (_, i) => ({
      day: i + 1,
      predictions: Math.floor(Math.random() * 50) + 100,
      interventions: Math.floor(Math.random() * 30) + 20,
      retained: Math.floor(Math.random() * 25) + 15
    }));
  }, []);

  const modelPerformanceData = useMemo(() => {
    return models.map(model => ({
      name: model.name,
      accuracy: model.accuracy * 100,
      precision: model.precision * 100,
      recall: model.recall * 100,
      f1Score: model.f1Score * 100
    }));
  }, [models]);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <AlertTriangle className="h-6 w-6" />
            Advanced Churn Prediction
          </h2>
          <p className="text-gray-600">AI-powered churn prediction with personalized intervention strategies</p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Retrain Model
          </Button>
        </div>
      </div>

      <Tabs defaultValue="predictions" className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="predictions">Risk Predictions</TabsTrigger>
          <TabsTrigger value="interventions">Intervention Strategies</TabsTrigger>
          <TabsTrigger value="campaigns">Automated Campaigns</TabsTrigger>
          <TabsTrigger value="models">Model Performance</TabsTrigger>
          <TabsTrigger value="cohorts">Cohort Analysis</TabsTrigger>
        </TabsList>

        <TabsContent value="predictions">
          <div className="space-y-6">
            {/* Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4 text-blue-600" />
                    <div>
                      <p className="text-sm text-gray-600">Total Customers</p>
                      <p className="text-2xl font-bold">{predictions.length}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <AlertTriangle className="h-4 w-4 text-red-600" />
                    <div>
                      <p className="text-sm text-gray-600">High Risk</p>
                      <p className="text-2xl font-bold">
                        {predictions.filter(p => p.riskLevel === 'critical' || p.riskLevel === 'high').length}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Target className="h-4 w-4 text-green-600" />
                    <div>
                      <p className="text-sm text-gray-600">Avg Confidence</p>
                      <p className="text-2xl font-bold">
                        {(predictions.reduce((acc, p) => acc + p.confidence, 0) / predictions.length * 100).toFixed(1)}%
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <DollarSign className="h-4 w-4 text-yellow-600" />
                    <div>
                      <p className="text-sm text-gray-600">At-Risk Revenue</p>
                      <p className="text-2xl font-bold">
                        ${(predictions
                          .filter(p => p.riskLevel === 'critical' || p.riskLevel === 'high')
                          .reduce((acc, p) => acc + p.customerProfile.lifetimeValue, 0) / 1000
                        ).toFixed(0)}K
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Filters and Search */}
            <div className="flex items-center gap-4">
              <div className="flex-1">
                <Input
                  placeholder="Search customers..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full"
                />
              </div>
              <select
                value={selectedRiskLevel}
                onChange={(e) => setSelectedRiskLevel(e.target.value)}
                className="px-3 py-2 border rounded-md"
              >
                <option value="all">All Risk Levels</option>
                <option value="critical">Critical</option>
                <option value="high">High</option>
                <option value="medium">Medium</option>
                <option value="low">Low</option>
              </select>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="px-3 py-2 border rounded-md"
              >
                <option value="probability">Sort by Probability</option>
                <option value="timeToChurn">Sort by Time to Churn</option>
                <option value="clv">Sort by CLV</option>
              </select>
            </div>

            {/* Risk Distribution Chart */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Risk Distribution</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <RechartsPieChart>
                      <Pie
                        data={riskDistribution}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percentage }) => `${name}: ${percentage}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {riskDistribution.map((entry, index) => (
                          <Cell
                            key={`cell-${index}`}
                            fill={index === 0 ? '#ef4444' : index === 1 ? '#f97316' : index === 2 ? '#eab308' : '#22c55e'}
                          />
                        ))}
                      </Pie>
                      <Tooltip />
                    </RechartsPieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Churn Prediction Trends</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <RechartsLineChart data={churnTrendData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="day" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Line type="monotone" dataKey="predictions" stroke="#8884d8" name="Predictions" />
                      <Line type="monotone" dataKey="interventions" stroke="#82ca9d" name="Interventions" />
                      <Line type="monotone" dataKey="retained" stroke="#ffc658" name="Retained" />
                    </RechartsLineChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>

            {/* Predictions Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredPredictions.map((prediction) => (
                <ChurnPredictionCard
                  key={prediction.customerId}
                  prediction={prediction}
                />
              ))}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="interventions">
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Intervention Strategies</h3>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Create Strategy
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {generateInterventionStrategies().map((strategy) => (
                <InterventionCard key={strategy.id} strategy={strategy} />
              ))}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="campaigns">
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Automated Campaigns</h3>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Create Campaign
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {generateAutomatedCampaigns().map((campaign) => (
                <CampaignCard key={campaign.id} campaign={campaign} />
              ))}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="models">
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Model Performance</h3>
              <Button size="sm">
                <Brain className="h-4 w-4 mr-2" />
                Train New Model
              </Button>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {models.map((model) => (
                <Card key={model.id}>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Cpu className="h-5 w-5" />
                      {model.name}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <span>Status</span>
                        <Badge variant={model.status === 'active' ? 'default' : 'secondary'}>
                          {model.status}
                        </Badge>
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <p className="text-sm text-gray-600">Accuracy</p>
                          <p className="text-lg font-semibold">{(model.accuracy * 100).toFixed(1)}%</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">AUC</p>
                          <p className="text-lg font-semibold">{model.auc.toFixed(3)}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">Precision</p>
                          <p className="text-lg font-semibold">{(model.precision * 100).toFixed(1)}%</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">Recall</p>
                          <p className="text-lg font-semibold">{(model.recall * 100).toFixed(1)}%</p>
                        </div>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600 mb-1">Features</p>
                        <div className="flex flex-wrap gap-1">
                          {model.features.map((feature, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {feature}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Model Comparison</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={400}>
                  <BarChart data={modelPerformanceData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="accuracy" fill="#8884d8" name="Accuracy" />
                    <Bar dataKey="precision" fill="#82ca9d" name="Precision" />
                    <Bar dataKey="recall" fill="#ffc658" name="Recall" />
                    <Bar dataKey="f1Score" fill="#ff7300" name="F1 Score" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="cohorts">
          <div className="space-y-6">
            <h3 className="text-lg font-semibold">Cohort Analysis</h3>

            <Card>
              <CardHeader>
                <CardTitle>Churn Rate by Cohort</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={400}>
                  <RechartsLineChart data={cohortData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="cohort" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Line type="monotone" dataKey="churnRate" stroke="#8884d8" name="Churn Rate" />
                  </RechartsLineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Intervention Success</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={cohortData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="cohort" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="interventionsDeployed" fill="#8884d8" name="Deployed" />
                      <Bar dataKey="interventionSuccess" fill="#82ca9d" name="Successful" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Retained Revenue</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <AreaChart data={cohortData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="cohort" />
                      <YAxis />
                      <Tooltip />
                      <Area type="monotone" dataKey="retainedRevenue" stroke="#8884d8" fill="#8884d8" />
                    </AreaChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AdvancedChurnPrediction;