/**
 * AI-Powered Business Intelligence Engine
 * Automated insight generation, anomaly detection, and intelligent recommendations
 */

import React, { useState, useMemo, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Brain, 
  Zap, 
  TrendingUp, 
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  Target,
  Eye,
  MessageSquare,
  Lightbulb,
  BarChart3,
  PieChart,
  Activity,
  Search,
  Filter,
  Download,
  Share,
  Settings,
  Play,
  Pause,
  RefreshCw,
  Calendar,
  Clock,
  Users,
  DollarSign,
  ShoppingCart,
  Package,
  Star,
  ArrowUpRight,
  ArrowDownRight,
  Minus,
  Info,
  ExternalLink,
  Database,
  Cpu,
  Network,
  Globe,
  Lock,
  Unlock
} from 'lucide-react';
import { 
  LineChart, 
  Line, 
  AreaChart, 
  Area, 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  ScatterChart,
  Scatter
} from 'recharts';

// Types
interface AIInsight {
  id: string;
  type: 'anomaly' | 'trend' | 'opportunity' | 'risk' | 'recommendation';
  title: string;
  description: string;
  confidence: number;
  impact: 'low' | 'medium' | 'high' | 'critical';
  urgency: 'low' | 'medium' | 'high' | 'urgent';
  category: 'revenue' | 'customers' | 'products' | 'marketing' | 'operations';
  dataPoints: any[];
  evidence: string[];
  recommendations: string[];
  automatedActions: string[];
  timestamp: Date;
  status: 'new' | 'acknowledged' | 'in_progress' | 'implemented' | 'dismissed';
  assignee?: string;
  estimatedValue?: number;
  timeframe: string;
  relatedMetrics: string[];
}

interface AnomalyDetection {
  id: string;
  metric: string;
  timestamp: Date;
  expectedValue: number;
  actualValue: number;
  deviation: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
  pattern: 'spike' | 'drop' | 'trend_change' | 'seasonality_break' | 'outlier';
  context: {
    historicalAverage: number;
    seasonalTrend: string;
    externalFactors: string[];
    correlatedEvents: string[];
  };
  explanation: string;
  suggestedActions: string[];
  autoResolved: boolean;
}

interface PredictiveModel {
  id: string;
  name: string;
  type: 'regression' | 'classification' | 'clustering' | 'time_series' | 'deep_learning';
  purpose: string;
  accuracy: number;
  lastTrained: Date;
  dataPoints: number;
  features: string[];
  status: 'training' | 'active' | 'deprecated' | 'error';
  predictions: ModelPrediction[];
  performance: {
    precision: number;
    recall: number;
    f1Score: number;
    mse: number;
    mae: number;
  };
  driftDetection: {
    detected: boolean;
    severity: string;
    lastCheck: Date;
  };
}

interface ModelPrediction {
  id: string;
  modelId: string;
  timestamp: Date;
  inputData: Record<string, any>;
  prediction: any;
  confidence: number;
  explanation: string[];
  featureImportance: Record<string, number>;
}

interface IntelligentRecommendation {
  id: string;
  category: 'pricing' | 'inventory' | 'marketing' | 'customer_experience' | 'operations';
  title: string;
  description: string;
  rationale: string;
  expectedImpact: {
    revenue: number;
    conversion: number;
    efficiency: number;
  };
  confidence: number;
  complexity: 'low' | 'medium' | 'high';
  timeToImplement: string;
  resources: string[];
  risks: string[];
  successMetrics: string[];
  aiGenerated: boolean;
  basedOnModels: string[];
}

interface AIBusinessIntelligenceProps {
  onInsightAction?: (insightId: string, action: string) => void;
  onModelRetrain?: (modelId: string) => void;
  onRecommendationImplement?: (recommendationId: string) => void;
  className?: string;
}

// Mock data generation
const generateAIInsights = (): AIInsight[] => [
  {
    id: 'insight-revenue-spike',
    type: 'opportunity',
    title: 'Unusual Revenue Spike in Electronics Category',
    description: 'Electronics category showing 34% above normal revenue for the past 3 days. Pattern suggests viral social media mention driving traffic.',
    confidence: 92,
    impact: 'high',
    urgency: 'medium',
    category: 'revenue',
    dataPoints: [],
    evidence: [
      'Electronics revenue increased from $12K to $16K daily average',
      'Traffic from social media up 156% in same period',
      'Specific products: wireless earbuds (+89%), gaming keyboards (+67%)',
      'Conversion rate remained stable at 3.2%'
    ],
    recommendations: [
      'Increase inventory for trending electronics products',
      'Launch targeted social media campaigns to capitalize on momentum',
      'Create product bundles with trending items',
      'Monitor competitor pricing to maintain advantage'
    ],
    automatedActions: [
      'Increased ad spend on electronics campaigns by 25%',
      'Triggered inventory alerts for top-performing products',
      'Enabled dynamic pricing optimization'
    ],
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
    status: 'new',
    estimatedValue: 45000,
    timeframe: 'Next 7 days',
    relatedMetrics: ['revenue', 'conversion_rate', 'social_traffic']
  },
  {
    id: 'insight-churn-risk',
    type: 'risk',
    title: 'High-Value Customer Churn Risk Detected',
    description: '23 high-value customers (LTV >$2000) showing early churn signals. Behavioral patterns indicate 67% probability of churn within 30 days.',
    confidence: 88,
    impact: 'critical',
    urgency: 'urgent',
    category: 'customers',
    dataPoints: [],
    evidence: [
      'Decreased login frequency: 3x per week → 1x per week',
      'Reduced purchase frequency: 2x per month → 1x per month',
      'Lower engagement with marketing emails: 45% → 12% open rate',
      'Increased support ticket volume with billing inquiries'
    ],
    recommendations: [
      'Launch immediate retention campaign with personalized offers',
      'Assign dedicated customer success manager to at-risk accounts',
      'Offer exclusive early access to new products',
      'Provide billing consultation and optimization'
    ],
    automatedActions: [
      'Triggered retention email sequence',
      'Applied automatic loyalty bonus to accounts',
      'Escalated to customer success team'
    ],
    timestamp: new Date(Date.now() - 30 * 60 * 1000),
    status: 'acknowledged',
    assignee: 'Customer Success Team',
    estimatedValue: -46000,
    timeframe: 'Next 30 days',
    relatedMetrics: ['customer_ltv', 'churn_rate', 'engagement_score']
  },
  {
    id: 'insight-market-basket',
    type: 'recommendation',
    title: 'Cross-sell Opportunity: Home & Garden + Electronics',
    description: 'AI analysis reveals 73% of customers buying smart home devices also purchase garden automation within 3 months. Significant untapped cross-sell potential.',
    confidence: 85,
    impact: 'medium',
    urgency: 'low',
    category: 'products',
    dataPoints: [],
    evidence: [
      'Market basket analysis of 50K+ transactions',
      'Smart home buyers: 73% purchase rate for garden automation',
      'Average order value increases by $234 with cross-sell',
      'Current cross-sell capture rate: only 23%'
    ],
    recommendations: [
      'Create smart home + garden automation product bundles',
      'Implement recommendation engine on product pages',
      'Design email campaigns highlighting complementary products',
      'Train sales team on cross-sell opportunities'
    ],
    automatedActions: [
      'Updated recommendation algorithms',
      'Created dynamic product bundles',
      'Enabled cross-sell prompts in checkout'
    ],
    timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),
    status: 'in_progress',
    assignee: 'Product Team',
    estimatedValue: 67000,
    timeframe: 'Next 90 days',
    relatedMetrics: ['cross_sell_rate', 'avg_order_value', 'product_affinity']
  },
  {
    id: 'insight-pricing-anomaly',
    type: 'anomaly',
    title: 'Pricing Algorithm Malfunction Detected',
    description: 'Dynamic pricing algorithm showing erratic behavior for mid-tier products. Prices fluctuating beyond optimal range, potentially impacting conversion.',
    confidence: 94,
    impact: 'high',
    urgency: 'high',
    category: 'operations',
    dataPoints: [],
    evidence: [
      'Price volatility increased 340% for mid-tier products',
      'Conversion rate dropped 18% in affected categories',
      'Algorithm confidence scores below 70% threshold',
      'Manual price overrides increased 5x'
    ],
    recommendations: [
      'Temporarily disable dynamic pricing for affected products',
      'Retrain pricing model with recent market data',
      'Implement stricter price change limits',
      'Conduct pricing algorithm audit'
    ],
    automatedActions: [
      'Applied emergency price stability rules',
      'Triggered algorithm health alerts',
      'Reverted to manual pricing for critical products'
    ],
    timestamp: new Date(Date.now() - 45 * 60 * 1000),
    status: 'implemented',
    assignee: 'Data Science Team',
    estimatedValue: -12000,
    timeframe: 'Immediate',
    relatedMetrics: ['pricing_volatility', 'conversion_rate', 'algorithm_confidence']
  }
];

const generateAnomalyDetections = (): AnomalyDetection[] => [
  {
    id: 'anomaly-traffic-spike',
    metric: 'Website Traffic',
    timestamp: new Date(Date.now() - 30 * 60 * 1000),
    expectedValue: 5420,
    actualValue: 8750,
    deviation: 61.4,
    severity: 'medium',
    pattern: 'spike',
    context: {
      historicalAverage: 5200,
      seasonalTrend: 'Typical Friday afternoon pattern',
      externalFactors: ['Social media viral post', 'Influencer mention'],
      correlatedEvents: ['Email campaign sent 2 hours ago', 'Product featured in tech blog']
    },
    explanation: 'Unusual traffic spike detected 61% above expected levels. Analysis suggests external viral content driving organic traffic. Pattern consistent with social media-driven events.',
    suggestedActions: [
      'Monitor server capacity and performance',
      'Prepare additional inventory for trending products',
      'Activate surge pricing if appropriate',
      'Capture visitor emails for future marketing'
    ],
    autoResolved: false
  },
  {
    id: 'anomaly-conversion-drop',
    metric: 'Conversion Rate',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
    expectedValue: 3.2,
    actualValue: 2.1,
    deviation: -34.4,
    severity: 'high',
    pattern: 'drop',
    context: {
      historicalAverage: 3.15,
      seasonalTrend: 'Stable weekday performance',
      externalFactors: ['Competitor launched sale', 'Payment gateway issues reported'],
      correlatedEvents: ['Site performance degradation', 'Checkout process changes deployed']
    },
    explanation: 'Significant conversion rate drop detected. Analysis points to technical issues in checkout process affecting user experience. Immediate investigation required.',
    suggestedActions: [
      'Investigate checkout process technical issues',
      'Review recent code deployments',
      'Monitor payment gateway status',
      'Implement temporary conversion optimization measures'
    ],
    autoResolved: false
  },
  {
    id: 'anomaly-refund-increase',
    metric: 'Refund Rate',
    timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000),
    expectedValue: 2.8,
    actualValue: 5.2,
    deviation: 85.7,
    severity: 'critical',
    pattern: 'spike',
    context: {
      historicalAverage: 2.5,
      seasonalTrend: 'Post-holiday return period',
      externalFactors: ['Quality issues with recent batch', 'Shipping delays'],
      correlatedEvents: ['Supplier quality incident', 'Weather-related shipping delays']
    },
    explanation: 'Critical spike in refund requests indicates potential product quality or fulfillment issues. Requires immediate attention to prevent brand damage.',
    suggestedActions: [
      'Investigate product quality issues',
      'Contact affected customers proactively',
      'Review supplier quality controls',
      'Implement enhanced quality assurance'
    ],
    autoResolved: false
  }
];

const generatePredictiveModels = (): PredictiveModel[] => [
  {
    id: 'model-clv-prediction',
    name: 'Customer Lifetime Value Predictor',
    type: 'regression',
    purpose: 'Predict customer lifetime value for acquisition and retention optimization',
    accuracy: 87.3,
    lastTrained: new Date(Date.now() - 24 * 60 * 60 * 1000),
    dataPoints: 125000,
    features: [
      'First purchase value',
      'Purchase frequency',
      'Product categories',
      'Customer demographics',
      'Engagement metrics',
      'Support interactions'
    ],
    status: 'active',
    predictions: [],
    performance: {
      precision: 0.89,
      recall: 0.85,
      f1Score: 0.87,
      mse: 245.6,
      mae: 187.3
    },
    driftDetection: {
      detected: false,
      severity: 'low',
      lastCheck: new Date(Date.now() - 2 * 60 * 60 * 1000)
    }
  },
  {
    id: 'model-demand-forecasting',
    name: 'Product Demand Forecaster',
    type: 'time_series',
    purpose: 'Forecast product demand for inventory optimization and procurement planning',
    accuracy: 82.1,
    lastTrained: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
    dataPoints: 200000,
    features: [
      'Historical sales data',
      'Seasonal patterns',
      'Marketing campaigns',
      'Economic indicators',
      'Weather data',
      'Competitor pricing'
    ],
    status: 'active',
    predictions: [],
    performance: {
      precision: 0.84,
      recall: 0.81,
      f1Score: 0.82,
      mse: 156.8,
      mae: 123.4
    },
    driftDetection: {
      detected: true,
      severity: 'medium',
      lastCheck: new Date(Date.now() - 1 * 60 * 60 * 1000)
    }
  },
  {
    id: 'model-churn-prediction',
    name: 'Customer Churn Predictor',
    type: 'classification',
    purpose: 'Identify customers at risk of churning for proactive retention',
    accuracy: 91.7,
    lastTrained: new Date(Date.now() - 12 * 60 * 60 * 1000),
    dataPoints: 85000,
    features: [
      'Purchase recency',
      'Purchase frequency',
      'Customer tenure',
      'Support tickets',
      'Email engagement',
      'Product returns'
    ],
    status: 'active',
    predictions: [],
    performance: {
      precision: 0.93,
      recall: 0.89,
      f1Score: 0.91,
      mse: 0.08,
      mae: 0.06
    },
    driftDetection: {
      detected: false,
      severity: 'low',
      lastCheck: new Date(Date.now() - 30 * 60 * 1000)
    }
  },
  {
    id: 'model-price-optimization',
    name: 'Dynamic Pricing Optimizer',
    type: 'deep_learning',
    purpose: 'Optimize product pricing for maximum revenue and profit margins',
    accuracy: 79.4,
    lastTrained: new Date(Date.now() - 6 * 60 * 60 * 1000),
    dataPoints: 300000,
    features: [
      'Historical pricing data',
      'Competitor prices',
      'Demand elasticity',
      'Inventory levels',
      'Market conditions',
      'Customer segments'
    ],
    status: 'training',
    predictions: [],
    performance: {
      precision: 0.81,
      recall: 0.78,
      f1Score: 0.79,
      mse: 234.5,
      mae: 189.7
    },
    driftDetection: {
      detected: true,
      severity: 'high',
      lastCheck: new Date(Date.now() - 15 * 60 * 1000)
    }
  }
];

const generateIntelligentRecommendations = (): IntelligentRecommendation[] => [
  {
    id: 'rec-personalized-pricing',
    category: 'pricing',
    title: 'Implement Personalized Pricing Strategy',
    description: 'Deploy customer segment-based pricing to increase revenue while maintaining customer satisfaction',
    rationale: 'Analysis shows 23% of customers are price-insensitive and willing to pay premium for convenience. Segmented pricing could increase revenue by 8-12%.',
    expectedImpact: {
      revenue: 156000,
      conversion: 4.2,
      efficiency: 12.5
    },
    confidence: 87,
    complexity: 'high',
    timeToImplement: '6-8 weeks',
    resources: ['Data Science Team', 'Product Team', 'Legal Review'],
    risks: [
      'Customer perception of unfairness',
      'Potential regulatory compliance issues',
      'Implementation complexity'
    ],
    successMetrics: [
      'Revenue per customer increase >8%',
      'Customer satisfaction score >4.2',
      'Conversion rate maintenance'
    ],
    aiGenerated: true,
    basedOnModels: ['price-optimization', 'customer-segmentation']
  },
  {
    id: 'rec-inventory-optimization',
    category: 'inventory',
    title: 'AI-Driven Inventory Rebalancing',
    description: 'Rebalance inventory allocation based on predictive demand modeling and regional preferences',
    rationale: 'Current inventory allocation shows 15% mismatch with predicted demand patterns. Rebalancing could reduce stockouts by 34% and overstock by 28%.',
    expectedImpact: {
      revenue: 89000,
      conversion: 2.8,
      efficiency: 34.5
    },
    confidence: 92,
    complexity: 'medium',
    timeToImplement: '3-4 weeks',
    resources: ['Supply Chain Team', 'Data Analytics', 'Warehouse Operations'],
    risks: [
      'Short-term disruption during rebalancing',
      'Transportation costs increase',
      'Supplier relationship impacts'
    ],
    successMetrics: [
      'Stockout reduction >30%',
      'Overstock reduction >25%',
      'Inventory turnover improvement >15%'
    ],
    aiGenerated: true,
    basedOnModels: ['demand-forecasting', 'inventory-optimization']
  },
  {
    id: 'rec-customer-experience',
    category: 'customer_experience',
    title: 'Proactive Customer Support Initiative',
    description: 'Deploy predictive customer support to address issues before they escalate to complaints',
    rationale: 'ML models can predict 78% of customer issues 2-3 days before customers contact support. Proactive intervention could improve satisfaction by 25%.',
    expectedImpact: {
      revenue: 45000,
      conversion: 1.2,
      efficiency: 28.3
    },
    confidence: 81,
    complexity: 'medium',
    timeToImplement: '4-6 weeks',
    resources: ['Customer Support', 'ML Engineering', 'CRM Integration'],
    risks: [
      'Customer privacy concerns',
      'False positive predictions',
      'Support team capacity'
    ],
    successMetrics: [
      'Customer satisfaction increase >20%',
      'Support ticket volume reduction >15%',
      'Customer retention improvement >5%'
    ],
    aiGenerated: true,
    basedOnModels: ['customer-behavior', 'support-prediction']
  }
];

// AI Insight card component
const AIInsightCard: React.FC<{
  insight: AIInsight;
  onAction?: (action: string) => void;
}> = ({ insight, onAction }) => {
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'anomaly': return <AlertTriangle className="w-5 h-5 text-orange-600" />;
      case 'trend': return <TrendingUp className="w-5 h-5 text-blue-600" />;
      case 'opportunity': return <Target className="w-5 h-5 text-green-600" />;
      case 'risk': return <AlertTriangle className="w-5 h-5 text-red-600" />;
      case 'recommendation': return <Lightbulb className="w-5 h-5 text-purple-600" />;
      default: return <Info className="w-5 h-5 text-gray-600" />;
    }
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'critical': return 'border-red-500 bg-red-50';
      case 'high': return 'border-orange-500 bg-orange-50';
      case 'medium': return 'border-yellow-500 bg-yellow-50';
      case 'low': return 'border-green-500 bg-green-50';
      default: return 'border-gray-500 bg-gray-50';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'new': return 'bg-blue-100 text-blue-800';
      case 'acknowledged': return 'bg-yellow-100 text-yellow-800';
      case 'in_progress': return 'bg-purple-100 text-purple-800';
      case 'implemented': return 'bg-green-100 text-green-800';
      case 'dismissed': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card className={`hover:shadow-lg transition-shadow border-2 ${getImpactColor(insight.impact)}`}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3">
            {getTypeIcon(insight.type)}
            <div className="flex-1">
              <CardTitle className="text-lg">{insight.title}</CardTitle>
              <p className="text-sm text-gray-600 mt-1">{insight.description}</p>
            </div>
          </div>
          <div className="flex flex-col space-y-1">
            <Badge className={getStatusColor(insight.status)}>
              {insight.status.replace('_', ' ')}
            </Badge>
            <Badge variant="outline">
              {insight.confidence}% confidence
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Impact:</span>
              <span className={`ml-2 font-medium ${
                insight.impact === 'critical' ? 'text-red-600' :
                insight.impact === 'high' ? 'text-orange-600' :
                insight.impact === 'medium' ? 'text-yellow-600' :
                'text-green-600'
              }`}>
                {insight.impact.toUpperCase()}
              </span>
            </div>
            <div>
              <span className="text-gray-600">Urgency:</span>
              <span className={`ml-2 font-medium ${
                insight.urgency === 'urgent' ? 'text-red-600' :
                insight.urgency === 'high' ? 'text-orange-600' :
                insight.urgency === 'medium' ? 'text-yellow-600' :
                'text-green-600'
              }`}>
                {insight.urgency.toUpperCase()}
              </span>
            </div>
            <div>
              <span className="text-gray-600">Category:</span>
              <span className="ml-2 font-medium capitalize">{insight.category}</span>
            </div>
            <div>
              <span className="text-gray-600">Timeframe:</span>
              <span className="ml-2 font-medium">{insight.timeframe}</span>
            </div>
          </div>

          {insight.estimatedValue && (
            <div className="p-3 bg-blue-50 rounded-lg">
              <div className="text-sm font-medium text-blue-900">
                Estimated Impact: 
                <span className={`ml-2 text-lg font-bold ${
                  insight.estimatedValue > 0 ? 'text-green-600' : 'text-red-600'
                }`}>
                  {insight.estimatedValue > 0 ? '+' : ''}${Math.abs(insight.estimatedValue).toLocaleString()}
                </span>
              </div>
            </div>
          )}

          <div>
            <h4 className="font-medium text-sm mb-2">Evidence:</h4>
            <ul className="space-y-1">
              {insight.evidence.slice(0, 3).map((evidence, index) => (
                <li key={index} className="text-xs text-gray-600 flex items-start space-x-2">
                  <div className="w-1 h-1 bg-gray-400 rounded-full mt-2 flex-shrink-0" />
                  <span>{evidence}</span>
                </li>
              ))}
            </ul>
          </div>

          <div>
            <h4 className="font-medium text-sm mb-2">Recommendations:</h4>
            <ul className="space-y-1">
              {insight.recommendations.slice(0, 2).map((rec, index) => (
                <li key={index} className="text-xs text-gray-600 flex items-start space-x-2">
                  <Lightbulb className="w-3 h-3 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span>{rec}</span>
                </li>
              ))}
            </ul>
          </div>

          {insight.automatedActions.length > 0 && (
            <div className="p-3 bg-green-50 rounded-lg">
              <h4 className="font-medium text-sm mb-2 text-green-900">Automated Actions Taken:</h4>
              <ul className="space-y-1">
                {insight.automatedActions.map((action, index) => (
                  <li key={index} className="text-xs text-green-700 flex items-start space-x-2">
                    <CheckCircle className="w-3 h-3 text-green-600 mt-0.5 flex-shrink-0" />
                    <span>{action}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          <div className="flex justify-between items-center pt-3 border-t">
            <div className="text-xs text-gray-500">
              {insight.timestamp.toLocaleString()}
              {insight.assignee && (
                <span className="ml-2">• Assigned to {insight.assignee}</span>
              )}
            </div>
            <div className="flex space-x-2">
              <Button variant="outline" size="sm" onClick={() => onAction?.('view')}>
                <Eye className="w-4 h-4" />
              </Button>
              <Button variant="outline" size="sm" onClick={() => onAction?.('implement')}>
                <Play className="w-4 h-4" />
              </Button>
              <Button variant="outline" size="sm" onClick={() => onAction?.('dismiss')}>
                <Minus className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Anomaly detection card
const AnomalyCard: React.FC<{
  anomaly: AnomalyDetection;
}> = ({ anomaly }) => {
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-600 bg-red-100';
      case 'high': return 'text-orange-600 bg-orange-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getPatternIcon = (pattern: string) => {
    switch (pattern) {
      case 'spike': return <ArrowUpRight className="w-4 h-4" />;
      case 'drop': return <ArrowDownRight className="w-4 h-4" />;
      case 'trend_change': return <TrendingUp className="w-4 h-4" />;
      case 'seasonality_break': return <Calendar className="w-4 h-4" />;
      case 'outlier': return <Target className="w-4 h-4" />;
      default: return <Activity className="w-4 h-4" />;
    }
  };

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {getPatternIcon(anomaly.pattern)}
            <CardTitle className="text-lg">{anomaly.metric}</CardTitle>
          </div>
          <Badge className={getSeverityColor(anomaly.severity)}>
            {anomaly.severity}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <span className="text-sm text-gray-600">Expected:</span>
              <div className="font-medium">{anomaly.expectedValue.toLocaleString()}</div>
            </div>
            <div>
              <span className="text-sm text-gray-600">Actual:</span>
              <div className={`font-medium ${
                anomaly.deviation > 0 ? 'text-red-600' : 'text-green-600'
              }`}>
                {anomaly.actualValue.toLocaleString()}
              </div>
            </div>
          </div>

          <div className="p-3 bg-gray-50 rounded-lg">
            <div className="text-sm font-medium mb-2">Deviation</div>
            <div className={`text-2xl font-bold ${
              Math.abs(anomaly.deviation) > 50 ? 'text-red-600' :
              Math.abs(anomaly.deviation) > 25 ? 'text-orange-600' :
              'text-yellow-600'
            }`}>
              {anomaly.deviation > 0 ? '+' : ''}{anomaly.deviation.toFixed(1)}%
            </div>
          </div>

          <div>
            <h4 className="text-sm font-medium mb-2">Explanation</h4>
            <p className="text-sm text-gray-600">{anomaly.explanation}</p>
          </div>

          <div>
            <h4 className="text-sm font-medium mb-2">Context</h4>
            <div className="space-y-1 text-xs">
              <div><span className="font-medium">Historical Avg:</span> {anomaly.context.historicalAverage}</div>
              <div><span className="font-medium">Seasonal Trend:</span> {anomaly.context.seasonalTrend}</div>
              {anomaly.context.externalFactors.length > 0 && (
                <div><span className="font-medium">External Factors:</span> {anomaly.context.externalFactors.join(', ')}</div>
              )}
            </div>
          </div>

          <div className="flex justify-between items-center pt-3 border-t">
            <span className="text-xs text-gray-500">
              {anomaly.timestamp.toLocaleString()}
            </span>
            <div className="flex space-x-2">
              <Button variant="outline" size="sm">
                <Eye className="w-4 h-4" />
              </Button>
              <Button variant="outline" size="sm">
                <Settings className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Model performance card
const ModelCard: React.FC<{
  model: PredictiveModel;
  onRetrain?: () => void;
}> = ({ model, onRetrain }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'training': return 'bg-blue-100 text-blue-800';
      case 'deprecated': return 'bg-gray-100 text-gray-800';
      case 'error': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getAccuracyColor = (accuracy: number) => {
    if (accuracy >= 90) return 'text-green-600';
    if (accuracy >= 80) return 'text-yellow-600';
    if (accuracy >= 70) return 'text-orange-600';
    return 'text-red-600';
  };

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg">{model.name}</CardTitle>
            <p className="text-sm text-gray-600 mt-1">{model.purpose}</p>
          </div>
          <Badge className={getStatusColor(model.status)}>
            {model.status}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <span className="text-sm text-gray-600">Accuracy:</span>
              <div className={`text-2xl font-bold ${getAccuracyColor(model.accuracy)}`}>
                {model.accuracy.toFixed(1)}%
              </div>
            </div>
            <div>
              <span className="text-sm text-gray-600">Data Points:</span>
              <div className="font-medium">{model.dataPoints.toLocaleString()}</div>
            </div>
          </div>

          <div>
            <span className="text-sm text-gray-600">Type:</span>
            <Badge variant="outline" className="ml-2 capitalize">
              {model.type.replace('_', ' ')}
            </Badge>
          </div>

          <div>
            <h4 className="text-sm font-medium mb-2">Performance Metrics</h4>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div>Precision: <span className="font-medium">{(model.performance.precision * 100).toFixed(1)}%</span></div>
              <div>Recall: <span className="font-medium">{(model.performance.recall * 100).toFixed(1)}%</span></div>
              <div>F1 Score: <span className="font-medium">{(model.performance.f1Score * 100).toFixed(1)}%</span></div>
              <div>MAE: <span className="font-medium">{model.performance.mae.toFixed(1)}</span></div>
            </div>
          </div>

          {model.driftDetection.detected && (
            <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="flex items-center space-x-2">
                <AlertTriangle className="w-4 h-4 text-yellow-600" />
                <span className="text-sm font-medium text-yellow-800">
                  Data drift detected ({model.driftDetection.severity})
                </span>
              </div>
              <p className="text-xs text-yellow-700 mt-1">
                Model may need retraining to maintain accuracy
              </p>
            </div>
          )}

          <div className="flex justify-between items-center pt-3 border-t">
            <span className="text-xs text-gray-500">
              Last trained: {model.lastTrained.toLocaleDateString()}
            </span>
            <div className="flex space-x-2">
              <Button variant="outline" size="sm">
                <Eye className="w-4 h-4" />
              </Button>
              <Button variant="outline" size="sm" onClick={onRetrain}>
                <RefreshCw className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Main component
const AIBusinessIntelligence: React.FC<AIBusinessIntelligenceProps> = ({
  onInsightAction,
  onModelRetrain,
  onRecommendationImplement,
  className = ''
}) => {
  const [activeTab, setActiveTab] = useState('insights');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<string>('all');
  
  const insights = useMemo(() => generateAIInsights(), []);
  const anomalies = useMemo(() => generateAnomalyDetections(), []);
  const models = useMemo(() => generatePredictiveModels(), []);
  const recommendations = useMemo(() => generateIntelligentRecommendations(), []);

  const filteredInsights = useMemo(() => {
    let filtered = insights;
    
    if (searchTerm) {
      filtered = filtered.filter(insight =>
        insight.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        insight.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        insight.category.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    
    if (filterType !== 'all') {
      filtered = filtered.filter(insight => insight.type === filterType);
    }
    
    return filtered.sort((a, b) => {
      // Sort by urgency and impact
      const urgencyWeight = { urgent: 4, high: 3, medium: 2, low: 1 };
      const impactWeight = { critical: 4, high: 3, medium: 2, low: 1 };
      
      const aScore = urgencyWeight[a.urgency] + impactWeight[a.impact];
      const bScore = urgencyWeight[b.urgency] + impactWeight[b.impact];
      
      return bScore - aScore;
    });
  }, [insights, searchTerm, filterType]);

  const handleInsightAction = useCallback((insightId: string, action: string) => {
    onInsightAction?.(insightId, action);
    console.log(`Action ${action} on insight ${insightId}`);
  }, [onInsightAction]);

  const stats = useMemo(() => {
    const criticalInsights = insights.filter(i => i.impact === 'critical').length;
    const activeModels = models.filter(m => m.status === 'active').length;
    const detectedAnomalies = anomalies.length;
    const avgConfidence = insights.reduce((sum, i) => sum + i.confidence, 0) / insights.length;
    
    return { criticalInsights, activeModels, detectedAnomalies, avgConfidence };
  }, [insights, models, anomalies]);

  return (
    <div className={`space-y-6 ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Brain className="w-6 h-6 text-purple-600" />
          <h2 className="text-2xl font-bold">AI Business Intelligence</h2>
          <Badge className="bg-purple-100 text-purple-800">Phase 10</Badge>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            Export Report
          </Button>
          <Button variant="outline">
            <Settings className="w-4 h-4 mr-2" />
            Configure AI
          </Button>
        </div>
      </div>

      {/* AI Intelligence Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Critical Insights</p>
                <p className="text-3xl font-bold text-red-600">{stats.criticalInsights}</p>
                <p className="text-xs text-red-600 mt-1">Require immediate attention</p>
              </div>
              <AlertTriangle className="w-8 h-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Active Models</p>
                <p className="text-3xl font-bold text-green-600">{stats.activeModels}</p>
                <p className="text-xs text-green-600 mt-1">ML models running</p>
              </div>
              <Cpu className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Anomalies Detected</p>
                <p className="text-3xl font-bold text-orange-600">{stats.detectedAnomalies}</p>
                <p className="text-xs text-orange-600 mt-1">In last 24 hours</p>
              </div>
              <Activity className="w-8 h-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Avg Confidence</p>
                <p className="text-3xl font-bold text-blue-600">{stats.avgConfidence.toFixed(1)}%</p>
                <p className="text-xs text-blue-600 mt-1">AI prediction accuracy</p>
              </div>
              <Target className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="insights">AI Insights</TabsTrigger>
          <TabsTrigger value="anomalies">Anomaly Detection</TabsTrigger>
          <TabsTrigger value="models">ML Models</TabsTrigger>
          <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
        </TabsList>

        <TabsContent value="insights" className="space-y-6">
          <div className="flex items-center space-x-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search insights..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              className="p-2 border rounded-md text-sm"
            >
              <option value="all">All Types</option>
              <option value="anomaly">Anomalies</option>
              <option value="trend">Trends</option>
              <option value="opportunity">Opportunities</option>
              <option value="risk">Risks</option>
              <option value="recommendation">Recommendations</option>
            </select>
            <Button variant="outline">
              <Filter className="w-4 h-4 mr-2" />
              Filter
            </Button>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {filteredInsights.map((insight, index) => (
              <motion.div
                key={insight.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <AIInsightCard
                  insight={insight}
                  onAction={(action) => handleInsightAction(insight.id, action)}
                />
              </motion.div>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="anomalies" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {anomalies.map((anomaly, index) => (
              <motion.div
                key={anomaly.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <AnomalyCard anomaly={anomaly} />
              </motion.div>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="models" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {models.map((model, index) => (
              <motion.div
                key={model.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <ModelCard
                  model={model}
                  onRetrain={() => onModelRetrain?.(model.id)}
                />
              </motion.div>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="recommendations" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {recommendations.map((rec, index) => (
              <motion.div
                key={rec.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Card className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div>
                        <CardTitle className="text-lg">{rec.title}</CardTitle>
                        <p className="text-sm text-gray-600 mt-1">{rec.description}</p>
                      </div>
                      <Badge className="bg-purple-100 text-purple-800 capitalize">
                        {rec.category.replace('_', ' ')}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <h4 className="text-sm font-medium mb-2">Expected Impact:</h4>
                        <div className="grid grid-cols-3 gap-2 text-xs">
                          <div className="text-center p-2 bg-green-50 rounded">
                            <div className="font-bold text-green-600">+${rec.expectedImpact.revenue.toLocaleString()}</div>
                            <div className="text-green-600">Revenue</div>
                          </div>
                          <div className="text-center p-2 bg-blue-50 rounded">
                            <div className="font-bold text-blue-600">+{rec.expectedImpact.conversion}%</div>
                            <div className="text-blue-600">Conversion</div>
                          </div>
                          <div className="text-center p-2 bg-purple-50 rounded">
                            <div className="font-bold text-purple-600">+{rec.expectedImpact.efficiency}%</div>
                            <div className="text-purple-600">Efficiency</div>
                          </div>
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-gray-600">Confidence:</span>
                          <span className="ml-2 font-medium">{rec.confidence}%</span>
                        </div>
                        <div>
                          <span className="text-gray-600">Complexity:</span>
                          <span className={`ml-2 font-medium ${
                            rec.complexity === 'high' ? 'text-red-600' :
                            rec.complexity === 'medium' ? 'text-yellow-600' :
                            'text-green-600'
                          }`}>
                            {rec.complexity.toUpperCase()}
                          </span>
                        </div>
                        <div>
                          <span className="text-gray-600">Timeline:</span>
                          <span className="ml-2 font-medium">{rec.timeToImplement}</span>
                        </div>
                        <div>
                          <span className="text-gray-600">AI Generated:</span>
                          <span className="ml-2 font-medium">{rec.aiGenerated ? 'Yes' : 'No'}</span>
                        </div>
                      </div>

                      <div>
                        <h4 className="text-sm font-medium mb-2">Rationale:</h4>
                        <p className="text-xs text-gray-600">{rec.rationale}</p>
                      </div>

                      <div className="flex justify-end space-x-2 pt-3 border-t">
                        <Button variant="outline" size="sm">
                          <Eye className="w-4 h-4 mr-1" />
                          Details
                        </Button>
                        <Button 
                          size="sm" 
                          onClick={() => onRecommendationImplement?.(rec.id)}
                        >
                          <Play className="w-4 h-4 mr-1" />
                          Implement
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AIBusinessIntelligence;
export { type AIInsight, type AnomalyDetection, type PredictiveModel, type IntelligentRecommendation };