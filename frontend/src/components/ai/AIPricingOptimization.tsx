/**
 * AI-Driven Pricing Optimization
 * Advanced machine learning pricing strategies with dynamic optimization and A/B testing
 */

import React, { useState, useMemo, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { motion } from 'framer-motion';
import { 
  DollarSign, 
  TrendingUp, 
  TrendingDown,
  Target,
  Zap,
  Brain,
  BarChart3,
  LineChart,
  Activity,
  AlertTriangle,
  CheckCircle,
  Eye,
  Settings,
  Download,
  Upload,
  Share,
  Filter,
  Search,
  Play,
  Pause,
  RefreshCw,
  Calendar,
  Clock,
  Users,
  ShoppingCart,
  Package,
  Tag,
  Percent,
  Hash,
  ArrowUpRight,
  ArrowDownRight,
  Minus,
  Plus,
  Award,
  Star,
  Lightbulb,
  Database,
  Cpu,
  Network,
  Globe,
  Layers,
  RotateCcw,
  FastForward
} from 'lucide-react';
import { 
  <PERSON><PERSON><PERSON> as RechartsLineChart, 
  Line, 
  AreaChart, 
  Area, 
  BarChart, 
  Bar, 
  ScatterChart,
  Scatter,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  Cell,
  PieChart,
  Pie
} from 'recharts';

// Types
interface PricingRecommendation {
  productId: string;
  productName: string;
  category: string;
  currentPrice: number;
  recommendedPrice: number;
  priceChange: number;
  priceChangePercent: number;
  confidence: number;
  strategy: 'competitive' | 'demand_based' | 'value_based' | 'dynamic' | 'penetration' | 'skimming';
  rationale: string;
  expectedImpact: {
    revenueChange: number;
    volumeChange: number;
    marginChange: number;
    competitivePosition: number;
  };
  riskFactors: string[];
  opportunities: string[];
  dataPoints: {
    demandElasticity: number;
    competitorPrices: number[];
    seasonalFactor: number;
    inventoryLevel: number;
    salesVelocity: number;
  };
  testingRecommendation: {
    abTestDesign: string;
    duration: string;
    trafficSplit: number;
    successMetrics: string[];
  };
  implementationPriority: 'high' | 'medium' | 'low';
  lastUpdated: Date;
}

interface PricingStrategy {
  id: string;
  name: string;
  description: string;
  type: 'rule_based' | 'ml_dynamic' | 'competitive' | 'value_based' | 'behavioral';
  status: 'active' | 'inactive' | 'testing' | 'scheduled';
  applicableCategories: string[];
  rules: {
    conditions: string[];
    actions: string[];
    constraints: string[];
  };
  performance: {
    productsAffected: number;
    avgPriceChange: number;
    revenueImpact: number;
    marginImpact: number;
    conversionImpact: number;
  };
  schedule: {
    frequency: 'real_time' | 'hourly' | 'daily' | 'weekly';
    blackoutPeriods: string[];
    triggerConditions: string[];
  };
  aiModel: {
    algorithm: string;
    accuracy: number;
    features: string[];
    lastTrained: Date;
  };
  createdAt: Date;
  createdBy: string;
}

interface PriceTest {
  id: string;
  name: string;
  description: string;
  productIds: string[];
  testType: 'ab_test' | 'multivariate' | 'bandit' | 'gradual_rollout';
  variants: {
    id: string;
    name: string;
    priceChange: number;
    trafficPercentage: number;
    description: string;
  }[];
  status: 'draft' | 'running' | 'paused' | 'completed' | 'failed';
  startDate: Date;
  endDate: Date;
  metrics: {
    revenue: number;
    conversion: number;
    orders: number;
    avgOrderValue: number;
    margin: number;
    customerSatisfaction: number;
  };
  results: {
    winningVariant?: string;
    confidence: number;
    statisticalSignificance: boolean;
    recommendations: string[];
  };
  duration: string;
  trafficSplit: Record<string, number>;
  successCriteria: string[];
}

interface CompetitorAnalysis {
  productId: string;
  productName: string;
  ourPrice: number;
  competitors: {
    name: string;
    price: number;
    availability: boolean;
    lastUpdated: Date;
    priceHistory: { date: Date; price: number }[];
  }[];
  marketPosition: 'lowest' | 'below_avg' | 'average' | 'above_avg' | 'highest';
  priceGap: number;
  recommendations: string[];
  priceRange: {
    min: number;
    max: number;
    average: number;
  };
  lastChecked: Date;
}

interface PricingAnalytics {
  productId: string;
  period: string;
  metrics: {
    revenue: number;
    volume: number;
    conversion: number;
    margin: number;
    elasticity: number;
  };
  priceChanges: {
    date: Date;
    oldPrice: number;
    newPrice: number;
    reason: string;
    impact: number;
  }[];
  trends: {
    direction: 'up' | 'down' | 'stable';
    magnitude: number;
    duration: string;
    factors: string[];
  };
}

interface AIPricingOptimizationProps {
  onPriceUpdate?: (productId: string, newPrice: number, strategy: string) => void;
  onTestStart?: (testId: string) => void;
  onStrategyActivate?: (strategyId: string) => void;
  className?: string;
}

// Mock data generation
const generatePricingRecommendations = (): PricingRecommendation[] => [
  {
    productId: 'prod-001',
    productName: 'Wireless Bluetooth Earbuds Pro',
    category: 'Electronics',
    currentPrice: 89.99,
    recommendedPrice: 94.99,
    priceChange: 5.00,
    priceChangePercent: 5.6,
    confidence: 87,
    strategy: 'demand_based',
    rationale: 'High demand detected with low price elasticity. Competitor analysis shows room for premium positioning. Inventory levels optimal for price increase.',
    expectedImpact: {
      revenueChange: 12.3,
      volumeChange: -3.2,
      marginChange: 8.7,
      competitivePosition: 2.1
    },
    riskFactors: [
      'Potential customer backlash on price increase',
      'Competitor response risk',
      'Seasonal demand uncertainty'
    ],
    opportunities: [
      'Premium positioning opportunity',
      'Improved margin contribution',
      'Brand perception enhancement'
    ],
    dataPoints: {
      demandElasticity: -0.45,
      competitorPrices: [92.99, 97.50, 85.00, 99.99],
      seasonalFactor: 1.15,
      inventoryLevel: 856,
      salesVelocity: 23.4
    },
    testingRecommendation: {
      abTestDesign: '70/30 split test with gradual rollout',
      duration: '14 days',
      trafficSplit: 30,
      successMetrics: ['Revenue per visitor', 'Conversion rate', 'Customer satisfaction']
    },
    implementationPriority: 'high',
    lastUpdated: new Date(Date.now() - 1 * 60 * 60 * 1000)
  },
  {
    productId: 'prod-002',
    productName: 'Smart Home Security Camera',
    category: 'Home & Garden',
    currentPrice: 129.99,
    recommendedPrice: 119.99,
    priceChange: -10.00,
    priceChangePercent: -7.7,
    confidence: 92,
    strategy: 'competitive',
    rationale: 'Competitor price analysis reveals we are 15% above market average. Decreasing price will improve market share while maintaining healthy margins.',
    expectedImpact: {
      revenueChange: 8.4,
      volumeChange: 18.7,
      marginChange: -2.3,
      competitivePosition: 12.5
    },
    riskFactors: [
      'Margin compression',
      'Price war initiation',
      'Brand devaluation perception'
    ],
    opportunities: [
      'Market share expansion',
      'Volume growth acceleration',
      'Competitive advantage gain'
    ],
    dataPoints: {
      demandElasticity: -1.23,
      competitorPrices: [115.00, 124.99, 109.99, 134.95],
      seasonalFactor: 0.95,
      inventoryLevel: 1243,
      salesVelocity: 15.7
    },
    testingRecommendation: {
      abTestDesign: '50/50 controlled experiment',
      duration: '21 days',
      trafficSplit: 50,
      successMetrics: ['Total revenue', 'Market share', 'Customer acquisition']
    },
    implementationPriority: 'medium',
    lastUpdated: new Date(Date.now() - 2 * 60 * 60 * 1000)
  },
  {
    productId: 'prod-003',
    productName: 'Professional Coffee Machine',
    category: 'Kitchen & Dining',
    currentPrice: 299.99,
    recommendedPrice: 324.99,
    priceChange: 25.00,
    priceChangePercent: 8.3,
    confidence: 78,
    strategy: 'value_based',
    rationale: 'Customer reviews indicate high value perception. Premium features justify higher pricing. Low competition in this segment allows for value-based pricing.',
    expectedImpact: {
      revenueChange: 15.7,
      volumeChange: -5.8,
      marginChange: 12.1,
      competitivePosition: 3.4
    },
    riskFactors: [
      'Price sensitivity of target segment',
      'Alternative product substitution',
      'Economic downturn impact'
    ],
    opportunities: [
      'Premium brand positioning',
      'Higher margin realization',
      'Customer value optimization'
    ],
    dataPoints: {
      demandElasticity: -0.72,
      competitorPrices: [279.99, 349.99, 289.00, 359.95],
      seasonalFactor: 1.08,
      inventoryLevel: 234,
      salesVelocity: 8.9
    },
    testingRecommendation: {
      abTestDesign: 'Gradual price increase with customer segment targeting',
      duration: '28 days',
      trafficSplit: 25,
      successMetrics: ['Revenue per customer', 'Customer lifetime value', 'Brand perception']
    },
    implementationPriority: 'low',
    lastUpdated: new Date(Date.now() - 4 * 60 * 60 * 1000)
  }
];

const generatePricingStrategies = (): PricingStrategy[] => [
  {
    id: 'strategy-dynamic',
    name: 'Dynamic Pricing Engine',
    description: 'Real-time pricing adjustments based on demand, inventory, and competition',
    type: 'ml_dynamic',
    status: 'active',
    applicableCategories: ['Electronics', 'Fashion', 'Sports'],
    rules: {
      conditions: [
        'Inventory level < 100 units',
        'Demand spike > 25%',
        'Competitor price change > 5%'
      ],
      actions: [
        'Increase price by demand factor',
        'Apply scarcity premium',
        'Match competitive pricing'
      ],
      constraints: [
        'Max price increase: 15%',
        'Min margin: 20%',
        'Price change frequency: max 2x daily'
      ]
    },
    performance: {
      productsAffected: 1247,
      avgPriceChange: 3.2,
      revenueImpact: 8.7,
      marginImpact: 4.5,
      conversionImpact: -1.2
    },
    schedule: {
      frequency: 'real_time',
      blackoutPeriods: ['Black Friday', 'Cyber Monday'],
      triggerConditions: ['Demand threshold', 'Inventory threshold', 'Competitor action']
    },
    aiModel: {
      algorithm: 'Gradient Boosting Regressor',
      accuracy: 84.2,
      features: ['demand', 'inventory', 'competition', 'seasonality', 'customer_segment'],
      lastTrained: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)
    },
    createdAt: new Date('2024-03-15'),
    createdBy: 'AI Pricing Team'
  },
  {
    id: 'strategy-competitive',
    name: 'Competitive Price Matching',
    description: 'Automated competitive monitoring and price matching for key products',
    type: 'competitive',
    status: 'active',
    applicableCategories: ['Electronics', 'Books', 'Home & Garden'],
    rules: {
      conditions: [
        'Competitor price below our price',
        'Product availability confirmed',
        'Margin above minimum threshold'
      ],
      actions: [
        'Match competitor price',
        'Price 5% below competitor',
        'Apply promotional pricing'
      ],
      constraints: [
        'Maintain minimum margin',
        'Price change notification required',
        'Maximum 3 changes per week'
      ]
    },
    performance: {
      productsAffected: 543,
      avgPriceChange: -2.8,
      revenueImpact: 12.4,
      marginImpact: -3.7,
      conversionImpact: 8.9
    },
    schedule: {
      frequency: 'hourly',
      blackoutPeriods: ['Major sale events'],
      triggerConditions: ['Competitor price change', 'Market share loss']
    },
    aiModel: {
      algorithm: 'Random Forest Classifier',
      accuracy: 91.7,
      features: ['competitor_prices', 'market_share', 'product_similarity', 'customer_preference'],
      lastTrained: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)
    },
    createdAt: new Date('2024-02-20'),
    createdBy: 'Competitive Intelligence Team'
  }
];

const generatePriceTests = (): PriceTest[] => [
  {
    id: 'test-earbuds-001',
    name: 'Earbuds Pro Pricing Test',
    description: 'Testing optimal price point for wireless earbuds with different value propositions',
    productIds: ['prod-001'],
    testType: 'ab_test',
    variants: [
      {
        id: 'control',
        name: 'Current Price',
        priceChange: 0,
        trafficPercentage: 50,
        description: 'Existing price point $89.99'
      },
      {
        id: 'variant-a',
        name: 'Premium Position',
        priceChange: 5.00,
        trafficPercentage: 50,
        description: 'Increased price to $94.99 with premium messaging'
      }
    ],
    status: 'running',
    startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
    endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
    metrics: {
      revenue: 45670,
      conversion: 3.42,
      orders: 1247,
      avgOrderValue: 89.99,
      margin: 34.5,
      customerSatisfaction: 4.3
    },
    results: {
      confidence: 67,
      statisticalSignificance: false,
      recommendations: [
        'Continue test for full duration',
        'Monitor customer feedback closely',
        'Consider premium messaging optimization'
      ]
    },
    duration: '14 days',
    trafficSplit: { control: 50, 'variant-a': 50 },
    successCriteria: ['Revenue increase >5%', 'Conversion rate maintained', 'Customer satisfaction >4.0']
  },
  {
    id: 'test-camera-002',
    name: 'Security Camera Market Positioning',
    description: 'Multi-variant test to find optimal competitive positioning',
    productIds: ['prod-002'],
    testType: 'multivariate',
    variants: [
      {
        id: 'control',
        name: 'Current Price',
        priceChange: 0,
        trafficPercentage: 25,
        description: 'Current market price $129.99'
      },
      {
        id: 'aggressive',
        name: 'Aggressive Pricing',
        priceChange: -15.00,
        trafficPercentage: 25,
        description: 'Competitive price $114.99'
      },
      {
        id: 'moderate',
        name: 'Moderate Reduction',
        priceChange: -10.00,
        trafficPercentage: 25,
        description: 'Moderate price $119.99'
      },
      {
        id: 'premium',
        name: 'Premium Features',
        priceChange: 10.00,
        trafficPercentage: 25,
        description: 'Premium positioning $139.99'
      }
    ],
    status: 'completed',
    startDate: new Date(Date.now() - 21 * 24 * 60 * 60 * 1000),
    endDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
    metrics: {
      revenue: 78940,
      conversion: 4.67,
      orders: 2156,
      avgOrderValue: 119.99,
      margin: 28.7,
      customerSatisfaction: 4.5
    },
    results: {
      winningVariant: 'moderate',
      confidence: 94,
      statisticalSignificance: true,
      recommendations: [
        'Implement moderate pricing reduction',
        'Monitor competitor response',
        'Consider promotional support'
      ]
    },
    duration: '21 days',
    trafficSplit: { control: 25, aggressive: 25, moderate: 25, premium: 25 },
    successCriteria: ['Revenue optimization', 'Market share growth', 'Maintained margins']
  }
];

const generateCompetitorAnalysis = (): CompetitorAnalysis[] => [
  {
    productId: 'prod-001',
    productName: 'Wireless Bluetooth Earbuds Pro',
    ourPrice: 89.99,
    competitors: [
      {
        name: 'TechCorp',
        price: 92.99,
        availability: true,
        lastUpdated: new Date(Date.now() - 2 * 60 * 60 * 1000),
        priceHistory: [
          { date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), price: 94.99 },
          { date: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), price: 92.99 }
        ]
      },
      {
        name: 'AudioMax',
        price: 85.00,
        availability: true,
        lastUpdated: new Date(Date.now() - 1 * 60 * 60 * 1000),
        priceHistory: [
          { date: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000), price: 89.99 },
          { date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), price: 85.00 }
        ]
      },
      {
        name: 'SoundWave',
        price: 97.50,
        availability: false,
        lastUpdated: new Date(Date.now() - 6 * 60 * 60 * 1000),
        priceHistory: [
          { date: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000), price: 97.50 }
        ]
      }
    ],
    marketPosition: 'average',
    priceGap: -2.99,
    recommendations: [
      'Consider premium positioning against TechCorp',
      'Monitor AudioMax aggressive pricing',
      'Leverage SoundWave unavailability'
    ],
    priceRange: {
      min: 85.00,
      max: 97.50,
      average: 91.83
    },
    lastChecked: new Date(Date.now() - 30 * 60 * 1000)
  }
];

// Pricing recommendation card
const PricingRecommendationCard: React.FC<{
  recommendation: PricingRecommendation;
  onImplement?: () => void;
  onTest?: () => void;
}> = ({ recommendation, onImplement, onTest }) => {
  const getStrategyColor = (strategy: string) => {
    switch (strategy) {
      case 'competitive': return 'bg-blue-100 text-blue-800';
      case 'demand_based': return 'bg-green-100 text-green-800';
      case 'value_based': return 'bg-purple-100 text-purple-800';
      case 'dynamic': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getChangeIcon = (change: number) => {
    if (change > 0) return <ArrowUpRight className="w-4 h-4 text-green-600" />;
    if (change < 0) return <ArrowDownRight className="w-4 h-4 text-red-600" />;
    return <Minus className="w-4 h-4 text-gray-600" />;
  };

  return (
    <Card className="hover:shadow-lg transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div>
            <CardTitle className="text-lg">{recommendation.productName}</CardTitle>
            <p className="text-sm text-gray-600">{recommendation.category}</p>
          </div>
          <div className="flex flex-col space-y-1">
            <Badge className={getStrategyColor(recommendation.strategy)}>
              {recommendation.strategy.replace('_', ' ')}
            </Badge>
            <Badge className={getPriorityColor(recommendation.implementationPriority)}>
              {recommendation.implementationPriority} priority
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="text-sm text-gray-600">Current Price</div>
              <div className="text-xl font-bold">${recommendation.currentPrice.toFixed(2)}</div>
            </div>
            <div className="text-center p-3 bg-blue-50 rounded-lg">
              <div className="text-sm text-gray-600">Recommended Price</div>
              <div className="text-xl font-bold text-blue-600">${recommendation.recommendedPrice.toFixed(2)}</div>
            </div>
          </div>

          <div className="flex items-center justify-center space-x-2 p-3 bg-green-50 rounded-lg">
            {getChangeIcon(recommendation.priceChange)}
            <span className="text-lg font-semibold">
              ${Math.abs(recommendation.priceChange).toFixed(2)} ({recommendation.priceChangePercent > 0 ? '+' : ''}{recommendation.priceChangePercent.toFixed(1)}%)
            </span>
          </div>

          <div>
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">Confidence Score</span>
              <span className="text-sm font-bold">{recommendation.confidence}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full" 
                style={{ width: `${recommendation.confidence}%` }}
              />
            </div>
          </div>

          <div>
            <h4 className="font-medium text-sm mb-2">Expected Impact</h4>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div>Revenue: <span className={`font-medium ${recommendation.expectedImpact.revenueChange > 0 ? 'text-green-600' : 'text-red-600'}`}>
                {recommendation.expectedImpact.revenueChange > 0 ? '+' : ''}{recommendation.expectedImpact.revenueChange.toFixed(1)}%
              </span></div>
              <div>Volume: <span className={`font-medium ${recommendation.expectedImpact.volumeChange > 0 ? 'text-green-600' : 'text-red-600'}`}>
                {recommendation.expectedImpact.volumeChange > 0 ? '+' : ''}{recommendation.expectedImpact.volumeChange.toFixed(1)}%
              </span></div>
              <div>Margin: <span className={`font-medium ${recommendation.expectedImpact.marginChange > 0 ? 'text-green-600' : 'text-red-600'}`}>
                {recommendation.expectedImpact.marginChange > 0 ? '+' : ''}{recommendation.expectedImpact.marginChange.toFixed(1)}%
              </span></div>
              <div>Position: <span className={`font-medium ${recommendation.expectedImpact.competitivePosition > 0 ? 'text-green-600' : 'text-red-600'}`}>
                {recommendation.expectedImpact.competitivePosition > 0 ? '+' : ''}{recommendation.expectedImpact.competitivePosition.toFixed(1)}%
              </span></div>
            </div>
          </div>

          <div>
            <h4 className="font-medium text-sm mb-2">Rationale</h4>
            <p className="text-xs text-gray-600">{recommendation.rationale}</p>
          </div>

          <div>
            <h4 className="font-medium text-sm mb-2">Market Data</h4>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div>Elasticity: <span className="font-medium">{recommendation.dataPoints.demandElasticity.toFixed(2)}</span></div>
              <div>Inventory: <span className="font-medium">{recommendation.dataPoints.inventoryLevel}</span></div>
              <div>Velocity: <span className="font-medium">{recommendation.dataPoints.salesVelocity}/day</span></div>
              <div>Seasonal: <span className="font-medium">{recommendation.dataPoints.seasonalFactor.toFixed(2)}x</span></div>
            </div>
          </div>

          <div className="flex justify-between space-x-2 pt-3 border-t">
            <Button variant="outline" size="sm" onClick={onTest}>
              <Play className="w-4 h-4 mr-1" />
              A/B Test
            </Button>
            <Button size="sm" onClick={onImplement}>
              <CheckCircle className="w-4 h-4 mr-1" />
              Implement
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Pricing strategy card
const PricingStrategyCard: React.FC<{
  strategy: PricingStrategy;
  onToggle?: () => void;
  onEdit?: () => void;
}> = ({ strategy, onToggle, onEdit }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      case 'testing': return 'bg-yellow-100 text-yellow-800';
      case 'scheduled': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'ml_dynamic': return <Brain className="w-5 h-5 text-purple-600" />;
      case 'competitive': return <Target className="w-5 h-5 text-blue-600" />;
      case 'value_based': return <Award className="w-5 h-5 text-green-600" />;
      case 'behavioral': return <Users className="w-5 h-5 text-orange-600" />;
      default: return <Settings className="w-5 h-5 text-gray-600" />;
    }
  };

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3">
            {getTypeIcon(strategy.type)}
            <div>
              <CardTitle className="text-lg">{strategy.name}</CardTitle>
              <p className="text-sm text-gray-600">{strategy.description}</p>
            </div>
          </div>
          <Badge className={getStatusColor(strategy.status)}>
            {strategy.status}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Products Affected:</span>
              <span className="ml-2 font-medium">{strategy.performance.productsAffected}</span>
            </div>
            <div>
              <span className="text-gray-600">Avg Price Change:</span>
              <span className={`ml-2 font-medium ${strategy.performance.avgPriceChange > 0 ? 'text-green-600' : 'text-red-600'}`}>
                {strategy.performance.avgPriceChange > 0 ? '+' : ''}{strategy.performance.avgPriceChange.toFixed(1)}%
              </span>
            </div>
            <div>
              <span className="text-gray-600">Revenue Impact:</span>
              <span className={`ml-2 font-medium ${strategy.performance.revenueImpact > 0 ? 'text-green-600' : 'text-red-600'}`}>
                {strategy.performance.revenueImpact > 0 ? '+' : ''}{strategy.performance.revenueImpact.toFixed(1)}%
              </span>
            </div>
            <div>
              <span className="text-gray-600">Frequency:</span>
              <span className="ml-2 font-medium capitalize">{strategy.schedule.frequency.replace('_', ' ')}</span>
            </div>
          </div>

          {strategy.type === 'ml_dynamic' && (
            <div className="p-3 bg-purple-50 rounded-lg">
              <h4 className="font-medium text-sm mb-2">AI Model Performance</h4>
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div>Algorithm: <span className="font-medium">{strategy.aiModel.algorithm}</span></div>
                <div>Accuracy: <span className="font-medium">{strategy.aiModel.accuracy.toFixed(1)}%</span></div>
                <div>Features: <span className="font-medium">{strategy.aiModel.features.length}</span></div>
                <div>Last Trained: <span className="font-medium">{strategy.aiModel.lastTrained.toLocaleDateString()}</span></div>
              </div>
            </div>
          )}

          <div>
            <h4 className="font-medium text-sm mb-2">Categories</h4>
            <div className="flex flex-wrap gap-1">
              {strategy.applicableCategories.map((category, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {category}
                </Badge>
              ))}
            </div>
          </div>

          <div className="flex justify-between space-x-2 pt-3 border-t">
            <Button variant="outline" size="sm" onClick={onEdit}>
              <Settings className="w-4 h-4 mr-1" />
              Configure
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={onToggle}
            >
              {strategy.status === 'active' ? (
                <>
                  <Pause className="w-4 h-4 mr-1" />
                  Pause
                </>
              ) : (
                <>
                  <Play className="w-4 h-4 mr-1" />
                  Activate
                </>
              )}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Price test card
const PriceTestCard: React.FC<{
  test: PriceTest;
  onManage?: () => void;
}> = ({ test, onManage }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'bg-green-100 text-green-800';
      case 'completed': return 'bg-blue-100 text-blue-800';
      case 'paused': return 'bg-yellow-100 text-yellow-800';
      case 'draft': return 'bg-gray-100 text-gray-800';
      case 'failed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const winningVariant = test.results.winningVariant 
    ? test.variants.find(v => v.id === test.results.winningVariant)
    : null;

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div>
            <CardTitle className="text-lg">{test.name}</CardTitle>
            <p className="text-sm text-gray-600">{test.description}</p>
          </div>
          <Badge className={getStatusColor(test.status)}>
            {test.status}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Test Type:</span>
              <span className="ml-2 font-medium capitalize">{test.testType.replace('_', ' ')}</span>
            </div>
            <div>
              <span className="text-gray-600">Duration:</span>
              <span className="ml-2 font-medium">{test.duration}</span>
            </div>
            <div>
              <span className="text-gray-600">Variants:</span>
              <span className="ml-2 font-medium">{test.variants.length}</span>
            </div>
            <div>
              <span className="text-gray-600">Products:</span>
              <span className="ml-2 font-medium">{test.productIds.length}</span>
            </div>
          </div>

          <div>
            <h4 className="font-medium text-sm mb-2">Performance Metrics</h4>
            <div className="grid grid-cols-3 gap-2 text-xs">
              <div className="text-center p-2 bg-green-50 rounded">
                <div className="font-bold text-green-600">${test.metrics.revenue.toLocaleString()}</div>
                <div className="text-green-600">Revenue</div>
              </div>
              <div className="text-center p-2 bg-blue-50 rounded">
                <div className="font-bold text-blue-600">{test.metrics.conversion.toFixed(2)}%</div>
                <div className="text-blue-600">Conversion</div>
              </div>
              <div className="text-center p-2 bg-purple-50 rounded">
                <div className="font-bold text-purple-600">{test.metrics.orders}</div>
                <div className="text-purple-600">Orders</div>
              </div>
            </div>
          </div>

          <div>
            <h4 className="font-medium text-sm mb-2">Variants</h4>
            <div className="space-y-2">
              {test.variants.map((variant, index) => (
                <div 
                  key={variant.id} 
                  className={`p-2 rounded border ${
                    variant.id === test.results.winningVariant ? 'border-green-500 bg-green-50' : 'border-gray-200'
                  }`}
                >
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">{variant.name}</span>
                    <div className="flex items-center space-x-2">
                      <span className="text-xs">{variant.trafficPercentage}%</span>
                      {variant.id === test.results.winningVariant && (
                        <Award className="w-4 h-4 text-green-600" />
                      )}
                    </div>
                  </div>
                  <div className="text-xs text-gray-600">{variant.description}</div>
                </div>
              ))}
            </div>
          </div>

          {test.status === 'completed' && test.results.winningVariant && (
            <div className="p-3 bg-green-50 rounded-lg">
              <h4 className="font-medium text-sm mb-2 text-green-900">Test Results</h4>
              <div className="text-sm">
                <div>Winner: <span className="font-medium">{winningVariant?.name}</span></div>
                <div>Confidence: <span className="font-medium">{test.results.confidence}%</span></div>
                <div className="text-xs text-green-700 mt-1">
                  {test.results.statisticalSignificance ? '✓ Statistically significant' : '⚠ Not statistically significant'}
                </div>
              </div>
            </div>
          )}

          <div className="flex justify-end space-x-2 pt-3 border-t">
            <Button variant="outline" size="sm">
              <Eye className="w-4 h-4 mr-1" />
              View Details
            </Button>
            <Button variant="outline" size="sm" onClick={onManage}>
              <Settings className="w-4 h-4 mr-1" />
              Manage
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Main component
const AIPricingOptimization: React.FC<AIPricingOptimizationProps> = ({
  onPriceUpdate,
  onTestStart,
  onStrategyActivate,
  className = ''
}) => {
  const [activeTab, setActiveTab] = useState('recommendations');
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  
  const recommendations = useMemo(() => generatePricingRecommendations(), []);
  const strategies = useMemo(() => generatePricingStrategies(), []);
  const tests = useMemo(() => generatePriceTests(), []);
  const competitorAnalysis = useMemo(() => generateCompetitorAnalysis(), []);

  const filteredRecommendations = useMemo(() => {
    let filtered = recommendations;
    
    if (searchTerm) {
      filtered = filtered.filter(rec =>
        rec.productName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        rec.category.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    
    if (categoryFilter !== 'all') {
      filtered = filtered.filter(rec => rec.category.toLowerCase() === categoryFilter.toLowerCase());
    }
    
    return filtered.sort((a, b) => {
      const priorityWeight = { high: 3, medium: 2, low: 1 };
      return priorityWeight[b.implementationPriority] - priorityWeight[a.implementationPriority];
    });
  }, [recommendations, searchTerm, categoryFilter]);

  const handlePriceImplement = useCallback((productId: string, newPrice: number, strategy: string) => {
    onPriceUpdate?.(productId, newPrice, strategy);
    console.log(`Implementing price ${newPrice} for product ${productId} using ${strategy} strategy`);
  }, [onPriceUpdate]);

  const handleTestStart = useCallback((testId: string) => {
    onTestStart?.(testId);
    console.log('Starting test:', testId);
  }, [onTestStart]);

  const stats = useMemo(() => {
    const totalRecommendations = recommendations.length;
    const avgPriceChange = recommendations.reduce((sum, r) => sum + Math.abs(r.priceChangePercent), 0) / recommendations.length;
    const activeStrategies = strategies.filter(s => s.status === 'active').length;
    const runningTests = tests.filter(t => t.status === 'running').length;
    
    return { totalRecommendations, avgPriceChange, activeStrategies, runningTests };
  }, [recommendations, strategies, tests]);

  return (
    <div className={`space-y-6 ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Tag className="w-6 h-6 text-orange-600" />
          <h2 className="text-2xl font-bold">AI Pricing Optimization</h2>
          <Badge className="bg-orange-100 text-orange-800">Phase 10</Badge>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            Export Analysis
          </Button>
          <Button variant="outline">
            <Settings className="w-4 h-4 mr-2" />
            Pricing Settings
          </Button>
        </div>
      </div>

      {/* Pricing Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Price Recommendations</p>
                <p className="text-3xl font-bold text-blue-600">{stats.totalRecommendations}</p>
                <p className="text-xs text-blue-600 mt-1">AI-generated</p>
              </div>
              <Lightbulb className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Avg Price Impact</p>
                <p className="text-3xl font-bold text-green-600">{stats.avgPriceChange.toFixed(1)}%</p>
                <p className="text-xs text-green-600 mt-1">Optimization potential</p>
              </div>
              <TrendingUp className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Active Strategies</p>
                <p className="text-3xl font-bold text-purple-600">{stats.activeStrategies}</p>
                <p className="text-xs text-purple-600 mt-1">Automated pricing</p>
              </div>
              <Brain className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Running Tests</p>
                <p className="text-3xl font-bold text-orange-600">{stats.runningTests}</p>
                <p className="text-xs text-orange-600 mt-1">A/B experiments</p>
              </div>
              <Activity className="w-8 h-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="recommendations">Price Recommendations</TabsTrigger>
          <TabsTrigger value="strategies">Pricing Strategies</TabsTrigger>
          <TabsTrigger value="testing">A/B Testing</TabsTrigger>
          <TabsTrigger value="competition">Competitor Analysis</TabsTrigger>
        </TabsList>

        <TabsContent value="recommendations" className="space-y-6">
          <div className="flex items-center space-x-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search products..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <select
              value={categoryFilter}
              onChange={(e) => setCategoryFilter(e.target.value)}
              className="p-2 border rounded-md text-sm"
            >
              <option value="all">All Categories</option>
              <option value="electronics">Electronics</option>
              <option value="home & garden">Home & Garden</option>
              <option value="kitchen & dining">Kitchen & Dining</option>
              <option value="fashion">Fashion</option>
            </select>
            <Button variant="outline">
              <Filter className="w-4 h-4 mr-2" />
              Filter
            </Button>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {filteredRecommendations.map((recommendation, index) => (
              <motion.div
                key={recommendation.productId}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <PricingRecommendationCard
                  recommendation={recommendation}
                  onImplement={() => handlePriceImplement(
                    recommendation.productId, 
                    recommendation.recommendedPrice, 
                    recommendation.strategy
                  )}
                  onTest={() => handleTestStart(`test-${recommendation.productId}`)}
                />
              </motion.div>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="strategies" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {strategies.map((strategy, index) => (
              <motion.div
                key={strategy.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <PricingStrategyCard
                  strategy={strategy}
                  onToggle={() => onStrategyActivate?.(strategy.id)}
                />
              </motion.div>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="testing" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {tests.map((test, index) => (
              <motion.div
                key={test.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <PriceTestCard test={test} />
              </motion.div>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="competition" className="space-y-6">
          <div className="grid grid-cols-1 gap-6">
            {competitorAnalysis.map((analysis, index) => (
              <motion.div
                key={analysis.productId}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Card className="hover:shadow-md transition-shadow">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle className="text-lg">{analysis.productName}</CardTitle>
                        <p className="text-sm text-gray-600">Market Position: {analysis.marketPosition.replace('_', ' ')}</p>
                      </div>
                      <Badge variant="outline">
                        ${analysis.ourPrice.toFixed(2)}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="grid grid-cols-3 gap-4 text-sm">
                        <div className="text-center p-3 bg-red-50 rounded-lg">
                          <div className="text-red-600 font-bold">${analysis.priceRange.min.toFixed(2)}</div>
                          <div className="text-red-600">Minimum</div>
                        </div>
                        <div className="text-center p-3 bg-gray-50 rounded-lg">
                          <div className="font-bold">${analysis.priceRange.average.toFixed(2)}</div>
                          <div className="text-gray-600">Average</div>
                        </div>
                        <div className="text-center p-3 bg-green-50 rounded-lg">
                          <div className="text-green-600 font-bold">${analysis.priceRange.max.toFixed(2)}</div>
                          <div className="text-green-600">Maximum</div>
                        </div>
                      </div>

                      <div>
                        <h4 className="font-medium text-sm mb-2">Competitors</h4>
                        <div className="space-y-2">
                          {analysis.competitors.map((competitor, idx) => (
                            <div key={idx} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                              <div>
                                <span className="font-medium">{competitor.name}</span>
                                <span className={`ml-2 text-xs ${competitor.availability ? 'text-green-600' : 'text-red-600'}`}>
                                  {competitor.availability ? 'Available' : 'Out of Stock'}
                                </span>
                              </div>
                              <span className="font-bold">${competitor.price.toFixed(2)}</span>
                            </div>
                          ))}
                        </div>
                      </div>

                      <div>
                        <h4 className="font-medium text-sm mb-2">Recommendations</h4>
                        <ul className="space-y-1">
                          {analysis.recommendations.map((rec, idx) => (
                            <li key={idx} className="text-xs text-gray-600 flex items-start space-x-2">
                              <Target className="w-3 h-3 text-blue-500 mt-0.5 flex-shrink-0" />
                              <span>{rec}</span>
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div className="flex justify-between items-center pt-3 border-t">
                        <span className="text-xs text-gray-500">
                          Last checked: {analysis.lastChecked.toLocaleString()}
                        </span>
                        <Button variant="outline" size="sm">
                          <RefreshCw className="w-4 h-4 mr-1" />
                          Refresh
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AIPricingOptimization;
export { type PricingRecommendation, type PricingStrategy, type PriceTest, type CompetitorAnalysis };