/**
 * Real-time ML Model Serving Infrastructure
 * Advanced ML model deployment, monitoring, and serving platform with real-time inference
 */

import React, { useState, useMemo, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { motion } from 'framer-motion';
import { 
  Cpu,
  Zap,
  Brain,
  Activity,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  Database,
  Network,
  Cloud,
  Server,
  Globe,
  Shield,
  Lock,
  Unlock,
  Settings,
  Monitor,
  BarChart3,
  LineChart,
  PieChart,
  Target,
  Eye,
  Download,
  Upload,
  Share,
  RefreshCw,
  Play,
  Pause,
  Square,
  SkipForward,
  SkipBack,
  Volume2,
  VolumeX,
  Filter,
  Search,
  Calendar,
  Users,
  Package,
  Tag,
  Hash,
  Layers,
  Grid,
  List,
  MoreHorizontal,
  ExternalLink,
  Info,
  Plus,
  Minus,
  Edit,
  Trash2,
  Copy,
  Bookmark,
  Star,
  Award,
  Lightbulb,
  Workflow,
  GitBranch,
  Code,
  Terminal,
  Gauge,
  Timer,
  HardDrive,
  MemoryStick,
  Wifi,
  WifiOff,
  Power,
  PowerOff,
  Thermometer,
  Wrench,
  Bug,
  CheckCheck,
  AlertCircle,
  Construction,
  Rocket,
  FlaskConical
} from 'lucide-react';
import { 
  LineChart as RechartsLineChart, 
  Line, 
  AreaChart, 
  Area, 
  BarChart, 
  Bar, 
  ScatterChart,
  Scatter,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  Cell,
  PieChart as RechartsPieChart,
  Pie,
  RadialBarChart,
  RadialBar,
  ComposedChart
} from 'recharts';

// Types
interface MLModel {
  id: string;
  name: string;
  version: string;
  type: 'classification' | 'regression' | 'clustering' | 'recommendation' | 'forecasting' | 'nlp' | 'computer_vision';
  framework: 'tensorflow' | 'pytorch' | 'scikit_learn' | 'xgboost' | 'lightgbm' | 'onnx';
  status: 'training' | 'validating' | 'deployed' | 'serving' | 'paused' | 'failed' | 'deprecated';
  deployment: {
    environment: 'development' | 'staging' | 'production';
    endpoint: string;
    instances: number;
    autoScaling: boolean;
    minInstances: number;
    maxInstances: number;
    targetCPU: number;
    targetMemory: number;
  };
  performance: {
    accuracy: number;
    latency: number; // milliseconds
    throughput: number; // requests per second
    errorRate: number;
    availability: number;
  };
  metrics: {
    requests: number;
    successfulPredictions: number;
    failedPredictions: number;
    avgResponseTime: number;
    p95ResponseTime: number;
    p99ResponseTime: number;
    cpuUsage: number;
    memoryUsage: number;
    diskUsage: number;
  };
  training: {
    dataset: string;
    trainingTime: number; // hours
    lastTrained: Date;
    nextScheduledTraining?: Date;
    hyperparameters: Record<string, any>;
    validationScore: number;
    testScore: number;
  };
  monitoring: {
    alerts: Alert[];
    healthChecks: HealthCheck[];
    logs: LogEntry[];
  };
  security: {
    encryption: boolean;
    authentication: boolean;
    rateLimiting: boolean;
    ipWhitelist: string[];
  };
  cost: {
    hourly: number;
    monthly: number;
    compute: number;
    storage: number;
    network: number;
  };
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
}

interface ModelDeployment {
  id: string;
  modelId: string;
  version: string;
  environment: string;
  status: 'deploying' | 'active' | 'rolling_back' | 'failed';
  strategy: 'blue_green' | 'canary' | 'rolling' | 'recreate';
  progress: number;
  startTime: Date;
  endTime?: Date;
  rollbackVersion?: string;
  healthChecks: {
    passed: number;
    failed: number;
    total: number;
  };
  traffic: {
    current: number; // percentage
    target: number; // percentage
  };
  logs: DeploymentLog[];
}

interface Alert {
  id: string;
  modelId: string;
  type: 'performance' | 'error' | 'resource' | 'security' | 'business';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  threshold: number;
  currentValue: number;
  status: 'active' | 'acknowledged' | 'resolved';
  createdAt: Date;
  resolvedAt?: Date;
  assignee?: string;
}

interface HealthCheck {
  id: string;
  name: string;
  type: 'endpoint' | 'model' | 'dependency' | 'resource';
  status: 'healthy' | 'degraded' | 'unhealthy';
  lastCheck: Date;
  responseTime: number;
  details: string;
}

interface LogEntry {
  id: string;
  timestamp: Date;
  level: 'debug' | 'info' | 'warn' | 'error' | 'fatal';
  message: string;
  metadata: Record<string, any>;
}

interface DeploymentLog {
  timestamp: Date;
  level: 'info' | 'warn' | 'error';
  message: string;
  stage: string;
}

interface InferenceRequest {
  id: string;
  modelId: string;
  timestamp: Date;
  input: any;
  output: any;
  latency: number;
  success: boolean;
  error?: string;
  requestId: string;
  userId?: string;
  clientIP: string;
}

interface ModelPipeline {
  id: string;
  name: string;
  description: string;
  models: {
    modelId: string;
    order: number;
    dependencies: string[];
  }[];
  status: 'active' | 'paused' | 'error';
  throughput: number;
  latency: number;
  errorRate: number;
}

interface ExperimentResults {
  id: string;
  name: string;
  description: string;
  models: {
    modelId: string;
    traffic: number;
    conversions: number;
    revenue: number;
    metrics: Record<string, number>;
  }[];
  status: 'running' | 'completed' | 'paused';
  startDate: Date;
  endDate?: Date;
  significance: number;
  winner?: string;
}

// Mock data generators
const generateMLModels = (): MLModel[] => {
  const modelTypes: MLModel['type'][] = ['classification', 'regression', 'recommendation', 'forecasting', 'nlp'];
  const frameworks: MLModel['framework'][] = ['tensorflow', 'pytorch', 'scikit_learn', 'xgboost'];
  const statuses: MLModel['status'][] = ['serving', 'deployed', 'training', 'paused'];
  const environments: MLModel['deployment']['environment'][] = ['production', 'staging', 'development'];

  return Array.from({ length: 12 }, (_, index) => {
    const type = modelTypes[Math.floor(Math.random() * modelTypes.length)];
    const framework = frameworks[Math.floor(Math.random() * frameworks.length)];
    const status = statuses[Math.floor(Math.random() * statuses.length)];
    const environment = environments[Math.floor(Math.random() * environments.length)];
    
    return {
      id: `MODEL-${String(index + 1).padStart(3, '0')}`,
      name: `${type.charAt(0).toUpperCase() + type.slice(1)} Model ${index + 1}`,
      version: `v1.${Math.floor(Math.random() * 10)}.${Math.floor(Math.random() * 10)}`,
      type,
      framework,
      status,
      deployment: {
        environment,
        endpoint: `https://api.example.com/models/model-${index + 1}`,
        instances: Math.floor(Math.random() * 5) + 1,
        autoScaling: Math.random() > 0.5,
        minInstances: 1,
        maxInstances: 10,
        targetCPU: 70,
        targetMemory: 80
      },
      performance: {
        accuracy: 0.85 + Math.random() * 0.1,
        latency: 50 + Math.random() * 200,
        throughput: 100 + Math.random() * 900,
        errorRate: Math.random() * 0.05,
        availability: 0.95 + Math.random() * 0.05
      },
      metrics: {
        requests: Math.floor(Math.random() * 100000) + 10000,
        successfulPredictions: Math.floor(Math.random() * 95000) + 9000,
        failedPredictions: Math.floor(Math.random() * 1000) + 100,
        avgResponseTime: 100 + Math.random() * 300,
        p95ResponseTime: 200 + Math.random() * 500,
        p99ResponseTime: 500 + Math.random() * 1000,
        cpuUsage: 30 + Math.random() * 50,
        memoryUsage: 40 + Math.random() * 40,
        diskUsage: 20 + Math.random() * 30
      },
      training: {
        dataset: `Dataset_${Math.floor(Math.random() * 10) + 1}`,
        trainingTime: Math.random() * 48,
        lastTrained: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
        hyperparameters: {
          learning_rate: 0.001 + Math.random() * 0.01,
          batch_size: Math.pow(2, Math.floor(Math.random() * 5) + 4),
          epochs: Math.floor(Math.random() * 50) + 10
        },
        validationScore: 0.8 + Math.random() * 0.15,
        testScore: 0.78 + Math.random() * 0.15
      },
      monitoring: {
        alerts: generateAlerts(),
        healthChecks: generateHealthChecks(),
        logs: generateLogs()
      },
      security: {
        encryption: Math.random() > 0.2,
        authentication: Math.random() > 0.1,
        rateLimiting: Math.random() > 0.3,
        ipWhitelist: ['10.0.0.0/8', '***********/16']
      },
      cost: {
        hourly: Math.random() * 10 + 1,
        monthly: Math.random() * 300 + 30,
        compute: Math.random() * 200 + 20,
        storage: Math.random() * 50 + 5,
        network: Math.random() * 20 + 2
      },
      createdAt: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000),
      updatedAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000),
      createdBy: `user${Math.floor(Math.random() * 10) + 1}@example.com`
    };
  });
};

const generateAlerts = (): Alert[] => {
  const types: Alert['type'][] = ['performance', 'error', 'resource', 'security'];
  const severities: Alert['severity'][] = ['low', 'medium', 'high', 'critical'];
  const statuses: Alert['status'][] = ['active', 'acknowledged', 'resolved'];

  return Array.from({ length: 5 }, (_, index) => ({
    id: `ALERT-${String(index + 1).padStart(3, '0')}`,
    modelId: `MODEL-001`,
    type: types[Math.floor(Math.random() * types.length)],
    severity: severities[Math.floor(Math.random() * severities.length)],
    title: `Alert ${index + 1}`,
    description: `This is alert description ${index + 1}`,
    threshold: Math.random() * 100,
    currentValue: Math.random() * 120,
    status: statuses[Math.floor(Math.random() * statuses.length)],
    createdAt: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000),
    resolvedAt: Math.random() > 0.5 ? new Date() : undefined
  }));
};

const generateHealthChecks = (): HealthCheck[] => {
  const types: HealthCheck['type'][] = ['endpoint', 'model', 'dependency', 'resource'];
  const statuses: HealthCheck['status'][] = ['healthy', 'degraded', 'unhealthy'];

  return Array.from({ length: 4 }, (_, index) => ({
    id: `HEALTH-${String(index + 1).padStart(3, '0')}`,
    name: `Health Check ${index + 1}`,
    type: types[index],
    status: statuses[Math.floor(Math.random() * statuses.length)],
    lastCheck: new Date(Date.now() - Math.random() * 60 * 60 * 1000),
    responseTime: Math.random() * 500,
    details: `Health check details ${index + 1}`
  }));
};

const generateLogs = (): LogEntry[] => {
  const levels: LogEntry['level'][] = ['debug', 'info', 'warn', 'error'];

  return Array.from({ length: 10 }, (_, index) => ({
    id: `LOG-${String(index + 1).padStart(3, '0')}`,
    timestamp: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000),
    level: levels[Math.floor(Math.random() * levels.length)],
    message: `Log message ${index + 1}`,
    metadata: { requestId: `req-${index + 1}` }
  }));
};

const generateDeployments = (): ModelDeployment[] => {
  const statuses: ModelDeployment['status'][] = ['active', 'deploying', 'failed'];
  const strategies: ModelDeployment['strategy'][] = ['blue_green', 'canary', 'rolling'];

  return Array.from({ length: 6 }, (_, index) => ({
    id: `DEPLOY-${String(index + 1).padStart(3, '0')}`,
    modelId: `MODEL-${String(Math.floor(Math.random() * 12) + 1).padStart(3, '0')}`,
    version: `v1.${Math.floor(Math.random() * 5)}.0`,
    environment: ['production', 'staging', 'development'][Math.floor(Math.random() * 3)],
    status: statuses[Math.floor(Math.random() * statuses.length)],
    strategy: strategies[Math.floor(Math.random() * strategies.length)],
    progress: Math.random() * 100,
    startTime: new Date(Date.now() - Math.random() * 4 * 60 * 60 * 1000),
    endTime: Math.random() > 0.5 ? new Date() : undefined,
    healthChecks: {
      passed: Math.floor(Math.random() * 8) + 2,
      failed: Math.floor(Math.random() * 2),
      total: 10
    },
    traffic: {
      current: Math.random() * 100,
      target: Math.random() * 100
    },
    logs: []
  }));
};

const generateExperiments = (): ExperimentResults[] => {
  return Array.from({ length: 4 }, (_, index) => ({
    id: `EXP-${String(index + 1).padStart(3, '0')}`,
    name: `A/B Test ${index + 1}`,
    description: `Testing model performance for experiment ${index + 1}`,
    models: [
      {
        modelId: `MODEL-${String(index * 2 + 1).padStart(3, '0')}`,
        traffic: 50,
        conversions: Math.floor(Math.random() * 1000) + 500,
        revenue: Math.random() * 10000 + 5000,
        metrics: { accuracy: 0.85 + Math.random() * 0.1 }
      },
      {
        modelId: `MODEL-${String(index * 2 + 2).padStart(3, '0')}`,
        traffic: 50,
        conversions: Math.floor(Math.random() * 1000) + 500,
        revenue: Math.random() * 10000 + 5000,
        metrics: { accuracy: 0.85 + Math.random() * 0.1 }
      }
    ],
    status: ['running', 'completed'][Math.floor(Math.random() * 2)] as any,
    startDate: new Date(Date.now() - Math.random() * 14 * 24 * 60 * 60 * 1000),
    endDate: Math.random() > 0.5 ? new Date() : undefined,
    significance: Math.random(),
    winner: Math.random() > 0.5 ? `MODEL-${String(index * 2 + 1).padStart(3, '0')}` : undefined
  }));
};

// Components
const ModelCard: React.FC<{ model: MLModel }> = ({ model }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'serving': return 'bg-green-100 text-green-800 border-green-200';
      case 'deployed': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'training': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'paused': return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'failed': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'serving': return <Zap className="h-3 w-3" />;
      case 'deployed': return <CheckCircle className="h-3 w-3" />;
      case 'training': return <RefreshCw className="h-3 w-3 animate-spin" />;
      case 'paused': return <Pause className="h-3 w-3" />;
      case 'failed': return <XCircle className="h-3 w-3" />;
      default: return <Info className="h-3 w-3" />;
    }
  };

  const getFrameworkIcon = (framework: string) => {
    switch (framework) {
      case 'tensorflow': return <Brain className="h-4 w-4" />;
      case 'pytorch': return <Cpu className="h-4 w-4" />;
      default: return <Code className="h-4 w-4" />;
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className="border rounded-lg p-4 hover:shadow-md transition-shadow"
    >
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-2">
          {getFrameworkIcon(model.framework)}
          <div>
            <h4 className="font-medium">{model.name}</h4>
            <p className="text-sm text-gray-600">{model.version}</p>
          </div>
        </div>
        <Badge className={getStatusColor(model.status)}>
          {getStatusIcon(model.status)}
          {model.status.toUpperCase()}
        </Badge>
      </div>

      <div className="grid grid-cols-2 gap-4 mb-3">
        <div>
          <p className="text-sm text-gray-600">Accuracy</p>
          <p className="text-lg font-semibold">{(model.performance.accuracy * 100).toFixed(1)}%</p>
        </div>
        <div>
          <p className="text-sm text-gray-600">Latency</p>
          <p className="text-lg font-semibold">{model.performance.latency.toFixed(0)}ms</p>
        </div>
        <div>
          <p className="text-sm text-gray-600">Throughput</p>
          <p className="text-lg font-semibold">{model.performance.throughput.toFixed(0)} RPS</p>
        </div>
        <div>
          <p className="text-sm text-gray-600">Availability</p>
          <p className="text-lg font-semibold">{(model.performance.availability * 100).toFixed(1)}%</p>
        </div>
      </div>

      <div className="mb-3">
        <div className="flex items-center justify-between text-sm mb-1">
          <span className="text-gray-600">CPU Usage</span>
          <span>{model.metrics.cpuUsage.toFixed(0)}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-blue-600 h-2 rounded-full"
            style={{ width: `${model.metrics.cpuUsage}%` }}
          />
        </div>
      </div>

      <div className="flex items-center justify-between text-sm">
        <span className="text-gray-600">Environment: {model.deployment.environment}</span>
        <span className="text-gray-600">Cost: ${model.cost.hourly.toFixed(2)}/hr</span>
      </div>
    </motion.div>
  );
};

const DeploymentCard: React.FC<{ deployment: ModelDeployment }> = ({ deployment }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'deploying': return 'bg-yellow-100 text-yellow-800';
      case 'failed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="border rounded-lg p-4 hover:shadow-md transition-shadow"
    >
      <div className="flex items-start justify-between mb-3">
        <div>
          <h4 className="font-medium">{deployment.modelId}</h4>
          <p className="text-sm text-gray-600">{deployment.version} → {deployment.environment}</p>
        </div>
        <Badge className={getStatusColor(deployment.status)}>
          {deployment.status.toUpperCase()}
        </Badge>
      </div>

      <div className="mb-3">
        <div className="flex items-center justify-between text-sm mb-1">
          <span className="text-gray-600">Progress</span>
          <span>{deployment.progress.toFixed(0)}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${deployment.progress}%` }}
          />
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4 mb-3">
        <div>
          <p className="text-sm text-gray-600">Strategy</p>
          <p className="text-sm font-medium capitalize">{deployment.strategy.replace('_', ' ')}</p>
        </div>
        <div>
          <p className="text-sm text-gray-600">Health Checks</p>
          <p className="text-sm font-medium">
            {deployment.healthChecks.passed}/{deployment.healthChecks.total}
          </p>
        </div>
      </div>

      <div className="flex items-center justify-between text-sm">
        <span className="text-gray-600">
          Traffic: {deployment.traffic.current.toFixed(0)}%
        </span>
        <span className="text-gray-600">
          Started: {deployment.startTime.toLocaleTimeString()}
        </span>
      </div>
    </motion.div>
  );
};

const AlertCard: React.FC<{ alert: Alert }> = ({ alert }) => {
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-blue-100 text-blue-800 border-blue-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical': return <AlertTriangle className="h-4 w-4" />;
      case 'high': return <AlertCircle className="h-4 w-4" />;
      case 'medium': return <Info className="h-4 w-4" />;
      case 'low': return <CheckCircle className="h-4 w-4" />;
      default: return <Info className="h-4 w-4" />;
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      className="border rounded-lg p-4 hover:shadow-md transition-shadow"
    >
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-2">
          {getSeverityIcon(alert.severity)}
          <div>
            <h4 className="font-medium">{alert.title}</h4>
            <p className="text-sm text-gray-600">{alert.description}</p>
          </div>
        </div>
        <Badge className={getSeverityColor(alert.severity)}>
          {alert.severity.toUpperCase()}
        </Badge>
      </div>

      <div className="grid grid-cols-2 gap-4 mb-3">
        <div>
          <p className="text-sm text-gray-600">Threshold</p>
          <p className="text-sm font-medium">{alert.threshold.toFixed(2)}</p>
        </div>
        <div>
          <p className="text-sm text-gray-600">Current Value</p>
          <p className="text-sm font-medium">{alert.currentValue.toFixed(2)}</p>
        </div>
      </div>

      <div className="flex items-center justify-between text-sm">
        <span className="text-gray-600">Type: {alert.type}</span>
        <span className="text-gray-600">
          {alert.createdAt.toLocaleTimeString()}
        </span>
      </div>
    </motion.div>
  );
};

export const MLModelServing: React.FC = () => {
  const [selectedEnvironment, setSelectedEnvironment] = useState<string>('all');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'name' | 'status' | 'accuracy' | 'latency' | 'cost'>('name');

  const models = useMemo(() => generateMLModels(), []);
  const deployments = useMemo(() => generateDeployments(), []);
  const experiments = useMemo(() => generateExperiments(), []);

  const filteredModels = useMemo(() => {
    return models
      .filter(model => selectedEnvironment === 'all' || model.deployment.environment === selectedEnvironment)
      .filter(model => selectedStatus === 'all' || model.status === selectedStatus)
      .filter(model => model.name.toLowerCase().includes(searchQuery.toLowerCase()))
      .sort((a, b) => {
        switch (sortBy) {
          case 'name': return a.name.localeCompare(b.name);
          case 'status': return a.status.localeCompare(b.status);
          case 'accuracy': return b.performance.accuracy - a.performance.accuracy;
          case 'latency': return a.performance.latency - b.performance.latency;
          case 'cost': return b.cost.hourly - a.cost.hourly;
          default: return 0;
        }
      });
  }, [models, selectedEnvironment, selectedStatus, searchQuery, sortBy]);

  const platformMetrics = useMemo(() => {
    const totalModels = models.length;
    const servingModels = models.filter(m => m.status === 'serving').length;
    const totalRequests = models.reduce((sum, m) => sum + m.metrics.requests, 0);
    const avgLatency = models.reduce((sum, m) => sum + m.performance.latency, 0) / models.length;
    const totalCost = models.reduce((sum, m) => sum + m.cost.hourly, 0);
    const avgAccuracy = models.reduce((sum, m) => sum + m.performance.accuracy, 0) / models.length;

    return {
      totalModels,
      servingModels,
      totalRequests,
      avgLatency,
      totalCost,
      avgAccuracy,
      activeAlerts: models.reduce((sum, m) => sum + m.monitoring.alerts.filter(a => a.status === 'active').length, 0)
    };
  }, [models]);

  const performanceData = useMemo(() => {
    return Array.from({ length: 24 }, (_, i) => ({
      hour: i,
      requests: Math.floor(Math.random() * 1000) + 500,
      latency: 50 + Math.random() * 100,
      errors: Math.floor(Math.random() * 50),
      cpu: 30 + Math.random() * 40,
      memory: 40 + Math.random() * 30
    }));
  }, []);

  const modelDistributionData = useMemo(() => {
    const frameworks = models.reduce((acc, model) => {
      acc[model.framework] = (acc[model.framework] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(frameworks).map(([framework, count]) => ({
      name: framework,
      value: count,
      percentage: ((count / models.length) * 100).toFixed(1)
    }));
  }, [models]);

  const costTrendData = useMemo(() => {
    return Array.from({ length: 30 }, (_, i) => ({
      day: i + 1,
      compute: 100 + Math.random() * 50,
      storage: 20 + Math.random() * 10,
      network: 10 + Math.random() * 5,
      total: 130 + Math.random() * 65
    }));
  }, []);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Server className="h-6 w-6" />
            ML Model Serving
          </h2>
          <p className="text-gray-600">Real-time ML model deployment, monitoring, and serving infrastructure</p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Upload className="h-4 w-4 mr-2" />
            Deploy Model
          </Button>
          <Button size="sm">
            <Rocket className="h-4 w-4 mr-2" />
            Create Experiment
          </Button>
        </div>
      </div>

      <Tabs defaultValue="models" className="w-full">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="models">Models</TabsTrigger>
          <TabsTrigger value="deployments">Deployments</TabsTrigger>
          <TabsTrigger value="monitoring">Monitoring</TabsTrigger>
          <TabsTrigger value="experiments">Experiments</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="costs">Costs</TabsTrigger>
        </TabsList>

        <TabsContent value="models">
          <div className="space-y-6">
            {/* Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Brain className="h-4 w-4 text-blue-600" />
                    <div>
                      <p className="text-sm text-gray-600">Total Models</p>
                      <p className="text-2xl font-bold">{platformMetrics.totalModels}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Zap className="h-4 w-4 text-green-600" />
                    <div>
                      <p className="text-sm text-gray-600">Serving</p>
                      <p className="text-2xl font-bold">{platformMetrics.servingModels}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Activity className="h-4 w-4 text-yellow-600" />
                    <div>
                      <p className="text-sm text-gray-600">Avg Latency</p>
                      <p className="text-2xl font-bold">{platformMetrics.avgLatency.toFixed(0)}ms</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <AlertTriangle className="h-4 w-4 text-red-600" />
                    <div>
                      <p className="text-sm text-gray-600">Active Alerts</p>
                      <p className="text-2xl font-bold">{platformMetrics.activeAlerts}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Filters */}
            <div className="flex items-center gap-4">
              <div className="flex-1">
                <Input
                  placeholder="Search models..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full"
                />
              </div>
              <select
                value={selectedEnvironment}
                onChange={(e) => setSelectedEnvironment(e.target.value)}
                className="px-3 py-2 border rounded-md"
              >
                <option value="all">All Environments</option>
                <option value="production">Production</option>
                <option value="staging">Staging</option>
                <option value="development">Development</option>
              </select>
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="px-3 py-2 border rounded-md"
              >
                <option value="all">All Status</option>
                <option value="serving">Serving</option>
                <option value="deployed">Deployed</option>
                <option value="training">Training</option>
                <option value="paused">Paused</option>
              </select>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="px-3 py-2 border rounded-md"
              >
                <option value="name">Sort by Name</option>
                <option value="status">Sort by Status</option>
                <option value="accuracy">Sort by Accuracy</option>
                <option value="latency">Sort by Latency</option>
                <option value="cost">Sort by Cost</option>
              </select>
            </div>

            {/* Charts */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Framework Distribution</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <RechartsPieChart>
                      <Pie
                        data={modelDistributionData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percentage }) => `${name}: ${percentage}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {modelDistributionData.map((entry, index) => (
                          <Cell
                            key={`cell-${index}`}
                            fill={['#8884d8', '#82ca9d', '#ffc658', '#ff7300'][index % 4]}
                          />
                        ))}
                      </Pie>
                      <Tooltip />
                    </RechartsPieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Model Performance Overview</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <ScatterChart data={models.map(m => ({
                      name: m.name,
                      accuracy: m.performance.accuracy * 100,
                      latency: m.performance.latency,
                      requests: m.metrics.requests / 1000
                    }))}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="latency" name="Latency (ms)" />
                      <YAxis dataKey="accuracy" name="Accuracy (%)" />
                      <Tooltip cursor={{ strokeDasharray: '3 3' }} />
                      <Scatter name="Models" dataKey="accuracy" fill="#8884d8" />
                    </ScatterChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>

            {/* Models Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredModels.map((model) => (
                <ModelCard key={model.id} model={model} />
              ))}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="deployments">
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Active Deployments</h3>
              <Button size="sm">
                <Rocket className="h-4 w-4 mr-2" />
                New Deployment
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {deployments.map((deployment) => (
                <DeploymentCard key={deployment.id} deployment={deployment} />
              ))}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="monitoring">
          <div className="space-y-6">
            <h3 className="text-lg font-semibold">System Monitoring</h3>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Real-time Performance</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <ComposedChart data={performanceData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="hour" />
                      <YAxis yAxisId="left" />
                      <YAxis yAxisId="right" orientation="right" />
                      <Tooltip />
                      <Legend />
                      <Bar yAxisId="left" dataKey="requests" fill="#8884d8" name="Requests" />
                      <Line yAxisId="right" type="monotone" dataKey="latency" stroke="#ff7300" name="Latency (ms)" />
                    </ComposedChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Resource Usage</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <AreaChart data={performanceData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="hour" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Area type="monotone" dataKey="cpu" stackId="1" stroke="#8884d8" fill="#8884d8" name="CPU %" />
                      <Area type="monotone" dataKey="memory" stackId="1" stroke="#82ca9d" fill="#82ca9d" name="Memory %" />
                    </AreaChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>

            {/* Alerts */}
            <div>
              <h4 className="text-md font-semibold mb-4">Active Alerts</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {models
                  .flatMap(m => m.monitoring.alerts)
                  .filter(alert => alert.status === 'active')
                  .slice(0, 6)
                  .map((alert) => (
                    <AlertCard key={alert.id} alert={alert} />
                  ))}
              </div>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="experiments">
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">A/B Testing & Experiments</h3>
              <Button size="sm">
                <FlaskConical className="h-4 w-4 mr-2" />
                Create Experiment
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {experiments.map((experiment) => (
                <motion.div
                  key={experiment.id}
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                >
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center justify-between">
                        {experiment.name}
                        <Badge variant={experiment.status === 'running' ? 'default' : 'secondary'}>
                          {experiment.status}
                        </Badge>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-gray-600 mb-4">{experiment.description}</p>
                      
                      <div className="space-y-3">
                        {experiment.models.map((model, index) => (
                          <div key={model.modelId} className="border rounded p-3">
                            <div className="flex items-center justify-between mb-2">
                              <span className="font-medium">{model.modelId}</span>
                              <span className="text-sm text-gray-600">{model.traffic}% traffic</span>
                            </div>
                            <div className="grid grid-cols-3 gap-4 text-sm">
                              <div>
                                <span className="text-gray-600">Conversions</span>
                                <p className="font-medium">{model.conversions}</p>
                              </div>
                              <div>
                                <span className="text-gray-600">Revenue</span>
                                <p className="font-medium">${model.revenue.toFixed(0)}</p>
                              </div>
                              <div>
                                <span className="text-gray-600">Accuracy</span>
                                <p className="font-medium">{(model.metrics.accuracy * 100).toFixed(1)}%</p>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>

                      <div className="mt-4 flex items-center justify-between text-sm">
                        <span className="text-gray-600">
                          Started: {experiment.startDate.toLocaleDateString()}
                        </span>
                        {experiment.winner && (
                          <Badge variant="outline" className="text-green-600">
                            Winner: {experiment.winner}
                          </Badge>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="performance">
          <div className="space-y-6">
            <h3 className="text-lg font-semibold">Performance Analytics</h3>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Request Volume & Latency</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <ComposedChart data={performanceData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="hour" />
                      <YAxis yAxisId="left" />
                      <YAxis yAxisId="right" orientation="right" />
                      <Tooltip />
                      <Legend />
                      <Bar yAxisId="left" dataKey="requests" fill="#8884d8" name="Requests/hour" />
                      <Line yAxisId="right" type="monotone" dataKey="latency" stroke="#ff7300" name="Avg Latency (ms)" />
                    </ComposedChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Error Rate</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <AreaChart data={performanceData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="hour" />
                      <YAxis />
                      <Tooltip />
                      <Area type="monotone" dataKey="errors" stroke="#ef4444" fill="#ef4444" />
                    </AreaChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>

            {/* Performance Table */}
            <Card>
              <CardHeader>
                <CardTitle>Model Performance Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left p-2">Model</th>
                        <th className="text-left p-2">Requests</th>
                        <th className="text-left p-2">Success Rate</th>
                        <th className="text-left p-2">Avg Latency</th>
                        <th className="text-left p-2">P95 Latency</th>
                        <th className="text-left p-2">Throughput</th>
                      </tr>
                    </thead>
                    <tbody>
                      {models.slice(0, 8).map((model) => (
                        <motion.tr
                          key={model.id}
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          className="border-b hover:bg-gray-50"
                        >
                          <td className="p-2 font-medium">{model.name}</td>
                          <td className="p-2">{model.metrics.requests.toLocaleString()}</td>
                          <td className="p-2">
                            {((model.metrics.successfulPredictions / model.metrics.requests) * 100).toFixed(1)}%
                          </td>
                          <td className="p-2">{model.metrics.avgResponseTime.toFixed(0)}ms</td>
                          <td className="p-2">{model.metrics.p95ResponseTime.toFixed(0)}ms</td>
                          <td className="p-2">{model.performance.throughput.toFixed(0)} RPS</td>
                        </motion.tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="costs">
          <div className="space-y-6">
            <h3 className="text-lg font-semibold">Cost Analysis</h3>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Server className="h-4 w-4 text-green-600" />
                    <div>
                      <p className="text-sm text-gray-600">Total Monthly</p>
                      <p className="text-2xl font-bold">${(platformMetrics.totalCost * 24 * 30).toFixed(0)}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Cpu className="h-4 w-4 text-blue-600" />
                    <div>
                      <p className="text-sm text-gray-600">Compute</p>
                      <p className="text-2xl font-bold">${(platformMetrics.totalCost * 0.7 * 24 * 30).toFixed(0)}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <HardDrive className="h-4 w-4 text-yellow-600" />
                    <div>
                      <p className="text-sm text-gray-600">Storage</p>
                      <p className="text-2xl font-bold">${(platformMetrics.totalCost * 0.2 * 24 * 30).toFixed(0)}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Network className="h-4 w-4 text-purple-600" />
                    <div>
                      <p className="text-sm text-gray-600">Network</p>
                      <p className="text-2xl font-bold">${(platformMetrics.totalCost * 0.1 * 24 * 30).toFixed(0)}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Cost Trends (30 Days)</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={400}>
                  <AreaChart data={costTrendData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="day" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Area type="monotone" dataKey="compute" stackId="1" stroke="#8884d8" fill="#8884d8" name="Compute" />
                    <Area type="monotone" dataKey="storage" stackId="1" stroke="#82ca9d" fill="#82ca9d" name="Storage" />
                    <Area type="monotone" dataKey="network" stackId="1" stroke="#ffc658" fill="#ffc658" name="Network" />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Cost per Model */}
            <Card>
              <CardHeader>
                <CardTitle>Cost per Model</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {models.slice(0, 8).map((model) => (
                    <div key={model.id} className="flex items-center justify-between p-3 border rounded">
                      <div>
                        <p className="font-medium">{model.name}</p>
                        <p className="text-sm text-gray-600">{model.deployment.environment}</p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">${model.cost.hourly.toFixed(2)}/hr</p>
                        <p className="text-sm text-gray-600">${model.cost.monthly.toFixed(0)}/mo</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default MLModelServing;