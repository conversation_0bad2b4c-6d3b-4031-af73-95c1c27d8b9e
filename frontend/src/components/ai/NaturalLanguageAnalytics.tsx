/**
 * Natural Language Analytics Platform
 * AI-powered text analysis, sentiment insights, and automated content intelligence
 */

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  MessageCircle,
  Brain,
  Heart,
  ThumbsUp,
  ThumbsDown,
  Star,
  TrendingUp,
  TrendingDown,
  Activity,
  BarChart3,
  LineChart,
  PieChart,
  Users,
  Target,
  Eye,
  Zap,
  Clock,
  Calendar,
  Globe,
  Filter,
  Search,
  Download,
  Upload,
  Share,
  Settings,
  RefreshCw,
  Lightbulb,
  AlertTriangle,
  CheckCircle,
  Info,
  FileText,
  BookOpen,
  Mic,
  Speaker,
  Volume2,
  Languages,
  Type,
  AlignLeft,
  Hash,
  Tag,
  Quote,
  Sparkles,
  Wand2,
  Focus,
  Layers,
  Network,
  Database,
  Cpu,
  Monitor,
  Smartphone,
  Tablet,
  Mail,
  Phone,
  Bell,
  BellRing,
  Award,
  Crown,
  Gem,
  Trophy,
  Flag,
  MapPin,
  Navigation,
  Compass,
  Route,
  ArrowUp,
  ArrowDown,
  ArrowRight,
  ArrowLeft,
  ChevronUp,
  ChevronDown,
  Plus,
  Minus,
  Edit,
  Trash,
  MoreHorizontal,
  ExternalLink,
  Link2,
  Save,
  Archive,
  Folder,
  File,
  Image,
  Video,
  Music
} from 'lucide-react';
import {
  LineChart as RechartsLineChart,
  AreaChart,
  BarChart,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Area,
  Bar,
  Line,
  PieChart as RechartsPieChart,
  Cell,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
  ComposedChart,
  ScatterChart,
  Scatter,
  TreeMap
} from 'recharts';

// Natural Language Processing Interfaces
interface TextAnalysisModel {
  id: string;
  name: string;
  type: 'sentiment' | 'emotion' | 'intent' | 'topic' | 'summary' | 'entity' | 'language' | 'toxicity';
  accuracy: number;
  speed: number;
  lastTrained: string;
  status: 'active' | 'training' | 'inactive';
  processedTexts: number;
  averageConfidence: number;
}

interface SentimentAnalysis {
  id: string;
  text: string;
  sentiment: 'positive' | 'negative' | 'neutral';
  confidence: number;
  emotions: {
    joy: number;
    anger: number;
    fear: number;
    sadness: number;
    surprise: number;
    disgust: number;
  };
  keywords: string[];
  entities: {
    type: 'person' | 'organization' | 'location' | 'product' | 'brand';
    text: string;
    confidence: number;
  }[];
  source: string;
  timestamp: string;
  language: string;
  toxicity: number;
}

interface TopicAnalysis {
  id: string;
  topic: string;
  keywords: string[];
  confidence: number;
  documents: number;
  sentiment: number;
  trending: boolean;
  relatedTopics: string[];
  timeframe: string;
  sources: string[];
}

interface ContentInsight {
  id: string;
  type: 'trend' | 'anomaly' | 'opportunity' | 'risk' | 'recommendation';
  title: string;
  description: string;
  confidence: number;
  impact: 'high' | 'medium' | 'low';
  category: string;
  actionable: boolean;
  relatedContent: string[];
  timestamp: string;
  priority: number;
}

interface LanguageMetrics {
  language: string;
  confidence: number;
  textCount: number;
  sentimentDistribution: {
    positive: number;
    negative: number;
    neutral: number;
  };
  topTopics: string[];
  avgComplexity: number;
  avgLength: number;
}

interface VoiceOfCustomer {
  id: string;
  customer: string;
  feedback: string;
  sentiment: number;
  emotions: string[];
  intent: string;
  urgency: 'high' | 'medium' | 'low';
  category: string;
  resolution: string;
  status: 'open' | 'in_progress' | 'resolved';
  timestamp: string;
  source: string;
  priority: number;
}

interface AutomatedReport {
  id: string;
  title: string;
  type: 'daily' | 'weekly' | 'monthly' | 'custom';
  summary: string;
  keyInsights: string[];
  sentimentTrends: any[];
  topicEvolution: any[];
  recommendations: string[];
  generatedAt: string;
  confidence: number;
  dataPoints: number;
  wordCount: number;
}

// Mock Data Generation
const generateNLPModels = (): TextAnalysisModel[] => {
  const models = [
    { name: 'Advanced Sentiment Engine', type: 'sentiment' as const, accuracy: 94.2 },
    { name: 'Emotion Recognition AI', type: 'emotion' as const, accuracy: 89.7 },
    { name: 'Intent Classification', type: 'intent' as const, accuracy: 91.5 },
    { name: 'Topic Modeling Pro', type: 'topic' as const, accuracy: 87.3 },
    { name: 'Smart Summarizer', type: 'summary' as const, accuracy: 92.8 },
    { name: 'Entity Extractor', type: 'entity' as const, accuracy: 95.1 },
    { name: 'Language Detector', type: 'language' as const, accuracy: 98.6 },
    { name: 'Toxicity Classifier', type: 'toxicity' as const, accuracy: 93.4 }
  ];

  return models.map((model, index) => ({
    id: `model-${index + 1}`,
    name: model.name,
    type: model.type,
    accuracy: model.accuracy,
    speed: Math.random() * 1000 + 100,
    lastTrained: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
    status: Math.random() > 0.1 ? 'active' : Math.random() > 0.5 ? 'training' : 'inactive',
    processedTexts: Math.floor(Math.random() * 100000) + 10000,
    averageConfidence: Math.random() * 30 + 70
  }));
};

const generateSentimentData = (): SentimentAnalysis[] => {
  const sources = ['reviews', 'social_media', 'support_tickets', 'surveys', 'comments'];
  const sentiments = ['positive', 'negative', 'neutral'] as const;
  const languages = ['en', 'es', 'fr', 'de', 'ja', 'zh'];
  
  return Array.from({ length: 50 }, (_, index) => ({
    id: `sentiment-${index + 1}`,
    text: `Sample text analysis ${index + 1}`,
    sentiment: sentiments[Math.floor(Math.random() * sentiments.length)],
    confidence: Math.random() * 40 + 60,
    emotions: {
      joy: Math.random() * 100,
      anger: Math.random() * 100,
      fear: Math.random() * 100,
      sadness: Math.random() * 100,
      surprise: Math.random() * 100,
      disgust: Math.random() * 100
    },
    keywords: Array.from({ length: Math.floor(Math.random() * 5) + 2 }, (_, i) => `keyword${i + 1}`),
    entities: Array.from({ length: Math.floor(Math.random() * 3) + 1 }, (_, i) => ({
      type: ['person', 'organization', 'location', 'product', 'brand'][Math.floor(Math.random() * 5)] as any,
      text: `Entity ${i + 1}`,
      confidence: Math.random() * 40 + 60
    })),
    source: sources[Math.floor(Math.random() * sources.length)],
    timestamp: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
    language: languages[Math.floor(Math.random() * languages.length)],
    toxicity: Math.random() * 30
  }));
};

const generateTopicData = (): TopicAnalysis[] => {
  const topics = [
    'Product Quality', 'Customer Service', 'Shipping & Delivery', 'Pricing', 'User Experience',
    'Payment Issues', 'Returns & Refunds', 'Technical Support', 'Feature Requests', 'Bug Reports'
  ];
  
  return topics.map((topic, index) => ({
    id: `topic-${index + 1}`,
    topic,
    keywords: Array.from({ length: Math.floor(Math.random() * 8) + 3 }, (_, i) => `keyword${i + 1}`),
    confidence: Math.random() * 30 + 70,
    documents: Math.floor(Math.random() * 1000) + 100,
    sentiment: (Math.random() - 0.5) * 2,
    trending: Math.random() > 0.7,
    relatedTopics: topics.filter((_, i) => i !== index && Math.random() > 0.7).slice(0, 3),
    timeframe: ['1d', '7d', '30d'][Math.floor(Math.random() * 3)],
    sources: ['reviews', 'social_media', 'support_tickets'].filter(() => Math.random() > 0.5)
  }));
};

const generateContentInsights = (): ContentInsight[] => {
  const types = ['trend', 'anomaly', 'opportunity', 'risk', 'recommendation'] as const;
  const impacts = ['high', 'medium', 'low'] as const;
  
  return Array.from({ length: 20 }, (_, index) => ({
    id: `insight-${index + 1}`,
    type: types[Math.floor(Math.random() * types.length)],
    title: `Content Insight ${index + 1}`,
    description: `Detailed analysis and recommendations for insight ${index + 1}`,
    confidence: Math.random() * 30 + 70,
    impact: impacts[Math.floor(Math.random() * impacts.length)],
    category: ['sentiment', 'topics', 'engagement', 'quality'][Math.floor(Math.random() * 4)],
    actionable: Math.random() > 0.3,
    relatedContent: Array.from({ length: Math.floor(Math.random() * 3) + 1 }, (_, i) => `content${i + 1}`),
    timestamp: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000).toISOString(),
    priority: Math.floor(Math.random() * 10) + 1
  }));
};

const generateLanguageMetrics = (): LanguageMetrics[] => {
  const languages = [
    { language: 'English', code: 'en' },
    { language: 'Spanish', code: 'es' },
    { language: 'French', code: 'fr' },
    { language: 'German', code: 'de' },
    { language: 'Japanese', code: 'ja' },
    { language: 'Chinese', code: 'zh' }
  ];
  
  return languages.map(lang => ({
    language: lang.language,
    confidence: Math.random() * 20 + 80,
    textCount: Math.floor(Math.random() * 10000) + 1000,
    sentimentDistribution: {
      positive: Math.random() * 50 + 25,
      negative: Math.random() * 30 + 10,
      neutral: Math.random() * 40 + 20
    },
    topTopics: Array.from({ length: 5 }, (_, i) => `Topic ${i + 1}`),
    avgComplexity: Math.random() * 10 + 5,
    avgLength: Math.random() * 200 + 50
  }));
};

const generateVoiceOfCustomer = (): VoiceOfCustomer[] => {
  const intents = ['complaint', 'compliment', 'question', 'request', 'feedback'];
  const categories = ['product', 'service', 'billing', 'technical', 'general'];
  const urgencies = ['high', 'medium', 'low'] as const;
  const statuses = ['open', 'in_progress', 'resolved'] as const;
  
  return Array.from({ length: 30 }, (_, index) => ({
    id: `voc-${index + 1}`,
    customer: `Customer ${index + 1}`,
    feedback: `Customer feedback content ${index + 1}`,
    sentiment: (Math.random() - 0.5) * 2,
    emotions: ['joy', 'anger', 'surprise'].filter(() => Math.random() > 0.5),
    intent: intents[Math.floor(Math.random() * intents.length)],
    urgency: urgencies[Math.floor(Math.random() * urgencies.length)],
    category: categories[Math.floor(Math.random() * categories.length)],
    resolution: Math.random() > 0.5 ? `Resolution for feedback ${index + 1}` : '',
    status: statuses[Math.floor(Math.random() * statuses.length)],
    timestamp: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
    source: ['email', 'chat', 'phone', 'social'][Math.floor(Math.random() * 4)],
    priority: Math.floor(Math.random() * 10) + 1
  }));
};

const generateAutomatedReports = (): AutomatedReport[] => {
  const types = ['daily', 'weekly', 'monthly', 'custom'] as const;
  
  return Array.from({ length: 10 }, (_, index) => ({
    id: `report-${index + 1}`,
    title: `Automated Report ${index + 1}`,
    type: types[Math.floor(Math.random() * types.length)],
    summary: `Executive summary for report ${index + 1}`,
    keyInsights: Array.from({ length: Math.floor(Math.random() * 5) + 3 }, (_, i) => `Key insight ${i + 1}`),
    sentimentTrends: Array.from({ length: 7 }, (_, i) => ({
      date: new Date(Date.now() - (6 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      positive: Math.random() * 100,
      negative: Math.random() * 100,
      neutral: Math.random() * 100
    })),
    topicEvolution: Array.from({ length: 5 }, (_, i) => ({
      topic: `Topic ${i + 1}`,
      growth: (Math.random() - 0.5) * 100,
      volume: Math.random() * 1000
    })),
    recommendations: Array.from({ length: Math.floor(Math.random() * 3) + 2 }, (_, i) => `Recommendation ${i + 1}`),
    generatedAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
    confidence: Math.random() * 20 + 80,
    dataPoints: Math.floor(Math.random() * 50000) + 10000,
    wordCount: Math.floor(Math.random() * 5000) + 1000
  }));
};

const NaturalLanguageAnalytics: React.FC = () => {
  const [selectedTimeframe, setSelectedTimeframe] = useState('7d');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedModel, setSelectedModel] = useState('all');
  const [selectedLanguage, setSelectedLanguage] = useState('all');
  const [activeView, setActiveView] = useState('overview');

  // Generate mock data
  const nlpModels = useMemo(() => generateNLPModels(), []);
  const sentimentData = useMemo(() => generateSentimentData(), []);
  const topicData = useMemo(() => generateTopicData(), []);
  const contentInsights = useMemo(() => generateContentInsights(), []);
  const languageMetrics = useMemo(() => generateLanguageMetrics(), []);
  const voiceOfCustomer = useMemo(() => generateVoiceOfCustomer(), []);
  const automatedReports = useMemo(() => generateAutomatedReports(), []);

  // Filter and process data
  const filteredSentimentData = useMemo(() => {
    return sentimentData.filter(item => {
      const matchesSearch = searchTerm === '' || item.text.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesLanguage = selectedLanguage === 'all' || item.language === selectedLanguage;
      return matchesSearch && matchesLanguage;
    });
  }, [sentimentData, searchTerm, selectedLanguage]);

  const sentimentDistribution = useMemo(() => {
    const distribution = { positive: 0, negative: 0, neutral: 0 };
    filteredSentimentData.forEach(item => {
      distribution[item.sentiment]++;
    });
    return Object.entries(distribution).map(([sentiment, count]) => ({
      sentiment,
      count,
      percentage: (count / filteredSentimentData.length) * 100
    }));
  }, [filteredSentimentData]);

  const emotionAnalysis = useMemo(() => {
    const emotions = { joy: 0, anger: 0, fear: 0, sadness: 0, surprise: 0, disgust: 0 };
    filteredSentimentData.forEach(item => {
      Object.keys(emotions).forEach(emotion => {
        emotions[emotion as keyof typeof emotions] += item.emotions[emotion as keyof typeof item.emotions];
      });
    });
    
    const total = Object.values(emotions).reduce((sum, value) => sum + value, 0);
    return Object.entries(emotions).map(([emotion, value]) => ({
      emotion,
      value: value / filteredSentimentData.length,
      percentage: (value / total) * 100
    }));
  }, [filteredSentimentData]);

  const topicTrends = useMemo(() => {
    return topicData.map(topic => ({
      topic: topic.topic,
      volume: topic.documents,
      sentiment: topic.sentiment,
      trending: topic.trending,
      growth: (Math.random() - 0.5) * 50
    }));
  }, [topicData]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-600 bg-green-50';
      case 'training': return 'text-yellow-600 bg-yellow-50';
      case 'inactive': return 'text-gray-600 bg-gray-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getSentimentColor = (sentiment: string) => {
    switch (sentiment) {
      case 'positive': return 'text-green-600 bg-green-50';
      case 'negative': return 'text-red-600 bg-red-50';
      case 'neutral': return 'text-gray-600 bg-gray-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high': return 'text-red-600 bg-red-50';
      case 'medium': return 'text-yellow-600 bg-yellow-50';
      case 'low': return 'text-green-600 bg-green-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const colors = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4', '#84CC16', '#F97316'];

  return (
    <div className="p-6 space-y-6 bg-gradient-to-br from-indigo-50 via-white to-purple-50 min-h-screen">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-2">
            <MessageCircle className="text-indigo-600" />
            Natural Language Analytics
          </h1>
          <p className="text-gray-600 mt-1">AI-powered text analysis and content intelligence</p>
        </div>
        <div className="flex items-center gap-4">
          <select
            value={selectedTimeframe}
            onChange={(e) => setSelectedTimeframe(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
          >
            <option value="1d">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
            <option value="90d">Last 90 Days</option>
          </select>
          <Button className="bg-indigo-600 hover:bg-indigo-700">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh Analysis
          </Button>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-100">Texts Analyzed</p>
                  <p className="text-3xl font-bold">{filteredSentimentData.length.toLocaleString()}</p>
                  <p className="text-blue-100 text-sm">+{Math.floor(Math.random() * 20 + 5)}% vs last period</p>
                </div>
                <FileText className="h-12 w-12 text-blue-200" />
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <Card className="bg-gradient-to-r from-green-500 to-green-600 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-green-100">Positive Sentiment</p>
                  <p className="text-3xl font-bold">{sentimentDistribution.find(s => s.sentiment === 'positive')?.percentage.toFixed(1) || 0}%</p>
                  <p className="text-green-100 text-sm">Avg. Confidence: {(Math.random() * 20 + 80).toFixed(1)}%</p>
                </div>
                <ThumbsUp className="h-12 w-12 text-green-200" />
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <Card className="bg-gradient-to-r from-purple-500 to-purple-600 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-purple-100">Active Models</p>
                  <p className="text-3xl font-bold">{nlpModels.filter(m => m.status === 'active').length}</p>
                  <p className="text-purple-100 text-sm">Avg. Accuracy: {(nlpModels.reduce((sum, m) => sum + m.accuracy, 0) / nlpModels.length).toFixed(1)}%</p>
                </div>
                <Brain className="h-12 w-12 text-purple-200" />
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          <Card className="bg-gradient-to-r from-orange-500 to-orange-600 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-orange-100">Languages Detected</p>
                  <p className="text-3xl font-bold">{languageMetrics.length}</p>
                  <p className="text-orange-100 text-sm">Top: {languageMetrics[0]?.language || 'English'}</p>
                </div>
                <Languages className="h-12 w-12 text-orange-200" />
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeView} onValueChange={setActiveView} className="space-y-6">
        <TabsList className="grid w-full grid-cols-7">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="sentiment">Sentiment</TabsTrigger>
          <TabsTrigger value="topics">Topics</TabsTrigger>
          <TabsTrigger value="insights">Insights</TabsTrigger>
          <TabsTrigger value="voice">Voice of Customer</TabsTrigger>
          <TabsTrigger value="models">Models</TabsTrigger>
          <TabsTrigger value="reports">Reports</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Sentiment Distribution */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <PieChart className="h-5 w-5 text-indigo-600" />
                  Sentiment Distribution
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <RechartsPieChart>
                    <Tooltip />
                    <Legend />
                    <RechartsPieChart data={sentimentDistribution}>
                      {sentimentDistribution.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
                      ))}
                    </RechartsPieChart>
                  </RechartsPieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Emotion Analysis */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Heart className="h-5 w-5 text-red-600" />
                  Emotion Analysis
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <RadarChart data={emotionAnalysis}>
                    <PolarGrid />
                    <PolarAngleAxis dataKey="emotion" />
                    <PolarRadiusAxis />
                    <Radar name="Intensity" dataKey="value" stroke="#8884d8" fill="#8884d8" fillOpacity={0.6} />
                    <Tooltip />
                  </RadarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          {/* Topic Trends */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5 text-green-600" />
                Trending Topics
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <BarChart data={topicTrends}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="topic" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="volume" fill="#3B82F6" name="Volume" />
                  <Bar dataKey="sentiment" fill="#10B981" name="Sentiment Score" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Sentiment Analysis Tab */}
        <TabsContent value="sentiment" className="space-y-6">
          {/* Search and Filters */}
          <Card>
            <CardContent className="p-4">
              <div className="flex flex-wrap gap-4 items-center">
                <div className="flex-1 min-w-64">
                  <Input
                    placeholder="Search sentiment data..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full"
                  />
                </div>
                <select
                  value={selectedLanguage}
                  onChange={(e) => setSelectedLanguage(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                >
                  <option value="all">All Languages</option>
                  <option value="en">English</option>
                  <option value="es">Spanish</option>
                  <option value="fr">French</option>
                  <option value="de">German</option>
                  <option value="ja">Japanese</option>
                  <option value="zh">Chinese</option>
                </select>
                <Button variant="outline">
                  <Filter className="h-4 w-4 mr-2" />
                  Filters
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Sentiment Analysis Results */}
          <div className="grid gap-4">
            {filteredSentimentData.slice(0, 10).map((item) => (
              <motion.div
                key={item.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
              >
                <Card>
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start mb-3">
                      <div className="flex items-center gap-2">
                        <Badge className={getSentimentColor(item.sentiment)}>
                          {item.sentiment}
                        </Badge>
                        <Badge variant="outline">
                          {(item.confidence).toFixed(1)}% confidence
                        </Badge>
                        <Badge variant="outline">
                          {item.language.toUpperCase()}
                        </Badge>
                      </div>
                      <span className="text-sm text-gray-500">
                        {new Date(item.timestamp).toLocaleDateString()}
                      </span>
                    </div>
                    
                    <p className="text-gray-800 mb-3">{item.text}</p>
                    
                    <div className="flex flex-wrap gap-2 mb-3">
                      {item.keywords.map((keyword, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {keyword}
                        </Badge>
                      ))}
                    </div>
                    
                    <div className="grid grid-cols-3 md:grid-cols-6 gap-2 text-xs">
                      {Object.entries(item.emotions).map(([emotion, value]) => (
                        <div key={emotion} className="text-center">
                          <div className="text-gray-600 capitalize">{emotion}</div>
                          <div className="font-semibold">{value.toFixed(0)}%</div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </TabsContent>

        {/* Topics Tab */}
        <TabsContent value="topics" className="space-y-6">
          <div className="grid gap-4">
            {topicData.map((topic) => (
              <motion.div
                key={topic.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                <Card>
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start mb-3">
                      <div className="flex items-center gap-2">
                        <h3 className="font-semibold text-lg">{topic.topic}</h3>
                        {topic.trending && (
                          <Badge className="bg-orange-100 text-orange-800">
                            <TrendingUp className="h-3 w-3 mr-1" />
                            Trending
                          </Badge>
                        )}
                      </div>
                      <div className="text-right">
                        <div className="text-sm text-gray-600">{topic.documents} documents</div>
                        <div className="text-sm font-semibold">{(topic.confidence).toFixed(1)}% confidence</div>
                      </div>
                    </div>
                    
                    <div className="flex flex-wrap gap-2 mb-3">
                      {topic.keywords.map((keyword, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          #{keyword}
                        </Badge>
                      ))}
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-1">
                          <div className={`w-3 h-3 rounded-full ${topic.sentiment > 0 ? 'bg-green-500' : topic.sentiment < 0 ? 'bg-red-500' : 'bg-gray-500'}`}></div>
                          <span className="text-sm">Sentiment: {topic.sentiment.toFixed(2)}</span>
                        </div>
                        <div className="text-sm text-gray-600">
                          Sources: {topic.sources.join(', ')}
                        </div>
                      </div>
                      <Button variant="outline" size="sm">
                        <Eye className="h-4 w-4 mr-1" />
                        View Details
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </TabsContent>

        {/* Content Insights Tab */}
        <TabsContent value="insights" className="space-y-6">
          <div className="grid gap-4">
            {contentInsights.map((insight) => (
              <motion.div
                key={insight.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
              >
                <Card>
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start mb-3">
                      <div className="flex items-center gap-2">
                        <div className="flex items-center gap-2">
                          {insight.type === 'trend' && <TrendingUp className="h-4 w-4 text-green-600" />}
                          {insight.type === 'anomaly' && <AlertTriangle className="h-4 w-4 text-yellow-600" />}
                          {insight.type === 'opportunity' && <Target className="h-4 w-4 text-blue-600" />}
                          {insight.type === 'risk' && <AlertTriangle className="h-4 w-4 text-red-600" />}
                          {insight.type === 'recommendation' && <Lightbulb className="h-4 w-4 text-purple-600" />}
                          <h3 className="font-semibold">{insight.title}</h3>
                        </div>
                        <Badge className={getImpactColor(insight.impact)}>
                          {insight.impact} impact
                        </Badge>
                      </div>
                      <div className="text-right">
                        <div className="text-sm text-gray-600">{(insight.confidence).toFixed(1)}% confidence</div>
                        <div className="text-xs text-gray-500">Priority: {insight.priority}</div>
                      </div>
                    </div>
                    
                    <p className="text-gray-700 mb-3">{insight.description}</p>
                    
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-4">
                        <Badge variant="outline" className="text-xs">
                          {insight.category}
                        </Badge>
                        {insight.actionable && (
                          <Badge className="bg-green-100 text-green-800 text-xs">
                            Actionable
                          </Badge>
                        )}
                      </div>
                      <span className="text-xs text-gray-500">
                        {new Date(insight.timestamp).toLocaleDateString()}
                      </span>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </TabsContent>

        {/* Voice of Customer Tab */}
        <TabsContent value="voice" className="space-y-6">
          <div className="grid gap-4">
            {voiceOfCustomer.map((voc) => (
              <motion.div
                key={voc.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                <Card>
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start mb-3">
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4 text-blue-600" />
                        <h3 className="font-semibold">{voc.customer}</h3>
                        <Badge className={voc.urgency === 'high' ? 'bg-red-100 text-red-800' : voc.urgency === 'medium' ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'}>
                          {voc.urgency} urgency
                        </Badge>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-semibold">{voc.intent}</div>
                        <div className="text-xs text-gray-500">{voc.source}</div>
                      </div>
                    </div>
                    
                    <p className="text-gray-700 mb-3">{voc.feedback}</p>
                    
                    <div className="flex flex-wrap gap-2 mb-3">
                      {voc.emotions.map((emotion, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {emotion}
                        </Badge>
                      ))}
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-1">
                          <div className={`w-3 h-3 rounded-full ${voc.sentiment > 0 ? 'bg-green-500' : voc.sentiment < 0 ? 'bg-red-500' : 'bg-gray-500'}`}></div>
                          <span className="text-sm">Sentiment: {voc.sentiment.toFixed(2)}</span>
                        </div>
                        <Badge variant="outline" className="text-xs">
                          {voc.category}
                        </Badge>
                        <Badge className={voc.status === 'resolved' ? 'bg-green-100 text-green-800' : voc.status === 'in_progress' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'}>
                          {voc.status}
                        </Badge>
                      </div>
                      <span className="text-xs text-gray-500">
                        {new Date(voc.timestamp).toLocaleDateString()}
                      </span>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </TabsContent>

        {/* Models Tab */}
        <TabsContent value="models" className="space-y-6">
          <div className="grid gap-4">
            {nlpModels.map((model) => (
              <motion.div
                key={model.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
              >
                <Card>
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start mb-3">
                      <div className="flex items-center gap-2">
                        <Brain className="h-5 w-5 text-purple-600" />
                        <h3 className="font-semibold text-lg">{model.name}</h3>
                        <Badge className={getStatusColor(model.status)}>
                          {model.status}
                        </Badge>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-semibold">{model.accuracy.toFixed(1)}% accuracy</div>
                        <div className="text-xs text-gray-500">{model.speed.toFixed(0)}ms avg speed</div>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-3">
                      <div>
                        <div className="text-xs text-gray-600">Type</div>
                        <div className="font-semibold capitalize">{model.type}</div>
                      </div>
                      <div>
                        <div className="text-xs text-gray-600">Processed</div>
                        <div className="font-semibold">{model.processedTexts.toLocaleString()}</div>
                      </div>
                      <div>
                        <div className="text-xs text-gray-600">Confidence</div>
                        <div className="font-semibold">{model.averageConfidence.toFixed(1)}%</div>
                      </div>
                      <div>
                        <div className="text-xs text-gray-600">Last Trained</div>
                        <div className="font-semibold">{new Date(model.lastTrained).toLocaleDateString()}</div>
                      </div>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          <Settings className="h-4 w-4 mr-1" />
                          Configure
                        </Button>
                        <Button variant="outline" size="sm">
                          <Activity className="h-4 w-4 mr-1" />
                          Performance
                        </Button>
                      </div>
                      <Button variant="outline" size="sm">
                        <RefreshCw className="h-4 w-4 mr-1" />
                        Retrain
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </TabsContent>

        {/* Automated Reports Tab */}
        <TabsContent value="reports" className="space-y-6">
          <div className="grid gap-4">
            {automatedReports.map((report) => (
              <motion.div
                key={report.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                <Card>
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start mb-3">
                      <div className="flex items-center gap-2">
                        <FileText className="h-5 w-5 text-indigo-600" />
                        <h3 className="font-semibold text-lg">{report.title}</h3>
                        <Badge variant="outline" className="capitalize">
                          {report.type}
                        </Badge>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-semibold">{report.confidence.toFixed(1)}% confidence</div>
                        <div className="text-xs text-gray-500">{report.wordCount.toLocaleString()} words</div>
                      </div>
                    </div>
                    
                    <p className="text-gray-700 mb-3">{report.summary}</p>
                    
                    <div className="space-y-2 mb-3">
                      <div className="text-sm font-semibold">Key Insights:</div>
                      <ul className="list-disc list-inside space-y-1">
                        {report.keyInsights.slice(0, 3).map((insight, index) => (
                          <li key={index} className="text-sm text-gray-600">{insight}</li>
                        ))}
                      </ul>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-4">
                        <div className="text-sm text-gray-600">
                          {report.dataPoints.toLocaleString()} data points analyzed
                        </div>
                        <span className="text-xs text-gray-500">
                          Generated: {new Date(report.generatedAt).toLocaleDateString()}
                        </span>
                      </div>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          <Download className="h-4 w-4 mr-1" />
                          Download
                        </Button>
                        <Button variant="outline" size="sm">
                          <Share className="h-4 w-4 mr-1" />
                          Share
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default NaturalLanguageAnalytics;