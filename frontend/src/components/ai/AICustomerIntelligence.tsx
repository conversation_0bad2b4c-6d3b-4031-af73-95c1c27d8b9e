/**
 * AI-Powered Customer Intelligence Dashboard
 * Advanced customer behavior analysis, churn prediction, and personalization insights
 */

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Users,
  Brain,
  Target,
  TrendingUp,
  TrendingDown,
  Activity,
  Heart,
  Star,
  AlertTriangle,
  CheckCircle,
  Clock,
  DollarSign,
  ShoppingCart,
  Eye,
  MessageCircle,
  ThumbsUp,
  UserCheck,
  UserX,
  UserPlus,
  BarChart3,
  LineChart,
  PieChart,
  Zap,
  Settings,
  RefreshCw,
  Download,
  Upload,
  Share,
  Filter,
  Search,
  Calendar,
  MapPin,
  Globe,
  Smartphone,
  Monitor,
  Tablet,
  Navigation,
  Compass,
  Route,
  Award,
  Crown,
  Gem,
  Sparkles,
  Wand2,
  Focus,
  Crosshair,
  Layers,
  Network,
  Database,
  Cpu,
  Cloud,
  Server,
  Gauge,
  Lightbulb,
  Rocket,
  ArrowUp,
  ArrowDown,
  ArrowRight,
  ArrowLeft,
  ChevronUp,
  ChevronDown,
  Plus,
  Minus,
  Edit,
  Trash,
  MoreHorizontal,
  ExternalLink,
  Link2,
  BookOpen,
  FileText,
  File,
  Folder,
  Archive,
  Save,
  Mail,
  Phone,
  Bell,
  BellRing,
  Volume2,
  Mic,
  Speaker,
  Headphones,
  Watch,
  Laptop,
  Wifi,
  Signal,
  Battery,
  Power,
  Shield,
  Lock,
  Key,
  Unlock,
  PersonStanding,
  Building2,
  Home,
  Store,
  Car,
  Plane
} from 'lucide-react';
import { LineChart as RechartsLineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area, BarChart as RechartsBarChart, Bar, ComposedChart, ScatterChart, Scatter, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar, Cell, Treemap, Sankey, PieChart as RechartsPieChart, Pie } from 'recharts';

// TypeScript interfaces for AI customer intelligence
interface CustomerProfile {
  id: string;
  customerId: string;
  demographics: {
    ageGroup: string;
    gender: string;
    location: {
      country: string;
      state: string;
      city: string;
      coordinates: { lat: number; lng: number };
    };
    income: string;
    education: string;
    profession: string;
    maritalStatus: string;
    householdSize: number;
  };
  behavioralSegment: 'high_value' | 'frequent_buyer' | 'discount_seeker' | 'brand_loyal' | 'price_sensitive' | 'occasional' | 'new_customer' | 'at_risk';
  engagementProfile: {
    totalSessions: number;
    averageSessionDuration: number;
    pagesPerSession: number;
    bounceRate: number;
    lastActiveDate: string;
    firstInteractionDate: string;
    preferredChannels: string[];
    devicePreferences: { device: string; usage: number }[];
  };
  purchaseHistory: {
    totalOrders: number;
    totalSpent: number;
    averageOrderValue: number;
    firstPurchaseDate: string;
    lastPurchaseDate: string;
    purchaseFrequency: number;
    preferredCategories: { category: string; spend: number; frequency: number }[];
    seasonalPatterns: { season: string; activity: number }[];
  };
  psychographics: {
    interests: string[];
    values: string[];
    lifestyle: string[];
    personality: {
      openness: number;
      conscientiousness: number;
      extraversion: number;
      agreeableness: number;
      neuroticism: number;
    };
    motivations: string[];
    painPoints: string[];
  };
  digitalFootprint: {
    socialMediaActivity: { platform: string; engagement: number }[];
    contentPreferences: string[];
    communicationStyle: string;
    influenceLevel: number;
    reviewActivity: { reviews: number; averageRating: number };
    referralActivity: { referrals: number; successRate: number };
  };
  predictiveMetrics: {
    churnProbability: number;
    lifetimeValue: number;
    nextPurchaseProbability: number;
    upsellPotential: number;
    brandLoyalty: number;
    priceElasticity: number;
    responsiveness: number;
  };
  aiInsights: {
    primaryMotivations: string[];
    preferredCommunication: string;
    optimizedTouchpoints: string[];
    personalizationOpportunities: string[];
    retentionStrategies: string[];
    growthOpportunities: string[];
  };
  riskFactors: {
    churnRisk: { level: 'low' | 'medium' | 'high' | 'critical'; factors: string[] };
    fraudRisk: { score: number; indicators: string[] };
    creditRisk: { rating: string; factors: string[] };
  };
  lastUpdated: string;
}

interface ChurnPrediction {
  customerId: string;
  churnProbability: number;
  churnRisk: 'low' | 'medium' | 'high' | 'critical';
  timeToChurn: number; // days
  confidenceScore: number;
  keyFactors: {
    factor: string;
    impact: number;
    category: 'behavioral' | 'transactional' | 'engagement' | 'external';
    description: string;
  }[];
  earlyWarningSignals: {
    signal: string;
    severity: number;
    trend: 'increasing' | 'decreasing' | 'stable';
    description: string;
  }[];
  interventionRecommendations: {
    strategy: string;
    priority: 'low' | 'medium' | 'high' | 'urgent';
    expectedImpact: number;
    effort: 'low' | 'medium' | 'high';
    channel: string;
    timing: string;
    personalization: string[];
  }[];
  cohortComparison: {
    segment: string;
    averageChurnRate: number;
    relativeRisk: number;
  };
  historicalPatterns: {
    similarCustomers: number;
    successfulRetentions: number;
    retentionStrategies: string[];
  };
  businessImpact: {
    revenueAtRisk: number;
    lifetimeValueLoss: number;
    replacementCost: number;
    retentionValue: number;
  };
  predictionDate: string;
  expiryDate: string;
}

interface PersonalizationEngine {
  customerId: string;
  personalizationProfile: {
    contentPreferences: {
      format: string[];
      topics: string[];
      tone: string;
      complexity: string;
      frequency: string;
    };
    productRecommendations: {
      immediate: { productId: string; score: number; reason: string }[];
      seasonal: { productId: string; score: number; season: string }[];
      trending: { productId: string; score: number; trend: string }[];
      complementary: { productId: string; score: number; baseProduct: string }[];
    };
    pricingStrategy: {
      priceElasticity: number;
      discountSensitivity: number;
      premiumWillingness: number;
      optimalPriceRange: { min: number; max: number };
      promotionEffectiveness: { type: string; effectiveness: number }[];
    };
    communicationOptimization: {
      preferredChannels: { channel: string; effectiveness: number }[];
      optimalTiming: { day: string; hour: number; effectiveness: number }[];
      messageFrequency: string;
      responsePatterns: { trigger: string; response: number }[];
    };
    experiencePersonalization: {
      preferredJourney: string[];
      featureUsage: { feature: string; usage: number }[];
      interfacePreferences: { element: string; preference: string }[];
      accessibilityNeeds: string[];
    };
  };
  recommendations: {
    immediate: string[];
    shortTerm: string[];
    longTerm: string[];
  };
  abTestSegments: {
    segmentId: string;
    hypothesis: string;
    expectedLift: number;
    status: 'planned' | 'running' | 'completed' | 'paused';
  }[];
  performanceMetrics: {
    engagementLift: number;
    conversionLift: number;
    revenueImpact: number;
    satisfactionScore: number;
  };
}

interface CustomerSegment {
  id: string;
  name: string;
  description: string;
  criteria: {
    demographic: Record<string, any>;
    behavioral: Record<string, any>;
    transactional: Record<string, any>;
    psychographic: Record<string, any>;
  };
  size: number;
  percentage: number;
  growth: number;
  characteristics: {
    averageAge: number;
    averageIncome: number;
    averageOrderValue: number;
    averageLifetimeValue: number;
    churnRate: number;
    engagementScore: number;
  };
  preferences: {
    channels: { channel: string; preference: number }[];
    products: { category: string; affinity: number }[];
    communication: { style: string; effectiveness: number }[];
    timing: { period: string; activity: number }[];
  };
  businessValue: {
    revenueContribution: number;
    profitMargin: number;
    acquisitionCost: number;
    retentionCost: number;
    growthPotential: number;
  };
  strategies: {
    acquisition: string[];
    retention: string[];
    growth: string[];
    winback: string[];
  };
  kpis: {
    primary: { metric: string; current: number; target: number; trend: number }[];
    secondary: { metric: string; current: number; benchmark: number }[];
  };
  insights: {
    opportunities: string[];
    threats: string[];
    recommendations: string[];
  };
}

interface BehaviorPattern {
  id: string;
  patternType: 'purchase' | 'browsing' | 'engagement' | 'seasonal' | 'lifecycle' | 'abandonment';
  name: string;
  description: string;
  frequency: number;
  strength: number;
  affected_customers: number;
  timeframe: string;
  triggers: {
    trigger: string;
    probability: number;
    context: string;
  }[];
  sequence: {
    step: number;
    action: string;
    probability: number;
    avgDuration: number;
  }[];
  outcomes: {
    outcome: string;
    probability: number;
    value: number;
  }[];
  variations: {
    segment: string;
    difference: string;
    significance: number;
  }[];
  interventionPoints: {
    step: number;
    opportunity: string;
    impact: number;
    difficulty: number;
  }[];
  businessImpact: {
    revenueInfluence: number;
    conversionImpact: number;
    retentionEffect: number;
    satisfactionCorrelation: number;
  };
}

interface LifecycleStage {
  stage: 'prospect' | 'new_customer' | 'developing' | 'established' | 'loyal' | 'champion' | 'at_risk' | 'churned';
  customerCount: number;
  percentage: number;
  averageValue: number;
  averageDuration: number;
  characteristics: string[];
  typicalBehaviors: string[];
  keyMetrics: { metric: string; value: number; benchmark: number }[];
  transitions: {
    to: string;
    probability: number;
    avgTime: number;
    triggers: string[];
  }[];
  strategies: {
    objective: string;
    tactics: string[];
    expectedOutcome: string;
  }[];
  riskFactors: string[];
  opportunities: string[];
}

// Mock data generation functions
const generateCustomerProfiles = (): CustomerProfile[] => {
  const behavioralSegments = ['high_value', 'frequent_buyer', 'discount_seeker', 'brand_loyal', 'price_sensitive', 'occasional', 'new_customer', 'at_risk'] as const;
  const ageGroups = ['18-24', '25-34', '35-44', '45-54', '55-64', '65+'];
  const genders = ['Male', 'Female', 'Other'];
  const incomeRanges = ['<$30K', '$30-50K', '$50-75K', '$75-100K', '$100K+'];
  const educationLevels = ['High School', 'Bachelor\'s', 'Master\'s', 'PhD'];
  const professions = ['Student', 'Professional', 'Manager', 'Executive', 'Retired', 'Self-employed'];

  return Array.from({ length: 20 }, (_, i) => ({
    id: `profile-${i + 1}`,
    customerId: `customer-${i + 1}`,
    demographics: {
      ageGroup: ageGroups[i % ageGroups.length],
      gender: genders[i % genders.length],
      location: {
        country: 'United States',
        state: ['CA', 'NY', 'TX', 'FL', 'IL'][i % 5],
        city: ['Los Angeles', 'New York', 'Houston', 'Miami', 'Chicago'][i % 5],
        coordinates: { lat: 40.7128 + (Math.random() - 0.5) * 10, lng: -74.0060 + (Math.random() - 0.5) * 20 }
      },
      income: incomeRanges[i % incomeRanges.length],
      education: educationLevels[i % educationLevels.length],
      profession: professions[i % professions.length],
      maritalStatus: ['Single', 'Married', 'Divorced'][i % 3],
      householdSize: Math.floor(Math.random() * 5) + 1
    },
    behavioralSegment: behavioralSegments[i % behavioralSegments.length],
    engagementProfile: {
      totalSessions: Math.floor(Math.random() * 200) + 50,
      averageSessionDuration: Math.random() * 300 + 120,
      pagesPerSession: Math.random() * 10 + 3,
      bounceRate: Math.random() * 50 + 20,
      lastActiveDate: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
      firstInteractionDate: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString(),
      preferredChannels: ['Website', 'Mobile App', 'Email', 'Social Media'].slice(0, Math.floor(Math.random() * 3) + 1),
      devicePreferences: [
        { device: 'Mobile', usage: Math.random() * 60 + 20 },
        { device: 'Desktop', usage: Math.random() * 50 + 30 },
        { device: 'Tablet', usage: Math.random() * 30 + 10 }
      ]
    },
    purchaseHistory: {
      totalOrders: Math.floor(Math.random() * 50) + 5,
      totalSpent: Math.floor(Math.random() * 5000) + 500,
      averageOrderValue: Math.random() * 200 + 50,
      firstPurchaseDate: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString(),
      lastPurchaseDate: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000).toISOString(),
      purchaseFrequency: Math.random() * 5 + 1,
      preferredCategories: [
        { category: 'Electronics', spend: Math.random() * 1000 + 200, frequency: Math.floor(Math.random() * 10) + 1 },
        { category: 'Clothing', spend: Math.random() * 800 + 150, frequency: Math.floor(Math.random() * 8) + 1 },
        { category: 'Books', spend: Math.random() * 300 + 50, frequency: Math.floor(Math.random() * 15) + 1 }
      ],
      seasonalPatterns: [
        { season: 'Spring', activity: Math.random() * 100 },
        { season: 'Summer', activity: Math.random() * 100 },
        { season: 'Fall', activity: Math.random() * 100 },
        { season: 'Winter', activity: Math.random() * 100 }
      ]
    },
    psychographics: {
      interests: ['Technology', 'Travel', 'Food', 'Sports', 'Arts', 'Music'].slice(0, Math.floor(Math.random() * 4) + 2),
      values: ['Quality', 'Value', 'Convenience', 'Innovation', 'Sustainability'].slice(0, Math.floor(Math.random() * 3) + 2),
      lifestyle: ['Active', 'Social', 'Professional', 'Family-oriented', 'Minimalist'].slice(0, Math.floor(Math.random() * 3) + 1),
      personality: {
        openness: Math.random() * 100,
        conscientiousness: Math.random() * 100,
        extraversion: Math.random() * 100,
        agreeableness: Math.random() * 100,
        neuroticism: Math.random() * 100
      },
      motivations: ['Save time', 'Save money', 'Status', 'Quality', 'Convenience'].slice(0, Math.floor(Math.random() * 3) + 1),
      painPoints: ['High prices', 'Slow delivery', 'Poor quality', 'Complex process', 'Limited options'].slice(0, Math.floor(Math.random() * 2) + 1)
    },
    digitalFootprint: {
      socialMediaActivity: [
        { platform: 'Facebook', engagement: Math.random() * 100 },
        { platform: 'Instagram', engagement: Math.random() * 100 },
        { platform: 'Twitter', engagement: Math.random() * 100 }
      ],
      contentPreferences: ['Videos', 'Articles', 'Images', 'Reviews', 'Tutorials'].slice(0, Math.floor(Math.random() * 3) + 2),
      communicationStyle: ['Formal', 'Casual', 'Direct', 'Detailed'][Math.floor(Math.random() * 4)],
      influenceLevel: Math.random() * 100,
      reviewActivity: { reviews: Math.floor(Math.random() * 20), averageRating: Math.random() * 2 + 3 },
      referralActivity: { referrals: Math.floor(Math.random() * 10), successRate: Math.random() * 100 }
    },
    predictiveMetrics: {
      churnProbability: Math.random() * 100,
      lifetimeValue: Math.floor(Math.random() * 10000) + 1000,
      nextPurchaseProbability: Math.random() * 100,
      upsellPotential: Math.random() * 100,
      brandLoyalty: Math.random() * 100,
      priceElasticity: Math.random() * 2 + 0.5,
      responsiveness: Math.random() * 100
    },
    aiInsights: {
      primaryMotivations: ['Seeks convenience and quality', 'Values product innovation', 'Price-conscious but quality-minded'],
      preferredCommunication: 'Personalized emails with product recommendations',
      optimizedTouchpoints: ['Product detail pages', 'Email campaigns', 'Mobile notifications'],
      personalizationOpportunities: ['Custom product bundles', 'Timing-based promotions', 'Content personalization'],
      retentionStrategies: ['Loyalty program enrollment', 'Exclusive access offers', 'Proactive customer service'],
      growthOpportunities: ['Cross-category expansion', 'Premium tier advancement', 'Referral program activation']
    },
    riskFactors: {
      churnRisk: { 
        level: ['low', 'medium', 'high', 'critical'][Math.floor(Math.random() * 4)] as any,
        factors: ['Declining engagement', 'Price sensitivity', 'Competitor activity'].slice(0, Math.floor(Math.random() * 2) + 1)
      },
      fraudRisk: { 
        score: Math.random() * 100,
        indicators: ['Unusual purchase patterns', 'Multiple payment methods', 'Geographic inconsistencies'].slice(0, Math.floor(Math.random() * 2))
      },
      creditRisk: { 
        rating: ['Excellent', 'Good', 'Fair', 'Poor'][Math.floor(Math.random() * 4)],
        factors: ['Payment history', 'Credit utilization', 'Account age'].slice(0, Math.floor(Math.random() * 2) + 1)
      }
    },
    lastUpdated: new Date().toISOString()
  }));
};

const generateChurnPredictions = (): ChurnPrediction[] => {
  const riskLevels = ['low', 'medium', 'high', 'critical'] as const;
  const factorCategories = ['behavioral', 'transactional', 'engagement', 'external'] as const;

  return Array.from({ length: 15 }, (_, i) => ({
    customerId: `customer-${i + 1}`,
    churnProbability: Math.random() * 100,
    churnRisk: riskLevels[Math.floor(Math.random() * 4)],
    timeToChurn: Math.floor(Math.random() * 180) + 30, // 30-210 days
    confidenceScore: Math.random() * 30 + 70, // 70-100%
    keyFactors: [
      {
        factor: 'Decreased session frequency',
        impact: Math.random() * 40 + 20,
        category: 'behavioral',
        description: 'Customer login frequency has decreased by 60% over the last 30 days'
      },
      {
        factor: 'Lower purchase value',
        impact: Math.random() * 35 + 15,
        category: 'transactional',
        description: 'Average order value has declined from $150 to $89 in recent purchases'
      },
      {
        factor: 'Reduced email engagement',
        impact: Math.random() * 30 + 10,
        category: 'engagement',
        description: 'Email open rates have dropped from 45% to 12% in the last quarter'
      }
    ],
    earlyWarningSignals: [
      {
        signal: 'Support ticket frequency increase',
        severity: Math.random() * 100,
        trend: ['increasing', 'decreasing', 'stable'][Math.floor(Math.random() * 3)] as any,
        description: 'Customer has submitted 3 support tickets in the past week'
      },
      {
        signal: 'Cart abandonment rate spike',
        severity: Math.random() * 100,
        trend: ['increasing', 'decreasing', 'stable'][Math.floor(Math.random() * 3)] as any,
        description: 'Abandoned cart rate increased from 15% to 45% for this customer'
      }
    ],
    interventionRecommendations: [
      {
        strategy: 'Personalized retention offer',
        priority: ['low', 'medium', 'high', 'urgent'][Math.floor(Math.random() * 4)] as any,
        expectedImpact: Math.random() * 50 + 30,
        effort: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)] as any,
        channel: 'Email + Push Notification',
        timing: 'Within 48 hours',
        personalization: ['Product recommendations based on past purchases', 'Exclusive discount on favorite category']
      },
      {
        strategy: 'Proactive customer success outreach',
        priority: ['low', 'medium', 'high', 'urgent'][Math.floor(Math.random() * 4)] as any,
        expectedImpact: Math.random() * 40 + 25,
        effort: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)] as any,
        channel: 'Phone call',
        timing: 'Next business day',
        personalization: ['Acknowledge specific pain points', 'Offer dedicated account management']
      }
    ],
    cohortComparison: {
      segment: 'High-value customers',
      averageChurnRate: Math.random() * 20 + 5,
      relativeRisk: Math.random() * 3 + 0.5
    },
    historicalPatterns: {
      similarCustomers: Math.floor(Math.random() * 100) + 20,
      successfulRetentions: Math.floor(Math.random() * 80) + 10,
      retentionStrategies: ['Loyalty program', 'Exclusive offers', 'Personal service']
    },
    businessImpact: {
      revenueAtRisk: Math.floor(Math.random() * 5000) + 1000,
      lifetimeValueLoss: Math.floor(Math.random() * 10000) + 2000,
      replacementCost: Math.floor(Math.random() * 500) + 100,
      retentionValue: Math.floor(Math.random() * 8000) + 1500
    },
    predictionDate: new Date().toISOString(),
    expiryDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
  }));
};

const generateCustomerSegments = (): CustomerSegment[] => {
  const segmentNames = [
    'High-Value Loyalists',
    'Price-Sensitive Bargain Hunters',
    'Tech-Savvy Early Adopters',
    'Convenience-Focused Professionals',
    'Brand-Conscious Millennials',
    'Family-Oriented Value Seekers'
  ];

  return segmentNames.map((name, i) => ({
    id: `segment-${i + 1}`,
    name,
    description: [
      'Premium customers with high lifetime value and strong brand loyalty',
      'Cost-conscious shoppers who prioritize discounts and value deals',
      'Technology enthusiasts who adopt new products and features early',
      'Busy professionals who value convenience and time-saving solutions',
      'Quality-focused millennials with strong brand preferences',
      'Family shoppers looking for bulk purchases and practical solutions'
    ][i],
    criteria: {
      demographic: { ageRange: ['35-55', '25-45', '18-35', '30-50', '25-40', '30-55'][i] },
      behavioral: { engagement: ['high', 'medium', 'high', 'medium', 'high', 'medium'][i] },
      transactional: { frequency: ['high', 'medium', 'high', 'medium', 'medium', 'high'][i] },
      psychographic: { values: [['quality', 'loyalty'], ['value', 'savings'], ['innovation', 'technology'], ['convenience', 'efficiency'], ['brand', 'quality'], ['value', 'family']][i] }
    },
    size: Math.floor(Math.random() * 5000) + 1000,
    percentage: Math.random() * 25 + 5,
    growth: (Math.random() - 0.5) * 20,
    characteristics: {
      averageAge: Math.floor(Math.random() * 30) + 25,
      averageIncome: Math.floor(Math.random() * 80000) + 40000,
      averageOrderValue: Math.floor(Math.random() * 200) + 50,
      averageLifetimeValue: Math.floor(Math.random() * 5000) + 1000,
      churnRate: Math.random() * 15 + 3,
      engagementScore: Math.random() * 40 + 60
    },
    preferences: {
      channels: [
        { channel: 'Website', preference: Math.random() * 100 },
        { channel: 'Mobile App', preference: Math.random() * 100 },
        { channel: 'Email', preference: Math.random() * 100 },
        { channel: 'Social Media', preference: Math.random() * 100 }
      ],
      products: [
        { category: 'Electronics', affinity: Math.random() * 100 },
        { category: 'Fashion', affinity: Math.random() * 100 },
        { category: 'Home & Garden', affinity: Math.random() * 100 }
      ],
      communication: [
        { style: 'Personalized', effectiveness: Math.random() * 100 },
        { style: 'Informative', effectiveness: Math.random() * 100 },
        { style: 'Promotional', effectiveness: Math.random() * 100 }
      ],
      timing: [
        { period: 'Morning', activity: Math.random() * 100 },
        { period: 'Afternoon', activity: Math.random() * 100 },
        { period: 'Evening', activity: Math.random() * 100 }
      ]
    },
    businessValue: {
      revenueContribution: Math.random() * 30 + 10,
      profitMargin: Math.random() * 25 + 15,
      acquisitionCost: Math.floor(Math.random() * 200) + 50,
      retentionCost: Math.floor(Math.random() * 100) + 20,
      growthPotential: Math.random() * 100
    },
    strategies: {
      acquisition: ['Premium positioning', 'Referral programs', 'Influencer partnerships'],
      retention: ['Loyalty rewards', 'Exclusive access', 'Personal service'],
      growth: ['Category expansion', 'Premium upselling', 'Subscription models'],
      winback: ['Win-back campaigns', 'Special offers', 'Feedback collection']
    },
    kpis: {
      primary: [
        { metric: 'Revenue per Customer', current: Math.floor(Math.random() * 2000) + 500, target: Math.floor(Math.random() * 2500) + 700, trend: (Math.random() - 0.5) * 20 },
        { metric: 'Retention Rate', current: Math.random() * 30 + 70, target: Math.random() * 20 + 80, trend: (Math.random() - 0.5) * 10 }
      ],
      secondary: [
        { metric: 'Engagement Score', current: Math.random() * 40 + 60, benchmark: Math.random() * 30 + 70 },
        { metric: 'Satisfaction Score', current: Math.random() * 20 + 80, benchmark: Math.random() * 15 + 85 }
      ]
    },
    insights: {
      opportunities: ['Expand into adjacent categories', 'Increase purchase frequency', 'Develop subscription offerings'],
      threats: ['Competitive pressure', 'Economic sensitivity', 'Channel disruption'],
      recommendations: ['Implement loyalty program', 'Personalize communications', 'Optimize pricing strategy']
    }
  }));
};

const generateBehaviorPatterns = (): BehaviorPattern[] => {
  const patternTypes = ['purchase', 'browsing', 'engagement', 'seasonal', 'lifecycle', 'abandonment'] as const;

  return Array.from({ length: 8 }, (_, i) => ({
    id: `pattern-${i + 1}`,
    patternType: patternTypes[i % patternTypes.length],
    name: [
      'Weekend Purchase Surge',
      'Mobile-First Browse Pattern',
      'Email Click-Through Journey',
      'Holiday Shopping Behavior',
      'New Customer Onboarding',
      'Cart Abandonment Recovery',
      'Subscription Renewal Pattern',
      'Cross-Category Discovery'
    ][i],
    description: [
      'Customers show 40% higher purchase activity on weekends',
      'Mobile users browse 3x more pages before making decisions',
      'Email campaigns drive 25% of weekly engagement',
      'Holiday seasons show 300% increase in gift purchases',
      'New customers require 3-5 touchpoints before first purchase',
      'Cart abandonment peaks at 2pm and 9pm daily',
      'Subscription renewals correlate with usage frequency',
      'Electronics buyers often purchase accessories within 7 days'
    ][i],
    frequency: Math.random() * 100,
    strength: Math.random() * 100,
    affected_customers: Math.floor(Math.random() * 5000) + 500,
    timeframe: ['Weekly', 'Daily', 'Monthly', 'Seasonal', 'Quarterly', 'Daily', 'Monthly', 'Weekly'][i],
    triggers: [
      { trigger: 'Weekend leisure time', probability: 0.8, context: 'Higher disposable time and relaxed mindset' },
      { trigger: 'Mobile device usage', probability: 0.9, context: 'Convenience and accessibility during commute' },
      { trigger: 'Email notification', probability: 0.6, context: 'Direct marketing communication' }
    ],
    sequence: Array.from({ length: 5 }, (_, j) => ({
      step: j + 1,
      action: ['Browse', 'Compare', 'Add to Cart', 'Review', 'Purchase'][j],
      probability: Math.random() * 100,
      avgDuration: Math.random() * 300 + 60
    })),
    outcomes: [
      { outcome: 'Purchase', probability: Math.random() * 40 + 20, value: Math.random() * 200 + 50 },
      { outcome: 'Save for Later', probability: Math.random() * 30 + 10, value: Math.random() * 100 + 25 },
      { outcome: 'Abandonment', probability: Math.random() * 30 + 10, value: 0 }
    ],
    variations: [
      { segment: 'New Customers', difference: 'Lower conversion but higher exploration', significance: 0.75 },
      { segment: 'VIP Customers', difference: 'Faster decision-making process', significance: 0.85 }
    ],
    interventionPoints: [
      { step: 2, opportunity: 'Product recommendation', impact: 85, difficulty: 30 },
      { step: 4, opportunity: 'Urgency messaging', impact: 70, difficulty: 20 }
    ],
    businessImpact: {
      revenueInfluence: Math.random() * 100000 + 50000,
      conversionImpact: Math.random() * 25 + 15,
      retentionEffect: Math.random() * 20 + 10,
      satisfactionCorrelation: Math.random() * 30 + 70
    }
  }));
};

const generateLifecycleStages = (): LifecycleStage[] => {
  const stages = ['prospect', 'new_customer', 'developing', 'established', 'loyal', 'champion', 'at_risk', 'churned'] as const;

  return stages.map((stage, i) => ({
    stage,
    customerCount: Math.floor(Math.random() * 2000) + 500,
    percentage: Math.random() * 20 + 5,
    averageValue: Math.floor(Math.random() * 2000) + 300,
    averageDuration: Math.floor(Math.random() * 365) + 30,
    characteristics: [
      ['High intent', 'Research-focused', 'Price-sensitive'],
      ['First purchase completed', 'Learning product features', 'Cautious engagement'],
      ['Repeat purchases', 'Exploring categories', 'Building trust'],
      ['Regular engagement', 'Predictable patterns', 'Brand preference'],
      ['High lifetime value', 'Low churn risk', 'Brand advocacy'],
      ['Referrals active', 'Premium purchases', 'Community engagement'],
      ['Declining activity', 'Support issues', 'Competitive interest'],
      ['No recent activity', 'Unresponsive', 'Account dormant']
    ][i],
    typicalBehaviors: [
      ['Product research', 'Price comparison', 'Review reading'],
      ['Tutorial engagement', 'Support usage', 'Feature exploration'],
      ['Category expansion', 'Loyalty program signup', 'Review writing'],
      ['Consistent purchasing', 'Subscription adoption', 'Referral giving'],
      ['Premium feature usage', 'Community participation', 'Beta testing'],
      ['Content creation', 'Influencer activity', 'Partnership interest'],
      ['Reduced engagement', 'Support contacts', 'Complaint filing'],
      ['Account inactivity', 'Email unsubscribes', 'No responses']
    ][i],
    keyMetrics: [
      { metric: 'Conversion Rate', value: Math.random() * 100, benchmark: Math.random() * 100 },
      { metric: 'Engagement Score', value: Math.random() * 100, benchmark: Math.random() * 100 },
      { metric: 'Satisfaction', value: Math.random() * 100, benchmark: Math.random() * 100 }
    ],
    transitions: stages.slice(i + 1, i + 3).map(toStage => ({
      to: toStage,
      probability: Math.random() * 100,
      avgTime: Math.floor(Math.random() * 180) + 30,
      triggers: ['Positive experience', 'Value realization', 'Trust building']
    })),
    strategies: {
      objective: [
        'Convert to first purchase',
        'Ensure successful onboarding',
        'Encourage repeat purchases',
        'Maintain consistent engagement',
        'Maximize lifetime value',
        'Leverage advocacy potential',
        'Prevent churn through intervention',
        'Attempt win-back or learn from departure'
      ][i],
      tactics: [
        ['Targeted offers', 'Educational content', 'Social proof'],
        ['Onboarding sequence', 'Success milestones', 'Support access'],
        ['Cross-sell campaigns', 'Loyalty program', 'Personalization'],
        ['Consistent value delivery', 'Program expansion', 'Feedback collection'],
        ['Premium offerings', 'Exclusive access', 'Personal service'],
        ['Referral programs', 'Ambassador opportunities', 'Co-creation'],
        ['Win-back campaigns', 'Issue resolution', 'Value demonstration'],
        ['Exit surveys', 'Competitive analysis', 'Process improvement']
      ][i],
      expectedOutcome: [
        'First purchase conversion',
        'Successful product adoption',
        'Category expansion',
        'Long-term retention',
        'Premium tier advancement',
        'Active brand advocacy',
        'Churn prevention',
        'Learning and improvement'
      ][i]
    },
    riskFactors: [
      ['High competition', 'Price sensitivity', 'Information overload'],
      ['Poor onboarding', 'Unmet expectations', 'Support gaps'],
      ['Value realization delays', 'Alternative discovery', 'Price increases'],
      ['Routine disruption', 'Life changes', 'Competitive offers'],
      ['Value plateau', 'Service issues', 'Price sensitivity'],
      ['Burnout risk', 'Over-engagement', 'Platform changes'],
      ['Multiple warning signs', 'Competitor adoption', 'Unresolved issues'],
      ['Complete disengagement', 'Negative sentiment', 'Acquisition challenges']
    ][i],
    opportunities: [
      ['Personalized onboarding', 'Value demonstration', 'Trust building'],
      ['Success path guidance', 'Quick wins', 'Community introduction'],
      ['Cross-category recommendations', 'Loyalty benefits', 'Exclusive access'],
      ['Subscription models', 'Premium features', 'Advocacy programs'],
      ['VIP treatment', 'Co-creation opportunities', 'Partnership potential'],
      ['Platform expansion', 'Revenue sharing', 'Strategic partnerships'],
      ['Immediate intervention', 'Value re-demonstration', 'Personal attention'],
      ['Win-back campaigns', 'Alternative offerings', 'Feedback utilization']
    ][i]
  }));
};

const AICustomerIntelligence: React.FC = () => {
  const [selectedTimeframe, setSelectedTimeframe] = useState('30d');
  const [selectedSegment, setSelectedSegment] = useState('all');
  const [selectedRiskLevel, setSelectedRiskLevel] = useState('all');
  const [viewMode, setViewMode] = useState('overview');
  const [searchTerm, setSearchTerm] = useState('');

  // Generated data
  const customerProfiles = useMemo(() => generateCustomerProfiles(), []);
  const churnPredictions = useMemo(() => generateChurnPredictions(), []);
  const customerSegments = useMemo(() => generateCustomerSegments(), []);
  const behaviorPatterns = useMemo(() => generateBehaviorPatterns(), []);
  const lifecycleStages = useMemo(() => generateLifecycleStages(), []);

  // Filtering and processing
  const filteredProfiles = useMemo(() => {
    return customerProfiles.filter(profile => 
      (selectedSegment === 'all' || profile.behavioralSegment === selectedSegment) &&
      (selectedRiskLevel === 'all' || profile.riskFactors.churnRisk.level === selectedRiskLevel) &&
      (profile.customerId.toLowerCase().includes(searchTerm.toLowerCase()) ||
       profile.demographics.profession.toLowerCase().includes(searchTerm.toLowerCase()))
    );
  }, [customerProfiles, selectedSegment, selectedRiskLevel, searchTerm]);

  const filteredChurnPredictions = useMemo(() => {
    return churnPredictions.filter(prediction => 
      (selectedRiskLevel === 'all' || prediction.churnRisk === selectedRiskLevel)
    );
  }, [churnPredictions, selectedRiskLevel]);

  const aggregatedMetrics = useMemo(() => {
    const totalCustomers = customerProfiles.length * 100; // Simulate larger dataset
    const avgLifetimeValue = customerProfiles.reduce((sum, p) => sum + p.predictiveMetrics.lifetimeValue, 0) / customerProfiles.length;
    const avgChurnRisk = churnPredictions.reduce((sum, p) => sum + p.churnProbability, 0) / churnPredictions.length;
    const highValueCustomers = customerProfiles.filter(p => p.behavioralSegment === 'high_value').length;

    return {
      totalCustomers,
      avgLifetimeValue,
      avgChurnRisk,
      highValueCustomers: (highValueCustomers / customerProfiles.length) * 100
    };
  }, [customerProfiles, churnPredictions]);

  const segmentPerformanceData = useMemo(() => {
    return customerSegments.map(segment => ({
      name: segment.name.split(' ').slice(0, 2).join(' '),
      size: segment.size,
      value: segment.characteristics.averageLifetimeValue,
      growth: segment.growth,
      churn: segment.characteristics.churnRate
    }));
  }, [customerSegments]);

  const churnRiskDistribution = useMemo(() => {
    const distribution = churnPredictions.reduce((acc, prediction) => {
      acc[prediction.churnRisk] = (acc[prediction.churnRisk] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    return Object.entries(distribution).map(([risk, count]) => ({
      risk: risk.charAt(0).toUpperCase() + risk.slice(1),
      count,
      percentage: (count / churnPredictions.length) * 100
    }));
  }, [churnPredictions]);

  const getSegmentIcon = (segment: string) => {
    switch (segment) {
      case 'high_value': return <Crown className="h-4 w-4" />;
      case 'frequent_buyer': return <ShoppingCart className="h-4 w-4" />;
      case 'discount_seeker': return <DollarSign className="h-4 w-4" />;
      case 'brand_loyal': return <Heart className="h-4 w-4" />;
      case 'price_sensitive': return <Target className="h-4 w-4" />;
      case 'occasional': return <Clock className="h-4 w-4" />;
      case 'new_customer': return <UserPlus className="h-4 w-4" />;
      case 'at_risk': return <AlertTriangle className="h-4 w-4" />;
      default: return <Users className="h-4 w-4" />;
    }
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getLifecycleIcon = (stage: string) => {
    switch (stage) {
      case 'prospect': return <Eye className="h-4 w-4" />;
      case 'new_customer': return <UserPlus className="h-4 w-4" />;
      case 'developing': return <TrendingUp className="h-4 w-4" />;
      case 'established': return <UserCheck className="h-4 w-4" />;
      case 'loyal': return <Heart className="h-4 w-4" />;
      case 'champion': return <Crown className="h-4 w-4" />;
      case 'at_risk': return <AlertTriangle className="h-4 w-4" />;
      case 'churned': return <UserX className="h-4 w-4" />;
      default: return <Users className="h-4 w-4" />;
    }
  };

  return (
    <div className="w-full space-y-6 p-6 bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50 min-h-screen">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-2">
            <Brain className="h-8 w-8 text-purple-600" />
            AI Customer Intelligence
          </h1>
          <p className="text-gray-600 mt-1">Advanced customer behavior analysis, churn prediction, and personalization insights</p>
        </div>
        
        <div className="flex flex-wrap items-center gap-3">
          <div className="flex items-center gap-2">
            <Search className="h-4 w-4 text-gray-500" />
            <Input
              placeholder="Search customers..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-64"
            />
          </div>
          
          <select
            value={selectedSegment}
            onChange={(e) => setSelectedSegment(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-purple-500"
          >
            <option value="all">All Segments</option>
            <option value="high_value">High Value</option>
            <option value="frequent_buyer">Frequent Buyer</option>
            <option value="discount_seeker">Discount Seeker</option>
            <option value="brand_loyal">Brand Loyal</option>
            <option value="price_sensitive">Price Sensitive</option>
            <option value="occasional">Occasional</option>
            <option value="new_customer">New Customer</option>
            <option value="at_risk">At Risk</option>
          </select>
          
          <select
            value={selectedRiskLevel}
            onChange={(e) => setSelectedRiskLevel(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-purple-500"
          >
            <option value="all">All Risk Levels</option>
            <option value="low">Low Risk</option>
            <option value="medium">Medium Risk</option>
            <option value="high">High Risk</option>
            <option value="critical">Critical Risk</option>
          </select>
          
          <select
            value={selectedTimeframe}
            onChange={(e) => setSelectedTimeframe(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-purple-500"
          >
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
            <option value="90d">Last 90 Days</option>
            <option value="365d">Last Year</option>
          </select>
          
          <Button variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="bg-gradient-to-r from-purple-500 to-purple-600 text-white">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-100 text-sm">Total Customers</p>
                <p className="text-2xl font-bold">{(aggregatedMetrics.totalCustomers / 1000).toFixed(1)}K</p>
                <p className="text-purple-100 text-xs flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +8.2% vs last month
                </p>
              </div>
              <Users className="h-8 w-8 text-purple-200" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-100 text-sm">Avg Lifetime Value</p>
                <p className="text-2xl font-bold">${(aggregatedMetrics.avgLifetimeValue / 1000).toFixed(1)}K</p>
                <p className="text-blue-100 text-xs flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +5.7% vs last month
                </p>
              </div>
              <DollarSign className="h-8 w-8 text-blue-200" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-orange-500 to-orange-600 text-white">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-orange-100 text-sm">Avg Churn Risk</p>
                <p className="text-2xl font-bold">{aggregatedMetrics.avgChurnRisk.toFixed(1)}%</p>
                <p className="text-orange-100 text-xs flex items-center mt-1">
                  <TrendingDown className="h-3 w-3 mr-1" />
                  -2.1% vs last month
                </p>
              </div>
              <AlertTriangle className="h-8 w-8 text-orange-200" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-green-500 to-green-600 text-white">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-100 text-sm">High Value %</p>
                <p className="text-2xl font-bold">{aggregatedMetrics.highValueCustomers.toFixed(1)}%</p>
                <p className="text-green-100 text-xs flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +1.4% vs last month
                </p>
              </div>
              <Crown className="h-8 w-8 text-green-200" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={viewMode} onValueChange={setViewMode} className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="profiles">Profiles</TabsTrigger>
          <TabsTrigger value="churn">Churn Risk</TabsTrigger>
          <TabsTrigger value="segments">Segments</TabsTrigger>
          <TabsTrigger value="lifecycle">Lifecycle</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Segment Performance */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Customer Segment Performance
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <ComposedChart data={segmentPerformanceData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" angle={-45} textAnchor="end" height={80} />
                    <YAxis yAxisId="left" />
                    <YAxis yAxisId="right" orientation="right" />
                    <Tooltip />
                    <Bar yAxisId="left" dataKey="size" fill="#8B5CF6" name="Size" />
                    <Line yAxisId="right" type="monotone" dataKey="value" stroke="#10B981" strokeWidth={2} name="CLV" />
                  </ComposedChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Churn Risk Distribution */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5" />
                  Churn Risk Distribution
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <RechartsPieChart>
                    <Tooltip />
                    <Cell />
                  </RechartsPieChart>
                </ResponsiveContainer>
                <div className="grid grid-cols-2 gap-2 mt-4">
                  {churnRiskDistribution.map((item, i) => (
                    <div key={i} className="flex items-center gap-2 text-sm">
                      <div className={`w-3 h-3 rounded-full ${
                        item.risk === 'Critical' ? 'bg-red-500' :
                        item.risk === 'High' ? 'bg-orange-500' :
                        item.risk === 'Medium' ? 'bg-yellow-500' : 'bg-green-500'
                      }`}></div>
                      <span className="text-gray-600">{item.risk}</span>
                      <span className="font-medium">{item.percentage.toFixed(1)}%</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Behavior Patterns */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Key Behavior Patterns
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {behaviorPatterns.slice(0, 4).map((pattern) => (
                  <div key={pattern.id} className="bg-gray-50 p-4 rounded-lg">
                    <h4 className="font-medium text-gray-900 mb-2">{pattern.name}</h4>
                    <p className="text-sm text-gray-600 mb-3">{pattern.description}</p>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Frequency:</span>
                        <span className="font-medium">{pattern.frequency.toFixed(0)}%</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Strength:</span>
                        <span className="font-medium">{pattern.strength.toFixed(0)}%</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Affected:</span>
                        <span className="font-medium">{pattern.affected_customers.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Revenue Impact:</span>
                        <span className="font-medium text-green-600">${(pattern.businessImpact.revenueInfluence / 1000).toFixed(0)}K</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="profiles" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {filteredProfiles.slice(0, 9).map((profile) => (
              <Card key={profile.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <CardTitle className="flex items-center gap-2">
                      {getSegmentIcon(profile.behavioralSegment)}
                      Customer {profile.customerId.split('-')[1]}
                    </CardTitle>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="capitalize">
                        {profile.behavioralSegment.replace('_', ' ')}
                      </Badge>
                      <Badge className={getRiskColor(profile.riskFactors.churnRisk.level)}>
                        {profile.riskFactors.churnRisk.level}
                      </Badge>
                    </div>
                  </div>
                  <p className="text-sm text-gray-600">
                    {profile.demographics.profession} • {profile.demographics.ageGroup} • {profile.demographics.location.city}
                  </p>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Key Metrics */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Key Metrics</h4>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div className="bg-blue-50 p-2 rounded text-center">
                        <p className="font-bold text-blue-600">${(profile.predictiveMetrics.lifetimeValue / 1000).toFixed(1)}K</p>
                        <p className="text-xs text-gray-600">Lifetime Value</p>
                      </div>
                      <div className="bg-green-50 p-2 rounded text-center">
                        <p className="font-bold text-green-600">{profile.predictiveMetrics.churnProbability.toFixed(0)}%</p>
                        <p className="text-xs text-gray-600">Churn Risk</p>
                      </div>
                      <div className="bg-purple-50 p-2 rounded text-center">
                        <p className="font-bold text-purple-600">{profile.purchaseHistory.totalOrders}</p>
                        <p className="text-xs text-gray-600">Total Orders</p>
                      </div>
                      <div className="bg-orange-50 p-2 rounded text-center">
                        <p className="font-bold text-orange-600">${profile.purchaseHistory.averageOrderValue.toFixed(0)}</p>
                        <p className="text-xs text-gray-600">Avg Order</p>
                      </div>
                    </div>
                  </div>

                  {/* Behavioral Insights */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Engagement Profile</h4>
                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Sessions:</span>
                        <span className="font-medium">{profile.engagementProfile.totalSessions}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Avg Duration:</span>
                        <span className="font-medium">{Math.round(profile.engagementProfile.averageSessionDuration / 60)}m</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Bounce Rate:</span>
                        <span className="font-medium">{profile.engagementProfile.bounceRate.toFixed(1)}%</span>
                      </div>
                    </div>
                  </div>

                  {/* Preferred Categories */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Preferred Categories</h4>
                    <div className="flex flex-wrap gap-1">
                      {profile.purchaseHistory.preferredCategories.slice(0, 3).map((category, i) => (
                        <Badge key={i} variant="secondary" className="text-xs">
                          {category.category} (${category.spend.toFixed(0)})
                        </Badge>
                      ))}
                    </div>
                  </div>

                  {/* AI Insights */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">AI Insights</h4>
                    <div className="space-y-1">
                      <p className="text-xs text-gray-600">Primary Motivation:</p>
                      <p className="text-sm">{profile.aiInsights.primaryMotivations[0]}</p>
                      <p className="text-xs text-gray-600 mt-2">Recommended Strategy:</p>
                      <p className="text-sm">{profile.aiInsights.retentionStrategies[0]}</p>
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <Button variant="outline" size="sm" className="flex-1">
                      <Eye className="h-4 w-4 mr-2" />
                      Details
                    </Button>
                    <Button size="sm" className="flex-1">
                      <Target className="h-4 w-4 mr-2" />
                      Action
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="churn" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {filteredChurnPredictions.slice(0, 6).map((prediction) => (
              <motion.div
                key={prediction.customerId}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="group"
              >
                <Card className="h-full hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <CardTitle className="flex items-center gap-2">
                        <AlertTriangle className="h-5 w-5" />
                        Customer {prediction.customerId.split('-')[1]}
                      </CardTitle>
                      <div className="flex items-center gap-2">
                        <Badge className={getRiskColor(prediction.churnRisk)}>
                          {prediction.churnRisk}
                        </Badge>
                        <span className="text-sm text-gray-500">{prediction.timeToChurn}d</span>
                      </div>
                    </div>
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-gray-600">Churn Probability:</span>
                        <span className="font-bold text-orange-600">{prediction.churnProbability.toFixed(1)}%</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-gray-600">Confidence:</span>
                        <span className="font-medium">{prediction.confidenceScore.toFixed(1)}%</span>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Key Risk Factors */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Key Risk Factors</h4>
                      <div className="space-y-2">
                        {prediction.keyFactors.slice(0, 3).map((factor, i) => (
                          <div key={i} className="flex items-center justify-between text-sm">
                            <span className="text-gray-600">{factor.factor}</span>
                            <div className="flex items-center gap-2">
                              <div className="w-16 bg-gray-200 rounded-full h-2">
                                <div 
                                  className="bg-red-600 h-2 rounded-full" 
                                  style={{ width: `${factor.impact}%` }}
                                ></div>
                              </div>
                              <span className="font-medium">{factor.impact.toFixed(0)}%</span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Business Impact */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Business Impact</h4>
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <div className="bg-red-50 p-2 rounded text-center">
                          <p className="font-bold text-red-600">${(prediction.businessImpact.revenueAtRisk / 1000).toFixed(1)}K</p>
                          <p className="text-xs text-gray-600">Revenue at Risk</p>
                        </div>
                        <div className="bg-orange-50 p-2 rounded text-center">
                          <p className="font-bold text-orange-600">${(prediction.businessImpact.lifetimeValueLoss / 1000).toFixed(1)}K</p>
                          <p className="text-xs text-gray-600">CLV Loss</p>
                        </div>
                        <div className="bg-yellow-50 p-2 rounded text-center">
                          <p className="font-bold text-yellow-600">${prediction.businessImpact.replacementCost}</p>
                          <p className="text-xs text-gray-600">Replacement</p>
                        </div>
                        <div className="bg-green-50 p-2 rounded text-center">
                          <p className="font-bold text-green-600">${(prediction.businessImpact.retentionValue / 1000).toFixed(1)}K</p>
                          <p className="text-xs text-gray-600">Retention Value</p>
                        </div>
                      </div>
                    </div>

                    {/* Intervention Recommendations */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Recommended Actions</h4>
                      <div className="space-y-2">
                        {prediction.interventionRecommendations.slice(0, 2).map((rec, i) => (
                          <div key={i} className="p-3 bg-gray-50 rounded-lg">
                            <div className="flex items-start justify-between">
                              <p className="text-sm font-medium text-gray-900">{rec.strategy}</p>
                              <Badge 
                                variant="outline" 
                                className={
                                  rec.priority === 'urgent' ? 'border-red-200 text-red-800' :
                                  rec.priority === 'high' ? 'border-orange-200 text-orange-800' :
                                  rec.priority === 'medium' ? 'border-yellow-200 text-yellow-800' :
                                  'border-green-200 text-green-800'
                                }
                              >
                                {rec.priority}
                              </Badge>
                            </div>
                            <div className="flex items-center justify-between mt-2 text-xs">
                              <span className="text-gray-500">Impact: {rec.expectedImpact.toFixed(0)}%</span>
                              <span className="text-gray-500">{rec.timing}</span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="flex gap-2">
                      <Button variant="outline" size="sm" className="flex-1">
                        <Eye className="h-4 w-4 mr-2" />
                        Details
                      </Button>
                      <Button size="sm" className="flex-1">
                        <Zap className="h-4 w-4 mr-2" />
                        Intervene
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="segments" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {customerSegments.map((segment) => (
              <Card key={segment.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="h-5 w-5" />
                    {segment.name}
                  </CardTitle>
                  <p className="text-sm text-gray-600 leading-relaxed">{segment.description}</p>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Segment Size & Growth */}
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm text-gray-600">Segment Size</span>
                      <span className="font-medium">{segment.size.toLocaleString()} ({segment.percentage.toFixed(1)}%)</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Growth Rate</span>
                      <span className={`font-medium ${segment.growth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {segment.growth >= 0 ? '+' : ''}{segment.growth.toFixed(1)}%
                      </span>
                    </div>
                  </div>

                  {/* Key Characteristics */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Key Metrics</h4>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div className="bg-blue-50 p-2 rounded text-center">
                        <p className="font-bold text-blue-600">${(segment.characteristics.averageLifetimeValue / 1000).toFixed(1)}K</p>
                        <p className="text-xs text-gray-600">Avg CLV</p>
                      </div>
                      <div className="bg-green-50 p-2 rounded text-center">
                        <p className="font-bold text-green-600">${segment.characteristics.averageOrderValue.toFixed(0)}</p>
                        <p className="text-xs text-gray-600">Avg Order</p>
                      </div>
                      <div className="bg-purple-50 p-2 rounded text-center">
                        <p className="font-bold text-purple-600">{segment.characteristics.churnRate.toFixed(1)}%</p>
                        <p className="text-xs text-gray-600">Churn Rate</p>
                      </div>
                      <div className="bg-orange-50 p-2 rounded text-center">
                        <p className="font-bold text-orange-600">{segment.characteristics.engagementScore.toFixed(0)}</p>
                        <p className="text-xs text-gray-600">Engagement</p>
                      </div>
                    </div>
                  </div>

                  {/* Channel Preferences */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Channel Preferences</h4>
                    <div className="space-y-1">
                      {segment.preferences.channels.slice(0, 3).map((channel, i) => (
                        <div key={i} className="flex items-center justify-between text-sm">
                          <span className="text-gray-600">{channel.channel}</span>
                          <div className="flex items-center gap-2">
                            <div className="w-12 bg-gray-200 rounded-full h-1">
                              <div 
                                className="bg-blue-600 h-1 rounded-full" 
                                style={{ width: `${channel.preference}%` }}
                              ></div>
                            </div>
                            <span className="font-medium">{channel.preference.toFixed(0)}%</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Business Value */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Business Value</h4>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div>
                        <span className="text-gray-600">Revenue:</span>
                        <span className="ml-1 font-medium">{segment.businessValue.revenueContribution.toFixed(1)}%</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Profit Margin:</span>
                        <span className="ml-1 font-medium">{segment.businessValue.profitMargin.toFixed(1)}%</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Acquisition:</span>
                        <span className="ml-1 font-medium">${segment.businessValue.acquisitionCost}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Retention:</span>
                        <span className="ml-1 font-medium">${segment.businessValue.retentionCost}</span>
                      </div>
                    </div>
                  </div>

                  {/* Key Strategies */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Key Strategies</h4>
                    <div className="space-y-2">
                      <div>
                        <p className="text-xs font-medium text-blue-700 mb-1">Acquisition:</p>
                        <div className="flex flex-wrap gap-1">
                          {segment.strategies.acquisition.slice(0, 2).map((strategy, i) => (
                            <Badge key={i} variant="secondary" className="text-xs">{strategy}</Badge>
                          ))}
                        </div>
                      </div>
                      <div>
                        <p className="text-xs font-medium text-green-700 mb-1">Retention:</p>
                        <div className="flex flex-wrap gap-1">
                          {segment.strategies.retention.slice(0, 2).map((strategy, i) => (
                            <Badge key={i} variant="outline" className="text-xs">{strategy}</Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>

                  <Button variant="outline" className="w-full">
                    <Eye className="h-4 w-4 mr-2" />
                    View Segment Analysis
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="lifecycle" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-4 gap-6">
            {lifecycleStages.map((stage) => (
              <Card key={stage.stage} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    {getLifecycleIcon(stage.stage)}
                    {stage.stage.charAt(0).toUpperCase() + stage.stage.slice(1).replace('_', ' ')}
                  </CardTitle>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Count:</span>
                    <span className="font-medium">{stage.customerCount.toLocaleString()}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Percentage:</span>
                    <span className="font-medium">{stage.percentage.toFixed(1)}%</span>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Stage Metrics */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Key Metrics</h4>
                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Avg Value:</span>
                        <span className="font-medium">${stage.averageValue.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Avg Duration:</span>
                        <span className="font-medium">{stage.averageDuration}d</span>
                      </div>
                    </div>
                  </div>

                  {/* Key Characteristics */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Characteristics</h4>
                    <div className="flex flex-wrap gap-1">
                      {stage.characteristics.slice(0, 3).map((char, i) => (
                        <Badge key={i} variant="secondary" className="text-xs">{char}</Badge>
                      ))}
                    </div>
                  </div>

                  {/* Typical Behaviors */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Behaviors</h4>
                    <ul className="text-xs text-gray-600 space-y-1">
                      {stage.typicalBehaviors.slice(0, 3).map((behavior, i) => (
                        <li key={i} className="flex items-start gap-2">
                          <span className="text-gray-400 mt-1">•</span>
                          <span>{behavior}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Transitions */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Next Stage</h4>
                    {stage.transitions.slice(0, 2).map((transition, i) => (
                      <div key={i} className="flex items-center justify-between text-xs">
                        <span className="text-gray-600 capitalize">{transition.to.replace('_', ' ')}:</span>
                        <span className="font-medium">{transition.probability.toFixed(0)}%</span>
                      </div>
                    ))}
                  </div>

                  {/* Strategy */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Strategy</h4>
                    <p className="text-xs text-gray-600">{stage.strategies.objective}</p>
                    <div className="flex flex-wrap gap-1 mt-2">
                      {stage.strategies.tactics.slice(0, 2).map((tactic, i) => (
                        <Badge key={i} variant="outline" className="text-xs">{tactic}</Badge>
                      ))}
                    </div>
                  </div>

                  <Button variant="outline" size="sm" className="w-full">
                    <Eye className="h-4 w-4 mr-2" />
                    View Details
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AICustomerIntelligence;