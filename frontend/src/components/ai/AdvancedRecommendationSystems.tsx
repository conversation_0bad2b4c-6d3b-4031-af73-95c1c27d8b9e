/**
 * Advanced Recommendation Systems Dashboard
 * AI-powered recommendation engines with collaborative filtering, content-based, and hybrid approaches
 */

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Sparkles,
  Brain,
  Target,
  Users,
  ShoppingCart,
  TrendingUp,
  TrendingDown,
  Star,
  Heart,
  Eye,
  Zap,
  Activity,
  BarChart3,
  LineChart,
  PieChart,
  Clock,
  Calendar,
  Globe,
  Filter,
  Search,
  Download,
  Upload,
  Share,
  Settings,
  RefreshCw,
  Lightbulb,
  AlertTriangle,
  CheckCircle,
  Info,
  ThumbsUp,
  ThumbsDown,
  Award,
  Crown,
  Gem,
  Wand2,
  Focus,
  Crosshair,
  Layers,
  Network,
  Database,
  Cpu,
  Monitor,
  Smartphone,
  Tablet,
  Mail,
  Phone,
  Bell,
  BellRing,
  Flag,
  MapPin,
  Navigation,
  Compass,
  Route,
  ArrowUp,
  ArrowDown,
  ArrowRight,
  ArrowLeft,
  ChevronUp,
  ChevronDown,
  Plus,
  Minus,
  Edit,
  Trash,
  MoreHorizontal,
  ExternalLink,
  Link2,
  Save,
  Archive,
  Folder,
  File,
  FileText,
  BookOpen,
  Package,
  DollarSign,
  CreditCard,
  Wallet,
  Store,
  Building2,
  Home,
  Car,
  Plane,
  Coffee,
  Music,
  Volume2,
  Mic,
  Speaker,
  Headphones,
  Watch,
  Laptop,
  Server,
  Cloud,
  Wifi,
  Signal,
  Battery,
  Power,
  Lock,
  Unlock,
  Key,
  Shield,
  Hand,
  Fingerprint,
  Image,
  Video,
  Camera,
  Scan,
  Palette,
  Brush,
  Type,
  AlignLeft,
  Hash,
  Tag,
  Quote,
  Languages,
  MessageCircle
} from 'lucide-react';
import {
  LineChart as RechartsLineChart,
  AreaChart,
  BarChart,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Area,
  Bar,
  Line,
  PieChart as RechartsPieChart,
  Cell,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
  ComposedChart,
  ScatterChart,
  Scatter,
  TreeMap,
  Sankey
} from 'recharts';

// Recommendation System Interfaces
interface RecommendationEngine {
  id: string;
  name: string;
  type: 'collaborative_filtering' | 'content_based' | 'hybrid' | 'deep_learning' | 'matrix_factorization' | 'association_rules' | 'clustering' | 'neural_collaborative';
  status: 'active' | 'training' | 'inactive' | 'testing';
  accuracy: number;
  precision: number;
  recall: number;
  f1Score: number;
  coverage: number;
  diversity: number;
  novelty: number;
  serendipity: number;
  responseTime: number;
  throughput: number;
  lastTrained: string;
  dataPoints: number;
  activeUsers: number;
  totalRecommendations: number;
  clickThroughRate: number;
  conversionRate: number;
  revenue: number;
}

interface ProductRecommendation {
  id: string;
  productId: string;
  productName: string;
  category: string;
  price: number;
  rating: number;
  imageUrl: string;
  confidence: number;
  reason: string;
  algorithm: string;
  userPersonalization: number;
  trendingScore: number;
  seasonalityScore: number;
  inventoryScore: number;
  profitabilityScore: number;
  clickProbability: number;
  purchaseProbability: number;
  recommendedAt: string;
  context: {
    location: string;
    device: string;
    timeOfDay: string;
    dayOfWeek: string;
    weather?: string;
    occasion?: string;
  };
}

interface UserSegment {
  id: string;
  name: string;
  description: string;
  userCount: number;
  characteristics: {
    ageRange: string;
    gender: string;
    spendingHabits: string;
    preferredCategories: string[];
    shoppingFrequency: string;
    devicePreference: string;
    pricesensitivity: 'low' | 'medium' | 'high';
  };
  behaviorPatterns: {
    averageSessionTime: number;
    averageOrderValue: number;
    conversionRate: number;
    returnRate: number;
    brandLoyalty: number;
    seasonalityIndex: number;
  };
  recommendationPreferences: {
    algorithm: string;
    diversityPreference: number;
    noveltyPreference: number;
    popularityBias: number;
    contextualWeight: number;
  };
  performance: {
    clickThroughRate: number;
    conversionRate: number;
    revenue: number;
    satisfaction: number;
  };
}

interface RecommendationPerformance {
  id: string;
  engineId: string;
  date: string;
  totalRecommendations: number;
  uniqueUsers: number;
  impressions: number;
  clicks: number;
  purchases: number;
  revenue: number;
  clickThroughRate: number;
  conversionRate: number;
  averageOrderValue: number;
  returnOnInvestment: number;
  userSatisfaction: number;
  diversityScore: number;
  noveltyScore: number;
  coverage: number;
  catalogCoverage: number;
  longTailCoverage: number;
  responseTime: number;
  errorRate: number;
}

interface RecommendationInsight {
  id: string;
  type: 'performance' | 'user_behavior' | 'product_affinity' | 'seasonal_trend' | 'algorithm_drift' | 'data_quality' | 'business_impact';
  title: string;
  description: string;
  confidence: number;
  impact: 'high' | 'medium' | 'low';
  category: string;
  metrics: {
    baseline: number;
    current: number;
    change: number;
    significance: number;
  };
  recommendations: string[];
  affectedSegments: string[];
  timeframe: string;
  actionable: boolean;
  priority: number;
  timestamp: string;
}

interface ABTestExperiment {
  id: string;
  name: string;
  description: string;
  status: 'planning' | 'running' | 'completed' | 'paused' | 'cancelled';
  startDate: string;
  endDate: string;
  hypothesis: string;
  variants: {
    id: string;
    name: string;
    description: string;
    trafficAllocation: number;
    algorithm: string;
    parameters: any;
    performance: {
      impressions: number;
      clicks: number;
      conversions: number;
      revenue: number;
      clickThroughRate: number;
      conversionRate: number;
      statisticalSignificance: number;
    };
  }[];
  primaryMetric: string;
  secondaryMetrics: string[];
  confidence: number;
  winner?: string;
  results: {
    summary: string;
    insights: string[];
    nextSteps: string[];
  };
}

interface RealTimeRecommendation {
  id: string;
  userId: string;
  sessionId: string;
  recommendations: ProductRecommendation[];
  context: {
    currentPage: string;
    cartItems: string[];
    browsingHistory: string[];
    searchQuery?: string;
    referrer?: string;
    location: string;
    device: string;
    timestamp: string;
  };
  personalization: {
    userSegment: string;
    preferences: string[];
    affinities: { [category: string]: number };
    behaviors: string[];
  };
  performance: {
    generationTime: number;
    cacheHit: boolean;
    freshness: number;
  };
}

// Mock Data Generation
const generateRecommendationEngines = (): RecommendationEngine[] => {
  const engines = [
    { name: 'Collaborative Filtering Pro', type: 'collaborative_filtering' as const, baseAccuracy: 0.85 },
    { name: 'Content-Based Intelligence', type: 'content_based' as const, baseAccuracy: 0.82 },
    { name: 'Hybrid Recommendation AI', type: 'hybrid' as const, baseAccuracy: 0.91 },
    { name: 'Deep Learning Recommender', type: 'deep_learning' as const, baseAccuracy: 0.88 },
    { name: 'Matrix Factorization Engine', type: 'matrix_factorization' as const, baseAccuracy: 0.84 },
    { name: 'Association Rules Miner', type: 'association_rules' as const, baseAccuracy: 0.79 },
    { name: 'Clustering-Based System', type: 'clustering' as const, baseAccuracy: 0.81 },
    { name: 'Neural Collaborative Filter', type: 'neural_collaborative' as const, baseAccuracy: 0.89 }
  ];

  return engines.map((engine, index) => ({
    id: `engine-${index + 1}`,
    name: engine.name,
    type: engine.type,
    status: Math.random() > 0.1 ? 'active' : Math.random() > 0.5 ? 'training' : 'inactive',
    accuracy: engine.baseAccuracy + (Math.random() * 0.1 - 0.05),
    precision: engine.baseAccuracy + (Math.random() * 0.08 - 0.04),
    recall: engine.baseAccuracy + (Math.random() * 0.06 - 0.03),
    f1Score: engine.baseAccuracy + (Math.random() * 0.07 - 0.035),
    coverage: Math.random() * 0.4 + 0.6,
    diversity: Math.random() * 0.3 + 0.7,
    novelty: Math.random() * 0.5 + 0.5,
    serendipity: Math.random() * 0.4 + 0.3,
    responseTime: Math.random() * 200 + 50,
    throughput: Math.random() * 10000 + 1000,
    lastTrained: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
    dataPoints: Math.floor(Math.random() * 10000000) + 1000000,
    activeUsers: Math.floor(Math.random() * 100000) + 10000,
    totalRecommendations: Math.floor(Math.random() * 1000000) + 100000,
    clickThroughRate: Math.random() * 0.15 + 0.05,
    conversionRate: Math.random() * 0.08 + 0.02,
    revenue: Math.random() * 500000 + 100000
  }));
};

const generateProductRecommendations = (): ProductRecommendation[] => {
  const categories = ['Electronics', 'Clothing', 'Home & Garden', 'Sports', 'Beauty', 'Books', 'Toys', 'Automotive'];
  const algorithms = ['collaborative', 'content_based', 'hybrid', 'trending', 'similar_users', 'frequently_bought'];
  const reasons = [
    'Customers who bought this also bought',
    'Based on your browsing history',
    'Trending in your area',
    'Similar to items you liked',
    'Recommended for you',
    'Popular in Electronics',
    'Frequently bought together',
    'New arrivals you might like'
  ];
  
  return Array.from({ length: 50 }, (_, index) => ({
    id: `rec-${index + 1}`,
    productId: `prod-${index + 1}`,
    productName: `Product ${index + 1}`,
    category: categories[Math.floor(Math.random() * categories.length)],
    price: Math.random() * 500 + 10,
    rating: Math.random() * 2 + 3,
    imageUrl: `/api/images/product-${index + 1}.jpg`,
    confidence: Math.random() * 0.3 + 0.7,
    reason: reasons[Math.floor(Math.random() * reasons.length)],
    algorithm: algorithms[Math.floor(Math.random() * algorithms.length)],
    userPersonalization: Math.random(),
    trendingScore: Math.random(),
    seasonalityScore: Math.random(),
    inventoryScore: Math.random(),
    profitabilityScore: Math.random(),
    clickProbability: Math.random() * 0.3 + 0.1,
    purchaseProbability: Math.random() * 0.15 + 0.02,
    recommendedAt: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000).toISOString(),
    context: {
      location: ['US', 'UK', 'DE', 'FR', 'CA'][Math.floor(Math.random() * 5)],
      device: ['desktop', 'mobile', 'tablet'][Math.floor(Math.random() * 3)],
      timeOfDay: ['morning', 'afternoon', 'evening', 'night'][Math.floor(Math.random() * 4)],
      dayOfWeek: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'][Math.floor(Math.random() * 7)],
      weather: ['sunny', 'rainy', 'cloudy', 'snowy'][Math.floor(Math.random() * 4)],
      occasion: Math.random() > 0.5 ? ['birthday', 'holiday', 'weekend', 'workday'][Math.floor(Math.random() * 4)] : undefined
    }
  }));
};

const generateUserSegments = (): UserSegment[] => {
  const segments = [
    { name: 'Frequent Shoppers', desc: 'High-value customers with regular purchasing patterns' },
    { name: 'Price-Conscious Buyers', desc: 'Budget-focused shoppers seeking deals and discounts' },
    { name: 'Premium Customers', desc: 'Luxury-oriented buyers preferring high-end products' },
    { name: 'Tech Enthusiasts', desc: 'Early adopters interested in latest technology' },
    { name: 'Casual Browsers', desc: 'Occasional shoppers with diverse interests' },
    { name: 'Mobile-First Users', desc: 'Primarily shop via mobile devices' },
    { name: 'Seasonal Shoppers', desc: 'Active during holidays and special occasions' },
    { name: 'Brand Loyalists', desc: 'Prefer specific brands and repeat purchases' }
  ];
  
  return segments.map((segment, index) => ({
    id: `segment-${index + 1}`,
    name: segment.name,
    description: segment.desc,
    userCount: Math.floor(Math.random() * 50000) + 5000,
    characteristics: {
      ageRange: ['18-25', '26-35', '36-45', '46-55', '56+'][Math.floor(Math.random() * 5)],
      gender: ['Male', 'Female', 'Mixed'][Math.floor(Math.random() * 3)],
      spendingHabits: ['High', 'Medium', 'Low'][Math.floor(Math.random() * 3)],
      preferredCategories: ['Electronics', 'Clothing', 'Home'].filter(() => Math.random() > 0.5),
      shoppingFrequency: ['Daily', 'Weekly', 'Monthly', 'Occasional'][Math.floor(Math.random() * 4)],
      devicePreference: ['Mobile', 'Desktop', 'Mixed'][Math.floor(Math.random() * 3)],
      pricesensitivity: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)] as any
    },
    behaviorPatterns: {
      averageSessionTime: Math.random() * 30 + 5,
      averageOrderValue: Math.random() * 200 + 50,
      conversionRate: Math.random() * 0.1 + 0.02,
      returnRate: Math.random() * 0.3 + 0.05,
      brandLoyalty: Math.random() * 0.5 + 0.3,
      seasonalityIndex: Math.random() * 2 + 0.5
    },
    recommendationPreferences: {
      algorithm: ['collaborative', 'content_based', 'hybrid'][Math.floor(Math.random() * 3)],
      diversityPreference: Math.random(),
      noveltyPreference: Math.random(),
      popularityBias: Math.random(),
      contextualWeight: Math.random()
    },
    performance: {
      clickThroughRate: Math.random() * 0.2 + 0.05,
      conversionRate: Math.random() * 0.1 + 0.02,
      revenue: Math.random() * 100000 + 10000,
      satisfaction: Math.random() * 2 + 3
    }
  }));
};

const generatePerformanceData = (): RecommendationPerformance[] => {
  return Array.from({ length: 30 }, (_, index) => ({
    id: `perf-${index + 1}`,
    engineId: `engine-${Math.floor(Math.random() * 8) + 1}`,
    date: new Date(Date.now() - (29 - index) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    totalRecommendations: Math.floor(Math.random() * 50000) + 10000,
    uniqueUsers: Math.floor(Math.random() * 10000) + 2000,
    impressions: Math.floor(Math.random() * 100000) + 20000,
    clicks: Math.floor(Math.random() * 5000) + 1000,
    purchases: Math.floor(Math.random() * 500) + 100,
    revenue: Math.random() * 50000 + 10000,
    clickThroughRate: Math.random() * 0.1 + 0.05,
    conversionRate: Math.random() * 0.05 + 0.02,
    averageOrderValue: Math.random() * 100 + 50,
    returnOnInvestment: Math.random() * 5 + 2,
    userSatisfaction: Math.random() * 2 + 3,
    diversityScore: Math.random() * 0.3 + 0.7,
    noveltyScore: Math.random() * 0.5 + 0.5,
    coverage: Math.random() * 0.4 + 0.6,
    catalogCoverage: Math.random() * 0.6 + 0.4,
    longTailCoverage: Math.random() * 0.4 + 0.3,
    responseTime: Math.random() * 200 + 50,
    errorRate: Math.random() * 0.05
  }));
};

const generateRecommendationInsights = (): RecommendationInsight[] => {
  const types = ['performance', 'user_behavior', 'product_affinity', 'seasonal_trend', 'algorithm_drift', 'data_quality', 'business_impact'] as const;
  const impacts = ['high', 'medium', 'low'] as const;
  
  return Array.from({ length: 20 }, (_, index) => ({
    id: `insight-${index + 1}`,
    type: types[Math.floor(Math.random() * types.length)],
    title: `Recommendation Insight ${index + 1}`,
    description: `Detailed analysis reveals important patterns in recommendation performance and user behavior for insight ${index + 1}`,
    confidence: Math.random() * 0.3 + 0.7,
    impact: impacts[Math.floor(Math.random() * impacts.length)],
    category: ['algorithm', 'user_experience', 'business_metrics', 'data_quality'][Math.floor(Math.random() * 4)],
    metrics: {
      baseline: Math.random() * 100 + 50,
      current: Math.random() * 100 + 50,
      change: (Math.random() - 0.5) * 50,
      significance: Math.random() * 0.3 + 0.7
    },
    recommendations: Array.from({ length: Math.floor(Math.random() * 3) + 1 }, (_, i) => `Recommendation ${i + 1} for insight ${index + 1}`),
    affectedSegments: ['Frequent Shoppers', 'Premium Customers', 'Tech Enthusiasts'].filter(() => Math.random() > 0.5),
    timeframe: ['1d', '7d', '30d', '90d'][Math.floor(Math.random() * 4)],
    actionable: Math.random() > 0.3,
    priority: Math.floor(Math.random() * 10) + 1,
    timestamp: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString()
  }));
};

const generateABTestExperiments = (): ABTestExperiment[] => {
  const statuses = ['planning', 'running', 'completed', 'paused'] as const;
  
  return Array.from({ length: 8 }, (_, index) => ({
    id: `experiment-${index + 1}`,
    name: `A/B Test Experiment ${index + 1}`,
    description: `Testing different recommendation algorithms and parameters for experiment ${index + 1}`,
    status: statuses[Math.floor(Math.random() * statuses.length)],
    startDate: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
    endDate: new Date(Date.now() + Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
    hypothesis: `Hypothesis for experiment ${index + 1}: Changing algorithm parameters will improve CTR`,
    variants: [
      {
        id: 'control',
        name: 'Control',
        description: 'Current production algorithm',
        trafficAllocation: 50,
        algorithm: 'current_prod',
        parameters: {},
        performance: {
          impressions: Math.floor(Math.random() * 50000) + 10000,
          clicks: Math.floor(Math.random() * 2500) + 500,
          conversions: Math.floor(Math.random() * 250) + 50,
          revenue: Math.random() * 25000 + 5000,
          clickThroughRate: Math.random() * 0.1 + 0.05,
          conversionRate: Math.random() * 0.05 + 0.02,
          statisticalSignificance: Math.random() * 0.3 + 0.7
        }
      },
      {
        id: 'variant',
        name: 'Variant A',
        description: 'Modified algorithm with new parameters',
        trafficAllocation: 50,
        algorithm: 'modified_algo',
        parameters: { diversity_weight: 0.3, novelty_weight: 0.2 },
        performance: {
          impressions: Math.floor(Math.random() * 50000) + 10000,
          clicks: Math.floor(Math.random() * 3000) + 600,
          conversions: Math.floor(Math.random() * 300) + 60,
          revenue: Math.random() * 30000 + 6000,
          clickThroughRate: Math.random() * 0.12 + 0.06,
          conversionRate: Math.random() * 0.06 + 0.025,
          statisticalSignificance: Math.random() * 0.3 + 0.7
        }
      }
    ],
    primaryMetric: 'clickThroughRate',
    secondaryMetrics: ['conversionRate', 'revenue', 'userSatisfaction'],
    confidence: Math.random() * 0.3 + 0.7,
    winner: Math.random() > 0.5 ? 'variant' : 'control',
    results: {
      summary: `Experiment ${index + 1} results summary`,
      insights: [`Insight 1 for experiment ${index + 1}`, `Insight 2 for experiment ${index + 1}`],
      nextSteps: [`Next step 1 for experiment ${index + 1}`, `Next step 2 for experiment ${index + 1}`]
    }
  }));
};

const AdvancedRecommendationSystems: React.FC = () => {
  const [selectedTimeframe, setSelectedTimeframe] = useState('7d');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedEngine, setSelectedEngine] = useState('all');
  const [selectedSegment, setSelectedSegment] = useState('all');
  const [activeView, setActiveView] = useState('overview');

  // Generate mock data
  const engines = useMemo(() => generateRecommendationEngines(), []);
  const recommendations = useMemo(() => generateProductRecommendations(), []);
  const userSegments = useMemo(() => generateUserSegments(), []);
  const performanceData = useMemo(() => generatePerformanceData(), []);
  const insights = useMemo(() => generateRecommendationInsights(), []);
  const experiments = useMemo(() => generateABTestExperiments(), []);

  // Filter and process data
  const filteredRecommendations = useMemo(() => {
    return recommendations.filter(rec => {
      const matchesSearch = searchTerm === '' || rec.productName.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesEngine = selectedEngine === 'all' || rec.algorithm === selectedEngine;
      return matchesSearch && matchesEngine;
    });
  }, [recommendations, searchTerm, selectedEngine]);

  const enginePerformance = useMemo(() => {
    return engines.map(engine => ({
      name: engine.name.substring(0, 15),
      accuracy: engine.accuracy * 100,
      precision: engine.precision * 100,
      recall: engine.recall * 100,
      f1Score: engine.f1Score * 100,
      clickThroughRate: engine.clickThroughRate * 100,
      conversionRate: engine.conversionRate * 100
    }));
  }, [engines]);

  const segmentPerformance = useMemo(() => {
    return userSegments.map(segment => ({
      segment: segment.name,
      users: segment.userCount,
      ctr: segment.performance.clickThroughRate * 100,
      conversion: segment.performance.conversionRate * 100,
      revenue: segment.performance.revenue,
      satisfaction: segment.performance.satisfaction
    }));
  }, [userSegments]);

  const performanceTrends = useMemo(() => {
    return performanceData.map(data => ({
      date: data.date,
      recommendations: data.totalRecommendations,
      clicks: data.clicks,
      conversions: data.purchases,
      revenue: data.revenue,
      ctr: data.clickThroughRate * 100,
      conversion_rate: data.conversionRate * 100,
      response_time: data.responseTime
    }));
  }, [performanceData]);

  const algorithmDistribution = useMemo(() => {
    const distribution: { [key: string]: number } = {};
    recommendations.forEach(rec => {
      distribution[rec.algorithm] = (distribution[rec.algorithm] || 0) + 1;
    });
    
    return Object.entries(distribution).map(([algorithm, count]) => ({
      name: algorithm.replace('_', ' '),
      value: count,
      percentage: ((count / recommendations.length) * 100).toFixed(1)
    }));
  }, [recommendations]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-600 bg-green-50';
      case 'training': return 'text-yellow-600 bg-yellow-50';
      case 'inactive': return 'text-gray-600 bg-gray-50';
      case 'testing': return 'text-blue-600 bg-blue-50';
      case 'running': return 'text-blue-600 bg-blue-50';
      case 'completed': return 'text-green-600 bg-green-50';
      case 'paused': return 'text-yellow-600 bg-yellow-50';
      case 'planning': return 'text-purple-600 bg-purple-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high': return 'text-red-600 bg-red-50';
      case 'medium': return 'text-yellow-600 bg-yellow-50';
      case 'low': return 'text-green-600 bg-green-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const colors = ['#8B5CF6', '#10B981', '#F59E0B', '#EF4444', '#3B82F6', '#06B6D4', '#84CC16', '#F97316'];

  return (
    <div className="p-6 space-y-6 bg-gradient-to-br from-violet-50 via-white to-purple-50 min-h-screen">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-2">
            <Sparkles className="text-violet-600" />
            Advanced Recommendation Systems
          </h1>
          <p className="text-gray-600 mt-1">AI-powered personalized product recommendations and user intelligence</p>
        </div>
        <div className="flex items-center gap-4">
          <select
            value={selectedTimeframe}
            onChange={(e) => setSelectedTimeframe(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-violet-500 focus:border-transparent"
          >
            <option value="1d">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
            <option value="90d">Last 90 Days</option>
          </select>
          <Button className="bg-violet-600 hover:bg-violet-700">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh Data
          </Button>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Card className="bg-gradient-to-r from-violet-500 to-violet-600 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-violet-100">Active Engines</p>
                  <p className="text-3xl font-bold">{engines.filter(e => e.status === 'active').length}</p>
                  <p className="text-violet-100 text-sm">Avg. Accuracy: {(engines.filter(e => e.status === 'active').reduce((sum, e) => sum + e.accuracy, 0) / engines.filter(e => e.status === 'active').length * 100).toFixed(1)}%</p>
                </div>
                <Brain className="h-12 w-12 text-violet-200" />
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-100">Recommendations Served</p>
                  <p className="text-3xl font-bold">{engines.reduce((sum, e) => sum + e.totalRecommendations, 0).toLocaleString()}</p>
                  <p className="text-blue-100 text-sm">+{Math.floor(Math.random() * 30 + 15)}% vs last period</p>
                </div>
                <Target className="h-12 w-12 text-blue-200" />
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <Card className="bg-gradient-to-r from-green-500 to-green-600 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-green-100">Click-Through Rate</p>
                  <p className="text-3xl font-bold">{(engines.reduce((sum, e) => sum + e.clickThroughRate, 0) / engines.length * 100).toFixed(1)}%</p>
                  <p className="text-green-100 text-sm">Conversion: {(engines.reduce((sum, e) => sum + e.conversionRate, 0) / engines.length * 100).toFixed(1)}%</p>
                </div>
                <TrendingUp className="h-12 w-12 text-green-200" />
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          <Card className="bg-gradient-to-r from-orange-500 to-orange-600 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-orange-100">Revenue Generated</p>
                  <p className="text-3xl font-bold">${(engines.reduce((sum, e) => sum + e.revenue, 0) / 1000).toFixed(0)}K</p>
                  <p className="text-orange-100 text-sm">Active Users: {engines.reduce((sum, e) => sum + e.activeUsers, 0).toLocaleString()}</p>
                </div>
                <DollarSign className="h-12 w-12 text-orange-200" />
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeView} onValueChange={setActiveView} className="space-y-6">
        <TabsList className="grid w-full grid-cols-7">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="engines">Engines</TabsTrigger>
          <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
          <TabsTrigger value="segments">User Segments</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="insights">Insights</TabsTrigger>
          <TabsTrigger value="experiments">A/B Tests</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Engine Performance Radar */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Brain className="h-5 w-5 text-violet-600" />
                  Engine Performance Comparison
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <RadarChart data={enginePerformance.slice(0, 5)}>
                    <PolarGrid />
                    <PolarAngleAxis dataKey="name" />
                    <PolarRadiusAxis />
                    <Radar name="Accuracy" dataKey="accuracy" stroke="#8B5CF6" fill="#8B5CF6" fillOpacity={0.3} />
                    <Radar name="Precision" dataKey="precision" stroke="#10B981" fill="#10B981" fillOpacity={0.3} />
                    <Radar name="Recall" dataKey="recall" stroke="#F59E0B" fill="#F59E0B" fillOpacity={0.3} />
                    <Tooltip />
                    <Legend />
                  </RadarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Algorithm Distribution */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <PieChart className="h-5 w-5 text-blue-600" />
                  Algorithm Usage Distribution
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <RechartsPieChart>
                    <Tooltip />
                    <Legend />
                    <RechartsPieChart data={algorithmDistribution}>
                      {algorithmDistribution.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
                      ))}
                    </RechartsPieChart>
                  </RechartsPieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          {/* Performance Trends */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <LineChart className="h-5 w-5 text-green-600" />
                Recommendation Performance Trends
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <ComposedChart data={performanceTrends}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis yAxisId="left" />
                  <YAxis yAxisId="right" orientation="right" />
                  <Tooltip />
                  <Legend />
                  <Bar yAxisId="left" dataKey="recommendations" fill="#8B5CF6" name="Recommendations" />
                  <Line yAxisId="right" type="monotone" dataKey="ctr" stroke="#10B981" name="CTR %" />
                  <Line yAxisId="right" type="monotone" dataKey="conversion_rate" stroke="#F59E0B" name="Conversion %" />
                </ComposedChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* User Segment Performance */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5 text-purple-600" />
                User Segment Performance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={segmentPerformance}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="segment" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="ctr" fill="#8B5CF6" name="CTR %" />
                  <Bar dataKey="conversion" fill="#10B981" name="Conversion %" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Recommendation Engines Tab */}
        <TabsContent value="engines" className="space-y-6">
          <div className="grid gap-4">
            {engines.map((engine) => (
              <motion.div
                key={engine.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
              >
                <Card>
                  <CardContent className="p-6">
                    <div className="flex justify-between items-start mb-4">
                      <div className="flex items-center gap-3">
                        <Brain className="h-5 w-5 text-violet-600" />
                        <div>
                          <h3 className="font-semibold text-lg">{engine.name}</h3>
                          <p className="text-sm text-gray-600 capitalize">{engine.type.replace('_', ' ')}</p>
                        </div>
                        <Badge className={getStatusColor(engine.status)}>
                          {engine.status}
                        </Badge>
                      </div>
                      <div className="text-right">
                        <div className="text-2xl font-bold">{(engine.accuracy * 100).toFixed(1)}%</div>
                        <div className="text-sm text-gray-600">Accuracy</div>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 mb-4">
                      <div>
                        <div className="text-xs text-gray-600">Precision</div>
                        <div className="font-semibold">{(engine.precision * 100).toFixed(1)}%</div>
                      </div>
                      <div>
                        <div className="text-xs text-gray-600">Recall</div>
                        <div className="font-semibold">{(engine.recall * 100).toFixed(1)}%</div>
                      </div>
                      <div>
                        <div className="text-xs text-gray-600">F1 Score</div>
                        <div className="font-semibold">{(engine.f1Score * 100).toFixed(1)}%</div>
                      </div>
                      <div>
                        <div className="text-xs text-gray-600">Coverage</div>
                        <div className="font-semibold">{(engine.coverage * 100).toFixed(1)}%</div>
                      </div>
                      <div>
                        <div className="text-xs text-gray-600">Diversity</div>
                        <div className="font-semibold">{(engine.diversity * 100).toFixed(1)}%</div>
                      </div>
                      <div>
                        <div className="text-xs text-gray-600">Novelty</div>
                        <div className="font-semibold">{(engine.novelty * 100).toFixed(1)}%</div>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                      <div>
                        <div className="text-xs text-gray-600">Active Users</div>
                        <div className="font-semibold">{engine.activeUsers.toLocaleString()}</div>
                      </div>
                      <div>
                        <div className="text-xs text-gray-600">Recommendations</div>
                        <div className="font-semibold">{engine.totalRecommendations.toLocaleString()}</div>
                      </div>
                      <div>
                        <div className="text-xs text-gray-600">Response Time</div>
                        <div className="font-semibold">{engine.responseTime.toFixed(0)}ms</div>
                      </div>
                      <div>
                        <div className="text-xs text-gray-600">Revenue</div>
                        <div className="font-semibold">${(engine.revenue / 1000).toFixed(0)}K</div>
                      </div>
                    </div>

                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-4">
                        <div className="text-sm">
                          <span className="text-gray-600">CTR:</span>
                          <span className="font-semibold ml-1">{(engine.clickThroughRate * 100).toFixed(1)}%</span>
                        </div>
                        <div className="text-sm">
                          <span className="text-gray-600">Conversion:</span>
                          <span className="font-semibold ml-1">{(engine.conversionRate * 100).toFixed(1)}%</span>
                        </div>
                        <div className="text-sm text-gray-600">
                          Last trained: {new Date(engine.lastTrained).toLocaleDateString()}
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          <Settings className="h-4 w-4 mr-1" />
                          Configure
                        </Button>
                        <Button variant="outline" size="sm">
                          <Activity className="h-4 w-4 mr-1" />
                          Monitor
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </TabsContent>

        {/* Recommendations Tab */}
        <TabsContent value="recommendations" className="space-y-6">
          {/* Search and Filters */}
          <Card>
            <CardContent className="p-4">
              <div className="flex flex-wrap gap-4 items-center">
                <div className="flex-1 min-w-64">
                  <Input
                    placeholder="Search recommendations..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full"
                  />
                </div>
                <select
                  value={selectedEngine}
                  onChange={(e) => setSelectedEngine(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-violet-500 focus:border-transparent"
                >
                  <option value="all">All Algorithms</option>
                  <option value="collaborative">Collaborative Filtering</option>
                  <option value="content_based">Content Based</option>
                  <option value="hybrid">Hybrid</option>
                  <option value="trending">Trending</option>
                </select>
                <Button variant="outline">
                  <Filter className="h-4 w-4 mr-2" />
                  Advanced Filters
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Recommendations Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {filteredRecommendations.slice(0, 12).map((rec) => (
              <motion.div
                key={rec.id}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3 }}
              >
                <Card className="hover:shadow-lg transition-shadow">
                  <CardContent className="p-4">
                    <div className="aspect-square bg-gray-100 rounded-lg mb-3 flex items-center justify-center">
                      <Package className="h-8 w-8 text-gray-400" />
                    </div>
                    
                    <div className="space-y-2">
                      <h4 className="font-semibold truncate">{rec.productName}</h4>
                      <p className="text-sm text-gray-600">{rec.category}</p>
                      
                      <div className="flex justify-between items-center">
                        <span className="font-bold text-lg">${rec.price.toFixed(0)}</span>
                        <div className="flex items-center gap-1">
                          <Star className="h-4 w-4 text-yellow-500 fill-current" />
                          <span className="text-sm">{rec.rating.toFixed(1)}</span>
                        </div>
                      </div>
                      
                      <div className="space-y-1">
                        <div className="flex justify-between text-xs">
                          <span className="text-gray-600">Confidence</span>
                          <span className="font-semibold">{(rec.confidence * 100).toFixed(0)}%</span>
                        </div>
                        <div className="flex justify-between text-xs">
                          <span className="text-gray-600">Click Prob.</span>
                          <span className="font-semibold">{(rec.clickProbability * 100).toFixed(1)}%</span>
                        </div>
                      </div>
                      
                      <Badge variant="outline" className="text-xs">
                        {rec.algorithm.replace('_', ' ')}
                      </Badge>
                      
                      <p className="text-xs text-gray-600 italic">{rec.reason}</p>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </TabsContent>

        {/* User Segments Tab */}
        <TabsContent value="segments" className="space-y-6">
          <div className="grid gap-6">
            {userSegments.map((segment) => (
              <motion.div
                key={segment.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                <Card>
                  <CardContent className="p-6">
                    <div className="flex justify-between items-start mb-4">
                      <div>
                        <h3 className="font-semibold text-lg flex items-center gap-2">
                          <Users className="h-5 w-5 text-purple-600" />
                          {segment.name}
                        </h3>
                        <p className="text-gray-600 mt-1">{segment.description}</p>
                      </div>
                      <div className="text-right">
                        <div className="text-2xl font-bold">{segment.userCount.toLocaleString()}</div>
                        <div className="text-sm text-gray-600">Users</div>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      {/* Characteristics */}
                      <div>
                        <h4 className="font-semibold mb-2">Characteristics</h4>
                        <div className="space-y-2 text-sm">
                          <div><span className="text-gray-600">Age Range:</span> {segment.characteristics.ageRange}</div>
                          <div><span className="text-gray-600">Gender:</span> {segment.characteristics.gender}</div>
                          <div><span className="text-gray-600">Spending:</span> {segment.characteristics.spendingHabits}</div>
                          <div><span className="text-gray-600">Frequency:</span> {segment.characteristics.shoppingFrequency}</div>
                          <div><span className="text-gray-600">Device:</span> {segment.characteristics.devicePreference}</div>
                          <div><span className="text-gray-600">Price Sensitivity:</span> 
                            <Badge className={`ml-1 ${segment.characteristics.priceSensitivity === 'high' ? 'bg-red-100 text-red-800' : segment.characteristics.priceSensitivity === 'medium' ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'}`}>
                              {segment.characteristics.priceSensitivity}
                            </Badge>
                          </div>
                        </div>
                      </div>

                      {/* Behavior Patterns */}
                      <div>
                        <h4 className="font-semibold mb-2">Behavior Patterns</h4>
                        <div className="space-y-2 text-sm">
                          <div><span className="text-gray-600">Session Time:</span> {segment.behaviorPatterns.averageSessionTime.toFixed(1)}min</div>
                          <div><span className="text-gray-600">Order Value:</span> ${segment.behaviorPatterns.averageOrderValue.toFixed(0)}</div>
                          <div><span className="text-gray-600">Conversion:</span> {(segment.behaviorPatterns.conversionRate * 100).toFixed(1)}%</div>
                          <div><span className="text-gray-600">Return Rate:</span> {(segment.behaviorPatterns.returnRate * 100).toFixed(1)}%</div>
                          <div><span className="text-gray-600">Brand Loyalty:</span> {(segment.behaviorPatterns.brandLoyalty * 100).toFixed(0)}%</div>
                        </div>
                      </div>

                      {/* Performance */}
                      <div>
                        <h4 className="font-semibold mb-2">Recommendation Performance</h4>
                        <div className="space-y-2 text-sm">
                          <div><span className="text-gray-600">CTR:</span> {(segment.performance.clickThroughRate * 100).toFixed(1)}%</div>
                          <div><span className="text-gray-600">Conversion:</span> {(segment.performance.conversionRate * 100).toFixed(1)}%</div>
                          <div><span className="text-gray-600">Revenue:</span> ${(segment.performance.revenue / 1000).toFixed(0)}K</div>
                          <div><span className="text-gray-600">Satisfaction:</span> 
                            <div className="flex items-center gap-1 mt-1">
                              {Array.from({ length: 5 }, (_, i) => (
                                <Star
                                  key={i}
                                  className={`h-3 w-3 ${i < Math.floor(segment.performance.satisfaction) ? 'text-yellow-500 fill-current' : 'text-gray-300'}`}
                                />
                              ))}
                              <span className="ml-1">{segment.performance.satisfaction.toFixed(1)}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="mt-4 pt-4 border-t border-gray-200">
                      <div className="flex justify-between items-center">
                        <div className="flex flex-wrap gap-1">
                          {segment.characteristics.preferredCategories.map((category, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {category}
                            </Badge>
                          ))}
                        </div>
                        <div className="flex gap-2">
                          <Button variant="outline" size="sm">
                            <Target className="h-4 w-4 mr-1" />
                            Target Campaign
                          </Button>
                          <Button variant="outline" size="sm">
                            <Settings className="h-4 w-4 mr-1" />
                            Customize Algorithm
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </TabsContent>

        {/* Performance Tab */}
        <TabsContent value="performance" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* CTR Trends */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5 text-green-600" />
                  Click-Through Rate Trends
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={performanceTrends}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Area type="monotone" dataKey="ctr" stroke="#10B981" fill="#10B981" fillOpacity={0.3} />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Response Time */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5 text-blue-600" />
                  Response Time Performance
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <RechartsLineChart data={performanceTrends}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Line type="monotone" dataKey="response_time" stroke="#3B82F6" />
                  </RechartsLineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          {/* Revenue and Conversion Trends */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5 text-green-600" />
                Revenue and Conversion Performance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <ComposedChart data={performanceTrends}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis yAxisId="left" />
                  <YAxis yAxisId="right" orientation="right" />
                  <Tooltip />
                  <Legend />
                  <Bar yAxisId="left" dataKey="revenue" fill="#10B981" name="Revenue ($)" />
                  <Line yAxisId="right" type="monotone" dataKey="conversion_rate" stroke="#8B5CF6" name="Conversion Rate %" />
                </ComposedChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Performance Summary Table */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5 text-purple-600" />
                Engine Performance Summary
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left p-2">Engine</th>
                      <th className="text-left p-2">Status</th>
                      <th className="text-left p-2">Accuracy</th>
                      <th className="text-left p-2">CTR</th>
                      <th className="text-left p-2">Conversion</th>
                      <th className="text-left p-2">Revenue</th>
                      <th className="text-left p-2">Response Time</th>
                    </tr>
                  </thead>
                  <tbody>
                    {engines.map((engine) => (
                      <motion.tr
                        key={engine.id}
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        className="border-b hover:bg-gray-50"
                      >
                        <td className="p-2 font-medium">{engine.name}</td>
                        <td className="p-2">
                          <Badge className={getStatusColor(engine.status)}>
                            {engine.status}
                          </Badge>
                        </td>
                        <td className="p-2">{(engine.accuracy * 100).toFixed(1)}%</td>
                        <td className="p-2">{(engine.clickThroughRate * 100).toFixed(1)}%</td>
                        <td className="p-2">{(engine.conversionRate * 100).toFixed(1)}%</td>
                        <td className="p-2">${(engine.revenue / 1000).toFixed(0)}K</td>
                        <td className="p-2">{engine.responseTime.toFixed(0)}ms</td>
                      </motion.tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Insights Tab */}
        <TabsContent value="insights" className="space-y-6">
          <div className="grid gap-4">
            {insights.map((insight) => (
              <motion.div
                key={insight.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
              >
                <Card>
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start mb-3">
                      <div className="flex items-center gap-2">
                        <div className="flex items-center gap-2">
                          {insight.type === 'performance' && <TrendingUp className="h-4 w-4 text-green-600" />}
                          {insight.type === 'user_behavior' && <Users className="h-4 w-4 text-blue-600" />}
                          {insight.type === 'product_affinity' && <Heart className="h-4 w-4 text-red-600" />}
                          {insight.type === 'seasonal_trend' && <Calendar className="h-4 w-4 text-orange-600" />}
                          {insight.type === 'algorithm_drift' && <Brain className="h-4 w-4 text-purple-600" />}
                          {insight.type === 'data_quality' && <Database className="h-4 w-4 text-gray-600" />}
                          {insight.type === 'business_impact' && <DollarSign className="h-4 w-4 text-green-600" />}
                          <h3 className="font-semibold">{insight.title}</h3>
                        </div>
                        <Badge className={getImpactColor(insight.impact)}>
                          {insight.impact} impact
                        </Badge>
                      </div>
                      <div className="text-right">
                        <div className="text-sm text-gray-600">{(insight.confidence * 100).toFixed(1)}% confidence</div>
                        <div className="text-xs text-gray-500">Priority: {insight.priority}</div>
                      </div>
                    </div>
                    
                    <p className="text-gray-700 mb-3">{insight.description}</p>
                    
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-3">
                      <div>
                        <div className="text-xs text-gray-600">Baseline</div>
                        <div className="font-semibold">{insight.metrics.baseline.toFixed(1)}</div>
                      </div>
                      <div>
                        <div className="text-xs text-gray-600">Current</div>
                        <div className="font-semibold">{insight.metrics.current.toFixed(1)}</div>
                      </div>
                      <div>
                        <div className="text-xs text-gray-600">Change</div>
                        <div className={`font-semibold ${insight.metrics.change > 0 ? 'text-green-600' : insight.metrics.change < 0 ? 'text-red-600' : 'text-gray-600'}`}>
                          {insight.metrics.change > 0 ? '+' : ''}{insight.metrics.change.toFixed(1)}
                        </div>
                      </div>
                      <div>
                        <div className="text-xs text-gray-600">Significance</div>
                        <div className="font-semibold">{(insight.metrics.significance * 100).toFixed(1)}%</div>
                      </div>
                    </div>
                    
                    {insight.recommendations.length > 0 && (
                      <div className="mb-3">
                        <h4 className="font-semibold text-sm mb-1">Recommendations:</h4>
                        <ul className="list-disc list-inside space-y-1">
                          {insight.recommendations.map((rec, index) => (
                            <li key={index} className="text-sm text-gray-600">{rec}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                    
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-4">
                        <Badge variant="outline" className="text-xs">
                          {insight.category}
                        </Badge>
                        {insight.actionable && (
                          <Badge className="bg-green-100 text-green-800 text-xs">
                            Actionable
                          </Badge>
                        )}
                        {insight.affectedSegments.length > 0 && (
                          <div className="text-xs text-gray-600">
                            Segments: {insight.affectedSegments.join(', ')}
                          </div>
                        )}
                      </div>
                      <span className="text-xs text-gray-500">
                        {new Date(insight.timestamp).toLocaleDateString()}
                      </span>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </TabsContent>

        {/* A/B Tests Tab */}
        <TabsContent value="experiments" className="space-y-6">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold">A/B Test Experiments</h3>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create Experiment
            </Button>
          </div>

          <div className="grid gap-6">
            {experiments.map((experiment) => (
              <motion.div
                key={experiment.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                <Card>
                  <CardContent className="p-6">
                    <div className="flex justify-between items-start mb-4">
                      <div>
                        <h3 className="font-semibold text-lg flex items-center gap-2">
                          <Zap className="h-5 w-5 text-yellow-600" />
                          {experiment.name}
                        </h3>
                        <p className="text-gray-600 mt-1">{experiment.description}</p>
                      </div>
                      <div className="text-right">
                        <Badge className={getStatusColor(experiment.status)}>
                          {experiment.status}
                        </Badge>
                        <div className="text-sm text-gray-600 mt-1">
                          {(experiment.confidence * 100).toFixed(1)}% confidence
                        </div>
                      </div>
                    </div>

                    <div className="mb-4">
                      <h4 className="font-semibold mb-2">Hypothesis</h4>
                      <p className="text-sm text-gray-700 italic">{experiment.hypothesis}</p>
                    </div>

                    <div className="space-y-4">
                      <h4 className="font-semibold">Variants Performance</h4>
                      {experiment.variants.map((variant) => (
                        <div key={variant.id} className="border rounded-lg p-4 bg-gray-50">
                          <div className="flex justify-between items-start mb-3">
                            <div>
                              <h5 className="font-medium">{variant.name}</h5>
                              <p className="text-sm text-gray-600">{variant.description}</p>
                            </div>
                            <div className="text-right">
                              <div className="text-sm text-gray-600">Traffic: {variant.trafficAllocation}%</div>
                              {experiment.winner === variant.id && (
                                <Badge className="bg-green-100 text-green-800 mt-1">
                                  <Crown className="h-3 w-3 mr-1" />
                                  Winner
                                </Badge>
                              )}
                            </div>
                          </div>
                          
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                            <div>
                              <div className="text-xs text-gray-600">Impressions</div>
                              <div className="font-semibold">{variant.performance.impressions.toLocaleString()}</div>
                            </div>
                            <div>
                              <div className="text-xs text-gray-600">CTR</div>
                              <div className="font-semibold">{(variant.performance.clickThroughRate * 100).toFixed(1)}%</div>
                            </div>
                            <div>
                              <div className="text-xs text-gray-600">Conversion</div>
                              <div className="font-semibold">{(variant.performance.conversionRate * 100).toFixed(1)}%</div>
                            </div>
                            <div>
                              <div className="text-xs text-gray-600">Revenue</div>
                              <div className="font-semibold">${(variant.performance.revenue / 1000).toFixed(0)}K</div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>

                    {experiment.status === 'completed' && experiment.results && (
                      <div className="mt-4 pt-4 border-t border-gray-200">
                        <h4 className="font-semibold mb-2">Results Summary</h4>
                        <p className="text-sm text-gray-700 mb-3">{experiment.results.summary}</p>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <h5 className="font-medium mb-1">Key Insights</h5>
                            <ul className="list-disc list-inside space-y-1">
                              {experiment.results.insights.map((insight, index) => (
                                <li key={index} className="text-sm text-gray-600">{insight}</li>
                              ))}
                            </ul>
                          </div>
                          <div>
                            <h5 className="font-medium mb-1">Next Steps</h5>
                            <ul className="list-disc list-inside space-y-1">
                              {experiment.results.nextSteps.map((step, index) => (
                                <li key={index} className="text-sm text-gray-600">{step}</li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      </div>
                    )}

                    <div className="flex justify-between items-center mt-4 pt-4 border-t border-gray-200">
                      <div className="text-sm text-gray-600">
                        <span>Primary Metric: {experiment.primaryMetric}</span>
                        <span className="mx-2">•</span>
                        <span>{new Date(experiment.startDate).toLocaleDateString()} - {new Date(experiment.endDate).toLocaleDateString()}</span>
                      </div>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4 mr-1" />
                          View Details
                        </Button>
                        <Button variant="outline" size="sm">
                          <Download className="h-4 w-4 mr-1" />
                          Export Results
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AdvancedRecommendationSystems;