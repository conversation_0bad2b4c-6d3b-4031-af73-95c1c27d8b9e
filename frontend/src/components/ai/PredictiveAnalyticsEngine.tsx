/**
 * Predictive Analytics Engine
 * Advanced AI-powered forecasting and predictive modeling for e-commerce analytics
 */

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Brain,
  TrendingUp,
  TrendingDown,
  Activity,
  BarChart3,
  LineChart,
  PieChart,
  Target,
  Zap,
  Eye,
  Clock,
  Calendar,
  Users,
  ShoppingCart,
  DollarSign,
  Package,
  Globe,
  AlertTriangle,
  CheckCircle,
  Info,
  Settings,
  RefreshCw,
  Download,
  Upload,
  Share,
  Filter,
  Search,
  Play,
  Pause,
  RotateCw,
  Layers,
  Database,
  Cpu,
  Network,
  Gauge,
  Lightbulb,
  Rocket,
  Star,
  Award,
  Crown,
  Gem,
  Sparkles,
  Wand2,
  Focus,
  Crosshair,
  Navigation,
  Compass,
  MapPin,
  Route,
  ArrowUp,
  ArrowDown,
  ArrowRight,
  ArrowLeft,
  ChevronUp,
  ChevronDown,
  Plus,
  Minus,
  Edit,
  Trash,
  MoreHorizontal,
  ExternalLink,
  Link2,
  BookOpen,
  FileText,
  File,
  Folder,
  Archive,
  Save,
  Mail,
  Phone,
  Bell,
  BellRing,
  Volume2,
  Mic,
  Speaker,
  Headphones,
  Monitor,
  Smartphone,
  Tablet,
  Watch,
  Laptop,
  Server,
  Cloud,
  Wifi,
  Signal,
  Battery,
  Power,
  Shield,
  Lock,
  Key,
  Unlock
} from 'lucide-react';
import { LineChart as RechartsLineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area, BarChart as RechartsBarChart, Bar, ComposedChart, ScatterChart, Scatter, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar, Cell } from 'recharts';

// TypeScript interfaces for predictive analytics
interface PredictionModel {
  id: string;
  name: string;
  type: 'sales_forecast' | 'demand_forecast' | 'churn_prediction' | 'clv_prediction' | 'price_optimization' | 'inventory_optimization' | 'market_trend' | 'seasonal_analysis';
  description: string;
  algorithm: 'linear_regression' | 'random_forest' | 'xgboost' | 'lstm' | 'arima' | 'prophet' | 'neural_network' | 'ensemble';
  status: 'training' | 'ready' | 'deployed' | 'updating' | 'error';
  accuracy: number;
  confidence: number;
  lastUpdated: string;
  nextUpdate: string;
  trainingData: {
    startDate: string;
    endDate: string;
    dataPoints: number;
    features: string[];
    targetVariable: string;
  };
  performance: {
    mape: number; // Mean Absolute Percentage Error
    rmse: number; // Root Mean Square Error
    mae: number;  // Mean Absolute Error
    r2Score: number; // R-squared Score
    precision: number;
    recall: number;
    f1Score: number;
  };
  hyperparameters: Record<string, any>;
  featureImportance: { feature: string; importance: number; description: string }[];
  businessImpact: {
    revenueImpact: number;
    costSavings: number;
    efficiency: number;
    riskReduction: number;
  };
  deployment: {
    environment: 'staging' | 'production';
    version: string;
    apiEndpoint: string;
    latency: number;
    throughput: number;
    uptime: number;
  };
}

interface ForecastPrediction {
  id: string;
  modelId: string;
  predictionType: 'sales' | 'demand' | 'revenue' | 'customers' | 'inventory' | 'churn' | 'clv';
  timeHorizon: '1d' | '7d' | '30d' | '90d' | '365d';
  granularity: 'hourly' | 'daily' | 'weekly' | 'monthly' | 'quarterly';
  forecastData: {
    timestamp: string;
    predicted: number;
    lower_bound: number;
    upper_bound: number;
    confidence: number;
    actual?: number;
    factors: { factor: string; impact: number }[];
  }[];
  seasonalComponents: {
    trend: number;
    seasonal: number;
    cyclical: number;
    irregular: number;
  };
  businessContext: {
    businessSegment: string;
    productCategory: string;
    customerSegment: string;
    geographicRegion: string;
    channel: string;
  };
  accuracy: {
    historical: number;
    recentTrend: number;
    seasonalAccuracy: number;
    volatility: number;
  };
  actionableInsights: {
    insight: string;
    confidence: number;
    impact: 'low' | 'medium' | 'high' | 'critical';
    timeframe: string;
    recommendation: string;
  }[];
  anomalies: {
    timestamp: string;
    severity: 'low' | 'medium' | 'high';
    description: string;
    impact: number;
  }[];
  createdAt: string;
  expiresAt: string;
}

interface SeasonalPattern {
  id: string;
  name: string;
  type: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly' | 'holiday' | 'event';
  pattern: {
    period: string;
    amplitude: number;
    phase: number;
    strength: number;
    consistency: number;
  };
  historicalData: {
    year: number;
    data: { period: string; value: number; variance: number }[];
  }[];
  influencingFactors: {
    factor: string;
    correlation: number;
    impact: number;
    description: string;
  }[];
  businessImplications: {
    inventoryManagement: string;
    marketingStrategy: string;
    staffingNeeds: string;
    pricingStrategy: string;
  };
  predictions: {
    nextOccurrence: string;
    expectedMagnitude: number;
    confidence: number;
    preparationTime: string;
  };
}

interface TrendAnalysis {
  id: string;
  category: 'sales_trend' | 'customer_trend' | 'product_trend' | 'market_trend' | 'behavioral_trend';
  title: string;
  description: string;
  timeframe: string;
  strength: number;
  direction: 'increasing' | 'decreasing' | 'stable' | 'volatile';
  significance: number;
  dataPoints: {
    timestamp: string;
    value: number;
    change: number;
    volatility: number;
  }[];
  drivingFactors: {
    factor: string;
    contribution: number;
    confidence: number;
    description: string;
  }[];
  futureProjection: {
    shortTerm: { value: number; confidence: number; timeframe: string };
    mediumTerm: { value: number; confidence: number; timeframe: string };
    longTerm: { value: number; confidence: number; timeframe: string };
  };
  businessImpact: {
    revenue: number;
    customerAcquisition: number;
    marketShare: number;
    operationalEfficiency: number;
  };
  recommendations: {
    action: string;
    priority: 'low' | 'medium' | 'high' | 'critical';
    timeframe: string;
    expectedOutcome: string;
    resources: string[];
  }[];
  riskFactors: {
    risk: string;
    probability: number;
    impact: number;
    mitigation: string;
  }[];
}

interface PredictiveScenario {
  id: string;
  name: string;
  description: string;
  probability: number;
  timeframe: string;
  inputParameters: {
    parameter: string;
    baseValue: number;
    scenarioValue: number;
    impact: number;
  }[];
  predictions: {
    metric: string;
    baseCase: number;
    scenarioCase: number;
    variance: number;
    confidence: number;
  }[];
  businessOutcomes: {
    revenue: { base: number; scenario: number; change: number };
    costs: { base: number; scenario: number; change: number };
    customers: { base: number; scenario: number; change: number };
    marketShare: { base: number; scenario: number; change: number };
  };
  riskAssessment: {
    financialRisk: number;
    operationalRisk: number;
    marketRisk: number;
    competitiveRisk: number;
  };
  actionPlan: {
    preventiveMeasures: string[];
    contingencyPlans: string[];
    opportunityActions: string[];
    monitoringMetrics: string[];
  };
}

interface ModelPerformanceMetrics {
  modelId: string;
  timeframe: string;
  accuracy: {
    overall: number;
    trend: { timestamp: string; accuracy: number }[];
    byCategory: { category: string; accuracy: number }[];
    byTimeHorizon: { horizon: string; accuracy: number }[];
  };
  latency: {
    average: number;
    p95: number;
    p99: number;
    trend: { timestamp: string; latency: number }[];
  };
  throughput: {
    requestsPerSecond: number;
    dailyPredictions: number;
    trend: { timestamp: string; throughput: number }[];
  };
  resourceUsage: {
    cpuUtilization: number;
    memoryUsage: number;
    diskUsage: number;
    networkIO: number;
  };
  businessMetrics: {
    revenueImpact: number;
    costSavings: number;
    decisionQuality: number;
    userSatisfaction: number;
  };
  errorAnalysis: {
    commonErrors: { error: string; frequency: number; impact: number }[];
    errorTrends: { timestamp: string; errorRate: number }[];
    rootCauses: { cause: string; frequency: number; resolution: string }[];
  };
}

// Mock data generation functions
const generatePredictionModels = (): PredictionModel[] => {
  const modelTypes = ['sales_forecast', 'demand_forecast', 'churn_prediction', 'clv_prediction', 'price_optimization', 'inventory_optimization', 'market_trend', 'seasonal_analysis'] as const;
  const algorithms = ['linear_regression', 'random_forest', 'xgboost', 'lstm', 'arima', 'prophet', 'neural_network', 'ensemble'] as const;
  const statuses = ['training', 'ready', 'deployed', 'updating', 'error'] as const;

  return Array.from({ length: 8 }, (_, i) => ({
    id: `model-${i + 1}`,
    name: [
      'Sales Revenue Forecaster',
      'Product Demand Predictor',
      'Customer Churn Analyzer',
      'Lifetime Value Calculator',
      'Dynamic Pricing Optimizer',
      'Inventory Level Predictor',
      'Market Trend Analyzer',
      'Seasonal Pattern Detector'
    ][i],
    type: modelTypes[i],
    description: [
      'Predicts future sales revenue with 85% accuracy using ensemble methods',
      'Forecasts product demand across categories with seasonal adjustments',
      'Identifies customers at risk of churning with advanced feature engineering',
      'Calculates predicted customer lifetime value using LSTM networks',
      'Optimizes product pricing for maximum revenue and competitiveness',
      'Predicts optimal inventory levels to minimize stockouts and overstock',
      'Analyzes market trends and competitive landscape changes',
      'Detects and predicts seasonal patterns for business planning'
    ][i],
    algorithm: algorithms[i % algorithms.length],
    status: statuses[Math.floor(Math.random() * statuses.length)],
    accuracy: Math.random() * 25 + 75, // 75-100%
    confidence: Math.random() * 20 + 80, // 80-100%
    lastUpdated: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
    nextUpdate: new Date(Date.now() + Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
    trainingData: {
      startDate: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      endDate: new Date().toISOString().split('T')[0],
      dataPoints: Math.floor(Math.random() * 50000) + 10000,
      features: [
        ['Revenue', 'Season', 'Marketing Spend', 'Customer Count', 'Product Mix'],
        ['Category', 'Price', 'Seasonality', 'Promotions', 'Competitor Activity'],
        ['Engagement', 'Recency', 'Frequency', 'Monetary', 'Support Tickets'],
        ['Purchase History', 'Demographics', 'Behavior', 'Engagement', 'Satisfaction'],
        ['Demand', 'Competition', 'Costs', 'Inventory', 'Market Conditions'],
        ['Sales History', 'Lead Time', 'Seasonality', 'Promotions', 'Supply Chain'],
        ['Search Volume', 'Social Media', 'News Sentiment', 'Economic Indicators', 'Competition'],
        ['Historical Sales', 'Weather', 'Events', 'Holidays', 'Economic Cycles']
      ][i],
      targetVariable: [
        'Monthly Revenue', 'Product Demand', 'Churn Probability', 'CLV Score',
        'Optimal Price', 'Stock Level', 'Trend Direction', 'Seasonal Index'
      ][i]
    },
    performance: {
      mape: Math.random() * 15 + 5, // 5-20%
      rmse: Math.random() * 1000 + 100,
      mae: Math.random() * 500 + 50,
      r2Score: Math.random() * 0.3 + 0.7, // 0.7-1.0
      precision: Math.random() * 0.2 + 0.8, // 0.8-1.0
      recall: Math.random() * 0.2 + 0.8, // 0.8-1.0
      f1Score: Math.random() * 0.2 + 0.8 // 0.8-1.0
    },
    hyperparameters: {
      learningRate: Math.random() * 0.01 + 0.001,
      maxDepth: Math.floor(Math.random() * 10) + 5,
      nEstimators: Math.floor(Math.random() * 500) + 100,
      regularization: Math.random() * 0.1 + 0.01
    },
    featureImportance: Array.from({ length: 5 }, (_, j) => ({
      feature: [
        'Historical Sales', 'Seasonality', 'Marketing Spend', 'Customer Behavior', 'Market Conditions',
        'Price Elasticity', 'Competitor Activity', 'Inventory Level', 'Promotion Impact', 'Economic Indicators'
      ][j] || `Feature ${j + 1}`,
      importance: Math.random() * 100,
      description: `Key driver of ${[
        'revenue patterns', 'demand fluctuations', 'customer retention', 'value generation', 'pricing decisions'
      ][j % 5]}`
    })).sort((a, b) => b.importance - a.importance),
    businessImpact: {
      revenueImpact: Math.random() * 500000 + 100000,
      costSavings: Math.random() * 200000 + 50000,
      efficiency: Math.random() * 40 + 60,
      riskReduction: Math.random() * 30 + 70
    },
    deployment: {
      environment: Math.random() > 0.3 ? 'production' : 'staging',
      version: `v1.${Math.floor(Math.random() * 10)}.${Math.floor(Math.random() * 10)}`,
      apiEndpoint: `/api/models/model-${i + 1}/predict`,
      latency: Math.random() * 200 + 50,
      throughput: Math.floor(Math.random() * 1000) + 100,
      uptime: Math.random() * 5 + 95
    }
  }));
};

const generateForecastPredictions = (): ForecastPrediction[] => {
  const predictionTypes = ['sales', 'demand', 'revenue', 'customers', 'inventory', 'churn', 'clv'] as const;
  const timeHorizons = ['1d', '7d', '30d', '90d', '365d'] as const;
  const granularities = ['hourly', 'daily', 'weekly', 'monthly', 'quarterly'] as const;

  return Array.from({ length: 6 }, (_, i) => {
    const timeHorizon = timeHorizons[i % timeHorizons.length];
    const granularity = granularities[i % granularities.length];
    const dataPoints = timeHorizon === '1d' ? 24 : timeHorizon === '7d' ? 7 : timeHorizon === '30d' ? 30 : timeHorizon === '90d' ? 12 : 12;

    return {
      id: `forecast-${i + 1}`,
      modelId: `model-${(i % 8) + 1}`,
      predictionType: predictionTypes[i % predictionTypes.length],
      timeHorizon,
      granularity,
      forecastData: Array.from({ length: dataPoints }, (_, j) => {
        const baseValue = Math.random() * 10000 + 1000;
        const variance = baseValue * 0.1;
        return {
          timestamp: new Date(Date.now() + j * (timeHorizon === '1d' ? 60 * 60 * 1000 : 24 * 60 * 60 * 1000)).toISOString(),
          predicted: baseValue + (Math.random() - 0.5) * variance,
          lower_bound: baseValue - variance,
          upper_bound: baseValue + variance,
          confidence: Math.random() * 20 + 80,
          actual: j < dataPoints / 2 ? baseValue + (Math.random() - 0.5) * variance * 0.8 : undefined,
          factors: [
            { factor: 'Seasonality', impact: Math.random() * 30 + 10 },
            { factor: 'Marketing', impact: Math.random() * 25 + 5 },
            { factor: 'Competition', impact: Math.random() * 20 + 5 },
            { factor: 'Economic', impact: Math.random() * 15 + 5 }
          ]
        };
      }),
      seasonalComponents: {
        trend: Math.random() * 40 + 30,
        seasonal: Math.random() * 30 + 20,
        cyclical: Math.random() * 20 + 10,
        irregular: Math.random() * 15 + 5
      },
      businessContext: {
        businessSegment: ['B2C', 'B2B', 'Enterprise', 'SMB'][i % 4],
        productCategory: ['Electronics', 'Clothing', 'Home', 'Books', 'Sports'][i % 5],
        customerSegment: ['New', 'Returning', 'VIP', 'Churned'][i % 4],
        geographicRegion: ['North America', 'Europe', 'Asia', 'Global'][i % 4],
        channel: ['Online', 'Mobile', 'Store', 'Phone'][i % 4]
      },
      accuracy: {
        historical: Math.random() * 25 + 75,
        recentTrend: Math.random() * 20 + 80,
        seasonalAccuracy: Math.random() * 30 + 70,
        volatility: Math.random() * 40 + 10
      },
      actionableInsights: [
        {
          insight: 'Sales are predicted to increase by 23% next month due to seasonal trends',
          confidence: Math.random() * 20 + 80,
          impact: ['low', 'medium', 'high', 'critical'][Math.floor(Math.random() * 4)] as any,
          timeframe: '30 days',
          recommendation: 'Increase inventory levels and prepare marketing campaigns'
        },
        {
          insight: 'Customer acquisition cost is trending downward, suggesting improved efficiency',
          confidence: Math.random() * 20 + 80,
          impact: ['low', 'medium', 'high', 'critical'][Math.floor(Math.random() * 4)] as any,
          timeframe: '60 days',
          recommendation: 'Scale successful marketing channels and optimize budget allocation'
        }
      ],
      anomalies: [
        {
          timestamp: new Date(Date.now() + Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
          severity: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)] as any,
          description: 'Unusual spike in demand detected, possibly due to viral social media mention',
          impact: Math.random() * 50 + 10
        }
      ],
      createdAt: new Date().toISOString(),
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
    };
  });
};

const generateSeasonalPatterns = (): SeasonalPattern[] => {
  const patternTypes = ['daily', 'weekly', 'monthly', 'quarterly', 'yearly', 'holiday', 'event'] as const;

  return Array.from({ length: 5 }, (_, i) => ({
    id: `pattern-${i + 1}`,
    name: [
      'Holiday Shopping Season',
      'Back-to-School Period',
      'Summer Sales Surge',
      'Black Friday Effect',
      'End-of-Year Clearance'
    ][i],
    type: patternTypes[i % patternTypes.length],
    pattern: {
      period: ['November-December', 'August-September', 'June-August', 'Late November', 'December'][i],
      amplitude: Math.random() * 50 + 25, // 25-75% variation
      phase: Math.random() * 365, // Days offset
      strength: Math.random() * 40 + 60, // 60-100% consistency
      consistency: Math.random() * 30 + 70 // 70-100% reliability
    },
    historicalData: Array.from({ length: 3 }, (_, year) => ({
      year: 2024 - year,
      data: Array.from({ length: 12 }, (_, month) => ({
        period: new Date(2024 - year, month).toLocaleDateString('en-US', { month: 'long' }),
        value: Math.random() * 10000 + 5000,
        variance: Math.random() * 1000 + 500
      }))
    })),
    influencingFactors: [
      {
        factor: 'Marketing Campaigns',
        correlation: Math.random() * 0.4 + 0.6,
        impact: Math.random() * 30 + 20,
        description: 'Increased advertising spend during peak seasons drives higher sales'
      },
      {
        factor: 'Weather Patterns',
        correlation: Math.random() * 0.6 + 0.2,
        impact: Math.random() * 25 + 15,
        description: 'Seasonal weather changes affect product demand and shopping behavior'
      },
      {
        factor: 'Economic Conditions',
        correlation: Math.random() * 0.5 + 0.3,
        impact: Math.random() * 20 + 10,
        description: 'Economic health influences consumer spending during seasonal periods'
      }
    ],
    businessImplications: {
      inventoryManagement: 'Increase stock levels 4-6 weeks before peak season begins',
      marketingStrategy: 'Launch targeted campaigns 2 months prior to seasonal peak',
      staffingNeeds: 'Hire additional seasonal staff and plan training programs',
      pricingStrategy: 'Implement dynamic pricing to maximize revenue during peak demand'
    },
    predictions: {
      nextOccurrence: new Date(Date.now() + Math.random() * 180 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      expectedMagnitude: Math.random() * 40 + 30, // 30-70% increase
      confidence: Math.random() * 20 + 80,
      preparationTime: `${Math.floor(Math.random() * 8) + 4} weeks`
    }
  }));
};

const generateTrendAnalyses = (): TrendAnalysis[] => {
  const categories = ['sales_trend', 'customer_trend', 'product_trend', 'market_trend', 'behavioral_trend'] as const;
  const directions = ['increasing', 'decreasing', 'stable', 'volatile'] as const;

  return Array.from({ length: 6 }, (_, i) => ({
    id: `trend-${i + 1}`,
    category: categories[i % categories.length],
    title: [
      'Mobile Commerce Growth Acceleration',
      'Customer Acquisition Cost Optimization',
      'Premium Product Category Expansion',
      'Competitive Landscape Shifts',
      'Omnichannel Shopping Behavior Evolution',
      'Subscription Model Adoption Trend'
    ][i],
    description: [
      'Mobile commerce showing 34% quarter-over-quarter growth with improved conversion rates',
      'Customer acquisition costs decreasing by 18% due to improved targeting and retention',
      'Premium product categories experiencing 28% growth in customer preference',
      'Market share redistribution following competitor pricing strategy changes',
      'Customers increasingly using multiple channels within single purchase journeys',
      'Subscription-based offerings gaining traction with 45% higher customer lifetime value'
    ][i],
    timeframe: ['90 days', '6 months', '4 months', '1 year', '8 months', '5 months'][i],
    strength: Math.random() * 40 + 60, // 60-100%
    direction: directions[i % directions.length],
    significance: Math.random() * 30 + 70, // 70-100%
    dataPoints: Array.from({ length: 12 }, (_, j) => ({
      timestamp: new Date(Date.now() - (11 - j) * 7 * 24 * 60 * 60 * 1000).toISOString(),
      value: Math.random() * 1000 + 500 + (j * 10), // Trending upward
      change: (Math.random() - 0.5) * 20,
      volatility: Math.random() * 15 + 5
    })),
    drivingFactors: [
      {
        factor: 'Technology Adoption',
        contribution: Math.random() * 30 + 20,
        confidence: Math.random() * 20 + 80,
        description: 'Improved mobile apps and payment systems driving adoption'
      },
      {
        factor: 'Consumer Behavior',
        contribution: Math.random() * 25 + 15,
        confidence: Math.random() * 20 + 80,
        description: 'Shift in consumer preferences toward digital experiences'
      },
      {
        factor: 'Market Conditions',
        contribution: Math.random() * 20 + 10,
        confidence: Math.random() * 20 + 80,
        description: 'Economic factors influencing purchasing decisions'
      }
    ],
    futureProjection: {
      shortTerm: { 
        value: Math.random() * 1500 + 1000, 
        confidence: Math.random() * 20 + 80, 
        timeframe: '30 days' 
      },
      mediumTerm: { 
        value: Math.random() * 2000 + 1200, 
        confidence: Math.random() * 25 + 75, 
        timeframe: '90 days' 
      },
      longTerm: { 
        value: Math.random() * 3000 + 1500, 
        confidence: Math.random() * 30 + 70, 
        timeframe: '1 year' 
      }
    },
    businessImpact: {
      revenue: Math.random() * 300000 + 100000,
      customerAcquisition: Math.random() * 25 + 15,
      marketShare: Math.random() * 10 + 5,
      operationalEfficiency: Math.random() * 20 + 10
    },
    recommendations: [
      {
        action: 'Increase mobile app development investment',
        priority: ['low', 'medium', 'high', 'critical'][Math.floor(Math.random() * 4)] as any,
        timeframe: '60 days',
        expectedOutcome: '15% improvement in mobile conversion rates',
        resources: ['Development Team', 'UX Designers', 'QA Engineers']
      },
      {
        action: 'Optimize marketing spend allocation',
        priority: ['low', 'medium', 'high', 'critical'][Math.floor(Math.random() * 4)] as any,
        timeframe: '30 days',
        expectedOutcome: '12% reduction in customer acquisition costs',
        resources: ['Marketing Team', 'Data Analysts', 'Campaign Managers']
      }
    ],
    riskFactors: [
      {
        risk: 'Market saturation potential',
        probability: Math.random() * 40 + 20,
        impact: Math.random() * 60 + 40,
        mitigation: 'Diversify into adjacent markets and customer segments'
      },
      {
        risk: 'Competitive response',
        probability: Math.random() * 50 + 30,
        impact: Math.random() * 50 + 30,
        mitigation: 'Strengthen unique value proposition and customer loyalty programs'
      }
    ]
  }));
};

const generatePredictiveScenarios = (): PredictiveScenario[] => {
  return Array.from({ length: 4 }, (_, i) => ({
    id: `scenario-${i + 1}`,
    name: [
      'Economic Recession Impact',
      'Competitor Price War',
      'Supply Chain Disruption',
      'Market Expansion Success'
    ][i],
    description: [
      'Analysis of business performance under economic downturn conditions',
      'Impact assessment of aggressive competitor pricing strategies',
      'Operational and financial effects of supply chain interruptions',
      'Projections for successful expansion into new geographic markets'
    ][i],
    probability: [0.25, 0.4, 0.3, 0.6][i],
    timeframe: ['12 months', '6 months', '9 months', '18 months'][i],
    inputParameters: [
      {
        parameter: 'Consumer Spending',
        baseValue: 100,
        scenarioValue: [70, 95, 85, 120][i],
        impact: [30, 5, 15, -20][i]
      },
      {
        parameter: 'Market Competition',
        baseValue: 100,
        scenarioValue: [110, 140, 105, 85][i],
        impact: [-10, -40, -5, 15][i]
      },
      {
        parameter: 'Operational Costs',
        baseValue: 100,
        scenarioValue: [95, 105, 125, 110][i],
        impact: [5, -5, -25, -10][i]
      }
    ],
    predictions: [
      {
        metric: 'Revenue',
        baseCase: 10000000,
        scenarioCase: [7500000, 8500000, 8000000, 12500000][i],
        variance: [-25, -15, -20, 25][i],
        confidence: Math.random() * 20 + 80
      },
      {
        metric: 'Customer Count',
        baseCase: 50000,
        scenarioCase: [42000, 47000, 45000, 62000][i],
        variance: [-16, -6, -10, 24][i],
        confidence: Math.random() * 20 + 80
      },
      {
        metric: 'Market Share',
        baseCase: 15,
        scenarioCase: [12, 13, 14, 18][i],
        variance: [-20, -13, -7, 20][i],
        confidence: Math.random() * 20 + 80
      }
    ],
    businessOutcomes: {
      revenue: { 
        base: 10000000, 
        scenario: [7500000, 8500000, 8000000, 12500000][i], 
        change: [-25, -15, -20, 25][i] 
      },
      costs: { 
        base: 6000000, 
        scenario: [5700000, 6300000, 7500000, 6600000][i], 
        change: [-5, 5, 25, 10][i] 
      },
      customers: { 
        base: 50000, 
        scenario: [42000, 47000, 45000, 62000][i], 
        change: [-16, -6, -10, 24][i] 
      },
      marketShare: { 
        base: 15, 
        scenario: [12, 13, 14, 18][i], 
        change: [-20, -13, -7, 20][i] 
      }
    },
    riskAssessment: {
      financialRisk: [85, 70, 75, 40][i],
      operationalRisk: [60, 45, 90, 55][i],
      marketRisk: [90, 95, 50, 35][i],
      competitiveRisk: [70, 85, 40, 60][i]
    },
    actionPlan: {
      preventiveMeasures: [
        ['Cost optimization programs', 'Customer retention focus', 'Cash flow management'],
        ['Price matching strategy', 'Value proposition enhancement', 'Customer loyalty programs'],
        ['Supplier diversification', 'Inventory buffer increase', 'Alternative logistics'],
        ['Market research', 'Local partnerships', 'Regulatory compliance']
      ][i],
      contingencyPlans: [
        ['Emergency cost cutting', 'Credit line activation', 'Strategic partnerships'],
        ['Aggressive counter-pricing', 'Product differentiation', 'Market repositioning'],
        ['Emergency suppliers', 'Production relocation', 'Customer communication'],
        ['Expansion pause', 'Resource reallocation', 'Risk mitigation']
      ][i],
      opportunityActions: [
        ['Market share gain', 'Talent acquisition', 'Technology investment'],
        ['Premium positioning', 'Innovation acceleration', 'Market expansion'],
        ['Operational efficiency', 'Technology upgrade', 'Process optimization'],
        ['Brand establishment', 'Customer acquisition', 'Revenue growth']
      ][i],
      monitoringMetrics: [
        ['GDP growth', 'Consumer confidence', 'Unemployment rates'],
        ['Competitor pricing', 'Market share', 'Customer satisfaction'],
        ['Supplier performance', 'Inventory levels', 'Delivery times'],
        ['Market penetration', 'Customer acquisition', 'Revenue growth']
      ][i]
    }
  }));
};

const PredictiveAnalyticsEngine: React.FC = () => {
  const [selectedTimeframe, setSelectedTimeframe] = useState('30d');
  const [selectedModel, setSelectedModel] = useState('all');
  const [selectedPredictionType, setSelectedPredictionType] = useState('all');
  const [viewMode, setViewMode] = useState('overview');
  const [searchTerm, setSearchTerm] = useState('');

  // Generated data
  const predictionModels = useMemo(() => generatePredictionModels(), []);
  const forecastPredictions = useMemo(() => generateForecastPredictions(), []);
  const seasonalPatterns = useMemo(() => generateSeasonalPatterns(), []);
  const trendAnalyses = useMemo(() => generateTrendAnalyses(), []);
  const predictiveScenarios = useMemo(() => generatePredictiveScenarios(), []);

  // Filtering and processing
  const filteredModels = useMemo(() => {
    return predictionModels.filter(model => 
      (selectedModel === 'all' || model.id === selectedModel) &&
      (model.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
       model.description.toLowerCase().includes(searchTerm.toLowerCase()))
    );
  }, [predictionModels, selectedModel, searchTerm]);

  const filteredPredictions = useMemo(() => {
    return forecastPredictions.filter(prediction => 
      (selectedPredictionType === 'all' || prediction.predictionType === selectedPredictionType)
    );
  }, [forecastPredictions, selectedPredictionType]);

  const aggregatedMetrics = useMemo(() => {
    const activeModels = predictionModels.filter(m => m.status === 'deployed').length;
    const averageAccuracy = predictionModels.reduce((sum, m) => sum + m.accuracy, 0) / predictionModels.length;
    const totalPredictions = forecastPredictions.length * 100; // Simulate total predictions
    const businessImpact = predictionModels.reduce((sum, m) => sum + m.businessImpact.revenueImpact, 0);

    return {
      activeModels,
      averageAccuracy,
      totalPredictions,
      businessImpact
    };
  }, [predictionModels, forecastPredictions]);

  const modelPerformanceData = useMemo(() => {
    return predictionModels.map(model => ({
      name: model.name.split(' ').slice(0, 2).join(' '),
      accuracy: model.accuracy,
      confidence: model.confidence,
      impact: model.businessImpact.revenueImpact / 1000,
      latency: model.deployment.latency
    }));
  }, [predictionModels]);

  const getModelIcon = (type: string) => {
    switch (type) {
      case 'sales_forecast': return <TrendingUp className="h-4 w-4" />;
      case 'demand_forecast': return <Package className="h-4 w-4" />;
      case 'churn_prediction': return <Users className="h-4 w-4" />;
      case 'clv_prediction': return <Target className="h-4 w-4" />;
      case 'price_optimization': return <DollarSign className="h-4 w-4" />;
      case 'inventory_optimization': return <Package className="h-4 w-4" />;
      case 'market_trend': return <Globe className="h-4 w-4" />;
      case 'seasonal_analysis': return <Calendar className="h-4 w-4" />;
      default: return <Brain className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'deployed': return 'bg-green-100 text-green-800 border-green-200';
      case 'ready': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'training': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'updating': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'error': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getDirectionIcon = (direction: string) => {
    switch (direction) {
      case 'increasing': return <TrendingUp className="h-4 w-4 text-green-600" />;
      case 'decreasing': return <TrendingDown className="h-4 w-4 text-red-600" />;
      case 'stable': return <Activity className="h-4 w-4 text-blue-600" />;
      case 'volatile': return <Activity className="h-4 w-4 text-orange-600" />;
      default: return <Activity className="h-4 w-4 text-gray-600" />;
    }
  };

  return (
    <div className="w-full space-y-6 p-6 bg-gradient-to-br from-indigo-50 via-blue-50 to-cyan-50 min-h-screen">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-2">
            <Brain className="h-8 w-8 text-blue-600" />
            Predictive Analytics Engine
          </h1>
          <p className="text-gray-600 mt-1">AI-powered forecasting and predictive modeling for e-commerce analytics</p>
        </div>
        
        <div className="flex flex-wrap items-center gap-3">
          <div className="flex items-center gap-2">
            <Search className="h-4 w-4 text-gray-500" />
            <Input
              placeholder="Search models..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-64"
            />
          </div>
          
          <select
            value={selectedModel}
            onChange={(e) => setSelectedModel(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Models</option>
            {predictionModels.map(model => (
              <option key={model.id} value={model.id}>{model.name}</option>
            ))}
          </select>
          
          <select
            value={selectedPredictionType}
            onChange={(e) => setSelectedPredictionType(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Predictions</option>
            <option value="sales">Sales</option>
            <option value="demand">Demand</option>
            <option value="revenue">Revenue</option>
            <option value="customers">Customers</option>
            <option value="inventory">Inventory</option>
            <option value="churn">Churn</option>
            <option value="clv">CLV</option>
          </select>
          
          <select
            value={selectedTimeframe}
            onChange={(e) => setSelectedTimeframe(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
            <option value="90d">Last 90 Days</option>
            <option value="365d">Last Year</option>
          </select>
          
          <Button variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-100 text-sm">Active Models</p>
                <p className="text-2xl font-bold">{aggregatedMetrics.activeModels}</p>
                <p className="text-blue-100 text-xs flex items-center mt-1">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Production ready
                </p>
              </div>
              <Brain className="h-8 w-8 text-blue-200" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-green-500 to-green-600 text-white">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-100 text-sm">Avg Accuracy</p>
                <p className="text-2xl font-bold">{aggregatedMetrics.averageAccuracy.toFixed(1)}%</p>
                <p className="text-green-100 text-xs flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +2.3% vs last month
                </p>
              </div>
              <Target className="h-8 w-8 text-green-200" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-purple-500 to-purple-600 text-white">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-100 text-sm">Predictions</p>
                <p className="text-2xl font-bold">{(aggregatedMetrics.totalPredictions / 1000).toFixed(1)}K</p>
                <p className="text-purple-100 text-xs flex items-center mt-1">
                  <Activity className="h-3 w-3 mr-1" />
                  Last 30 days
                </p>
              </div>
              <Zap className="h-8 w-8 text-purple-200" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-orange-500 to-orange-600 text-white">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-orange-100 text-sm">Business Impact</p>
                <p className="text-2xl font-bold">${(aggregatedMetrics.businessImpact / 1000000).toFixed(1)}M</p>
                <p className="text-orange-100 text-xs flex items-center mt-1">
                  <DollarSign className="h-3 w-3 mr-1" />
                  Annual value
                </p>
              </div>
              <Rocket className="h-8 w-8 text-orange-200" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={viewMode} onValueChange={setViewMode} className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="models">Models</TabsTrigger>
          <TabsTrigger value="forecasts">Forecasts</TabsTrigger>
          <TabsTrigger value="trends">Trends</TabsTrigger>
          <TabsTrigger value="scenarios">Scenarios</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Model Performance */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Model Performance Overview
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <ComposedChart data={modelPerformanceData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" angle={-45} textAnchor="end" height={80} />
                    <YAxis yAxisId="left" />
                    <YAxis yAxisId="right" orientation="right" />
                    <Tooltip />
                    <Bar yAxisId="left" dataKey="accuracy" fill="#3B82F6" name="Accuracy %" />
                    <Line yAxisId="right" type="monotone" dataKey="impact" stroke="#10B981" strokeWidth={2} name="Impact ($K)" />
                  </ComposedChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Recent Predictions */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  Recent Prediction Accuracy
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <RechartsLineChart data={filteredPredictions[0]?.forecastData.slice(0, 10) || []}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="timestamp" tickFormatter={(value) => new Date(value).toLocaleDateString()} />
                    <YAxis />
                    <Tooltip labelFormatter={(value) => new Date(value).toLocaleDateString()} />
                    <Line type="monotone" dataKey="predicted" stroke="#8B5CF6" strokeWidth={2} name="Predicted" />
                    <Line type="monotone" dataKey="actual" stroke="#10B981" strokeWidth={2} name="Actual" strokeDasharray="5 5" />
                  </RechartsLineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          {/* Seasonal Patterns */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Detected Seasonal Patterns
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {seasonalPatterns.slice(0, 3).map((pattern) => (
                  <div key={pattern.id} className="bg-gray-50 p-4 rounded-lg">
                    <h4 className="font-medium text-gray-900 mb-2">{pattern.name}</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Period:</span>
                        <span className="font-medium">{pattern.pattern.period}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Strength:</span>
                        <span className="font-medium">{pattern.pattern.strength.toFixed(1)}%</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Next Peak:</span>
                        <span className="font-medium">{new Date(pattern.predictions.nextOccurrence).toLocaleDateString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Confidence:</span>
                        <span className="font-medium text-green-600">{pattern.predictions.confidence.toFixed(1)}%</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="models" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {filteredModels.map((model) => (
              <Card key={model.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <CardTitle className="flex items-center gap-2">
                      {getModelIcon(model.type)}
                      {model.name}
                    </CardTitle>
                    <Badge className={getStatusColor(model.status)}>
                      {model.status}
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-600">{model.description}</p>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Performance Metrics */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Performance</h4>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div className="bg-blue-50 p-2 rounded text-center">
                        <p className="font-bold text-blue-600">{model.accuracy.toFixed(1)}%</p>
                        <p className="text-xs text-gray-600">Accuracy</p>
                      </div>
                      <div className="bg-green-50 p-2 rounded text-center">
                        <p className="font-bold text-green-600">{model.confidence.toFixed(1)}%</p>
                        <p className="text-xs text-gray-600">Confidence</p>
                      </div>
                      <div className="bg-purple-50 p-2 rounded text-center">
                        <p className="font-bold text-purple-600">{model.performance.mape.toFixed(1)}%</p>
                        <p className="text-xs text-gray-600">MAPE</p>
                      </div>
                      <div className="bg-orange-50 p-2 rounded text-center">
                        <p className="font-bold text-orange-600">{model.performance.r2Score.toFixed(2)}</p>
                        <p className="text-xs text-gray-600">R² Score</p>
                      </div>
                    </div>
                  </div>

                  {/* Algorithm & Data */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Model Details</h4>
                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Algorithm:</span>
                        <span className="font-medium capitalize">{model.algorithm.replace('_', ' ')}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Data Points:</span>
                        <span className="font-medium">{model.trainingData.dataPoints.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Last Updated:</span>
                        <span className="font-medium">{new Date(model.lastUpdated).toLocaleDateString()}</span>
                      </div>
                    </div>
                  </div>

                  {/* Business Impact */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Business Impact</h4>
                    <div className="bg-green-50 p-3 rounded-lg">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Revenue Impact:</span>
                        <span className="font-bold text-green-600">${(model.businessImpact.revenueImpact / 1000).toFixed(0)}K</span>
                      </div>
                      <div className="flex items-center justify-between mt-1">
                        <span className="text-sm text-gray-600">Cost Savings:</span>
                        <span className="font-bold text-green-600">${(model.businessImpact.costSavings / 1000).toFixed(0)}K</span>
                      </div>
                    </div>
                  </div>

                  {/* Top Features */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Top Features</h4>
                    <div className="space-y-1">
                      {model.featureImportance.slice(0, 3).map((feature, i) => (
                        <div key={i} className="flex items-center justify-between text-sm">
                          <span className="text-gray-600">{feature.feature}</span>
                          <div className="flex items-center gap-2">
                            <div className="w-16 bg-gray-200 rounded-full h-2">
                              <div 
                                className="bg-blue-600 h-2 rounded-full" 
                                style={{ width: `${feature.importance}%` }}
                              ></div>
                            </div>
                            <span className="font-medium">{feature.importance.toFixed(0)}%</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <Button variant="outline" size="sm" className="flex-1">
                      <Eye className="h-4 w-4 mr-2" />
                      Details
                    </Button>
                    <Button size="sm" className="flex-1">
                      <Play className="h-4 w-4 mr-2" />
                      Run
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="forecasts" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {filteredPredictions.map((prediction) => (
              <Card key={prediction.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <CardTitle className="flex items-center gap-2">
                      <TrendingUp className="h-5 w-5" />
                      {prediction.predictionType.charAt(0).toUpperCase() + prediction.predictionType.slice(1)} Forecast
                    </CardTitle>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">{prediction.timeHorizon}</Badge>
                      <Badge variant="secondary">{prediction.granularity}</Badge>
                    </div>
                  </div>
                  <p className="text-sm text-gray-600">
                    {prediction.businessContext.businessSegment} • {prediction.businessContext.productCategory}
                  </p>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Forecast Chart */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Prediction Timeline</h4>
                    <ResponsiveContainer width="100%" height={200}>
                      <ComposedChart data={prediction.forecastData.slice(0, 12)}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="timestamp" tickFormatter={(value) => new Date(value).toLocaleDateString()} />
                        <YAxis />
                        <Tooltip labelFormatter={(value) => new Date(value).toLocaleDateString()} />
                        <Area dataKey="upper_bound" fill="#E0E7FF" stroke="none" />
                        <Area dataKey="lower_bound" fill="#FFFFFF" stroke="none" />
                        <Line type="monotone" dataKey="predicted" stroke="#3B82F6" strokeWidth={2} />
                        <Line type="monotone" dataKey="actual" stroke="#10B981" strokeWidth={2} strokeDasharray="5 5" />
                      </ComposedChart>
                    </ResponsiveContainer>
                  </div>

                  {/* Accuracy Metrics */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Accuracy Assessment</h4>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div className="bg-blue-50 p-2 rounded text-center">
                        <p className="font-bold text-blue-600">{prediction.accuracy.historical.toFixed(1)}%</p>
                        <p className="text-xs text-gray-600">Historical</p>
                      </div>
                      <div className="bg-green-50 p-2 rounded text-center">
                        <p className="font-bold text-green-600">{prediction.accuracy.recentTrend.toFixed(1)}%</p>
                        <p className="text-xs text-gray-600">Recent Trend</p>
                      </div>
                      <div className="bg-purple-50 p-2 rounded text-center">
                        <p className="font-bold text-purple-600">{prediction.accuracy.seasonalAccuracy.toFixed(1)}%</p>
                        <p className="text-xs text-gray-600">Seasonal</p>
                      </div>
                      <div className="bg-orange-50 p-2 rounded text-center">
                        <p className="font-bold text-orange-600">{prediction.accuracy.volatility.toFixed(1)}%</p>
                        <p className="text-xs text-gray-600">Volatility</p>
                      </div>
                    </div>
                  </div>

                  {/* Key Insights */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Key Insights</h4>
                    <div className="space-y-2">
                      {prediction.actionableInsights.slice(0, 2).map((insight, i) => (
                        <div key={i} className="p-3 bg-gray-50 rounded-lg">
                          <div className="flex items-start justify-between">
                            <p className="text-sm font-medium text-gray-900">{insight.insight}</p>
                            <Badge 
                              variant="outline" 
                              className={
                                insight.impact === 'critical' ? 'border-red-200 text-red-800' :
                                insight.impact === 'high' ? 'border-orange-200 text-orange-800' :
                                insight.impact === 'medium' ? 'border-yellow-200 text-yellow-800' :
                                'border-green-200 text-green-800'
                              }
                            >
                              {insight.impact}
                            </Badge>
                          </div>
                          <p className="text-xs text-gray-600 mt-1">{insight.recommendation}</p>
                          <div className="flex items-center justify-between mt-2 text-xs">
                            <span className="text-gray-500">Confidence: {insight.confidence.toFixed(0)}%</span>
                            <span className="text-gray-500">{insight.timeframe}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  <Button variant="outline" className="w-full">
                    <Eye className="h-4 w-4 mr-2" />
                    View Full Forecast
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="trends" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {trendAnalyses.map((trend) => (
              <motion.div
                key={trend.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="group"
              >
                <Card className="h-full hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <CardTitle className="flex items-center gap-2">
                        {getDirectionIcon(trend.direction)}
                        {trend.title}
                      </CardTitle>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="capitalize">
                          {trend.category.replace('_', ' ')}
                        </Badge>
                        <span className="text-sm text-gray-500">{trend.timeframe}</span>
                      </div>
                    </div>
                    <p className="text-sm text-gray-600 leading-relaxed">{trend.description}</p>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Trend Strength */}
                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm text-gray-600">Trend Strength</span>
                        <span className="font-medium">{trend.strength.toFixed(0)}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-blue-600 h-2 rounded-full" 
                          style={{ width: `${trend.strength}%` }}
                        ></div>
                      </div>
                    </div>

                    {/* Trend Data */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Trend Visualization</h4>
                      <ResponsiveContainer width="100%" height={150}>
                        <RechartsLineChart data={trend.dataPoints.slice(-8)}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="timestamp" tickFormatter={(value) => new Date(value).toLocaleDateString()} />
                          <YAxis />
                          <Tooltip labelFormatter={(value) => new Date(value).toLocaleDateString()} />
                          <Line type="monotone" dataKey="value" stroke="#3B82F6" strokeWidth={2} />
                        </RechartsLineChart>
                      </ResponsiveContainer>
                    </div>

                    {/* Driving Factors */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Key Drivers</h4>
                      <div className="space-y-1">
                        {trend.drivingFactors.slice(0, 3).map((factor, i) => (
                          <div key={i} className="flex items-center justify-between text-sm">
                            <span className="text-gray-600">{factor.factor}</span>
                            <div className="flex items-center gap-2">
                              <div className="w-12 bg-gray-200 rounded-full h-1">
                                <div 
                                  className="bg-green-600 h-1 rounded-full" 
                                  style={{ width: `${factor.contribution}%` }}
                                ></div>
                              </div>
                              <span className="font-medium">{factor.contribution.toFixed(0)}%</span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Future Projections */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Projections</h4>
                      <div className="grid grid-cols-3 gap-2 text-xs">
                        <div className="bg-blue-50 p-2 rounded text-center">
                          <p className="font-bold text-blue-600">{trend.futureProjection.shortTerm.value.toFixed(0)}</p>
                          <p className="text-gray-600">{trend.futureProjection.shortTerm.timeframe}</p>
                        </div>
                        <div className="bg-green-50 p-2 rounded text-center">
                          <p className="font-bold text-green-600">{trend.futureProjection.mediumTerm.value.toFixed(0)}</p>
                          <p className="text-gray-600">{trend.futureProjection.mediumTerm.timeframe}</p>
                        </div>
                        <div className="bg-purple-50 p-2 rounded text-center">
                          <p className="font-bold text-purple-600">{trend.futureProjection.longTerm.value.toFixed(0)}</p>
                          <p className="text-gray-600">{trend.futureProjection.longTerm.timeframe}</p>
                        </div>
                      </div>
                    </div>

                    {/* Business Impact */}
                    <div className="bg-green-50 p-3 rounded-lg">
                      <h4 className="font-medium text-gray-900 mb-1">Expected Impact</h4>
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <div>
                          <span className="text-gray-600">Revenue:</span>
                          <span className="ml-1 font-medium text-green-600">${(trend.businessImpact.revenue / 1000).toFixed(0)}K</span>
                        </div>
                        <div>
                          <span className="text-gray-600">Market Share:</span>
                          <span className="ml-1 font-medium text-green-600">+{trend.businessImpact.marketShare.toFixed(1)}%</span>
                        </div>
                      </div>
                    </div>

                    <Button variant="outline" size="sm" className="w-full">
                      <Eye className="h-4 w-4 mr-2" />
                      View Analysis Details
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="scenarios" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {predictiveScenarios.map((scenario) => (
              <Card key={scenario.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <CardTitle className="flex items-center gap-2">
                      <Globe className="h-5 w-5" />
                      {scenario.name}
                    </CardTitle>
                    <div className="flex items-center gap-2">
                      <Badge 
                        variant="outline"
                        className={
                          scenario.probability >= 0.7 ? 'border-red-200 text-red-800' :
                          scenario.probability >= 0.4 ? 'border-yellow-200 text-yellow-800' :
                          'border-green-200 text-green-800'
                        }
                      >
                        {(scenario.probability * 100).toFixed(0)}% probability
                      </Badge>
                      <span className="text-sm text-gray-500">{scenario.timeframe}</span>
                    </div>
                  </div>
                  <p className="text-sm text-gray-600 leading-relaxed">{scenario.description}</p>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Scenario Parameters */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Key Parameters</h4>
                    <div className="space-y-2">
                      {scenario.inputParameters.map((param, i) => (
                        <div key={i} className="flex items-center justify-between text-sm">
                          <span className="text-gray-600">{param.parameter}:</span>
                          <div className="flex items-center gap-2">
                            <span className="text-gray-500">{param.baseValue}</span>
                            <ArrowRight className="h-3 w-3 text-gray-400" />
                            <span className={`font-medium ${param.scenarioValue > param.baseValue ? 'text-green-600' : 'text-red-600'}`}>
                              {param.scenarioValue}
                            </span>
                            <span className={`text-xs ${param.impact > 0 ? 'text-green-600' : 'text-red-600'}`}>
                              ({param.impact > 0 ? '+' : ''}{param.impact}%)
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Business Outcomes */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Predicted Outcomes</h4>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div className={`p-2 rounded ${scenario.businessOutcomes.revenue.change >= 0 ? 'bg-green-50' : 'bg-red-50'}`}>
                        <p className="text-xs text-gray-600">Revenue Impact</p>
                        <p className={`font-bold ${scenario.businessOutcomes.revenue.change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {scenario.businessOutcomes.revenue.change >= 0 ? '+' : ''}{scenario.businessOutcomes.revenue.change}%
                        </p>
                      </div>
                      <div className={`p-2 rounded ${scenario.businessOutcomes.customers.change >= 0 ? 'bg-green-50' : 'bg-red-50'}`}>
                        <p className="text-xs text-gray-600">Customer Impact</p>
                        <p className={`font-bold ${scenario.businessOutcomes.customers.change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {scenario.businessOutcomes.customers.change >= 0 ? '+' : ''}{scenario.businessOutcomes.customers.change}%
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Risk Assessment */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Risk Assessment</h4>
                    <div className="space-y-1">
                      {Object.entries(scenario.riskAssessment).map(([risk, level]) => (
                        <div key={risk} className="flex items-center justify-between text-sm">
                          <span className="text-gray-600 capitalize">{risk.replace(/([A-Z])/g, ' $1').trim()}:</span>
                          <div className="flex items-center gap-2">
                            <div className="w-16 bg-gray-200 rounded-full h-2">
                              <div 
                                className={`h-2 rounded-full ${
                                  level >= 80 ? 'bg-red-600' :
                                  level >= 60 ? 'bg-orange-500' :
                                  level >= 40 ? 'bg-yellow-500' :
                                  'bg-green-600'
                                }`}
                                style={{ width: `${level}%` }}
                              ></div>
                            </div>
                            <span className="font-medium">{level}%</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Action Plan */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Key Actions</h4>
                    <div className="space-y-2">
                      <div>
                        <p className="text-xs font-medium text-green-700 mb-1">Opportunities:</p>
                        <div className="flex flex-wrap gap-1">
                          {scenario.actionPlan.opportunityActions.slice(0, 2).map((action, i) => (
                            <Badge key={i} variant="secondary" className="text-xs">{action}</Badge>
                          ))}
                        </div>
                      </div>
                      <div>
                        <p className="text-xs font-medium text-orange-700 mb-1">Contingencies:</p>
                        <div className="flex flex-wrap gap-1">
                          {scenario.actionPlan.contingencyPlans.slice(0, 2).map((plan, i) => (
                            <Badge key={i} variant="outline" className="text-xs">{plan}</Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>

                  <Button variant="outline" className="w-full">
                    <Eye className="h-4 w-4 mr-2" />
                    View Scenario Details
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default PredictiveAnalyticsEngine;