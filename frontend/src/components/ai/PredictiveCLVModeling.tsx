/**
 * Predictive Customer Lifetime Value Modeling
 * Advanced ML-powered CLV prediction with behavioral analysis and intervention strategies
 */

import React, { useState, useMemo, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { motion } from 'framer-motion';
import { 
  TrendingUp, 
  TrendingDown,
  Target,
  Users,
  DollarSign,
  Calendar,
  Clock,
  Brain,
  Zap,
  AlertTriangle,
  CheckCircle,
  Eye,
  Settings,
  Download,
  Upload,
  Share,
  Filter,
  Search,
  BarChart3,
  PieChart,
  LineChart,
  Activity,
  Star,
  Heart,
  ShoppingCart,
  CreditCard,
  Mail,
  Phone,
  MessageSquare,
  Gift,
  Award,
  Percent,
  Hash,
  ArrowUpRight,
  ArrowDownRight,
  Minus,
  Plus,
  RefreshCw,
  Play,
  Pause,
  Database,
  Cpu,
  Network,
  Globe
} from 'lucide-react';
import { 
  LineChart as RechartsLineChart, 
  Line, 
  AreaChart, 
  Area, 
  BarChart, 
  Bar, 
  ScatterChart,
  Scatter,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  Cell,
  PieChart as RechartsPieChart,
  Pie
} from 'recharts';

// Types
interface CLVPrediction {
  customerId: string;
  customerEmail: string;
  customerName: string;
  currentCLV: number;
  predictedCLV: number;
  clvGrowthPotential: number;
  confidenceScore: number;
  predictionTimeframe: string;
  riskFactors: string[];
  opportunityFactors: string[];
  behavioralSegment: 'high_value' | 'growth_potential' | 'stable' | 'at_risk' | 'declining';
  purchaseHistory: {
    totalOrders: number;
    avgOrderValue: number;
    frequency: number;
    recency: number;
    firstPurchase: Date;
    lastPurchase: Date;
  };
  engagementMetrics: {
    emailOpenRate: number;
    clickThroughRate: number;
    websiteVisits: number;
    timeOnSite: number;
    supportTickets: number;
  };
  predictiveFactors: {
    feature: string;
    importance: number;
    impact: 'positive' | 'negative' | 'neutral';
  }[];
  interventionRecommendations: string[];
  lastUpdated: Date;
}

interface CLVModel {
  id: string;
  name: string;
  type: 'rf_regression' | 'gradient_boosting' | 'neural_network' | 'ensemble';
  version: string;
  accuracy: number;
  rmse: number;
  mae: number;
  trainingDate: Date;
  status: 'active' | 'training' | 'deprecated' | 'testing';
  features: string[];
  hyperparameters: Record<string, any>;
  performanceMetrics: {
    r2Score: number;
    crossValidationScore: number;
    featureImportance: Record<string, number>;
    predictionDistribution: any[];
  };
  dataQuality: {
    sampleSize: number;
    completeness: number;
    outliers: number;
    biasScore: number;
  };
}

interface CLVSegment {
  id: string;
  name: string;
  description: string;
  criteria: {
    minCLV: number;
    maxCLV: number;
    behavioralTraits: string[];
    riskFactors: string[];
  };
  customerCount: number;
  avgCLV: number;
  growthRate: number;
  retentionRate: number;
  interventionStrategies: string[];
  expectedROI: number;
  color: string;
}

interface CLVIntervention {
  id: string;
  type: 'retention' | 'upsell' | 'cross_sell' | 'win_back' | 'loyalty';
  name: string;
  description: string;
  targetSegments: string[];
  trigger: {
    conditions: string[];
    timeframe: string;
    frequency: string;
  };
  actions: {
    channels: string[];
    content: string;
    incentives: string[];
    personalization: string[];
  };
  expectedImpact: {
    clvIncrease: number;
    retentionImprovement: number;
    revenueIncrease: number;
  };
  cost: number;
  roi: number;
  status: 'active' | 'paused' | 'testing' | 'draft';
  performance: {
    implemented: number;
    successful: number;
    failureRate: number;
    avgCLVImpact: number;
  };
}

interface PredictiveCLVModelingProps {
  onModelRetrain?: (modelId: string) => void;
  onInterventionTrigger?: (customerId: string, interventionId: string) => void;
  onSegmentUpdate?: (segmentId: string, updates: any) => void;
  className?: string;
}

// Mock data generation
const generateCLVPredictions = (): CLVPrediction[] => [
  {
    customerId: 'cust-001',
    customerEmail: '<EMAIL>',
    customerName: 'Sarah Johnson',
    currentCLV: 2450,
    predictedCLV: 3890,
    clvGrowthPotential: 58.8,
    confidenceScore: 87,
    predictionTimeframe: '24 months',
    riskFactors: ['Decreased purchase frequency', 'Lower email engagement'],
    opportunityFactors: ['High AOV trend', 'Premium category affinity', 'Mobile engagement increase'],
    behavioralSegment: 'growth_potential',
    purchaseHistory: {
      totalOrders: 24,
      avgOrderValue: 125.60,
      frequency: 2.1,
      recency: 12,
      firstPurchase: new Date('2023-03-15'),
      lastPurchase: new Date('2024-06-02')
    },
    engagementMetrics: {
      emailOpenRate: 34.5,
      clickThroughRate: 4.2,
      websiteVisits: 67,
      timeOnSite: 380,
      supportTickets: 2
    },
    predictiveFactors: [
      { feature: 'Average Order Value', importance: 0.23, impact: 'positive' },
      { feature: 'Purchase Frequency', importance: 0.19, impact: 'positive' },
      { feature: 'Email Engagement', importance: 0.15, impact: 'positive' },
      { feature: 'Category Preference', importance: 0.12, impact: 'positive' },
      { feature: 'Support Interactions', importance: 0.08, impact: 'neutral' }
    ],
    interventionRecommendations: [
      'Send personalized product recommendations for premium electronics',
      'Offer loyalty program enrollment with immediate benefits',
      'Create mobile-optimized shopping experience',
      'Implement abandoned cart recovery sequence'
    ],
    lastUpdated: new Date(Date.now() - 2 * 60 * 60 * 1000)
  },
  {
    customerId: 'cust-002',
    customerEmail: '<EMAIL>',
    customerName: 'Michael Chen',
    currentCLV: 890,
    predictedCLV: 2340,
    clvGrowthPotential: 163.0,
    confidenceScore: 92,
    predictionTimeframe: '18 months',
    riskFactors: ['New customer', 'Single category purchases'],
    opportunityFactors: ['High engagement rate', 'B2B purchasing pattern', 'Rapid purchase acceleration'],
    behavioralSegment: 'high_value',
    purchaseHistory: {
      totalOrders: 6,
      avgOrderValue: 215.30,
      frequency: 3.2,
      recency: 5,
      firstPurchase: new Date('2024-02-10'),
      lastPurchase: new Date('2024-06-20')
    },
    engagementMetrics: {
      emailOpenRate: 67.8,
      clickThroughRate: 8.9,
      websiteVisits: 43,
      timeOnSite: 520,
      supportTickets: 1
    },
    predictiveFactors: [
      { feature: 'Engagement Rate', importance: 0.28, impact: 'positive' },
      { feature: 'Purchase Acceleration', importance: 0.22, impact: 'positive' },
      { feature: 'B2B Indicators', importance: 0.18, impact: 'positive' },
      { feature: 'Category Expansion', importance: 0.14, impact: 'positive' },
      { feature: 'Customer Tenure', importance: 0.09, impact: 'neutral' }
    ],
    interventionRecommendations: [
      'Introduce to B2B bulk purchase options',
      'Provide dedicated account management',
      'Offer volume discounts and corporate pricing',
      'Create cross-category product discovery flows'
    ],
    lastUpdated: new Date(Date.now() - 1 * 60 * 60 * 1000)
  },
  {
    customerId: 'cust-003',
    customerEmail: '<EMAIL>',
    customerName: 'Emily Davis',
    currentCLV: 3420,
    predictedCLV: 2890,
    clvGrowthPotential: -15.5,
    confidenceScore: 78,
    predictionTimeframe: '12 months',
    riskFactors: ['Declining purchase frequency', 'Increased support tickets', 'Price sensitivity'],
    opportunityFactors: ['High historical value', 'Brand loyalty', 'Premium product affinity'],
    behavioralSegment: 'at_risk',
    purchaseHistory: {
      totalOrders: 38,
      avgOrderValue: 156.80,
      frequency: 1.2,
      recency: 45,
      firstPurchase: new Date('2022-08-20'),
      lastPurchase: new Date('2024-05-15')
    },
    engagementMetrics: {
      emailOpenRate: 23.1,
      clickThroughRate: 2.1,
      websiteVisits: 34,
      timeOnSite: 290,
      supportTickets: 7
    },
    predictiveFactors: [
      { feature: 'Purchase Recency', importance: 0.31, impact: 'negative' },
      { feature: 'Support Ticket Volume', importance: 0.24, impact: 'negative' },
      { feature: 'Email Engagement Decline', importance: 0.18, impact: 'negative' },
      { feature: 'Historical CLV', importance: 0.15, impact: 'positive' },
      { feature: 'Brand Interaction', importance: 0.07, impact: 'positive' }
    ],
    interventionRecommendations: [
      'Immediate retention campaign with exclusive offers',
      'Proactive customer service outreach',
      'Win-back email sequence with personalized content',
      'VIP customer experience upgrade'
    ],
    lastUpdated: new Date(Date.now() - 30 * 60 * 1000)
  }
];

const generateCLVModels = (): CLVModel[] => [
  {
    id: 'model-clv-main',
    name: 'CLV Prediction - Main Model',
    type: 'ensemble',
    version: '2.3.1',
    accuracy: 87.3,
    rmse: 234.5,
    mae: 187.2,
    trainingDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
    status: 'active',
    features: [
      'purchase_frequency',
      'avg_order_value',
      'recency',
      'tenure',
      'email_engagement',
      'category_affinity',
      'support_interactions',
      'payment_method',
      'geographic_location',
      'seasonal_patterns'
    ],
    hyperparameters: {
      n_estimators: 100,
      max_depth: 12,
      learning_rate: 0.1,
      subsample: 0.8
    },
    performanceMetrics: {
      r2Score: 0.834,
      crossValidationScore: 0.821,
      featureImportance: {
        'purchase_frequency': 0.18,
        'avg_order_value': 0.16,
        'recency': 0.14,
        'email_engagement': 0.12,
        'tenure': 0.11,
        'category_affinity': 0.09,
        'support_interactions': 0.08,
        'payment_method': 0.06,
        'geographic_location': 0.04,
        'seasonal_patterns': 0.02
      },
      predictionDistribution: []
    },
    dataQuality: {
      sampleSize: 125000,
      completeness: 94.2,
      outliers: 1247,
      biasScore: 0.23
    }
  },
  {
    id: 'model-clv-segment',
    name: 'Segment-Specific CLV Model',
    type: 'neural_network',
    version: '1.8.2',
    accuracy: 82.7,
    rmse: 289.1,
    mae: 203.8,
    trainingDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
    status: 'testing',
    features: [
      'segment_behavior',
      'cross_category_purchases',
      'loyalty_program_engagement',
      'social_media_interaction',
      'device_preference',
      'time_of_purchase_patterns'
    ],
    hyperparameters: {
      hidden_layers: 3,
      neurons_per_layer: 128,
      dropout_rate: 0.2,
      activation: 'relu',
      optimizer: 'adam'
    },
    performanceMetrics: {
      r2Score: 0.787,
      crossValidationScore: 0.772,
      featureImportance: {
        'segment_behavior': 0.22,
        'cross_category_purchases': 0.19,
        'loyalty_program_engagement': 0.17,
        'social_media_interaction': 0.15,
        'device_preference': 0.14,
        'time_of_purchase_patterns': 0.13
      },
      predictionDistribution: []
    },
    dataQuality: {
      sampleSize: 89000,
      completeness: 91.7,
      outliers: 892,
      biasScore: 0.18
    }
  }
];

const generateCLVSegments = (): CLVSegment[] => [
  {
    id: 'segment-champions',
    name: 'Champions',
    description: 'Highest value customers with strong engagement and loyalty',
    criteria: {
      minCLV: 2500,
      maxCLV: 10000,
      behavioralTraits: ['High frequency', 'High AOV', 'Strong engagement'],
      riskFactors: ['Over-dependency risk', 'Expectation management']
    },
    customerCount: 1247,
    avgCLV: 4350,
    growthRate: 12.3,
    retentionRate: 94.2,
    interventionStrategies: [
      'VIP treatment and exclusive access',
      'Personal account management',
      'Early product previews',
      'Loyalty program top tier benefits'
    ],
    expectedROI: 340,
    color: '#22c55e'
  },
  {
    id: 'segment-loyal',
    name: 'Loyal Customers',
    description: 'Consistent customers with regular purchase patterns',
    criteria: {
      minCLV: 1000,
      maxCLV: 2500,
      behavioralTraits: ['Regular purchases', 'Brand loyalty', 'Stable engagement'],
      riskFactors: ['Competitor attraction', 'Price sensitivity']
    },
    customerCount: 3456,
    avgCLV: 1650,
    growthRate: 8.7,
    retentionRate: 87.5,
    interventionStrategies: [
      'Loyalty program engagement',
      'Cross-sell campaigns',
      'Seasonal promotions',
      'Product recommendation optimization'
    ],
    expectedROI: 180,
    color: '#3b82f6'
  },
  {
    id: 'segment-potential',
    name: 'Potential Loyalists',
    description: 'New or growing customers with high potential value',
    criteria: {
      minCLV: 500,
      maxCLV: 1500,
      behavioralTraits: ['Growing engagement', 'Category exploration', 'Learning behavior'],
      riskFactors: ['Early churn risk', 'Competitive switching']
    },
    customerCount: 5892,
    avgCLV: 890,
    growthRate: 23.4,
    retentionRate: 73.8,
    interventionStrategies: [
      'Onboarding optimization',
      'Educational content',
      'Product discovery assistance',
      'Incentivized trial periods'
    ],
    expectedROI: 220,
    color: '#f59e0b'
  },
  {
    id: 'segment-atrisk',
    name: 'At Risk',
    description: 'Previously valuable customers showing decline signals',
    criteria: {
      minCLV: 800,
      maxCLV: 3000,
      behavioralTraits: ['Declining frequency', 'Reduced engagement', 'Support issues'],
      riskFactors: ['Imminent churn', 'Negative experience', 'Competitor migration']
    },
    customerCount: 1834,
    avgCLV: 1420,
    growthRate: -12.1,
    retentionRate: 56.3,
    interventionStrategies: [
      'Immediate retention campaigns',
      'Proactive customer service',
      'Win-back offers',
      'Personalized re-engagement'
    ],
    expectedROI: 150,
    color: '#ef4444'
  }
];

const generateCLVInterventions = (): CLVIntervention[] => [
  {
    id: 'intervention-vip',
    type: 'retention',
    name: 'VIP Experience Program',
    description: 'Exclusive program for high-value customers with personalized service',
    targetSegments: ['segment-champions'],
    trigger: {
      conditions: ['CLV > $2500', 'Purchase frequency > 2/month'],
      timeframe: 'Immediate',
      frequency: 'Ongoing'
    },
    actions: {
      channels: ['Email', 'Phone', 'In-app'],
      content: 'Personalized offers, early access, exclusive content',
      incentives: ['Free shipping', 'Priority support', 'Exclusive discounts'],
      personalization: ['Purchase history', 'Preferences', 'Behavioral data']
    },
    expectedImpact: {
      clvIncrease: 25.0,
      retentionImprovement: 8.5,
      revenueIncrease: 340000
    },
    cost: 85000,
    roi: 400,
    status: 'active',
    performance: {
      implemented: 1247,
      successful: 1156,
      failureRate: 7.3,
      avgCLVImpact: 23.4
    }
  },
  {
    id: 'intervention-winback',
    type: 'win_back',
    name: 'At-Risk Customer Recovery',
    description: 'Targeted campaign to re-engage declining customers',
    targetSegments: ['segment-atrisk'],
    trigger: {
      conditions: ['No purchase in 30 days', 'Declining engagement score'],
      timeframe: '24 hours',
      frequency: 'Once per quarter'
    },
    actions: {
      channels: ['Email', 'SMS', 'Push notification'],
      content: 'Personalized win-back offers, product recommendations',
      incentives: ['15% discount', 'Free shipping', 'Loyalty points'],
      personalization: ['Last viewed products', 'Purchase history', 'Preferred categories']
    },
    expectedImpact: {
      clvIncrease: 35.0,
      retentionImprovement: 22.0,
      revenueIncrease: 180000
    },
    cost: 45000,
    roi: 280,
    status: 'active',
    performance: {
      implemented: 1834,
      successful: 891,
      failureRate: 51.4,
      avgCLVImpact: 18.7
    }
  }
];

// CLV prediction card component
const CLVPredictionCard: React.FC<{
  prediction: CLVPrediction;
  onViewDetails?: () => void;
  onTriggerIntervention?: () => void;
}> = ({ prediction, onViewDetails, onTriggerIntervention }) => {
  const getSegmentColor = (segment: string) => {
    switch (segment) {
      case 'high_value': return 'bg-green-100 text-green-800';
      case 'growth_potential': return 'bg-blue-100 text-blue-800';
      case 'stable': return 'bg-gray-100 text-gray-800';
      case 'at_risk': return 'bg-yellow-100 text-yellow-800';
      case 'declining': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getGrowthIcon = (growth: number) => {
    if (growth > 0) return <ArrowUpRight className="w-4 h-4 text-green-600" />;
    if (growth < 0) return <ArrowDownRight className="w-4 h-4 text-red-600" />;
    return <Minus className="w-4 h-4 text-gray-600" />;
  };

  return (
    <Card className="hover:shadow-lg transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div>
            <CardTitle className="text-lg">{prediction.customerName}</CardTitle>
            <p className="text-sm text-gray-600">{prediction.customerEmail}</p>
          </div>
          <div className="flex flex-col space-y-1">
            <Badge className={getSegmentColor(prediction.behavioralSegment)}>
              {prediction.behavioralSegment.replace('_', ' ')}
            </Badge>
            <Badge variant="outline">
              {prediction.confidenceScore}% confidence
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center p-3 bg-blue-50 rounded-lg">
              <div className="text-sm text-gray-600">Current CLV</div>
              <div className="text-2xl font-bold text-blue-600">
                ${prediction.currentCLV.toLocaleString()}
              </div>
            </div>
            <div className="text-center p-3 bg-green-50 rounded-lg">
              <div className="text-sm text-gray-600">Predicted CLV</div>
              <div className="text-2xl font-bold text-green-600">
                ${prediction.predictedCLV.toLocaleString()}
              </div>
            </div>
          </div>

          <div className="flex items-center justify-center space-x-2 p-3 bg-gray-50 rounded-lg">
            {getGrowthIcon(prediction.clvGrowthPotential)}
            <span className="text-lg font-semibold">
              {prediction.clvGrowthPotential > 0 ? '+' : ''}{prediction.clvGrowthPotential.toFixed(1)}%
            </span>
            <span className="text-sm text-gray-600">growth potential</span>
          </div>

          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Total Orders:</span>
              <span className="ml-2 font-medium">{prediction.purchaseHistory.totalOrders}</span>
            </div>
            <div>
              <span className="text-gray-600">Avg Order Value:</span>
              <span className="ml-2 font-medium">${prediction.purchaseHistory.avgOrderValue.toFixed(2)}</span>
            </div>
            <div>
              <span className="text-gray-600">Frequency:</span>
              <span className="ml-2 font-medium">{prediction.purchaseHistory.frequency}/month</span>
            </div>
            <div>
              <span className="text-gray-600">Last Purchase:</span>
              <span className="ml-2 font-medium">{prediction.purchaseHistory.recency} days ago</span>
            </div>
          </div>

          <div>
            <h4 className="font-medium text-sm mb-2">Top Predictive Factors:</h4>
            <div className="space-y-1">
              {prediction.predictiveFactors.slice(0, 3).map((factor, index) => (
                <div key={index} className="flex items-center justify-between text-xs">
                  <span>{factor.feature}</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-12 bg-gray-200 rounded-full h-1">
                      <div 
                        className="bg-blue-500 h-1 rounded-full" 
                        style={{ width: `${factor.importance * 100}%` }}
                      />
                    </div>
                    <span className="font-medium">{(factor.importance * 100).toFixed(0)}%</span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {prediction.riskFactors.length > 0 && (
            <div>
              <h4 className="font-medium text-sm mb-2 text-red-600">Risk Factors:</h4>
              <ul className="space-y-1">
                {prediction.riskFactors.slice(0, 2).map((risk, index) => (
                  <li key={index} className="text-xs text-red-600 flex items-start space-x-2">
                    <AlertTriangle className="w-3 h-3 mt-0.5 flex-shrink-0" />
                    <span>{risk}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          <div className="flex justify-between space-x-2 pt-3 border-t">
            <Button variant="outline" size="sm" onClick={onViewDetails}>
              <Eye className="w-4 h-4 mr-1" />
              Details
            </Button>
            <Button size="sm" onClick={onTriggerIntervention}>
              <Zap className="w-4 h-4 mr-1" />
              Intervene
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Model performance card
const ModelPerformanceCard: React.FC<{
  model: CLVModel;
  onRetrain?: () => void;
}> = ({ model, onRetrain }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'training': return 'bg-blue-100 text-blue-800';
      case 'testing': return 'bg-yellow-100 text-yellow-800';
      case 'deprecated': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getAccuracyColor = (accuracy: number) => {
    if (accuracy >= 85) return 'text-green-600';
    if (accuracy >= 75) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg">{model.name}</CardTitle>
            <p className="text-sm text-gray-600">Version {model.version} • {model.type.replace('_', ' ')}</p>
          </div>
          <Badge className={getStatusColor(model.status)}>
            {model.status}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-sm text-gray-600">Accuracy</div>
              <div className={`text-2xl font-bold ${getAccuracyColor(model.accuracy)}`}>
                {model.accuracy.toFixed(1)}%
              </div>
            </div>
            <div className="text-center">
              <div className="text-sm text-gray-600">RMSE</div>
              <div className="text-lg font-medium">{model.rmse.toFixed(1)}</div>
            </div>
            <div className="text-center">
              <div className="text-sm text-gray-600">MAE</div>
              <div className="text-lg font-medium">{model.mae.toFixed(1)}</div>
            </div>
          </div>

          <div>
            <h4 className="font-medium text-sm mb-2">Performance Metrics</h4>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div>R² Score: <span className="font-medium">{model.performanceMetrics.r2Score.toFixed(3)}</span></div>
              <div>CV Score: <span className="font-medium">{model.performanceMetrics.crossValidationScore.toFixed(3)}</span></div>
            </div>
          </div>

          <div>
            <h4 className="font-medium text-sm mb-2">Top Features</h4>
            <div className="space-y-1">
              {Object.entries(model.performanceMetrics.featureImportance)
                .sort(([,a], [,b]) => b - a)
                .slice(0, 4)
                .map(([feature, importance]) => (
                  <div key={feature} className="flex items-center justify-between text-xs">
                    <span className="capitalize">{feature.replace('_', ' ')}</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-12 bg-gray-200 rounded-full h-1">
                        <div 
                          className="bg-purple-500 h-1 rounded-full" 
                          style={{ width: `${importance * 100}%` }}
                        />
                      </div>
                      <span className="font-medium">{(importance * 100).toFixed(0)}%</span>
                    </div>
                  </div>
                ))}
            </div>
          </div>

          <div>
            <h4 className="font-medium text-sm mb-2">Data Quality</h4>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div>Sample Size: <span className="font-medium">{model.dataQuality.sampleSize.toLocaleString()}</span></div>
              <div>Completeness: <span className="font-medium">{model.dataQuality.completeness.toFixed(1)}%</span></div>
              <div>Outliers: <span className="font-medium">{model.dataQuality.outliers}</span></div>
              <div>Bias Score: <span className="font-medium">{model.dataQuality.biasScore.toFixed(2)}</span></div>
            </div>
          </div>

          <div className="flex justify-between items-center pt-3 border-t">
            <span className="text-xs text-gray-500">
              Trained: {model.trainingDate.toLocaleDateString()}
            </span>
            <div className="flex space-x-2">
              <Button variant="outline" size="sm">
                <Eye className="w-4 h-4" />
              </Button>
              <Button variant="outline" size="sm" onClick={onRetrain}>
                <RefreshCw className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Segment overview card
const SegmentCard: React.FC<{
  segment: CLVSegment;
  onUpdate?: () => void;
}> = ({ segment, onUpdate }) => {
  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div 
              className="w-4 h-4 rounded-full" 
              style={{ backgroundColor: segment.color }}
            />
            <div>
              <CardTitle className="text-lg">{segment.name}</CardTitle>
              <p className="text-sm text-gray-600">{segment.description}</p>
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center p-3 bg-blue-50 rounded-lg">
              <div className="text-sm text-gray-600">Customers</div>
              <div className="text-xl font-bold text-blue-600">
                {segment.customerCount.toLocaleString()}
              </div>
            </div>
            <div className="text-center p-3 bg-green-50 rounded-lg">
              <div className="text-sm text-gray-600">Avg CLV</div>
              <div className="text-xl font-bold text-green-600">
                ${segment.avgCLV.toLocaleString()}
              </div>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Growth Rate:</span>
              <span className={`ml-2 font-medium ${
                segment.growthRate > 0 ? 'text-green-600' : 'text-red-600'
              }`}>
                {segment.growthRate > 0 ? '+' : ''}{segment.growthRate.toFixed(1)}%
              </span>
            </div>
            <div>
              <span className="text-gray-600">Retention:</span>
              <span className="ml-2 font-medium">{segment.retentionRate.toFixed(1)}%</span>
            </div>
            <div>
              <span className="text-gray-600">Expected ROI:</span>
              <span className="ml-2 font-medium text-green-600">{segment.expectedROI}%</span>
            </div>
          </div>

          <div>
            <h4 className="font-medium text-sm mb-2">Intervention Strategies:</h4>
            <ul className="space-y-1">
              {segment.interventionStrategies.slice(0, 3).map((strategy, index) => (
                <li key={index} className="text-xs text-gray-600 flex items-start space-x-2">
                  <Target className="w-3 h-3 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span>{strategy}</span>
                </li>
              ))}
            </ul>
          </div>

          <div className="flex justify-end space-x-2 pt-3 border-t">
            <Button variant="outline" size="sm">
              <Eye className="w-4 h-4 mr-1" />
              View
            </Button>
            <Button variant="outline" size="sm" onClick={onUpdate}>
              <Settings className="w-4 h-4 mr-1" />
              Configure
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Main component
const PredictiveCLVModeling: React.FC<PredictiveCLVModelingProps> = ({
  onModelRetrain,
  onInterventionTrigger,
  onSegmentUpdate,
  className = ''
}) => {
  const [activeTab, setActiveTab] = useState('predictions');
  const [searchTerm, setSearchTerm] = useState('');
  const [segmentFilter, setSegmentFilter] = useState('all');
  
  const predictions = useMemo(() => generateCLVPredictions(), []);
  const models = useMemo(() => generateCLVModels(), []);
  const segments = useMemo(() => generateCLVSegments(), []);
  const interventions = useMemo(() => generateCLVInterventions(), []);

  const filteredPredictions = useMemo(() => {
    let filtered = predictions;
    
    if (searchTerm) {
      filtered = filtered.filter(prediction =>
        prediction.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        prediction.customerEmail.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    
    if (segmentFilter !== 'all') {
      filtered = filtered.filter(prediction => prediction.behavioralSegment === segmentFilter);
    }
    
    return filtered.sort((a, b) => b.clvGrowthPotential - a.clvGrowthPotential);
  }, [predictions, searchTerm, segmentFilter]);

  const handleModelRetrain = useCallback((modelId: string) => {
    onModelRetrain?.(modelId);
    console.log('Retraining model:', modelId);
  }, [onModelRetrain]);

  const handleInterventionTrigger = useCallback((customerId: string) => {
    onInterventionTrigger?.(customerId, 'auto-selected');
    console.log('Triggering intervention for customer:', customerId);
  }, [onInterventionTrigger]);

  const stats = useMemo(() => {
    const totalPredictions = predictions.length;
    const avgPredictedCLV = predictions.reduce((sum, p) => sum + p.predictedCLV, 0) / predictions.length;
    const avgGrowthPotential = predictions.reduce((sum, p) => sum + p.clvGrowthPotential, 0) / predictions.length;
    const highRiskCustomers = predictions.filter(p => p.behavioralSegment === 'at_risk').length;
    
    return { totalPredictions, avgPredictedCLV, avgGrowthPotential, highRiskCustomers };
  }, [predictions]);

  return (
    <div className={`space-y-6 ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Target className="w-6 h-6 text-green-600" />
          <h2 className="text-2xl font-bold">Predictive CLV Modeling</h2>
          <Badge className="bg-green-100 text-green-800">Phase 10</Badge>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            Export Predictions
          </Button>
          <Button variant="outline">
            <Settings className="w-4 h-4 mr-2" />
            Model Settings
          </Button>
        </div>
      </div>

      {/* CLV Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Predictions</p>
                <p className="text-3xl font-bold text-blue-600">{stats.totalPredictions}</p>
                <p className="text-xs text-blue-600 mt-1">Active customers</p>
              </div>
              <Users className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Avg Predicted CLV</p>
                <p className="text-3xl font-bold text-green-600">${(stats.avgPredictedCLV / 1000).toFixed(1)}K</p>
                <p className="text-xs text-green-600 mt-1">24-month horizon</p>
              </div>
              <DollarSign className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Growth Potential</p>
                <p className="text-3xl font-bold text-purple-600">+{stats.avgGrowthPotential.toFixed(1)}%</p>
                <p className="text-xs text-purple-600 mt-1">Average opportunity</p>
              </div>
              <TrendingUp className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">High Risk Customers</p>
                <p className="text-3xl font-bold text-red-600">{stats.highRiskCustomers}</p>
                <p className="text-xs text-red-600 mt-1">Require intervention</p>
              </div>
              <AlertTriangle className="w-8 h-8 text-red-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="predictions">CLV Predictions</TabsTrigger>
          <TabsTrigger value="models">Model Performance</TabsTrigger>
          <TabsTrigger value="segments">Customer Segments</TabsTrigger>
          <TabsTrigger value="interventions">Interventions</TabsTrigger>
        </TabsList>

        <TabsContent value="predictions" className="space-y-6">
          <div className="flex items-center space-x-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search customers..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <select
              value={segmentFilter}
              onChange={(e) => setSegmentFilter(e.target.value)}
              className="p-2 border rounded-md text-sm"
            >
              <option value="all">All Segments</option>
              <option value="high_value">High Value</option>
              <option value="growth_potential">Growth Potential</option>
              <option value="stable">Stable</option>
              <option value="at_risk">At Risk</option>
              <option value="declining">Declining</option>
            </select>
            <Button variant="outline">
              <Filter className="w-4 h-4 mr-2" />
              Filter
            </Button>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {filteredPredictions.map((prediction, index) => (
              <motion.div
                key={prediction.customerId}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <CLVPredictionCard
                  prediction={prediction}
                  onTriggerIntervention={() => handleInterventionTrigger(prediction.customerId)}
                />
              </motion.div>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="models" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {models.map((model, index) => (
              <motion.div
                key={model.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <ModelPerformanceCard
                  model={model}
                  onRetrain={() => handleModelRetrain(model.id)}
                />
              </motion.div>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="segments" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {segments.map((segment, index) => (
              <motion.div
                key={segment.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <SegmentCard
                  segment={segment}
                  onUpdate={() => onSegmentUpdate?.(segment.id, {})}
                />
              </motion.div>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="interventions" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {interventions.map((intervention, index) => (
              <motion.div
                key={intervention.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Card className="hover:shadow-md transition-shadow">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle className="text-lg">{intervention.name}</CardTitle>
                        <p className="text-sm text-gray-600 capitalize">{intervention.type.replace('_', ' ')}</p>
                      </div>
                      <Badge className={
                        intervention.status === 'active' ? 'bg-green-100 text-green-800' :
                        intervention.status === 'testing' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-gray-100 text-gray-800'
                      }>
                        {intervention.status}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <p className="text-sm text-gray-700">{intervention.description}</p>
                      
                      <div className="grid grid-cols-3 gap-2 text-xs">
                        <div className="text-center p-2 bg-green-50 rounded">
                          <div className="font-bold text-green-600">+{intervention.expectedImpact.clvIncrease}%</div>
                          <div className="text-green-600">CLV Increase</div>
                        </div>
                        <div className="text-center p-2 bg-blue-50 rounded">
                          <div className="font-bold text-blue-600">{intervention.roi}%</div>
                          <div className="text-blue-600">ROI</div>
                        </div>
                        <div className="text-center p-2 bg-purple-50 rounded">
                          <div className="font-bold text-purple-600">{intervention.performance.successful}</div>
                          <div className="text-purple-600">Successful</div>
                        </div>
                      </div>

                      <div className="flex justify-end space-x-2 pt-3 border-t">
                        <Button variant="outline" size="sm">
                          <Eye className="w-4 h-4 mr-1" />
                          Details
                        </Button>
                        <Button size="sm">
                          <Play className="w-4 h-4 mr-1" />
                          Configure
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default PredictiveCLVModeling;
export { type CLVPrediction, type CLVModel, type CLVSegment, type CLVIntervention };