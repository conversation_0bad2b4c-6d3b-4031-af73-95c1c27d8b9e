/**
 * Automated Anomaly Detection System
 * Real-time anomaly detection with AI-powered pattern recognition and automated alerting
 */

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  AlertTriangle,
  Shield,
  Eye,
  Zap,
  Brain,
  Activity,
  TrendingUp,
  TrendingDown,
  Bell,
  BellRing,
  Clock,
  Target,
  CheckCircle,
  XCircle,
  AlertCircle,
  Info,
  Settings,
  RefreshCw,
  Search,
  Filter,
  Download,
  Upload,
  Share,
  Play,
  Pause,
  Stop,
  BarChart3,
  LineChart,
  PieChart,
  Gauge,
  Network,
  Database,
  Server,
  Cloud,
  Cpu,
  Monitor,
  Smartphone,
  Tablet,
  Globe,
  MapPin,
  Users,
  ShoppingCart,
  DollarSign,
  Package,
  Calendar,
  Navigation,
  Compass,
  Route,
  Layers,
  Focus,
  Crosshair,
  Scan,
  Radar as RadarIcon,
  Wifi,
  Signal,
  Battery,
  Power,
  Lock,
  Unlock,
  Key,
  Link2,
  ExternalLink,
  ArrowUp,
  ArrowDown,
  ArrowRight,
  ArrowLeft,
  ChevronUp,
  ChevronDown,
  Plus,
  Minus,
  Edit,
  Trash,
  MoreHorizontal,
  Star,
  Heart,
  ThumbsUp,
  MessageCircle,
  Flag,
  Award,
  Trophy,
  Crown,
  Gem,
  Sparkles,
  Wand2,
  Lightbulb,
  Rocket,
  BookOpen,
  FileText,
  File,
  Folder,
  Archive,
  Save,
  Mail,
  Phone,
  Volume2,
  Mic,
  Speaker,
  Headphones,
  Watch,
  Laptop
} from 'lucide-react';
import { LineChart as RechartsLineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area, BarChart as RechartsBarChart, Bar, ComposedChart, ScatterChart, Scatter, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar, Cell } from 'recharts';

// TypeScript interfaces for anomaly detection
interface AnomalyDetectionModel {
  id: string;
  name: string;
  type: 'statistical' | 'ml_based' | 'deep_learning' | 'ensemble' | 'time_series' | 'clustering' | 'rule_based';
  algorithm: 'isolation_forest' | 'one_class_svm' | 'lstm_autoencoder' | 'dbscan' | 'statistical_threshold' | 'ensemble_voting' | 'prophet_anomaly';
  description: string;
  status: 'active' | 'training' | 'testing' | 'inactive' | 'error';
  dataSource: string;
  monitoredMetrics: string[];
  detectionWindow: string;
  sensitivity: 'low' | 'medium' | 'high' | 'adaptive';
  falsePositiveRate: number;
  truePositiveRate: number;
  accuracy: number;
  latency: number; // milliseconds
  throughput: number; // records per second
  lastUpdated: string;
  configuration: {
    thresholds: { metric: string; min: number; max: number; deviation: number }[];
    features: string[];
    trainingPeriod: string;
    retrainingFrequency: string;
    alertingEnabled: boolean;
    autoRemediation: boolean;
  };
  performance: {
    anomaliesDetected: number;
    alertsTriggered: number;
    falseAlerts: number;
    missedAnomalies: number;
    averageDetectionTime: number;
    uptime: number;
  };
  businessImpact: {
    issuesPrevented: number;
    downtime_avoided: number;
    revenue_protected: number;
    costs_saved: number;
  };
}

interface DetectedAnomaly {
  id: string;
  modelId: string;
  timestamp: string;
  severity: 'critical' | 'high' | 'medium' | 'low' | 'info';
  status: 'new' | 'acknowledged' | 'investigating' | 'resolved' | 'false_positive' | 'suppressed';
  category: 'performance' | 'security' | 'business' | 'system' | 'user_behavior' | 'data_quality' | 'operational';
  title: string;
  description: string;
  affectedMetrics: {
    metric: string;
    currentValue: number;
    expectedValue: number;
    deviation: number;
    unit: string;
  }[];
  context: {
    source: string;
    environment: string;
    component: string;
    tags: string[];
    relatedEntities: string[];
  };
  detectionMethod: {
    algorithm: string;
    confidence: number;
    threshold: number;
    historicalComparison: {
      baseline: number;
      variance: number;
      seasonality: number;
    };
  };
  timeline: {
    detectedAt: string;
    firstOccurrence: string;
    lastOccurrence: string;
    duration: number;
    frequency: number;
  };
  impact: {
    usersAffected: number;
    systemsAffected: string[];
    businessProcesses: string[];
    estimatedLoss: number;
    urgency: number;
  };
  rootCauseAnalysis: {
    possibleCauses: { cause: string; probability: number; evidence: string[] }[];
    correlatedEvents: { event: string; correlation: number; timestamp: string }[];
    similarIncidents: { id: string; similarity: number; resolution: string }[];
  };
  recommendations: {
    immediate: string[];
    shortTerm: string[];
    longTerm: string[];
    preventive: string[];
  };
  resolution: {
    assignedTo: string;
    actions: { action: string; timestamp: string; user: string; result: string }[];
    resolvedAt?: string;
    resolution?: string;
    preventionMeasures?: string[];
  };
  alerting: {
    channels: string[];
    recipients: string[];
    escalationLevel: number;
    suppressionRules: string[];
  };
}

interface AnomalyPattern {
  id: string;
  name: string;
  description: string;
  patternType: 'seasonal' | 'trend' | 'cyclic' | 'point' | 'collective' | 'contextual' | 'drift';
  frequency: number;
  strength: number;
  timeframe: string;
  characteristics: {
    duration: number;
    magnitude: number;
    scope: string[];
    triggers: string[];
    indicators: string[];
  };
  businessContext: {
    impact: 'revenue' | 'operations' | 'customer' | 'security' | 'compliance' | 'performance';
    severity: number;
    affected_areas: string[];
    stakeholders: string[];
  };
  detectionRules: {
    conditions: string[];
    thresholds: Record<string, number>;
    correlations: { metric1: string; metric2: string; correlation: number }[];
    dependencies: string[];
  };
  historicalOccurrences: {
    timestamp: string;
    severity: number;
    duration: number;
    impact: number;
    resolution: string;
  }[];
  predictiveIndicators: {
    leadingMetrics: string[];
    earlyWarningTime: number;
    predictionAccuracy: number;
    preventionOpportunities: string[];
  };
  learningInsights: {
    evolutionTrend: string;
    adaptations: string[];
    improvements: string[];
    futureRisk: number;
  };
}

interface AlertConfiguration {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
  modelIds: string[];
  conditions: {
    severityLevels: string[];
    categories: string[];
    metrics: string[];
    thresholds: Record<string, any>;
    timeWindows: string[];
  };
  channels: {
    email: {
      enabled: boolean;
      recipients: string[];
      template: string;
      aggregation: 'immediate' | 'batch' | 'digest';
    };
    slack: {
      enabled: boolean;
      webhook: string;
      channels: string[];
      mentions: string[];
    };
    sms: {
      enabled: boolean;
      numbers: string[];
      urgencyLevels: string[];
    };
    webhook: {
      enabled: boolean;
      endpoints: { url: string; method: string; headers: Record<string, string> }[];
      retryPolicy: { attempts: number; delay: number };
    };
    dashboard: {
      enabled: boolean;
      notifications: boolean;
      popups: boolean;
      badges: boolean;
    };
  };
  escalation: {
    levels: {
      level: number;
      delay: number;
      recipients: string[];
      actions: string[];
    }[];
    maxEscalations: number;
    escalationCriteria: string[];
  };
  suppression: {
    rules: {
      condition: string;
      duration: number;
      reason: string;
    }[];
    quietHours: { start: string; end: string; timezone: string }[];
    dependencies: string[];
  };
  automation: {
    autoAcknowledge: boolean;
    autoInvestigate: boolean;
    autoRemediate: boolean;
    workflowTriggers: string[];
    integrations: string[];
  };
}

interface SystemHealth {
  timestamp: string;
  overallHealth: number;
  componentHealth: {
    component: string;
    status: 'healthy' | 'warning' | 'critical' | 'unknown';
    health: number;
    metrics: { metric: string; value: number; threshold: number; status: string }[];
    lastCheck: string;
  }[];
  detectionCapacity: {
    modelsActive: number;
    throughput: number;
    latency: number;
    accuracy: number;
    coverage: number;
  };
  alertingSystem: {
    alertsActive: number;
    notificationsSent: number;
    escalationsTriggered: number;
    suppressedAlerts: number;
    responseTime: number;
  };
  dataQuality: {
    completeness: number;
    accuracy: number;
    timeliness: number;
    consistency: number;
    validity: number;
  };
  resourceUtilization: {
    cpu: number;
    memory: number;
    storage: number;
    network: number;
    database: number;
  };
  trends: {
    anomalyRate: { timestamp: string; rate: number }[];
    falsePositiveRate: { timestamp: string; rate: number }[];
    detectionLatency: { timestamp: string; latency: number }[];
    systemLoad: { timestamp: string; load: number }[];
  };
}

interface AnomalyInsight {
  id: string;
  category: 'detection_improvement' | 'pattern_analysis' | 'system_optimization' | 'business_impact' | 'prevention_strategy';
  insight: string;
  confidence: number;
  impact: 'low' | 'medium' | 'high' | 'critical';
  evidence: {
    dataPoints: number;
    timeframe: string;
    correlations: string[];
    patterns: string[];
  };
  recommendations: {
    immediate: string[];
    shortTerm: string[];
    strategic: string[];
  };
  implementationPlan: {
    steps: { step: string; effort: string; timeline: string; owner: string }[];
    resources: string[];
    dependencies: string[];
    expectedOutcome: string;
  };
  businessValue: {
    riskReduction: number;
    efficiencyGain: number;
    costSavings: number;
    revenueProtection: number;
  };
  priority: number;
  createdAt: string;
}

// Mock data generation functions
const generateAnomalyDetectionModels = (): AnomalyDetectionModel[] => {
  const modelTypes = ['statistical', 'ml_based', 'deep_learning', 'ensemble', 'time_series', 'clustering', 'rule_based'] as const;
  const algorithms = ['isolation_forest', 'one_class_svm', 'lstm_autoencoder', 'dbscan', 'statistical_threshold', 'ensemble_voting', 'prophet_anomaly'] as const;
  const statuses = ['active', 'training', 'testing', 'inactive', 'error'] as const;
  const sensitivities = ['low', 'medium', 'high', 'adaptive'] as const;

  return Array.from({ length: 8 }, (_, i) => ({
    id: `model-${i + 1}`,
    name: [
      'Revenue Anomaly Detector',
      'User Behavior Monitor',
      'System Performance Watcher',
      'Security Threat Detector',
      'Inventory Level Monitor',
      'Customer Churn Predictor',
      'Fraud Detection Engine',
      'Website Performance Guard'
    ][i],
    type: modelTypes[i % modelTypes.length],
    algorithm: algorithms[i % algorithms.length],
    description: [
      'Detects unusual patterns in revenue streams and transaction volumes',
      'Monitors user behavior for suspicious or anomalous activities',
      'Watches system metrics for performance degradations and outages',
      'Identifies potential security threats and unauthorized access attempts',
      'Monitors inventory levels for unusual stock movements and demands',
      'Predicts customer churn based on behavioral pattern changes',
      'Detects fraudulent transactions and payment anomalies',
      'Monitors website performance and user experience metrics'
    ][i],
    status: statuses[Math.floor(Math.random() * statuses.length)],
    dataSource: [
      'Revenue Database', 'User Analytics', 'System Metrics', 'Security Logs',
      'Inventory System', 'Customer Database', 'Payment System', 'Web Analytics'
    ][i],
    monitoredMetrics: [
      ['Revenue', 'Transaction Volume', 'Average Order Value', 'Conversion Rate'],
      ['Session Duration', 'Page Views', 'Click Patterns', 'Navigation Flow'],
      ['CPU Usage', 'Memory Usage', 'Response Time', 'Error Rate'],
      ['Login Attempts', 'API Calls', 'Access Patterns', 'Privilege Escalations'],
      ['Stock Levels', 'Demand Patterns', 'Supply Chain', 'Reorder Points'],
      ['Engagement Score', 'Purchase Frequency', 'Support Tickets', 'App Usage'],
      ['Transaction Amounts', 'Payment Methods', 'Velocity', 'Geographic Patterns'],
      ['Page Load Time', 'Bounce Rate', 'Error Rates', 'Server Response']
    ][i],
    detectionWindow: ['5 minutes', '15 minutes', '1 hour', '6 hours', '24 hours', '1 week', '1 month'][i % 7],
    sensitivity: sensitivities[Math.floor(Math.random() * sensitivities.length)],
    falsePositiveRate: Math.random() * 10 + 2, // 2-12%
    truePositiveRate: Math.random() * 20 + 80, // 80-100%
    accuracy: Math.random() * 15 + 85, // 85-100%
    latency: Math.random() * 500 + 50, // 50-550ms
    throughput: Math.floor(Math.random() * 5000) + 1000, // 1000-6000 records/sec
    lastUpdated: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
    configuration: {
      thresholds: [
        { metric: 'Revenue', min: 50000, max: 200000, deviation: 2.5 },
        { metric: 'Error Rate', min: 0, max: 5, deviation: 3.0 },
        { metric: 'Response Time', min: 0, max: 2000, deviation: 2.0 }
      ],
      features: ['Temporal', 'Statistical', 'Behavioral', 'Contextual'],
      trainingPeriod: '90 days',
      retrainingFrequency: 'Weekly',
      alertingEnabled: true,
      autoRemediation: Math.random() > 0.5
    },
    performance: {
      anomaliesDetected: Math.floor(Math.random() * 500) + 100,
      alertsTriggered: Math.floor(Math.random() * 200) + 50,
      falseAlerts: Math.floor(Math.random() * 50) + 5,
      missedAnomalies: Math.floor(Math.random() * 20) + 2,
      averageDetectionTime: Math.random() * 300 + 60, // 1-6 minutes
      uptime: Math.random() * 5 + 95 // 95-100%
    },
    businessImpact: {
      issuesPrevented: Math.floor(Math.random() * 50) + 10,
      downtime_avoided: Math.random() * 100 + 20, // hours
      revenue_protected: Math.floor(Math.random() * 500000) + 100000,
      costs_saved: Math.floor(Math.random() * 100000) + 25000
    }
  }));
};

const generateDetectedAnomalies = (): DetectedAnomaly[] => {
  const severities = ['critical', 'high', 'medium', 'low', 'info'] as const;
  const statuses = ['new', 'acknowledged', 'investigating', 'resolved', 'false_positive', 'suppressed'] as const;
  const categories = ['performance', 'security', 'business', 'system', 'user_behavior', 'data_quality', 'operational'] as const;

  return Array.from({ length: 15 }, (_, i) => ({
    id: `anomaly-${i + 1}`,
    modelId: `model-${(i % 8) + 1}`,
    timestamp: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000).toISOString(),
    severity: severities[Math.floor(Math.random() * severities.length)],
    status: statuses[Math.floor(Math.random() * statuses.length)],
    category: categories[i % categories.length],
    title: [
      'Unusual Revenue Drop Detected',
      'Suspicious User Activity Pattern',
      'System Performance Degradation',
      'Potential Security Breach',
      'Inventory Anomaly Alert',
      'Customer Churn Spike',
      'Fraudulent Transaction Pattern',
      'Website Response Time Spike',
      'Payment Processing Delay',
      'Database Connection Issues',
      'API Rate Limit Exceeded',
      'Unusual Geographic Access',
      'Memory Usage Anomaly',
      'Email Delivery Failure Spike',
      'Social Media Mention Surge'
    ][i],
    description: [
      'Revenue has dropped by 35% compared to the same time last week with no identifiable cause',
      'Detected unusual user behavior pattern with rapid page navigation and suspicious click sequences',
      'System response time has increased by 400% over the last 30 minutes',
      'Multiple failed login attempts from suspicious IP addresses detected',
      'Inventory levels showing unexpected depletion patterns for high-demand products',
      'Customer churn rate has increased by 50% in the past 48 hours',
      'Pattern of transactions matching known fraudulent behavior signatures',
      'Website response time exceeded normal thresholds by 300% during peak hours',
      'Payment processing experiencing delays beyond acceptable service levels',
      'Database connection pool showing unusual timeout patterns',
      'API rate limits being exceeded by unknown client applications',
      'User access patterns from geographic locations outside normal business areas',
      'Memory usage spiking beyond normal operational parameters',
      'Email delivery failure rate increased to 25% from normal 2%',
      'Social media mentions increased by 800% with negative sentiment spike'
    ][i],
    affectedMetrics: [
      {
        metric: 'Revenue per Hour',
        currentValue: Math.random() * 10000 + 5000,
        expectedValue: Math.random() * 15000 + 10000,
        deviation: (Math.random() - 0.5) * 50,
        unit: 'USD'
      },
      {
        metric: 'Response Time',
        currentValue: Math.random() * 2000 + 500,
        expectedValue: Math.random() * 800 + 200,
        deviation: Math.random() * 200 + 50,
        unit: 'ms'
      }
    ],
    context: {
      source: ['Web Analytics', 'Payment System', 'User Database', 'Security Logs', 'System Metrics'][i % 5],
      environment: ['Production', 'Staging', 'Development'][Math.floor(Math.random() * 3)],
      component: ['Frontend', 'Backend', 'Database', 'Payment Gateway', 'CDN'][i % 5],
      tags: ['high-priority', 'customer-impact', 'revenue-risk'].slice(0, Math.floor(Math.random() * 3) + 1),
      relatedEntities: ['User Sessions', 'Payment Transactions', 'Database Queries'].slice(0, Math.floor(Math.random() * 2) + 1)
    },
    detectionMethod: {
      algorithm: ['Isolation Forest', 'LSTM Autoencoder', 'Statistical Threshold', 'Ensemble'][Math.floor(Math.random() * 4)],
      confidence: Math.random() * 30 + 70, // 70-100%
      threshold: Math.random() * 5 + 2, // 2-7 standard deviations
      historicalComparison: {
        baseline: Math.random() * 10000 + 5000,
        variance: Math.random() * 1000 + 500,
        seasonality: Math.random() * 20 + 80
      }
    },
    timeline: {
      detectedAt: new Date(Date.now() - Math.random() * 60 * 60 * 1000).toISOString(),
      firstOccurrence: new Date(Date.now() - Math.random() * 4 * 60 * 60 * 1000).toISOString(),
      lastOccurrence: new Date(Date.now() - Math.random() * 30 * 60 * 1000).toISOString(),
      duration: Math.floor(Math.random() * 240) + 30, // 30-270 minutes
      frequency: Math.floor(Math.random() * 10) + 1
    },
    impact: {
      usersAffected: Math.floor(Math.random() * 10000) + 100,
      systemsAffected: ['Web Server', 'Database', 'Payment Gateway'].slice(0, Math.floor(Math.random() * 2) + 1),
      businessProcesses: ['Order Processing', 'Customer Service', 'Inventory Management'].slice(0, Math.floor(Math.random() * 2) + 1),
      estimatedLoss: Math.floor(Math.random() * 50000) + 5000,
      urgency: Math.floor(Math.random() * 10) + 1
    },
    rootCauseAnalysis: {
      possibleCauses: [
        { cause: 'Database performance degradation', probability: 0.75, evidence: ['Slow query logs', 'Connection timeouts', 'High CPU usage'] },
        { cause: 'Third-party service outage', probability: 0.45, evidence: ['External API errors', 'Timeout responses'] },
        { cause: 'Network connectivity issues', probability: 0.30, evidence: ['Packet loss', 'Latency spikes'] }
      ],
      correlatedEvents: [
        { event: 'Database maintenance window', correlation: 0.85, timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString() },
        { event: 'Traffic spike from marketing campaign', correlation: 0.65, timestamp: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString() }
      ],
      similarIncidents: [
        { id: 'INC-2024-001', similarity: 0.78, resolution: 'Database index optimization' },
        { id: 'INC-2024-005', similarity: 0.65, resolution: 'Load balancer reconfiguration' }
      ]
    },
    recommendations: {
      immediate: [
        'Investigate database performance metrics',
        'Check third-party service status',
        'Review recent configuration changes'
      ],
      shortTerm: [
        'Implement database query optimization',
        'Add monitoring for external dependencies',
        'Configure automatic failover mechanisms'
      ],
      longTerm: [
        'Implement predictive scaling',
        'Establish SLA monitoring',
        'Create comprehensive runbooks'
      ],
      preventive: [
        'Regular performance testing',
        'Automated health checks',
        'Capacity planning reviews'
      ]
    },
    resolution: {
      assignedTo: `engineer-${Math.floor(Math.random() * 5) + 1}`,
      actions: [
        { action: 'Initial investigation started', timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(), user: 'system', result: 'In progress' },
        { action: 'Database performance reviewed', timestamp: new Date(Date.now() - 20 * 60 * 1000).toISOString(), user: 'engineer-1', result: 'Completed' }
      ],
      resolvedAt: Math.random() > 0.6 ? new Date(Date.now() - 10 * 60 * 1000).toISOString() : undefined,
      resolution: Math.random() > 0.6 ? 'Database index optimization resolved performance issues' : undefined,
      preventionMeasures: Math.random() > 0.6 ? ['Automated performance monitoring', 'Proactive index maintenance'] : undefined
    },
    alerting: {
      channels: ['Email', 'Slack', 'SMS', 'Dashboard'].slice(0, Math.floor(Math.random() * 3) + 1),
      recipients: ['ops-team', 'engineering-lead', 'on-call-engineer'],
      escalationLevel: Math.floor(Math.random() * 3) + 1,
      suppressionRules: ['maintenance-window', 'known-issue'].slice(0, Math.floor(Math.random() * 2))
    }
  }));
};

const generateAnomalyPatterns = (): AnomalyPattern[] => {
  const patternTypes = ['seasonal', 'trend', 'cyclic', 'point', 'collective', 'contextual', 'drift'] as const;
  const impacts = ['revenue', 'operations', 'customer', 'security', 'compliance', 'performance'] as const;

  return Array.from({ length: 6 }, (_, i) => ({
    id: `pattern-${i + 1}`,
    name: [
      'Weekend Traffic Anomaly',
      'Payment Processing Drift',
      'Seasonal Inventory Spike',
      'Security Alert Clusters',
      'Performance Degradation Waves',
      'Customer Churn Patterns'
    ][i],
    description: [
      'Unusual traffic patterns occurring specifically during weekend hours',
      'Gradual drift in payment processing times over the past month',
      'Seasonal spikes in inventory demand that exceed historical patterns',
      'Clusters of security alerts appearing in specific geographic regions',
      'Recurring waves of performance degradation during business hours',
      'Emerging patterns in customer churn behavior across different segments'
    ][i],
    patternType: patternTypes[i % patternTypes.length],
    frequency: Math.random() * 100,
    strength: Math.random() * 100,
    timeframe: ['Daily', 'Weekly', 'Monthly', 'Quarterly', 'Seasonal', 'Yearly'][i % 6],
    characteristics: {
      duration: Math.floor(Math.random() * 240) + 60, // 1-5 hours
      magnitude: Math.random() * 100 + 50, // 50-150% deviation
      scope: ['Global', 'Regional', 'Local', 'Specific Component'].slice(0, Math.floor(Math.random() * 2) + 1),
      triggers: ['Time-based', 'Event-driven', 'Threshold-based', 'External factors'].slice(0, Math.floor(Math.random() * 2) + 1),
      indicators: ['Metric spikes', 'User behavior changes', 'System alerts', 'Performance issues'].slice(0, Math.floor(Math.random() * 3) + 1)
    },
    businessContext: {
      impact: impacts[i % impacts.length],
      severity: Math.floor(Math.random() * 10) + 1,
      affected_areas: ['Sales', 'Customer Service', 'Operations', 'IT', 'Security'].slice(0, Math.floor(Math.random() * 3) + 1),
      stakeholders: ['Engineering', 'Product', 'Operations', 'Security', 'Business'].slice(0, Math.floor(Math.random() * 3) + 1)
    },
    detectionRules: {
      conditions: [
        'Metric exceeds 2 standard deviations',
        'Pattern repeats 3+ times',
        'Duration longer than 30 minutes',
        'Affects multiple systems'
      ].slice(0, Math.floor(Math.random() * 3) + 1),
      thresholds: {
        deviation: Math.random() * 3 + 1.5,
        duration: Math.floor(Math.random() * 60) + 15,
        frequency: Math.floor(Math.random() * 5) + 2
      },
      correlations: [
        { metric1: 'CPU Usage', metric2: 'Response Time', correlation: 0.85 },
        { metric1: 'Error Rate', metric2: 'User Satisfaction', correlation: -0.72 }
      ],
      dependencies: ['Database Performance', 'Network Latency', 'External APIs'].slice(0, Math.floor(Math.random() * 2) + 1)
    },
    historicalOccurrences: Array.from({ length: 5 }, (_, j) => ({
      timestamp: new Date(Date.now() - (j + 1) * 7 * 24 * 60 * 60 * 1000).toISOString(),
      severity: Math.floor(Math.random() * 10) + 1,
      duration: Math.floor(Math.random() * 180) + 30,
      impact: Math.floor(Math.random() * 100000) + 10000,
      resolution: ['Auto-resolved', 'Manual intervention', 'Configuration change', 'System restart'][Math.floor(Math.random() * 4)]
    })),
    predictiveIndicators: {
      leadingMetrics: ['Memory Usage', 'API Response Time', 'User Activity', 'Database Connections'].slice(0, Math.floor(Math.random() * 3) + 1),
      earlyWarningTime: Math.floor(Math.random() * 60) + 15, // 15-75 minutes
      predictionAccuracy: Math.random() * 30 + 70, // 70-100%
      preventionOpportunities: ['Proactive scaling', 'Predictive maintenance', 'Resource optimization'].slice(0, Math.floor(Math.random() * 2) + 1)
    },
    learningInsights: {
      evolutionTrend: ['Increasing frequency', 'Decreasing severity', 'Stable pattern', 'Evolving characteristics'][Math.floor(Math.random() * 4)],
      adaptations: ['Model retraining', 'Threshold adjustment', 'New detection rules'].slice(0, Math.floor(Math.random() * 2) + 1),
      improvements: ['Faster detection', 'Reduced false positives', 'Better context'],
      futureRisk: Math.random() * 100
    }
  }));
};

const generateSystemHealth = (): SystemHealth => {
  const components = ['Detection Engine', 'Alert Manager', 'Data Pipeline', 'Model Training', 'API Gateway', 'Database', 'Cache Layer', 'Message Queue'];
  
  return {
    timestamp: new Date().toISOString(),
    overallHealth: Math.random() * 20 + 80, // 80-100%
    componentHealth: components.map(component => ({
      component,
      status: ['healthy', 'warning', 'critical', 'unknown'][Math.floor(Math.random() * 4)] as any,
      health: Math.random() * 100,
      metrics: [
        { metric: 'CPU Usage', value: Math.random() * 100, threshold: 80, status: Math.random() > 0.2 ? 'normal' : 'warning' },
        { metric: 'Memory Usage', value: Math.random() * 100, threshold: 85, status: Math.random() > 0.3 ? 'normal' : 'critical' },
        { metric: 'Response Time', value: Math.random() * 1000 + 100, threshold: 500, status: Math.random() > 0.25 ? 'normal' : 'warning' }
      ],
      lastCheck: new Date(Date.now() - Math.random() * 60 * 1000).toISOString()
    })),
    detectionCapacity: {
      modelsActive: Math.floor(Math.random() * 5) + 6,
      throughput: Math.floor(Math.random() * 5000) + 3000,
      latency: Math.random() * 200 + 50,
      accuracy: Math.random() * 15 + 85,
      coverage: Math.random() * 20 + 80
    },
    alertingSystem: {
      alertsActive: Math.floor(Math.random() * 50) + 10,
      notificationsSent: Math.floor(Math.random() * 200) + 100,
      escalationsTriggered: Math.floor(Math.random() * 20) + 5,
      suppressedAlerts: Math.floor(Math.random() * 30) + 10,
      responseTime: Math.random() * 300 + 60
    },
    dataQuality: {
      completeness: Math.random() * 10 + 90,
      accuracy: Math.random() * 15 + 85,
      timeliness: Math.random() * 20 + 80,
      consistency: Math.random() * 10 + 90,
      validity: Math.random() * 15 + 85
    },
    resourceUtilization: {
      cpu: Math.random() * 60 + 20,
      memory: Math.random() * 70 + 20,
      storage: Math.random() * 50 + 30,
      network: Math.random() * 40 + 20,
      database: Math.random() * 80 + 15
    },
    trends: {
      anomalyRate: Array.from({ length: 24 }, (_, i) => ({
        timestamp: new Date(Date.now() - (23 - i) * 60 * 60 * 1000).toISOString(),
        rate: Math.random() * 10 + 2
      })),
      falsePositiveRate: Array.from({ length: 24 }, (_, i) => ({
        timestamp: new Date(Date.now() - (23 - i) * 60 * 60 * 1000).toISOString(),
        rate: Math.random() * 8 + 1
      })),
      detectionLatency: Array.from({ length: 24 }, (_, i) => ({
        timestamp: new Date(Date.now() - (23 - i) * 60 * 60 * 1000).toISOString(),
        latency: Math.random() * 200 + 100
      })),
      systemLoad: Array.from({ length: 24 }, (_, i) => ({
        timestamp: new Date(Date.now() - (23 - i) * 60 * 60 * 1000).toISOString(),
        load: Math.random() * 60 + 30
      }))
    }
  };
};

const generateAnomalyInsights = (): AnomalyInsight[] => {
  const categories = ['detection_improvement', 'pattern_analysis', 'system_optimization', 'business_impact', 'prevention_strategy'] as const;
  const impacts = ['low', 'medium', 'high', 'critical'] as const;

  return Array.from({ length: 8 }, (_, i) => ({
    id: `insight-${i + 1}`,
    category: categories[i % categories.length],
    insight: [
      'Machine learning models show 23% higher accuracy with ensemble approach',
      'Payment anomalies peak during specific geographic regions and time windows',
      'System performance degradation patterns correlate with database query complexity',
      'Customer behavior anomalies indicate 67% higher churn probability within 30 days',
      'Proactive threshold adjustment could reduce false positive rate by 35%',
      'Cross-correlation analysis reveals hidden dependencies between system components',
      'Seasonal adjustment algorithms improve detection accuracy by 18% during peak periods',
      'Real-time feature engineering enhances anomaly detection latency by 45%'
    ][i],
    confidence: Math.random() * 30 + 70, // 70-100%
    impact: impacts[Math.floor(Math.random() * impacts.length)],
    evidence: {
      dataPoints: Math.floor(Math.random() * 100000) + 10000,
      timeframe: ['30 days', '60 days', '90 days', '6 months'][Math.floor(Math.random() * 4)],
      correlations: ['Database performance', 'User behavior', 'System load', 'Network latency'].slice(0, Math.floor(Math.random() * 3) + 1),
      patterns: ['Temporal clustering', 'Geographic correlation', 'Feature importance', 'Threshold sensitivity'].slice(0, Math.floor(Math.random() * 2) + 1)
    },
    recommendations: {
      immediate: [
        'Implement ensemble model architecture',
        'Adjust detection thresholds based on time-of-day patterns',
        'Add geographic context to anomaly scoring'
      ].slice(0, Math.floor(Math.random() * 2) + 1),
      shortTerm: [
        'Deploy automated model retraining pipeline',
        'Integrate external data sources for context',
        'Implement dynamic threshold adjustment'
      ].slice(0, Math.floor(Math.random() * 2) + 1),
      strategic: [
        'Develop predictive anomaly prevention system',
        'Create unified anomaly detection platform',
        'Implement cross-domain correlation analysis'
      ].slice(0, Math.floor(Math.random() * 2) + 1)
    },
    implementationPlan: {
      steps: [
        { step: 'Model architecture evaluation', effort: 'Medium', timeline: '2 weeks', owner: 'ML Team' },
        { step: 'Ensemble implementation', effort: 'High', timeline: '4 weeks', owner: 'Engineering' },
        { step: 'Performance validation', effort: 'Low', timeline: '1 week', owner: 'QA Team' },
        { step: 'Production deployment', effort: 'Medium', timeline: '1 week', owner: 'DevOps' }
      ],
      resources: ['ML Engineers', 'Data Scientists', 'DevOps Engineers', 'QA Specialists'].slice(0, Math.floor(Math.random() * 3) + 2),
      dependencies: ['Model training infrastructure', 'Feature store', 'Monitoring systems'].slice(0, Math.floor(Math.random() * 2) + 1),
      expectedOutcome: `${Math.floor(Math.random() * 40) + 20}% improvement in detection accuracy and ${Math.floor(Math.random() * 30) + 15}% reduction in false positives`
    },
    businessValue: {
      riskReduction: Math.random() * 50 + 30,
      efficiencyGain: Math.random() * 40 + 20,
      costSavings: Math.floor(Math.random() * 200000) + 50000,
      revenueProtection: Math.floor(Math.random() * 1000000) + 250000
    },
    priority: Math.floor(Math.random() * 10) + 1,
    createdAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString()
  }));
};

const AutomatedAnomalyDetection: React.FC = () => {
  const [selectedTimeframe, setSelectedTimeframe] = useState('24h');
  const [selectedSeverity, setSelectedSeverity] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [viewMode, setViewMode] = useState('overview');
  const [searchTerm, setSearchTerm] = useState('');

  // Generated data
  const detectionModels = useMemo(() => generateAnomalyDetectionModels(), []);
  const detectedAnomalies = useMemo(() => generateDetectedAnomalies(), []);
  const anomalyPatterns = useMemo(() => generateAnomalyPatterns(), []);
  const systemHealth = useMemo(() => generateSystemHealth(), []);
  const anomalyInsights = useMemo(() => generateAnomalyInsights(), []);

  // Filtering and processing
  const filteredAnomalies = useMemo(() => {
    return detectedAnomalies.filter(anomaly => 
      (selectedSeverity === 'all' || anomaly.severity === selectedSeverity) &&
      (selectedStatus === 'all' || anomaly.status === selectedStatus) &&
      (anomaly.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
       anomaly.description.toLowerCase().includes(searchTerm.toLowerCase()))
    );
  }, [detectedAnomalies, selectedSeverity, selectedStatus, searchTerm]);

  const filteredModels = useMemo(() => {
    return detectionModels.filter(model => 
      model.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      model.description.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [detectionModels, searchTerm]);

  const aggregatedMetrics = useMemo(() => {
    const activeModels = detectionModels.filter(m => m.status === 'active').length;
    const totalAnomalies = detectedAnomalies.length;
    const criticalAnomalies = detectedAnomalies.filter(a => a.severity === 'critical').length;
    const avgAccuracy = detectionModels.reduce((sum, m) => sum + m.accuracy, 0) / detectionModels.length;
    const totalProtectedRevenue = detectionModels.reduce((sum, m) => sum + m.businessImpact.revenue_protected, 0);

    return {
      activeModels,
      totalAnomalies,
      criticalAnomalies,
      avgAccuracy,
      totalProtectedRevenue
    };
  }, [detectionModels, detectedAnomalies]);

  const anomalySeverityDistribution = useMemo(() => {
    const distribution = detectedAnomalies.reduce((acc, anomaly) => {
      acc[anomaly.severity] = (acc[anomaly.severity] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    return Object.entries(distribution).map(([severity, count]) => ({
      severity: severity.charAt(0).toUpperCase() + severity.slice(1),
      count,
      percentage: (count / detectedAnomalies.length) * 100
    }));
  }, [detectedAnomalies]);

  const modelPerformanceData = useMemo(() => {
    return detectionModels.map(model => ({
      name: model.name.split(' ').slice(0, 2).join(' '),
      accuracy: model.accuracy,
      throughput: model.throughput / 1000, // Convert to thousands
      latency: model.latency,
      anomalies: model.performance.anomaliesDetected
    }));
  }, [detectionModels]);

  const getModelIcon = (type: string) => {
    switch (type) {
      case 'statistical': return <BarChart3 className="h-4 w-4" />;
      case 'ml_based': return <Brain className="h-4 w-4" />;
      case 'deep_learning': return <Network className="h-4 w-4" />;
      case 'ensemble': return <Layers className="h-4 w-4" />;
      case 'time_series': return <LineChart className="h-4 w-4" />;
      case 'clustering': return <Target className="h-4 w-4" />;
      case 'rule_based': return <Settings className="h-4 w-4" />;
      default: return <Eye className="h-4 w-4" />;
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'info': return 'bg-gray-100 text-gray-800 border-gray-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'new': return 'bg-red-100 text-red-800 border-red-200';
      case 'acknowledged': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'investigating': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'resolved': return 'bg-green-100 text-green-800 border-green-200';
      case 'false_positive': return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'suppressed': return 'bg-purple-100 text-purple-800 border-purple-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getHealthColor = (health: number) => {
    if (health >= 90) return 'text-green-600';
    if (health >= 70) return 'text-yellow-600';
    if (health >= 50) return 'text-orange-600';
    return 'text-red-600';
  };

  return (
    <div className="w-full space-y-6 p-6 bg-gradient-to-br from-red-50 via-orange-50 to-yellow-50 min-h-screen">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-2">
            <AlertTriangle className="h-8 w-8 text-red-600" />
            Automated Anomaly Detection
          </h1>
          <p className="text-gray-600 mt-1">Real-time anomaly detection with AI-powered pattern recognition and automated alerting</p>
        </div>
        
        <div className="flex flex-wrap items-center gap-3">
          <div className="flex items-center gap-2">
            <Search className="h-4 w-4 text-gray-500" />
            <Input
              placeholder="Search anomalies..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-64"
            />
          </div>
          
          <select
            value={selectedSeverity}
            onChange={(e) => setSelectedSeverity(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-red-500"
          >
            <option value="all">All Severities</option>
            <option value="critical">Critical</option>
            <option value="high">High</option>
            <option value="medium">Medium</option>
            <option value="low">Low</option>
            <option value="info">Info</option>
          </select>
          
          <select
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-red-500"
          >
            <option value="all">All Statuses</option>
            <option value="new">New</option>
            <option value="acknowledged">Acknowledged</option>
            <option value="investigating">Investigating</option>
            <option value="resolved">Resolved</option>
            <option value="false_positive">False Positive</option>
            <option value="suppressed">Suppressed</option>
          </select>
          
          <select
            value={selectedTimeframe}
            onChange={(e) => setSelectedTimeframe(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-red-500"
          >
            <option value="1h">Last Hour</option>
            <option value="24h">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
          </select>
          
          <Button variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card className="bg-gradient-to-r from-red-500 to-red-600 text-white">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-red-100 text-sm">Active Models</p>
                <p className="text-2xl font-bold">{aggregatedMetrics.activeModels}</p>
                <p className="text-red-100 text-xs flex items-center mt-1">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Running smoothly
                </p>
              </div>
              <Shield className="h-8 w-8 text-red-200" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-orange-500 to-orange-600 text-white">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-orange-100 text-sm">Total Anomalies</p>
                <p className="text-2xl font-bold">{aggregatedMetrics.totalAnomalies}</p>
                <p className="text-orange-100 text-xs flex items-center mt-1">
                  <Activity className="h-3 w-3 mr-1" />
                  Last 24 hours
                </p>
              </div>
              <AlertTriangle className="h-8 w-8 text-orange-200" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-yellow-500 to-yellow-600 text-white">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-yellow-100 text-sm">Critical Alerts</p>
                <p className="text-2xl font-bold">{aggregatedMetrics.criticalAnomalies}</p>
                <p className="text-yellow-100 text-xs flex items-center mt-1">
                  <Bell className="h-3 w-3 mr-1" />
                  Requires attention
                </p>
              </div>
              <BellRing className="h-8 w-8 text-yellow-200" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-green-500 to-green-600 text-white">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-100 text-sm">Avg Accuracy</p>
                <p className="text-2xl font-bold">{aggregatedMetrics.avgAccuracy.toFixed(1)}%</p>
                <p className="text-green-100 text-xs flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +2.1% this week
                </p>
              </div>
              <Target className="h-8 w-8 text-green-200" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-100 text-sm">Revenue Protected</p>
                <p className="text-2xl font-bold">${(aggregatedMetrics.totalProtectedRevenue / 1000000).toFixed(1)}M</p>
                <p className="text-blue-100 text-xs flex items-center mt-1">
                  <DollarSign className="h-3 w-3 mr-1" />
                  This month
                </p>
              </div>
              <Shield className="h-8 w-8 text-blue-200" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={viewMode} onValueChange={setViewMode} className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="anomalies">Anomalies</TabsTrigger>
          <TabsTrigger value="models">Models</TabsTrigger>
          <TabsTrigger value="patterns">Patterns</TabsTrigger>
          <TabsTrigger value="health">System Health</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Anomaly Detection Trends */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <LineChart className="h-5 w-5" />
                  Anomaly Detection Trends
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <ComposedChart data={systemHealth.trends.anomalyRate}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="timestamp" tickFormatter={(value) => new Date(value).toLocaleTimeString()} />
                    <YAxis yAxisId="left" />
                    <YAxis yAxisId="right" orientation="right" />
                    <Tooltip labelFormatter={(value) => new Date(value).toLocaleString()} />
                    <Area yAxisId="left" type="monotone" dataKey="rate" stackId="1" stroke="#EF4444" fill="#FEE2E2" />
                    <Line yAxisId="right" type="monotone" dataKey="rate" stroke="#DC2626" strokeWidth={2} />
                  </ComposedChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Severity Distribution */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <PieChart className="h-5 w-5" />
                  Anomaly Severity Distribution
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Tooltip />
                    <Cell />
                  </PieChart>
                </ResponsiveContainer>
                <div className="grid grid-cols-2 gap-2 mt-4">
                  {anomalySeverityDistribution.map((item, i) => (
                    <div key={i} className="flex items-center gap-2 text-sm">
                      <div className={`w-3 h-3 rounded-full ${
                        item.severity === 'Critical' ? 'bg-red-500' :
                        item.severity === 'High' ? 'bg-orange-500' :
                        item.severity === 'Medium' ? 'bg-yellow-500' :
                        item.severity === 'Low' ? 'bg-blue-500' : 'bg-gray-500'
                      }`}></div>
                      <span className="text-gray-600">{item.severity}</span>
                      <span className="font-medium">{item.percentage.toFixed(1)}%</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Model Performance Overview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Model Performance Overview
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <ComposedChart data={modelPerformanceData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" angle={-45} textAnchor="end" height={80} />
                  <YAxis yAxisId="left" />
                  <YAxis yAxisId="right" orientation="right" />
                  <Tooltip />
                  <Bar yAxisId="left" dataKey="accuracy" fill="#10B981" name="Accuracy %" />
                  <Bar yAxisId="left" dataKey="throughput" fill="#3B82F6" name="Throughput (K/s)" />
                  <Line yAxisId="right" type="monotone" dataKey="anomalies" stroke="#EF4444" strokeWidth={2} name="Anomalies Detected" />
                </ComposedChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Recent Critical Anomalies */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertCircle className="h-5 w-5" />
                Recent Critical Anomalies
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredAnomalies.filter(a => a.severity === 'critical').slice(0, 3).map((anomaly) => (
                  <div key={anomaly.id} className="flex items-start justify-between p-4 bg-red-50 rounded-lg border border-red-200">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <Badge className={getSeverityColor(anomaly.severity)}>
                          {anomaly.severity}
                        </Badge>
                        <Badge className={getStatusColor(anomaly.status)}>
                          {anomaly.status.replace('_', ' ')}
                        </Badge>
                        <span className="text-sm text-gray-500">
                          {new Date(anomaly.timestamp).toLocaleString()}
                        </span>
                      </div>
                      <h4 className="font-medium text-gray-900 mb-1">{anomaly.title}</h4>
                      <p className="text-sm text-gray-600 mb-2">{anomaly.description}</p>
                      <div className="flex items-center gap-4 text-sm">
                        <span className="text-gray-600">
                          Users Affected: <span className="font-medium">{anomaly.impact.usersAffected.toLocaleString()}</span>
                        </span>
                        <span className="text-gray-600">
                          Est. Loss: <span className="font-medium text-red-600">${anomaly.impact.estimatedLoss.toLocaleString()}</span>
                        </span>
                        <span className="text-gray-600">
                          Confidence: <span className="font-medium">{anomaly.detectionMethod.confidence.toFixed(1)}%</span>
                        </span>
                      </div>
                    </div>
                    <div className="flex gap-2 ml-4">
                      <Button variant="outline" size="sm">
                        <Eye className="h-4 w-4 mr-2" />
                        Details
                      </Button>
                      <Button size="sm" className="bg-red-600 hover:bg-red-700">
                        <Zap className="h-4 w-4 mr-2" />
                        Investigate
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="anomalies" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {filteredAnomalies.map((anomaly) => (
              <motion.div
                key={anomaly.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="group"
              >
                <Card className="h-full hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <CardTitle className="flex items-center gap-2">
                        <AlertTriangle className="h-5 w-5" />
                        {anomaly.title}
                      </CardTitle>
                      <div className="flex items-center gap-2">
                        <Badge className={getSeverityColor(anomaly.severity)}>
                          {anomaly.severity}
                        </Badge>
                        <Badge className={getStatusColor(anomaly.status)}>
                          {anomaly.status.replace('_', ' ')}
                        </Badge>
                      </div>
                    </div>
                    <p className="text-sm text-gray-600">{anomaly.description}</p>
                    <div className="flex items-center gap-4 text-xs text-gray-500">
                      <span>Detected: {new Date(anomaly.timestamp).toLocaleString()}</span>
                      <span>Duration: {anomaly.timeline.duration}m</span>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Affected Metrics */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Affected Metrics</h4>
                      <div className="space-y-2">
                        {anomaly.affectedMetrics.slice(0, 2).map((metric, i) => (
                          <div key={i} className="flex items-center justify-between text-sm">
                            <span className="text-gray-600">{metric.metric}</span>
                            <div className="text-right">
                              <div className="flex items-center gap-2">
                                <span className="font-medium">{metric.currentValue.toFixed(0)} {metric.unit}</span>
                                <span className={`text-xs ${metric.deviation >= 0 ? 'text-red-600' : 'text-green-600'}`}>
                                  ({metric.deviation >= 0 ? '+' : ''}{metric.deviation.toFixed(1)}%)
                                </span>
                              </div>
                              <p className="text-xs text-gray-500">Expected: {metric.expectedValue.toFixed(0)} {metric.unit}</p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Detection Info */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Detection Details</h4>
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <div>
                          <span className="text-gray-600">Algorithm:</span>
                          <p className="font-medium">{anomaly.detectionMethod.algorithm}</p>
                        </div>
                        <div>
                          <span className="text-gray-600">Confidence:</span>
                          <p className="font-medium">{anomaly.detectionMethod.confidence.toFixed(1)}%</p>
                        </div>
                        <div>
                          <span className="text-gray-600">Model:</span>
                          <p className="font-medium">{anomaly.modelId}</p>
                        </div>
                        <div>
                          <span className="text-gray-600">Category:</span>
                          <p className="font-medium capitalize">{anomaly.category.replace('_', ' ')}</p>
                        </div>
                      </div>
                    </div>

                    {/* Impact Assessment */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Impact Assessment</h4>
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <div className={`p-2 rounded ${anomaly.severity === 'critical' ? 'bg-red-50' : anomaly.severity === 'high' ? 'bg-orange-50' : 'bg-yellow-50'}`}>
                          <p className="text-xs text-gray-600">Users Affected</p>
                          <p className="font-bold">{anomaly.impact.usersAffected.toLocaleString()}</p>
                        </div>
                        <div className={`p-2 rounded ${anomaly.severity === 'critical' ? 'bg-red-50' : anomaly.severity === 'high' ? 'bg-orange-50' : 'bg-yellow-50'}`}>
                          <p className="text-xs text-gray-600">Est. Loss</p>
                          <p className="font-bold">${anomaly.impact.estimatedLoss.toLocaleString()}</p>
                        </div>
                      </div>
                    </div>

                    {/* Recommendations */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Immediate Actions</h4>
                      <ul className="text-sm text-gray-600 space-y-1">
                        {anomaly.recommendations.immediate.slice(0, 2).map((rec, i) => (
                          <li key={i} className="flex items-start gap-2">
                            <span className="text-orange-600 mt-1">•</span>
                            <span>{rec}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    {/* Actions */}
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm" className="flex-1">
                        <Eye className="h-4 w-4 mr-2" />
                        Details
                      </Button>
                      <Button size="sm" className="flex-1">
                        <Zap className="h-4 w-4 mr-2" />
                        Investigate
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="models" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {filteredModels.map((model) => (
              <Card key={model.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <CardTitle className="flex items-center gap-2">
                      {getModelIcon(model.type)}
                      {model.name}
                    </CardTitle>
                    <Badge className={
                      model.status === 'active' ? 'bg-green-100 text-green-800' :
                      model.status === 'training' ? 'bg-blue-100 text-blue-800' :
                      model.status === 'testing' ? 'bg-yellow-100 text-yellow-800' :
                      model.status === 'inactive' ? 'bg-gray-100 text-gray-800' :
                      'bg-red-100 text-red-800'
                    }>
                      {model.status}
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-600">{model.description}</p>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Performance Metrics */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Performance</h4>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div className="bg-green-50 p-2 rounded text-center">
                        <p className="font-bold text-green-600">{model.accuracy.toFixed(1)}%</p>
                        <p className="text-xs text-gray-600">Accuracy</p>
                      </div>
                      <div className="bg-blue-50 p-2 rounded text-center">
                        <p className="font-bold text-blue-600">{model.truePositiveRate.toFixed(1)}%</p>
                        <p className="text-xs text-gray-600">True Positive</p>
                      </div>
                      <div className="bg-orange-50 p-2 rounded text-center">
                        <p className="font-bold text-orange-600">{model.falsePositiveRate.toFixed(1)}%</p>
                        <p className="text-xs text-gray-600">False Positive</p>
                      </div>
                      <div className="bg-purple-50 p-2 rounded text-center">
                        <p className="font-bold text-purple-600">{model.latency.toFixed(0)}ms</p>
                        <p className="text-xs text-gray-600">Latency</p>
                      </div>
                    </div>
                  </div>

                  {/* Model Configuration */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Configuration</h4>
                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Algorithm:</span>
                        <span className="font-medium capitalize">{model.algorithm.replace('_', ' ')}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Data Source:</span>
                        <span className="font-medium">{model.dataSource}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Detection Window:</span>
                        <span className="font-medium">{model.detectionWindow}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Sensitivity:</span>
                        <span className="font-medium capitalize">{model.sensitivity}</span>
                      </div>
                    </div>
                  </div>

                  {/* Recent Activity */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Recent Activity</h4>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div>
                        <span className="text-gray-600">Anomalies Detected:</span>
                        <p className="font-medium">{model.performance.anomaliesDetected}</p>
                      </div>
                      <div>
                        <span className="text-gray-600">Alerts Triggered:</span>
                        <p className="font-medium">{model.performance.alertsTriggered}</p>
                      </div>
                      <div>
                        <span className="text-gray-600">False Alerts:</span>
                        <p className="font-medium text-orange-600">{model.performance.falseAlerts}</p>
                      </div>
                      <div>
                        <span className="text-gray-600">Uptime:</span>
                        <p className="font-medium text-green-600">{model.performance.uptime.toFixed(1)}%</p>
                      </div>
                    </div>
                  </div>

                  {/* Business Impact */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Business Impact</h4>
                    <div className="bg-green-50 p-3 rounded-lg">
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <div>
                          <span className="text-gray-600">Revenue Protected:</span>
                          <p className="font-bold text-green-600">${(model.businessImpact.revenue_protected / 1000).toFixed(0)}K</p>
                        </div>
                        <div>
                          <span className="text-gray-600">Costs Saved:</span>
                          <p className="font-bold text-green-600">${(model.businessImpact.costs_saved / 1000).toFixed(0)}K</p>
                        </div>
                        <div>
                          <span className="text-gray-600">Issues Prevented:</span>
                          <p className="font-bold text-green-600">{model.businessImpact.issuesPrevented}</p>
                        </div>
                        <div>
                          <span className="text-gray-600">Downtime Avoided:</span>
                          <p className="font-bold text-green-600">{model.businessImpact.downtime_avoided.toFixed(0)}h</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Monitored Metrics */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Monitored Metrics</h4>
                    <div className="flex flex-wrap gap-1">
                      {model.monitoredMetrics.slice(0, 4).map((metric, i) => (
                        <Badge key={i} variant="secondary" className="text-xs">{metric}</Badge>
                      ))}
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <Button variant="outline" size="sm" className="flex-1">
                      <Settings className="h-4 w-4 mr-2" />
                      Configure
                    </Button>
                    <Button size="sm" className="flex-1">
                      <Eye className="h-4 w-4 mr-2" />
                      Monitor
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="patterns" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {anomalyPatterns.map((pattern) => (
              <Card key={pattern.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Target className="h-5 w-5" />
                    {pattern.name}
                  </CardTitle>
                  <p className="text-sm text-gray-600 leading-relaxed">{pattern.description}</p>
                  <div className="flex items-center gap-4 text-sm">
                    <Badge variant="outline" className="capitalize">
                      {pattern.patternType}
                    </Badge>
                    <span className="text-gray-500">{pattern.timeframe}</span>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Pattern Strength */}
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm text-gray-600">Pattern Strength</span>
                      <span className="font-medium">{pattern.strength.toFixed(0)}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-orange-600 h-2 rounded-full" 
                        style={{ width: `${pattern.strength}%` }}
                      ></div>
                    </div>
                  </div>

                  {/* Characteristics */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Characteristics</h4>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div>
                        <span className="text-gray-600">Duration:</span>
                        <p className="font-medium">{pattern.characteristics.duration}min</p>
                      </div>
                      <div>
                        <span className="text-gray-600">Magnitude:</span>
                        <p className="font-medium">{pattern.characteristics.magnitude.toFixed(0)}%</p>
                      </div>
                      <div>
                        <span className="text-gray-600">Frequency:</span>
                        <p className="font-medium">{pattern.frequency.toFixed(0)}%</p>
                      </div>
                      <div>
                        <span className="text-gray-600">Severity:</span>
                        <p className="font-medium">{pattern.businessContext.severity}/10</p>
                      </div>
                    </div>
                  </div>

                  {/* Business Impact */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Business Context</h4>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-600">Primary Impact:</span>
                        <span className="font-medium capitalize">{pattern.businessContext.impact}</span>
                      </div>
                      <div>
                        <p className="text-xs text-gray-600 mb-1">Affected Areas:</p>
                        <div className="flex flex-wrap gap-1">
                          {pattern.businessContext.affected_areas.map((area, i) => (
                            <Badge key={i} variant="secondary" className="text-xs">{area}</Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Historical Occurrences */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Recent Occurrences</h4>
                    <div className="space-y-2">
                      {pattern.historicalOccurrences.slice(0, 3).map((occurrence, i) => (
                        <div key={i} className="flex items-center justify-between text-sm p-2 bg-gray-50 rounded">
                          <span className="text-gray-600">{new Date(occurrence.timestamp).toLocaleDateString()}</span>
                          <div className="flex items-center gap-2">
                            <span className="font-medium">Severity: {occurrence.severity}/10</span>
                            <span className="text-gray-500">{occurrence.duration}min</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Predictive Insights */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Predictive Insights</h4>
                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Early Warning:</span>
                        <span className="font-medium">{pattern.predictiveIndicators.earlyWarningTime}min</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Prediction Accuracy:</span>
                        <span className="font-medium">{pattern.predictiveIndicators.predictionAccuracy.toFixed(1)}%</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Future Risk:</span>
                        <span className="font-medium text-orange-600">{pattern.learningInsights.futureRisk.toFixed(0)}%</span>
                      </div>
                    </div>
                  </div>

                  <Button variant="outline" className="w-full">
                    <Eye className="h-4 w-4 mr-2" />
                    View Pattern Analysis
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="health" className="space-y-6">
          {/* Overall System Health */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Monitor className="h-5 w-5" />
                System Health Overview
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                <div className="text-center">
                  <div className={`text-3xl font-bold ${getHealthColor(systemHealth.overallHealth)}`}>
                    {systemHealth.overallHealth.toFixed(1)}%
                  </div>
                  <p className="text-sm text-gray-600">Overall Health</p>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600">
                    {systemHealth.detectionCapacity.modelsActive}
                  </div>
                  <p className="text-sm text-gray-600">Active Models</p>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-600">
                    {(systemHealth.detectionCapacity.throughput / 1000).toFixed(1)}K
                  </div>
                  <p className="text-sm text-gray-600">Throughput/sec</p>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-purple-600">
                    {systemHealth.detectionCapacity.latency.toFixed(0)}ms
                  </div>
                  <p className="text-sm text-gray-600">Avg Latency</p>
                </div>
              </div>

              <ResponsiveContainer width="100%" height={300}>
                <ComposedChart data={systemHealth.trends.systemLoad}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="timestamp" tickFormatter={(value) => new Date(value).toLocaleTimeString()} />
                  <YAxis />
                  <Tooltip labelFormatter={(value) => new Date(value).toLocaleString()} />
                  <Area type="monotone" dataKey="load" stroke="#3B82F6" fill="#DBEAFE" />
                </ComposedChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Component Health */}
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-4 gap-6">
            {systemHealth.componentHealth.map((component) => (
              <Card key={component.component}>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span className="flex items-center gap-2">
                      <Server className="h-5 w-5" />
                      {component.component}
                    </span>
                    <Badge className={
                      component.status === 'healthy' ? 'bg-green-100 text-green-800' :
                      component.status === 'warning' ? 'bg-yellow-100 text-yellow-800' :
                      component.status === 'critical' ? 'bg-red-100 text-red-800' :
                      'bg-gray-100 text-gray-800'
                    }>
                      {component.status}
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="text-center">
                      <div className={`text-2xl font-bold ${getHealthColor(component.health)}`}>
                        {component.health.toFixed(1)}%
                      </div>
                      <p className="text-xs text-gray-600">Health Score</p>
                    </div>
                    
                    <div className="space-y-2">
                      {component.metrics.map((metric, i) => (
                        <div key={i} className="flex items-center justify-between text-sm">
                          <span className="text-gray-600">{metric.metric}:</span>
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{metric.value.toFixed(0)}</span>
                            <div className={`w-2 h-2 rounded-full ${
                              metric.status === 'normal' ? 'bg-green-500' :
                              metric.status === 'warning' ? 'bg-yellow-500' :
                              'bg-red-500'
                            }`}></div>
                          </div>
                        </div>
                      ))}
                    </div>
                    
                    <div className="text-xs text-gray-500 text-center pt-2 border-t">
                      Last check: {new Date(component.lastCheck).toLocaleTimeString()}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Resource Utilization */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Gauge className="h-5 w-5" />
                Resource Utilization
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                {Object.entries(systemHealth.resourceUtilization).map(([resource, utilization]) => (
                  <div key={resource} className="text-center">
                    <div className="relative w-20 h-20 mx-auto mb-2">
                      <svg className="w-20 h-20 transform -rotate-90" viewBox="0 0 36 36">
                        <path
                          d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                          fill="none"
                          stroke="#E5E7EB"
                          strokeWidth="3"
                        />
                        <path
                          d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                          fill="none"
                          stroke={utilization >= 80 ? "#EF4444" : utilization >= 60 ? "#F59E0B" : "#10B981"}
                          strokeWidth="3"
                          strokeDasharray={`${utilization}, 100`}
                        />
                      </svg>
                      <div className="absolute inset-0 flex items-center justify-center">
                        <span className="text-sm font-bold">{utilization.toFixed(0)}%</span>
                      </div>
                    </div>
                    <p className="text-sm font-medium capitalize">{resource}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Data Quality Metrics */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                Data Quality Metrics
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                {Object.entries(systemHealth.dataQuality).map(([metric, score]) => (
                  <div key={metric} className="text-center">
                    <div className={`text-2xl font-bold ${getHealthColor(score)}`}>
                      {score.toFixed(1)}%
                    </div>
                    <p className="text-sm text-gray-600 capitalize">{metric}</p>
                    <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                      <div 
                        className={`h-2 rounded-full ${
                          score >= 90 ? 'bg-green-500' :
                          score >= 70 ? 'bg-yellow-500' :
                          'bg-red-500'
                        }`}
                        style={{ width: `${score}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AutomatedAnomalyDetection;