/**
 * Natural Language Explanations
 * AI-powered system that converts complex data patterns into human-readable explanations
 */

import React, { useState, useMemo, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { motion } from 'framer-motion';
import { 
  MessageSquare, 
  Brain, 
  Mic, 
  Volume2,
  Copy,
  Download,
  Share,
  ThumbsUp,
  ThumbsDown,
  MoreHorizontal,
  Play,
  Pause,
  SkipForward,
  SkipBack,
  BookOpen,
  Lightbulb,
  TrendingUp,
  TrendingDown,
  BarChart3,
  PieChart,
  Users,
  DollarSign,
  Calendar,
  Clock,
  Target,
  Zap,
  Eye,
  Edit,
  Settings,
  Filter,
  Search,
  Sparkles,
  FileText,
  Video,
  Headphones,
  Languages,
  Globe,
  Cpu,
  Database,
  Network
} from 'lucide-react';

// Types
interface NLExplanation {
  id: string;
  type: 'chart' | 'metric' | 'trend' | 'anomaly' | 'comparison' | 'forecast';
  context: {
    chartType: string;
    dataSource: string;
    timeRange: string;
    filters: string[];
    metrics: string[];
  };
  explanation: {
    summary: string;
    detailed: string;
    keyInsights: string[];
    implications: string[];
    recommendations: string[];
  };
  narrative: {
    intro: string;
    body: string[];
    conclusion: string;
  };
  complexity: 'simple' | 'intermediate' | 'advanced';
  audience: 'executive' | 'manager' | 'analyst' | 'general';
  language: string;
  confidence: number;
  generatedAt: Date;
  lastUpdated: Date;
  feedback: {
    helpful: number;
    notHelpful: number;
    comments: string[];
  };
  audioGenerated: boolean;
  videoGenerated: boolean;
  relatedQuestions: string[];
}

interface ExplanationRequest {
  dataType: string;
  chartConfig: any;
  userQuery?: string;
  context: string;
  audience: string;
  complexity: string;
  language: string;
}

interface DataStory {
  id: string;
  title: string;
  description: string;
  chapters: DataChapter[];
  duration: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  tags: string[];
  createdAt: Date;
  views: number;
  likes: number;
  isPublic: boolean;
}

interface DataChapter {
  id: string;
  title: string;
  content: string;
  visualizations: any[];
  narration: string;
  duration: string;
  keyPoints: string[];
}

interface ConversationalResponse {
  id: string;
  query: string;
  response: string;
  confidence: number;
  sources: string[];
  followUpQuestions: string[];
  timestamp: Date;
  feedback?: 'positive' | 'negative';
}

interface NaturalLanguageExplanationsProps {
  onExplanationRequest?: (request: ExplanationRequest) => void;
  onFeedback?: (explanationId: string, feedback: string) => void;
  className?: string;
}

// Mock data generation
const generateNLExplanations = (): NLExplanation[] => [
  {
    id: 'exp-revenue-trend',
    type: 'trend',
    context: {
      chartType: 'line_chart',
      dataSource: 'sales_data',
      timeRange: 'last_6_months',
      filters: ['product_category: electronics'],
      metrics: ['revenue', 'units_sold']
    },
    explanation: {
      summary: 'Electronics revenue shows strong 23% growth over the past 6 months, driven primarily by increased unit sales rather than price increases.',
      detailed: 'The electronics category has experienced consistent month-over-month growth, with particularly strong performance in Q2. The growth is primarily volume-driven, with unit sales increasing 28% while average selling prices have remained relatively stable, increasing only 4%. This suggests healthy demand expansion rather than inflation-driven revenue growth.',
      keyInsights: [
        'Revenue increased from $2.1M to $2.6M (+23%)',
        'Unit sales grew 28% while prices increased only 4%',
        'May showed the strongest single-month performance (+15%)',
        'Mobile accessories and smart home devices led the growth',
        'Customer acquisition in electronics improved 31%'
      ],
      implications: [
        'Market demand for electronics products is expanding',
        'Price sensitivity remains low, indicating quality positioning',
        'Inventory planning should account for continued growth',
        'Marketing investments in electronics are paying off'
      ],
      recommendations: [
        'Increase inventory allocation for high-performing electronics subcategories',
        'Consider expanding the electronics product line',
        'Invest in electronics-focused marketing campaigns',
        'Explore premium pricing opportunities for top-selling items'
      ]
    },
    narrative: {
      intro: 'Looking at our electronics revenue over the past six months reveals a compelling growth story.',
      body: [
        'Starting in January with $2.1 million in monthly revenue, we\'ve seen consistent upward momentum that culminated in a strong finish of $2.6 million in June.',
        'What makes this growth particularly noteworthy is its foundation in genuine demand expansion. While our competitors have relied on price increases to drive revenue, we\'ve achieved 23% growth primarily through volume increases.',
        'The standout month was May, where we saw a remarkable 15% month-over-month spike. This coincided with our smart home device promotion and the launch of our mobile accessories line.',
        'Customer acquisition metrics support this positive trend, with new customer acquisition in electronics improving by 31%, suggesting our value proposition is resonating with the market.'
      ],
      conclusion: 'This sustained growth trajectory positions us well for continued expansion in the electronics category, with opportunities for both volume and margin optimization.'
    },
    complexity: 'intermediate',
    audience: 'manager',
    language: 'en',
    confidence: 92,
    generatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
    lastUpdated: new Date(Date.now() - 30 * 60 * 1000),
    feedback: {
      helpful: 14,
      notHelpful: 2,
      comments: ['Very clear explanation', 'Helpful for planning', 'Would like more detail on seasonality']
    },
    audioGenerated: true,
    videoGenerated: false,
    relatedQuestions: [
      'What drove the May revenue spike?',
      'How does this compare to industry trends?',
      'What are the margin implications?',
      'Which specific products contributed most to growth?'
    ]
  },
  {
    id: 'exp-customer-cohort',
    type: 'chart',
    context: {
      chartType: 'cohort_analysis',
      dataSource: 'customer_data',
      timeRange: 'last_12_months',
      filters: ['signup_date: 2024'],
      metrics: ['retention_rate', 'revenue_per_cohort']
    },
    explanation: {
      summary: 'Customer cohort analysis reveals improving retention patterns, with recent cohorts showing 15% better 6-month retention than cohorts from early 2024.',
      detailed: 'The cohort analysis demonstrates a clear improvement in our customer retention strategy effectiveness. Customers who joined in Q2 2024 show significantly better retention rates compared to Q1 cohorts, with 6-month retention improving from 45% to 60%. This improvement correlates with the implementation of our enhanced onboarding process and personalized engagement campaigns.',
      keyInsights: [
        'Q2 2024 cohorts show 60% retention at 6 months vs 45% for Q1',
        'Revenue per customer in newer cohorts is 18% higher',
        'First-week engagement strongly predicts long-term retention',
        'Cohorts from promotional periods show lower retention but higher initial value',
        'Mobile-first customers have 23% better retention than desktop-first'
      ],
      implications: [
        'Onboarding improvements are delivering measurable results',
        'Mobile experience optimization should be prioritized',
        'Promotional acquisition may need quality filters',
        'Early engagement is critical for long-term success'
      ],
      recommendations: [
        'Scale the improved onboarding process to all new customers',
        'Develop mobile-specific retention strategies',
        'Create early engagement triggers for at-risk customers',
        'Review promotional customer acquisition strategies'
      ]
    },
    narrative: {
      intro: 'Our customer cohort analysis tells a story of continuous improvement in customer relationships.',
      body: [
        'When we compare customers who joined us in different months, a clear pattern emerges: we\'re getting better at keeping customers engaged over time.',
        'The most striking finding is the improvement between our Q1 and Q2 2024 cohorts. While customers who joined in early 2024 had a 45% retention rate at the 6-month mark, those who joined in Q2 are showing 60% retention.',
        'This improvement isn\'t just about keeping more customers—it\'s about keeping more valuable customers. Revenue per customer in our newer cohorts is running 18% higher than earlier ones.',
        'We\'ve also discovered that mobile-first customers stick around 23% longer than those who primarily use desktop, which gives us clear direction for platform investment.'
      ],
      conclusion: 'These trends validate our customer experience investments and point toward mobile optimization as our next major opportunity.'
    },
    complexity: 'advanced',
    audience: 'analyst',
    language: 'en',
    confidence: 88,
    generatedAt: new Date(Date.now() - 4 * 60 * 60 * 1000),
    lastUpdated: new Date(Date.now() - 1 * 60 * 60 * 1000),
    feedback: {
      helpful: 21,
      notHelpful: 1,
      comments: ['Excellent cohort insights', 'Mobile finding is actionable', 'Great narrative flow']
    },
    audioGenerated: true,
    videoGenerated: true,
    relatedQuestions: [
      'What specific onboarding changes drove improvement?',
      'How do mobile vs desktop user behaviors differ?',
      'What early engagement metrics predict retention?',
      'How should we adjust promotional strategies?'
    ]
  }
];

const generateDataStories = (): DataStory[] => [
  {
    id: 'story-q2-performance',
    title: 'Q2 2024: A Quarter of Strategic Transformation',
    description: 'Comprehensive analysis of Q2 performance across all business metrics with strategic insights',
    duration: '12 minutes',
    difficulty: 'intermediate',
    tags: ['quarterly-review', 'strategic', 'performance'],
    createdAt: new Date('2024-07-01'),
    views: 347,
    likes: 28,
    isPublic: true,
    chapters: [
      {
        id: 'ch1',
        title: 'Revenue Growth Acceleration',
        content: 'Q2 marked a turning point in our revenue trajectory...',
        visualizations: [],
        narration: 'Looking at our Q2 revenue performance, we see a remarkable acceleration...',
        duration: '3 minutes',
        keyPoints: ['23% revenue growth', 'Electronics category leadership', 'Customer acquisition improvement']
      },
      {
        id: 'ch2',
        title: 'Customer Experience Evolution',
        content: 'Our customer metrics tell a story of focused improvement...',
        visualizations: [],
        narration: 'The customer experience data reveals significant progress...',
        duration: '4 minutes',
        keyPoints: ['Retention rate improvement', 'NPS score increase', 'Mobile experience gains']
      },
      {
        id: 'ch3',
        title: 'Operational Excellence',
        content: 'Behind the numbers, our operational metrics show increasing efficiency...',
        visualizations: [],
        narration: 'Our operational transformation is evident in the metrics...',
        duration: '3 minutes',
        keyPoints: ['Fulfillment speed improvement', 'Cost reduction', 'Quality metrics']
      },
      {
        id: 'ch4',
        title: 'Strategic Outlook',
        content: 'Looking ahead, the data points to several strategic opportunities...',
        visualizations: [],
        narration: 'As we look to Q3 and beyond, the data reveals clear opportunities...',
        duration: '2 minutes',
        keyPoints: ['Market expansion opportunities', 'Technology investments', 'Scaling strategies']
      }
    ]
  }
];

const generateConversationalResponses = (): ConversationalResponse[] => [
  {
    id: 'resp-1',
    query: 'Why did revenue spike in May?',
    response: 'The May revenue spike of 15% was driven by three key factors: our smart home device promotion generated 34% more sales in that category, the launch of our mobile accessories line contributed $180K in new revenue, and a viral social media mention of our wireless earbuds drove unexpected organic traffic. The timing coincided perfectly with Mother\'s Day gifting, amplifying the effect.',
    confidence: 94,
    sources: ['sales_data', 'marketing_campaigns', 'social_media_analytics'],
    followUpQuestions: [
      'Which smart home devices performed best?',
      'How can we replicate this social media success?',
      'What was the profit margin on mobile accessories?'
    ],
    timestamp: new Date(Date.now() - 30 * 60 * 1000),
    feedback: 'positive'
  },
  {
    id: 'resp-2',
    query: 'How do our customer retention rates compare to industry benchmarks?',
    response: 'Our current 6-month retention rate of 60% significantly exceeds the e-commerce industry average of 43%. We\'re performing particularly well in the electronics category (68% vs 51% industry average) and home goods (65% vs 45%). However, our fashion category lags slightly at 52% compared to the fashion e-commerce average of 55%.',
    confidence: 87,
    sources: ['retention_analytics', 'industry_benchmarks', 'category_performance'],
    followUpQuestions: [
      'What\'s driving strong electronics retention?',
      'How can we improve fashion category retention?',
      'What are the retention drivers across categories?'
    ],
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000)
  }
];

// Explanation card component
const ExplanationCard: React.FC<{
  explanation: NLExplanation;
  onFeedback?: (feedback: string) => void;
}> = ({ explanation, onFeedback }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);

  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'simple': return 'bg-green-100 text-green-800';
      case 'intermediate': return 'bg-yellow-100 text-yellow-800';
      case 'advanced': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getAudienceIcon = (audience: string) => {
    switch (audience) {
      case 'executive': return <Target className="w-4 h-4" />;
      case 'manager': return <Users className="w-4 h-4" />;
      case 'analyst': return <BarChart3 className="w-4 h-4" />;
      case 'general': return <Globe className="w-4 h-4" />;
      default: return <Users className="w-4 h-4" />;
    }
  };

  return (
    <Card className="hover:shadow-lg transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3">
            <MessageSquare className="w-5 h-5 text-blue-600 mt-1" />
            <div className="flex-1">
              <div className="flex items-center space-x-2 mb-2">
                <Badge className={getComplexityColor(explanation.complexity)}>
                  {explanation.complexity}
                </Badge>
                <div className="flex items-center space-x-1">
                  {getAudienceIcon(explanation.audience)}
                  <span className="text-sm text-gray-600 capitalize">{explanation.audience}</span>
                </div>
                <Badge variant="outline">
                  {explanation.confidence}% confidence
                </Badge>
              </div>
              <p className="text-sm text-gray-600">{explanation.context.chartType.replace('_', ' ')} • {explanation.context.timeRange.replace('_', ' ')}</p>
            </div>
          </div>
          <div className="flex space-x-1">
            {explanation.audioGenerated && (
              <Button 
                variant="ghost" 
                size="sm"
                onClick={() => setIsPlaying(!isPlaying)}
              >
                {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
              </Button>
            )}
            <Button variant="ghost" size="sm">
              <Copy className="w-4 h-4" />
            </Button>
            <Button variant="ghost" size="sm">
              <Share className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <h4 className="font-medium text-lg mb-2">Summary</h4>
            <p className="text-gray-700">{explanation.explanation.summary}</p>
          </div>

          {isExpanded && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="space-y-4"
            >
              <div>
                <h4 className="font-medium mb-2">Detailed Analysis</h4>
                <p className="text-gray-700 text-sm">{explanation.explanation.detailed}</p>
              </div>

              <div>
                <h4 className="font-medium mb-2">Key Insights</h4>
                <ul className="space-y-1">
                  {explanation.explanation.keyInsights.map((insight, index) => (
                    <li key={index} className="text-sm text-gray-700 flex items-start space-x-2">
                      <Lightbulb className="w-3 h-3 text-yellow-500 mt-0.5 flex-shrink-0" />
                      <span>{insight}</span>
                    </li>
                  ))}
                </ul>
              </div>

              <div>
                <h4 className="font-medium mb-2">Recommendations</h4>
                <ul className="space-y-1">
                  {explanation.explanation.recommendations.map((rec, index) => (
                    <li key={index} className="text-sm text-gray-700 flex items-start space-x-2">
                      <Target className="w-3 h-3 text-blue-500 mt-0.5 flex-shrink-0" />
                      <span>{rec}</span>
                    </li>
                  ))}
                </ul>
              </div>

              <div>
                <h4 className="font-medium mb-2">Narrative</h4>
                <div className="p-4 bg-gray-50 rounded-lg">
                  <p className="text-sm text-gray-700 mb-3 italic">{explanation.narrative.intro}</p>
                  {explanation.narrative.body.map((paragraph, index) => (
                    <p key={index} className="text-sm text-gray-700 mb-2">{paragraph}</p>
                  ))}
                  <p className="text-sm text-gray-700 font-medium">{explanation.narrative.conclusion}</p>
                </div>
              </div>

              <div>
                <h4 className="font-medium mb-2">Related Questions</h4>
                <div className="space-y-1">
                  {explanation.relatedQuestions.map((question, index) => (
                    <button
                      key={index}
                      className="text-sm text-blue-600 hover:text-blue-800 hover:underline block text-left"
                    >
                      {question}
                    </button>
                  ))}
                </div>
              </div>
            </motion.div>
          )}

          <div className="flex items-center justify-between pt-3 border-t">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsExpanded(!isExpanded)}
              >
                {isExpanded ? 'Show Less' : 'Show More'}
              </Button>
              <div className="flex items-center space-x-2 text-sm text-gray-500">
                <ThumbsUp className="w-4 h-4" />
                <span>{explanation.feedback.helpful}</span>
                <ThumbsDown className="w-4 h-4 ml-2" />
                <span>{explanation.feedback.notHelpful}</span>
              </div>
            </div>
            <div className="flex space-x-2">
              <Button variant="outline" size="sm" onClick={() => onFeedback?.('helpful')}>
                <ThumbsUp className="w-4 h-4" />
              </Button>
              <Button variant="outline" size="sm" onClick={() => onFeedback?.('not_helpful')}>
                <ThumbsDown className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Data story card component
const DataStoryCard: React.FC<{
  story: DataStory;
}> = ({ story }) => {
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'bg-green-100 text-green-800';
      case 'intermediate': return 'bg-yellow-100 text-yellow-800';
      case 'advanced': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card className="hover:shadow-lg transition-shadow">
      <CardHeader>
        <div className="flex items-start justify-between">
          <div>
            <CardTitle className="text-lg">{story.title}</CardTitle>
            <p className="text-sm text-gray-600 mt-1">{story.description}</p>
          </div>
          <Badge className={getDifficultyColor(story.difficulty)}>
            {story.difficulty}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="grid grid-cols-3 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Duration:</span>
              <div className="font-medium">{story.duration}</div>
            </div>
            <div>
              <span className="text-gray-600">Chapters:</span>
              <div className="font-medium">{story.chapters.length}</div>
            </div>
            <div>
              <span className="text-gray-600">Views:</span>
              <div className="font-medium">{story.views}</div>
            </div>
          </div>

          <div>
            <h4 className="font-medium mb-2">Chapters</h4>
            <div className="space-y-2">
              {story.chapters.map((chapter, index) => (
                <div key={chapter.id} className="flex items-center space-x-3 p-2 bg-gray-50 rounded">
                  <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center text-xs font-medium text-blue-600">
                    {index + 1}
                  </div>
                  <div className="flex-1">
                    <div className="text-sm font-medium">{chapter.title}</div>
                    <div className="text-xs text-gray-600">{chapter.duration}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div>
            <div className="flex flex-wrap gap-1">
              {story.tags.map((tag, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {tag}
                </Badge>
              ))}
            </div>
          </div>

          <div className="flex justify-between items-center pt-3 border-t">
            <div className="flex items-center space-x-4 text-sm text-gray-500">
              <div className="flex items-center space-x-1">
                <Eye className="w-4 h-4" />
                <span>{story.views}</span>
              </div>
              <div className="flex items-center space-x-1">
                <ThumbsUp className="w-4 h-4" />
                <span>{story.likes}</span>
              </div>
            </div>
            <Button size="sm">
              <Play className="w-4 h-4 mr-1" />
              Play Story
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Conversational interface component
const ConversationalInterface: React.FC<{
  responses: ConversationalResponse[];
}> = ({ responses }) => {
  const [query, setQuery] = useState('');
  const [isListening, setIsListening] = useState(false);

  return (
    <div className="space-y-4">
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center space-x-4">
            <div className="flex-1 relative">
              <Input
                placeholder="Ask me anything about your data..."
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                className="pr-20"
              />
              <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex space-x-1">
                <Button 
                  variant="ghost" 
                  size="sm"
                  onClick={() => setIsListening(!isListening)}
                >
                  <Mic className={`w-4 h-4 ${isListening ? 'text-red-500' : ''}`} />
                </Button>
                <Button variant="ghost" size="sm">
                  <Search className="w-4 h-4" />
                </Button>
              </div>
            </div>
            <Button>Ask</Button>
          </div>

          <div className="mt-4 flex flex-wrap gap-2">
            <span className="text-sm text-gray-600">Suggested questions:</span>
            {[
              'Why did revenue increase last month?',
              'Which products are trending?',
              'How is customer retention?',
              'What are the top opportunities?'
            ].map((suggestion, index) => (
              <button
                key={index}
                className="text-xs px-2 py-1 bg-gray-100 hover:bg-gray-200 rounded text-gray-700"
                onClick={() => setQuery(suggestion)}
              >
                {suggestion}
              </button>
            ))}
          </div>
        </CardContent>
      </Card>

      <div className="space-y-4">
        {responses.map((response, index) => (
          <motion.div
            key={response.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card>
              <CardContent className="p-6">
                <div className="space-y-4">
                  <div>
                    <div className="flex items-center space-x-2 mb-2">
                      <Brain className="w-4 h-4 text-purple-600" />
                      <span className="font-medium">{response.query}</span>
                      <Badge variant="outline">{response.confidence}% confidence</Badge>
                    </div>
                    <p className="text-gray-700">{response.response}</p>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium mb-2">Sources:</h4>
                    <div className="flex space-x-2">
                      {response.sources.map((source, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {source.replace('_', ' ')}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium mb-2">Follow-up questions:</h4>
                    <div className="space-y-1">
                      {response.followUpQuestions.map((question, index) => (
                        <button
                          key={index}
                          className="text-sm text-blue-600 hover:text-blue-800 hover:underline block text-left"
                          onClick={() => setQuery(question)}
                        >
                          {question}
                        </button>
                      ))}
                    </div>
                  </div>

                  <div className="flex items-center justify-between pt-3 border-t">
                    <span className="text-xs text-gray-500">
                      {response.timestamp.toLocaleString()}
                    </span>
                    <div className="flex space-x-2">
                      <Button variant="ghost" size="sm">
                        <Copy className="w-4 h-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Volume2 className="w-4 h-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <ThumbsUp className="w-4 h-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <ThumbsDown className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>
    </div>
  );
};

// Main component
const NaturalLanguageExplanations: React.FC<NaturalLanguageExplanationsProps> = ({
  onExplanationRequest,
  onFeedback,
  className = ''
}) => {
  const [activeTab, setActiveTab] = useState('explanations');
  const [searchTerm, setSearchTerm] = useState('');
  
  const explanations = useMemo(() => generateNLExplanations(), []);
  const dataStories = useMemo(() => generateDataStories(), []);
  const conversationalResponses = useMemo(() => generateConversationalResponses(), []);

  const filteredExplanations = useMemo(() => {
    if (!searchTerm) return explanations;
    return explanations.filter(explanation =>
      explanation.explanation.summary.toLowerCase().includes(searchTerm.toLowerCase()) ||
      explanation.context.chartType.toLowerCase().includes(searchTerm.toLowerCase()) ||
      explanation.audience.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [explanations, searchTerm]);

  const handleFeedback = useCallback((explanationId: string, feedback: string) => {
    onFeedback?.(explanationId, feedback);
    console.log(`Feedback ${feedback} for explanation ${explanationId}`);
  }, [onFeedback]);

  const stats = useMemo(() => {
    const totalExplanations = explanations.length;
    const avgConfidence = explanations.reduce((sum, e) => sum + e.confidence, 0) / explanations.length;
    const totalFeedback = explanations.reduce((sum, e) => sum + e.feedback.helpful + e.feedback.notHelpful, 0);
    const satisfactionRate = explanations.reduce((sum, e) => sum + e.feedback.helpful, 0) / totalFeedback * 100;
    
    return { totalExplanations, avgConfidence, totalFeedback, satisfactionRate };
  }, [explanations]);

  return (
    <div className={`space-y-6 ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <MessageSquare className="w-6 h-6 text-blue-600" />
          <h2 className="text-2xl font-bold">Natural Language Explanations</h2>
          <Badge className="bg-blue-100 text-blue-800">Phase 10</Badge>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">
            <Languages className="w-4 h-4 mr-2" />
            Language Settings
          </Button>
          <Button variant="outline">
            <Settings className="w-4 h-4 mr-2" />
            Configure AI
          </Button>
        </div>
      </div>

      {/* NL Explanations Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Generated Explanations</p>
                <p className="text-3xl font-bold text-blue-600">{stats.totalExplanations}</p>
                <p className="text-xs text-blue-600 mt-1">Auto-generated</p>
              </div>
              <MessageSquare className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Avg Confidence</p>
                <p className="text-3xl font-bold text-green-600">{stats.avgConfidence.toFixed(1)}%</p>
                <p className="text-xs text-green-600 mt-1">AI accuracy</p>
              </div>
              <Brain className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">User Feedback</p>
                <p className="text-3xl font-bold text-purple-600">{stats.totalFeedback}</p>
                <p className="text-xs text-purple-600 mt-1">Total responses</p>
              </div>
              <ThumbsUp className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Satisfaction Rate</p>
                <p className="text-3xl font-bold text-orange-600">{stats.satisfactionRate.toFixed(1)}%</p>
                <p className="text-xs text-orange-600 mt-1">Positive feedback</p>
              </div>
              <Sparkles className="w-8 h-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="explanations">AI Explanations</TabsTrigger>
          <TabsTrigger value="conversation">Ask Questions</TabsTrigger>
          <TabsTrigger value="stories">Data Stories</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="explanations" className="space-y-6">
          <div className="flex items-center space-x-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search explanations..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button variant="outline">
              <Filter className="w-4 h-4 mr-2" />
              Filter
            </Button>
          </div>

          <div className="space-y-6">
            {filteredExplanations.map((explanation, index) => (
              <motion.div
                key={explanation.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <ExplanationCard
                  explanation={explanation}
                  onFeedback={(feedback) => handleFeedback(explanation.id, feedback)}
                />
              </motion.div>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="conversation" className="space-y-6">
          <ConversationalInterface responses={conversationalResponses} />
        </TabsContent>

        <TabsContent value="stories" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {dataStories.map((story, index) => (
              <motion.div
                key={story.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <DataStoryCard story={story} />
              </motion.div>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="settings" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Language Preferences</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium mb-2 block">Primary Language</label>
                    <select className="w-full p-2 border rounded-md">
                      <option value="en">English</option>
                      <option value="es">Spanish</option>
                      <option value="fr">French</option>
                      <option value="de">German</option>
                      <option value="zh">Chinese</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium mb-2 block">Complexity Level</label>
                    <select className="w-full p-2 border rounded-md">
                      <option value="simple">Simple</option>
                      <option value="intermediate">Intermediate</option>
                      <option value="advanced">Advanced</option>
                      <option value="auto">Auto-detect</option>
                    </select>
                  </div>

                  <div>
                    <label className="text-sm font-medium mb-2 block">Target Audience</label>
                    <select className="w-full p-2 border rounded-md">
                      <option value="executive">Executive</option>
                      <option value="manager">Manager</option>
                      <option value="analyst">Analyst</option>
                      <option value="general">General</option>
                    </select>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Voice & Audio Settings</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Enable voice narration</span>
                    <Button variant="outline" size="sm">
                      <Volume2 className="w-4 h-4" />
                    </Button>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium mb-2 block">Voice Type</label>
                    <select className="w-full p-2 border rounded-md">
                      <option value="professional">Professional</option>
                      <option value="casual">Casual</option>
                      <option value="technical">Technical</option>
                    </select>
                  </div>

                  <div>
                    <label className="text-sm font-medium mb-2 block">Speech Speed</label>
                    <input type="range" min="0.5" max="2" step="0.1" className="w-full" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default NaturalLanguageExplanations;
export { type NLExplanation, type DataStory, type ConversationalResponse, type ExplanationRequest };