import { ReactNode } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useAuth } from '../App';
import { 
  BarChart3, 
  Link as LinkIcon, 
  Home, 
  LogOut, 
  User,
  Menu,
  X,
  TrendingUp,
  Settings,
  Palette,
  Zap,
  Globe,
  Eye,
  Sparkles,
  Brain,
  Shield,
  Layers,
  Wand2,
  Network,
  Database,
  LineChart,
  FileText,
  Cpu,
  Target,
  Crown,
  Brush
} from 'lucide-react';
import { useState } from 'react';

interface LayoutProps {
  children: ReactNode;
}

export default function Layout({ children }: LayoutProps) {
  const { user, logout } = useAuth();
  const location = useLocation();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const navigation = [
    { name: 'Dashboard', href: '/dashboard', icon: Home },
    { name: 'Links', href: '/links', icon: LinkIcon },
    { name: 'Analytics', href: '/analytics', icon: BarChart3 },
    { name: 'Advanced Analytics', href: '/analytics/advanced', icon: TrendingUp },
    { name: 'D3.js Dashboard', href: '/analytics/d3-dashboard', icon: LineChart },
    { 
      name: '2D Visualizations', 
      href: '/2d-visualizations', 
      icon: BarChart3,
      badge: 'Phase 8'
    },
    { 
      name: 'AI Analytics', 
      href: '/ai-analytics', 
      icon: Sparkles,
      badge: 'Phase 8' 
    },
    { 
      name: 'Integration Platform', 
      href: '/phase14', 
      icon: Network,
      badge: 'Phase 14' 
    },
    { 
      name: 'Integration Ecosystem', 
      href: '/phase15', 
      icon: Database,
      badge: 'Phase 15' 
    },
    { 
      name: '2D Visualization Engine', 
      href: '/phase16', 
      icon: LineChart,
      badge: 'Phase 16' 
    },
    { 
      name: 'Reporting & Storytelling', 
      href: '/phase17', 
      icon: FileText,
      badge: 'Phase 17' 
    },
    { 
      name: 'Advanced Analytics Suite', 
      href: '/phase18', 
      icon: TrendingUp,
      badge: 'Phase 18' 
    },
    { 
      name: 'Enterprise Features', 
      href: '/phase19', 
      icon: Shield,
      badge: 'Phase 19' 
    },
    // Metaverse Analytics removed - VR/AR replaced with D3.js visualizations
    { 
      name: 'AI & ML Intelligence', 
      href: '/phase21', 
      icon: Brain,
      badge: 'Phase 21' 
    },
    { 
      name: 'Autonomous Systems', 
      href: '/phase22', 
      icon: Cpu,
      badge: 'Phase 22' 
    },
    { 
      name: 'Zero-Touch Operations', 
      href: '/phase23', 
      icon: Target,
      badge: 'Phase 23' 
    },
    { 
      name: 'Executive Suite', 
      href: '/executive', 
      icon: Crown,
      badge: 'Executive' 
    },
    { 
      name: 'Enhanced UX', 
      href: '/enhanced-ux', 
      icon: Brush,
      badge: 'UX' 
    },
    { 
      name: 'White Label', 
      href: '/white-label', 
      icon: Palette,
      badge: 'Pro' 
    },
    { name: 'Settings', href: '/settings', icon: Settings },
  ];

  const isActive = (path: string) => location.pathname === path;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile sidebar backdrop */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        >
          <div className="fixed inset-0 bg-gray-600 bg-opacity-75" />
        </div>
      )}

      {/* Sidebar */}
      <div
        className={`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform ${
          sidebarOpen ? 'translate-x-0' : '-translate-x-full'
        } transition-transform duration-200 ease-in-out lg:translate-x-0 lg:static lg:inset-0 flex flex-col`}
        style={{ zIndex: 1000 }}
      >
        {/* Fixed Header */}
        <div className="flex items-center justify-between h-16 px-6 border-b border-gray-200 flex-shrink-0">
          <h1 className="text-xl font-bold text-gray-900">Analytics</h1>
          <button
            onClick={() => setSidebarOpen(false)}
            className="lg:hidden p-2 rounded-md hover:bg-gray-100"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Scrollable Navigation */}
        <div className="flex-1 overflow-y-auto">
          <nav className="mt-6 px-3 pb-6">
            {navigation.map((item) => {
              const Icon = item.icon;
              return (
                <Link
                  key={item.name}
                  to={item.href}
                  className={`flex items-center justify-between px-3 py-2 mb-1 rounded-md text-sm font-medium transition-colors ${
                    isActive(item.href)
                      ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                      : 'text-gray-700 hover:bg-gray-100'
                  }`}
                  onClick={() => setSidebarOpen(false)}
                >
                  <div className="flex items-center">
                    <Icon className="mr-3 h-5 w-5" />
                    {item.name}
                  </div>
                  {(item as any).badge && (
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      (item as any).badge === 'New' 
                        ? 'bg-green-100 text-green-800'
                        : (item as any).badge === 'Pro'
                        ? 'bg-amber-100 text-amber-800'
                        : (item as any).badge === 'Executive'
                        ? 'bg-purple-100 text-purple-800'
                        : (item as any).badge === 'UX'
                        ? 'bg-pink-100 text-pink-800'
                        : (item as any).badge.startsWith('Phase 23')
                        ? 'bg-red-100 text-red-800'
                        : (item as any).badge.startsWith('Phase 22')
                        ? 'bg-orange-100 text-orange-800'
                        : (item as any).badge.startsWith('Phase 2')
                        ? 'bg-violet-100 text-violet-800'
                        : (item as any).badge.startsWith('Phase 1')
                        ? 'bg-blue-100 text-blue-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {(item as any).badge}
                    </span>
                  )}
                </Link>
              );
            })}
          </nav>
        </div>

        {/* Fixed User info at bottom */}
        <div className="flex-shrink-0 p-4 border-t border-gray-200 bg-white">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                <User className="h-4 w-4 text-blue-600" />
              </div>
            </div>
            <div className="ml-3 flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 truncate">
                {user?.name || user?.email}
              </p>
              <p className="text-xs text-gray-500 truncate">{user?.email}</p>
            </div>
            <button
              onClick={logout}
              className="ml-3 p-1 rounded-md hover:bg-gray-100 text-gray-400 hover:text-gray-600"
              title="Logout"
            >
              <LogOut className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="lg:pl-64">
        {/* Top bar */}
        <div className="sticky top-0 z-40 bg-white shadow-sm border-b border-gray-200">
          <div className="flex items-center justify-between h-16 px-6">
            <button
              onClick={() => setSidebarOpen(true)}
              className="lg:hidden p-2 rounded-md hover:bg-gray-100"
            >
              <Menu className="h-5 w-5" />
            </button>
            
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-500">
                Last updated: {new Date().toLocaleTimeString()}
              </span>
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className="p-6">
          {children}
        </main>
      </div>
    </div>
  );
}