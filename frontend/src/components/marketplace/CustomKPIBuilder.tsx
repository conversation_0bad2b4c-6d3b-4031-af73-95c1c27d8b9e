/**
 * Custom KPI Builder
 * Drag-and-drop interface for creating custom metrics and KPIs
 */

import React, { useState, useCallback, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { motion, AnimatePresence } from 'framer-motion';
import { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd';
import { 
  Target, 
  Plus, 
  Trash2, 
  Edit, 
  Save, 
  Download, 
  Upload, 
  Share,
  Copy,
  Eye,
  Settings,
  Calculator,
  BarChart3,
  PieChart,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Users,
  ShoppingCart,
  Calendar,
  Filter,
  Layers,
  Zap,
  Database,
  Calculator as FunctionIcon,
  Sigma,
  Percent,
  Hash,
  Clock,
  Globe,
  Activity
} from 'lucide-react';

// Types
interface DataSource {
  id: string;
  name: string;
  type: 'table' | 'view' | 'api' | 'integration';
  fields: DataField[];
  description: string;
  icon: string;
  category: 'sales' | 'marketing' | 'product' | 'finance' | 'operations';
}

interface DataField {
  id: string;
  name: string;
  displayName: string;
  type: 'number' | 'string' | 'date' | 'boolean' | 'currency';
  aggregatable: boolean;
  filterable: boolean;
  description: string;
}

interface KPIFormula {
  id: string;
  operator: 'sum' | 'avg' | 'count' | 'min' | 'max' | 'divide' | 'multiply' | 'subtract' | 'add' | 'percentage';
  operands: (DataField | KPIFormula | number)[];
  label?: string;
}

interface KPIFilter {
  id: string;
  field: DataField;
  operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains' | 'between' | 'in';
  value: any;
  label: string;
}

interface CustomKPI {
  id: string;
  name: string;
  description: string;
  formula: KPIFormula;
  filters: KPIFilter[];
  format: 'number' | 'currency' | 'percentage' | 'time';
  category: string;
  visualization: 'number' | 'chart' | 'gauge' | 'trend';
  target?: number;
  benchmark?: number;
  createdAt: Date;
  createdBy: string;
  isPublic: boolean;
  tags: string[];
}

interface KPITemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  formula: KPIFormula;
  requiredFields: string[];
  icon: string;
  popularity: number;
}

interface CustomKPIBuilderProps {
  onKPICreate?: (kpi: CustomKPI) => void;
  onKPIUpdate?: (kpiId: string, updates: Partial<CustomKPI>) => void;
  onKPIDelete?: (kpiId: string) => void;
  className?: string;
}

// Mock data
const generateDataSources = (): DataSource[] => [
  {
    id: 'sales_orders',
    name: 'Sales Orders',
    type: 'table',
    description: 'All sales order data with customer and product information',
    icon: 'ShoppingCart',
    category: 'sales',
    fields: [
      {
        id: 'order_id',
        name: 'order_id',
        displayName: 'Order ID',
        type: 'string',
        aggregatable: false,
        filterable: true,
        description: 'Unique order identifier'
      },
      {
        id: 'order_value',
        name: 'order_value',
        displayName: 'Order Value',
        type: 'currency',
        aggregatable: true,
        filterable: true,
        description: 'Total value of the order'
      },
      {
        id: 'customer_id',
        name: 'customer_id',
        displayName: 'Customer ID',
        type: 'string',
        aggregatable: false,
        filterable: true,
        description: 'Customer identifier'
      },
      {
        id: 'order_date',
        name: 'order_date',
        displayName: 'Order Date',
        type: 'date',
        aggregatable: false,
        filterable: true,
        description: 'Date when order was placed'
      },
      {
        id: 'quantity',
        name: 'quantity',
        displayName: 'Quantity',
        type: 'number',
        aggregatable: true,
        filterable: true,
        description: 'Number of items ordered'
      }
    ]
  },
  {
    id: 'customers',
    name: 'Customers',
    type: 'table',
    description: 'Customer profile and demographic data',
    icon: 'Users',
    category: 'marketing',
    fields: [
      {
        id: 'customer_id',
        name: 'customer_id',
        displayName: 'Customer ID',
        type: 'string',
        aggregatable: false,
        filterable: true,
        description: 'Unique customer identifier'
      },
      {
        id: 'signup_date',
        name: 'signup_date',
        displayName: 'Signup Date',
        type: 'date',
        aggregatable: false,
        filterable: true,
        description: 'Date customer signed up'
      },
      {
        id: 'total_spent',
        name: 'total_spent',
        displayName: 'Total Spent',
        type: 'currency',
        aggregatable: true,
        filterable: true,
        description: 'Lifetime customer value'
      },
      {
        id: 'is_active',
        name: 'is_active',
        displayName: 'Is Active',
        type: 'boolean',
        aggregatable: false,
        filterable: true,
        description: 'Whether customer is currently active'
      }
    ]
  },
  {
    id: 'marketing_campaigns',
    name: 'Marketing Campaigns',
    type: 'table',
    description: 'Campaign performance and attribution data',
    icon: 'TrendingUp',
    category: 'marketing',
    fields: [
      {
        id: 'campaign_id',
        name: 'campaign_id',
        displayName: 'Campaign ID',
        type: 'string',
        aggregatable: false,
        filterable: true,
        description: 'Campaign identifier'
      },
      {
        id: 'spend',
        name: 'spend',
        displayName: 'Spend',
        type: 'currency',
        aggregatable: true,
        filterable: true,
        description: 'Amount spent on campaign'
      },
      {
        id: 'impressions',
        name: 'impressions',
        displayName: 'Impressions',
        type: 'number',
        aggregatable: true,
        filterable: true,
        description: 'Number of ad impressions'
      },
      {
        id: 'clicks',
        name: 'clicks',
        displayName: 'Clicks',
        type: 'number',
        aggregatable: true,
        filterable: true,
        description: 'Number of clicks'
      },
      {
        id: 'conversions',
        name: 'conversions',
        displayName: 'Conversions',
        type: 'number',
        aggregatable: true,
        filterable: true,
        description: 'Number of conversions'
      }
    ]
  }
];

const generateKPITemplates = (): KPITemplate[] => [
  {
    id: 'template-1',
    name: 'Average Order Value',
    description: 'Average value per order over time period',
    category: 'sales',
    popularity: 95,
    icon: 'DollarSign',
    requiredFields: ['order_value', 'order_id'],
    formula: {
      id: 'aov-formula',
      operator: 'divide',
      operands: [
        {
          id: 'total-revenue',
          operator: 'sum',
          operands: [],
          label: 'Total Revenue'
        },
        {
          id: 'total-orders',
          operator: 'count',
          operands: [],
          label: 'Total Orders'
        }
      ]
    }
  },
  {
    id: 'template-2',
    name: 'Customer Acquisition Cost',
    description: 'Cost to acquire a new customer',
    category: 'marketing',
    popularity: 88,
    icon: 'Users',
    requiredFields: ['spend', 'conversions'],
    formula: {
      id: 'cac-formula',
      operator: 'divide',
      operands: [
        {
          id: 'total-spend',
          operator: 'sum',
          operands: [],
          label: 'Total Marketing Spend'
        },
        {
          id: 'new-customers',
          operator: 'count',
          operands: [],
          label: 'New Customers'
        }
      ]
    }
  },
  {
    id: 'template-3',
    name: 'Conversion Rate',
    description: 'Percentage of visitors who convert',
    category: 'marketing',
    popularity: 92,
    icon: 'Percent',
    requiredFields: ['conversions', 'clicks'],
    formula: {
      id: 'conversion-rate-formula',
      operator: 'percentage',
      operands: [
        {
          id: 'conversions',
          operator: 'sum',
          operands: [],
          label: 'Conversions'
        },
        {
          id: 'clicks',
          operator: 'sum',
          operands: [],
          label: 'Clicks'
        }
      ]
    }
  },
  {
    id: 'template-4',
    name: 'Monthly Recurring Revenue',
    description: 'Predictable monthly revenue from subscriptions',
    category: 'finance',
    popularity: 87,
    icon: 'TrendingUp',
    requiredFields: ['order_value', 'customer_id'],
    formula: {
      id: 'mrr-formula',
      operator: 'sum',
      operands: [],
      label: 'Monthly Recurring Revenue'
    }
  }
];

// Field draggable component
const FieldItem: React.FC<{
  field: DataField;
  index: number;
  isDragging: boolean;
}> = ({ field, index, isDragging }) => {
  const getFieldIcon = (type: string) => {
    switch (type) {
      case 'currency': return <DollarSign className="w-4 h-4" />;
      case 'number': return <Hash className="w-4 h-4" />;
      case 'date': return <Calendar className="w-4 h-4" />;
      case 'boolean': return <Filter className="w-4 h-4" />;
      default: return <Database className="w-4 h-4" />;
    }
  };

  return (
    <Draggable draggableId={field.id} index={index}>
      {(provided, snapshot) => (
        <div
          ref={provided.innerRef}
          {...provided.draggableProps}
          {...provided.dragHandleProps}
          className={`p-3 bg-white border rounded-lg cursor-move transition-shadow ${
            snapshot.isDragging ? 'shadow-lg' : 'hover:shadow-md'
          }`}
        >
          <div className="flex items-center space-x-2">
            {getFieldIcon(field.type)}
            <div className="flex-1">
              <div className="font-medium text-sm">{field.displayName}</div>
              <div className="text-xs text-gray-500">{field.type}</div>
            </div>
            <div className="flex space-x-1">
              {field.aggregatable && (
                <Badge variant="outline" className="text-xs">AGG</Badge>
              )}
              {field.filterable && (
                <Badge variant="outline" className="text-xs">FILTER</Badge>
              )}
            </div>
          </div>
        </div>
      )}
    </Draggable>
  );
};

// Formula builder component
const FormulaBuilder: React.FC<{
  formula: KPIFormula;
  onChange: (formula: KPIFormula) => void;
  dataSources: DataSource[];
}> = ({ formula, onChange, dataSources }) => {
  const operators = [
    { id: 'sum', name: 'Sum', icon: <Sigma className="w-4 h-4" /> },
    { id: 'avg', name: 'Average', icon: <BarChart3 className="w-4 h-4" /> },
    { id: 'count', name: 'Count', icon: <Hash className="w-4 h-4" /> },
    { id: 'min', name: 'Minimum', icon: <TrendingDown className="w-4 h-4" /> },
    { id: 'max', name: 'Maximum', icon: <TrendingUp className="w-4 h-4" /> },
    { id: 'divide', name: 'Divide', icon: <FunctionIcon className="w-4 h-4" /> },
    { id: 'multiply', name: 'Multiply', icon: <FunctionIcon className="w-4 h-4" /> },
    { id: 'percentage', name: 'Percentage', icon: <Percent className="w-4 h-4" /> }
  ];

  return (
    <div className="space-y-4">
      <div>
        <label className="text-sm font-medium mb-2 block">Operation</label>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
          {operators.map(op => (
            <button
              key={op.id}
              onClick={() => onChange({ ...formula, operator: op.id as any })}
              className={`p-3 border rounded-lg flex items-center space-x-2 transition-colors ${
                formula.operator === op.id ? 'bg-blue-50 border-blue-500' : 'hover:bg-gray-50'
              }`}
            >
              {op.icon}
              <span className="text-sm">{op.name}</span>
            </button>
          ))}
        </div>
      </div>

      <Droppable droppableId="formula-builder" type="FIELD">
        {(provided, snapshot) => (
          <div
            ref={provided.innerRef}
            {...provided.droppableProps}
            className={`min-h-32 border-2 border-dashed rounded-lg p-4 transition-colors ${
              snapshot.isDraggingOver ? 'border-blue-500 bg-blue-50' : 'border-gray-300'
            }`}
          >
            <div className="text-sm text-gray-600 mb-3">
              Drag fields here to build your formula
            </div>
            
            <div className="space-y-2">
              {formula.operands.map((operand, index) => (
                <div key={index} className="p-3 bg-gray-50 rounded-lg flex items-center justify-between">
                  <span className="text-sm">
                    {typeof operand === 'number' ? operand : 'field' in operand ? (operand as DataField).displayName : 'Formula'}
                  </span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      const newOperands = [...formula.operands];
                      newOperands.splice(index, 1);
                      onChange({ ...formula, operands: newOperands });
                    }}
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              ))}
            </div>
            
            {provided.placeholder}
          </div>
        )}
      </Droppable>
    </div>
  );
};

// KPI template gallery
const KPITemplateGallery: React.FC<{
  templates: KPITemplate[];
  onTemplateSelect: (template: KPITemplate) => void;
}> = ({ templates, onTemplateSelect }) => {
  const getTemplateIcon = (icon: string) => {
    const iconMap: { [key: string]: React.ReactNode } = {
      'DollarSign': <DollarSign className="w-6 h-6" />,
      'Users': <Users className="w-6 h-6" />,
      'Percent': <Percent className="w-6 h-6" />,
      'TrendingUp': <TrendingUp className="w-6 h-6" />
    };
    return iconMap[icon] || <Target className="w-6 h-6" />;
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {templates.map((template, index) => (
        <motion.div
          key={template.id}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.1 }}
        >
          <Card className="cursor-pointer hover:shadow-lg transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center text-blue-600">
                  {getTemplateIcon(template.icon)}
                </div>
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-green-500 rounded-full" />
                  <span className="text-xs text-gray-600">{template.popularity}% popular</span>
                </div>
              </div>
              
              <h3 className="font-semibold mb-2">{template.name}</h3>
              <p className="text-sm text-gray-600 mb-4">{template.description}</p>
              
              <div className="flex items-center justify-between">
                <Badge variant="outline" className="capitalize">
                  {template.category}
                </Badge>
                <Button
                  size="sm"
                  onClick={() => onTemplateSelect(template)}
                >
                  Use Template
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      ))}
    </div>
  );
};

// Main component
const CustomKPIBuilder: React.FC<CustomKPIBuilderProps> = ({
  onKPICreate,
  onKPIUpdate,
  onKPIDelete,
  className = ''
}) => {
  const [activeTab, setActiveTab] = useState('builder');
  const [selectedDataSource, setSelectedDataSource] = useState<DataSource | null>(null);
  const [currentKPI, setCurrentKPI] = useState<Partial<CustomKPI>>({
    name: '',
    description: '',
    formula: {
      id: 'formula-1',
      operator: 'sum',
      operands: []
    },
    filters: [],
    format: 'number',
    category: 'custom',
    visualization: 'number',
    tags: []
  });
  
  const dataSources = useMemo(() => generateDataSources(), []);
  const templates = useMemo(() => generateKPITemplates(), []);

  const handleDragEnd = useCallback((result: any) => {
    if (!result.destination) return;

    const { source, destination } = result;
    
    if (destination.droppableId === 'formula-builder') {
      // Find the field being dragged
      const sourceDataSource = dataSources.find(ds => 
        ds.fields.some(f => f.id === result.draggableId)
      );
      const field = sourceDataSource?.fields.find(f => f.id === result.draggableId);
      
      if (field && currentKPI.formula) {
        const newOperands = [...currentKPI.formula.operands];
        newOperands.splice(destination.index, 0, field);
        
        setCurrentKPI({
          ...currentKPI,
          formula: {
            ...currentKPI.formula,
            operands: newOperands
          }
        });
      }
    }
  }, [dataSources, currentKPI]);

  const handleTemplateSelect = useCallback((template: KPITemplate) => {
    setCurrentKPI({
      ...currentKPI,
      name: template.name,
      description: template.description,
      formula: template.formula,
      category: template.category
    });
    setActiveTab('builder');
  }, [currentKPI]);

  const handleSaveKPI = useCallback(() => {
    if (currentKPI.name && currentKPI.formula) {
      const newKPI: CustomKPI = {
        id: `kpi-${Date.now()}`,
        name: currentKPI.name,
        description: currentKPI.description || '',
        formula: currentKPI.formula,
        filters: currentKPI.filters || [],
        format: currentKPI.format || 'number',
        category: currentKPI.category || 'custom',
        visualization: currentKPI.visualization || 'number',
        target: currentKPI.target,
        benchmark: currentKPI.benchmark,
        createdAt: new Date(),
        createdBy: 'current-user',
        isPublic: false,
        tags: currentKPI.tags || []
      };
      
      onKPICreate?.(newKPI);
      
      // Reset form
      setCurrentKPI({
        name: '',
        description: '',
        formula: {
          id: 'formula-new',
          operator: 'sum',
          operands: []
        },
        filters: [],
        format: 'number',
        category: 'custom',
        visualization: 'number',
        tags: []
      });
    }
  }, [currentKPI, onKPICreate]);

  return (
    <div className={`space-y-6 ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Target className="w-6 h-6 text-green-600" />
          <h2 className="text-2xl font-bold">Custom KPI Builder</h2>
          <Badge className="bg-green-100 text-green-800">Phase 9</Badge>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">
            <Upload className="w-4 h-4 mr-2" />
            Import
          </Button>
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="templates">KPI Templates</TabsTrigger>
          <TabsTrigger value="builder">Formula Builder</TabsTrigger>
          <TabsTrigger value="preview">Preview & Test</TabsTrigger>
        </TabsList>

        <TabsContent value="templates" className="space-y-6">
          <div>
            <h3 className="text-lg font-semibold mb-4">Popular KPI Templates</h3>
            <p className="text-gray-600 mb-6">
              Start with pre-built formulas for common business metrics
            </p>
            <KPITemplateGallery
              templates={templates}
              onTemplateSelect={handleTemplateSelect}
            />
          </div>
        </TabsContent>

        <TabsContent value="builder" className="space-y-6">
          <DragDropContext onDragEnd={handleDragEnd}>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Data Sources */}
              <div className="space-y-4">
                <h3 className="font-semibold">Data Sources</h3>
                <div className="space-y-3">
                  {dataSources.map(source => (
                    <Card
                      key={source.id}
                      className={`cursor-pointer transition-colors ${
                        selectedDataSource?.id === source.id ? 'bg-blue-50 border-blue-500' : ''
                      }`}
                      onClick={() => setSelectedDataSource(source)}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-center space-x-3">
                          <div className="w-8 h-8 bg-gray-100 rounded flex items-center justify-center">
                            <Database className="w-4 h-4" />
                          </div>
                          <div>
                            <div className="font-medium text-sm">{source.name}</div>
                            <div className="text-xs text-gray-500">{source.fields.length} fields</div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>

                {/* Fields */}
                {selectedDataSource && (
                  <div>
                    <h4 className="font-medium mb-3">Available Fields</h4>
                    <Droppable droppableId="fields" type="FIELD">
                      {(provided) => (
                        <div
                          ref={provided.innerRef}
                          {...provided.droppableProps}
                          className="space-y-2"
                        >
                          {selectedDataSource.fields.map((field, index) => (
                            <FieldItem
                              key={field.id}
                              field={field}
                              index={index}
                              isDragging={false}
                            />
                          ))}
                          {provided.placeholder}
                        </div>
                      )}
                    </Droppable>
                  </div>
                )}
              </div>

              {/* Formula Builder */}
              <div className="lg:col-span-2 space-y-6">
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium mb-2 block">KPI Name</label>
                    <Input
                      value={currentKPI.name || ''}
                      onChange={(e) => setCurrentKPI({ ...currentKPI, name: e.target.value })}
                      placeholder="Enter KPI name..."
                    />
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium mb-2 block">Description</label>
                    <Input
                      value={currentKPI.description || ''}
                      onChange={(e) => setCurrentKPI({ ...currentKPI, description: e.target.value })}
                      placeholder="Describe what this KPI measures..."
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium mb-2 block">Format</label>
                      <select 
                        value={currentKPI.format}
                        onChange={(e) => setCurrentKPI({ ...currentKPI, format: e.target.value as any })}
                        className="w-full p-2 border rounded-md"
                      >
                        <option value="number">Number</option>
                        <option value="currency">Currency</option>
                        <option value="percentage">Percentage</option>
                        <option value="time">Time</option>
                      </select>
                    </div>
                    
                    <div>
                      <label className="text-sm font-medium mb-2 block">Visualization</label>
                      <select 
                        value={currentKPI.visualization}
                        onChange={(e) => setCurrentKPI({ ...currentKPI, visualization: e.target.value as any })}
                        className="w-full p-2 border rounded-md"
                      >
                        <option value="number">Number</option>
                        <option value="chart">Chart</option>
                        <option value="gauge">Gauge</option>
                        <option value="trend">Trend</option>
                      </select>
                    </div>
                  </div>
                </div>

                {currentKPI.formula && (
                  <FormulaBuilder
                    formula={currentKPI.formula}
                    onChange={(formula) => setCurrentKPI({ ...currentKPI, formula })}
                    dataSources={dataSources}
                  />
                )}

                <div className="flex justify-end space-x-2">
                  <Button variant="outline">
                    <Eye className="w-4 h-4 mr-2" />
                    Preview
                  </Button>
                  <Button onClick={handleSaveKPI}>
                    <Save className="w-4 h-4 mr-2" />
                    Save KPI
                  </Button>
                </div>
              </div>
            </div>
          </DragDropContext>
        </TabsContent>

        <TabsContent value="preview" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>KPI Preview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="p-6 border-2 border-dashed border-gray-300 rounded-lg text-center">
                  <div className="text-3xl font-bold text-gray-900 mb-2">
                    {currentKPI.name || 'Untitled KPI'}
                  </div>
                  <div className="text-lg text-gray-600 mb-4">$45,230</div>
                  <div className="text-sm text-gray-500">
                    {currentKPI.description || 'No description provided'}
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-medium mb-2">Formula Preview</h4>
                    <div className="p-3 bg-gray-50 rounded-lg text-sm">
                      {currentKPI.formula?.operator.toUpperCase()} of selected fields
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="font-medium mb-2">Sample Data</h4>
                    <div className="p-3 bg-gray-50 rounded-lg text-sm">
                      Based on last 30 days of data
                    </div>
                  </div>
                </div>

                <div>
                  <Button className="w-full">
                    <Save className="w-4 h-4 mr-2" />
                    Create KPI
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default CustomKPIBuilder;
export { type CustomKPI, type KPITemplate, type DataSource, type KPIFormula };