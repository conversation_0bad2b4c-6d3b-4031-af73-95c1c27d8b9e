import { render, screen, fireEvent } from '@testing-library/react';
import { expect, it, describe, vi, beforeEach } from 'vitest';
import ErrorBoundary from '../ErrorBoundary';
import ApiErrorBoundary from '../ApiErrorBoundary';
import { errorReporting } from '../../services/errorReporting';

// Mock Sentry
vi.mock('@sentry/react', () => ({
  setUser: vi.fn(),
  captureException: vi.fn(),
  captureMessage: vi.fn(),
  setContext: vi.fn(),
  setTag: vi.fn(),
  addBreadcrumb: vi.fn(),
}));

// Test component that throws an error
const ThrowError = ({ shouldThrow, errorType = 'generic' }: { 
  shouldThrow: boolean; 
  errorType?: string;
}) => {
  if (shouldThrow) {
    if (errorType === 'api') {
      throw new Error('API request failed');
    } else if (errorType === 'network') {
      throw new Error('Network fetch failed');
    } else {
      throw new Error('Test error');
    }
  }
  return <div>No error</div>;
};

describe('Error Reporting Integration', () => {
  beforeEach(() => {
    // Clear error metrics before each test
    errorReporting.clearMetrics();
    vi.clearAllMocks();
  });

  describe('ErrorBoundary with Error Reporting', () => {
    it('reports component errors to error reporting service', () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      const reportSpy = vi.spyOn(errorReporting, 'reportComponentError');

      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      );

      expect(reportSpy).toHaveBeenCalledWith(
        expect.any(Error),
        'ErrorBoundary',
        expect.objectContaining({
          componentStack: expect.any(String),
          errorBoundary: true,
        })
      );

      consoleSpy.mockRestore();
    });

    it('updates error metrics when error occurs', () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      );

      const metrics = errorReporting.getErrorMetrics();
      expect(metrics.errorCount).toBe(1);
      expect(metrics.errorTypes['Error']).toBe(1);
      expect(metrics.componentErrors['ErrorBoundary']).toBe(1);

      consoleSpy.mockRestore();
    });
  });

  describe('ApiErrorBoundary with Error Reporting', () => {
    it('reports API errors with enhanced context', () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      const reportSpy = vi.spyOn(errorReporting, 'reportApiError');

      render(
        <ApiErrorBoundary resourceName="users">
          <ThrowError shouldThrow={true} errorType="api" />
        </ApiErrorBoundary>
      );

      expect(reportSpy).toHaveBeenCalledWith(
        expect.any(Error),
        'users',
        'GET',
        undefined,
        expect.objectContaining({
          isNetworkError: false,
          resourceName: 'users',
          timestamp: expect.any(String),
        })
      );

      consoleSpy.mockRestore();
    });

    it('detects and reports network errors', () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      const reportSpy = vi.spyOn(errorReporting, 'reportApiError');

      render(
        <ApiErrorBoundary resourceName="api-endpoint">
          <ThrowError shouldThrow={true} errorType="network" />
        </ApiErrorBoundary>
      );

      expect(reportSpy).toHaveBeenCalledWith(
        expect.any(Error),
        'api-endpoint',
        'GET',
        undefined,
        expect.objectContaining({
          isNetworkError: true,
          resourceName: 'api-endpoint',
        })
      );

      consoleSpy.mockRestore();
    });
  });

  describe('Error Reporting Service', () => {
    it('tracks error metrics correctly', () => {
      const error1 = new Error('First error');
      const error2 = new TypeError('Second error');

      errorReporting.reportError(error1, { component: 'TestComponent1' });
      errorReporting.reportError(error2, { component: 'TestComponent2' });

      const metrics = errorReporting.getErrorMetrics();
      expect(metrics.errorCount).toBe(2);
      expect(metrics.errorTypes['Error']).toBe(1);
      expect(metrics.errorTypes['TypeError']).toBe(1);
      expect(metrics.componentErrors['TestComponent1']).toBe(1);
      expect(metrics.componentErrors['TestComponent2']).toBe(1);
    });

    it('adds breadcrumbs correctly', () => {
      errorReporting.addBreadcrumb('User clicked button', 'user', 'info');
      errorReporting.addBreadcrumb('API call started', 'api', 'info');
      errorReporting.addBreadcrumb('Error occurred', 'error', 'error');

      const breadcrumbs = errorReporting.getBreadcrumbs();
      expect(breadcrumbs).toHaveLength(3);
      expect(breadcrumbs[0].message).toBe('User clicked button');
      expect(breadcrumbs[1].message).toBe('API call started');
      expect(breadcrumbs[2].message).toBe('Error occurred');
    });

    it('limits breadcrumbs to maximum count', () => {
      // Add more than the maximum number of breadcrumbs
      for (let i = 0; i < 60; i++) {
        errorReporting.addBreadcrumb(`Breadcrumb ${i}`, 'test', 'info');
      }

      const breadcrumbs = errorReporting.getBreadcrumbs();
      expect(breadcrumbs.length).toBeLessThanOrEqual(50); // MAX_BREADCRUMBS
    });

    it('reports warnings correctly', () => {
      const reportSpy = vi.spyOn(errorReporting, 'reportWarning');
      
      errorReporting.reportWarning('This is a warning', {
        component: 'TestComponent',
        action: 'test-action',
      });

      expect(reportSpy).toHaveBeenCalledWith(
        'This is a warning',
        expect.objectContaining({
          component: 'TestComponent',
          action: 'test-action',
        })
      );
    });

    it('handles user context correctly', () => {
      errorReporting.setUser('user123', '<EMAIL>', 'tenant456');
      
      // Verify that user context is set (would need to mock Sentry to fully test)
      expect(true).toBe(true); // Placeholder assertion
      
      errorReporting.clearUser();
      
      // Verify that user context is cleared
      expect(true).toBe(true); // Placeholder assertion
    });
  });

  describe('Error Recovery', () => {
    it('allows retry after error in ErrorBoundary', () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      const { rerender } = render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      );

      expect(screen.getByText('Something went wrong')).toBeInTheDocument();

      // Click retry button
      fireEvent.click(screen.getByRole('button', { name: 'Try Again' }));

      // Rerender with no error
      rerender(
        <ErrorBoundary>
          <ThrowError shouldThrow={false} />
        </ErrorBoundary>
      );

      expect(screen.getByText('No error')).toBeInTheDocument();
      expect(screen.queryByText('Something went wrong')).not.toBeInTheDocument();

      consoleSpy.mockRestore();
    });

    it('provides retry functionality in ApiErrorBoundary', () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      const retryFn = vi.fn();

      render(
        <ApiErrorBoundary retry={retryFn} resourceName="test-api">
          <ThrowError shouldThrow={true} errorType="api" />
        </ApiErrorBoundary>
      );

      expect(screen.getByText(/Failed to load test-api/)).toBeInTheDocument();

      // Click retry button
      const retryButton = screen.getByRole('button', { name: 'Try Again' });
      fireEvent.click(retryButton);

      expect(retryFn).toHaveBeenCalled();

      consoleSpy.mockRestore();
    });
  });
});
