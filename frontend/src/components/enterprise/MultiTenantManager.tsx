/**
 * Multi-Tenant Architecture Manager
 * Complete tenant isolation with custom domains and resource management
 */

import React, { useState, useMemo, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { motion } from 'framer-motion';
import { 
  Building2, 
  Users, 
  Globe, 
  Database,
  Shield,
  Cpu,
  HardDrive,
  Network,
  Settings,
  Plus,
  Edit,
  Trash2,
  Eye,
  AlertTriangle,
  CheckCircle,
  Clock,
  DollarSign,
  TrendingUp,
  BarChart3,
  Download,
  RefreshCw,
  Zap
} from 'lucide-react';

// Types
interface Tenant {
  id: string;
  name: string;
  domain: string;
  customDomain?: string;
  plan: 'starter' | 'professional' | 'enterprise' | 'custom';
  status: 'active' | 'suspended' | 'provisioning' | 'inactive';
  createdAt: Date;
  lastActive: Date;
  users: number;
  dataSize: number; // GB
  apiCalls: number;
  monthlyRevenue: number;
  features: string[];
  limits: {
    users: number;
    storage: number; // GB
    apiCalls: number;
    customDomains: number;
    integrations: number;
  };
  usage: {
    users: number;
    storage: number;
    apiCalls: number;
    bandwidth: number; // GB
  };
  metadata: {
    industry: string;
    region: string;
    timezone: string;
    currency: string;
    contactEmail: string;
    billingEmail: string;
  };
}

interface TenantResource {
  tenantId: string;
  type: 'database' | 'storage' | 'compute' | 'network';
  name: string;
  status: 'healthy' | 'warning' | 'critical' | 'offline';
  usage: number; // percentage
  capacity: string;
  location: string;
  lastChecked: Date;
}

interface MultiTenantManagerProps {
  onTenantCreate?: (tenant: Partial<Tenant>) => void;
  onTenantUpdate?: (tenantId: string, updates: Partial<Tenant>) => void;
  onTenantDelete?: (tenantId: string) => void;
  className?: string;
}

// Mock data generation
const generateTenants = (): Tenant[] => [
  {
    id: 'tenant-1',
    name: 'Acme Corporation',
    domain: 'acme-corp',
    customDomain: 'analytics.acme.com',
    plan: 'enterprise',
    status: 'active',
    createdAt: new Date('2024-01-15'),
    lastActive: new Date(),
    users: 247,
    dataSize: 12.4,
    apiCalls: 1250000,
    monthlyRevenue: 9500,
    features: ['white_label', 'sso', 'api_access', 'advanced_analytics', 'priority_support'],
    limits: {
      users: 500,
      storage: 100,
      apiCalls: 2000000,
      customDomains: 5,
      integrations: 50
    },
    usage: {
      users: 247,
      storage: 12.4,
      apiCalls: 1250000,
      bandwidth: 45.2
    },
    metadata: {
      industry: 'Technology',
      region: 'North America',
      timezone: 'UTC-8',
      currency: 'USD',
      contactEmail: '<EMAIL>',
      billingEmail: '<EMAIL>'
    }
  },
  {
    id: 'tenant-2',
    name: 'Global Retail Inc',
    domain: 'global-retail',
    plan: 'professional',
    status: 'active',
    createdAt: new Date('2024-02-20'),
    lastActive: new Date(Date.now() - 2 * 60 * 60 * 1000),
    users: 89,
    dataSize: 4.7,
    apiCalls: 450000,
    monthlyRevenue: 2900,
    features: ['advanced_analytics', 'integrations', 'email_support'],
    limits: {
      users: 100,
      storage: 25,
      apiCalls: 500000,
      customDomains: 2,
      integrations: 15
    },
    usage: {
      users: 89,
      storage: 4.7,
      apiCalls: 450000,
      bandwidth: 18.3
    },
    metadata: {
      industry: 'Retail',
      region: 'Europe',
      timezone: 'UTC+1',
      currency: 'EUR',
      contactEmail: '<EMAIL>',
      billingEmail: '<EMAIL>'
    }
  },
  {
    id: 'tenant-3',
    name: 'StartupXYZ',
    domain: 'startupxyz',
    plan: 'starter',
    status: 'active',
    createdAt: new Date('2024-05-10'),
    lastActive: new Date(Date.now() - 30 * 60 * 1000),
    users: 12,
    dataSize: 0.8,
    apiCalls: 25000,
    monthlyRevenue: 99,
    features: ['basic_analytics', 'email_support'],
    limits: {
      users: 25,
      storage: 5,
      apiCalls: 50000,
      customDomains: 0,
      integrations: 5
    },
    usage: {
      users: 12,
      storage: 0.8,
      apiCalls: 25000,
      bandwidth: 2.1
    },
    metadata: {
      industry: 'Technology',
      region: 'North America',
      timezone: 'UTC-5',
      currency: 'USD',
      contactEmail: '<EMAIL>',
      billingEmail: '<EMAIL>'
    }
  }
];

const generateTenantResources = (): TenantResource[] => [
  {
    tenantId: 'tenant-1',
    type: 'database',
    name: 'Primary Database',
    status: 'healthy',
    usage: 67,
    capacity: '500GB',
    location: 'us-west-2',
    lastChecked: new Date()
  },
  {
    tenantId: 'tenant-1',
    type: 'storage',
    name: 'File Storage',
    status: 'healthy',
    usage: 34,
    capacity: '1TB',
    location: 'us-west-2',
    lastChecked: new Date()
  },
  {
    tenantId: 'tenant-1',
    type: 'compute',
    name: 'Application Server',
    status: 'warning',
    usage: 89,
    capacity: '8 vCPU',
    location: 'us-west-2',
    lastChecked: new Date()
  },
  {
    tenantId: 'tenant-2',
    type: 'database',
    name: 'Primary Database',
    status: 'healthy',
    usage: 45,
    capacity: '200GB',
    location: 'eu-central-1',
    lastChecked: new Date()
  }
];

// Tenant overview component
const TenantOverview: React.FC<{
  tenants: Tenant[];
  onTenantSelect?: (tenant: Tenant) => void;
}> = ({ tenants, onTenantSelect }) => {
  const stats = useMemo(() => {
    const totalRevenue = tenants.reduce((sum, t) => sum + t.monthlyRevenue, 0);
    const totalUsers = tenants.reduce((sum, t) => sum + t.users, 0);
    const activeCount = tenants.filter(t => t.status === 'active').length;
    const avgUsage = tenants.reduce((sum, t) => sum + (t.usage.users / t.limits.users), 0) / tenants.length;
    
    return { totalRevenue, totalUsers, activeCount, avgUsage: avgUsage * 100 };
  }, [tenants]);

  const getPlanColor = (plan: string) => {
    switch (plan) {
      case 'enterprise': return 'bg-purple-100 text-purple-800';
      case 'professional': return 'bg-blue-100 text-blue-800';
      case 'starter': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'suspended': return 'bg-red-100 text-red-800';
      case 'provisioning': return 'bg-yellow-100 text-yellow-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Tenants</p>
                <p className="text-3xl font-bold text-blue-600">{tenants.length}</p>
                <p className="text-xs text-green-600 mt-1">{stats.activeCount} active</p>
              </div>
              <Building2 className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Monthly Revenue</p>
                <p className="text-3xl font-bold text-green-600">${stats.totalRevenue.toLocaleString()}</p>
                <p className="text-xs text-green-600 mt-1">+12.3% vs last month</p>
              </div>
              <DollarSign className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Users</p>
                <p className="text-3xl font-bold text-purple-600">{stats.totalUsers.toLocaleString()}</p>
                <p className="text-xs text-purple-600 mt-1">Across all tenants</p>
              </div>
              <Users className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Avg Utilization</p>
                <p className="text-3xl font-bold text-orange-600">{stats.avgUsage.toFixed(1)}%</p>
                <p className="text-xs text-orange-600 mt-1">Resource usage</p>
              </div>
              <BarChart3 className="w-8 h-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tenants Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Tenant Management</span>
            <div className="flex space-x-2">
              <Button variant="outline" size="sm">
                <RefreshCw className="w-4 h-4 mr-2" />
                Refresh
              </Button>
              <Button size="sm">
                <Plus className="w-4 h-4 mr-2" />
                New Tenant
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b">
                  <th className="text-left p-3 font-medium">Tenant</th>
                  <th className="text-left p-3 font-medium">Plan</th>
                  <th className="text-left p-3 font-medium">Status</th>
                  <th className="text-center p-3 font-medium">Users</th>
                  <th className="text-center p-3 font-medium">Usage</th>
                  <th className="text-center p-3 font-medium">Revenue</th>
                  <th className="text-center p-3 font-medium">Actions</th>
                </tr>
              </thead>
              <tbody>
                {tenants.map((tenant, index) => (
                  <motion.tr
                    key={tenant.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="border-b hover:bg-gray-50 cursor-pointer"
                    onClick={() => onTenantSelect?.(tenant)}
                  >
                    <td className="p-3">
                      <div>
                        <div className="font-medium">{tenant.name}</div>
                        <div className="text-xs text-gray-500">
                          {tenant.customDomain || `${tenant.domain}.analytics.com`}
                        </div>
                        <div className="text-xs text-gray-400">
                          Created {tenant.createdAt.toLocaleDateString()}
                        </div>
                      </div>
                    </td>
                    <td className="p-3">
                      <Badge className={getPlanColor(tenant.plan)}>
                        {tenant.plan}
                      </Badge>
                    </td>
                    <td className="p-3">
                      <Badge className={getStatusColor(tenant.status)}>
                        {tenant.status}
                      </Badge>
                    </td>
                    <td className="p-3 text-center">
                      <div className="font-medium">{tenant.users}</div>
                      <div className="text-xs text-gray-500">
                        of {tenant.limits.users}
                      </div>
                    </td>
                    <td className="p-3 text-center">
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-blue-600 h-2 rounded-full" 
                          style={{ width: `${(tenant.usage.users / tenant.limits.users) * 100}%` }}
                        />
                      </div>
                      <div className="text-xs text-gray-500 mt-1">
                        {((tenant.usage.users / tenant.limits.users) * 100).toFixed(0)}%
                      </div>
                    </td>
                    <td className="p-3 text-center">
                      <div className="font-medium">${tenant.monthlyRevenue}</div>
                      <div className="text-xs text-gray-500">monthly</div>
                    </td>
                    <td className="p-3 text-center">
                      <div className="flex items-center justify-center space-x-1">
                        <Button variant="ghost" size="sm">
                          <Eye className="w-4 h-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Settings className="w-4 h-4" />
                        </Button>
                      </div>
                    </td>
                  </motion.tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// Resource monitoring component
const ResourceMonitoring: React.FC<{
  resources: TenantResource[];
  tenants: Tenant[];
}> = ({ resources, tenants }) => {
  const getResourceIcon = (type: string) => {
    switch (type) {
      case 'database': return <Database className="w-5 h-5" />;
      case 'storage': return <HardDrive className="w-5 h-5" />;
      case 'compute': return <Cpu className="w-5 h-5" />;
      case 'network': return <Network className="w-5 h-5" />;
      default: return <Zap className="w-5 h-5" />;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy': return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'warning': return <AlertTriangle className="w-4 h-4 text-yellow-600" />;
      case 'critical': return <AlertTriangle className="w-4 h-4 text-red-600" />;
      case 'offline': return <Clock className="w-4 h-4 text-gray-600" />;
      default: return <Clock className="w-4 h-4 text-gray-600" />;
    }
  };

  const getTenantName = (tenantId: string) => {
    return tenants.find(t => t.id === tenantId)?.name || 'Unknown';
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Resources</p>
                <p className="text-3xl font-bold text-blue-600">{resources.length}</p>
              </div>
              <Database className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Healthy</p>
                <p className="text-3xl font-bold text-green-600">
                  {resources.filter(r => r.status === 'healthy').length}
                </p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Warnings</p>
                <p className="text-3xl font-bold text-yellow-600">
                  {resources.filter(r => r.status === 'warning').length}
                </p>
              </div>
              <AlertTriangle className="w-8 h-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Avg Usage</p>
                <p className="text-3xl font-bold text-purple-600">
                  {Math.round(resources.reduce((sum, r) => sum + r.usage, 0) / resources.length)}%
                </p>
              </div>
              <BarChart3 className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {resources.map((resource, index) => (
          <motion.div
            key={`${resource.tenantId}-${resource.type}-${resource.name}`}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card>
              <CardContent className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                      {getResourceIcon(resource.type)}
                    </div>
                    <div>
                      <h3 className="font-medium">{resource.name}</h3>
                      <p className="text-sm text-gray-600">{getTenantName(resource.tenantId)}</p>
                      <p className="text-xs text-gray-500">{resource.location}</p>
                    </div>
                  </div>
                  {getStatusIcon(resource.status)}
                </div>

                <div className="space-y-3">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Usage</span>
                      <span>{resource.usage}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${
                          resource.usage > 90 ? 'bg-red-500' :
                          resource.usage > 70 ? 'bg-yellow-500' : 'bg-green-500'
                        }`}
                        style={{ width: `${resource.usage}%` }}
                      />
                    </div>
                  </div>

                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Capacity:</span>
                    <span className="font-medium">{resource.capacity}</span>
                  </div>

                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Last Check:</span>
                    <span className="font-medium">
                      {resource.lastChecked.toLocaleTimeString()}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>
    </div>
  );
};

// Main component
const MultiTenantManager: React.FC<MultiTenantManagerProps> = ({
  onTenantCreate,
  onTenantUpdate,
  onTenantDelete,
  className = ''
}) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedTenant, setSelectedTenant] = useState<Tenant | null>(null);
  
  const tenants = useMemo(() => generateTenants(), []);
  const resources = useMemo(() => generateTenantResources(), []);

  const handleTenantSelect = useCallback((tenant: Tenant) => {
    setSelectedTenant(tenant);
    // Could open tenant detail modal or navigate to tenant-specific view
  }, []);

  return (
    <div className={`space-y-6 ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Building2 className="w-6 h-6 text-blue-600" />
          <h2 className="text-2xl font-bold">Multi-Tenant Manager</h2>
          <Badge className="bg-blue-100 text-blue-800">Phase 9</Badge>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            Export Report
          </Button>
          <Button variant="outline">
            <Settings className="w-4 h-4 mr-2" />
            Global Settings
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="overview">Tenant Overview</TabsTrigger>
          <TabsTrigger value="resources">Resource Monitoring</TabsTrigger>
          <TabsTrigger value="isolation">Tenant Isolation</TabsTrigger>
          <TabsTrigger value="billing">Billing & Usage</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <TenantOverview
            tenants={tenants}
            onTenantSelect={handleTenantSelect}
          />
        </TabsContent>

        <TabsContent value="resources" className="space-y-6">
          <ResourceMonitoring
            resources={resources}
            tenants={tenants}
          />
        </TabsContent>

        <TabsContent value="isolation" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Tenant Isolation Architecture</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h3 className="font-semibold">Database Isolation</h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="w-4 h-4 text-green-600" />
                      <span>Separate database schemas per tenant</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="w-4 h-4 text-green-600" />
                      <span>Row-level security (RLS) policies</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="w-4 h-4 text-green-600" />
                      <span>Encrypted connections and data</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="font-semibold">Application Isolation</h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="w-4 h-4 text-green-600" />
                      <span>Tenant context middleware</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="w-4 h-4 text-green-600" />
                      <span>API request filtering</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="w-4 h-4 text-green-600" />
                      <span>Resource quotas and limits</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="billing" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Revenue Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Monthly Revenue:</span>
                    <span className="font-semibold text-green-600">
                      ${tenants.reduce((sum, t) => sum + t.monthlyRevenue, 0).toLocaleString()}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Enterprise Plans:</span>
                    <span className="font-semibold">
                      {tenants.filter(t => t.plan === 'enterprise').length}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Growth Rate:</span>
                    <span className="font-semibold text-green-600">+12.3%</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle>Usage by Plan</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {['enterprise', 'professional', 'starter'].map(plan => {
                    const planTenants = tenants.filter(t => t.plan === plan);
                    const revenue = planTenants.reduce((sum, t) => sum + t.monthlyRevenue, 0);
                    return (
                      <div key={plan} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                          <span className="font-medium capitalize">{plan}</span>
                          <div className="text-sm text-gray-600">
                            {planTenants.length} tenants
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="font-semibold">${revenue.toLocaleString()}</div>
                          <div className="text-sm text-gray-600">monthly</div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default MultiTenantManager;
export { type Tenant, type TenantResource };