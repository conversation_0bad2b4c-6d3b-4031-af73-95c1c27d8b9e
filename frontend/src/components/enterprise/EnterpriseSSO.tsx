/**
 * Enterprise Single Sign-On (SSO) Integration
 * SAML, LDAP, Active Directory, and Okta integration with automated provisioning
 */

import React, { useState, useMemo, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { motion } from 'framer-motion';
import { 
  Key, 
  Shield, 
  Users,
  UserCheck,
  UserPlus,
  UserX,
  Settings,
  Globe,
  Lock,
  Unlock,
  CheckCircle,
  AlertTriangle,
  Clock,
  Database,
  Server,
  Cloud,
  FileText,
  Download,
  Upload,
  RefreshCw,
  Link,
  Unlink,
  Eye,
  EyeOff,
  Copy,
  Edit,
  Trash2,
  Plus,
  Zap
} from 'lucide-react';

// Types
interface SSOProvider {
  id: string;
  name: string;
  type: 'saml' | 'ldap' | 'oidc' | 'oauth';
  status: 'active' | 'inactive' | 'testing' | 'error';
  isDefault: boolean;
  configuration: {
    entityId?: string;
    ssoUrl?: string;
    sloUrl?: string;
    certificate?: string;
    host?: string;
    port?: number;
    baseDn?: string;
    bindDn?: string;
    bindPassword?: string;
    userFilter?: string;
    clientId?: string;
    clientSecret?: string;
    discoveryUrl?: string;
    scopes?: string[];
  };
  attributeMapping: {
    email: string;
    firstName: string;
    lastName: string;
    department: string;
    role: string;
    groups: string;
  };
  provisioning: {
    enabled: boolean;
    autoCreateUsers: boolean;
    autoAssignRoles: boolean;
    autoUpdateUsers: boolean;
    defaultRole: string;
    allowedDomains: string[];
  };
  createdAt: Date;
  lastSync: Date;
  syncedUsers: number;
  errorCount: number;
}

interface SSOConnection {
  id: string;
  providerId: string;
  userId: string;
  userEmail: string;
  externalId: string;
  attributes: Record<string, any>;
  lastLogin: Date;
  loginCount: number;
  status: 'active' | 'suspended';
}

interface SSOAuditLog {
  id: string;
  providerId: string;
  action: 'login' | 'logout' | 'provision' | 'update' | 'error';
  userId?: string;
  userEmail?: string;
  timestamp: Date;
  ipAddress: string;
  userAgent: string;
  success: boolean;
  errorMessage?: string;
  metadata: Record<string, any>;
}

interface EnterpriseSSOProps {
  onProviderCreate?: (provider: Partial<SSOProvider>) => void;
  onProviderUpdate?: (providerId: string, updates: Partial<SSOProvider>) => void;
  onProviderTest?: (providerId: string) => void;
  className?: string;
}

// Mock data generation
const generateSSOProviders = (): SSOProvider[] => [
  {
    id: 'okta-main',
    name: 'Okta Enterprise',
    type: 'oidc',
    status: 'active',
    isDefault: true,
    configuration: {
      clientId: 'acme-analytics-client',
      clientSecret: '***hidden***',
      discoveryUrl: 'https://acme-corp.okta.com/.well-known/openid_configuration',
      scopes: ['openid', 'profile', 'email', 'groups']
    },
    attributeMapping: {
      email: 'email',
      firstName: 'given_name',
      lastName: 'family_name',
      department: 'department',
      role: 'role',
      groups: 'groups'
    },
    provisioning: {
      enabled: true,
      autoCreateUsers: true,
      autoAssignRoles: true,
      autoUpdateUsers: true,
      defaultRole: 'viewer',
      allowedDomains: ['acme.com', 'acme-corp.com']
    },
    createdAt: new Date('2024-01-15'),
    lastSync: new Date(Date.now() - 15 * 60 * 1000),
    syncedUsers: 247,
    errorCount: 0
  },
  {
    id: 'azure-ad',
    name: 'Azure Active Directory',
    type: 'saml',
    status: 'active',
    isDefault: false,
    configuration: {
      entityId: 'https://analytics.acme.com/saml/metadata',
      ssoUrl: 'https://login.microsoftonline.com/tenant-id/saml2',
      sloUrl: 'https://login.microsoftonline.com/tenant-id/saml2/logout',
      certificate: '-----BEGIN CERTIFICATE-----\nMIIC...\n-----END CERTIFICATE-----'
    },
    attributeMapping: {
      email: 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress',
      firstName: 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname',
      lastName: 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname',
      department: 'http://schemas.microsoft.com/ws/2008/06/identity/claims/department',
      role: 'http://schemas.microsoft.com/ws/2008/06/identity/claims/role',
      groups: 'http://schemas.xmlsoap.org/claims/Group'
    },
    provisioning: {
      enabled: false,
      autoCreateUsers: false,
      autoAssignRoles: false,
      autoUpdateUsers: false,
      defaultRole: 'viewer',
      allowedDomains: ['acme.com']
    },
    createdAt: new Date('2024-02-01'),
    lastSync: new Date(Date.now() - 2 * 60 * 60 * 1000),
    syncedUsers: 89,
    errorCount: 2
  },
  {
    id: 'ldap-corp',
    name: 'Corporate LDAP',
    type: 'ldap',
    status: 'testing',
    isDefault: false,
    configuration: {
      host: 'ldap.acme-corp.local',
      port: 636,
      baseDn: 'dc=acme-corp,dc=local',
      bindDn: 'cn=analytics-service,ou=services,dc=acme-corp,dc=local',
      bindPassword: '***hidden***',
      userFilter: '(&(objectClass=user)(mail=*))'
    },
    attributeMapping: {
      email: 'mail',
      firstName: 'givenName',
      lastName: 'sn',
      department: 'department',
      role: 'title',
      groups: 'memberOf'
    },
    provisioning: {
      enabled: true,
      autoCreateUsers: true,
      autoAssignRoles: false,
      autoUpdateUsers: true,
      defaultRole: 'analyst',
      allowedDomains: ['acme-corp.local']
    },
    createdAt: new Date('2024-03-10'),
    lastSync: new Date(Date.now() - 24 * 60 * 60 * 1000),
    syncedUsers: 156,
    errorCount: 5
  }
];

const generateSSOConnections = (): SSOConnection[] => [
  {
    id: 'conn-1',
    providerId: 'okta-main',
    userId: 'user-1',
    userEmail: '<EMAIL>',
    externalId: 'okta-user-123456',
    attributes: {
      given_name: 'John',
      family_name: 'Doe',
      department: 'IT',
      role: 'Admin',
      groups: ['IT-Admins', 'Analytics-Users']
    },
    lastLogin: new Date(Date.now() - 2 * 60 * 60 * 1000),
    loginCount: 245,
    status: 'active'
  },
  {
    id: 'conn-2',
    providerId: 'okta-main',
    userId: 'user-2',
    userEmail: '<EMAIL>',
    externalId: 'okta-user-789012',
    attributes: {
      given_name: 'Sarah',
      family_name: 'Johnson',
      department: 'Marketing',
      role: 'Manager',
      groups: ['Marketing-Team', 'Analytics-Users']
    },
    lastLogin: new Date(Date.now() - 30 * 60 * 1000),
    loginCount: 132,
    status: 'active'
  }
];

const generateSSOAuditLogs = (): SSOAuditLog[] => [
  {
    id: 'audit-sso-1',
    providerId: 'okta-main',
    action: 'login',
    userId: 'user-2',
    userEmail: '<EMAIL>',
    timestamp: new Date(Date.now() - 30 * 60 * 1000),
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    success: true,
    metadata: {
      provider: 'Okta Enterprise',
      sessionDuration: 3600,
      mfaUsed: true
    }
  },
  {
    id: 'audit-sso-2',
    providerId: 'azure-ad',
    action: 'provision',
    userId: 'user-3',
    userEmail: '<EMAIL>',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
    ipAddress: '*********',
    userAgent: 'SSO-Provisioning-Agent/1.0',
    success: true,
    metadata: {
      department: 'Sales',
      autoAssignedRole: 'analyst'
    }
  },
  {
    id: 'audit-sso-3',
    providerId: 'ldap-corp',
    action: 'error',
    timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),
    ipAddress: '**********',
    userAgent: 'LDAP-Sync-Service/2.1',
    success: false,
    errorMessage: 'Connection timeout to LDAP server',
    metadata: {
      errorCode: 'LDAP_TIMEOUT',
      retryAttempt: 3
    }
  }
];

// SSO Provider card component
const SSOProviderCard: React.FC<{
  provider: SSOProvider;
  onEdit?: () => void;
  onTest?: () => void;
  onToggle?: () => void;
}> = ({ provider, onEdit, onTest, onToggle }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      case 'testing': return 'bg-yellow-100 text-yellow-800';
      case 'error': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'saml': return <FileText className="w-5 h-5 text-blue-600" />;
      case 'ldap': return <Database className="w-5 h-5 text-green-600" />;
      case 'oidc': return <Cloud className="w-5 h-5 text-purple-600" />;
      case 'oauth': return <Key className="w-5 h-5 text-orange-600" />;
      default: return <Shield className="w-5 h-5 text-gray-600" />;
    }
  };

  return (
    <Card className={`${provider.isDefault ? 'ring-2 ring-blue-500' : ''}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {getTypeIcon(provider.type)}
            <div>
              <CardTitle className="text-lg">{provider.name}</CardTitle>
              <div className="flex items-center space-x-2 mt-1">
                <Badge className={getStatusColor(provider.status)}>
                  {provider.status}
                </Badge>
                <Badge variant="outline" className="uppercase">
                  {provider.type}
                </Badge>
                {provider.isDefault && (
                  <Badge className="bg-blue-100 text-blue-800">Default</Badge>
                )}
              </div>
            </div>
          </div>
          <div className="flex space-x-1">
            <Button variant="ghost" size="sm" onClick={onTest}>
              <Zap className="w-4 h-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={onEdit}>
              <Edit className="w-4 h-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={onToggle}>
              {provider.status === 'active' ? 
                <Lock className="w-4 h-4" /> : 
                <Unlock className="w-4 h-4" />
              }
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Synced Users:</span>
              <span className="ml-2 font-medium">{provider.syncedUsers}</span>
            </div>
            <div>
              <span className="text-gray-600">Errors:</span>
              <span className={`ml-2 font-medium ${provider.errorCount > 0 ? 'text-red-600' : 'text-green-600'}`}>
                {provider.errorCount}
              </span>
            </div>
            <div>
              <span className="text-gray-600">Last Sync:</span>
              <span className="ml-2 font-medium text-xs">
                {provider.lastSync.toLocaleString()}
              </span>
            </div>
            <div>
              <span className="text-gray-600">Auto Provision:</span>
              <span className={`ml-2 font-medium ${provider.provisioning.enabled ? 'text-green-600' : 'text-gray-600'}`}>
                {provider.provisioning.enabled ? 'Yes' : 'No'}
              </span>
            </div>
          </div>

          {provider.provisioning.enabled && (
            <div className="p-3 bg-blue-50 rounded-lg">
              <h4 className="font-medium text-sm mb-2">Provisioning Settings</h4>
              <div className="space-y-1 text-xs">
                <div className="flex items-center space-x-2">
                  {provider.provisioning.autoCreateUsers ? 
                    <CheckCircle className="w-3 h-3 text-green-600" /> : 
                    <AlertTriangle className="w-3 h-3 text-gray-400" />
                  }
                  <span>Auto-create users</span>
                </div>
                <div className="flex items-center space-x-2">
                  {provider.provisioning.autoAssignRoles ? 
                    <CheckCircle className="w-3 h-3 text-green-600" /> : 
                    <AlertTriangle className="w-3 h-3 text-gray-400" />
                  }
                  <span>Auto-assign roles</span>
                </div>
                <div className="flex items-center space-x-2">
                  {provider.provisioning.autoUpdateUsers ? 
                    <CheckCircle className="w-3 h-3 text-green-600" /> : 
                    <AlertTriangle className="w-3 h-3 text-gray-400" />
                  }
                  <span>Auto-update users</span>
                </div>
              </div>
            </div>
          )}

          <div className="pt-3 border-t">
            <div className="text-xs text-gray-500">
              Allowed domains: {provider.provisioning.allowedDomains.join(', ') || 'All domains'}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// SSO connections table
const SSOConnectionsTable: React.FC<{
  connections: SSOConnection[];
  providers: SSOProvider[];
}> = ({ connections, providers }) => {
  const getProviderName = (providerId: string) => {
    return providers.find(p => p.id === providerId)?.name || 'Unknown';
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Active SSO Connections</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b">
                <th className="text-left p-3 font-medium">User</th>
                <th className="text-left p-3 font-medium">Provider</th>
                <th className="text-left p-3 font-medium">External ID</th>
                <th className="text-left p-3 font-medium">Department</th>
                <th className="text-left p-3 font-medium">Last Login</th>
                <th className="text-center p-3 font-medium">Login Count</th>
                <th className="text-center p-3 font-medium">Status</th>
              </tr>
            </thead>
            <tbody>
              {connections.map((connection, index) => (
                <motion.tr
                  key={connection.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="border-b hover:bg-gray-50"
                >
                  <td className="p-3">
                    <div>
                      <div className="font-medium">
                        {connection.attributes.given_name} {connection.attributes.family_name}
                      </div>
                      <div className="text-gray-600 text-xs">{connection.userEmail}</div>
                    </div>
                  </td>
                  <td className="p-3">
                    <Badge variant="outline">
                      {getProviderName(connection.providerId)}
                    </Badge>
                  </td>
                  <td className="p-3">
                    <code className="text-xs bg-gray-100 px-2 py-1 rounded">
                      {connection.externalId}
                    </code>
                  </td>
                  <td className="p-3">
                    <Badge variant="outline">
                      {connection.attributes.department}
                    </Badge>
                  </td>
                  <td className="p-3">
                    <div>
                      <div>{connection.lastLogin.toLocaleDateString()}</div>
                      <div className="text-gray-500 text-xs">
                        {connection.lastLogin.toLocaleTimeString()}
                      </div>
                    </div>
                  </td>
                  <td className="p-3 text-center">
                    <span className="font-medium">{connection.loginCount}</span>
                  </td>
                  <td className="p-3 text-center">
                    <Badge className={connection.status === 'active' ? 
                      'bg-green-100 text-green-800' : 
                      'bg-red-100 text-red-800'
                    }>
                      {connection.status}
                    </Badge>
                  </td>
                </motion.tr>
              ))}
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  );
};

// SSO audit logs
const SSOAuditLogs: React.FC<{
  logs: SSOAuditLog[];
  providers: SSOProvider[];
}> = ({ logs, providers }) => {
  const getProviderName = (providerId: string) => {
    return providers.find(p => p.id === providerId)?.name || 'Unknown';
  };

  const getActionIcon = (action: string) => {
    switch (action) {
      case 'login': return <UserCheck className="w-4 h-4 text-green-600" />;
      case 'logout': return <UserX className="w-4 h-4 text-blue-600" />;
      case 'provision': return <UserPlus className="w-4 h-4 text-purple-600" />;
      case 'update': return <Edit className="w-4 h-4 text-orange-600" />;
      case 'error': return <AlertTriangle className="w-4 h-4 text-red-600" />;
      default: return <Eye className="w-4 h-4 text-gray-600" />;
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>SSO Audit Logs</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b">
                <th className="text-left p-3 font-medium">Action</th>
                <th className="text-left p-3 font-medium">Provider</th>
                <th className="text-left p-3 font-medium">User</th>
                <th className="text-left p-3 font-medium">Timestamp</th>
                <th className="text-left p-3 font-medium">IP Address</th>
                <th className="text-center p-3 font-medium">Status</th>
                <th className="text-center p-3 font-medium">Details</th>
              </tr>
            </thead>
            <tbody>
              {logs.map((log, index) => (
                <motion.tr
                  key={log.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="border-b hover:bg-gray-50"
                >
                  <td className="p-3">
                    <div className="flex items-center space-x-2">
                      {getActionIcon(log.action)}
                      <span className="font-medium capitalize">{log.action}</span>
                    </div>
                  </td>
                  <td className="p-3">
                    <Badge variant="outline">
                      {getProviderName(log.providerId)}
                    </Badge>
                  </td>
                  <td className="p-3">
                    {log.userEmail ? (
                      <div className="text-gray-900">{log.userEmail}</div>
                    ) : (
                      <span className="text-gray-500 italic">System</span>
                    )}
                  </td>
                  <td className="p-3">
                    <div>
                      <div>{log.timestamp.toLocaleDateString()}</div>
                      <div className="text-gray-500 text-xs">
                        {log.timestamp.toLocaleTimeString()}
                      </div>
                    </div>
                  </td>
                  <td className="p-3">
                    <code className="text-xs bg-gray-100 px-2 py-1 rounded">
                      {log.ipAddress}
                    </code>
                  </td>
                  <td className="p-3 text-center">
                    {log.success ? (
                      <CheckCircle className="w-4 h-4 text-green-600 mx-auto" />
                    ) : (
                      <AlertTriangle className="w-4 h-4 text-red-600 mx-auto" />
                    )}
                  </td>
                  <td className="p-3 text-center">
                    <Button variant="ghost" size="sm">
                      <Eye className="w-4 h-4" />
                    </Button>
                  </td>
                </motion.tr>
              ))}
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  );
};

// Main component
const EnterpriseSSO: React.FC<EnterpriseSSOProps> = ({
  onProviderCreate,
  onProviderUpdate,
  onProviderTest,
  className = ''
}) => {
  const [activeTab, setActiveTab] = useState('providers');
  const [selectedProvider, setSelectedProvider] = useState<SSOProvider | null>(null);
  
  const providers = useMemo(() => generateSSOProviders(), []);
  const connections = useMemo(() => generateSSOConnections(), []);
  const auditLogs = useMemo(() => generateSSOAuditLogs(), []);

  const handleProviderTest = useCallback((providerId: string) => {
    onProviderTest?.(providerId);
    console.log('Testing SSO provider:', providerId);
  }, [onProviderTest]);

  const handleProviderEdit = useCallback((provider: SSOProvider) => {
    setSelectedProvider(provider);
    // Open edit modal
  }, []);

  const handleProviderToggle = useCallback((provider: SSOProvider) => {
    const newStatus = provider.status === 'active' ? 'inactive' : 'active';
    onProviderUpdate?.(provider.id, { status: newStatus });
  }, [onProviderUpdate]);

  const stats = useMemo(() => {
    const totalUsers = connections.length;
    const activeProviders = providers.filter(p => p.status === 'active').length;
    const totalLogins = connections.reduce((sum, c) => sum + c.loginCount, 0);
    const errorRate = providers.reduce((sum, p) => sum + p.errorCount, 0);
    
    return { totalUsers, activeProviders, totalLogins, errorRate };
  }, [providers, connections]);

  return (
    <div className={`space-y-6 ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Key className="w-6 h-6 text-blue-600" />
          <h2 className="text-2xl font-bold">Enterprise SSO</h2>
          <Badge className="bg-blue-100 text-blue-800">Phase 9</Badge>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            Export Config
          </Button>
          <Button>
            <Plus className="w-4 h-4 mr-2" />
            Add Provider
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">SSO Providers</p>
                <p className="text-3xl font-bold text-blue-600">{providers.length}</p>
                <p className="text-xs text-green-600 mt-1">{stats.activeProviders} active</p>
              </div>
              <Shield className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">SSO Users</p>
                <p className="text-3xl font-bold text-green-600">{stats.totalUsers}</p>
                <p className="text-xs text-green-600 mt-1">Connected accounts</p>
              </div>
              <UserCheck className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Logins</p>
                <p className="text-3xl font-bold text-purple-600">{stats.totalLogins.toLocaleString()}</p>
                <p className="text-xs text-purple-600 mt-1">All time</p>
              </div>
              <Users className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Error Count</p>
                <p className={`text-3xl font-bold ${stats.errorRate > 0 ? 'text-red-600' : 'text-green-600'}`}>
                  {stats.errorRate}
                </p>
                <p className="text-xs text-gray-600 mt-1">Last 24 hours</p>
              </div>
              <AlertTriangle className={`w-8 h-8 ${stats.errorRate > 0 ? 'text-red-600' : 'text-green-600'}`} />
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="providers">SSO Providers</TabsTrigger>
          <TabsTrigger value="connections">User Connections</TabsTrigger>
          <TabsTrigger value="provisioning">Auto Provisioning</TabsTrigger>
          <TabsTrigger value="audit">Audit Logs</TabsTrigger>
        </TabsList>

        <TabsContent value="providers" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {providers.map((provider, index) => (
              <motion.div
                key={provider.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <SSOProviderCard
                  provider={provider}
                  onEdit={() => handleProviderEdit(provider)}
                  onTest={() => handleProviderTest(provider.id)}
                  onToggle={() => handleProviderToggle(provider)}
                />
              </motion.div>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="connections" className="space-y-6">
          <SSOConnectionsTable
            connections={connections}
            providers={providers}
          />
        </TabsContent>

        <TabsContent value="provisioning" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Provisioning Rules</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="p-4 border rounded-lg">
                    <h4 className="font-semibold mb-2">Default Role Assignment</h4>
                    <p className="text-sm text-gray-600 mb-3">
                      Automatically assign roles based on user attributes
                    </p>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>IT Department:</span>
                        <Badge variant="outline">tenant_admin</Badge>
                      </div>
                      <div className="flex justify-between">
                        <span>Marketing Department:</span>
                        <Badge variant="outline">analytics_manager</Badge>
                      </div>
                      <div className="flex justify-between">
                        <span>Default:</span>
                        <Badge variant="outline">viewer</Badge>
                      </div>
                    </div>
                  </div>

                  <div className="p-4 border rounded-lg">
                    <h4 className="font-semibold mb-2">Domain Restrictions</h4>
                    <p className="text-sm text-gray-600 mb-3">
                      Only allow users from approved domains
                    </p>
                    <div className="space-y-1">
                      <Badge variant="outline">acme.com</Badge>
                      <Badge variant="outline">acme-corp.com</Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Sync Status</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {providers.filter(p => p.provisioning.enabled).map(provider => (
                    <div key={provider.id} className="p-4 border rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium">{provider.name}</h4>
                        <Badge className={provider.status === 'active' ? 
                          'bg-green-100 text-green-800' : 
                          'bg-yellow-100 text-yellow-800'
                        }>
                          {provider.status}
                        </Badge>
                      </div>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span>Last Sync:</span>
                          <span>{provider.lastSync.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Synced Users:</span>
                          <span>{provider.syncedUsers}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Sync Errors:</span>
                          <span className={provider.errorCount > 0 ? 'text-red-600' : 'text-green-600'}>
                            {provider.errorCount}
                          </span>
                        </div>
                      </div>
                      <Button variant="outline" size="sm" className="mt-3 w-full">
                        <RefreshCw className="w-4 h-4 mr-2" />
                        Sync Now
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="audit" className="space-y-6">
          <SSOAuditLogs
            logs={auditLogs}
            providers={providers}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default EnterpriseSSO;
export { type SSOProvider, type SSOConnection, type SSOAuditLog };