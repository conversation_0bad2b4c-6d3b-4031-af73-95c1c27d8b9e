/**
 * Advanced Role-Based Access Control (RBAC)
 * Department-level permissions with audit trails and compliance
 */

import React, { useState, useMemo, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Shield, 
  Users, 
  UserCheck,
  UserX,
  Lock,
  Unlock,
  Key,
  Eye,
  Edit,
  Trash2,
  Plus,
  Search,
  Filter,
  Download,
  AlertTriangle,
  CheckCircle,
  Clock,
  Building,
  Settings,
  FileText,
  History,
  Globe,
  Database,
  BarChart3,
  Zap
} from 'lucide-react';

// Types
interface Permission {
  id: string;
  name: string;
  description: string;
  resource: string;
  actions: string[];
  scope: 'global' | 'tenant' | 'department' | 'personal';
}

interface Role {
  id: string;
  name: string;
  description: string;
  type: 'system' | 'custom';
  permissions: string[];
  userCount: number;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  isActive: boolean;
  departmentRestricted: boolean;
  allowedDepartments: string[];
}

interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  roles: string[];
  department: string;
  status: 'active' | 'inactive' | 'suspended';
  lastLogin: Date;
  createdAt: Date;
  permissions: string[];
  mfaEnabled: boolean;
  ssoEnabled: boolean;
}

interface AuditLog {
  id: string;
  action: string;
  resource: string;
  resourceId: string;
  userId: string;
  userEmail: string;
  timestamp: Date;
  ipAddress: string;
  userAgent: string;
  success: boolean;
  metadata: Record<string, any>;
}

interface Department {
  id: string;
  name: string;
  description: string;
  parentId?: string;
  userCount: number;
  defaultRole: string;
  dataAccessLevel: 'full' | 'department' | 'team' | 'personal';
}

interface AdvancedRBACProps {
  onRoleCreate?: (role: Partial<Role>) => void;
  onRoleUpdate?: (roleId: string, updates: Partial<Role>) => void;
  onUserUpdate?: (userId: string, updates: Partial<User>) => void;
  className?: string;
}

// Mock data generation
const generatePermissions = (): Permission[] => [
  {
    id: 'dashboard_view',
    name: 'View Dashboard',
    description: 'Access to main analytics dashboard',
    resource: 'dashboard',
    actions: ['read'],
    scope: 'tenant'
  },
  {
    id: 'analytics_read',
    name: 'Read Analytics',
    description: 'View analytics data and reports',
    resource: 'analytics',
    actions: ['read'],
    scope: 'department'
  },
  {
    id: 'analytics_export',
    name: 'Export Analytics',
    description: 'Export analytics data and reports',
    resource: 'analytics',
    actions: ['read', 'export'],
    scope: 'department'
  },
  {
    id: 'user_management',
    name: 'User Management',
    description: 'Create, update, and delete users',
    resource: 'users',
    actions: ['create', 'read', 'update', 'delete'],
    scope: 'tenant'
  },
  {
    id: 'role_management',
    name: 'Role Management',
    description: 'Create and manage roles and permissions',
    resource: 'roles',
    actions: ['create', 'read', 'update', 'delete'],
    scope: 'global'
  },
  {
    id: 'billing_access',
    name: 'Billing Access',
    description: 'Access billing information and invoices',
    resource: 'billing',
    actions: ['read'],
    scope: 'tenant'
  },
  {
    id: 'integration_manage',
    name: 'Integration Management',
    description: 'Configure and manage integrations',
    resource: 'integrations',
    actions: ['create', 'read', 'update', 'delete'],
    scope: 'tenant'
  },
  {
    id: 'api_access',
    name: 'API Access',
    description: 'Generate and manage API keys',
    resource: 'api',
    actions: ['create', 'read', 'update', 'delete'],
    scope: 'department'
  },
  {
    id: 'audit_logs',
    name: 'Audit Logs',
    description: 'View system audit logs',
    resource: 'audit',
    actions: ['read'],
    scope: 'tenant'
  },
  {
    id: 'tenant_settings',
    name: 'Tenant Settings',
    description: 'Modify tenant configuration and settings',
    resource: 'tenant',
    actions: ['read', 'update'],
    scope: 'global'
  }
];

const generateRoles = (): Role[] => [
  {
    id: 'super_admin',
    name: 'Super Administrator',
    description: 'Full system access across all tenants',
    type: 'system',
    permissions: generatePermissions().map(p => p.id),
    userCount: 2,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    createdBy: 'system',
    isActive: true,
    departmentRestricted: false,
    allowedDepartments: []
  },
  {
    id: 'tenant_admin',
    name: 'Tenant Administrator',
    description: 'Full access within tenant scope',
    type: 'system',
    permissions: [
      'dashboard_view', 'analytics_read', 'analytics_export', 'user_management',
      'billing_access', 'integration_manage', 'api_access', 'audit_logs'
    ],
    userCount: 8,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    createdBy: 'system',
    isActive: true,
    departmentRestricted: false,
    allowedDepartments: []
  },
  {
    id: 'analytics_manager',
    name: 'Analytics Manager',
    description: 'Manage analytics and reporting for department',
    type: 'custom',
    permissions: ['dashboard_view', 'analytics_read', 'analytics_export', 'api_access'],
    userCount: 15,
    createdAt: new Date('2024-02-15'),
    updatedAt: new Date('2024-05-20'),
    createdBy: '<EMAIL>',
    isActive: true,
    departmentRestricted: true,
    allowedDepartments: ['marketing', 'sales', 'product']
  },
  {
    id: 'analyst',
    name: 'Data Analyst',
    description: 'View and analyze data within department',
    type: 'custom',
    permissions: ['dashboard_view', 'analytics_read'],
    userCount: 42,
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-03-10'),
    createdBy: '<EMAIL>',
    isActive: true,
    departmentRestricted: true,
    allowedDepartments: ['marketing', 'sales', 'product', 'finance']
  },
  {
    id: 'viewer',
    name: 'Dashboard Viewer',
    description: 'Read-only access to dashboards',
    type: 'system',
    permissions: ['dashboard_view'],
    userCount: 23,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    createdBy: 'system',
    isActive: true,
    departmentRestricted: false,
    allowedDepartments: []
  }
];

const generateUsers = (): User[] => [
  {
    id: 'user-1',
    email: '<EMAIL>',
    firstName: 'John',
    lastName: 'Doe',
    roles: ['tenant_admin'],
    department: 'IT',
    status: 'active',
    lastLogin: new Date(Date.now() - 2 * 60 * 60 * 1000),
    createdAt: new Date('2024-01-15'),
    permissions: [],
    mfaEnabled: true,
    ssoEnabled: true
  },
  {
    id: 'user-2',
    email: '<EMAIL>',
    firstName: 'Sarah',
    lastName: 'Johnson',
    roles: ['analytics_manager'],
    department: 'marketing',
    status: 'active',
    lastLogin: new Date(Date.now() - 30 * 60 * 1000),
    createdAt: new Date('2024-02-01'),
    permissions: [],
    mfaEnabled: true,
    ssoEnabled: false
  },
  {
    id: 'user-3',
    email: '<EMAIL>',
    firstName: 'Mike',
    lastName: 'Chen',
    roles: ['analyst'],
    department: 'sales',
    status: 'active',
    lastLogin: new Date(Date.now() - 4 * 60 * 60 * 1000),
    createdAt: new Date('2024-03-10'),
    permissions: [],
    mfaEnabled: false,
    ssoEnabled: true
  }
];

const generateDepartments = (): Department[] => [
  {
    id: 'it',
    name: 'IT',
    description: 'Information Technology',
    userCount: 8,
    defaultRole: 'tenant_admin',
    dataAccessLevel: 'full'
  },
  {
    id: 'marketing',
    name: 'Marketing',
    description: 'Marketing and Growth',
    userCount: 15,
    defaultRole: 'analytics_manager',
    dataAccessLevel: 'department'
  },
  {
    id: 'sales',
    name: 'Sales',
    description: 'Sales and Business Development',
    userCount: 22,
    defaultRole: 'analyst',
    dataAccessLevel: 'department'
  },
  {
    id: 'product',
    name: 'Product',
    description: 'Product Management',
    userCount: 12,
    defaultRole: 'analyst',
    dataAccessLevel: 'department'
  },
  {
    id: 'finance',
    name: 'Finance',
    description: 'Finance and Accounting',
    userCount: 6,
    defaultRole: 'viewer',
    dataAccessLevel: 'department'
  }
];

const generateAuditLogs = (): AuditLog[] => [
  {
    id: 'audit-1',
    action: 'role.create',
    resource: 'roles',
    resourceId: 'analytics_manager',
    userId: 'user-1',
    userEmail: '<EMAIL>',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)',
    success: true,
    metadata: { roleName: 'Analytics Manager', permissions: 4 }
  },
  {
    id: 'audit-2',
    action: 'user.login',
    resource: 'auth',
    resourceId: 'user-2',
    userId: 'user-2',
    userEmail: '<EMAIL>',
    timestamp: new Date(Date.now() - 30 * 60 * 1000),
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
    success: true,
    metadata: { method: 'sso', provider: 'okta' }
  },
  {
    id: 'audit-3',
    action: 'analytics.export',
    resource: 'analytics',
    resourceId: 'revenue-report-2024',
    userId: 'user-2',
    userEmail: '<EMAIL>',
    timestamp: new Date(Date.now() - 45 * 60 * 1000),
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
    success: true,
    metadata: { format: 'csv', rows: 1250 }
  }
];

// Roles management component
const RolesManagement: React.FC<{
  roles: Role[];
  permissions: Permission[];
  onRoleCreate?: (role: Partial<Role>) => void;
  onRoleUpdate?: (roleId: string, updates: Partial<Role>) => void;
}> = ({ roles, permissions, onRoleCreate, onRoleUpdate }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);

  const filteredRoles = useMemo(() => {
    return roles.filter(role => 
      role.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      role.description.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [roles, searchTerm]);

  const getRoleTypeColor = (type: string) => {
    return type === 'system' ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800';
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search roles..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-64"
            />
          </div>
          <Button variant="outline">
            <Filter className="w-4 h-4 mr-2" />
            Filter
          </Button>
        </div>
        <Button onClick={() => onRoleCreate?.({})}>
          <Plus className="w-4 h-4 mr-2" />
          New Role
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {filteredRoles.map((role, index) => (
          <motion.div
            key={role.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card className="cursor-pointer hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">{role.name}</CardTitle>
                  <div className="flex space-x-2">
                    <Badge className={getRoleTypeColor(role.type)}>
                      {role.type}
                    </Badge>
                    {!role.isActive && (
                      <Badge variant="destructive">Inactive</Badge>
                    )}
                  </div>
                </div>
                <p className="text-sm text-gray-600">{role.description}</p>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Users</span>
                    <span className="font-semibold">{role.userCount}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Permissions</span>
                    <span className="font-semibold">{role.permissions.length}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Department Restricted</span>
                    <span className={role.departmentRestricted ? 'text-yellow-600' : 'text-green-600'}>
                      {role.departmentRestricted ? 'Yes' : 'No'}
                    </span>
                  </div>
                  
                  {role.departmentRestricted && role.allowedDepartments.length > 0 && (
                    <div className="mt-3">
                      <div className="text-xs text-gray-600 mb-1">Allowed Departments:</div>
                      <div className="flex flex-wrap gap-1">
                        {role.allowedDepartments.slice(0, 3).map(dept => (
                          <Badge key={dept} variant="outline" className="text-xs">
                            {dept}
                          </Badge>
                        ))}
                        {role.allowedDepartments.length > 3 && (
                          <Badge variant="outline" className="text-xs">
                            +{role.allowedDepartments.length - 3}
                          </Badge>
                        )}
                      </div>
                    </div>
                  )}

                  <div className="flex justify-between items-center pt-3 border-t">
                    <span className="text-xs text-gray-500">
                      Updated {role.updatedAt.toLocaleDateString()}
                    </span>
                    <div className="flex space-x-1">
                      <Button variant="ghost" size="sm" onClick={() => setSelectedRole(role)}>
                        <Eye className="w-4 h-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Edit className="w-4 h-4" />
                      </Button>
                      {role.type === 'custom' && (
                        <Button variant="ghost" size="sm">
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>
    </div>
  );
};

// Users management component
const UsersManagement: React.FC<{
  users: User[];
  roles: Role[];
  departments: Department[];
  onUserUpdate?: (userId: string, updates: Partial<User>) => void;
}> = ({ users, roles, departments, onUserUpdate }) => {
  const [searchTerm, setSearchTerm] = useState('');

  const filteredUsers = useMemo(() => {
    return users.filter(user => 
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      `${user.firstName} ${user.lastName}`.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [users, searchTerm]);

  const getRoleName = (roleId: string) => {
    return roles.find(r => r.id === roleId)?.name || roleId;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      case 'suspended': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search users..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-64"
            />
          </div>
          <Button variant="outline">
            <Filter className="w-4 h-4 mr-2" />
            Filter
          </Button>
        </div>
        <Button>
          <Plus className="w-4 h-4 mr-2" />
          Invite User
        </Button>
      </div>

      <Card>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b bg-gray-50">
                  <th className="text-left p-4 font-medium">User</th>
                  <th className="text-left p-4 font-medium">Department</th>
                  <th className="text-left p-4 font-medium">Roles</th>
                  <th className="text-left p-4 font-medium">Status</th>
                  <th className="text-left p-4 font-medium">Security</th>
                  <th className="text-left p-4 font-medium">Last Login</th>
                  <th className="text-center p-4 font-medium">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredUsers.map((user, index) => (
                  <motion.tr
                    key={user.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="border-b hover:bg-gray-50"
                  >
                    <td className="p-4">
                      <div>
                        <div className="font-medium">{user.firstName} {user.lastName}</div>
                        <div className="text-gray-600">{user.email}</div>
                      </div>
                    </td>
                    <td className="p-4">
                      <Badge variant="outline" className="capitalize">
                        {user.department}
                      </Badge>
                    </td>
                    <td className="p-4">
                      <div className="flex flex-wrap gap-1">
                        {user.roles.map(roleId => (
                          <Badge key={roleId} variant="outline" className="text-xs">
                            {getRoleName(roleId)}
                          </Badge>
                        ))}
                      </div>
                    </td>
                    <td className="p-4">
                      <Badge className={getStatusColor(user.status)}>
                        {user.status}
                      </Badge>
                    </td>
                    <td className="p-4">
                      <div className="flex space-x-2">
                        {user.mfaEnabled ? (
                          <CheckCircle className="w-4 h-4 text-green-600" title="MFA Enabled" />
                        ) : (
                          <AlertTriangle className="w-4 h-4 text-yellow-600" title="MFA Disabled" />
                        )}
                        {user.ssoEnabled ? (
                          <Key className="w-4 h-4 text-blue-600" title="SSO Enabled" />
                        ) : (
                          <Lock className="w-4 h-4 text-gray-400" title="SSO Disabled" />
                        )}
                      </div>
                    </td>
                    <td className="p-4">
                      <div className="text-gray-600">
                        {user.lastLogin.toLocaleDateString()}
                      </div>
                      <div className="text-xs text-gray-500">
                        {user.lastLogin.toLocaleTimeString()}
                      </div>
                    </td>
                    <td className="p-4 text-center">
                      <div className="flex items-center justify-center space-x-1">
                        <Button variant="ghost" size="sm">
                          <Eye className="w-4 h-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Settings className="w-4 h-4" />
                        </Button>
                      </div>
                    </td>
                  </motion.tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// Audit logs component
const AuditLogs: React.FC<{
  logs: AuditLog[];
}> = ({ logs }) => {
  const [searchTerm, setSearchTerm] = useState('');

  const filteredLogs = useMemo(() => {
    return logs.filter(log => 
      log.action.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.userEmail.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.resource.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [logs, searchTerm]);

  const getActionIcon = (action: string) => {
    if (action.includes('create')) return <Plus className="w-4 h-4 text-green-600" />;
    if (action.includes('update')) return <Edit className="w-4 h-4 text-blue-600" />;
    if (action.includes('delete')) return <Trash2 className="w-4 h-4 text-red-600" />;
    if (action.includes('login')) return <UserCheck className="w-4 h-4 text-purple-600" />;
    if (action.includes('export')) return <Download className="w-4 h-4 text-orange-600" />;
    return <Eye className="w-4 h-4 text-gray-600" />;
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search audit logs..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-64"
            />
          </div>
          <Button variant="outline">
            <Filter className="w-4 h-4 mr-2" />
            Filter
          </Button>
        </div>
        <Button variant="outline">
          <Download className="w-4 h-4 mr-2" />
          Export Logs
        </Button>
      </div>

      <Card>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b bg-gray-50">
                  <th className="text-left p-4 font-medium">Action</th>
                  <th className="text-left p-4 font-medium">Resource</th>
                  <th className="text-left p-4 font-medium">User</th>
                  <th className="text-left p-4 font-medium">Timestamp</th>
                  <th className="text-left p-4 font-medium">IP Address</th>
                  <th className="text-left p-4 font-medium">Status</th>
                  <th className="text-center p-4 font-medium">Details</th>
                </tr>
              </thead>
              <tbody>
                {filteredLogs.map((log, index) => (
                  <motion.tr
                    key={log.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="border-b hover:bg-gray-50"
                  >
                    <td className="p-4">
                      <div className="flex items-center space-x-2">
                        {getActionIcon(log.action)}
                        <span className="font-medium">{log.action}</span>
                      </div>
                    </td>
                    <td className="p-4">
                      <div>
                        <div className="font-medium">{log.resource}</div>
                        <div className="text-gray-600 text-xs">{log.resourceId}</div>
                      </div>
                    </td>
                    <td className="p-4">
                      <div className="text-gray-900">{log.userEmail}</div>
                    </td>
                    <td className="p-4">
                      <div>
                        <div>{log.timestamp.toLocaleDateString()}</div>
                        <div className="text-gray-600 text-xs">
                          {log.timestamp.toLocaleTimeString()}
                        </div>
                      </div>
                    </td>
                    <td className="p-4">
                      <code className="text-xs bg-gray-100 px-2 py-1 rounded">
                        {log.ipAddress}
                      </code>
                    </td>
                    <td className="p-4">
                      {log.success ? (
                        <CheckCircle className="w-4 h-4 text-green-600" />
                      ) : (
                        <AlertTriangle className="w-4 h-4 text-red-600" />
                      )}
                    </td>
                    <td className="p-4 text-center">
                      <Button variant="ghost" size="sm">
                        <Eye className="w-4 h-4" />
                      </Button>
                    </td>
                  </motion.tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// Main component
const AdvancedRBAC: React.FC<AdvancedRBACProps> = ({
  onRoleCreate,
  onRoleUpdate,
  onUserUpdate,
  className = ''
}) => {
  const [activeTab, setActiveTab] = useState('roles');
  
  const permissions = useMemo(() => generatePermissions(), []);
  const roles = useMemo(() => generateRoles(), []);
  const users = useMemo(() => generateUsers(), []);
  const departments = useMemo(() => generateDepartments(), []);
  const auditLogs = useMemo(() => generateAuditLogs(), []);

  return (
    <div className={`space-y-6 ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Shield className="w-6 h-6 text-blue-600" />
          <h2 className="text-2xl font-bold">Advanced RBAC</h2>
          <Badge className="bg-blue-100 text-blue-800">Phase 9</Badge>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            Export Config
          </Button>
          <Button variant="outline">
            <Settings className="w-4 h-4 mr-2" />
            RBAC Settings
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Roles</p>
                <p className="text-2xl font-bold text-blue-600">{roles.length}</p>
              </div>
              <Shield className="w-6 h-6 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Active Users</p>
                <p className="text-2xl font-bold text-green-600">
                  {users.filter(u => u.status === 'active').length}
                </p>
              </div>
              <Users className="w-6 h-6 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Permissions</p>
                <p className="text-2xl font-bold text-purple-600">{permissions.length}</p>
              </div>
              <Key className="w-6 h-6 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Departments</p>
                <p className="text-2xl font-bold text-orange-600">{departments.length}</p>
              </div>
              <Building className="w-6 h-6 text-orange-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">MFA Enabled</p>
                <p className="text-2xl font-bold text-teal-600">
                  {users.filter(u => u.mfaEnabled).length}
                </p>
              </div>
              <Lock className="w-6 h-6 text-teal-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="roles">Roles & Permissions</TabsTrigger>
          <TabsTrigger value="users">User Management</TabsTrigger>
          <TabsTrigger value="departments">Departments</TabsTrigger>
          <TabsTrigger value="audit">Audit Logs</TabsTrigger>
        </TabsList>

        <TabsContent value="roles" className="space-y-6">
          <RolesManagement
            roles={roles}
            permissions={permissions}
            onRoleCreate={onRoleCreate}
            onRoleUpdate={onRoleUpdate}
          />
        </TabsContent>

        <TabsContent value="users" className="space-y-6">
          <UsersManagement
            users={users}
            roles={roles}
            departments={departments}
            onUserUpdate={onUserUpdate}
          />
        </TabsContent>

        <TabsContent value="departments" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {departments.map((dept, index) => (
              <motion.div
                key={dept.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <span>{dept.name}</span>
                      <Building className="w-5 h-5 text-blue-600" />
                    </CardTitle>
                    <p className="text-sm text-gray-600">{dept.description}</p>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Users:</span>
                        <span className="font-medium">{dept.userCount}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Default Role:</span>
                        <Badge variant="outline">{roles.find(r => r.id === dept.defaultRole)?.name}</Badge>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Data Access:</span>
                        <Badge variant="outline" className="capitalize">{dept.dataAccessLevel}</Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="audit" className="space-y-6">
          <AuditLogs logs={auditLogs} />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AdvancedRBAC;
export { type Role, type User, type Permission, type AuditLog, type Department };