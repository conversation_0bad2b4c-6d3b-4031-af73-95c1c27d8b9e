/**
 * API Rate Limiting and Enterprise Quotas
 * Advanced rate limiting controls, quota management, and API usage monitoring
 */

import React, { useState, useMemo, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { motion } from 'framer-motion';
import { 
  Zap, 
  Shield, 
  Clock, 
  Target,
  AlertTriangle,
  CheckCircle,
  BarChart3,
  Activity,
  Settings,
  Users,
  Key,
  Database,
  Server,
  Globe,
  TrendingUp,
  TrendingDown,
  Pause,
  Play,
  RotateCcw,
  Edit,
  Trash2,
  Plus,
  Download,
  Filter,
  Search,
  Eye,
  AlertCircle,
  XCircle,
  Gauge,
  Timer,
  Hash,
  Cpu,
  HardDrive,
  Network,
  RefreshCw
} from 'lucide-react';
import { 
  LineChart, 
  Line, 
  AreaChart, 
  Area, 
  BarChart, 
  Bar, 
  Pie<PERSON>hart,
  Pie,
  Cell,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer
} from 'recharts';

// Types
interface RateLimitRule {
  id: string;
  name: string;
  description: string;
  endpoint: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' | '*';
  limit: number;
  window: string;
  windowMs: number;
  scope: 'global' | 'user' | 'ip' | 'tenant' | 'api_key';
  status: 'active' | 'inactive' | 'suspended';
  priority: number;
  actions: {
    throttle: boolean;
    block: boolean;
    alert: boolean;
    logOnly: boolean;
  };
  exemptions: string[];
  createdAt: Date;
  updatedAt: Date;
  hitCount: number;
  violations: number;
}

interface APIQuota {
  id: string;
  tenantId: string;
  tenantName: string;
  plan: 'starter' | 'professional' | 'enterprise' | 'custom';
  quotas: {
    apiCalls: {
      limit: number;
      used: number;
      resetDate: Date;
      overage: number;
    };
    dataPoints: {
      limit: number;
      used: number;
      resetDate: Date;
      overage: number;
    };
    storage: {
      limit: number;
      used: number;
      unit: 'MB' | 'GB' | 'TB';
    };
    exports: {
      limit: number;
      used: number;
      resetDate: Date;
    };
    seats: {
      limit: number;
      used: number;
    };
  };
  billing: {
    overageEnabled: boolean;
    overageRate: number;
    currentOverageCharges: number;
  };
  restrictions: {
    rateLimitTier: string;
    features: string[];
    endpoints: string[];
  };
  alerts: {
    enabled: boolean;
    thresholds: number[];
    recipients: string[];
  };
  status: 'active' | 'suspended' | 'warning' | 'exceeded';
}

interface APIUsageMetrics {
  timestamp: Date;
  endpoint: string;
  method: string;
  tenantId: string;
  userId?: string;
  ipAddress: string;
  userAgent: string;
  responseTime: number;
  statusCode: number;
  rateLimitHit: boolean;
  quotaExceeded: boolean;
  cached: boolean;
  dataPointsConsumed: number;
}

interface RateLimitViolation {
  id: string;
  timestamp: Date;
  ruleId: string;
  ruleName: string;
  scope: string;
  identifier: string;
  endpoint: string;
  method: string;
  limit: number;
  actual: number;
  action: 'throttled' | 'blocked' | 'logged';
  duration: number;
  userAgent: string;
  ipAddress: string;
  resolved: boolean;
}

interface APIRateLimitingProps {
  onRuleCreate?: (rule: Partial<RateLimitRule>) => void;
  onRuleUpdate?: (ruleId: string, updates: Partial<RateLimitRule>) => void;
  onQuotaUpdate?: (tenantId: string, updates: Partial<APIQuota>) => void;
  className?: string;
}

// Mock data generation
const generateRateLimitRules = (): RateLimitRule[] => [
  {
    id: 'rule-auth',
    name: 'Authentication Rate Limit',
    description: 'Prevent brute force attacks on authentication endpoints',
    endpoint: '/api/auth/*',
    method: 'POST',
    limit: 10,
    window: '15 minutes',
    windowMs: 15 * 60 * 1000,
    scope: 'ip',
    status: 'active',
    priority: 1,
    actions: {
      throttle: false,
      block: true,
      alert: true,
      logOnly: false
    },
    exemptions: ['***********/24'],
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-06-15'),
    hitCount: 1250,
    violations: 23
  },
  {
    id: 'rule-api-general',
    name: 'General API Rate Limit',
    description: 'Standard rate limiting for all API endpoints',
    endpoint: '/api/*',
    method: '*',
    limit: 1000,
    window: '1 hour',
    windowMs: 60 * 60 * 1000,
    scope: 'user',
    status: 'active',
    priority: 2,
    actions: {
      throttle: true,
      block: false,
      alert: false,
      logOnly: false
    },
    exemptions: ['<EMAIL>'],
    createdAt: new Date('2024-02-01'),
    updatedAt: new Date('2024-06-10'),
    hitCount: 45680,
    violations: 156
  },
  {
    id: 'rule-data-export',
    name: 'Data Export Limit',
    description: 'Limit large data exports to prevent resource exhaustion',
    endpoint: '/api/export/*',
    method: 'POST',
    limit: 5,
    window: '1 hour',
    windowMs: 60 * 60 * 1000,
    scope: 'tenant',
    status: 'active',
    priority: 3,
    actions: {
      throttle: true,
      block: true,
      alert: true,
      logOnly: false
    },
    exemptions: [],
    createdAt: new Date('2024-03-10'),
    updatedAt: new Date('2024-06-01'),
    hitCount: 890,
    violations: 12
  },
  {
    id: 'rule-search',
    name: 'Search API Limit',
    description: 'Rate limit for search and query endpoints',
    endpoint: '/api/search/*',
    method: 'GET',
    limit: 100,
    window: '10 minutes',
    windowMs: 10 * 60 * 1000,
    scope: 'api_key',
    status: 'active',
    priority: 4,
    actions: {
      throttle: true,
      block: false,
      alert: false,
      logOnly: false
    },
    exemptions: [],
    createdAt: new Date('2024-04-05'),
    updatedAt: new Date('2024-05-20'),
    hitCount: 12340,
    violations: 78
  }
];

const generateAPIQuotas = (): APIQuota[] => [
  {
    id: 'quota-acme-corp',
    tenantId: 'tenant-acme',
    tenantName: 'Acme Corporation',
    plan: 'enterprise',
    quotas: {
      apiCalls: {
        limit: 1000000,
        used: 756432,
        resetDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        overage: 0
      },
      dataPoints: {
        limit: 50000000,
        used: 34567890,
        resetDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        overage: 0
      },
      storage: {
        limit: 1000,
        used: 687,
        unit: 'GB'
      },
      exports: {
        limit: 500,
        used: 234,
        resetDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
      },
      seats: {
        limit: 100,
        used: 87
      }
    },
    billing: {
      overageEnabled: true,
      overageRate: 0.001,
      currentOverageCharges: 0
    },
    restrictions: {
      rateLimitTier: 'enterprise',
      features: ['advanced_analytics', 'custom_reports', 'api_access', 'white_label'],
      endpoints: ['*']
    },
    alerts: {
      enabled: true,
      thresholds: [80, 90, 95],
      recipients: ['<EMAIL>', '<EMAIL>']
    },
    status: 'active'
  },
  {
    id: 'quota-startup-inc',
    tenantId: 'tenant-startup',
    tenantName: 'Startup Inc.',
    plan: 'professional',
    quotas: {
      apiCalls: {
        limit: 100000,
        used: 98750,
        resetDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000),
        overage: 2340
      },
      dataPoints: {
        limit: 5000000,
        used: 4890000,
        resetDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000),
        overage: 450000
      },
      storage: {
        limit: 100,
        used: 89,
        unit: 'GB'
      },
      exports: {
        limit: 50,
        used: 47,
        resetDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000)
      },
      seats: {
        limit: 20,
        used: 18
      }
    },
    billing: {
      overageEnabled: true,
      overageRate: 0.002,
      currentOverageCharges: 1368.5
    },
    restrictions: {
      rateLimitTier: 'professional',
      features: ['advanced_analytics', 'custom_reports', 'api_access'],
      endpoints: ['/api/analytics/*', '/api/reports/*']
    },
    alerts: {
      enabled: true,
      thresholds: [80, 90, 95],
      recipients: ['<EMAIL>']
    },
    status: 'warning'
  },
  {
    id: 'quota-small-biz',
    tenantId: 'tenant-smallbiz',
    tenantName: 'Small Business LLC',
    plan: 'starter',
    quotas: {
      apiCalls: {
        limit: 10000,
        used: 10450,
        resetDate: new Date(Date.now() + 12 * 24 * 60 * 60 * 1000),
        overage: 450
      },
      dataPoints: {
        limit: 500000,
        used: 523000,
        resetDate: new Date(Date.now() + 12 * 24 * 60 * 60 * 1000),
        overage: 23000
      },
      storage: {
        limit: 10,
        used: 8,
        unit: 'GB'
      },
      exports: {
        limit: 10,
        used: 8,
        resetDate: new Date(Date.now() + 12 * 24 * 60 * 60 * 1000)
      },
      seats: {
        limit: 5,
        used: 3
      }
    },
    billing: {
      overageEnabled: false,
      overageRate: 0.005,
      currentOverageCharges: 0
    },
    restrictions: {
      rateLimitTier: 'basic',
      features: ['basic_analytics'],
      endpoints: ['/api/basic/*']
    },
    alerts: {
      enabled: true,
      thresholds: [90, 95],
      recipients: ['<EMAIL>']
    },
    status: 'exceeded'
  }
];

const generateRateLimitViolations = (): RateLimitViolation[] => [
  {
    id: 'violation-1',
    timestamp: new Date(Date.now() - 30 * 60 * 1000),
    ruleId: 'rule-auth',
    ruleName: 'Authentication Rate Limit',
    scope: 'ip',
    identifier: '************',
    endpoint: '/api/auth/login',
    method: 'POST',
    limit: 10,
    actual: 15,
    action: 'blocked',
    duration: 900,
    userAgent: 'PostmanRuntime/7.32.2',
    ipAddress: '************',
    resolved: true
  },
  {
    id: 'violation-2',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
    ruleId: 'rule-api-general',
    ruleName: 'General API Rate Limit',
    scope: 'user',
    identifier: 'user-12345',
    endpoint: '/api/analytics/dashboard',
    method: 'GET',
    limit: 1000,
    actual: 1050,
    action: 'throttled',
    duration: 60,
    userAgent: 'Analytics-Dashboard/1.0',
    ipAddress: '*************',
    resolved: true
  },
  {
    id: 'violation-3',
    timestamp: new Date(Date.now() - 15 * 60 * 1000),
    ruleId: 'rule-data-export',
    ruleName: 'Data Export Limit',
    scope: 'tenant',
    identifier: 'tenant-startup',
    endpoint: '/api/export/customers',
    method: 'POST',
    limit: 5,
    actual: 6,
    action: 'blocked',
    duration: 3600,
    userAgent: 'Analytics-Client/2.1',
    ipAddress: '*********',
    resolved: false
  }
];

// Generate time series data for usage charts
const generateUsageData = (days: number = 7) => {
  const data = [];
  for (let i = 0; i < days; i++) {
    const date = new Date();
    date.setDate(date.getDate() - days + i);
    
    data.push({
      date: date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
      apiCalls: Math.floor(Math.random() * 50000) + 100000,
      rateLimited: Math.floor(Math.random() * 1000) + 500,
      quotaExceeded: Math.floor(Math.random() * 100) + 50,
      responseTime: Math.floor(Math.random() * 200) + 150
    });
  }
  return data;
};

// Rate limit rule card component
const RateLimitRuleCard: React.FC<{
  rule: RateLimitRule;
  onEdit?: () => void;
  onToggle?: () => void;
  onDelete?: () => void;
}> = ({ rule, onEdit, onToggle, onDelete }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      case 'suspended': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getScopeIcon = (scope: string) => {
    switch (scope) {
      case 'global': return <Globe className="w-4 h-4" />;
      case 'user': return <Users className="w-4 h-4" />;
      case 'ip': return <Network className="w-4 h-4" />;
      case 'tenant': return <Database className="w-4 h-4" />;
      case 'api_key': return <Key className="w-4 h-4" />;
      default: return <Server className="w-4 h-4" />;
    }
  };

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3">
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <Zap className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <CardTitle className="text-lg">{rule.name}</CardTitle>
              <p className="text-sm text-gray-600 mt-1">{rule.description}</p>
            </div>
          </div>
          <Badge className={getStatusColor(rule.status)}>
            {rule.status}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Endpoint:</span>
              <code className="ml-2 text-xs bg-gray-100 px-2 py-1 rounded">
                {rule.method} {rule.endpoint}
              </code>
            </div>
            <div className="flex items-center">
              <span className="text-gray-600">Scope:</span>
              <div className="ml-2 flex items-center space-x-1">
                {getScopeIcon(rule.scope)}
                <span className="capitalize font-medium">{rule.scope}</span>
              </div>
            </div>
            <div>
              <span className="text-gray-600">Limit:</span>
              <span className="ml-2 font-medium">{rule.limit} / {rule.window}</span>
            </div>
            <div>
              <span className="text-gray-600">Priority:</span>
              <span className="ml-2 font-medium">{rule.priority}</span>
            </div>
          </div>

          <div>
            <h4 className="font-medium text-sm mb-2">Actions:</h4>
            <div className="flex space-x-2">
              {rule.actions.throttle && <Badge variant="outline">Throttle</Badge>}
              {rule.actions.block && <Badge variant="outline">Block</Badge>}
              {rule.actions.alert && <Badge variant="outline">Alert</Badge>}
              {rule.actions.logOnly && <Badge variant="outline">Log Only</Badge>}
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Hits:</span>
              <span className="ml-2 font-medium">{rule.hitCount.toLocaleString()}</span>
            </div>
            <div>
              <span className="text-gray-600">Violations:</span>
              <span className={`ml-2 font-medium ${rule.violations > 0 ? 'text-red-600' : 'text-green-600'}`}>
                {rule.violations}
              </span>
            </div>
          </div>

          <div className="flex justify-end space-x-2 pt-3 border-t">
            <Button variant="ghost" size="sm" onClick={onEdit}>
              <Edit className="w-4 h-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={onToggle}>
              {rule.status === 'active' ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
            </Button>
            <Button variant="ghost" size="sm" onClick={onDelete}>
              <Trash2 className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// API quota card component
const APIQuotaCard: React.FC<{
  quota: APIQuota;
  onUpdate?: () => void;
}> = ({ quota, onUpdate }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'warning': return 'bg-yellow-100 text-yellow-800';
      case 'exceeded': return 'bg-red-100 text-red-800';
      case 'suspended': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPlanColor = (plan: string) => {
    switch (plan) {
      case 'starter': return 'bg-blue-100 text-blue-800';
      case 'professional': return 'bg-purple-100 text-purple-800';
      case 'enterprise': return 'bg-green-100 text-green-800';
      case 'custom': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getUsagePercentage = (used: number, limit: number) => {
    return Math.min(100, (used / limit) * 100);
  };

  const getUsageColor = (percentage: number) => {
    if (percentage >= 95) return 'bg-red-500';
    if (percentage >= 80) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div>
            <CardTitle className="text-lg">{quota.tenantName}</CardTitle>
            <p className="text-sm text-gray-600 mt-1">{quota.tenantId}</p>
          </div>
          <div className="flex flex-col space-y-1">
            <Badge className={getStatusColor(quota.status)}>
              {quota.status}
            </Badge>
            <Badge className={getPlanColor(quota.plan)}>
              {quota.plan}
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* API Calls Quota */}
          <div>
            <div className="flex justify-between text-sm mb-1">
              <span>API Calls</span>
              <span>{quota.quotas.apiCalls.used.toLocaleString()} / {quota.quotas.apiCalls.limit.toLocaleString()}</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className={`h-2 rounded-full ${getUsageColor(getUsagePercentage(quota.quotas.apiCalls.used, quota.quotas.apiCalls.limit))}`}
                style={{ width: `${getUsagePercentage(quota.quotas.apiCalls.used, quota.quotas.apiCalls.limit)}%` }}
              />
            </div>
            {quota.quotas.apiCalls.overage > 0 && (
              <div className="text-xs text-red-600 mt-1">
                Overage: {quota.quotas.apiCalls.overage.toLocaleString()}
              </div>
            )}
          </div>

          {/* Data Points Quota */}
          <div>
            <div className="flex justify-between text-sm mb-1">
              <span>Data Points</span>
              <span>{(quota.quotas.dataPoints.used / 1000000).toFixed(1)}M / {(quota.quotas.dataPoints.limit / 1000000).toFixed(1)}M</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className={`h-2 rounded-full ${getUsageColor(getUsagePercentage(quota.quotas.dataPoints.used, quota.quotas.dataPoints.limit))}`}
                style={{ width: `${getUsagePercentage(quota.quotas.dataPoints.used, quota.quotas.dataPoints.limit)}%` }}
              />
            </div>
            {quota.quotas.dataPoints.overage > 0 && (
              <div className="text-xs text-red-600 mt-1">
                Overage: {(quota.quotas.dataPoints.overage / 1000000).toFixed(1)}M
              </div>
            )}
          </div>

          {/* Storage Quota */}
          <div>
            <div className="flex justify-between text-sm mb-1">
              <span>Storage</span>
              <span>{quota.quotas.storage.used} / {quota.quotas.storage.limit} {quota.quotas.storage.unit}</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className={`h-2 rounded-full ${getUsageColor(getUsagePercentage(quota.quotas.storage.used, quota.quotas.storage.limit))}`}
                style={{ width: `${getUsagePercentage(quota.quotas.storage.used, quota.quotas.storage.limit)}%` }}
              />
            </div>
          </div>

          {/* Billing Information */}
          {quota.billing.currentOverageCharges > 0 && (
            <div className="p-3 bg-yellow-50 rounded-lg">
              <h4 className="font-medium text-sm mb-1">Current Overage Charges</h4>
              <div className="text-lg font-bold text-yellow-800">
                ${quota.billing.currentOverageCharges.toFixed(2)}
              </div>
              <div className="text-xs text-yellow-700">
                Rate: ${quota.billing.overageRate}/unit
              </div>
            </div>
          )}

          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Seats:</span>
              <span className="ml-2 font-medium">{quota.quotas.seats.used} / {quota.quotas.seats.limit}</span>
            </div>
            <div>
              <span className="text-gray-600">Exports:</span>
              <span className="ml-2 font-medium">{quota.quotas.exports.used} / {quota.quotas.exports.limit}</span>
            </div>
          </div>

          <div className="flex justify-end space-x-2 pt-3 border-t">
            <Button variant="outline" size="sm">
              <Eye className="w-4 h-4 mr-1" />
              Details
            </Button>
            <Button variant="outline" size="sm" onClick={onUpdate}>
              <Settings className="w-4 h-4 mr-1" />
              Configure
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Violations table component
const ViolationsTable: React.FC<{
  violations: RateLimitViolation[];
}> = ({ violations }) => {
  const getActionColor = (action: string) => {
    switch (action) {
      case 'blocked': return 'bg-red-100 text-red-800';
      case 'throttled': return 'bg-yellow-100 text-yellow-800';
      case 'logged': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Recent Rate Limit Violations</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b">
                <th className="text-left p-3 font-medium">Timestamp</th>
                <th className="text-left p-3 font-medium">Rule</th>
                <th className="text-left p-3 font-medium">Identifier</th>
                <th className="text-left p-3 font-medium">Endpoint</th>
                <th className="text-center p-3 font-medium">Limit</th>
                <th className="text-center p-3 font-medium">Actual</th>
                <th className="text-center p-3 font-medium">Action</th>
                <th className="text-center p-3 font-medium">Status</th>
              </tr>
            </thead>
            <tbody>
              {violations.map((violation, index) => (
                <motion.tr
                  key={violation.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="border-b hover:bg-gray-50"
                >
                  <td className="p-3">
                    <div>
                      <div>{violation.timestamp.toLocaleDateString()}</div>
                      <div className="text-gray-500 text-xs">
                        {violation.timestamp.toLocaleTimeString()}
                      </div>
                    </div>
                  </td>
                  <td className="p-3">
                    <div className="font-medium">{violation.ruleName}</div>
                    <div className="text-gray-500 text-xs capitalize">{violation.scope}</div>
                  </td>
                  <td className="p-3">
                    <code className="text-xs bg-gray-100 px-2 py-1 rounded">
                      {violation.identifier}
                    </code>
                  </td>
                  <td className="p-3">
                    <div>
                      <code className="text-xs">{violation.endpoint}</code>
                      <div className="text-gray-500 text-xs">{violation.method}</div>
                    </div>
                  </td>
                  <td className="p-3 text-center">
                    <span className="font-medium">{violation.limit}</span>
                  </td>
                  <td className="p-3 text-center">
                    <span className="font-medium text-red-600">{violation.actual}</span>
                  </td>
                  <td className="p-3 text-center">
                    <Badge className={getActionColor(violation.action)}>
                      {violation.action}
                    </Badge>
                  </td>
                  <td className="p-3 text-center">
                    {violation.resolved ? (
                      <CheckCircle className="w-4 h-4 text-green-600 mx-auto" />
                    ) : (
                      <Clock className="w-4 h-4 text-yellow-600 mx-auto" />
                    )}
                  </td>
                </motion.tr>
              ))}
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  );
};

// Main component
const APIRateLimiting: React.FC<APIRateLimitingProps> = ({
  onRuleCreate,
  onRuleUpdate,
  onQuotaUpdate,
  className = ''
}) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [searchTerm, setSearchTerm] = useState('');
  
  const rules = useMemo(() => generateRateLimitRules(), []);
  const quotas = useMemo(() => generateAPIQuotas(), []);
  const violations = useMemo(() => generateRateLimitViolations(), []);
  const usageData = useMemo(() => generateUsageData(), []);

  const filteredRules = useMemo(() => {
    if (!searchTerm) return rules;
    return rules.filter(rule =>
      rule.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      rule.endpoint.toLowerCase().includes(searchTerm.toLowerCase()) ||
      rule.description.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [rules, searchTerm]);

  const stats = useMemo(() => {
    const totalRules = rules.length;
    const activeRules = rules.filter(r => r.status === 'active').length;
    const totalViolations = violations.length;
    const totalQuotas = quotas.length;
    const exceededQuotas = quotas.filter(q => q.status === 'exceeded').length;
    const totalApiCalls = quotas.reduce((sum, q) => sum + q.quotas.apiCalls.used, 0);
    
    return { totalRules, activeRules, totalViolations, totalQuotas, exceededQuotas, totalApiCalls };
  }, [rules, violations, quotas]);

  const handleRuleToggle = useCallback((ruleId: string) => {
    const rule = rules.find(r => r.id === ruleId);
    if (rule) {
      const newStatus = rule.status === 'active' ? 'inactive' : 'active';
      onRuleUpdate?.(ruleId, { status: newStatus });
    }
  }, [rules, onRuleUpdate]);

  return (
    <div className={`space-y-6 ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Gauge className="w-6 h-6 text-blue-600" />
          <h2 className="text-2xl font-bold">API Rate Limiting & Quotas</h2>
          <Badge className="bg-blue-100 text-blue-800">Phase 9</Badge>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            Export Report
          </Button>
          <Button>
            <Plus className="w-4 h-4 mr-2" />
            Add Rule
          </Button>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Rate Limit Rules</p>
                <p className="text-3xl font-bold text-blue-600">{stats.totalRules}</p>
                <p className="text-xs text-green-600 mt-1">{stats.activeRules} active</p>
              </div>
              <Zap className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total API Calls</p>
                <p className="text-3xl font-bold text-green-600">{(stats.totalApiCalls / 1000000).toFixed(1)}M</p>
                <p className="text-xs text-green-600 mt-1">This month</p>
              </div>
              <Activity className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Rate Violations</p>
                <p className="text-3xl font-bold text-orange-600">{stats.totalViolations}</p>
                <p className="text-xs text-orange-600 mt-1">Last 24 hours</p>
              </div>
              <AlertTriangle className="w-8 h-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">API Quotas</p>
                <p className="text-3xl font-bold text-purple-600">{stats.totalQuotas}</p>
                <p className="text-xs text-red-600 mt-1">{stats.exceededQuotas} exceeded</p>
              </div>
              <Target className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Avg Response Time</p>
                <p className="text-3xl font-bold text-teal-600">187ms</p>
                <p className="text-xs text-teal-600 mt-1">Last 7 days</p>
              </div>
              <Timer className="w-8 h-8 text-teal-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="overview">Usage Overview</TabsTrigger>
          <TabsTrigger value="rules">Rate Limit Rules</TabsTrigger>
          <TabsTrigger value="quotas">API Quotas</TabsTrigger>
          <TabsTrigger value="violations">Violations</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Usage Charts */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>API Usage Trends</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={usageData}>
                      <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Line type="monotone" dataKey="apiCalls" stroke="#3b82f6" strokeWidth={2} name="API Calls" />
                      <Line type="monotone" dataKey="rateLimited" stroke="#ef4444" strokeWidth={2} name="Rate Limited" />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Response Time & Violations</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart data={usageData}>
                      <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Area type="monotone" dataKey="responseTime" stackId="1" stroke="#8b5cf6" fill="#8b5cf6" name="Response Time (ms)" />
                      <Area type="monotone" dataKey="quotaExceeded" stackId="2" stroke="#f59e0b" fill="#f59e0b" name="Quota Exceeded" />
                    </AreaChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Recent Activity */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Top Rate Limited Endpoints</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {[
                    { endpoint: '/api/auth/login', hits: 1250, violations: 23 },
                    { endpoint: '/api/analytics/dashboard', hits: 45680, violations: 156 },
                    { endpoint: '/api/export/customers', hits: 890, violations: 12 },
                    { endpoint: '/api/search/products', hits: 12340, violations: 78 }
                  ].map((item, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div>
                        <code className="text-sm font-medium">{item.endpoint}</code>
                        <div className="text-xs text-gray-600">{item.hits.toLocaleString()} total hits</div>
                      </div>
                      <Badge className={item.violations > 50 ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'}>
                        {item.violations} violations
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Quota Usage Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {quotas.map((quota) => (
                    <div key={quota.id} className="p-3 border rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <span className="font-medium">{quota.tenantName}</span>
                        <Badge className={quota.status === 'exceeded' ? 'bg-red-100 text-red-800' : 
                                        quota.status === 'warning' ? 'bg-yellow-100 text-yellow-800' : 
                                        'bg-green-100 text-green-800'}>
                          {quota.status}
                        </Badge>
                      </div>
                      <div className="text-sm text-gray-600">
                        API Calls: {((quota.quotas.apiCalls.used / quota.quotas.apiCalls.limit) * 100).toFixed(1)}% used
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-1 mt-1">
                        <div 
                          className={`h-1 rounded-full ${
                            quota.status === 'exceeded' ? 'bg-red-500' :
                            quota.status === 'warning' ? 'bg-yellow-500' :
                            'bg-green-500'
                          }`}
                          style={{ width: `${Math.min(100, (quota.quotas.apiCalls.used / quota.quotas.apiCalls.limit) * 100)}%` }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="rules" className="space-y-6">
          <div className="flex items-center space-x-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search rules..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button variant="outline">
              <Filter className="w-4 h-4 mr-2" />
              Filter
            </Button>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {filteredRules.map((rule, index) => (
              <motion.div
                key={rule.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <RateLimitRuleCard
                  rule={rule}
                  onToggle={() => handleRuleToggle(rule.id)}
                />
              </motion.div>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="quotas" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {quotas.map((quota, index) => (
              <motion.div
                key={quota.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <APIQuotaCard quota={quota} />
              </motion.div>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="violations" className="space-y-6">
          <ViolationsTable violations={violations} />
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Rate Limiting Effectiveness</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={usageData}>
                      <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="rateLimited" fill="#ef4444" name="Rate Limited" />
                      <Bar dataKey="quotaExceeded" fill="#f59e0b" name="Quota Exceeded" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>API Performance Impact</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-4 bg-blue-50 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">187ms</div>
                      <div className="text-sm text-blue-600">Avg Response Time</div>
                      <div className="text-xs text-gray-600">7-day average</div>
                    </div>
                    <div className="text-center p-4 bg-green-50 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">99.97%</div>
                      <div className="text-sm text-green-600">Uptime</div>
                      <div className="text-xs text-gray-600">7-day average</div>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div className="flex justify-between text-sm">
                      <span>Requests Blocked:</span>
                      <span className="font-medium">1,247</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Requests Throttled:</span>
                      <span className="font-medium">8,392</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>False Positives:</span>
                      <span className="font-medium">23</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Protection Rate:</span>
                      <span className="font-medium text-green-600">99.76%</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default APIRateLimiting;
export { type RateLimitRule, type APIQuota, type RateLimitViolation, type APIUsageMetrics };