import * as Sentry from '@sentry/react';

// Utility functions for Sentry error reporting

export const captureException = (error: Error, context?: Record<string, any>) => {
  Sentry.withScope((scope) => {
    if (context) {
      Object.keys(context).forEach((key) => {
        scope.setContext(key, context[key]);
      });
    }
    Sentry.captureException(error);
  });
};

export const captureMessage = (message: string, level: 'info' | 'warning' | 'error' = 'info') => {
  Sentry.captureMessage(message, level);
};

export const setUser = (user: { id?: string; email?: string; name?: string }) => {
  Sentry.setUser(user);
};

export const addBreadcrumb = (message: string, category?: string, level?: 'info' | 'warning' | 'error') => {
  Sentry.addBreadcrumb({
    message,
    category: category || 'custom',
    level: level || 'info',
    timestamp: Date.now() / 1000,
  });
};

export const setTag = (key: string, value: string) => {
  Sentry.setTag(key, value);
};

export const setContext = (key: string, context: Record<string, any>) => {
  Sentry.setContext(key, context);
};

// Performance monitoring utilities
export const startTransaction = (name: string, op?: string) => {
  return Sentry.startTransaction({ name, op: op || 'navigation' });
};

export const measureFunction = <T extends (...args: any[]) => any>(
  fn: T,
  name: string,
  op?: string
): T => {
  return ((...args: Parameters<T>) => {
    return Sentry.trace({ name, op: op || 'function' }, () => fn(...args));
  }) as T;
};