import * as Sentry from '@sentry/react';
import { captureException, captureMessage, setContext, setTag, addBreadcrumb } from '../utils/sentry';

export interface ErrorContext {
  component?: string;
  action?: string;
  userId?: string;
  tenantId?: string;
  url?: string;
  userAgent?: string;
  timestamp?: string;
  additionalData?: Record<string, any>;
}

export interface ErrorMetrics {
  errorCount: number;
  lastErrorTime: string;
  errorTypes: Record<string, number>;
  componentErrors: Record<string, number>;
}

class ErrorReportingService {
  private errorMetrics: ErrorMetrics = {
    errorCount: 0,
    lastErrorTime: '',
    errorTypes: {},
    componentErrors: {},
  };

  private readonly MAX_BREADCRUMBS = 50;
  private breadcrumbs: Array<{ message: string; timestamp: string; level: string }> = [];

  /**
   * Report a JavaScript error with context
   */
  reportError(error: Error, context?: ErrorContext): void {
    try {
      // Update metrics
      this.updateErrorMetrics(error, context);

      // Add context to Sentry
      if (context) {
        setContext('errorContext', {
          component: context.component,
          action: context.action,
          url: context.url || window.location.href,
          userAgent: context.userAgent || navigator.userAgent,
          timestamp: context.timestamp || new Date().toISOString(),
          ...context.additionalData,
        });

        // Set tags for filtering
        if (context.component) setTag('component', context.component);
        if (context.action) setTag('action', context.action);
        if (context.userId) setTag('userId', context.userId);
        if (context.tenantId) setTag('tenantId', context.tenantId);
      }

      // Add breadcrumb
      this.addErrorBreadcrumb(error, context);

      // Capture exception
      captureException(error, context);

      // Log to console in development
      if (process.env.NODE_ENV === 'development') {
        console.group('🚨 Error Reported');
        console.error('Error:', error);
        console.log('Context:', context);
        console.log('Stack:', error.stack);
        console.groupEnd();
      }
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError);
    }
  }

  /**
   * Report an API error with additional context
   */
  reportApiError(error: Error, endpoint: string, method: string, statusCode?: number, responseData?: any): void {
    const context: ErrorContext = {
      component: 'API',
      action: `${method} ${endpoint}`,
      additionalData: {
        endpoint,
        method,
        statusCode,
        responseData: responseData ? JSON.stringify(responseData).substring(0, 1000) : undefined,
      },
    };

    this.reportError(error, context);
  }

  /**
   * Report a component error
   */
  reportComponentError(error: Error, componentName: string, props?: any): void {
    const context: ErrorContext = {
      component: componentName,
      action: 'render',
      additionalData: {
        props: props ? JSON.stringify(props).substring(0, 1000) : undefined,
      },
    };

    this.reportError(error, context);
  }

  /**
   * Report a user action error
   */
  reportUserActionError(error: Error, action: string, component?: string, additionalData?: any): void {
    const context: ErrorContext = {
      component,
      action,
      additionalData,
    };

    this.reportError(error, context);
  }

  /**
   * Report a warning message
   */
  reportWarning(message: string, context?: ErrorContext): void {
    try {
      if (context) {
        setContext('warningContext', context);
      }

      captureMessage(message, 'warning');

      if (process.env.NODE_ENV === 'development') {
        console.warn('⚠️ Warning:', message, context);
      }
    } catch (reportingError) {
      console.error('Failed to report warning:', reportingError);
    }
  }

  /**
   * Add a breadcrumb for tracking user actions
   */
  addBreadcrumb(message: string, category: string = 'user', level: 'info' | 'warning' | 'error' = 'info'): void {
    const breadcrumb = {
      message,
      timestamp: new Date().toISOString(),
      level,
    };

    // Add to local breadcrumbs
    this.breadcrumbs.push(breadcrumb);
    if (this.breadcrumbs.length > this.MAX_BREADCRUMBS) {
      this.breadcrumbs.shift();
    }

    // Add to Sentry
    addBreadcrumb(message, category, level);
  }

  /**
   * Set user context for error reporting
   */
  setUser(userId: string, email?: string, tenantId?: string): void {
    Sentry.setUser({
      id: userId,
      email,
    });

    if (tenantId) {
      setTag('tenantId', tenantId);
    }
  }

  /**
   * Clear user context
   */
  clearUser(): void {
    Sentry.setUser(null);
  }

  /**
   * Get current error metrics
   */
  getErrorMetrics(): ErrorMetrics {
    return { ...this.errorMetrics };
  }

  /**
   * Get recent breadcrumbs
   */
  getBreadcrumbs(): Array<{ message: string; timestamp: string; level: string }> {
    return [...this.breadcrumbs];
  }

  /**
   * Clear error metrics (useful for testing)
   */
  clearMetrics(): void {
    this.errorMetrics = {
      errorCount: 0,
      lastErrorTime: '',
      errorTypes: {},
      componentErrors: {},
    };
    this.breadcrumbs = [];
  }

  private updateErrorMetrics(error: Error, context?: ErrorContext): void {
    this.errorMetrics.errorCount++;
    this.errorMetrics.lastErrorTime = new Date().toISOString();

    // Track error types
    const errorType = error.constructor.name;
    this.errorMetrics.errorTypes[errorType] = (this.errorMetrics.errorTypes[errorType] || 0) + 1;

    // Track component errors
    if (context?.component) {
      this.errorMetrics.componentErrors[context.component] = 
        (this.errorMetrics.componentErrors[context.component] || 0) + 1;
    }
  }

  private addErrorBreadcrumb(error: Error, context?: ErrorContext): void {
    const message = `Error in ${context?.component || 'unknown'}: ${error.message}`;
    this.addBreadcrumb(message, 'error', 'error');
  }
}

// Create singleton instance
export const errorReporting = new ErrorReportingService();

// Export convenience functions
export const reportError = (error: Error, context?: ErrorContext) => 
  errorReporting.reportError(error, context);

export const reportApiError = (error: Error, endpoint: string, method: string, statusCode?: number, responseData?: any) =>
  errorReporting.reportApiError(error, endpoint, method, statusCode, responseData);

export const reportComponentError = (error: Error, componentName: string, props?: any) =>
  errorReporting.reportComponentError(error, componentName, props);

export const reportUserActionError = (error: Error, action: string, component?: string, additionalData?: any) =>
  errorReporting.reportUserActionError(error, action, component, additionalData);

export const reportWarning = (message: string, context?: ErrorContext) =>
  errorReporting.reportWarning(message, context);

export default errorReporting;
