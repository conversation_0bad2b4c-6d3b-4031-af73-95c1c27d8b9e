
import React from 'react';
import KPIScorecard from '../components/dashboard/KPIScorecard';
import NexusInsightsWidget from '../components/dashboard/NexusInsightsWidget';
import InteractiveGlobe from '../components/dashboard/InteractiveGlobe';
import ErrorBoundary from '../components/ErrorBoundary';

const DashboardPage = () => {
  // Mock data for KPI Scorecards
  const kpiData = [
    { title: 'Total Revenue', value: '$1.2M', trend: 15, sparklineData: [10, 20, 15, 25, 22, 30, 28] },
    { title: 'Conversion Rate', value: '3.5%', trend: -2, sparklineData: [5, 4, 4.5, 3.8, 3.5, 3.2, 3.5] },
    { title: 'AOV', value: '$120', trend: 5, sparklineData: [110, 115, 120, 118, 125, 122, 120] },
    { title: 'CLV', value: '$500', trend: 10, sparklineData: [450, 480, 500, 490, 510, 505, 500] },
  ];

  // Mock data for Nexus Insights
  const insightsData = [
    { id: 1, type: 'success', message: '📈 Sales Forecast: We predict a 15% uplift in sales next week.', action: { label: 'View Details', onClick: () => alert('View Sales Forecast') } },
    { id: 2, type: 'warning', message: '⚠️ Churn Alert: 35 high-value customers at risk.', action: { label: 'Engage Customers', onClick: () => alert('Engage Customers') } },
    { id: 3, type: 'info', message: '💡 Recommendation: Reallocate ad spend to UK for Vintage Leather Jacket.', action: { label: 'Implement', onClick: () => alert('Implement Recommendation') } },
  ];

  return (
    <ErrorBoundary
      onError={(error) => console.error('Dashboard error:', error)}
      showStack={process.env.NODE_ENV === 'development'}
    >
      <div className="dashboard-page">
        <h2>Main Executive Summary Dashboard: "The Bridge"</h2>
        <div style={{ display: 'flex', gap: '20px', flexWrap: 'wrap', marginTop: '20px' }}>
          {kpiData.map((kpi, index) => (
            <ErrorBoundary key={index} fallback={<div className="p-4 bg-red-50 rounded">KPI widget failed to load</div>}>
              <KPIScorecard {...kpi} />
            </ErrorBoundary>
          ))}
        </div>
        <ErrorBoundary fallback={<div className="p-4 bg-red-50 rounded">Insights widget failed to load</div>}>
          <NexusInsightsWidget insights={insightsData} />
        </ErrorBoundary>
        <ErrorBoundary fallback={<div className="p-4 bg-red-50 rounded">Interactive globe failed to load</div>}>
          <InteractiveGlobe />
        </ErrorBoundary>
        {/* Other dashboard components will go here */}
      </div>
    </ErrorBoundary>
  );
};

export default DashboardPage;
