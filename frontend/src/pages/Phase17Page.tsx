import React, { useState } from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import ExecutiveReporting from '../components/visualization/ExecutiveReporting';
import DataStorytellingPlatform from '../components/visualization/DataStorytellingPlatform';
import KPIScorecards from '../components/visualization/KPIScorecards';
import InteractiveReportBuilder from '../components/visualization/InteractiveReportBuilder';
import AnomalyDetectionPlatform from '../components/visualization/AnomalyDetectionPlatform';

const Phase17Page: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Phase 17: Advanced Reporting & Storytelling</h1>
        <p className="text-gray-600 mt-2">
          Executive reporting, data storytelling, KPI scorecards, interactive reports, and anomaly detection
        </p>
      </div>

      <Tabs defaultValue="executive" className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="executive">Executive Reports</TabsTrigger>
          <TabsTrigger value="storytelling">Data Stories</TabsTrigger>
          <TabsTrigger value="kpi">KPI Scorecards</TabsTrigger>
          <TabsTrigger value="reports">Report Builder</TabsTrigger>
          <TabsTrigger value="anomaly">Anomaly Detection</TabsTrigger>
        </TabsList>

        <TabsContent value="executive" className="mt-6">
          <ExecutiveReporting />
        </TabsContent>

        <TabsContent value="storytelling" className="mt-6">
          <DataStorytellingPlatform />
        </TabsContent>

        <TabsContent value="kpi" className="mt-6">
          <KPIScorecards />
        </TabsContent>

        <TabsContent value="reports" className="mt-6">
          <InteractiveReportBuilder />
        </TabsContent>

        <TabsContent value="anomaly" className="mt-6">
          <AnomalyDetectionPlatform />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Phase17Page;