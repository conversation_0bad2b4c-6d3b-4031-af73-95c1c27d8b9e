import React, { useState } from 'react';
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import AdvancedAnimationSystem from '../components/animations/AdvancedAnimationSystem';
import MicroInteractions from '../components/animations/MicroInteractions';
import ParticleEffects from '../components/animations/ParticleEffects';
import CustomDashboardBuilder from '../components/customization/CustomDashboardBuilder';
import UserManagement from '../components/customization/UserManagement';

const EnhancedUXPage: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Enhanced UX & Animation Suite</h1>
        <p className="text-gray-600 mt-2">
          Advanced animations, micro-interactions, particle effects, custom dashboards, and user management
        </p>
      </div>

      <Tabs defaultValue="animations" className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="animations">Animation System</TabsTrigger>
          <TabsTrigger value="interactions">Micro Interactions</TabsTrigger>
          <TabsTrigger value="particles">Particle Effects</TabsTrigger>
          <TabsTrigger value="dashboard">Dashboard Builder</TabsTrigger>
          <TabsTrigger value="users">User Management</TabsTrigger>
        </TabsList>

        <TabsContent value="animations" className="mt-6">
          <AdvancedAnimationSystem />
        </TabsContent>

        <TabsContent value="interactions" className="mt-6">
          <MicroInteractions />
        </TabsContent>

        <TabsContent value="particles" className="mt-6">
          <ParticleEffects />
        </TabsContent>

        <TabsContent value="dashboard" className="mt-6">
          <CustomDashboardBuilder />
        </TabsContent>

        <TabsContent value="users" className="mt-6">
          <UserManagement />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default EnhancedUXPage;