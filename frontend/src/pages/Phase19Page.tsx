import React, { useState } from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import EnterpriseSSO from '../components/enterprise/EnterpriseSSO';
import MultiTenantManager from '../components/enterprise/MultiTenantManager';
import AdvancedRBAC from '../components/enterprise/AdvancedRBAC';
import APIRateLimiting from '../components/enterprise/APIRateLimiting';

const Phase19Page: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Phase 19: Enterprise Features</h1>
        <p className="text-gray-600 mt-2">
          Enterprise-grade SSO, multi-tenant management, advanced RBAC, and API rate limiting
        </p>
      </div>

      <Tabs defaultValue="sso" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="sso">Enterprise SSO</TabsTrigger>
          <TabsTrigger value="multi-tenant">Multi-Tenant</TabsTrigger>
          <TabsTrigger value="rbac">Advanced RBAC</TabsTrigger>
          <TabsTrigger value="api">API Rate Limiting</TabsTrigger>
        </TabsList>

        <TabsContent value="sso" className="mt-6">
          <EnterpriseSSO />
        </TabsContent>

        <TabsContent value="multi-tenant" className="mt-6">
          <MultiTenantManager />
        </TabsContent>

        <TabsContent value="rbac" className="mt-6">
          <AdvancedRBAC />
        </TabsContent>

        <TabsContent value="api" className="mt-6">
          <APIRateLimiting />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Phase19Page;