import React, { useState } from 'react';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import CompetitiveIntelligence from '../components/autonomous/CompetitiveIntelligence';
import SelfOptimizingInfrastructure from '../components/autonomous/SelfOptimizingInfrastructure';
import AutonomousAnomalyResolution from '../components/autonomous/AutonomousAnomalyResolution';
import ZeroTouchOperationsCenter from '../components/autonomous/ZeroTouchOperationsCenter';

const Phase23Page: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Phase 23: Zero-Touch Operations</h1>
        <p className="text-gray-600 mt-2">
          Competitive intelligence, self-optimizing infrastructure, autonomous anomaly resolution, and zero-touch operations
        </p>
      </div>

      <Tabs defaultValue="intelligence" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="intelligence">Competitive Intel</TabsTrigger>
          <TabsTrigger value="infrastructure">Self-Optimizing</TabsTrigger>
          <TabsTrigger value="resolution">Auto Resolution</TabsTrigger>
          <TabsTrigger value="operations">Zero-Touch Ops</TabsTrigger>
        </TabsList>

        <TabsContent value="intelligence" className="mt-6">
          <CompetitiveIntelligence />
        </TabsContent>

        <TabsContent value="infrastructure" className="mt-6">
          <SelfOptimizingInfrastructure />
        </TabsContent>

        <TabsContent value="resolution" className="mt-6">
          <AutonomousAnomalyResolution />
        </TabsContent>

        <TabsContent value="operations" className="mt-6">
          <ZeroTouchOperationsCenter />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Phase23Page;