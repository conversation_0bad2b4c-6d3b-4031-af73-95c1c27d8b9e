import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { linkTrackingService } from '../services/api';
import LoadingSpinner from '../components/LoadingSpinner';
import ApiErrorBoundary from '../components/ApiErrorBoundary';
import { 
  Plus, 
  ExternalLink, 
  Edit3, 
  Trash2, 
  Copy,
  Eye,
  TrendingUp,
  Calendar,
  Tag
} from 'lucide-react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

const linkSchema = z.object({
  target_url: z.string().url('Please enter a valid URL'),
  title: z.string().min(1, 'Title is required'),
  tags: z.string().optional(),
});

type LinkFormData = z.infer<typeof linkSchema>;

export default function LinksPage() {
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [selectedLink, setSelectedLink] = useState<string | null>(null);
  const queryClient = useQueryClient();
  
  // TODO: Get tenant_id from authentication context
  const tenantId = '00000000-0000-0000-0000-000000000001';

  const { data: linksData, isLoading } = useQuery({
    queryKey: ['links', tenantId],
    queryFn: () => linkTrackingService.getLinks({ tenant_id: tenantId }),
  });

  const { data: linkDetails } = useQuery({
    queryKey: ['link', selectedLink, tenantId],
    queryFn: () => linkTrackingService.getLinkById(selectedLink!, tenantId),
    enabled: !!selectedLink,
  });

  const createMutation = useMutation({
    mutationFn: linkTrackingService.createLink,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['links'] });
      setShowCreateForm(false);
      reset();
    },
  });

  const deleteMutation = useMutation({
    mutationFn: (id: string) => linkTrackingService.deleteLink(id, tenantId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['links'] });
    },
  });

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<LinkFormData>({
    resolver: zodResolver(linkSchema),
  });

  const onSubmit = (data: LinkFormData) => {
    const tags = data.tags ? data.tags.split(',').map(tag => tag.trim()).filter(Boolean) : [];
    createMutation.mutate({
      target_url: data.target_url,
      title: data.title,
      tenant_id: tenantId,
      utm_source: 'webapp',
      utm_medium: 'organic',
      utm_campaign: 'default',
    });
  };

  const copyToClipboard = (shortCode: string) => {
    const url = `${window.location.origin}/${shortCode}`;
    navigator.clipboard.writeText(url);
    // You could add a toast notification here
  };

  const formatNumber = (value: number) => 
    new Intl.NumberFormat('en-US').format(value);

  const formatCurrency = (value: number) => 
    new Intl.NumberFormat('en-US', { 
      style: 'currency', 
      currency: 'USD' 
    }).format(value);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner />
      </div>
    );
  }

  const links = linksData?.success ? linksData.data : [];

  return (
    <ApiErrorBoundary
      resourceName="links"
      retry={() => queryClient.invalidateQueries({ queryKey: ['links'] })}
    >
      <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Links</h1>
          <p className="text-gray-600">Manage and track your shortened links</p>
        </div>
        <button
          onClick={() => setShowCreateForm(true)}
          className="btn btn-primary flex items-center space-x-2"
        >
          <Plus className="h-4 w-4" />
          <span>Create Link</span>
        </button>
      </div>

      {/* Create Link Form */}
      {showCreateForm && (
        <div className="card">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Create New Link</h3>
            <button
              onClick={() => {
                setShowCreateForm(false);
                reset();
              }}
              className="text-gray-400 hover:text-gray-600"
            >
              ×
            </button>
          </div>
          
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <div>
              <label htmlFor="target_url" className="block text-sm font-medium text-gray-700">
                Target URL *
              </label>
              <input
                {...register('target_url')}
                type="url"
                className="input mt-1"
                placeholder="https://example.com/your-long-url"
              />
              {errors.target_url && (
                <p className="mt-1 text-sm text-red-600">{errors.target_url.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="title" className="block text-sm font-medium text-gray-700">
                Title *
              </label>
              <input
                {...register('title')}
                type="text"
                className="input mt-1"
                placeholder="Give your link a descriptive title"
              />
              {errors.title && (
                <p className="mt-1 text-sm text-red-600">{errors.title.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="tags" className="block text-sm font-medium text-gray-700">
                Tags
              </label>
              <input
                {...register('tags')}
                type="text"
                className="input mt-1"
                placeholder="e.g., marketing, campaign, social (comma-separated)"
              />
              <p className="mt-1 text-sm text-gray-500">
                Add tags to organize your links (comma-separated)
              </p>
            </div>

            <div className="flex space-x-3">
              <button
                type="submit"
                disabled={createMutation.isPending}
                className="btn btn-primary disabled:opacity-50"
              >
                {createMutation.isPending ? 'Creating...' : 'Create Link'}
              </button>
              <button
                type="button"
                onClick={() => {
                  setShowCreateForm(false);
                  reset();
                }}
                className="btn btn-secondary"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Links Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {links.map((link: any) => (
          <div key={link.id} className="card hover:shadow-md transition-shadow">
            <div className="flex items-start justify-between mb-3">
              <div className="flex-1 min-w-0">
                <h3 className="text-lg font-semibold text-gray-900 truncate">
                  {link.title}
                </h3>
                <p className="text-sm text-gray-500 truncate">
                  {link.target_url}
                </p>
              </div>
              <div className="flex space-x-1 ml-2">
                <button
                  onClick={() => setSelectedLink(link.id)}
                  className="p-1 text-gray-400 hover:text-blue-600"
                  title="View Details"
                >
                  <Eye className="h-4 w-4" />
                </button>
                <button
                  onClick={() => copyToClipboard(link.short_code)}
                  className="p-1 text-gray-400 hover:text-green-600"
                  title="Copy Link"
                >
                  <Copy className="h-4 w-4" />
                </button>
                <button
                  onClick={() => deleteMutation.mutate(link.id)}
                  className="p-1 text-gray-400 hover:text-red-600"
                  title="Delete"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>
            </div>

            <div className="mb-4">
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <ExternalLink className="h-4 w-4" />
                <span className="font-mono">/{link.short_code}</span>
              </div>
            </div>

            {link.tags && link.tags.length > 0 && (
              <div className="mb-4">
                <div className="flex items-center space-x-1">
                  <Tag className="h-3 w-3 text-gray-400" />
                  <div className="flex flex-wrap gap-1">
                    {link.tags.map((tag: string, index: number) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {link.performance_metrics && (
              <div className="grid grid-cols-2 gap-4 pt-4 border-t border-gray-100">
                <div>
                  <p className="text-xs text-gray-500">This Month</p>
                  <p className="text-sm font-medium text-gray-900">
                    {formatNumber(link.performance_metrics.clicks_this_month)} clicks
                  </p>
                </div>
                <div>
                  <p className="text-xs text-gray-500">Conversion Rate</p>
                  <p className="text-sm font-medium text-gray-900">
                    {link.performance_metrics.conversion_rate}%
                  </p>
                </div>
                <div>
                  <p className="text-xs text-gray-500">Revenue</p>
                  <p className="text-sm font-medium text-gray-900">
                    {formatCurrency(link.performance_metrics.revenue_this_month)}
                  </p>
                </div>
                <div>
                  <p className="text-xs text-gray-500">Created</p>
                  <p className="text-sm font-medium text-gray-900">
                    {new Date(link.created_at).toLocaleDateString()}
                  </p>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>

      {links.length === 0 && (
        <div className="text-center py-12">
          <ExternalLink className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No links yet</h3>
          <p className="mt-1 text-sm text-gray-500">
            Get started by creating your first shortened link.
          </p>
          <div className="mt-6">
            <button
              onClick={() => setShowCreateForm(true)}
              className="btn btn-primary"
            >
              Create Link
            </button>
          </div>
        </div>
      )}

      {/* Link Details Modal */}
      {selectedLink && linkDetails?.success && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75" onClick={() => setSelectedLink(null)} />
            
            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium text-gray-900">
                    Link Analytics: {linkDetails.data.title}
                  </h3>
                  <button
                    onClick={() => setSelectedLink(null)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    ×
                  </button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                  <div className="card">
                    <div className="text-center">
                      <p className="text-2xl font-bold text-blue-600">
                        {formatNumber(linkDetails.data.analytics.total_clicks)}
                      </p>
                      <p className="text-sm text-gray-500">Total Clicks</p>
                    </div>
                  </div>
                  <div className="card">
                    <div className="text-center">
                      <p className="text-2xl font-bold text-green-600">
                        {linkDetails.data.analytics.conversion_rate}%
                      </p>
                      <p className="text-sm text-gray-500">Conversion Rate</p>
                    </div>
                  </div>
                  <div className="card">
                    <div className="text-center">
                      <p className="text-2xl font-bold text-purple-600">
                        {formatCurrency(linkDetails.data.analytics.revenue_generated)}
                      </p>
                      <p className="text-sm text-gray-500">Revenue Generated</p>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Recent Clicks */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-3">Recent Clicks</h4>
                    <div className="space-y-2 max-h-64 overflow-y-auto">
                      {linkDetails.data.recent_clicks?.map((click: any, index: number) => (
                        <div key={index} className="p-3 bg-gray-50 rounded-lg">
                          <div className="flex justify-between items-start">
                            <div>
                              <p className="text-sm font-medium text-gray-900">
                                {click.country}
                              </p>
                              <p className="text-xs text-gray-500">
                                {click.device_type} • {click.referrer}
                              </p>
                            </div>
                            <p className="text-xs text-gray-400">
                              {new Date(click.clicked_at).toLocaleString()}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Geographic Stats */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-3">Geographic Distribution</h4>
                    <div className="space-y-2">
                      {linkDetails.data.geographic_stats?.map((stat: any, index: number) => (
                        <div key={index} className="flex justify-between items-center">
                          <span className="text-sm text-gray-700">{stat.country}</span>
                          <div className="flex items-center space-x-2">
                            <div className="w-20 bg-gray-200 rounded-full h-2">
                              <div
                                className="bg-blue-600 h-2 rounded-full"
                                style={{ width: `${stat.percentage}%` }}
                              />
                            </div>
                            <span className="text-sm text-gray-500 w-12 text-right">
                              {stat.percentage}%
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
    </ApiErrorBoundary>
  );
}