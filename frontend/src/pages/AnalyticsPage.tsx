
import React from 'react';
import CohortAnalysis from '../components/analytics/CohortAnalysis';
import ConversionFunnel from '../components/analytics/ConversionFunnel';
import PredictiveForecasting from '../components/analytics/PredictiveForecasting';
import ErrorBoundary from '../components/ErrorBoundary';

const AnalyticsPage = () => {
  return (
    <ErrorBoundary
      onError={(error) => console.error('Analytics page error:', error)}
      showStack={process.env.NODE_ENV === 'development'}
    >
      <div className="analytics-page">
        <h2>Advanced Analytics Modules: "The Microscope"</h2>
        <ErrorBoundary fallback={<div className="p-4 bg-red-50 rounded">Cohort analysis failed to load</div>}>
          <CohortAnalysis />
        </ErrorBoundary>
        <ErrorBoundary fallback={<div className="p-4 bg-red-50 rounded">Conversion funnel failed to load</div>}>
          <ConversionFunnel />
        </ErrorBoundary>
        <ErrorBoundary fallback={<div className="p-4 bg-red-50 rounded">Predictive forecasting failed to load</div>}>
          <PredictiveForecasting />
        </ErrorBoundary>
      </div>
    </ErrorBoundary>
  );
};

export default AnalyticsPage;
