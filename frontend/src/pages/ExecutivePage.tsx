import React, { useState } from 'react';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import ExecutiveDashboard from '../components/executive/ExecutiveDashboard';
import AppMarketplace from '../components/marketplace/AppMarketplace';
import CustomKPIBuilder from '../components/marketplace/CustomKPIBuilder';
import SOC2Dashboard from '../components/compliance/SOC2Dashboard';
import ComplianceDashboard from '../components/dashboard/ComplianceDashboard';

const ExecutivePage: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Executive & Marketplace Suite</h1>
        <p className="text-gray-600 mt-2">
          Executive dashboards, app marketplace, custom KPI builder, and compliance management
        </p>
      </div>

      <Tabs defaultValue="executive" className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="executive">Executive Dashboard</TabsTrigger>
          <TabsTrigger value="marketplace">App Marketplace</TabsTrigger>
          <TabsTrigger value="kpi">KPI Builder</TabsTrigger>
          <TabsTrigger value="soc2">SOC2 Compliance</TabsTrigger>
          <TabsTrigger value="compliance">Compliance Center</TabsTrigger>
        </TabsList>

        <TabsContent value="executive" className="mt-6">
          <ExecutiveDashboard />
        </TabsContent>

        <TabsContent value="marketplace" className="mt-6">
          <AppMarketplace />
        </TabsContent>

        <TabsContent value="kpi" className="mt-6">
          <CustomKPIBuilder />
        </TabsContent>

        <TabsContent value="soc2" className="mt-6">
          <SOC2Dashboard />
        </TabsContent>

        <TabsContent value="compliance" className="mt-6">
          <ComplianceDashboard />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ExecutivePage;