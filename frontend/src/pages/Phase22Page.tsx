import React, { useState } from 'react';
import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import AutonomousDataPipeline from '../components/autonomous/AutonomousDataPipeline';
import SelfHealingArchitecture from '../components/autonomous/SelfHealingArchitecture';
import AutonomousMLOps from '../components/autonomous/AutonomousMLOps';
import AIStrategyEngine from '../components/autonomous/AIStrategyEngine';

const Phase22Page: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Phase 22: Autonomous Systems Platform</h1>
        <p className="text-gray-600 mt-2">
          Self-managing data pipelines, self-healing architecture, autonomous MLOps, and AI strategy engine
        </p>
      </div>

      <Tabs defaultValue="pipeline" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="pipeline">Data Pipeline</TabsTrigger>
          <TabsTrigger value="healing">Self-Healing</TabsTrigger>
          <TabsTrigger value="mlops">Auto MLOps</TabsTrigger>
          <TabsTrigger value="strategy">AI Strategy</TabsTrigger>
        </TabsList>

        <TabsContent value="pipeline" className="mt-6">
          <AutonomousDataPipeline />
        </TabsContent>

        <TabsContent value="healing" className="mt-6">
          <SelfHealingArchitecture />
        </TabsContent>

        <TabsContent value="mlops" className="mt-6">
          <AutonomousMLOps />
        </TabsContent>

        <TabsContent value="strategy" className="mt-6">
          <AIStrategyEngine />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Phase22Page;