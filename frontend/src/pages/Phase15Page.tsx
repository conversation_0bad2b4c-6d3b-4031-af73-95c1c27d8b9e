import React, { useState } from 'react';
import { Tabs, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import WebhookManagement from '../components/integrations/WebhookManagement';
import CustomIntegrationBuilder from '../components/integrations/CustomIntegrationBuilder';
import DataQualityMonitoring from '../components/integrations/DataQualityMonitoring';
import IntegrationMarketplace from '../components/integrations/IntegrationMarketplace';

const Phase15Page: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Phase 15: Integration Ecosystem</h1>
        <p className="text-gray-600 mt-2">
          Webhook management, custom integration builder, data quality monitoring, and integration marketplace
        </p>
      </div>

      <Tabs defaultValue="webhooks" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="webhooks">Webhooks</TabsTrigger>
          <TabsTrigger value="builder">Integration Builder</TabsTrigger>
          <TabsTrigger value="quality">Data Quality</TabsTrigger>
          <TabsTrigger value="marketplace">Marketplace</TabsTrigger>
        </TabsList>

        <TabsContent value="webhooks" className="mt-6">
          <WebhookManagement />
        </TabsContent>

        <TabsContent value="builder" className="mt-6">
          <CustomIntegrationBuilder />
        </TabsContent>

        <TabsContent value="quality" className="mt-6">
          <DataQualityMonitoring />
        </TabsContent>

        <TabsContent value="marketplace" className="mt-6">
          <IntegrationMarketplace />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Phase15Page;