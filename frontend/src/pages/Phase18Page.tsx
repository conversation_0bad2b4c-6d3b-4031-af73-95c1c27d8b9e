import React, { useState } from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import SegmentationAnalysis from '../components/analytics/SegmentationAnalysis';
import AttributionModelComparison from '../components/attribution/AttributionModelComparison';
import PredictiveForecasting from '../components/analytics/PredictiveForecasting';

const Phase18Page: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Phase 18: Advanced Analytics Suite</h1>
        <p className="text-gray-600 mt-2">
          Advanced customer segmentation, cross-channel attribution, and predictive insights
        </p>
      </div>

      <Tabs defaultValue="segmentation" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="segmentation">Advanced Segmentation</TabsTrigger>
          <TabsTrigger value="attribution">Cross-Channel Attribution</TabsTrigger>
          <TabsTrigger value="predictive">Predictive Insights</TabsTrigger>
        </TabsList>

        <TabsContent value="segmentation" className="mt-6">
          <SegmentationAnalysis />
        </TabsContent>

        <TabsContent value="attribution" className="mt-6">
          <AttributionModelComparison />
        </TabsContent>

        <TabsContent value="predictive" className="mt-6">
          <PredictiveForecasting />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Phase18Page;