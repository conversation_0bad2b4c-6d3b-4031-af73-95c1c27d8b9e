import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import D3AnalyticsDashboard from '../components/visualization/D3/D3AnalyticsDashboard';
import D3CustomerJourney from '../components/visualization/D3/D3CustomerJourney';
import D3AttributionAnalysis from '../components/visualization/D3/D3AttributionAnalysis';
import D3CohortAnalysis from '../components/visualization/D3/D3CohortAnalysis';
import D3RealTimeMetrics from '../components/visualization/D3/D3RealTimeMetrics';
import { 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  TrendingUp, 
  Users, 
  DollarSign, 
  ShoppingCart,
  Target,
  Activity,
  Download,
  RefreshCw
} from 'lucide-react';

// Mock data generators
const generateMockChartData = () => {
  const now = new Date();
  const data = [];
  
  for (let i = 30; i >= 0; i--) {
    const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
    data.push({
      id: `point-${i}`,
      value: Math.floor(Math.random() * 1000) + 500,
      category: 'sales',
      label: date.toLocaleDateString(),
      date: date
    });
  }
  
  return data;
};

const generateMockJourneyData = () => {
  return {
    nodes: [
      { id: 'awareness-1', name: 'Social Media', type: 'touchpoint' as const, value: 1000, stage: 'awareness' as const },
      { id: 'awareness-2', name: 'Search Ads', type: 'touchpoint' as const, value: 800, stage: 'awareness' as const },
      { id: 'interest-1', name: 'Website Visit', type: 'touchpoint' as const, value: 600, stage: 'interest' as const },
      { id: 'consideration-1', name: 'Product View', type: 'touchpoint' as const, value: 400, stage: 'consideration' as const },
      { id: 'intent-1', name: 'Add to Cart', type: 'touchpoint' as const, value: 200, stage: 'intent' as const },
      { id: 'purchase-1', name: 'Purchase', type: 'conversion' as const, value: 100, stage: 'purchase' as const }
    ],
    links: [
      { source: 'awareness-1', target: 'interest-1', value: 300, type: 'flow' as const },
      { source: 'awareness-2', target: 'interest-1', value: 300, type: 'flow' as const },
      { source: 'interest-1', target: 'consideration-1', value: 400, type: 'flow' as const },
      { source: 'consideration-1', target: 'intent-1', value: 200, type: 'flow' as const },
      { source: 'intent-1', target: 'purchase-1', value: 100, type: 'conversion' as const }
    ]
  };
};

const generateMockAttributionData = () => {
  const channels = ['Google Ads', 'Facebook', 'Email', 'Organic Search', 'Direct'];
  const models = ['lastClick', 'firstClick', 'linear', 'timeDecay', 'positionBased'];
  
  const attributionData = [];
  const comparisonData = [];
  
  channels.forEach(channel => {
    models.forEach(model => {
      attributionData.push({
        channel,
        model,
        attributedValue: Math.floor(Math.random() * 50000) + 10000,
        conversions: Math.floor(Math.random() * 100) + 20,
        weight: Math.random(),
        touchpoints: Math.floor(Math.random() * 500) + 100
      });
    });
    
    comparisonData.push({
      channel,
      lastClick: Math.floor(Math.random() * 50000) + 10000,
      firstClick: Math.floor(Math.random() * 40000) + 8000,
      linear: Math.floor(Math.random() * 45000) + 9000,
      timeDecay: Math.floor(Math.random() * 48000) + 9500,
      positionBased: Math.floor(Math.random() * 46000) + 9200
    });
  });
  
  return { attributionData, comparisonData };
};

const generateMockCohortData = () => {
  const cohorts = ['2024-01', '2024-02', '2024-03', '2024-04', '2024-05'];
  const data = [];
  
  cohorts.forEach(cohort => {
    for (let period = 0; period <= 6; period++) {
      const baseCustomers = 1000 - period * 100;
      data.push({
        cohortMonth: cohort,
        period,
        customers: Math.max(baseCustomers + Math.floor(Math.random() * 200) - 100, 0),
        revenue: Math.floor(Math.random() * 50000) + 10000,
        retentionRate: Math.max(0.1, 1 - (period * 0.15) + (Math.random() * 0.1 - 0.05))
      });
    }
  });
  
  return data;
};

const generateMockRealTimeData = () => {
  const now = new Date();
  const streams = [
    {
      id: 'revenue',
      name: 'Revenue',
      color: '#10b981',
      unit: '$',
      format: 'currency' as const,
      data: []
    },
    {
      id: 'visitors',
      name: 'Active Visitors',
      color: '#3b82f6',
      unit: '',
      format: 'number' as const,
      data: []
    },
    {
      id: 'conversion',
      name: 'Conversion Rate',
      color: '#f59e0b',
      unit: '%',
      format: 'percentage' as const,
      data: []
    },
    {
      id: 'orders',
      name: 'Orders',
      color: '#ef4444',
      unit: '',
      format: 'number' as const,
      data: []
    }
  ];
  
  // Generate historical data
  for (let i = 60; i >= 0; i--) {
    const timestamp = new Date(now.getTime() - i * 60 * 1000);
    
    streams[0].data.push({
      timestamp,
      value: Math.floor(Math.random() * 5000) + 15000,
      metric: 'revenue'
    });
    
    streams[1].data.push({
      timestamp,
      value: Math.floor(Math.random() * 200) + 800,
      metric: 'visitors'
    });
    
    streams[2].data.push({
      timestamp,
      value: Math.random() * 5 + 2,
      metric: 'conversion'
    });
    
    streams[3].data.push({
      timestamp,
      value: Math.floor(Math.random() * 50) + 20,
      metric: 'orders'
    });
  }
  
  return streams;
};

const AnalyticsDashboardPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedAttributionModel, setSelectedAttributionModel] = useState('lastClick');
  const [refreshing, setRefreshing] = useState(false);

  // Generate mock data
  const chartData = useMemo(() => [
    {
      type: 'line' as const,
      data: generateMockChartData(),
      title: 'Daily Revenue',
      width: 400,
      height: 300
    },
    {
      type: 'bar' as const,
      data: [
        { id: '1', value: 1200, category: 'desktop', label: 'Desktop', color: '#3b82f6' },
        { id: '2', value: 800, category: 'mobile', label: 'Mobile', color: '#10b981' },
        { id: '3', value: 400, category: 'tablet', label: 'Tablet', color: '#f59e0b' }
      ],
      title: 'Traffic by Device',
      width: 400,
      height: 300
    },
    {
      type: 'pie' as const,
      data: [
        { id: '1', value: 45, category: 'organic', label: 'Organic', color: '#3b82f6' },
        { id: '2', value: 30, category: 'paid', label: 'Paid Ads', color: '#10b981' },
        { id: '3', value: 15, category: 'social', label: 'Social', color: '#f59e0b' },
        { id: '4', value: 10, category: 'direct', label: 'Direct', color: '#ef4444' }
      ],
      title: 'Traffic Sources',
      width: 400,
      height: 300
    }
  ], []);

  const journeyData = useMemo(() => generateMockJourneyData(), []);
  const { attributionData, comparisonData } = useMemo(() => generateMockAttributionData(), []);
  const cohortData = useMemo(() => generateMockCohortData(), []);
  const realTimeStreams = useMemo(() => generateMockRealTimeData(), []);

  const handleRefresh = async () => {
    setRefreshing(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    setRefreshing(false);
  };

  const handleDataInteraction = (chart: any, point: any) => {
    console.log('Data interaction:', chart.title, point);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Analytics Dashboard</h1>
            <p className="text-gray-600">Comprehensive e-commerce analytics with D3.js visualizations</p>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className="text-green-600">
              <Activity className="w-3 h-3 mr-1" />
              Live Data
            </Badge>
            <Button 
              onClick={handleRefresh} 
              disabled={refreshing}
              variant="outline"
            >
              <RefreshCw className={`w-4 h-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <Button variant="outline">
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
          </div>
        </div>

        {/* Key Metrics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                  <p className="text-2xl font-bold">$124,563</p>
                  <p className="text-xs text-green-600">+12.5% from last month</p>
                </div>
                <DollarSign className="w-8 h-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Orders</p>
                  <p className="text-2xl font-bold">1,234</p>
                  <p className="text-xs text-blue-600">+8.2% from last month</p>
                </div>
                <ShoppingCart className="w-8 h-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Conversion Rate</p>
                  <p className="text-2xl font-bold">3.24%</p>
                  <p className="text-xs text-orange-600">+0.3% from last month</p>
                </div>
                <Target className="w-8 h-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Active Users</p>
                  <p className="text-2xl font-bold">8,945</p>
                  <p className="text-xs text-purple-600">+15.1% from last month</p>
                </div>
                <Users className="w-8 h-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Analytics Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="journey">Customer Journey</TabsTrigger>
            <TabsTrigger value="attribution">Attribution</TabsTrigger>
            <TabsTrigger value="cohorts">Cohorts</TabsTrigger>
            <TabsTrigger value="realtime">Real-time</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <D3AnalyticsDashboard
              charts={chartData}
              realTimeData={true}
              interactive={true}
              onDataInteraction={handleDataInteraction}
            />
          </TabsContent>

          <TabsContent value="journey" className="space-y-6">
            <D3CustomerJourney
              data={journeyData}
              width={800}
              height={400}
              onNodeClick={(node) => console.log('Node clicked:', node)}
            />
          </TabsContent>

          <TabsContent value="attribution" className="space-y-6">
            <D3AttributionAnalysis
              attributionData={attributionData}
              comparisonData={comparisonData}
              selectedModel={selectedAttributionModel}
              onModelChange={setSelectedAttributionModel}
              onChannelClick={(channel) => console.log('Channel clicked:', channel)}
            />
          </TabsContent>

          <TabsContent value="cohorts" className="space-y-6">
            <D3CohortAnalysis
              data={cohortData}
              metric="retentionRate"
              onCellClick={(cohort) => console.log('Cohort cell clicked:', cohort)}
            />
          </TabsContent>

          <TabsContent value="realtime" className="space-y-6">
            <D3RealTimeMetrics
              streams={realTimeStreams}
              updateInterval={5000}
              maxDataPoints={50}
              onMetricClick={(metric) => console.log('Metric clicked:', metric)}
            />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default AnalyticsDashboardPage;
