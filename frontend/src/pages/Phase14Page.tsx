import React, { useState } from 'react';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import UniversalAPIGateway from '../components/integrations/UniversalAPIGateway';
import PrebuiltConnectors from '../components/integrations/PrebuiltConnectors';
import DataPipelineOrchestration from '../components/integrations/DataPipelineOrchestration';
import RealtimeStreaming from '../components/integrations/RealtimeStreaming';

const Phase14Page: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Phase 14: Advanced Integration Platform</h1>
        <p className="text-gray-600 mt-2">
          Universal API gateway, prebuilt connectors, data orchestration, and real-time streaming
        </p>
      </div>

      <Tabs defaultValue="gateway" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="gateway">API Gateway</TabsTrigger>
          <TabsTrigger value="connectors">Connectors</TabsTrigger>
          <TabsTrigger value="orchestration">Orchestration</TabsTrigger>
          <TabsTrigger value="streaming">Real-time Streaming</TabsTrigger>
        </TabsList>

        <TabsContent value="gateway" className="mt-6">
          <UniversalAPIGateway />
        </TabsContent>

        <TabsContent value="connectors" className="mt-6">
          <PrebuiltConnectors />
        </TabsContent>

        <TabsContent value="orchestration" className="mt-6">
          <DataPipelineOrchestration />
        </TabsContent>

        <TabsContent value="streaming" className="mt-6">
          <RealtimeStreaming />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Phase14Page;