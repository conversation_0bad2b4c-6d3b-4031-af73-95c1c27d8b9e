import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import PredictiveAnalyticsEngine from '../components/ai/PredictiveAnalyticsEngine';
import AICustomerIntelligence from '../components/ai/AICustomerIntelligence';
import AutomatedAnomalyDetection from '../components/ai/AutomatedAnomalyDetection';
import NaturalLanguageAnalytics from '../components/ai/NaturalLanguageAnalytics';
import ComputerVisionAnalytics from '../components/ai/ComputerVisionAnalytics';
import AdvancedRecommendationSystems from '../components/ai/AdvancedRecommendationSystems';

const Phase21Page: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Phase 21: Advanced AI & Machine Learning Intelligence</h1>
        <p className="text-gray-600 mt-2">
          Cutting-edge AI-powered analytics with predictive intelligence, natural language processing, and computer vision
        </p>
      </div>

      <Tabs defaultValue="predictive" className="w-full">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="predictive">Predictive Engine</TabsTrigger>
          <TabsTrigger value="customer-ai">Customer AI</TabsTrigger>
          <TabsTrigger value="anomaly">Anomaly Detection</TabsTrigger>
          <TabsTrigger value="nlp">Natural Language</TabsTrigger>
          <TabsTrigger value="vision">Computer Vision</TabsTrigger>
          <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
        </TabsList>

        <TabsContent value="predictive" className="mt-6">
          <PredictiveAnalyticsEngine />
        </TabsContent>

        <TabsContent value="customer-ai" className="mt-6">
          <AICustomerIntelligence />
        </TabsContent>

        <TabsContent value="anomaly" className="mt-6">
          <AutomatedAnomalyDetection />
        </TabsContent>

        <TabsContent value="nlp" className="mt-6">
          <NaturalLanguageAnalytics />
        </TabsContent>

        <TabsContent value="vision" className="mt-6">
          <ComputerVisionAnalytics />
        </TabsContent>

        <TabsContent value="recommendations" className="mt-6">
          <AdvancedRecommendationSystems />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Phase21Page;