
import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import * as Sentry from '@sentry/react';
import './index.css';
import Layout from './components/Layout';
import DashboardPage from './pages/DashboardPage';
import CampaignsPage from './pages/CampaignsPage';
import AnalyticsPage from './pages/AnalyticsPage';
import AttributionPage from './pages/AttributionPage';
import RealtimeAnalyticsPage from './pages/RealtimeAnalyticsPage';
import IntegrationsPage from './pages/IntegrationsPage';
import SettingsPage from './pages/SettingsPage';
import Enhanced2DVisualizationsPage from './pages/Enhanced2DVisualizationsPage';
import AnalyticsDashboardPage from './pages/AnalyticsDashboardPage';
import AIAnalyticsPage from './pages/AIAnalyticsPage';
import WhiteLabelPage from './pages/WhiteLabelPage';
import Phase14Page from './pages/Phase14Page';
import Phase15Page from './pages/Phase15Page';
import Phase16Page from './pages/Phase16Page';
import Phase17Page from './pages/Phase17Page';
import Phase18Page from './pages/Phase18Page';
import Phase19Page from './pages/Phase19Page';
// Phase20Page removed - VR/AR components replaced with D3.js
import Phase21Page from './pages/Phase21Page';
import Phase22Page from './pages/Phase22Page';
import Phase23Page from './pages/Phase23Page';
import ExecutivePage from './pages/ExecutivePage';
import EnhancedUXPage from './pages/EnhancedUXPage';

// Mock auth provider
const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return <>{children}</>;
};

// Mock auth hook
export const useAuth = () => {
  return {
    user: { name: 'Demo User', email: '<EMAIL>' },
    logout: () => console.log('Logout clicked')
  };
};

function App() {
  return (
    <AuthProvider>
      <Layout>
        <Routes>
          <Route path="/" element={<Navigate to="/dashboard" replace />} />
          <Route path="/dashboard" element={<DashboardPage />} />
          <Route path="/links" element={<CampaignsPage />} />
          <Route path="/analytics" element={<AnalyticsPage />} />
          <Route path="/analytics/advanced" element={<RealtimeAnalyticsPage />} />
          <Route path="/analytics/d3-dashboard" element={<AnalyticsDashboardPage />} />
          <Route path="/attribution" element={<AttributionPage />} />
          <Route path="/integrations" element={<IntegrationsPage />} />
          <Route path="/2d-visualizations" element={<Enhanced2DVisualizationsPage />} />
          <Route path="/ai-analytics" element={<AIAnalyticsPage />} />
          <Route path="/white-label" element={<WhiteLabelPage />} />
          <Route path="/phase14" element={<Phase14Page />} />
          <Route path="/phase15" element={<Phase15Page />} />
          <Route path="/phase16" element={<Phase16Page />} />
          <Route path="/phase17" element={<Phase17Page />} />
          <Route path="/phase18" element={<Phase18Page />} />
          <Route path="/phase19" element={<Phase19Page />} />
          {/* Phase20Page removed - VR/AR components replaced with D3.js */}
          <Route path="/phase21" element={<Phase21Page />} />
          <Route path="/phase22" element={<Phase22Page />} />
          <Route path="/phase23" element={<Phase23Page />} />
          <Route path="/executive" element={<ExecutivePage />} />
          <Route path="/enhanced-ux" element={<EnhancedUXPage />} />
          <Route path="/settings" element={<SettingsPage />} />
        </Routes>
      </Layout>
    </AuthProvider>
  );
}

export default Sentry.withErrorBoundary(App, {
  fallback: ({ error, resetError }) => (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full bg-white shadow-lg rounded-lg p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Something went wrong</h2>
        <p className="text-gray-600 mb-4">
          An unexpected error occurred. The error has been logged and we're working on fixing it.
        </p>
        <div className="flex gap-3">
          <button
            onClick={resetError}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Try again
          </button>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-gray-200 text-gray-900 rounded-md hover:bg-gray-300 transition-colors"
          >
            Reload page
          </button>
        </div>
        {process.env.NODE_ENV === 'development' && (
          <details className="mt-4">
            <summary className="cursor-pointer text-sm text-gray-500">Error details</summary>
            <pre className="mt-2 text-xs text-red-600 bg-red-50 p-2 rounded overflow-auto">
              {error.toString()}
            </pre>
          </details>
        )}
      </div>
    </div>
  ),
  beforeCapture: (scope) => {
    scope.setTag('errorBoundary', true);
  },
});
