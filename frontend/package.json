{"name": "ecommerce-analytics-frontend", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "type-check": "tsc --noEmit", "test": "vitest"}, "dependencies": {"@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^3.3.4", "@radix-ui/react-tabs": "^1.1.12", "@sentry/react": "^9.34.0", "@sentry/tracing": "^7.120.3", "@tanstack/react-query": "^5.25.0", "axios": "^1.6.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "d3": "^7.8.5", "@types/d3": "^7.4.3", "ecommerce-analytics-shared-types": "file:../packages/shared-types", "framer-motion": "^12.19.2", "lucide-react": "^0.365.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.51.0", "react-router-dom": "^6.22.3", "recharts": "^2.12.2", "tailwind-merge": "^3.3.1", "zod": "^3.22.4"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.19", "jsdom": "^26.1.0", "postcss": "^8.4.38", "tailwindcss": "^3.4.1", "typescript": "^5.3.3", "vite": "^5.2.0", "vitest": "^1.4.0"}}