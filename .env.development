# Development Environment Configuration
# This file overrides .env for development

# =============================================================================
# ENVIRONMENT SETTINGS
# =============================================================================
NODE_ENV=development
BUILD_TARGET=development
LOG_LEVEL=debug

# =============================================================================
# SERVICE PORTS
# =============================================================================
FRONTEND_PORT=3000
DASHBOARD_PORT=3001
ANALYTICS_PORT=3002
INTEGRATION_PORT=3003

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
DB_HOST=localhost
DB_PORT=5432
DB_NAME=ecommerce_analytics_dev
DB_USER=postgres
DB_PASSWORD=devpassword

# Relaxed pool settings for development
DB_POOL_MIN=1
DB_POOL_MAX=5
DB_POOL_IDLE_TIMEOUT=10000
DB_POOL_ACQUIRE_TIMEOUT=30000

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=1

# Development Redis settings
REDIS_CONNECT_TIMEOUT=5000
REDIS_COMMAND_TIMEOUT=3000
REDIS_RETRY_DELAY_ON_FAILURE=100
REDIS_MAX_RETRY_ATTEMPTS=2

# =============================================================================
# JWT AND SECURITY
# =============================================================================
JWT_SECRET=dev_jwt_secret_not_for_production_use_only
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Relaxed rate limiting for development
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000

# Development CORS - allow all origins
CORS_ORIGIN=*

# =============================================================================
# FRONTEND CONFIGURATION
# =============================================================================
VITE_API_URL=http://localhost:3001/api
VITE_ANALYTICS_URL=http://localhost:3002/api
VITE_INTEGRATION_URL=http://localhost:3003/api
VITE_WS_URL=ws://localhost:3001

# =============================================================================
# THIRD-PARTY INTEGRATIONS (TEST CREDENTIALS)
# =============================================================================

# Test Shopify Integration
SHOPIFY_API_KEY=test_shopify_key
SHOPIFY_SECRET=test_shopify_secret
SHOPIFY_WEBHOOK_SECRET=test_webhook_secret

# Test WooCommerce Integration
WOOCOMMERCE_KEY=ck_test_key
WOOCOMMERCE_SECRET=cs_test_secret

# =============================================================================
# EMAIL CONFIGURATION (Development)
# =============================================================================
# Use Mailcatcher for development
SMTP_HOST=localhost
SMTP_PORT=1025
SMTP_SECURE=false
SMTP_USER=
SMTP_PASS=
EMAIL_FROM=dev@localhost

# =============================================================================
# FILE STORAGE (Development)
# =============================================================================
UPLOAD_PATH=./dev-uploads
EXPORT_PATH=./dev-exports
LOG_PATH=./dev-logs

# =============================================================================
# MONITORING AND OBSERVABILITY (Development)
# =============================================================================
SENTRY_DSN=

# Health checks
HEALTH_CHECK_INTERVAL=10
HEALTH_CHECK_TIMEOUT=5
HEALTH_CHECK_RETRIES=2

# Performance monitoring enabled for development
ENABLE_PERFORMANCE_MONITORING=true
ENABLE_METRICS=true
METRICS_PORT=9090

# =============================================================================
# DEVELOPMENT TOOLS
# =============================================================================
# PgAdmin
PGADMIN_DEFAULT_EMAIL=dev@localhost
PGADMIN_DEFAULT_PASSWORD=devpassword

# Redis Commander
REDIS_COMMANDER_PORT=8081

# Mailcatcher
MAILCATCHER_WEB_PORT=1080
MAILCATCHER_SMTP_PORT=1025

# =============================================================================
# FEATURE FLAGS (All enabled for development)
# =============================================================================
ENABLE_REAL_TIME_ANALYTICS=true
ENABLE_ADVANCED_ATTRIBUTION=true
ENABLE_COHORT_ANALYSIS=true
ENABLE_FORECASTING=true
ENABLE_A_B_TESTING=true
ENABLE_CUSTOM_EVENTS=true

# Development debugging
DEBUG=*
VERBOSE_LOGGING=true