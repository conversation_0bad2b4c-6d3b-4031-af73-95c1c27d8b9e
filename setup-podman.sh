#!/bin/bash
# <PERSON>dman setup script for e-commerce analytics SaaS

set -e

echo "🚀 Setting up Podman environment for E-commerce Analytics SaaS"

# Enable Podman socket for Docker compatibility
echo "📡 Enabling Podman socket..."
systemctl --user enable --now podman.socket

# Set environment variables for Docker compatibility
echo "🔧 Setting up environment variables..."
export DOCKER_HOST=unix:///run/user/$(id -u)/podman/podman.sock
echo "export DOCKER_HOST=unix:///run/user/$(id -u)/podman/podman.sock" >> ~/.bashrc

# Create Podman-specific docker-compose override
echo "📝 Creating Podman-optimized docker-compose configuration..."
cat > docker-compose.podman.yml << 'EOF'
version: '3.8'

# Podman-specific overrides
services:
  # Database Services - Using docker.io explicitly for Podman
  postgres:
    image: docker.io/postgres:15-alpine
    user: "1001:1001"
    
  redis:
    image: docker.io/redis:7-alpine
    user: "1001:1001"

  # Application Services - Enhanced for Podman security
  link-tracking:
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    cap_add:
      - NET_BIND_SERVICE
    
  integration:
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
      
  analytics:
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
      
  dashboard:
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
      
  frontend:
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL

  # Monitoring Services
  prometheus:
    image: docker.io/prom/prometheus:latest
    user: "1001:1001"
    
  grafana:
    image: docker.io/grafana/grafana:latest
    user: "1001:1001"
EOF

# Create Podman-specific aliases
echo "⚡ Creating helpful aliases..."
cat >> ~/.bashrc << 'EOF'

# Podman aliases for Docker compatibility
alias docker='podman'
alias docker-compose='podman-compose'

# E-commerce Analytics SaaS shortcuts
alias ecom-start='cd ~/ecommerce-analytics-saas && podman-compose -f docker-compose.yml -f docker-compose.podman.yml up -d'
alias ecom-stop='cd ~/ecommerce-analytics-saas && podman-compose down'
alias ecom-logs='cd ~/ecommerce-analytics-saas && podman-compose logs -f'
alias ecom-status='cd ~/ecommerce-analytics-saas && podman-compose ps'
alias ecom-restart='cd ~/ecommerce-analytics-saas && podman-compose restart'
EOF

# Test Podman installation
echo "🧪 Testing Podman installation..."
if command -v podman &> /dev/null; then
    echo "✅ Podman is installed: $(podman --version)"
else
    echo "❌ Podman is not installed. Installing..."
    # Add Podman installation for different distros
    if command -v apt &> /dev/null; then
        sudo apt-get update && sudo apt-get install -y podman podman-compose
    elif command -v dnf &> /dev/null; then
        sudo dnf install -y podman podman-compose
    elif command -v pacman &> /dev/null; then
        sudo pacman -S podman podman-compose
    else
        echo "Please install Podman manually for your distribution"
        exit 1
    fi
fi

# Test Podman-compose installation
if command -v podman-compose &> /dev/null; then
    echo "✅ Podman-compose is available: $(podman-compose --version)"
else
    echo "📦 Installing podman-compose..."
    pip3 install --user podman-compose
fi

# Initialize Podman if needed
echo "🏗️ Initializing Podman..."
podman system info > /dev/null 2>&1 || podman system reset --force

# Create Podman network
echo "🌐 Creating Podman network..."
podman network exists ecommerce-network || podman network create ecommerce-network

echo ""
echo "🎉 Podman setup complete!"
echo ""
echo "🚀 Quick start commands:"
echo "  ecom-start     # Start all services"
echo "  ecom-stop      # Stop all services"
echo "  ecom-logs      # View logs"
echo "  ecom-status    # Check service status"
echo ""
echo "📱 Application URLs:"
echo "  Frontend:      http://localhost:5173"
echo "  Dashboard API: http://localhost:3000"
echo "  Analytics:     http://localhost:3002"
echo "  Link Tracking: http://localhost:8080"
echo "  Integration:   http://localhost:3001"
echo ""
echo "🔐 Demo credentials:"
echo "  Email:    <EMAIL>"
echo "  Password: password"
echo ""
echo "To start the application:"
echo "  source ~/.bashrc  # Reload environment"
echo "  ecom-start        # Start all services"
EOF