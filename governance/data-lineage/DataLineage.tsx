
import React from 'react';
import { Graph } from 'react-d3-graph';

// Mock data for the data lineage graph. In a real application, this would come from an API.
const dataLineageData = {
  nodes: [
    { id: 'Source: CRM' },
    { id: 'Transformation: Cleanse Data' },
    { id: 'Dataset: Customer Profiles' },
    { id: 'Analysis: Churn Prediction' },
    { id: 'Report: Monthly Churn Report' },
  ],
  links: [
    { source: 'Source: CRM', target: 'Transformation: Cleanse Data' },
    { source: 'Transformation: Cleanse Data', target: 'Dataset: Customer Profiles' },
    { source: 'Dataset: Customer Profiles', target: 'Analysis: Churn Prediction' },
    { source: 'Analysis: Churn Prediction', target: 'Report: Monthly Churn Report' },
  ],
};

const myConfig = {
  nodeHighlightBehavior: true,
  node: {
    color: 'lightblue',
    size: 120,
    highlightStrokeColor: 'blue',
  },
  link: {
    highlightColor: 'lightblue',
  },
};

const DataLineage = () => {
  return (
    <div style={{ fontFamily: 'Arial, sans-serif', padding: '20px' }}>
      <h1 style={{ borderBottom: '2px solid #eee', paddingBottom: '10px' }}>Data Lineage</h1>

      <div style={{ border: '1px solid #ddd', marginTop: '20px' }}>
        <Graph
          id="data-lineage-graph"
          data={dataLineageData}
          config={myConfig}
        />
      </div>
    </div>
  );
};

export default DataLineage;
