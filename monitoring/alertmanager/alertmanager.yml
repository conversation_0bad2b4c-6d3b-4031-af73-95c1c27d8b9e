global:
  # Global configuration
  smtp_smarthost: '${SMTP_HOST}:${SMTP_PORT}'
  smtp_from: '${ALERT_EMAIL_FROM}'
  smtp_auth_username: '${SMTP_USER}'
  smtp_auth_password: '${SMTP_PASSWORD}'
  smtp_require_tls: true

# Templates for notifications
templates:
  - '/etc/alertmanager/templates/*.tmpl'

# Route tree for alerts
route:
  group_by: ['alertname', 'cluster', 'service']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'default'
  routes:
    # Critical alerts
    - match:
        severity: critical
      receiver: 'critical-alerts'
      group_wait: 10s
      group_interval: 5m
      repeat_interval: 30m
      routes:
        # Database critical alerts
        - match:
            team: database
          receiver: 'database-critical'
          group_wait: 5s
          repeat_interval: 15m
        
        # Infrastructure critical alerts
        - match:
            team: infrastructure
          receiver: 'infrastructure-critical'
          group_wait: 5s
          repeat_interval: 15m

    # Warning alerts
    - match:
        severity: warning
      receiver: 'warning-alerts'
      group_wait: 30s
      group_interval: 10m
      repeat_interval: 2h

    # Application specific alerts
    - match:
        team: platform
      receiver: 'platform-team'
      group_wait: 15s
      group_interval: 5m
      repeat_interval: 1h

    - match:
        team: analytics
      receiver: 'analytics-team'
      group_wait: 15s
      group_interval: 5m
      repeat_interval: 1h

    - match:
        team: integrations
      receiver: 'integrations-team'
      group_wait: 15s
      group_interval: 5m
      repeat_interval: 1h

    # Security alerts
    - match:
        team: security
      receiver: 'security-team'
      group_wait: 5s
      group_interval: 2m
      repeat_interval: 30m

    # Business alerts
    - match:
        team: marketing
      receiver: 'marketing-team'
      group_wait: 60s
      group_interval: 30m
      repeat_interval: 4h

    # Informational alerts
    - match:
        severity: info
      receiver: 'info-alerts'
      group_wait: 60s
      group_interval: 30m
      repeat_interval: 6h

# Inhibit rules to prevent alert spam
inhibit_rules:
  # Inhibit warning alerts if critical alert is firing for the same service
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'instance', 'job']

  # Inhibit service down alerts if node is down
  - source_match:
      alertname: 'NodeDown'
    target_match_re:
      alertname: '(ServiceDown|HighErrorRate|HighResponseTime)'
    equal: ['instance']

  # Inhibit database connection alerts if database is down
  - source_match:
      alertname: 'PostgreSQLDown'
    target_match_re:
      alertname: '(DatabaseConnectionPoolExhaustion|DatabaseSlowQueries)'
    equal: ['instance']

# Receivers define how to send notifications
receivers:
  - name: 'default'
    email_configs:
      - to: '${DEFAULT_EMAIL_TO}'
        subject: '[{{ .Status | toUpper }}] {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Details: {{ range .Labels.SortedPairs }}
            {{ .Name }}: {{ .Value }}
          {{ end }}
          {{ end }}

  - name: 'critical-alerts'
    email_configs:
      - to: '${CRITICAL_EMAIL_TO}'
        subject: '🚨 [CRITICAL] {{ .GroupLabels.alertname }}'
        body: |
          CRITICAL ALERT FIRED!
          
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Severity: {{ .Labels.severity }}
          Service: {{ .Labels.job }}
          Instance: {{ .Labels.instance }}
          
          Runbook: {{ .Annotations.runbook_url }}
          
          Time: {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}
          {{ end }}
        html: |
          <h2>🚨 CRITICAL ALERT</h2>
          {{ range .Alerts }}
          <h3>{{ .Annotations.summary }}</h3>
          <p><strong>Description:</strong> {{ .Annotations.description }}</p>
          <p><strong>Severity:</strong> <span style="color: red;">{{ .Labels.severity }}</span></p>
          <p><strong>Service:</strong> {{ .Labels.job }}</p>
          <p><strong>Instance:</strong> {{ .Labels.instance }}</p>
          <p><strong>Time:</strong> {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}</p>
          {{ if .Annotations.runbook_url }}
          <p><a href="{{ .Annotations.runbook_url }}">📖 Runbook</a></p>
          {{ end }}
          {{ end }}
    
    slack_configs:
      - api_url: '${SLACK_WEBHOOK_URL}'
        channel: '#alerts-critical'
        username: 'AlertManager'
        icon_emoji: ':fire:'
        title: '🚨 Critical Alert: {{ .GroupLabels.alertname }}'
        text: |
          {{ range .Alerts }}
          *{{ .Annotations.summary }}*
          {{ .Annotations.description }}
          
          *Service:* {{ .Labels.job }}
          *Instance:* {{ .Labels.instance }}
          *Severity:* {{ .Labels.severity }}
          {{ end }}
        actions:
          - type: button
            text: 'View in Grafana'
            url: '${GRAFANA_URL}/d/app-overview'
          - type: button
            text: 'View Logs'
            url: '${GRAFANA_URL}/explore'

  - name: 'warning-alerts'
    email_configs:
      - to: '${WARNING_EMAIL_TO}'
        subject: '⚠️ [WARNING] {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Service: {{ .Labels.job }}
          Instance: {{ .Labels.instance }}
          {{ end }}

  - name: 'database-critical'
    email_configs:
      - to: '${DBA_EMAIL}'
        subject: '🗄️ [DB CRITICAL] {{ .GroupLabels.alertname }}'
        body: |
          DATABASE CRITICAL ALERT!
          
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Instance: {{ .Labels.instance }}
          {{ end }}
    
    slack_configs:
      - api_url: '${SLACK_WEBHOOK_URL}'
        channel: '#db-alerts'
        username: 'DB-AlertManager'
        icon_emoji: ':database:'
        title: '🗄️ Database Critical: {{ .GroupLabels.alertname }}'

  - name: 'infrastructure-critical'
    email_configs:
      - to: '${INFRA_EMAIL}'
        subject: '🏗️ [INFRA CRITICAL] {{ .GroupLabels.alertname }}'
        body: |
          INFRASTRUCTURE CRITICAL ALERT!
          
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Instance: {{ .Labels.instance }}
          {{ end }}

  - name: 'platform-team'
    slack_configs:
      - api_url: '${SLACK_WEBHOOK_URL}'
        channel: '#platform-alerts'
        username: 'Platform-AlertManager'
        icon_emoji: ':gear:'
        title: '⚙️ Platform Alert: {{ .GroupLabels.alertname }}'

  - name: 'analytics-team'
    slack_configs:
      - api_url: '${SLACK_WEBHOOK_URL}'
        channel: '#analytics-alerts'
        username: 'Analytics-AlertManager'
        icon_emoji: ':chart_with_upwards_trend:'
        title: '📊 Analytics Alert: {{ .GroupLabels.alertname }}'

  - name: 'integrations-team'
    slack_configs:
      - api_url: '${SLACK_WEBHOOK_URL}'
        channel: '#integrations-alerts'
        username: 'Integrations-AlertManager'
        icon_emoji: ':link:'
        title: '🔗 Integration Alert: {{ .GroupLabels.alertname }}'

  - name: 'security-team'
    email_configs:
      - to: '${SECURITY_EMAIL}'
        subject: '🔒 [SECURITY] {{ .GroupLabels.alertname }}'
        body: |
          SECURITY ALERT!
          
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Source: {{ .Labels.instance }}
          Time: {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}
          {{ end }}
    
    slack_configs:
      - api_url: '${SLACK_SECURITY_WEBHOOK_URL}'
        channel: '#security-alerts'
        username: 'Security-AlertManager'
        icon_emoji: ':lock:'
        title: '🔒 Security Alert: {{ .GroupLabels.alertname }}'

  - name: 'marketing-team'
    email_configs:
      - to: '${MARKETING_EMAIL}'
        subject: '📈 [MARKETING] {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          {{ end }}

  - name: 'info-alerts'
    slack_configs:
      - api_url: '${SLACK_WEBHOOK_URL}'
        channel: '#general-alerts'
        username: 'Info-AlertManager'
        icon_emoji: ':information_source:'
        title: 'ℹ️ Info: {{ .GroupLabels.alertname }}'

# Time intervals for muting alerts
time_intervals:
  - name: 'business-hours'
    time_intervals:
      - times:
          - start_time: '09:00'
            end_time: '17:00'
        weekdays: ['monday:friday']

  - name: 'weekend'
    time_intervals:
      - weekdays: ['saturday', 'sunday']

  - name: 'maintenance-window'
    time_intervals:
      - times:
          - start_time: '02:00'
            end_time: '04:00'
        weekdays: ['sunday']