# E-commerce Analytics SaaS - Monitoring & Observability Stack

## Overview

This directory contains a comprehensive monitoring and observability stack for the e-commerce analytics platform. The stack provides metrics collection, alerting, log aggregation, distributed tracing, and visualization capabilities.

## Architecture

```mermaid
graph TB
    subgraph "Applications"
        APP1[Dashboard API]
        APP2[Analytics Service]
        APP3[Integration Service]
        APP4[Link Tracking]
        APP5[Error Tracking]
        APP6[Admin Service]
    end
    
    subgraph "Infrastructure"
        DB[(PostgreSQL)]
        CACHE[(Redis)]
        LB[Nginx]
        SYS[System]
    end
    
    subgraph "Metrics Collection"
        PROM[Prometheus]
        NODE[Node Exporter]
        POSTGRES_EXP[PostgreSQL Exporter]
        REDIS_EXP[Redis Exporter]
        NGINX_EXP[Nginx Exporter]
        CADVISOR[cAdvisor]
        BLACKBOX[Blackbox Exporter]
    end
    
    subgraph "Storage & Processing"
        VICTORIA[Victoria Metrics]
        LOKI[Loki]
        JAEGER[<PERSON><PERSON><PERSON>]
    end
    
    subgraph "Alerting"
        ALERTMGR[AlertManager]
        PROMTAIL[Promtail]
    end
    
    subgraph "Visualization"
        GRAFANA[Grafana]
    end
    
    APP1 --> PROM
    APP2 --> PROM
    APP3 --> PROM
    APP4 --> PROM
    APP5 --> PROM
    APP6 --> PROM
    
    DB --> POSTGRES_EXP
    CACHE --> REDIS_EXP
    LB --> NGINX_EXP
    SYS --> NODE
    SYS --> CADVISOR
    
    POSTGRES_EXP --> PROM
    REDIS_EXP --> PROM
    NGINX_EXP --> PROM
    NODE --> PROM
    CADVISOR --> PROM
    BLACKBOX --> PROM
    
    PROM --> VICTORIA
    PROM --> ALERTMGR
    PROMTAIL --> LOKI
    
    APP1 --> JAEGER
    APP2 --> JAEGER
    APP3 --> JAEGER
    APP4 --> JAEGER
    
    VICTORIA --> GRAFANA
    LOKI --> GRAFANA
    JAEGER --> GRAFANA
    PROM --> GRAFANA
    
    ALERTMGR --> GRAFANA
```

## Components

### 1. Metrics Collection & Storage

#### **Prometheus** (Port: 9090)
- Primary metrics collection and short-term storage
- Scrapes metrics from all services and exporters
- Evaluates alerting rules
- 30-day retention with compression

#### **Victoria Metrics** (Port: 8428)
- Long-term metrics storage (12 months)
- High-performance time series database
- Compatible with Prometheus query language

#### **Exporters**
- **Node Exporter** (9100): System-level metrics (CPU, memory, disk, network)
- **PostgreSQL Exporter** (9187): Database performance and health metrics
- **Redis Exporter** (9121): Cache performance and usage metrics
- **Nginx Exporter** (9113): Web server metrics
- **cAdvisor** (8080): Container resource usage metrics
- **Blackbox Exporter** (9115): External endpoint monitoring

### 2. Log Aggregation

#### **Loki** (Port: 3100)
- Centralized log storage and querying
- Efficient log indexing and compression
- 31-day retention period

#### **Promtail**
- Log shipping agent
- Parses and labels application logs
- Supports multiple log formats (JSON, structured, unstructured)

### 3. Distributed Tracing

#### **Jaeger** (Port: 16686)
- End-to-end request tracing
- Service dependency mapping
- Performance bottleneck identification

### 4. Alerting

#### **AlertManager** (Port: 9093)
- Alert routing and notification management
- Supports multiple notification channels (email, Slack, webhooks)
- Alert grouping and deduplication
- Configurable escalation rules

### 5. Visualization

#### **Grafana** (Port: 3007)
- Unified observability dashboard
- Pre-configured dashboards for applications and infrastructure
- Custom alerting and notification rules
- Multi-datasource support

## Quick Start

### Prerequisites

- Docker and Docker Compose
- At least 4GB RAM available
- Ports 3007, 3100, 8080, 8428, 9090-9121, 16686 available

### 1. Start the Monitoring Stack

```bash
# Start all monitoring services
./scripts/start-monitoring.sh start

# Check status
./scripts/start-monitoring.sh status

# View logs
./scripts/start-monitoring.sh logs
```

### 2. Access Dashboards

- **Grafana**: http://localhost:3007 (admin/admin123)
- **Prometheus**: http://localhost:9090
- **AlertManager**: http://localhost:9093
- **Jaeger**: http://localhost:16686
- **Loki**: http://localhost:3100

### 3. Stop the Stack

```bash
./scripts/start-monitoring.sh stop
```

## Configuration

### Environment Variables

Create a `.env` file with the following variables:

```env
# Database connection
DB_USER=postgres
DB_PASSWORD=your_password
DB_NAME=ecommerce_analytics

# Redis connection
REDIS_PASSWORD=your_redis_password

# Email notifications
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password
ALERT_EMAIL_FROM=<EMAIL>
DEFAULT_EMAIL_TO=<EMAIL>
CRITICAL_EMAIL_TO=<EMAIL>
WARNING_EMAIL_TO=<EMAIL>

# Team-specific emails
DBA_EMAIL=<EMAIL>
INFRA_EMAIL=<EMAIL>
SECURITY_EMAIL=<EMAIL>
MARKETING_EMAIL=<EMAIL>

# Slack notifications
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
SLACK_SECURITY_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SECURITY/WEBHOOK

# Grafana URLs
GRAFANA_URL=http://localhost:3007

# Grafana admin credentials
GRAFANA_ADMIN_USER=admin
GRAFANA_ADMIN_PASSWORD=admin123
```

### Custom Alerts

Add custom alert rules in `prometheus/alerts/`:

```yaml
groups:
  - name: custom.rules
    rules:
      - alert: CustomAlert
        expr: your_metric > threshold
        for: 5m
        labels:
          severity: warning
          team: your-team
        annotations:
          summary: "Custom alert description"
          description: "Detailed alert information"
```

### Custom Dashboards

Add Grafana dashboard JSON files to:
- `grafana/dashboards/application/` - Application dashboards
- `grafana/dashboards/infrastructure/` - Infrastructure dashboards
- `grafana/dashboards/business/` - Business metrics dashboards

## Pre-configured Dashboards

### Application Dashboards
- **Application Overview**: Service health, response times, error rates
- **Database Performance**: Query performance, connection pools, locks
- **Cache Performance**: Redis metrics, hit rates, memory usage
- **Error Tracking**: Error rates, trends, and resolution status

### Infrastructure Dashboards
- **System Overview**: CPU, memory, disk, network usage
- **Container Metrics**: Docker container resource usage
- **Network Performance**: Traffic, errors, latency
- **Security Monitoring**: Failed authentications, suspicious activity

### Business Dashboards
- **Link Analytics**: Click-through rates, geographic distribution
- **Conversion Tracking**: Attribution analysis, funnel metrics
- **Revenue Metrics**: Order values, conversion rates
- **User Behavior**: Session analytics, retention rates

## Alert Rules

### Critical Alerts (Immediate Response)
- Service down
- Database unavailable
- High error rates (>15%)
- Critical resource exhaustion

### Warning Alerts (Investigate)
- High response times
- Resource usage warnings
- Database slow queries
- Cache performance issues

### Informational Alerts (Monitor)
- Business metric anomalies
- Capacity planning warnings
- Security events

## Performance Tuning

### Prometheus
- Adjust retention period: `--storage.tsdb.retention.time=30d`
- Tune memory usage: `--storage.tsdb.retention.size=10GB`
- Optimize scrape intervals based on metric importance

### Grafana
- Enable caching for better dashboard performance
- Use query result caching for expensive queries
- Optimize dashboard refresh rates

### Loki
- Configure appropriate log retention
- Use efficient log parsing patterns
- Implement log sampling for high-volume applications

## Troubleshooting

### Common Issues

1. **High Memory Usage**
   - Reduce Prometheus retention period
   - Optimize metric labels and cardinality
   - Increase container memory limits

2. **Missing Metrics**
   - Check service discovery configuration
   - Verify network connectivity between services
   - Review scrape target health in Prometheus

3. **Alert Fatigue**
   - Fine-tune alert thresholds
   - Implement proper alert grouping
   - Use inhibition rules to prevent spam

4. **Slow Dashboards**
   - Optimize PromQL queries
   - Use recording rules for complex calculations
   - Implement dashboard-level caching

### Debugging Commands

```bash
# Check service health
docker-compose -f monitoring/docker-compose.monitoring.yml ps

# View service logs
docker-compose -f monitoring/docker-compose.monitoring.yml logs prometheus
docker-compose -f monitoring/docker-compose.monitoring.yml logs grafana

# Test Prometheus configuration
docker exec prometheus promtool check config /etc/prometheus/prometheus.yml

# Test AlertManager configuration
docker exec alertmanager amtool check-config /etc/alertmanager/alertmanager.yml
```

## Maintenance

### Regular Tasks

1. **Weekly**
   - Review alert noise and adjust thresholds
   - Check disk usage on monitoring volumes
   - Verify backup procedures

2. **Monthly**
   - Update monitoring stack components
   - Review and optimize expensive queries
   - Clean up unused dashboards and alerts

3. **Quarterly**
   - Review monitoring architecture
   - Evaluate new monitoring tools and features
   - Update runbooks and documentation

### Backup and Recovery

```bash
# Backup Grafana dashboards
docker exec grafana grafana-cli admin export-dashboard --folder=Application

# Backup Prometheus data
docker cp prometheus:/prometheus/data ./backup/prometheus-$(date +%Y%m%d)

# Backup AlertManager configuration
cp -r monitoring/alertmanager ./backup/alertmanager-$(date +%Y%m%d)
```

## Security Considerations

1. **Authentication**
   - Change default Grafana admin password
   - Implement proper user management
   - Use strong passwords for all services

2. **Network Security**
   - Use Docker networks for service isolation
   - Implement firewall rules for external access
   - Configure SSL/TLS for external endpoints

3. **Data Protection**
   - Encrypt sensitive configuration data
   - Use secrets management for credentials
   - Implement proper access controls

## Contributing

To add new monitoring features:

1. Update relevant configuration files
2. Add corresponding alerts if needed
3. Create or update Grafana dashboards
4. Update this documentation
5. Test the changes thoroughly

## Support

For issues with the monitoring stack:

1. Check service logs for error messages
2. Verify configuration syntax
3. Review resource usage and limits
4. Consult component-specific documentation
5. Create an issue with detailed error information