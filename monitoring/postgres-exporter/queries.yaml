pg_replication:
  query: "SELECT CASE WHEN NOT pg_is_in_recovery() THEN 0 ELSE GREATEST (0, EXTRACT(EPOCH FROM (now() - pg_last_xact_replay_timestamp()))) END AS lag"
  master: true
  metrics:
    - lag:
        usage: "GAUGE"
        description: "Replication lag behind master in seconds"

pg_database:
  query: |
    SELECT
      pg_database.datname,
      pg_database_size(pg_database.datname) as size_bytes,
      pg_stat_database.numbackends as connections,
      pg_stat_database.xact_commit as transactions_committed,
      pg_stat_database.xact_rollback as transactions_rolled_back,
      pg_stat_database.blks_read as blocks_read,
      pg_stat_database.blks_hit as blocks_hit,
      pg_stat_database.tup_returned as tuples_returned,
      pg_stat_database.tup_fetched as tuples_fetched,
      pg_stat_database.tup_inserted as tuples_inserted,
      pg_stat_database.tup_updated as tuples_updated,
      pg_stat_database.tup_deleted as tuples_deleted,
      pg_stat_database.deadlocks as deadlocks,
      pg_stat_database.temp_files as temporary_files,
      pg_stat_database.temp_bytes as temporary_bytes
    FROM pg_database
    JOIN pg_stat_database ON pg_database.oid = pg_stat_database.datid
    WHERE pg_database.datname != 'template0' AND pg_database.datname != 'template1'
  metrics:
    - datname:
        usage: "LABEL"
        description: "Database name"
    - size_bytes:
        usage: "GAUGE"
        description: "Database size in bytes"
    - connections:
        usage: "GAUGE"
        description: "Number of backends currently connected to this database"
    - transactions_committed:
        usage: "COUNTER"
        description: "Number of transactions in this database that have been committed"
    - transactions_rolled_back:
        usage: "COUNTER"
        description: "Number of transactions in this database that have been rolled back"
    - blocks_read:
        usage: "COUNTER"
        description: "Number of disk blocks read in this database"
    - blocks_hit:
        usage: "COUNTER"
        description: "Number of times disk blocks were found already in the buffer cache"
    - tuples_returned:
        usage: "COUNTER"
        description: "Number of rows returned by queries in this database"
    - tuples_fetched:
        usage: "COUNTER"
        description: "Number of rows fetched by queries in this database"
    - tuples_inserted:
        usage: "COUNTER"
        description: "Number of rows inserted by queries in this database"
    - tuples_updated:
        usage: "COUNTER"
        description: "Number of rows updated by queries in this database"
    - tuples_deleted:
        usage: "COUNTER"
        description: "Number of rows deleted by queries in this database"
    - deadlocks:
        usage: "COUNTER"
        description: "Number of deadlocks detected in this database"
    - temporary_files:
        usage: "COUNTER"
        description: "Number of temporary files created by queries in this database"
    - temporary_bytes:
        usage: "COUNTER"
        description: "Total amount of data written to temporary files by queries in this database"

pg_stat_user_tables:
  query: |
    SELECT
      schemaname,
      relname,
      seq_scan as sequential_scans,
      seq_tup_read as sequential_tuples_read,
      idx_scan as index_scans,
      idx_tup_fetch as index_tuples_fetched,
      n_tup_ins as tuples_inserted,
      n_tup_upd as tuples_updated,
      n_tup_del as tuples_deleted,
      n_tup_hot_upd as tuples_hot_updated,
      n_live_tup as live_tuples,
      n_dead_tup as dead_tuples,
      n_mod_since_analyze as tuples_modified_since_analyze,
      EXTRACT(EPOCH FROM last_vacuum) as last_vacuum,
      EXTRACT(EPOCH FROM last_autovacuum) as last_autovacuum,
      EXTRACT(EPOCH FROM last_analyze) as last_analyze,
      EXTRACT(EPOCH FROM last_autoanalyze) as last_autoanalyze,
      vacuum_count,
      autovacuum_count,
      analyze_count,
      autoanalyze_count
    FROM pg_stat_user_tables
  metrics:
    - schemaname:
        usage: "LABEL"
        description: "Schema name"
    - relname:
        usage: "LABEL"
        description: "Table name"
    - sequential_scans:
        usage: "COUNTER"
        description: "Number of sequential scans initiated on this table"
    - sequential_tuples_read:
        usage: "COUNTER"
        description: "Number of live rows fetched by sequential scans"
    - index_scans:
        usage: "COUNTER"
        description: "Number of index scans initiated on this table"
    - index_tuples_fetched:
        usage: "COUNTER"
        description: "Number of live rows fetched by index scans"
    - tuples_inserted:
        usage: "COUNTER"
        description: "Number of rows inserted"
    - tuples_updated:
        usage: "COUNTER"
        description: "Number of rows updated"
    - tuples_deleted:
        usage: "COUNTER"
        description: "Number of rows deleted"
    - tuples_hot_updated:
        usage: "COUNTER"
        description: "Number of rows HOT updated"
    - live_tuples:
        usage: "GAUGE"
        description: "Estimated number of live rows"
    - dead_tuples:
        usage: "GAUGE"
        description: "Estimated number of dead rows"
    - tuples_modified_since_analyze:
        usage: "GAUGE"
        description: "Estimated number of rows modified since this table was last analyzed"
    - last_vacuum:
        usage: "GAUGE"
        description: "Last time this table was manually vacuumed (not counting VACUUM FULL)"
    - last_autovacuum:
        usage: "GAUGE"
        description: "Last time this table was vacuumed by the autovacuum daemon"
    - last_analyze:
        usage: "GAUGE"
        description: "Last time this table was manually analyzed"
    - last_autoanalyze:
        usage: "GAUGE"
        description: "Last time this table was analyzed by the autovacuum daemon"
    - vacuum_count:
        usage: "COUNTER"
        description: "Number of times this table has been manually vacuumed"
    - autovacuum_count:
        usage: "COUNTER"
        description: "Number of times this table has been vacuumed by the autovacuum daemon"
    - analyze_count:
        usage: "COUNTER"
        description: "Number of times this table has been manually analyzed"
    - autoanalyze_count:
        usage: "COUNTER"
        description: "Number of times this table has been analyzed by the autovacuum daemon"

pg_stat_statements:
  query: |
    SELECT
      query,
      calls,
      total_exec_time as total_time_ms,
      mean_exec_time as mean_time_ms,
      rows,
      100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
    FROM pg_stat_statements
    ORDER BY total_exec_time DESC
    LIMIT 100
  master: true
  metrics:
    - query:
        usage: "LABEL"
        description: "Query text"
    - calls:
        usage: "COUNTER"
        description: "Number of times executed"
    - total_time_ms:
        usage: "COUNTER"
        description: "Total time spent executing this query, in milliseconds"
    - mean_time_ms:
        usage: "GAUGE"
        description: "Mean time spent executing this query, in milliseconds"
    - rows:
        usage: "COUNTER"
        description: "Total number of rows retrieved or affected by the statement"
    - hit_percent:
        usage: "GAUGE"
        description: "Buffer cache hit percentage"

pg_custom_application_metrics:
  query: |
    SELECT
      'links' as table_name,
      COUNT(*) as total_count,
      COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '1 hour') as created_last_hour,
      COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '1 day') as created_last_day
    FROM links
    UNION ALL
    SELECT
      'clicks' as table_name,
      COUNT(*) as total_count,
      COUNT(*) FILTER (WHERE timestamp >= NOW() - INTERVAL '1 hour') as created_last_hour,
      COUNT(*) FILTER (WHERE timestamp >= NOW() - INTERVAL '1 day') as created_last_day
    FROM clicks
    UNION ALL
    SELECT
      'orders' as table_name,
      COUNT(*) as total_count,
      COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '1 hour') as created_last_hour,
      COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '1 day') as created_last_day
    FROM orders
  metrics:
    - table_name:
        usage: "LABEL"
        description: "Table name"
    - total_count:
        usage: "GAUGE"
        description: "Total number of records"
    - created_last_hour:
        usage: "GAUGE"
        description: "Records created in the last hour"
    - created_last_day:
        usage: "GAUGE"
        description: "Records created in the last day"

pg_connection_states:
  query: |
    SELECT
      state,
      COUNT(*) as count
    FROM pg_stat_activity
    WHERE state IS NOT NULL
    GROUP BY state
  metrics:
    - state:
        usage: "LABEL"
        description: "Connection state"
    - count:
        usage: "GAUGE"
        description: "Number of connections in this state"

pg_locks:
  query: |
    SELECT
      mode,
      locktype,
      COUNT(*) as count
    FROM pg_locks
    GROUP BY mode, locktype
  metrics:
    - mode:
        usage: "LABEL"
        description: "Lock mode"
    - locktype:
        usage: "LABEL"
        description: "Lock type"
    - count:
        usage: "GAUGE"
        description: "Number of locks"

pg_slow_queries:
  query: |
    SELECT
      datname,
      usename,
      state,
      EXTRACT(EPOCH FROM (NOW() - query_start)) as query_duration_seconds
    FROM pg_stat_activity
    WHERE state != 'idle' 
      AND query_start IS NOT NULL
      AND EXTRACT(EPOCH FROM (NOW() - query_start)) > 5
  metrics:
    - datname:
        usage: "LABEL"
        description: "Database name"
    - usename:
        usage: "LABEL"
        description: "Username"
    - state:
        usage: "LABEL"
        description: "Query state"
    - query_duration_seconds:
        usage: "GAUGE"
        description: "Query duration in seconds"