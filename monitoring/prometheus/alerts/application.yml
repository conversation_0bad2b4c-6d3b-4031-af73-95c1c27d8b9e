groups:
  - name: application.rules
    rules:
      # Service Health Alerts
      - alert: ServiceDown
        expr: up == 0
        for: 30s
        labels:
          severity: critical
          team: platform
        annotations:
          summary: "Service {{ $labels.job }} is down"
          description: "Service {{ $labels.job }} on {{ $labels.instance }} has been down for more than 30 seconds."
          runbook_url: "https://docs.example.com/runbooks/service-down"

      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.05
        for: 5m
        labels:
          severity: warning
          team: platform
        annotations:
          summary: "High error rate detected for {{ $labels.job }}"
          description: "Error rate is {{ $value | humanizePercentage }} for service {{ $labels.job }}"

      - alert: CriticalErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.15
        for: 2m
        labels:
          severity: critical
          team: platform
        annotations:
          summary: "Critical error rate detected for {{ $labels.job }}"
          description: "Error rate is {{ $value | humanizePercentage }} for service {{ $labels.job }}"

      # Response Time Alerts
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 1
        for: 5m
        labels:
          severity: warning
          team: platform
        annotations:
          summary: "High response time for {{ $labels.job }}"
          description: "95th percentile response time is {{ $value }}s for {{ $labels.job }}"

      - alert: VeryHighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 3
        for: 2m
        labels:
          severity: critical
          team: platform
        annotations:
          summary: "Very high response time for {{ $labels.job }}"
          description: "95th percentile response time is {{ $value }}s for {{ $labels.job }}"

      # Database Connection Alerts
      - alert: DatabaseConnectionPoolExhaustion
        expr: database_connections_active / database_connections_max > 0.8
        for: 5m
        labels:
          severity: warning
          team: platform
        annotations:
          summary: "Database connection pool near exhaustion"
          description: "Database connection pool utilization is {{ $value | humanizePercentage }} for {{ $labels.job }}"

      - alert: DatabaseSlowQueries
        expr: rate(database_query_duration_seconds_total{quantile="0.95"}[5m]) > 2
        for: 5m
        labels:
          severity: warning
          team: platform
        annotations:
          summary: "Slow database queries detected"
          description: "95th percentile query time is {{ $value }}s for {{ $labels.job }}"

      # Redis Connection Alerts
      - alert: RedisConnectionFailure
        expr: redis_connected_clients < 1
        for: 1m
        labels:
          severity: critical
          team: platform
        annotations:
          summary: "Redis connection failure"
          description: "No connected Redis clients detected"

      - alert: RedisMemoryHigh
        expr: redis_memory_used_bytes / redis_memory_max_bytes > 0.8
        for: 5m
        labels:
          severity: warning
          team: platform
        annotations:
          summary: "Redis memory usage high"
          description: "Redis memory usage is {{ $value | humanizePercentage }}"

      # Application-Specific Alerts
      - alert: LinkTrackingHighLatency
        expr: histogram_quantile(0.95, rate(link_redirect_duration_seconds_bucket[5m])) > 0.1
        for: 3m
        labels:
          severity: warning
          team: analytics
        annotations:
          summary: "Link tracking redirect latency high"
          description: "95th percentile redirect time is {{ $value }}s"

      - alert: AnalyticsProcessingBacklog
        expr: analytics_queue_size > 1000
        for: 5m
        labels:
          severity: warning
          team: analytics
        annotations:
          summary: "Analytics processing backlog building"
          description: "Analytics queue size is {{ $value }} items"

      - alert: IntegrationSyncFailures
        expr: rate(integration_sync_failures_total[5m]) > 0.1
        for: 3m
        labels:
          severity: warning
          team: integrations
        annotations:
          summary: "High integration sync failure rate"
          description: "Integration sync failure rate is {{ $value }} per second"

      # Business Logic Alerts
      - alert: LowClickThroughRate
        expr: rate(link_clicks_total[1h]) / rate(link_impressions_total[1h]) < 0.02
        for: 15m
        labels:
          severity: info
          team: marketing
        annotations:
          summary: "Low click-through rate detected"
          description: "Hourly CTR is {{ $value | humanizePercentage }}"

      - alert: ConversionRateDrop
        expr: rate(orders_total[1h]) / rate(link_clicks_total[1h]) < 0.01
        for: 30m
        labels:
          severity: warning
          team: marketing
        annotations:
          summary: "Conversion rate dropped significantly"
          description: "Hourly conversion rate is {{ $value | humanizePercentage }}"

      # Security Alerts
      - alert: HighFailedAuthAttempts
        expr: rate(auth_failures_total[5m]) > 10
        for: 2m
        labels:
          severity: warning
          team: security
        annotations:
          summary: "High failed authentication attempts"
          description: "Failed auth rate is {{ $value }} per second"

      - alert: SuspiciousTrafficSpike
        expr: rate(http_requests_total[5m]) > 100
        for: 2m
        labels:
          severity: warning
          team: security
        annotations:
          summary: "Suspicious traffic spike detected"
          description: "Request rate is {{ $value }} per second"

      # Resource Utilization Alerts
      - alert: HighCPUUsage
        expr: rate(process_cpu_seconds_total[5m]) * 100 > 80
        for: 5m
        labels:
          severity: warning
          team: platform
        annotations:
          summary: "High CPU usage for {{ $labels.job }}"
          description: "CPU usage is {{ $value }}% for {{ $labels.job }}"

      - alert: HighMemoryUsage
        expr: process_resident_memory_bytes / (1024*1024*1024) > 1
        for: 5m
        labels:
          severity: warning
          team: platform
        annotations:
          summary: "High memory usage for {{ $labels.job }}"
          description: "Memory usage is {{ $value }}GB for {{ $labels.job }}"

      # Error Tracking Service Alerts
      - alert: HighErrorIngestionRate
        expr: rate(error_events_ingested_total[5m]) > 50
        for: 3m
        labels:
          severity: warning
          team: platform
        annotations:
          summary: "High error ingestion rate"
          description: "Error ingestion rate is {{ $value }} errors per second"

      - alert: UnresolvedErrorsBuildup
        expr: error_groups_unresolved > 100
        for: 10m
        labels:
          severity: info
          team: development
        annotations:
          summary: "Large number of unresolved errors"
          description: "{{ $value }} unresolved error groups detected"

      # Capacity Planning Alerts
      - alert: DiskSpaceWarning
        expr: (node_filesystem_size_bytes - node_filesystem_free_bytes) / node_filesystem_size_bytes > 0.8
        for: 5m
        labels:
          severity: warning
          team: platform
        annotations:
          summary: "Disk space usage high on {{ $labels.instance }}"
          description: "Disk usage is {{ $value | humanizePercentage }} on {{ $labels.device }}"

      - alert: DatabaseGrowthRate
        expr: rate(database_size_bytes[1h]) * 24 > 1024*1024*1024
        for: 30m
        labels:
          severity: info
          team: platform
        annotations:
          summary: "High database growth rate"
          description: "Database growing at {{ $value | humanizeBytes }} per day"