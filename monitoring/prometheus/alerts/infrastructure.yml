groups:
  - name: infrastructure.rules
    rules:
      # Node/System Alerts
      - alert: NodeDown
        expr: up{job="node-exporter"} == 0
        for: 30s
        labels:
          severity: critical
          team: infrastructure
        annotations:
          summary: "Node {{ $labels.instance }} is down"
          description: "Node exporter on {{ $labels.instance }} has been down for more than 30 seconds."

      - alert: HighCPULoad
        expr: node_load1 / on(instance) group_left count by (instance)(node_cpu_seconds_total{mode="idle"}) > 0.8
        for: 5m
        labels:
          severity: warning
          team: infrastructure
        annotations:
          summary: "High CPU load on {{ $labels.instance }}"
          description: "1-minute load average is {{ $value }} on {{ $labels.instance }}"

      - alert: CriticalCPULoad
        expr: node_load1 / on(instance) group_left count by (instance)(node_cpu_seconds_total{mode="idle"}) > 1.5
        for: 2m
        labels:
          severity: critical
          team: infrastructure
        annotations:
          summary: "Critical CPU load on {{ $labels.instance }}"
          description: "1-minute load average is {{ $value }} on {{ $labels.instance }}"

      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes > 0.8
        for: 5m
        labels:
          severity: warning
          team: infrastructure
        annotations:
          summary: "High memory usage on {{ $labels.instance }}"
          description: "Memory usage is {{ $value | humanizePercentage }} on {{ $labels.instance }}"

      - alert: CriticalMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes > 0.95
        for: 2m
        labels:
          severity: critical
          team: infrastructure
        annotations:
          summary: "Critical memory usage on {{ $labels.instance }}"
          description: "Memory usage is {{ $value | humanizePercentage }} on {{ $labels.instance }}"

      - alert: DiskSpaceWarning
        expr: (node_filesystem_size_bytes{fstype!~"tmpfs|fuse.lxcfs|squashfs"} - node_filesystem_free_bytes) / node_filesystem_size_bytes > 0.8
        for: 5m
        labels:
          severity: warning
          team: infrastructure
        annotations:
          summary: "Disk space usage high on {{ $labels.instance }}"
          description: "Disk usage is {{ $value | humanizePercentage }} on {{ $labels.device }} ({{ $labels.mountpoint }})"

      - alert: DiskSpaceCritical
        expr: (node_filesystem_size_bytes{fstype!~"tmpfs|fuse.lxcfs|squashfs"} - node_filesystem_free_bytes) / node_filesystem_size_bytes > 0.95
        for: 2m
        labels:
          severity: critical
          team: infrastructure
        annotations:
          summary: "Critical disk space usage on {{ $labels.instance }}"
          description: "Disk usage is {{ $value | humanizePercentage }} on {{ $labels.device }} ({{ $labels.mountpoint }})"

      - alert: HighDiskIOWait
        expr: rate(node_cpu_seconds_total{mode="iowait"}[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
          team: infrastructure
        annotations:
          summary: "High disk I/O wait on {{ $labels.instance }}"
          description: "I/O wait is {{ $value | humanizePercentage }} on {{ $labels.instance }}"

      - alert: HighNetworkTraffic
        expr: rate(node_network_receive_bytes_total[5m]) / 1024 / 1024 > 100
        for: 5m
        labels:
          severity: warning
          team: infrastructure
        annotations:
          summary: "High network receive traffic on {{ $labels.instance }}"
          description: "Network receive rate is {{ $value }}MB/s on {{ $labels.device }}"

      # PostgreSQL Alerts
      - alert: PostgreSQLDown
        expr: pg_up == 0
        for: 30s
        labels:
          severity: critical
          team: database
        annotations:
          summary: "PostgreSQL is down"
          description: "PostgreSQL instance {{ $labels.instance }} is down"

      - alert: PostgreSQLTooManyConnections
        expr: pg_stat_database_numbackends / pg_settings_max_connections > 0.8
        for: 5m
        labels:
          severity: warning
          team: database
        annotations:
          summary: "PostgreSQL has too many connections"
          description: "PostgreSQL connection usage is {{ $value | humanizePercentage }}"

      - alert: PostgreSQLSlowQueries
        expr: pg_stat_activity_max_tx_duration > 300
        for: 2m
        labels:
          severity: warning
          team: database
        annotations:
          summary: "PostgreSQL slow queries detected"
          description: "Longest running transaction is {{ $value }}s"

      - alert: PostgreSQLDeadlocks
        expr: rate(pg_stat_database_deadlocks[5m]) > 0
        for: 2m
        labels:
          severity: warning
          team: database
        annotations:
          summary: "PostgreSQL deadlocks detected"
          description: "{{ $value }} deadlocks per second detected"

      - alert: PostgreSQLHighCommitRatio
        expr: pg_stat_database_xact_rollback / (pg_stat_database_xact_commit + pg_stat_database_xact_rollback) > 0.05
        for: 5m
        labels:
          severity: warning
          team: database
        annotations:
          summary: "PostgreSQL high rollback ratio"
          description: "Rollback ratio is {{ $value | humanizePercentage }}"

      - alert: PostgreSQLReplicationLag
        expr: pg_replication_lag > 30
        for: 2m
        labels:
          severity: warning
          team: database
        annotations:
          summary: "PostgreSQL replication lag high"
          description: "Replication lag is {{ $value }}s"

      # Redis Alerts
      - alert: RedisDown
        expr: redis_up == 0
        for: 30s
        labels:
          severity: critical
          team: cache
        annotations:
          summary: "Redis is down"
          description: "Redis instance {{ $labels.instance }} is down"

      - alert: RedisHighMemoryUsage
        expr: redis_memory_used_bytes / redis_memory_max_bytes > 0.9
        for: 5m
        labels:
          severity: warning
          team: cache
        annotations:
          summary: "Redis memory usage high"
          description: "Redis memory usage is {{ $value | humanizePercentage }}"

      - alert: RedisHighConnectionsUsage
        expr: redis_connected_clients / redis_config_maxclients > 0.8
        for: 5m
        labels:
          severity: warning
          team: cache
        annotations:
          summary: "Redis connections usage high"
          description: "Redis connections usage is {{ $value | humanizePercentage }}"

      - alert: RedisHighKeyEvictionRate
        expr: rate(redis_evicted_keys_total[5m]) > 10
        for: 5m
        labels:
          severity: warning
          team: cache
        annotations:
          summary: "Redis key eviction rate high"
          description: "Redis evicting {{ $value }} keys per second"

      - alert: RedisSlowCommands
        expr: redis_slowlog_length > 10
        for: 5m
        labels:
          severity: warning
          team: cache
        annotations:
          summary: "Redis slow commands detected"
          description: "{{ $value }} slow commands in Redis slowlog"

      # Docker/Container Alerts
      - alert: ContainerDown
        expr: time() - container_last_seen > 60
        for: 30s
        labels:
          severity: critical
          team: platform
        annotations:
          summary: "Container {{ $labels.name }} is down"
          description: "Container {{ $labels.name }} has been down for more than 60 seconds"

      - alert: ContainerHighCPUUsage
        expr: rate(container_cpu_usage_seconds_total[5m]) * 100 > 80
        for: 5m
        labels:
          severity: warning
          team: platform
        annotations:
          summary: "Container {{ $labels.name }} high CPU usage"
          description: "Container CPU usage is {{ $value }}%"

      - alert: ContainerHighMemoryUsage
        expr: container_memory_usage_bytes / container_spec_memory_limit_bytes > 0.8
        for: 5m
        labels:
          severity: warning
          team: platform
        annotations:
          summary: "Container {{ $labels.name }} high memory usage"
          description: "Container memory usage is {{ $value | humanizePercentage }}"

      - alert: ContainerRestartLoop
        expr: rate(container_start_time_seconds[5m]) > 0
        for: 5m
        labels:
          severity: warning
          team: platform
        annotations:
          summary: "Container {{ $labels.name }} restart loop"
          description: "Container {{ $labels.name }} is restarting frequently"

      # Network Alerts
      - alert: HighNetworkErrors
        expr: rate(node_network_receive_errs_total[5m]) + rate(node_network_transmit_errs_total[5m]) > 1
        for: 5m
        labels:
          severity: warning
          team: infrastructure
        annotations:
          summary: "High network errors on {{ $labels.instance }}"
          description: "Network error rate is {{ $value }} per second on {{ $labels.device }}"

      - alert: NetworkInterfaceDown
        expr: node_network_up == 0
        for: 1m
        labels:
          severity: warning
          team: infrastructure
        annotations:
          summary: "Network interface down on {{ $labels.instance }}"
          description: "Network interface {{ $labels.device }} is down on {{ $labels.instance }}"

      # Time Synchronization
      - alert: ClockSkew
        expr: abs(node_time_seconds - time()) > 30
        for: 2m
        labels:
          severity: warning
          team: infrastructure
        annotations:
          summary: "Clock skew detected on {{ $labels.instance }}"
          description: "Clock skew is {{ $value }}s on {{ $labels.instance }}"

      # File Descriptor Alerts
      - alert: FileDescriptorsHigh
        expr: node_filefd_allocated / node_filefd_maximum > 0.8
        for: 5m
        labels:
          severity: warning
          team: infrastructure
        annotations:
          summary: "File descriptors usage high on {{ $labels.instance }}"
          description: "File descriptors usage is {{ $value | humanizePercentage }}"

      # Load Balancer/Proxy Alerts
      - alert: NginxDown
        expr: nginx_up == 0
        for: 30s
        labels:
          severity: critical
          team: infrastructure
        annotations:
          summary: "Nginx is down"
          description: "Nginx on {{ $labels.instance }} is down"

      - alert: NginxHighErrorRate
        expr: rate(nginx_http_requests_total{status=~"5.."}[5m]) / rate(nginx_http_requests_total[5m]) > 0.05
        for: 5m
        labels:
          severity: warning
          team: infrastructure
        annotations:
          summary: "Nginx high error rate"
          description: "Nginx error rate is {{ $value | humanizePercentage }}"