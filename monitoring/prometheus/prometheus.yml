global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'ecommerce-analytics'
    environment: 'production'

rule_files:
  - "alerts/*.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # Prometheus self-monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 30s
    metrics_path: /metrics

  # Node Exporter for system metrics
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 15s

  # PostgreSQL metrics
  - job_name: 'postgres-exporter'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 30s

  # Redis metrics
  - job_name: 'redis-exporter'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 30s

  # Dashboard API Service
  - job_name: 'dashboard-api'
    static_configs:
      - targets: ['dashboard:3000']
    metrics_path: /metrics
    scrape_interval: 15s
    scrape_timeout: 10s

  # Analytics Service
  - job_name: 'analytics-service'
    static_configs:
      - targets: ['analytics:3002']
    metrics_path: /metrics
    scrape_interval: 15s
    scrape_timeout: 10s

  # Integration Service
  - job_name: 'integration-service'
    static_configs:
      - targets: ['integration:3001']
    metrics_path: /metrics
    scrape_interval: 15s
    scrape_timeout: 10s

  # Link Tracking Service (Go)
  - job_name: 'link-tracking'
    static_configs:
      - targets: ['link-tracking:8080']
    metrics_path: /metrics
    scrape_interval: 15s
    scrape_timeout: 10s

  # Error Tracking Service
  - job_name: 'error-tracking'
    static_configs:
      - targets: ['error-tracking:3005']
    metrics_path: /metrics
    scrape_interval: 15s
    scrape_timeout: 10s

  # Admin Service
  - job_name: 'admin-service'
    static_configs:
      - targets: ['admin:3006']
    metrics_path: /metrics
    scrape_interval: 30s
    scrape_timeout: 10s

  # Nginx metrics
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:9113']
    metrics_path: /metrics
    scrape_interval: 30s

  # Application-specific metrics with custom labels
  - job_name: 'ecommerce-analytics-app'
    static_configs:
      - targets: 
        - 'dashboard:3000'
        - 'analytics:3002'
        - 'integration:3001'
        - 'link-tracking:8080'
        - 'error-tracking:3005'
    metrics_path: /app-metrics
    scrape_interval: 30s
    scrape_timeout: 15s
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: 'metrics-collector:8080'

  # Custom business metrics
  - job_name: 'business-metrics'
    static_configs:
      - targets: ['analytics:3002']
    metrics_path: /business-metrics
    scrape_interval: 60s
    scrape_timeout: 30s
    metric_relabel_configs:
      - source_labels: [__name__]
        regex: 'business_.*'
        target_label: category
        replacement: 'business'

  # Health check monitoring
  - job_name: 'health-checks'
    static_configs:
      - targets:
        - 'dashboard:3000'
        - 'analytics:3002'
        - 'integration:3001'
        - 'link-tracking:8080'
        - 'error-tracking:3005'
        - 'admin:3006'
    metrics_path: /health
    scrape_interval: 10s
    scrape_timeout: 5s

# Storage configuration
storage:
  tsdb:
    path: /prometheus/data
    retention.time: 30d
    retention.size: 10GB
    wal-compression: true

# Remote write configuration for long-term storage
remote_write:
  - url: http://victoria-metrics:8428/api/v1/write
    queue_config:
      max_samples_per_send: 10000
      batch_send_deadline: 5s
      min_shards: 1
      max_shards: 200
    metadata_config:
      send: true
      send_interval: 30s