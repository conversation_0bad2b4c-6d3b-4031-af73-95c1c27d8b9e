server:
  http_listen_port: 9080
  grpc_listen_port: 0

positions:
  filename: /tmp/positions.yaml

clients:
  - url: http://loki:3100/loki/api/v1/push

scrape_configs:
  # System logs
  - job_name: system
    static_configs:
      - targets:
          - localhost
        labels:
          job: varlogs
          __path__: /var/log/*log
    pipeline_stages:
      - match:
          selector: '{job="varlogs"}'
          stages:
            - regex:
                expression: '^(?P<timestamp>\S+\s+\d+\s+\d+:\d+:\d+)\s+(?P<hostname>\S+)\s+(?P<service>\S+):\s+(?P<message>.*)'
            - timestamp:
                source: timestamp
                format: 'Jan 02 15:04:05'
            - labels:
                service:
                hostname:

  # Docker container logs
  - job_name: containers
    static_configs:
      - targets:
          - localhost
        labels:
          job: containerlogs
          __path__: /var/lib/docker/containers/*/*log
    pipeline_stages:
      - json:
          expressions:
            output: log
            stream: stream
            attrs:
      - json:
          expressions:
            tag:
          source: attrs
      - regex:
          expression: (?P<container_name>(?:[^|]*))\|
          source: tag
      - timestamp:
          source: time
          format: RFC3339Nano
      - labels:
          stream:
          container_name:
      - output:
          source: output

  # Application logs - Analytics Service
  - job_name: analytics
    static_configs:
      - targets:
          - localhost
        labels:
          job: analytics
          service: analytics
          __path__: /var/log/analytics/*.log
    pipeline_stages:
      - json:
          expressions:
            level: level
            message: message
            timestamp: timestamp
            service: service
            request_id: requestId
            user_id: userId
            error_stack: stack
      - timestamp:
          source: timestamp
          format: '2006-01-02T15:04:05.000Z'
      - labels:
          level:
          service:
          request_id:
          user_id:

  # Application logs - Dashboard Service
  - job_name: dashboard
    static_configs:
      - targets:
          - localhost
        labels:
          job: dashboard
          service: dashboard
          __path__: /var/log/dashboard/*.log
    pipeline_stages:
      - json:
          expressions:
            level: level
            message: message
            timestamp: timestamp
            service: service
            request_id: requestId
            user_id: userId
            method: method
            url: url
            status_code: statusCode
            response_time: responseTime
      - timestamp:
          source: timestamp
          format: '2006-01-02T15:04:05.000Z'
      - labels:
          level:
          service:
          method:
          status_code:

  # Application logs - Integration Service
  - job_name: integration
    static_configs:
      - targets:
          - localhost
        labels:
          job: integration
          service: integration
          __path__: /var/log/integration/*.log
    pipeline_stages:
      - json:
          expressions:
            level: level
            message: message
            timestamp: timestamp
            service: service
            platform: platform
            sync_job_id: syncJobId
            webhook_id: webhookId
      - timestamp:
          source: timestamp
          format: '2006-01-02T15:04:05.000Z'
      - labels:
          level:
          service:
          platform:

  # Nginx access logs
  - job_name: nginx
    static_configs:
      - targets:
          - localhost
        labels:
          job: nginx
          service: nginx
          __path__: /var/log/nginx/access.log
    pipeline_stages:
      - regex:
          expression: '^(?P<remote_addr>\S+)\s+-\s+(?P<remote_user>\S+)\s+\[(?P<time_local>[^\]]+)\]\s+"(?P<method>\S+)\s+(?P<request>\S+)\s+(?P<protocol>\S+)"\s+(?P<status>\d+)\s+(?P<body_bytes_sent>\d+)\s+"(?P<http_referer>[^"]*)"\s+"(?P<http_user_agent>[^"]*)"'
      - timestamp:
          source: time_local
          format: '02/Jan/2006:15:04:05 -0700'
      - labels:
          method:
          status:
          remote_addr:

  # Nginx error logs
  - job_name: nginx-error
    static_configs:
      - targets:
          - localhost
        labels:
          job: nginx-error
          service: nginx
          __path__: /var/log/nginx/error.log
    pipeline_stages:
      - regex:
          expression: '^(?P<timestamp>\d{4}/\d{2}/\d{2}\s+\d{2}:\d{2}:\d{2})\s+\[(?P<level>\w+)\]\s+(?P<pid>\d+)#(?P<tid>\d+):\s+(?P<message>.*)'
      - timestamp:
          source: timestamp
          format: '2006/01/02 15:04:05'
      - labels:
          level:
          pid:

  # PostgreSQL logs
  - job_name: postgres
    static_configs:
      - targets:
          - localhost
        labels:
          job: postgres
          service: postgres
          __path__: /var/log/postgresql/*.log
    pipeline_stages:
      - regex:
          expression: '^(?P<timestamp>\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}.\d+\s+\w+)\s+\[(?P<pid>\d+)\]\s+(?P<level>\w+):\s+(?P<message>.*)'
      - timestamp:
          source: timestamp
          format: '2006-01-02 15:04:05.000 MST'
      - labels:
          level:
          pid:

  # Redis logs
  - job_name: redis
    static_configs:
      - targets:
          - localhost
        labels:
          job: redis
          service: redis
          __path__: /var/log/redis/*.log
    pipeline_stages:
      - regex:
          expression: '^(?P<pid>\d+):(?P<role>\w+)\s+(?P<timestamp>\d+\s+\w+\s+\d+\s+\d+:\d+:\d+.\d+)\s+(?P<level>\w+)\s+(?P<message>.*)'
      - timestamp:
          source: timestamp
          format: '02 Jan 2006 15:04:05.000'
      - labels:
          level:
          role:
          pid: