apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
    jsonData:
      httpMethod: POST
      queryTimeout: 60s
      timeInterval: 15s
      exemplarTraceIdDestinations:
        - name: trace_id
          datasourceUid: jaeger
    version: 1

  - name: Victoria Metrics
    type: prometheus
    access: proxy
    url: http://victoria-metrics:8428
    isDefault: false
    editable: true
    jsonData:
      httpMethod: POST
      queryTimeout: 60s
      timeInterval: 15s
    version: 1

  - name: Loki
    type: loki
    access: proxy
    url: http://loki:3100
    isDefault: false
    editable: true
    jsonData:
      maxLines: 1000
      timeout: 60s
    version: 1

  - name: Jaeger
    type: jaeger
    access: proxy
    url: http://jaeger:16686
    uid: jaeger
    isDefault: false
    editable: true
    jsonData:
      tracesToLogs:
        datasourceUid: loki
        filterByTraceID: true
        filterBySpanID: false
        tags: ['job', 'instance', 'pod', 'namespace']
      tracesToMetrics:
        datasourceUid: prometheus
        tags: [{ key: 'service.name', value: 'service' }, { key: 'job' }]
        queries:
          - name: 'Sample query'
            query: 'sum(rate(traces_spanmetrics_latency_bucket{$__tags}[5m]))'
      nodeGraph:
        enabled: true
    version: 1

  - name: PostgreSQL
    type: postgres
    access: proxy
    url: postgres:5432
    database: ecommerce_analytics
    user: ${POSTGRES_USER}
    secureJsonData:
      password: ${POSTGRES_PASSWORD}
    isDefault: false
    editable: true
    jsonData:
      sslmode: disable
      maxOpenConns: 10
      maxIdleConns: 2
      connMaxLifetime: 14400
      postgresVersion: 1500
      timescaledb: false
    version: 1

  - name: Redis
    type: redis-datasource
    access: proxy
    url: redis:6379
    isDefault: false
    editable: true
    jsonData:
      client: standalone
      poolSize: 5
      timeout: 10
      pingInterval: 0
      pipelineWindow: 0
    secureJsonData:
      password: ${REDIS_PASSWORD}
    version: 1