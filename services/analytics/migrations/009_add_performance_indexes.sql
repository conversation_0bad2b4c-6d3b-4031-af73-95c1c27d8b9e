-- Performance indexes for analytics queries
-- This migration adds indexes to optimize real-time analytics queries

-- Composite indexes for time-series queries with tenant isolation
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_clicks_tenant_clicked_at 
ON clicks(tenant_id, clicked_at DESC) 
WHERE clicked_at IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_tenant_created_at 
ON orders(tenant_id, created_at DESC) 
WHERE created_at IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_attributions_tenant_created_at 
ON attributions(tenant_id, created_at DESC) 
WHERE created_at IS NOT NULL;

-- Indexes for live metrics queries (last minute/hour data)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_clicks_recent_activity 
ON clicks(clicked_at DESC, ip_address) 
WHERE clicked_at >= NOW() - INTERVAL '1 hour';

-- Indexes for geographic analytics
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_clicks_country_device 
ON clicks(country, device_type) 
WHERE country IS NOT NULL;

-- Indexes for conversion tracking
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_attributions_click_order 
ON attributions(click_id, order_id) 
WHERE click_id IS NOT NULL AND order_id IS NOT NULL;

-- Indexes for link performance
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_links_tenant_active 
ON links(tenant_id, is_active) 
WHERE is_active = true;

-- Partial index for tracking IDs (only non-null values)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_clicks_tracking_id_partial 
ON clicks(tracking_id) 
WHERE tracking_id IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_tracking_id_partial 
ON orders(tracking_id) 
WHERE tracking_id IS NOT NULL;

-- Indexes for analytics_daily aggregations
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_analytics_daily_tenant_date_link 
ON analytics_daily(tenant_id, date DESC, link_id);

-- Composite index for revenue queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_tenant_amount_date 
ON orders(tenant_id, total_amount, created_at DESC) 
WHERE total_amount > 0;

-- Index for integration platform queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_integrations_tenant_platform 
ON integrations(tenant_id, platform) 
WHERE is_active = true;
