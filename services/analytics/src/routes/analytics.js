const express = require('express');
const router = express.Router();
const logger = require('../utils/logger');
const { recordHttpRequest } = require('../metrics');

// Analytics service will be loaded after database connection
let AnalyticsService;
let analyticsService;

// Middleware for metrics recording
router.use((req, res, next) => {
  const originalSend = res.send;
  res.send = function(data) {
    const duration = (Date.now() - req.startTime) / 1000;
    recordHttpRequest(req.method, req.route?.path || req.path, res.statusCode, duration);
    return originalSend.call(this, data);
  };
  next();
});

// Initialize analytics service
function ensureAnalyticsService() {
  if (!analyticsService) {
    AnalyticsService = require('../services/analyticsService');
    analyticsService = new AnalyticsService();
  }
  return analyticsService;
}

// GET /analytics/summary - Get analytics summary with caching
router.get('/summary', async (req, res, next) => {
  try {
    logger.info('Analytics summary endpoint called', { query: req.query });
    
    const {
      date_from,
      date_to,
      platform,
      link_id,
      integration_id
    } = req.query;

    // Use real analytics service
    const analyticsService = ensureAnalyticsService();
    const result = await analyticsService.getSummary({
      dateFrom: date_from,
      dateTo: date_to,
      platform,
      linkId: link_id,
      integrationId: integration_id
    });

    res.json({
      success: true,
      data: result
    });

  } catch (error) {
    logger.error('Analytics summary error:', error);
    next(error);
  }
});

// GET /analytics/links/:linkId - Get analytics for specific link
router.get('/links/:linkId', async (req, res, next) => {
  try {
    const { linkId } = req.params;
    const {
      date_from,
      date_to,
      granularity = 'day'
    } = req.query;

    logger.debug('Fetching link analytics', {
      linkId,
      date_from,
      date_to,
      granularity
    });

    const analytics = await ensureAnalyticsService().getLinkAnalytics(linkId, {
      dateFrom: date_from,
      dateTo: date_to,
      granularity
    });

    res.json({
      success: true,
      data: analytics
    });

  } catch (error) {
    next(error);
  }
});

// GET /analytics/attribution/:trackingId - Get attribution data
router.get('/attribution/:trackingId', async (req, res, next) => {
  try {
    const { trackingId } = req.params;

    logger.debug('Fetching attribution data', { trackingId });

    const attribution = await ensureAnalyticsService().getAttribution(trackingId);

    if (!attribution) {
      return res.status(404).json({
        success: false,
        error: 'Attribution data not found'
      });
    }

    res.json({
      success: true,
      data: attribution
    });

  } catch (error) {
    next(error);
  }
});

// GET /analytics/clicks - Get click analytics
router.get('/clicks', async (req, res, next) => {
  try {
    const {
      link_id,
      date_from,
      date_to,
      country,
      device_type,
      limit = 100,
      offset = 0
    } = req.query;

    logger.debug('Fetching click analytics', {
      link_id,
      date_from,
      date_to,
      country,
      device_type,
      limit,
      offset
    });

    const clicks = await ensureAnalyticsService().getClickAnalytics({
      linkId: link_id,
      dateFrom: date_from,
      dateTo: date_to,
      country,
      deviceType: device_type,
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      success: true,
      data: clicks
    });

  } catch (error) {
    next(error);
  }
});

// GET /analytics/conversions - Get conversion analytics
router.get('/conversions', async (req, res, next) => {
  try {
    const {
      link_id,
      platform,
      date_from,
      date_to,
      status,
      limit = 100,
      offset = 0
    } = req.query;

    logger.debug('Fetching conversion analytics', {
      link_id,
      platform,
      date_from,
      date_to,
      status,
      limit,
      offset
    });

    const conversions = await ensureAnalyticsService().getConversionAnalytics({
      linkId: link_id,
      platform,
      dateFrom: date_from,
      dateTo: date_to,
      status,
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      success: true,
      data: conversions
    });

  } catch (error) {
    next(error);
  }
});

// GET /analytics/top-performers - Get top performing links
router.get('/top-performers', async (req, res, next) => {
  try {
    const {
      metric = 'clicks',
      date_from,
      date_to,
      limit = 10
    } = req.query;

    logger.debug('Fetching top performers', {
      metric,
      date_from,
      date_to,
      limit
    });

    const topPerformers = await ensureAnalyticsService().getTopPerformers({
      metric,
      dateFrom: date_from,
      dateTo: date_to,
      limit: parseInt(limit)
    });

    res.json({
      success: true,
      data: topPerformers
    });

  } catch (error) {
    next(error);
  }
});

// GET /analytics/geographic - Get geographic analytics
router.get('/geographic', async (req, res, next) => {
  try {
    const {
      link_id,
      date_from,
      date_to,
      level = 'country'
    } = req.query;

    logger.debug('Fetching geographic analytics', {
      link_id,
      date_from,
      date_to,
      level
    });

    const geographic = await ensureAnalyticsService().getGeographicAnalytics({
      linkId: link_id,
      dateFrom: date_from,
      dateTo: date_to,
      level
    });

    res.json({
      success: true,
      data: geographic
    });

  } catch (error) {
    next(error);
  }
});

// GET /analytics/time-series - Get time series data
router.get('/time-series', async (req, res, next) => {
  try {
    const {
      metric = 'clicks',
      granularity = 'day',
      date_from,
      date_to,
      link_id,
      platform
    } = req.query;

    logger.debug('Fetching time series data', {
      metric,
      granularity,
      date_from,
      date_to,
      link_id,
      platform
    });

    const timeSeries = await ensureAnalyticsService().getTimeSeries({
      metric,
      granularity,
      dateFrom: date_from,
      dateTo: date_to,
      linkId: link_id,
      platform
    });

    res.json({
      success: true,
      data: timeSeries
    });

  } catch (error) {
    next(error);
  }
});

// GET /analytics/live-metrics - Real-time metrics from database
router.get('/live-metrics', async (req, res, next) => {
  try {
    const { tenant_id } = req.query;

    if (!tenant_id) {
      return res.status(400).json({
        success: false,
        error: 'tenant_id is required'
      });
    }

    const liveMetrics = await ensureAnalyticsService().getLiveMetrics(tenant_id);

    res.json({
      success: true,
      data: liveMetrics
    });

  } catch (error) {
    next(error);
  }
});

module.exports = router;