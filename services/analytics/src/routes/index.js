const express = require('express');
const router = express.Router();

const analyticsRoutes = require('./analytics');
const reportsRoutes = require('./reports');
const conversionRoutes = require('./conversions');
const cohortsRoutes = require('./cohorts');
const attributionRoutes = require('./attribution');
const forecastingRoutes = require('./forecasting');
const alertsRoutes = require('./alerts');
const explorationRoutes = require('./exploration');
const performanceRoutes = require('./performance');
const qualityRoutes = require('./quality');
const advancedRoutes = require('./advanced');

// Middleware for request timing
router.use((req, res, next) => {
  req.startTime = Date.now();
  next();
});

// Mount route handlers
router.use('/analytics', analyticsRoutes);
router.use('/reports', reportsRoutes);
router.use('/conversions', conversionRoutes);
router.use('/cohorts', cohortsRoutes);
router.use('/attribution', attributionRoutes);
router.use('/forecasting', forecastingRoutes);
router.use('/alerts', alertsRoutes);
router.use('/exploration', explorationRoutes);
router.use('/performance', performanceRoutes);
router.use('/quality', qualityRoutes);
router.use('/advanced', advancedRoutes);

// Default route
router.get('/', (req, res) => {
  res.json({
    success: true,
    service: 'analytics-service',
    version: '1.0.0',
    endpoints: [
      'GET /api/analytics/summary',
      'GET /api/analytics/links/:linkId',
      'GET /api/analytics/attribution/:trackingId',
      'GET /api/reports/performance',
      'GET /api/reports/conversion-funnel',
      'GET /api/conversions',
      'GET /api/conversions/:id',
      'GET /api/cohorts/users',
      'GET /api/cohorts/revenue',
      'GET /api/cohorts/retention',
      'GET /api/cohorts/behavioral',
      'GET /api/cohorts/churn',
      'GET /api/cohorts/summary',
      'GET /api/attribution/multi-touch',
      'GET /api/attribution/compare-models',
      'GET /api/attribution/metrics',
      'GET /api/attribution/channels',
      'GET /api/attribution/paths',
      'POST /api/attribution/analyze',
      'GET /api/forecasting/revenue',
      'GET /api/forecasting/conversions',
      'GET /api/alerts',
      'GET /api/alerts/rules',
      'POST /api/alerts/rules',
      'PUT /api/alerts/rules/:ruleId',
      'DELETE /api/alerts/rules/:ruleId',
      'POST /api/alerts/:alertId/acknowledge',
      'POST /api/alerts/:alertId/resolve',
      'GET /api/alerts/summary',
      'POST /api/alerts/test',
      'POST /api/exploration/query',
      'GET /api/exploration/schema',
      'POST /api/exploration/insights',
      'POST /api/exploration/funnel',
      'POST /api/exploration/cohort',
      'POST /api/exploration/queries/save',
      'GET /api/exploration/queries',
      'POST /api/exploration/correlation',
      'POST /api/exploration/segment',
      'GET /api/exploration/suggestions',
      'POST /api/reports/generate',
      'GET /api/reports/templates',
      'POST /api/reports/schedule',
      'GET /api/reports/scheduled',
      'POST /api/reports/export',
      'GET /api/reports/types',
      'POST /api/reports/preview',
      'GET /api/performance/summary',
      'GET /api/performance/recommendations',
      'POST /api/performance/optimize',
      'GET /api/performance/cache/stats',
      'POST /api/performance/cache/clear',
      'POST /api/performance/cache/warm-up',
      'GET /api/performance/database',
      'GET /api/performance/system',
      'POST /api/performance/alerts/configure',
      'GET /api/performance/health',
      'GET /api/quality/metrics',
      'POST /api/quality/check',
      'POST /api/quality/validate',
      'PUT /api/quality/thresholds',
      'GET /api/quality/report',
      'POST /api/quality/rules',
      'GET /api/quality/summary',
      'DELETE /api/quality/cleanup'
    ],
  });
});

module.exports = router;