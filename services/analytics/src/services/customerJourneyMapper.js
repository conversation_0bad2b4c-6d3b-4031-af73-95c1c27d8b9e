const logger = require('../utils/logger');
const { pool } = require('../database');

class CustomerJourneyMapper {
  constructor() {
    this.journeyStages = [
      'awareness',
      'interest', 
      'consideration',
      'intent',
      'purchase',
      'retention'
    ];
  }

  /**
   * Map customer journey for a specific customer
   */
  async mapCustomerJourney(customerEmail, tenantId, options = {}) {
    try {
      const {
        dateFrom,
        dateTo,
        includeAnonymous = true
      } = options;

      logger.info('Mapping customer journey', {
        customerEmail,
        tenantId,
        dateFrom,
        dateTo
      });

      // Get all touchpoints for this customer
      const touchpoints = await this.getCustomerTouchpoints(
        customerEmail, 
        tenantId, 
        dateFrom, 
        dateTo,
        includeAnonymous
      );

      // Get orders for this customer
      const orders = await this.getCustomerOrders(customerEmail, tenantId, dateFrom, dateTo);

      // Build journey timeline
      const journey = this.buildJourneyTimeline(touchpoints, orders);

      // Analyze journey patterns
      const analysis = this.analyzeJourney(journey);

      return {
        customerEmail,
        tenantId,
        journey,
        analysis,
        summary: {
          totalTouchpoints: touchpoints.length,
          totalOrders: orders.length,
          journeyDuration: this.calculateJourneyDuration(journey),
          conversionRate: orders.length > 0 ? 1 : 0
        }
      };

    } catch (error) {
      logger.error('Customer journey mapping failed', {
        customerEmail,
        tenantId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Get all touchpoints for a customer
   */
  async getCustomerTouchpoints(customerEmail, tenantId, dateFrom, dateTo, includeAnonymous) {
    let whereClause = `
      WHERE l.tenant_id = $1 
      AND (
        -- Direct email match in orders
        EXISTS (
          SELECT 1 FROM orders o 
          WHERE o.customer_email = $2 
          AND o.tracking_id = c.tracking_id
        )
    `;

    const params = [tenantId, customerEmail];
    let paramCount = 2;

    if (includeAnonymous) {
      // Include anonymous sessions that later converted
      whereClause += `
        OR c.ip_address IN (
          SELECT DISTINCT c2.ip_address 
          FROM clicks c2 
          JOIN orders o ON c2.tracking_id = o.tracking_id
          WHERE o.customer_email = $2
          AND c2.ip_address IS NOT NULL
        )
      `;
    }

    whereClause += ')';

    if (dateFrom) {
      whereClause += ` AND c.clicked_at >= $${++paramCount}`;
      params.push(new Date(dateFrom));
    }

    if (dateTo) {
      whereClause += ` AND c.clicked_at <= $${++paramCount}`;
      params.push(new Date(dateTo));
    }

    const query = `
      SELECT 
        c.id,
        c.clicked_at,
        c.ip_address,
        c.user_agent,
        c.referrer,
        c.country,
        c.city,
        c.device_type,
        c.browser,
        c.os,
        l.id as link_id,
        l.title as link_title,
        l.target_url,
        l.utm_source,
        l.utm_medium,
        l.utm_campaign,
        l.utm_term,
        l.utm_content,
        camp.id as campaign_id,
        camp.name as campaign_name
      FROM clicks c
      JOIN links l ON c.link_id = l.id
      LEFT JOIN campaigns camp ON l.campaign_id = camp.id
      ${whereClause}
      ORDER BY c.clicked_at ASC
    `;

    const result = await pool.query(query, params);
    return result.rows;
  }

  /**
   * Get customer orders
   */
  async getCustomerOrders(customerEmail, tenantId, dateFrom, dateTo) {
    let whereClause = 'WHERE customer_email = $1 AND tenant_id = $2';
    const params = [customerEmail, tenantId];
    let paramCount = 2;

    if (dateFrom) {
      whereClause += ` AND platform_created_at >= $${++paramCount}`;
      params.push(new Date(dateFrom));
    }

    if (dateTo) {
      whereClause += ` AND platform_created_at <= $${++paramCount}`;
      params.push(new Date(dateTo));
    }

    const query = `
      SELECT 
        id,
        platform_order_id,
        total_amount,
        currency,
        status,
        platform_created_at,
        tracking_id,
        integration_id
      FROM orders
      ${whereClause}
      ORDER BY platform_created_at ASC
    `;

    const result = await pool.query(query, params);
    return result.rows;
  }

  /**
   * Build journey timeline combining touchpoints and orders
   */
  buildJourneyTimeline(touchpoints, orders) {
    const events = [];

    // Add touchpoints
    touchpoints.forEach(touchpoint => {
      events.push({
        type: 'touchpoint',
        timestamp: new Date(touchpoint.clicked_at),
        stage: this.classifyTouchpointStage(touchpoint),
        data: touchpoint
      });
    });

    // Add orders
    orders.forEach(order => {
      events.push({
        type: 'conversion',
        timestamp: new Date(order.platform_created_at),
        stage: 'purchase',
        data: order
      });
    });

    // Sort by timestamp
    events.sort((a, b) => a.timestamp - b.timestamp);

    return events;
  }

  /**
   * Classify touchpoint stage based on UTM parameters and behavior
   */
  classifyTouchpointStage(touchpoint) {
    const source = touchpoint.utm_source?.toLowerCase() || '';
    const medium = touchpoint.utm_medium?.toLowerCase() || '';
    const campaign = touchpoint.utm_campaign?.toLowerCase() || '';
    const content = touchpoint.utm_content?.toLowerCase() || '';

    // Awareness stage indicators
    if (
      source.includes('display') ||
      source.includes('social') ||
      medium.includes('display') ||
      medium.includes('social') ||
      campaign.includes('awareness') ||
      campaign.includes('brand')
    ) {
      return 'awareness';
    }

    // Interest stage indicators
    if (
      source.includes('search') ||
      medium.includes('organic') ||
      campaign.includes('interest') ||
      content.includes('blog') ||
      content.includes('content')
    ) {
      return 'interest';
    }

    // Consideration stage indicators
    if (
      medium.includes('email') ||
      campaign.includes('nurture') ||
      campaign.includes('consideration') ||
      content.includes('comparison') ||
      content.includes('review')
    ) {
      return 'consideration';
    }

    // Intent stage indicators
    if (
      source.includes('google') ||
      medium.includes('cpc') ||
      medium.includes('paid') ||
      campaign.includes('intent') ||
      campaign.includes('purchase') ||
      content.includes('product') ||
      content.includes('buy')
    ) {
      return 'intent';
    }

    // Default to interest if we can't classify
    return 'interest';
  }

  /**
   * Analyze journey patterns
   */
  analyzeJourney(journey) {
    const analysis = {
      stageDistribution: {},
      deviceDistribution: {},
      sourceDistribution: {},
      conversionPath: [],
      timeToConversion: null,
      touchpointCount: 0,
      conversionCount: 0
    };

    // Initialize stage distribution
    this.journeyStages.forEach(stage => {
      analysis.stageDistribution[stage] = 0;
    });

    journey.forEach(event => {
      if (event.type === 'touchpoint') {
        analysis.touchpointCount++;
        
        // Stage distribution
        analysis.stageDistribution[event.stage]++;

        // Device distribution
        const device = event.data.device_type || 'unknown';
        analysis.deviceDistribution[device] = (analysis.deviceDistribution[device] || 0) + 1;

        // Source distribution
        const source = event.data.utm_source || 'direct';
        analysis.sourceDistribution[source] = (analysis.sourceDistribution[source] || 0) + 1;

        // Conversion path
        analysis.conversionPath.push({
          stage: event.stage,
          source: source,
          timestamp: event.timestamp
        });
      } else if (event.type === 'conversion') {
        analysis.conversionCount++;
      }
    });

    // Calculate time to conversion
    if (journey.length > 1) {
      const firstEvent = journey[0];
      const lastConversion = journey.filter(e => e.type === 'conversion').pop();
      
      if (lastConversion) {
        analysis.timeToConversion = lastConversion.timestamp - firstEvent.timestamp;
      }
    }

    return analysis;
  }

  /**
   * Calculate journey duration in milliseconds
   */
  calculateJourneyDuration(journey) {
    if (journey.length < 2) return 0;
    
    const firstEvent = journey[0];
    const lastEvent = journey[journey.length - 1];
    
    return lastEvent.timestamp - firstEvent.timestamp;
  }

  /**
   * Get journey insights for multiple customers
   */
  async getJourneyInsights(tenantId, options = {}) {
    try {
      const {
        dateFrom,
        dateTo,
        limit = 100
      } = options;

      // Get customers with orders in the period
      const customers = await this.getCustomersWithOrders(tenantId, dateFrom, dateTo, limit);

      const insights = {
        totalCustomers: customers.length,
        averageJourneyLength: 0,
        averageTimeToConversion: 0,
        stageDistribution: {},
        topSources: {},
        devicePreferences: {},
        conversionPaths: []
      };

      // Initialize stage distribution
      this.journeyStages.forEach(stage => {
        insights.stageDistribution[stage] = 0;
      });

      let totalJourneyLength = 0;
      let totalTimeToConversion = 0;
      let customersWithConversion = 0;

      for (const customer of customers) {
        try {
          const journey = await this.mapCustomerJourney(
            customer.customer_email,
            tenantId,
            { dateFrom, dateTo }
          );

          totalJourneyLength += journey.summary.totalTouchpoints;

          if (journey.summary.journeyDuration > 0) {
            totalTimeToConversion += journey.summary.journeyDuration;
            customersWithConversion++;
          }

          // Aggregate stage distribution
          Object.keys(journey.analysis.stageDistribution).forEach(stage => {
            insights.stageDistribution[stage] += journey.analysis.stageDistribution[stage];
          });

          // Aggregate source distribution
          Object.keys(journey.analysis.sourceDistribution).forEach(source => {
            insights.topSources[source] = (insights.topSources[source] || 0) + 
              journey.analysis.sourceDistribution[source];
          });

          // Aggregate device preferences
          Object.keys(journey.analysis.deviceDistribution).forEach(device => {
            insights.devicePreferences[device] = (insights.devicePreferences[device] || 0) + 
              journey.analysis.deviceDistribution[device];
          });

          // Store conversion path
          if (journey.analysis.conversionPath.length > 0) {
            insights.conversionPaths.push({
              customerEmail: customer.customer_email,
              path: journey.analysis.conversionPath
            });
          }

        } catch (error) {
          logger.warn('Failed to map journey for customer', {
            customerEmail: customer.customer_email,
            error: error.message
          });
        }
      }

      // Calculate averages
      if (customers.length > 0) {
        insights.averageJourneyLength = totalJourneyLength / customers.length;
      }

      if (customersWithConversion > 0) {
        insights.averageTimeToConversion = totalTimeToConversion / customersWithConversion;
      }

      return insights;

    } catch (error) {
      logger.error('Journey insights generation failed', {
        tenantId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Get customers with orders in the specified period
   */
  async getCustomersWithOrders(tenantId, dateFrom, dateTo, limit) {
    let whereClause = 'WHERE tenant_id = $1';
    const params = [tenantId];
    let paramCount = 1;

    if (dateFrom) {
      whereClause += ` AND platform_created_at >= $${++paramCount}`;
      params.push(new Date(dateFrom));
    }

    if (dateTo) {
      whereClause += ` AND platform_created_at <= $${++paramCount}`;
      params.push(new Date(dateTo));
    }

    const query = `
      SELECT DISTINCT customer_email
      FROM orders
      ${whereClause}
      AND customer_email IS NOT NULL
      ORDER BY customer_email
      LIMIT $${++paramCount}
    `;

    params.push(limit);

    const result = await pool.query(query, params);
    return result.rows;
  }
}

module.exports = CustomerJourneyMapper;
