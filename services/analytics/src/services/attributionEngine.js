const logger = require('../utils/logger');
const { pool } = require('../database');
const { v4: uuidv4 } = require('uuid');

class AttributionEngine {
  constructor() {
    this.models = {
      'last-click': this.lastClickAttribution.bind(this),
      'first-click': this.firstClickAttribution.bind(this),
      'linear': this.linearAttribution.bind(this),
      'time-decay': this.timeDecayAttribution.bind(this),
      'position-based': this.positionBasedAttribution.bind(this)
    };
    
    // Attribution window in days
    this.attributionWindow = 30;
  }

  /**
   * Process attribution for an order
   */
  async processOrderAttribution(orderId, options = {}) {
    try {
      const { 
        model = 'last-click',
        attributionWindow = this.attributionWindow 
      } = options;

      logger.info('Processing order attribution', {
        orderId,
        model,
        attributionWindow
      });

      // Get order details
      const order = await this.getOrderDetails(orderId);
      if (!order) {
        throw new Error('Order not found');
      }

      // Find all touchpoints for this customer
      const touchpoints = await this.findCustomerTouchpoints(
        order.customer_email,
        order.platform_created_at,
        attributionWindow
      );

      if (touchpoints.length === 0) {
        logger.info('No touchpoints found for order', { orderId });
        return null;
      }

      // Apply attribution model
      const attributionModel = this.models[model];
      if (!attributionModel) {
        throw new Error(`Unknown attribution model: ${model}`);
      }

      const attributions = await attributionModel(order, touchpoints);

      // Save attribution records
      const savedAttributions = [];
      for (const attribution of attributions) {
        const saved = await this.saveAttribution(attribution);
        savedAttributions.push(saved);
      }

      logger.info('Attribution processing completed', {
        orderId,
        model,
        attributionsCreated: savedAttributions.length
      });

      return savedAttributions;

    } catch (error) {
      logger.error('Attribution processing failed', {
        orderId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Get order details
   */
  async getOrderDetails(orderId) {
    const query = `
      SELECT 
        id, tenant_id, platform_order_id, customer_email, 
        total_amount, currency, platform_created_at, tracking_id
      FROM orders 
      WHERE id = $1
    `;
    
    const result = await pool.query(query, [orderId]);
    return result.rows[0] || null;
  }

  /**
   * Find customer touchpoints within attribution window
   */
  async findCustomerTouchpoints(customerEmail, orderDate, windowDays) {
    const windowStart = new Date(orderDate);
    windowStart.setDate(windowStart.getDate() - windowDays);

    const query = `
      SELECT 
        c.id as click_id,
        c.link_id,
        c.clicked_at,
        c.ip_address,
        c.user_agent,
        c.referrer,
        c.country,
        c.device_type,
        l.campaign_id,
        l.utm_source,
        l.utm_medium,
        l.utm_campaign,
        l.utm_term,
        l.utm_content,
        l.target_url,
        camp.name as campaign_name
      FROM clicks c
      JOIN links l ON c.link_id = l.id
      LEFT JOIN campaigns camp ON l.campaign_id = camp.id
      WHERE c.clicked_at >= $1 
        AND c.clicked_at <= $2
        AND (
          -- Direct tracking ID match
          c.tracking_id IN (
            SELECT DISTINCT tracking_id 
            FROM orders 
            WHERE customer_email = $3 
              AND tracking_id IS NOT NULL
          )
          -- Or IP-based matching (less reliable)
          OR c.ip_address IN (
            SELECT DISTINCT c2.ip_address
            FROM clicks c2
            JOIN orders o ON c2.tracking_id = o.tracking_id
            WHERE o.customer_email = $3
              AND c2.ip_address IS NOT NULL
          )
        )
      ORDER BY c.clicked_at ASC
    `;

    const result = await pool.query(query, [windowStart, orderDate, customerEmail]);
    return result.rows;
  }

  /**
   * Last-click attribution model
   */
  async lastClickAttribution(order, touchpoints) {
    const lastTouchpoint = touchpoints[touchpoints.length - 1];
    
    return [{
      tenant_id: order.tenant_id,
      click_id: lastTouchpoint.click_id,
      order_id: order.id,
      link_id: lastTouchpoint.link_id,
      campaign_id: lastTouchpoint.campaign_id,
      attribution_model: 'last-click',
      attribution_weight: 1.0,
      conversion_value: parseFloat(order.total_amount),
      attributed_value: parseFloat(order.total_amount)
    }];
  }

  /**
   * First-click attribution model
   */
  async firstClickAttribution(order, touchpoints) {
    const firstTouchpoint = touchpoints[0];
    
    return [{
      tenant_id: order.tenant_id,
      click_id: firstTouchpoint.click_id,
      order_id: order.id,
      link_id: firstTouchpoint.link_id,
      campaign_id: firstTouchpoint.campaign_id,
      attribution_model: 'first-click',
      attribution_weight: 1.0,
      conversion_value: parseFloat(order.total_amount),
      attributed_value: parseFloat(order.total_amount)
    }];
  }

  /**
   * Linear attribution model
   */
  async linearAttribution(order, touchpoints) {
    const weight = 1.0 / touchpoints.length;
    const attributedValue = parseFloat(order.total_amount) * weight;

    return touchpoints.map(touchpoint => ({
      tenant_id: order.tenant_id,
      click_id: touchpoint.click_id,
      order_id: order.id,
      link_id: touchpoint.link_id,
      campaign_id: touchpoint.campaign_id,
      attribution_model: 'linear',
      attribution_weight: weight,
      conversion_value: parseFloat(order.total_amount),
      attributed_value: attributedValue
    }));
  }

  /**
   * Time-decay attribution model (40-day half-life)
   */
  async timeDecayAttribution(order, touchpoints) {
    const orderTime = new Date(order.platform_created_at).getTime();
    const halfLifeDays = 40;
    const halfLifeMs = halfLifeDays * 24 * 60 * 60 * 1000;

    // Calculate weights based on time decay
    const weights = touchpoints.map(touchpoint => {
      const touchpointTime = new Date(touchpoint.clicked_at).getTime();
      const timeDiff = orderTime - touchpointTime;
      return Math.pow(0.5, timeDiff / halfLifeMs);
    });

    const totalWeight = weights.reduce((sum, weight) => sum + weight, 0);

    return touchpoints.map((touchpoint, index) => {
      const normalizedWeight = weights[index] / totalWeight;
      const attributedValue = parseFloat(order.total_amount) * normalizedWeight;

      return {
        tenant_id: order.tenant_id,
        click_id: touchpoint.click_id,
        order_id: order.id,
        link_id: touchpoint.link_id,
        campaign_id: touchpoint.campaign_id,
        attribution_model: 'time-decay',
        attribution_weight: normalizedWeight,
        conversion_value: parseFloat(order.total_amount),
        attributed_value: attributedValue
      };
    });
  }

  /**
   * Position-based attribution model (40% first, 40% last, 20% middle)
   */
  async positionBasedAttribution(order, touchpoints) {
    if (touchpoints.length === 1) {
      return this.lastClickAttribution(order, touchpoints);
    }

    const attributions = [];
    const orderValue = parseFloat(order.total_amount);

    touchpoints.forEach((touchpoint, index) => {
      let weight;
      let attributedValue;

      if (index === 0) {
        // First touchpoint gets 40%
        weight = 0.4;
        attributedValue = orderValue * 0.4;
      } else if (index === touchpoints.length - 1) {
        // Last touchpoint gets 40%
        weight = 0.4;
        attributedValue = orderValue * 0.4;
      } else {
        // Middle touchpoints share 20%
        const middleCount = touchpoints.length - 2;
        weight = 0.2 / middleCount;
        attributedValue = orderValue * weight;
      }

      attributions.push({
        tenant_id: order.tenant_id,
        click_id: touchpoint.click_id,
        order_id: order.id,
        link_id: touchpoint.link_id,
        campaign_id: touchpoint.campaign_id,
        attribution_model: 'position-based',
        attribution_weight: weight,
        conversion_value: orderValue,
        attributed_value: attributedValue
      });
    });

    return attributions;
  }

  /**
   * Save attribution record
   */
  async saveAttribution(attribution) {
    const attributionId = uuidv4();
    
    const query = `
      INSERT INTO attributions (
        id, tenant_id, click_id, order_id, link_id, campaign_id,
        attribution_model, attribution_weight, conversion_value, 
        attributed_value, created_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
      RETURNING *
    `;

    const values = [
      attributionId,
      attribution.tenant_id,
      attribution.click_id,
      attribution.order_id,
      attribution.link_id,
      attribution.campaign_id,
      attribution.attribution_model,
      attribution.attribution_weight,
      attribution.conversion_value,
      attribution.attributed_value,
      new Date()
    ];

    const result = await pool.query(query, values);
    return result.rows[0];
  }

  /**
   * Get attribution report for tenant
   */
  async getAttributionReport(tenantId, options = {}) {
    const {
      dateFrom,
      dateTo,
      model = 'last-click',
      groupBy = 'campaign'
    } = options;

    let whereClause = 'WHERE a.tenant_id = $1';
    const params = [tenantId];
    let paramCount = 1;

    if (dateFrom) {
      whereClause += ` AND a.created_at >= $${++paramCount}`;
      params.push(new Date(dateFrom));
    }

    if (dateTo) {
      whereClause += ` AND a.created_at <= $${++paramCount}`;
      params.push(new Date(dateTo));
    }

    if (model !== 'all') {
      whereClause += ` AND a.attribution_model = $${++paramCount}`;
      params.push(model);
    }

    let groupByClause;
    let selectClause;

    switch (groupBy) {
      case 'campaign':
        selectClause = `
          c.name as campaign_name,
          c.id as campaign_id,
          COUNT(DISTINCT a.order_id) as conversions,
          SUM(a.attributed_value) as attributed_revenue,
          AVG(a.attributed_value) as avg_order_value
        `;
        groupByClause = 'GROUP BY c.id, c.name';
        break;
      case 'link':
        selectClause = `
          l.title as link_title,
          l.id as link_id,
          COUNT(DISTINCT a.order_id) as conversions,
          SUM(a.attributed_value) as attributed_revenue,
          AVG(a.attributed_value) as avg_order_value
        `;
        groupByClause = 'GROUP BY l.id, l.title';
        break;
      default:
        throw new Error(`Invalid groupBy parameter: ${groupBy}`);
    }

    const query = `
      SELECT ${selectClause}
      FROM attributions a
      LEFT JOIN campaigns c ON a.campaign_id = c.id
      LEFT JOIN links l ON a.link_id = l.id
      ${whereClause}
      ${groupByClause}
      ORDER BY attributed_revenue DESC
    `;

    const result = await pool.query(query, params);
    return result.rows;
  }
}

module.exports = AttributionEngine;
