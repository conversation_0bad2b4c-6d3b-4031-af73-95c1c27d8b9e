const logger = require('../utils/logger');
const { pool } = require('../database');
const AttributionEngine = require('./attributionEngine');
const CustomerJourneyMapper = require('./customerJourneyMapper');

class AdvancedAnalyticsProcessor {
  constructor() {
    this.attributionEngine = new AttributionEngine();
    this.journeyMapper = new CustomerJourneyMapper();
  }

  /**
   * Process comprehensive analytics for a tenant
   */
  async processAnalytics(tenantId, options = {}) {
    try {
      const {
        dateFrom,
        dateTo,
        includeAttribution = true,
        includeJourneys = true,
        includeCohorts = true,
        includeSegmentation = true
      } = options;

      logger.info('Processing advanced analytics', {
        tenantId,
        dateFrom,
        dateTo,
        includeAttribution,
        includeJourneys
      });

      const analytics = {
        tenantId,
        period: { dateFrom, dateTo },
        processedAt: new Date(),
        overview: {},
        attribution: null,
        journeys: null,
        cohorts: null,
        segmentation: null
      };

      // Generate overview metrics
      analytics.overview = await this.generateOverviewMetrics(tenantId, dateFrom, dateTo);

      // Process attribution analysis
      if (includeAttribution) {
        analytics.attribution = await this.processAttributionAnalysis(tenantId, dateFrom, dateTo);
      }

      // Process customer journey analysis
      if (includeJourneys) {
        analytics.journeys = await this.processJourneyAnalysis(tenantId, dateFrom, dateTo);
      }

      // Process cohort analysis
      if (includeCohorts) {
        analytics.cohorts = await this.processCohortAnalysis(tenantId, dateFrom, dateTo);
      }

      // Process customer segmentation
      if (includeSegmentation) {
        analytics.segmentation = await this.processSegmentationAnalysis(tenantId, dateFrom, dateTo);
      }

      logger.info('Advanced analytics processing completed', {
        tenantId,
        metricsGenerated: Object.keys(analytics).length
      });

      return analytics;

    } catch (error) {
      logger.error('Advanced analytics processing failed', {
        tenantId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Generate overview metrics
   */
  async generateOverviewMetrics(tenantId, dateFrom, dateTo) {
    const metrics = {};

    // Basic metrics query
    let whereClause = 'WHERE tenant_id = $1';
    const params = [tenantId];
    let paramCount = 1;

    if (dateFrom) {
      whereClause += ` AND created_at >= $${++paramCount}`;
      params.push(new Date(dateFrom));
    }

    if (dateTo) {
      whereClause += ` AND created_at <= $${++paramCount}`;
      params.push(new Date(dateTo));
    }

    // Get click metrics
    const clickQuery = `
      SELECT 
        COUNT(*) as total_clicks,
        COUNT(DISTINCT ip_address) as unique_visitors,
        COUNT(DISTINCT link_id) as active_links,
        COUNT(DISTINCT country) as countries_reached
      FROM clicks c
      JOIN links l ON c.link_id = l.id
      ${whereClause}
    `;

    const clickResult = await pool.query(clickQuery, params);
    metrics.clicks = clickResult.rows[0];

    // Get order metrics
    const orderQuery = `
      SELECT 
        COUNT(*) as total_orders,
        COUNT(DISTINCT customer_email) as unique_customers,
        SUM(total_amount) as total_revenue,
        AVG(total_amount) as average_order_value,
        COUNT(DISTINCT integration_id) as active_integrations
      FROM orders
      ${whereClause}
    `;

    const orderResult = await pool.query(orderQuery, params);
    metrics.orders = orderResult.rows[0];

    // Calculate conversion rate
    metrics.conversionRate = metrics.clicks.total_clicks > 0 
      ? (metrics.orders.total_orders / metrics.clicks.total_clicks) * 100 
      : 0;

    // Get attribution metrics
    const attributionQuery = `
      SELECT 
        COUNT(*) as total_attributions,
        COUNT(DISTINCT attribution_model) as models_used,
        SUM(attributed_value) as total_attributed_value
      FROM attributions
      ${whereClause}
    `;

    const attributionResult = await pool.query(attributionQuery, params);
    metrics.attribution = attributionResult.rows[0];

    return metrics;
  }

  /**
   * Process attribution analysis
   */
  async processAttributionAnalysis(tenantId, dateFrom, dateTo) {
    const analysis = {
      modelComparison: {},
      topPerformingCampaigns: [],
      topPerformingLinks: [],
      attributionTrends: []
    };

    // Compare different attribution models
    const models = ['last-click', 'first-click', 'linear', 'time-decay', 'position-based'];
    
    for (const model of models) {
      const report = await this.attributionEngine.getAttributionReport(tenantId, {
        dateFrom,
        dateTo,
        model,
        groupBy: 'campaign'
      });

      analysis.modelComparison[model] = {
        totalRevenue: report.reduce((sum, item) => sum + parseFloat(item.attributed_revenue || 0), 0),
        totalConversions: report.reduce((sum, item) => sum + parseInt(item.conversions || 0), 0),
        topCampaigns: report.slice(0, 5)
      };
    }

    // Get top performing campaigns (using last-click as default)
    analysis.topPerformingCampaigns = await this.attributionEngine.getAttributionReport(tenantId, {
      dateFrom,
      dateTo,
      model: 'last-click',
      groupBy: 'campaign'
    });

    // Get top performing links
    analysis.topPerformingLinks = await this.attributionEngine.getAttributionReport(tenantId, {
      dateFrom,
      dateTo,
      model: 'last-click',
      groupBy: 'link'
    });

    return analysis;
  }

  /**
   * Process customer journey analysis
   */
  async processJourneyAnalysis(tenantId, dateFrom, dateTo) {
    const insights = await this.journeyMapper.getJourneyInsights(tenantId, {
      dateFrom,
      dateTo,
      limit: 500
    });

    // Add journey stage analysis
    const stageAnalysis = await this.analyzeJourneyStages(tenantId, dateFrom, dateTo);
    insights.stageAnalysis = stageAnalysis;

    return insights;
  }

  /**
   * Analyze journey stages
   */
  async analyzeJourneyStages(tenantId, dateFrom, dateTo) {
    let whereClause = 'WHERE l.tenant_id = $1';
    const params = [tenantId];
    let paramCount = 1;

    if (dateFrom) {
      whereClause += ` AND c.clicked_at >= $${++paramCount}`;
      params.push(new Date(dateFrom));
    }

    if (dateTo) {
      whereClause += ` AND c.clicked_at <= $${++paramCount}`;
      params.push(new Date(dateTo));
    }

    const query = `
      SELECT 
        l.utm_source,
        l.utm_medium,
        l.utm_campaign,
        COUNT(*) as clicks,
        COUNT(DISTINCT c.ip_address) as unique_visitors
      FROM clicks c
      JOIN links l ON c.link_id = l.id
      ${whereClause}
      GROUP BY l.utm_source, l.utm_medium, l.utm_campaign
      ORDER BY clicks DESC
    `;

    const result = await pool.query(query, params);
    return result.rows;
  }

  /**
   * Process cohort analysis
   */
  async processCohortAnalysis(tenantId, dateFrom, dateTo) {
    const cohorts = await this.generateCohortAnalysis(tenantId, dateFrom, dateTo);
    return cohorts;
  }

  /**
   * Generate cohort analysis
   */
  async generateCohortAnalysis(tenantId, dateFrom, dateTo) {
    // Get customers grouped by their first order month
    const query = `
      WITH first_orders AS (
        SELECT 
          customer_email,
          DATE_TRUNC('month', MIN(platform_created_at)) as cohort_month,
          MIN(platform_created_at) as first_order_date
        FROM orders
        WHERE tenant_id = $1
        GROUP BY customer_email
      ),
      customer_orders AS (
        SELECT 
          o.customer_email,
          fo.cohort_month,
          DATE_TRUNC('month', o.platform_created_at) as order_month,
          o.total_amount,
          EXTRACT(MONTH FROM AGE(o.platform_created_at, fo.first_order_date)) as month_number
        FROM orders o
        JOIN first_orders fo ON o.customer_email = fo.customer_email
        WHERE o.tenant_id = $1
      )
      SELECT 
        cohort_month,
        month_number,
        COUNT(DISTINCT customer_email) as customers,
        COUNT(*) as orders,
        SUM(total_amount) as revenue
      FROM customer_orders
      WHERE cohort_month >= $2 AND cohort_month <= $3
      GROUP BY cohort_month, month_number
      ORDER BY cohort_month, month_number
    `;

    const params = [
      tenantId,
      dateFrom ? new Date(dateFrom) : new Date('2020-01-01'),
      dateTo ? new Date(dateTo) : new Date()
    ];

    const result = await pool.query(query, params);
    
    // Transform data into cohort table format
    const cohortData = {};
    
    result.rows.forEach(row => {
      const cohortKey = row.cohort_month.toISOString().substring(0, 7);
      if (!cohortData[cohortKey]) {
        cohortData[cohortKey] = {};
      }
      
      cohortData[cohortKey][row.month_number] = {
        customers: parseInt(row.customers),
        orders: parseInt(row.orders),
        revenue: parseFloat(row.revenue)
      };
    });

    return cohortData;
  }

  /**
   * Process customer segmentation analysis
   */
  async processSegmentationAnalysis(tenantId, dateFrom, dateTo) {
    const segments = {
      byValue: await this.segmentByValue(tenantId, dateFrom, dateTo),
      byFrequency: await this.segmentByFrequency(tenantId, dateFrom, dateTo),
      byRecency: await this.segmentByRecency(tenantId, dateFrom, dateTo),
      rfmSegments: await this.generateRFMSegments(tenantId, dateFrom, dateTo)
    };

    return segments;
  }

  /**
   * Segment customers by order value
   */
  async segmentByValue(tenantId, dateFrom, dateTo) {
    let whereClause = 'WHERE tenant_id = $1';
    const params = [tenantId];
    let paramCount = 1;

    if (dateFrom) {
      whereClause += ` AND platform_created_at >= $${++paramCount}`;
      params.push(new Date(dateFrom));
    }

    if (dateTo) {
      whereClause += ` AND platform_created_at <= $${++paramCount}`;
      params.push(new Date(dateTo));
    }

    const query = `
      WITH customer_values AS (
        SELECT 
          customer_email,
          SUM(total_amount) as total_value,
          COUNT(*) as order_count
        FROM orders
        ${whereClause}
        GROUP BY customer_email
      ),
      value_percentiles AS (
        SELECT 
          PERCENTILE_CONT(0.8) WITHIN GROUP (ORDER BY total_value) as p80,
          PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY total_value) as p50,
          PERCENTILE_CONT(0.2) WITHIN GROUP (ORDER BY total_value) as p20
        FROM customer_values
      )
      SELECT 
        CASE 
          WHEN cv.total_value >= vp.p80 THEN 'High Value'
          WHEN cv.total_value >= vp.p50 THEN 'Medium Value'
          WHEN cv.total_value >= vp.p20 THEN 'Low Value'
          ELSE 'Very Low Value'
        END as segment,
        COUNT(*) as customer_count,
        AVG(cv.total_value) as avg_value,
        SUM(cv.total_value) as total_revenue
      FROM customer_values cv
      CROSS JOIN value_percentiles vp
      GROUP BY 
        CASE 
          WHEN cv.total_value >= vp.p80 THEN 'High Value'
          WHEN cv.total_value >= vp.p50 THEN 'Medium Value'
          WHEN cv.total_value >= vp.p20 THEN 'Low Value'
          ELSE 'Very Low Value'
        END
      ORDER BY avg_value DESC
    `;

    const result = await pool.query(query, params);
    return result.rows;
  }

  /**
   * Segment customers by order frequency
   */
  async segmentByFrequency(tenantId, dateFrom, dateTo) {
    let whereClause = 'WHERE tenant_id = $1';
    const params = [tenantId];
    let paramCount = 1;

    if (dateFrom) {
      whereClause += ` AND platform_created_at >= $${++paramCount}`;
      params.push(new Date(dateFrom));
    }

    if (dateTo) {
      whereClause += ` AND platform_created_at <= $${++paramCount}`;
      params.push(new Date(dateTo));
    }

    const query = `
      WITH customer_frequency AS (
        SELECT 
          customer_email,
          COUNT(*) as order_count,
          SUM(total_amount) as total_value
        FROM orders
        ${whereClause}
        GROUP BY customer_email
      )
      SELECT 
        CASE 
          WHEN order_count >= 5 THEN 'Frequent'
          WHEN order_count >= 3 THEN 'Regular'
          WHEN order_count >= 2 THEN 'Occasional'
          ELSE 'One-time'
        END as segment,
        COUNT(*) as customer_count,
        AVG(order_count) as avg_orders,
        SUM(total_value) as total_revenue
      FROM customer_frequency
      GROUP BY 
        CASE 
          WHEN order_count >= 5 THEN 'Frequent'
          WHEN order_count >= 3 THEN 'Regular'
          WHEN order_count >= 2 THEN 'Occasional'
          ELSE 'One-time'
        END
      ORDER BY avg_orders DESC
    `;

    const result = await pool.query(query, params);
    return result.rows;
  }

  /**
   * Segment customers by recency
   */
  async segmentByRecency(tenantId, dateFrom, dateTo) {
    const query = `
      WITH customer_recency AS (
        SELECT 
          customer_email,
          MAX(platform_created_at) as last_order_date,
          EXTRACT(DAYS FROM NOW() - MAX(platform_created_at)) as days_since_last_order,
          COUNT(*) as order_count,
          SUM(total_amount) as total_value
        FROM orders
        WHERE tenant_id = $1
        GROUP BY customer_email
      )
      SELECT 
        CASE 
          WHEN days_since_last_order <= 30 THEN 'Active'
          WHEN days_since_last_order <= 90 THEN 'Recent'
          WHEN days_since_last_order <= 180 THEN 'Dormant'
          ELSE 'Inactive'
        END as segment,
        COUNT(*) as customer_count,
        AVG(days_since_last_order) as avg_days_since_last_order,
        SUM(total_value) as total_revenue
      FROM customer_recency
      GROUP BY 
        CASE 
          WHEN days_since_last_order <= 30 THEN 'Active'
          WHEN days_since_last_order <= 90 THEN 'Recent'
          WHEN days_since_last_order <= 180 THEN 'Dormant'
          ELSE 'Inactive'
        END
      ORDER BY avg_days_since_last_order ASC
    `;

    const result = await pool.query(query, [tenantId]);
    return result.rows;
  }

  /**
   * Generate RFM (Recency, Frequency, Monetary) segments
   */
  async generateRFMSegments(tenantId, dateFrom, dateTo) {
    const query = `
      WITH customer_rfm AS (
        SELECT 
          customer_email,
          EXTRACT(DAYS FROM NOW() - MAX(platform_created_at)) as recency,
          COUNT(*) as frequency,
          SUM(total_amount) as monetary,
          NTILE(5) OVER (ORDER BY EXTRACT(DAYS FROM NOW() - MAX(platform_created_at)) DESC) as r_score,
          NTILE(5) OVER (ORDER BY COUNT(*)) as f_score,
          NTILE(5) OVER (ORDER BY SUM(total_amount)) as m_score
        FROM orders
        WHERE tenant_id = $1
        GROUP BY customer_email
      ),
      rfm_segments AS (
        SELECT 
          *,
          CASE 
            WHEN r_score >= 4 AND f_score >= 4 AND m_score >= 4 THEN 'Champions'
            WHEN r_score >= 3 AND f_score >= 3 AND m_score >= 3 THEN 'Loyal Customers'
            WHEN r_score >= 3 AND f_score <= 2 AND m_score >= 3 THEN 'Potential Loyalists'
            WHEN r_score >= 4 AND f_score <= 2 AND m_score <= 2 THEN 'New Customers'
            WHEN r_score <= 2 AND f_score >= 3 AND m_score >= 3 THEN 'At Risk'
            WHEN r_score <= 2 AND f_score <= 2 AND m_score >= 3 THEN 'Cannot Lose Them'
            WHEN r_score <= 2 AND f_score <= 2 AND m_score <= 2 THEN 'Lost'
            ELSE 'Others'
          END as rfm_segment
        FROM customer_rfm
      )
      SELECT 
        rfm_segment,
        COUNT(*) as customer_count,
        AVG(recency) as avg_recency,
        AVG(frequency) as avg_frequency,
        AVG(monetary) as avg_monetary,
        SUM(monetary) as total_revenue
      FROM rfm_segments
      GROUP BY rfm_segment
      ORDER BY total_revenue DESC
    `;

    const result = await pool.query(query, [tenantId]);
    return result.rows;
  }
}

module.exports = AdvancedAnalyticsProcessor;
