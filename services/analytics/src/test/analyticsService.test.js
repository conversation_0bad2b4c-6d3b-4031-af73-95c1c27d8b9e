const { expect } = require('chai');
const sinon = require('sinon');
const AnalyticsService = require('../services/analyticsService');

describe('AnalyticsService', () => {
  let analyticsService;
  let mockPool;
  let mockClient;

  beforeEach(() => {
    analyticsService = new AnalyticsService();
    
    // Mock database client
    mockClient = {
      query: sinon.stub(),
      release: sinon.stub()
    };
    
    // Mock database pool
    mockPool = {
      connect: sinon.stub().resolves(mockClient)
    };
    
    // Mock the pool getter
    sinon.stub(analyticsService, 'pool').get(() => mockPool);
    
    // Mock cache methods
    sinon.stub(analyticsService, 'getFromCache').resolves(null);
    sinon.stub(analyticsService, 'setCache').resolves();
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('getLiveMetrics', () => {
    it('should return live metrics for a tenant', async () => {
      const tenantId = 'test-tenant-123';
      
      // Mock query results
      const mockLastMinuteResult = {
        rows: [{
          clicks_last_minute: '5',
          conversions_last_minute: '2',
          revenue_last_minute: '150.00'
        }]
      };
      
      const mockActiveSessionsResult = {
        rows: [{ active_sessions: '25' }]
      };
      
      const mockRecentClicksResult = {
        rows: [
          {
            id: 'click-1',
            timestamp: new Date().toISOString(),
            link_id: 'link-1',
            short_code: 'test123',
            country: 'US',
            device_type: 'desktop',
            converted: true
          }
        ]
      };
      
      const mockTrendResult = {
        rows: [{
          current_clicks: '10',
          previous_clicks: '8',
          current_conversions: '3',
          previous_conversions: '2',
          current_revenue: '200.00',
          previous_revenue: '150.00'
        }]
      };

      // Setup query stubs
      mockClient.query
        .onCall(0).resolves(mockLastMinuteResult)
        .onCall(1).resolves(mockActiveSessionsResult)
        .onCall(2).resolves(mockRecentClicksResult)
        .onCall(3).resolves(mockTrendResult);

      const result = await analyticsService.getLiveMetrics(tenantId);

      expect(result).to.have.property('metrics');
      expect(result).to.have.property('trends');
      expect(result).to.have.property('recentClicks');
      expect(result).to.have.property('lastUpdate');

      expect(result.metrics.active_sessions).to.equal(25);
      expect(result.metrics.clicks_last_minute).to.equal(5);
      expect(result.metrics.conversions_last_minute).to.equal(2);
      expect(result.metrics.revenue_last_minute).to.equal(150);

      expect(result.trends.clicks_trend).to.equal('up');
      expect(result.trends.conversions_trend).to.equal('up');
      expect(result.trends.revenue_trend).to.equal('up');

      expect(result.recentClicks).to.have.length(1);
      expect(result.recentClicks[0]).to.have.property('link_id', 'link-1');
      expect(result.recentClicks[0]).to.have.property('converted', true);
    });

    it('should handle database errors gracefully', async () => {
      const tenantId = 'test-tenant-123';
      
      mockClient.query.rejects(new Error('Database connection failed'));

      try {
        await analyticsService.getLiveMetrics(tenantId);
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error.message).to.equal('Database connection failed');
      }
    });

    it('should use cached data when available', async () => {
      const tenantId = 'test-tenant-123';
      const cachedData = {
        metrics: { active_sessions: 10 },
        trends: { clicks_trend: 'stable' },
        recentClicks: [],
        lastUpdate: new Date().toISOString()
      };
      
      analyticsService.getFromCache.resolves(cachedData);

      const result = await analyticsService.getLiveMetrics(tenantId);

      expect(result).to.deep.equal(cachedData);
      expect(mockPool.connect).not.to.have.been.called;
    });
  });

  describe('getSummary', () => {
    it('should return analytics summary with tenant isolation', async () => {
      const options = {
        tenantId: 'test-tenant-123',
        dateFrom: '2024-01-01',
        dateTo: '2024-01-31'
      };

      const mockSummaryResult = {
        rows: [{
          total_clicks: '1000',
          total_conversions: '50',
          total_links: '10',
          total_revenue: '2500.00',
          avg_order_value: '50.00',
          conversion_rate: '5.0'
        }]
      };

      const mockTrendResult = {
        rows: [
          {
            period: '2024-01-01',
            clicks: '100',
            conversions: '5',
            revenue: '250.00',
            unique_visitors: '80'
          }
        ]
      };

      const mockPlatformResult = {
        rows: [
          { platform: 'shopify', clicks: '600', conversions: '30', revenue: '1500.00' },
          { platform: 'woocommerce', clicks: '400', conversions: '20', revenue: '1000.00' }
        ]
      };

      mockClient.query
        .onCall(0).resolves(mockSummaryResult)
        .onCall(1).resolves(mockTrendResult)
        .onCall(2).resolves(mockPlatformResult);

      const result = await analyticsService.getSummary(options);

      expect(result).to.have.property('summary');
      expect(result).to.have.property('trend');
      expect(result).to.have.property('platforms');

      expect(result.summary.total_clicks).to.equal(1000);
      expect(result.summary.total_conversions).to.equal(50);
      expect(result.summary.conversion_rate).to.equal(5.0);

      expect(result.trend).to.have.length(1);
      expect(result.platforms).to.have.length(2);

      // Verify tenant_id was included in query
      const queryCall = mockClient.query.getCall(0);
      expect(queryCall.args[1]).to.include('test-tenant-123');
    });

    it('should handle missing tenant_id', async () => {
      const options = {
        dateFrom: '2024-01-01',
        dateTo: '2024-01-31'
      };

      const mockResult = {
        rows: [{
          total_clicks: '0',
          total_conversions: '0',
          total_links: '0',
          total_revenue: '0',
          avg_order_value: '0',
          conversion_rate: '0'
        }]
      };

      mockClient.query.resolves(mockResult);

      const result = await analyticsService.getSummary(options);

      expect(result.summary.total_clicks).to.equal(0);
    });
  });

  describe('trend calculation', () => {
    it('should calculate trends correctly', () => {
      // This would test the trend calculation logic in getLiveMetrics
      // The logic is: if change > 5% = 'up', if change < -5% = 'down', else 'stable'
      
      const calculateTrend = (current, previous) => {
        if (previous === 0) return current > 0 ? 'up' : 'stable';
        const change = ((current - previous) / previous) * 100;
        if (change > 5) return 'up';
        if (change < -5) return 'down';
        return 'stable';
      };

      expect(calculateTrend(110, 100)).to.equal('up');    // 10% increase
      expect(calculateTrend(90, 100)).to.equal('down');   // 10% decrease
      expect(calculateTrend(102, 100)).to.equal('stable'); // 2% increase
      expect(calculateTrend(10, 0)).to.equal('up');       // From zero
      expect(calculateTrend(0, 0)).to.equal('stable');    // Both zero
    });
  });
});
