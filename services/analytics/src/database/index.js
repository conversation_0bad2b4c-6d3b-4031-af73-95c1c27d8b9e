const { Pool } = require('pg');
const logger = require('../utils/logger');

let pool;

const connectDatabase = async () => {
  try {
    const dbConfig = {
      connectionString: process.env.DATABASE_URL,
      max: 20,
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 2000,
      ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
    };

    pool = new Pool(dbConfig);
    
    // Test the connection
    const client = await pool.connect();
    await client.query('SELECT NOW()');
    client.release();
    
    logger.info('Database connected successfully');
    
  } catch (error) {
    logger.error('Database connection failed', error);
    throw error;
  }
};

const closeDatabase = async () => {
  if (pool) {
    await pool.end();
    logger.info('Database connection closed');
  }
};

// Query performance monitoring
const queryPerformanceLog = [];
const SLOW_QUERY_THRESHOLD = 1000; // 1 second

const executeQuery = async (text, params = []) => {
  const start = Date.now();
  const queryId = Math.random().toString(36).substr(2, 9);

  try {
    logger.debug('Executing query', { queryId, text: text.substring(0, 100) + '...' });

    const result = await pool.query(text, params);
    const duration = Date.now() - start;

    // Log performance metrics
    const performanceData = {
      queryId,
      duration,
      rowCount: result.rowCount,
      timestamp: new Date().toISOString(),
      query: text.substring(0, 200) + (text.length > 200 ? '...' : ''),
      paramCount: params.length
    };

    // Add to performance log (keep last 100 queries)
    queryPerformanceLog.push(performanceData);
    if (queryPerformanceLog.length > 100) {
      queryPerformanceLog.shift();
    }

    // Log slow queries
    if (duration > SLOW_QUERY_THRESHOLD) {
      logger.warn('Slow query detected', {
        queryId,
        duration,
        rowCount: result.rowCount,
        query: text.substring(0, 500)
      });
    } else {
      logger.debug('Query completed', { queryId, duration, rowCount: result.rowCount });
    }

    return result;
  } catch (error) {
    const duration = Date.now() - start;
    logger.error('Query failed', {
      queryId,
      duration,
      error: error.message,
      query: text.substring(0, 200)
    });
    throw error;
  }
};

const getQueryPerformanceStats = () => {
  if (queryPerformanceLog.length === 0) {
    return {
      totalQueries: 0,
      averageDuration: 0,
      slowQueries: 0,
      recentQueries: []
    };
  }

  const durations = queryPerformanceLog.map(q => q.duration);
  const slowQueries = queryPerformanceLog.filter(q => q.duration > SLOW_QUERY_THRESHOLD);

  return {
    totalQueries: queryPerformanceLog.length,
    averageDuration: durations.reduce((a, b) => a + b, 0) / durations.length,
    maxDuration: Math.max(...durations),
    minDuration: Math.min(...durations),
    slowQueries: slowQueries.length,
    slowQueryThreshold: SLOW_QUERY_THRESHOLD,
    recentQueries: queryPerformanceLog.slice(-10).map(q => ({
      queryId: q.queryId,
      duration: q.duration,
      rowCount: q.rowCount,
      timestamp: q.timestamp,
      query: q.query
    }))
  };
};

module.exports = {
  connectDatabase,
  closeDatabase,
  executeQuery,
  getQueryPerformanceStats,
  get pool() {
    if (!pool) {
      throw new Error('Database not connected. Call connectDatabase() first.');
    }
    return pool;
  },
};