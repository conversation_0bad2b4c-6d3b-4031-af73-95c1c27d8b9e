const WebSocket = require('ws');
const EventEmitter = require('events');
const logger = require('../utils/logger');

class RealTimeAnalyticsServer extends EventEmitter {
  constructor(server) {
    super();
    this.wss = new WebSocket.Server({ 
      server,
      path: '/analytics/live'
    });
    this.clients = new Map();
    this.setupWebSocketServer();
    this.startMetricsCollection();
    
    logger.info('Real-time analytics WebSocket server initialized');
  }

  setupWebSocketServer() {
    this.wss.on('connection', (ws, req) => {
      const clientId = this.generateClientId();
      const clientInfo = {
        id: clientId,
        ws: ws,
        userId: null, // Will be set after authentication
        tenantId: null,
        subscriptions: new Set(),
        connectedAt: new Date(),
        lastPing: new Date()
      };

      this.clients.set(clientId, clientInfo);
      logger.info(`WebSocket client connected: ${clientId}`);

      // Send initial connection message
      this.sendToClient(clientId, {
        type: 'connection',
        status: 'connected',
        clientId: clientId,
        timestamp: new Date().toISOString()
      });

      // Handle incoming messages
      ws.on('message', (data) => {
        try {
          const message = JSON.parse(data);
          this.handleClientMessage(clientId, message);
        } catch (error) {
          logger.error('Invalid WebSocket message:', error);
          this.sendToClient(clientId, {
            type: 'error',
            message: 'Invalid message format'
          });
        }
      });

      // Handle client disconnect
      ws.on('close', () => {
        this.clients.delete(clientId);
        logger.info(`WebSocket client disconnected: ${clientId}`);
      });

      // Handle connection errors
      ws.on('error', (error) => {
        logger.error(`WebSocket error for client ${clientId}:`, error);
        this.clients.delete(clientId);
      });

      // Setup ping/pong for connection health
      ws.on('pong', () => {
        const client = this.clients.get(clientId);
        if (client) {
          client.lastPing = new Date();
        }
      });
    });

    // Setup connection health monitoring
    setInterval(() => {
      this.healthCheck();
    }, 30000); // Check every 30 seconds
  }

  handleClientMessage(clientId, message) {
    const client = this.clients.get(clientId);
    if (!client) return;

    switch (message.type) {
      case 'authenticate':
        this.authenticateClient(clientId, message.token);
        break;
      
      case 'subscribe':
        this.subscribeClient(clientId, message.channels);
        break;
      
      case 'unsubscribe':
        this.unsubscribeClient(clientId, message.channels);
        break;
      
      case 'request_snapshot':
        this.sendSnapshot(clientId, message.dataType);
        break;
      
      case 'ping':
        this.sendToClient(clientId, { type: 'pong', timestamp: new Date().toISOString() });
        break;
      
      default:
        logger.warn(`Unknown message type: ${message.type}`);
    }
  }

  async authenticateClient(clientId, token) {
    try {
      // TODO: Implement JWT token validation
      // const decoded = jwt.verify(token, process.env.JWT_SECRET);
      // For now, mock authentication
      const client = this.clients.get(clientId);
      if (client) {
        client.userId = 'user-123'; // Mock user ID
        client.tenantId = 'tenant-123'; // Mock tenant ID
        
        this.sendToClient(clientId, {
          type: 'authenticated',
          status: 'success',
          userId: client.userId,
          tenantId: client.tenantId
        });

        // Send initial dashboard data
        await this.sendSnapshot(clientId, 'dashboard');
      }
    } catch (error) {
      logger.error('Authentication failed:', error);
      this.sendToClient(clientId, {
        type: 'authenticated',
        status: 'failed',
        error: 'Invalid token'
      });
    }
  }

  subscribeClient(clientId, channels) {
    const client = this.clients.get(clientId);
    if (!client) return;

    channels.forEach(channel => {
      client.subscriptions.add(channel);
    });

    this.sendToClient(clientId, {
      type: 'subscribed',
      channels: Array.from(client.subscriptions)
    });

    logger.info(`Client ${clientId} subscribed to channels: ${channels.join(', ')}`);
  }

  unsubscribeClient(clientId, channels) {
    const client = this.clients.get(clientId);
    if (!client) return;

    channels.forEach(channel => {
      client.subscriptions.delete(channel);
    });

    this.sendToClient(clientId, {
      type: 'unsubscribed',
      channels: channels
    });
  }

  async sendSnapshot(clientId, dataType) {
    const client = this.clients.get(clientId);
    if (!client || !client.tenantId) return;

    try {
      let data;
      switch (dataType) {
        case 'dashboard':
          data = await this.getDashboardSnapshot(client.tenantId);
          break;
        case 'analytics':
          data = await this.getAnalyticsSnapshot(client.tenantId);
          break;
        case 'links':
          data = await this.getLinksSnapshot(client.tenantId);
          break;
        default:
          data = { error: 'Unknown data type' };
      }

      this.sendToClient(clientId, {
        type: 'snapshot',
        dataType: dataType,
        data: data,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      logger.error(`Failed to send snapshot for ${dataType}:`, error);
      this.sendToClient(clientId, {
        type: 'error',
        message: `Failed to load ${dataType} data`
      });
    }
  }

  async getDashboardSnapshot(tenantId) {
    try {
      // Use the analytics service to get real data
      const AnalyticsService = require('../services/analyticsService');
      const analyticsService = new AnalyticsService();

      // Get summary data for the last 30 days
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const [summary, liveMetrics] = await Promise.all([
        analyticsService.getSummary({
          dateFrom: thirtyDaysAgo.toISOString(),
          dateTo: new Date().toISOString(),
          tenantId
        }),
        analyticsService.getLiveMetrics(tenantId)
      ]);

      return {
        summary: summary.summary || {
          total_clicks: 0,
          total_conversions: 0,
          total_revenue: 0,
          conversion_rate: 0,
          avg_order_value: 0
        },
        trend: summary.trend || [],
        platforms: summary.platforms || [],
        recent_activity: liveMetrics.recentClicks || [],
        live_metrics: liveMetrics.metrics || {
          active_sessions: 0,
          clicks_last_hour: 0,
          conversions_last_hour: 0,
          current_conversion_rate: 0
        }
      };
    } catch (error) {
      logger.error('Failed to get dashboard snapshot:', error);
      // Return empty data structure on error
      return {
        summary: {
          total_clicks: 0,
          total_conversions: 0,
          total_revenue: 0,
          conversion_rate: 0,
          avg_order_value: 0
        },
        trend: [],
        platforms: [],
        recent_activity: [],
        live_metrics: {
          active_sessions: 0,
          clicks_last_hour: 0,
          conversions_last_hour: 0,
          current_conversion_rate: 0
        }
      };
    }
  }

  async getAnalyticsSnapshot(tenantId) {
    try {
      const AnalyticsService = require('../services/analyticsService');
      const analyticsService = new AnalyticsService();

      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const summary = await analyticsService.getSummary({
        dateFrom: thirtyDaysAgo.toISOString(),
        dateTo: new Date().toISOString(),
        tenantId
      });

      return {
        summary: summary.summary || {
          total_clicks: 0,
          total_conversions: 0,
          total_revenue: 0,
          conversion_rate: 0,
          avg_order_value: 0
        },
        platforms: summary.platforms || [],
        trend: summary.trend || []
      };
    } catch (error) {
      logger.error('Failed to get analytics snapshot:', error);
      return {
        summary: {
          total_clicks: 0,
          total_conversions: 0,
          total_revenue: 0,
          conversion_rate: 0,
          avg_order_value: 0
        },
        platforms: [],
        trend: []
      };
    }
  }

  async getLinksSnapshot(tenantId) {
    return {
      total_links: Math.floor(Math.random() * 100) + 200,
      active_links: Math.floor(Math.random() * 80) + 150,
      top_performing: this.generateMockTopLinks()
    };
  }

  generateMockTrendData() {
    const data = [];
    const now = new Date();
    for (let i = 29; i >= 0; i--) {
      const date = new Date(now);
      date.setDate(date.getDate() - i);
      data.push({
        date: date.toISOString().split('T')[0],
        clicks: Math.floor(Math.random() * 1000) + 500,
        conversions: Math.floor(Math.random() * 50) + 25,
        revenue: Math.floor(Math.random() * 5000) + 1000
      });
    }
    return data;
  }

  generateMockPlatformData() {
    const countries = ['US', 'GB', 'CA', 'AU', 'DE', 'FR', 'JP', 'BR'];
    return countries.map(country => ({
      country,
      clicks: Math.floor(Math.random() * 5000) + 1000,
      conversions: Math.floor(Math.random() * 200) + 50
    }));
  }

  generateMockActivity() {
    const activities = [
      'New click on product link',
      'Order placed via affiliate link',
      'New link created for campaign',
      'High-performing link identified',
      'Conversion rate spike detected'
    ];

    return activities.map((description, index) => ({
      id: `activity-${index}`,
      type: 'activity',
      description,
      timestamp: new Date(Date.now() - Math.random() * 3600000).toISOString()
    }));
  }

  generateMockTopLinks() {
    return [
      {
        id: '1',
        title: 'Summer Sale Campaign',
        short_code: 'summer-sale',
        clicks: Math.floor(Math.random() * 1000) + 500,
        conversions: Math.floor(Math.random() * 50) + 20
      },
      {
        id: '2', 
        title: 'New Product Launch',
        short_code: 'new-product',
        clicks: Math.floor(Math.random() * 800) + 300,
        conversions: Math.floor(Math.random() * 40) + 15
      }
    ];
  }

  startMetricsCollection() {
    // Simulate real-time data updates every 5 seconds
    setInterval(() => {
      this.broadcastLiveUpdates();
    }, 5000);

    // Simulate click events every 2-10 seconds
    setInterval(() => {
      this.simulateClickEvent();
    }, Math.random() * 8000 + 2000);
  }

  broadcastLiveUpdates() {
    const liveUpdate = {
      type: 'live_update',
      data: {
        timestamp: new Date().toISOString(),
        metrics: {
          active_sessions: Math.floor(Math.random() * 100) + 50,
          clicks_last_minute: Math.floor(Math.random() * 10) + 2,
          conversions_last_minute: Math.floor(Math.random() * 2),
          revenue_last_minute: Math.floor(Math.random() * 500) + 50
        },
        trends: {
          clicks_trend: Math.random() > 0.5 ? 'up' : 'down',
          conversions_trend: Math.random() > 0.5 ? 'up' : 'down',
          revenue_trend: Math.random() > 0.5 ? 'up' : 'down'
        }
      }
    };

    this.broadcastToSubscribers('live_metrics', liveUpdate);
  }

  simulateClickEvent() {
    const clickEvent = {
      type: 'click_event',
      data: {
        timestamp: new Date().toISOString(),
        link_id: `link-${Math.floor(Math.random() * 10) + 1}`,
        country: ['US', 'GB', 'CA', 'AU', 'DE'][Math.floor(Math.random() * 5)],
        device: ['desktop', 'mobile', 'tablet'][Math.floor(Math.random() * 3)],
        converted: Math.random() > 0.85 // 15% conversion rate
      }
    };

    this.broadcastToSubscribers('click_events', clickEvent);
  }

  broadcastToSubscribers(channel, data) {
    this.clients.forEach((client, clientId) => {
      if (client.subscriptions.has(channel)) {
        this.sendToClient(clientId, data);
      }
    });
  }

  sendToClient(clientId, data) {
    const client = this.clients.get(clientId);
    if (client && client.ws.readyState === WebSocket.OPEN) {
      try {
        client.ws.send(JSON.stringify(data));
      } catch (error) {
        logger.error(`Failed to send data to client ${clientId}:`, error);
        this.clients.delete(clientId);
      }
    }
  }

  healthCheck() {
    const now = new Date();
    this.clients.forEach((client, clientId) => {
      if (client.ws.readyState === WebSocket.OPEN) {
        // Send ping
        client.ws.ping();
        
        // Check if client is responsive
        if (now - client.lastPing > 60000) { // 1 minute timeout
          logger.warn(`Client ${clientId} appears unresponsive, closing connection`);
          client.ws.close();
          this.clients.delete(clientId);
        }
      } else {
        this.clients.delete(clientId);
      }
    });

    logger.info(`WebSocket health check: ${this.clients.size} active connections`);
  }

  generateClientId() {
    return 'client_' + Math.random().toString(36).substring(2, 15) + 
           Math.random().toString(36).substring(2, 15);
  }

  getActiveConnectionsCount() {
    return this.clients.size;
  }

  getConnectionsInfo() {
    const connections = [];
    this.clients.forEach((client, clientId) => {
      connections.push({
        clientId,
        userId: client.userId,
        tenantId: client.tenantId,
        connectedAt: client.connectedAt,
        subscriptions: Array.from(client.subscriptions)
      });
    });
    return connections;
  }
}

module.exports = RealTimeAnalyticsServer;