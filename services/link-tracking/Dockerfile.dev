# Podman-optimized Dockerfile for Link Tracking Service
FROM docker.io/golang:1.23-alpine AS base

# Create non-root user for security
RUN addgroup -g 1001 -S golang && \
    adduser -S gouser -u 1001 -G golang

WORKDIR /app

# Copy go mod files
COPY go.mod go.sum ./
RUN go mod download

# Copy source code
COPY . .

# Build the application with proper flags for Podman
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build \
    -ldflags='-w -s -extldflags "-static"' \
    -a -installsuffix cgo \
    -o main ./cmd/server

# Change ownership to non-root user
R<PERSON> chown -R gouser:golang /app

# Switch to non-root user
USER gouser

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=10s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:8080/health || exit 1

# Run the application
CMD ["./main"]