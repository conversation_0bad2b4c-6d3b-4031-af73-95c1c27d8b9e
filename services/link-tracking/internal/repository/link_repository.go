package repository

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"

	"link-tracking/internal/database"
	"link-tracking/internal/metrics"
	"link-tracking/internal/models"
)

type LinkRepository struct {
	db *database.DB
}

// NewLinkRepository creates a new link repository
func NewLinkRepository(db *database.DB) *LinkRepository {
	return &LinkRepository{db: db}
}

// Create creates a new link
func (r *LinkRepository) Create(ctx context.Context, link *models.Link) error {
	start := time.Now()
	defer func() {
		metrics.RecordDatabaseOperation("create_link", "success", time.Since(start))
	}()

	query := `
		INSERT INTO links (id, tenant_id, campaign_id, short_code, target_url, title, description, 
			domain, utm_source, utm_medium, utm_campaign, utm_term, utm_content, is_active, expires_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)
	`

	_, err := r.db.ExecContext(ctx, query,
		link.ID, link.TenantID, link.CampaignID, link.ShortCode, link.TargetURL,
		link.Title, link.Description, link.Domain, link.UTMSource, link.UTMMedium,
		link.UTMCampaign, link.UTMTerm, link.UTMContent, link.IsActive, link.ExpiresAt,
	)

	if err != nil {
		metrics.RecordDatabaseOperation("create_link", "error", time.Since(start))
		logrus.WithError(err).Error("Failed to create link")
		return fmt.Errorf("failed to create link: %w", err)
	}

	logrus.WithFields(logrus.Fields{
		"link_id":    link.ID,
		"short_code": link.ShortCode,
		"tenant_id":  link.TenantID,
	}).Info("Link created successfully")

	return nil
}

// GetByID retrieves a link by ID
func (r *LinkRepository) GetByID(ctx context.Context, id uuid.UUID) (*models.Link, error) {
	start := time.Now()
	defer func() {
		metrics.RecordDatabaseOperation("get_link_by_id", "success", time.Since(start))
	}()

	query := `
		SELECT id, tenant_id, campaign_id, short_code, target_url, title, description,
			domain, utm_source, utm_medium, utm_campaign, utm_term, utm_content,
			is_active, expires_at, created_at, updated_at
		FROM links
		WHERE id = $1
	`

	link := &models.Link{}
	err := r.db.QueryRowContext(ctx, query, id).Scan(
		&link.ID, &link.TenantID, &link.CampaignID, &link.ShortCode, &link.TargetURL,
		&link.Title, &link.Description, &link.Domain, &link.UTMSource, &link.UTMMedium,
		&link.UTMCampaign, &link.UTMTerm, &link.UTMContent, &link.IsActive,
		&link.ExpiresAt, &link.CreatedAt, &link.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		metrics.RecordDatabaseOperation("get_link_by_id", "error", time.Since(start))
		return nil, fmt.Errorf("failed to get link by ID: %w", err)
	}

	return link, nil
}

// GetByShortCode retrieves a link by short code
func (r *LinkRepository) GetByShortCode(ctx context.Context, shortCode string) (*models.Link, error) {
	start := time.Now()
	defer func() {
		metrics.RecordDatabaseOperation("get_link_by_code", "success", time.Since(start))
	}()

	query := `
		SELECT id, tenant_id, campaign_id, short_code, target_url, title, description,
			domain, utm_source, utm_medium, utm_campaign, utm_term, utm_content,
			is_active, expires_at, created_at, updated_at
		FROM links
		WHERE short_code = $1 AND is_active = true
	`

	link := &models.Link{}
	err := r.db.QueryRowContext(ctx, query, shortCode).Scan(
		&link.ID, &link.TenantID, &link.CampaignID, &link.ShortCode, &link.TargetURL,
		&link.Title, &link.Description, &link.Domain, &link.UTMSource, &link.UTMMedium,
		&link.UTMCampaign, &link.UTMTerm, &link.UTMContent, &link.IsActive,
		&link.ExpiresAt, &link.CreatedAt, &link.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		metrics.RecordDatabaseOperation("get_link_by_code", "error", time.Since(start))
		return nil, fmt.Errorf("failed to get link by short code: %w", err)
	}

	// Check if link is expired
	if link.ExpiresAt != nil && link.ExpiresAt.Before(time.Now()) {
		return nil, nil
	}

	return link, nil
}

// Update updates a link
func (r *LinkRepository) Update(ctx context.Context, id uuid.UUID, updates *models.UpdateLinkRequest) error {
	start := time.Now()
	defer func() {
		metrics.RecordDatabaseOperation("update_link", "success", time.Since(start))
	}()

	// Build dynamic query based on provided fields
	setParts := []string{"updated_at = CURRENT_TIMESTAMP"}
	args := []interface{}{}
	argCount := 1

	if updates.TargetURL != nil {
		setParts = append(setParts, fmt.Sprintf("target_url = $%d", argCount))
		args = append(args, *updates.TargetURL)
		argCount++
	}

	if updates.Title != nil {
		setParts = append(setParts, fmt.Sprintf("title = $%d", argCount))
		args = append(args, *updates.Title)
		argCount++
	}

	if updates.Description != nil {
		setParts = append(setParts, fmt.Sprintf("description = $%d", argCount))
		args = append(args, *updates.Description)
		argCount++
	}

	if updates.IsActive != nil {
		setParts = append(setParts, fmt.Sprintf("is_active = $%d", argCount))
		args = append(args, *updates.IsActive)
		argCount++
	}

	if updates.ExpiresAt != nil {
		setParts = append(setParts, fmt.Sprintf("expires_at = $%d", argCount))
		args = append(args, *updates.ExpiresAt)
		argCount++
	}

	if len(setParts) == 1 {
		return fmt.Errorf("no fields to update")
	}

	query := fmt.Sprintf("UPDATE links SET %s WHERE id = $%d", 
		fmt.Sprintf("%s", setParts), argCount)
	args = append(args, id)

	result, err := r.db.ExecContext(ctx, query, args...)
	if err != nil {
		metrics.RecordDatabaseOperation("update_link", "error", time.Since(start))
		return fmt.Errorf("failed to update link: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("link not found")
	}

	logrus.WithFields(logrus.Fields{
		"link_id": id,
	}).Info("Link updated successfully")

	return nil
}

// Delete soft deletes a link
func (r *LinkRepository) Delete(ctx context.Context, id uuid.UUID) error {
	start := time.Now()
	defer func() {
		metrics.RecordDatabaseOperation("delete_link", "success", time.Since(start))
	}()

	query := `UPDATE links SET is_active = false, updated_at = CURRENT_TIMESTAMP WHERE id = $1`

	result, err := r.db.ExecContext(ctx, query, id)
	if err != nil {
		metrics.RecordDatabaseOperation("delete_link", "error", time.Since(start))
		return fmt.Errorf("failed to delete link: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("link not found")
	}

	logrus.WithFields(logrus.Fields{
		"link_id": id,
	}).Info("Link deleted successfully")

	return nil
}

// GetByTenant retrieves links for a tenant with pagination
func (r *LinkRepository) GetByTenant(ctx context.Context, tenantID uuid.UUID, offset, limit int) ([]*models.Link, error) {
	start := time.Now()
	defer func() {
		metrics.RecordDatabaseOperation("get_links_by_tenant", "success", time.Since(start))
	}()

	query := `
		SELECT id, tenant_id, campaign_id, short_code, target_url, title, description,
			domain, utm_source, utm_medium, utm_campaign, utm_term, utm_content,
			is_active, expires_at, created_at, updated_at
		FROM links
		WHERE tenant_id = $1
		ORDER BY created_at DESC
		LIMIT $2 OFFSET $3
	`

	rows, err := r.db.QueryContext(ctx, query, tenantID, limit, offset)
	if err != nil {
		metrics.RecordDatabaseOperation("get_links_by_tenant", "error", time.Since(start))
		return nil, fmt.Errorf("failed to get links by tenant: %w", err)
	}
	defer rows.Close()

	var links []*models.Link
	for rows.Next() {
		link := &models.Link{}
		err := rows.Scan(
			&link.ID, &link.TenantID, &link.CampaignID, &link.ShortCode, &link.TargetURL,
			&link.Title, &link.Description, &link.Domain, &link.UTMSource, &link.UTMMedium,
			&link.UTMCampaign, &link.UTMTerm, &link.UTMContent, &link.IsActive,
			&link.ExpiresAt, &link.CreatedAt, &link.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan link: %w", err)
		}
		links = append(links, link)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("failed to iterate rows: %w", err)
	}

	return links, nil
}

// List retrieves paginated links for a tenant with search and filtering
func (r *LinkRepository) List(ctx context.Context, tenantID uuid.UUID, params *models.ListLinksParams) ([]models.Link, int, error) {
	start := time.Now()
	defer func() {
		metrics.RecordDatabaseOperation("list_links", "success", time.Since(start))
	}()

	// Get total count for this tenant
	var total int
	countErr := r.db.QueryRowContext(ctx, "SELECT COUNT(*) FROM links WHERE tenant_id = $1", tenantID).Scan(&total)
	if countErr != nil {
		metrics.RecordDatabaseOperation("list_links", "error", time.Since(start))
		return nil, 0, fmt.Errorf("failed to get total count: %w", countErr)
	}

	// Use the existing GetByTenant method and convert result
	linksPtr, err := r.GetByTenant(ctx, tenantID, params.Offset, params.Limit)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get links: %w", err)
	}

	// Convert []*models.Link to []models.Link
	links := make([]models.Link, len(linksPtr))
	for i, linkPtr := range linksPtr {
		links[i] = *linkPtr
	}

	return links, total, nil
}

// IsShortCodeExists checks if a short code already exists
func (r *LinkRepository) IsShortCodeExists(ctx context.Context, shortCode string) (bool, error) {
	start := time.Now()
	defer func() {
		metrics.RecordDatabaseOperation("check_short_code", "success", time.Since(start))
	}()

	query := `SELECT EXISTS(SELECT 1 FROM links WHERE short_code = $1)`

	var exists bool
	err := r.db.QueryRowContext(ctx, query, shortCode).Scan(&exists)
	if err != nil {
		metrics.RecordDatabaseOperation("check_short_code", "error", time.Since(start))
		return false, fmt.Errorf("failed to check short code existence: %w", err)
	}

	return exists, nil
}