package repository

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"

	"link-tracking/internal/database"
	"link-tracking/internal/metrics"
	"link-tracking/internal/models"
)

type ClickRepository struct {
	db *database.DB
}

// NewClickRepository creates a new click repository
func NewClickRepository(db *database.DB) *ClickRepository {
	return &ClickRepository{db: db}
}

// <PERSON><PERSON> creates a new click record
func (r *ClickRepository) Create(ctx context.Context, click *models.Click) error {
	start := time.Now()
	defer func() {
		metrics.RecordDatabaseOperation("create_click", "success", time.Since(start))
	}()

	query := `
		INSERT INTO clicks (id, link_id, session_id, ip_address, user_agent, referrer,
			country, city, device_type, browser, os, clicked_at, tracking_id)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
	`

	_, err := r.db.ExecContext(ctx, query,
		click.ID, click.LinkID, click.SessionID, click.IPAddress, click.UserAgent,
		click.Referrer, click.Country, click.City, click.DeviceType, click.Browser,
		click.OS, click.ClickedAt, click.TrackingID,
	)

	if err != nil {
		metrics.RecordDatabaseOperation("create_click", "error", time.Since(start))
		logrus.WithError(err).Error("Failed to create click")
		return fmt.Errorf("failed to create click: %w", err)
	}

	logrus.WithFields(logrus.Fields{
		"click_id":    click.ID,
		"link_id":     click.LinkID,
		"tracking_id": click.TrackingID,
	}).Info("Click created successfully")

	return nil
}

// GetAnalytics retrieves analytics data for a link
func (r *ClickRepository) GetAnalytics(ctx context.Context, linkID uuid.UUID) (*models.LinkAnalytics, error) {
	start := time.Now()
	defer func() {
		metrics.RecordDatabaseOperation("get_analytics", "success", time.Since(start))
	}()

	analytics := &models.LinkAnalytics{
		LinkID: linkID,
	}

	// Get total clicks
	err := r.db.QueryRowContext(ctx, 
		`SELECT COUNT(*) FROM clicks WHERE link_id = $1`, 
		linkID).Scan(&analytics.TotalClicks)
	if err != nil {
		return nil, fmt.Errorf("failed to get total clicks: %w", err)
	}

	// Get unique clicks (based on IP address)
	err = r.db.QueryRowContext(ctx,
		`SELECT COUNT(DISTINCT ip_address) FROM clicks WHERE link_id = $1`,
		linkID).Scan(&analytics.UniqueClicks)
	if err != nil {
		return nil, fmt.Errorf("failed to get unique clicks: %w", err)
	}

	// Get clicks today
	err = r.db.QueryRowContext(ctx,
		`SELECT COUNT(*) FROM clicks WHERE link_id = $1 AND clicked_at >= CURRENT_DATE`,
		linkID).Scan(&analytics.ClicksToday)
	if err != nil {
		return nil, fmt.Errorf("failed to get clicks today: %w", err)
	}

	// Get clicks this week
	err = r.db.QueryRowContext(ctx,
		`SELECT COUNT(*) FROM clicks WHERE link_id = $1 AND clicked_at >= DATE_TRUNC('week', CURRENT_DATE)`,
		linkID).Scan(&analytics.ClicksThisWeek)
	if err != nil {
		return nil, fmt.Errorf("failed to get clicks this week: %w", err)
	}

	// Get clicks this month
	err = r.db.QueryRowContext(ctx,
		`SELECT COUNT(*) FROM clicks WHERE link_id = $1 AND clicked_at >= DATE_TRUNC('month', CURRENT_DATE)`,
		linkID).Scan(&analytics.ClicksThisMonth)
	if err != nil {
		return nil, fmt.Errorf("failed to get clicks this month: %w", err)
	}

	// Get top countries
	rows, err := r.db.QueryContext(ctx,
		`SELECT country, COUNT(*) as clicks 
		 FROM clicks 
		 WHERE link_id = $1 AND country IS NOT NULL 
		 GROUP BY country 
		 ORDER BY clicks DESC 
		 LIMIT 10`,
		linkID)
	if err != nil {
		return nil, fmt.Errorf("failed to get top countries: %w", err)
	}
	defer rows.Close()

	for rows.Next() {
		var countryClick models.CountryClick
		err := rows.Scan(&countryClick.Country, &countryClick.Clicks)
		if err != nil {
			return nil, fmt.Errorf("failed to scan country click: %w", err)
		}
		analytics.TopCountries = append(analytics.TopCountries, countryClick)
	}

	// Get top referrers
	rows, err = r.db.QueryContext(ctx,
		`SELECT COALESCE(referrer, 'Direct') as referrer, COUNT(*) as clicks 
		 FROM clicks 
		 WHERE link_id = $1 
		 GROUP BY referrer 
		 ORDER BY clicks DESC 
		 LIMIT 10`,
		linkID)
	if err != nil {
		return nil, fmt.Errorf("failed to get top referrers: %w", err)
	}
	defer rows.Close()

	for rows.Next() {
		var referrerClick models.ReferrerClick
		err := rows.Scan(&referrerClick.Referrer, &referrerClick.Clicks)
		if err != nil {
			return nil, fmt.Errorf("failed to scan referrer click: %w", err)
		}
		analytics.TopReferrers = append(analytics.TopReferrers, referrerClick)
	}

	// Get device breakdown
	rows, err = r.db.QueryContext(ctx,
		`SELECT COALESCE(device_type, 'Unknown') as device_type, COUNT(*) as clicks 
		 FROM clicks 
		 WHERE link_id = $1 
		 GROUP BY device_type 
		 ORDER BY clicks DESC`,
		linkID)
	if err != nil {
		return nil, fmt.Errorf("failed to get device breakdown: %w", err)
	}
	defer rows.Close()

	for rows.Next() {
		var deviceClick models.DeviceClick
		err := rows.Scan(&deviceClick.DeviceType, &deviceClick.Clicks)
		if err != nil {
			return nil, fmt.Errorf("failed to scan device click: %w", err)
		}
		analytics.DeviceBreakdown = append(analytics.DeviceBreakdown, deviceClick)
	}

	// Get browser breakdown
	rows, err = r.db.QueryContext(ctx,
		`SELECT COALESCE(browser, 'Unknown') as browser, COUNT(*) as clicks 
		 FROM clicks 
		 WHERE link_id = $1 
		 GROUP BY browser 
		 ORDER BY clicks DESC 
		 LIMIT 10`,
		linkID)
	if err != nil {
		return nil, fmt.Errorf("failed to get browser breakdown: %w", err)
	}
	defer rows.Close()

	for rows.Next() {
		var browserClick models.BrowserClick
		err := rows.Scan(&browserClick.Browser, &browserClick.Clicks)
		if err != nil {
			return nil, fmt.Errorf("failed to scan browser click: %w", err)
		}
		analytics.BrowserBreakdown = append(analytics.BrowserBreakdown, browserClick)
	}

	// Get hourly distribution for last 24 hours
	rows, err = r.db.QueryContext(ctx,
		`SELECT EXTRACT(HOUR FROM clicked_at) as hour, COUNT(*) as clicks 
		 FROM clicks 
		 WHERE link_id = $1 AND clicked_at >= NOW() - INTERVAL '24 hours'
		 GROUP BY EXTRACT(HOUR FROM clicked_at) 
		 ORDER BY hour`,
		linkID)
	if err != nil {
		return nil, fmt.Errorf("failed to get hourly distribution: %w", err)
	}
	defer rows.Close()

	for rows.Next() {
		var hourlyClick models.HourlyClick
		err := rows.Scan(&hourlyClick.Hour, &hourlyClick.Clicks)
		if err != nil {
			return nil, fmt.Errorf("failed to scan hourly click: %w", err)
		}
		analytics.HourlyDistribution = append(analytics.HourlyDistribution, hourlyClick)
	}

	return analytics, nil
}

// GetClicksByLink retrieves clicks for a specific link with pagination
func (r *ClickRepository) GetClicksByLink(ctx context.Context, linkID uuid.UUID, offset, limit int) ([]*models.Click, error) {
	start := time.Now()
	defer func() {
		metrics.RecordDatabaseOperation("get_clicks_by_link", "success", time.Since(start))
	}()

	query := `
		SELECT id, link_id, session_id, ip_address, user_agent, referrer,
			country, city, device_type, browser, os, clicked_at, tracking_id
		FROM clicks
		WHERE link_id = $1
		ORDER BY clicked_at DESC
		LIMIT $2 OFFSET $3
	`

	rows, err := r.db.QueryContext(ctx, query, linkID, limit, offset)
	if err != nil {
		metrics.RecordDatabaseOperation("get_clicks_by_link", "error", time.Since(start))
		return nil, fmt.Errorf("failed to get clicks by link: %w", err)
	}
	defer rows.Close()

	var clicks []*models.Click
	for rows.Next() {
		click := &models.Click{}
		err := rows.Scan(
			&click.ID, &click.LinkID, &click.SessionID, &click.IPAddress,
			&click.UserAgent, &click.Referrer, &click.Country, &click.City,
			&click.DeviceType, &click.Browser, &click.OS, &click.ClickedAt,
			&click.TrackingID,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan click: %w", err)
		}
		clicks = append(clicks, click)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("failed to iterate rows: %w", err)
	}

	return clicks, nil
}