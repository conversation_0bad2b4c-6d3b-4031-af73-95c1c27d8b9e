package models

import (
	"time"
	"github.com/google/uuid"
)

// Link represents a branded short link
type Link struct {
	ID          uuid.UUID  `json:"id" db:"id"`
	TenantID    uuid.UUID  `json:"tenant_id" db:"tenant_id"`
	CampaignID  *uuid.UUID `json:"campaign_id,omitempty" db:"campaign_id"`
	ShortCode   string     `json:"short_code" db:"short_code"`
	TargetURL   string     `json:"target_url" db:"target_url"`
	Title       *string    `json:"title,omitempty" db:"title"`
	Description *string    `json:"description,omitempty" db:"description"`
	Domain      *string    `json:"domain,omitempty" db:"domain"`
	UTMSource   *string    `json:"utm_source,omitempty" db:"utm_source"`
	UTMMedium   *string    `json:"utm_medium,omitempty" db:"utm_medium"`
	UTMCampaign *string    `json:"utm_campaign,omitempty" db:"utm_campaign"`
	UTMTerm     *string    `json:"utm_term,omitempty" db:"utm_term"`
	UTMContent  *string    `json:"utm_content,omitempty" db:"utm_content"`
	IsActive    bool       `json:"is_active" db:"is_active"`
	ExpiresAt   *time.Time `json:"expires_at,omitempty" db:"expires_at"`
	CreatedAt   time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at" db:"updated_at"`
}

// Click represents a click event on a link
type Click struct {
	ID         uuid.UUID  `json:"id" db:"id"`
	LinkID     uuid.UUID  `json:"link_id" db:"link_id"`
	SessionID  *string    `json:"session_id,omitempty" db:"session_id"`
	IPAddress  *string    `json:"ip_address,omitempty" db:"ip_address"`
	UserAgent  *string    `json:"user_agent,omitempty" db:"user_agent"`
	Referrer   *string    `json:"referrer,omitempty" db:"referrer"`
	Country    *string    `json:"country,omitempty" db:"country"`
	City       *string    `json:"city,omitempty" db:"city"`
	DeviceType *string    `json:"device_type,omitempty" db:"device_type"`
	Browser    *string    `json:"browser,omitempty" db:"browser"`
	OS         *string    `json:"os,omitempty" db:"os"`
	ClickedAt  time.Time  `json:"clicked_at" db:"clicked_at"`
	TrackingID *string    `json:"tracking_id,omitempty" db:"tracking_id"`
}

// CreateLinkRequest represents the request to create a new link
type CreateLinkRequest struct {
	TenantID    uuid.UUID  `json:"tenant_id" binding:"required"`
	CampaignID  *uuid.UUID `json:"campaign_id,omitempty"`
	TargetURL   string     `json:"target_url" binding:"required,url"`
	Title       *string    `json:"title,omitempty"`
	Description *string    `json:"description,omitempty"`
	Domain      *string    `json:"domain,omitempty"`
	CustomCode  *string    `json:"custom_code,omitempty"`
	UTMSource   *string    `json:"utm_source,omitempty"`
	UTMMedium   *string    `json:"utm_medium,omitempty"`
	UTMCampaign *string    `json:"utm_campaign,omitempty"`
	UTMTerm     *string    `json:"utm_term,omitempty"`
	UTMContent  *string    `json:"utm_content,omitempty"`
	ExpiresAt   *time.Time `json:"expires_at,omitempty"`
}

// UpdateLinkRequest represents the request to update a link
type UpdateLinkRequest struct {
	TargetURL   *string    `json:"target_url,omitempty"`
	Title       *string    `json:"title,omitempty"`
	Description *string    `json:"description,omitempty"`
	IsActive    *bool      `json:"is_active,omitempty"`
	ExpiresAt   *time.Time `json:"expires_at,omitempty"`
}

// ClickRequest represents the data collected when a link is clicked
type ClickRequest struct {
	IPAddress string `json:"ip_address"`
	UserAgent string `json:"user_agent"`
	Referrer  string `json:"referrer"`
}

// LinkAnalytics represents analytics data for a link
type LinkAnalytics struct {
	LinkID            uuid.UUID `json:"link_id"`
	TotalClicks       int       `json:"total_clicks"`
	UniqueClicks      int       `json:"unique_clicks"`
	ClicksToday       int       `json:"clicks_today"`
	ClicksThisWeek    int       `json:"clicks_this_week"`
	ClicksThisMonth   int       `json:"clicks_this_month"`
	TopCountries      []CountryClick `json:"top_countries"`
	TopReferrers      []ReferrerClick `json:"top_referrers"`
	DeviceBreakdown   []DeviceClick `json:"device_breakdown"`
	BrowserBreakdown  []BrowserClick `json:"browser_breakdown"`
	HourlyDistribution []HourlyClick `json:"hourly_distribution"`
}

// CountryClick represents click data by country
type CountryClick struct {
	Country string `json:"country"`
	Clicks  int    `json:"clicks"`
}

// ReferrerClick represents click data by referrer
type ReferrerClick struct {
	Referrer string `json:"referrer"`
	Clicks   int    `json:"clicks"`
}

// DeviceClick represents click data by device type
type DeviceClick struct {
	DeviceType string `json:"device_type"`
	Clicks     int    `json:"clicks"`
}

// BrowserClick represents click data by browser
type BrowserClick struct {
	Browser string `json:"browser"`
	Clicks  int    `json:"clicks"`
}

// HourlyClick represents click data by hour
type HourlyClick struct {
	Hour   int `json:"hour"`
	Clicks int `json:"clicks"`
}

// APIResponse represents a standard API response
type APIResponse struct {
	Success bool        `json:"success"`
	Data    interface{} `json:"data,omitempty"`
	Error   *string     `json:"error,omitempty"`
	Message *string     `json:"message,omitempty"`
}

// ListLinksParams represents parameters for listing links
type ListLinksParams struct {
	Limit     int    `json:"limit" form:"limit"`
	Offset    int    `json:"offset" form:"offset"`
	Search    string `json:"search,omitempty" form:"search"`
	IsActive  *bool  `json:"is_active,omitempty" form:"is_active"`
	SortBy    string `json:"sort_by,omitempty" form:"sort_by"`
	SortOrder string `json:"sort_order,omitempty" form:"sort_order"`
}

// ListLinksResponse represents the response for listing links
type ListLinksResponse struct {
	Links      []Link     `json:"links"`
	Pagination Pagination `json:"pagination"`
}

// Pagination represents pagination metadata
type Pagination struct {
	Total  int `json:"total"`
	Limit  int `json:"limit"`
	Offset int `json:"offset"`
	Page   int `json:"page"`
}

// HealthResponse represents health check response
type HealthResponse struct {
	Status    string `json:"status"`
	Service   string `json:"service"`
	Timestamp string `json:"timestamp"`
	Version   string `json:"version,omitempty"`
}