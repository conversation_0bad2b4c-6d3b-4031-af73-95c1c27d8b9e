package metrics

import (
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"github.com/prometheus/client_golang/prometheus/promhttp"
)

var (
	// HTTP metrics
	RequestsTotal = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "http_requests_total",
			Help: "Total number of HTTP requests",
		},
		[]string{"method", "endpoint", "status"},
	)

	RequestDuration = promauto.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "http_request_duration_seconds",
			Help:    "Duration of HTTP requests",
			Buckets: []float64{0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1, 2.5, 5, 10},
		},
		[]string{"method", "endpoint"},
	)

	// Link tracking specific metrics
	LinksCreatedTotal = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "links_created_total",
			Help: "Total number of links created",
		},
		[]string{"tenant_id"},
	)

	ClicksTotal = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "clicks_total",
			Help: "Total number of link clicks",
		},
		[]string{"domain"},
	)

	RedirectLatency = promauto.NewHistogram(
		prometheus.HistogramOpts{
			Name:    "redirect_latency_seconds",
			Help:    "Latency of redirect operations",
			Buckets: []float64{0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1},
		},
	)

	CacheHitTotal = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "cache_hits_total",
			Help: "Total number of cache hits",
		},
		[]string{"operation"},
	)

	CacheMissTotal = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "cache_misses_total",
			Help: "Total number of cache misses",
		},
		[]string{"operation"},
	)

	DatabaseOperationsTotal = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "database_operations_total",
			Help: "Total number of database operations",
		},
		[]string{"operation", "status"},
	)

	DatabaseOperationDuration = promauto.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "database_operation_duration_seconds",
			Help:    "Duration of database operations",
			Buckets: prometheus.DefBuckets,
		},
		[]string{"operation"},
	)
)

// Init initializes the metrics
func Init() {
	// promauto already registers metrics automatically
	// No need to manually register Go metrics as they are registered by default
}

// Handler returns the Prometheus metrics handler
func Handler() gin.HandlerFunc {
	return gin.WrapH(promhttp.Handler())
}

// PrometheusMiddleware records HTTP metrics
func PrometheusMiddleware() gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		start := time.Now()

		// Process request
		c.Next()

		// Record metrics
		duration := time.Since(start).Seconds()
		status := strconv.Itoa(c.Writer.Status())

		RequestsTotal.WithLabelValues(
			c.Request.Method,
			c.FullPath(),
			status,
		).Inc()

		RequestDuration.WithLabelValues(
			c.Request.Method,
			c.FullPath(),
		).Observe(duration)
	})
}

// RecordLinkCreated increments the links created counter
func RecordLinkCreated(tenantID string) {
	LinksCreatedTotal.WithLabelValues(tenantID).Inc()
}

// RecordClick increments the clicks counter
func RecordClick(domain string) {
	ClicksTotal.WithLabelValues(domain).Inc()
}

// RecordRedirectLatency records redirect latency
func RecordRedirectLatency(latency time.Duration) {
	RedirectLatency.Observe(latency.Seconds())
}

// RecordCacheHit increments cache hit counter
func RecordCacheHit(operation string) {
	CacheHitTotal.WithLabelValues(operation).Inc()
}

// RecordCacheMiss increments cache miss counter
func RecordCacheMiss(operation string) {
	CacheMissTotal.WithLabelValues(operation).Inc()
}

// RecordDatabaseOperation records database operation metrics
func RecordDatabaseOperation(operation, status string, duration time.Duration) {
	DatabaseOperationsTotal.WithLabelValues(operation, status).Inc()
	DatabaseOperationDuration.WithLabelValues(operation).Observe(duration.Seconds())
}