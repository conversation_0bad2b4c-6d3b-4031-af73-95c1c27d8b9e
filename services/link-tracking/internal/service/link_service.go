package service

import (
	"context"
	"crypto/rand"
	"fmt"
	"math/big"
	"net"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"

	"link-tracking/internal/metrics"
	"link-tracking/internal/models"
	"link-tracking/internal/repository"
)

const (
	// Default cache TTL for links
	defaultLinkCacheTTL = 24 * time.Hour
	// Character set for generating short codes
	charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	// Default short code length
	defaultCodeLength = 8
)

type LinkService struct {
	linkRepo   *repository.LinkRepository
	clickRepo  *repository.ClickRepository
	cache      *CacheService
}

// NewLinkService creates a new link service
func NewLinkService(linkRepo *repository.LinkRepository, clickRepo *repository.ClickRepository, cache *CacheService) *LinkService {
	return &LinkService{
		linkRepo:  linkRepo,
		clickRepo: clickRepo,
		cache:     cache,
	}
}

// CreateLink creates a new branded link
func (s *LinkService) CreateLink(ctx context.Context, req *models.CreateLinkRequest) (*models.Link, error) {
	// Generate unique short code
	shortCode, err := s.generateUniqueShortCode(ctx, req.CustomCode)
	if err != nil {
		return nil, fmt.Errorf("failed to generate short code: %w", err)
	}

	// Create the link object
	link := &models.Link{
		ID:          uuid.New(),
		TenantID:    req.TenantID,
		CampaignID:  req.CampaignID,
		ShortCode:   shortCode,
		TargetURL:   req.TargetURL,
		Title:       req.Title,
		Description: req.Description,
		Domain:      req.Domain,
		UTMSource:   req.UTMSource,
		UTMMedium:   req.UTMMedium,
		UTMCampaign: req.UTMCampaign,
		UTMTerm:     req.UTMTerm,
		UTMContent:  req.UTMContent,
		IsActive:    true,
		ExpiresAt:   req.ExpiresAt,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	// Save to database
	if err := s.linkRepo.Create(ctx, link); err != nil {
		return nil, fmt.Errorf("failed to create link: %w", err)
	}

	// Cache the link
	if err := s.cache.SetLink(ctx, link, defaultLinkCacheTTL); err != nil {
		logrus.WithError(err).Warn("Failed to cache link")
	}

	// Record metrics
	metrics.RecordLinkCreated(link.TenantID.String())

	logrus.WithFields(logrus.Fields{
		"link_id":    link.ID,
		"short_code": link.ShortCode,
		"tenant_id":  link.TenantID,
	}).Info("Link created successfully")

	return link, nil
}

// GetLink retrieves a link by ID
func (s *LinkService) GetLink(ctx context.Context, id uuid.UUID) (*models.Link, error) {
	link, err := s.linkRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get link: %w", err)
	}

	if link == nil {
		return nil, fmt.Errorf("link not found")
	}

	return link, nil
}

// ListLinks retrieves paginated links for a tenant
func (s *LinkService) ListLinks(ctx context.Context, tenantID uuid.UUID, params *models.ListLinksParams) (*models.ListLinksResponse, error) {
	links, total, err := s.linkRepo.List(ctx, tenantID, params)
	if err != nil {
		return nil, fmt.Errorf("failed to list links: %w", err)
	}

	response := &models.ListLinksResponse{
		Links: links,
		Pagination: models.Pagination{
			Total:  total,
			Limit:  params.Limit,
			Offset: params.Offset,
			Page:   (params.Offset / params.Limit) + 1,
		},
	}

	return response, nil
}

// GetLinkByShortCode retrieves a link by short code (with caching)
func (s *LinkService) GetLinkByShortCode(ctx context.Context, shortCode string) (*models.Link, error) {
	// Try cache first
	link, err := s.cache.GetLink(ctx, shortCode)
	if err == nil && link != nil {
		return link, nil
	}

	// Cache miss, get from database
	link, err = s.linkRepo.GetByShortCode(ctx, shortCode)
	if err != nil {
		return nil, fmt.Errorf("failed to get link by short code: %w", err)
	}

	if link == nil {
		return nil, nil
	}

	// Cache the result
	if err := s.cache.SetLink(ctx, link, defaultLinkCacheTTL); err != nil {
		logrus.WithError(err).Warn("Failed to cache link")
	}

	return link, nil
}

// UpdateLink updates a link
func (s *LinkService) UpdateLink(ctx context.Context, id uuid.UUID, req *models.UpdateLinkRequest) (*models.Link, error) {
	// Get the existing link first
	existingLink, err := s.linkRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get existing link: %w", err)
	}

	if existingLink == nil {
		return nil, fmt.Errorf("link not found")
	}

	// Update the link
	if err := s.linkRepo.Update(ctx, id, req); err != nil {
		return nil, fmt.Errorf("failed to update link: %w", err)
	}

	// Invalidate cache
	if err := s.cache.DeleteLink(ctx, existingLink.ShortCode); err != nil {
		logrus.WithError(err).Warn("Failed to invalidate link cache")
	}

	// Get the updated link
	updatedLink, err := s.linkRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get updated link: %w", err)
	}

	return updatedLink, nil
}

// DeleteLink soft deletes a link
func (s *LinkService) DeleteLink(ctx context.Context, id uuid.UUID) error {
	// Get the existing link first
	existingLink, err := s.linkRepo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to get existing link: %w", err)
	}

	if existingLink == nil {
		return fmt.Errorf("link not found")
	}

	// Delete the link
	if err := s.linkRepo.Delete(ctx, id); err != nil {
		return fmt.Errorf("failed to delete link: %w", err)
	}

	// Invalidate cache
	if err := s.cache.DeleteLink(ctx, existingLink.ShortCode); err != nil {
		logrus.WithError(err).Warn("Failed to invalidate link cache")
	}

	return nil
}

// HandleClick handles a click on a branded link
func (s *LinkService) HandleClick(ctx context.Context, shortCode string, ipAddress, userAgent, referrer string) (*models.Link, error) {
	startTime := time.Now()

	// Get the link
	link, err := s.GetLinkByShortCode(ctx, shortCode)
	if err != nil {
		return nil, fmt.Errorf("failed to get link: %w", err)
	}

	if link == nil {
		return nil, fmt.Errorf("link not found")
	}

	// Check if link is expired
	if link.ExpiresAt != nil && link.ExpiresAt.Before(time.Now()) {
		return nil, fmt.Errorf("link has expired")
	}

	// Create click record asynchronously
	go func() {
		// Use a fresh context with timeout for the async operation
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()
		s.recordClick(ctx, link, ipAddress, userAgent, referrer)
	}()

	// Record metrics
	domain := "default"
	if link.Domain != nil {
		domain = *link.Domain
	}
	metrics.RecordClick(domain)
	metrics.RecordRedirectLatency(time.Since(startTime))

	return link, nil
}

// recordClick creates a click record (called asynchronously)
func (s *LinkService) recordClick(ctx context.Context, link *models.Link, ipAddress, userAgent, referrer string) {
	// Generate tracking ID for attribution
	trackingID := s.generateTrackingID()

	// Parse user agent and IP for additional info
	deviceType, browser, os := s.parseUserAgent(userAgent)
	country, city := s.parseIPAddress(ipAddress)

	click := &models.Click{
		ID:         uuid.New(),
		LinkID:     link.ID,
		IPAddress:  &ipAddress,
		UserAgent:  &userAgent,
		Referrer:   &referrer,
		Country:    &country,
		City:       &city,
		DeviceType: &deviceType,
		Browser:    &browser,
		OS:         &os,
		ClickedAt:  time.Now(),
		TrackingID: &trackingID,
	}

	if err := s.clickRepo.Create(ctx, click); err != nil {
		logrus.WithError(err).Error("Failed to record click")
		return
	}

	// Increment cached click count
	if err := s.cache.IncrementClickCount(ctx, link.ID.String()); err != nil {
		logrus.WithError(err).Warn("Failed to increment cached click count")
	}

	logrus.WithFields(logrus.Fields{
		"click_id":    click.ID,
		"link_id":     click.LinkID,
		"tracking_id": click.TrackingID,
		"country":     country,
		"device_type": deviceType,
	}).Info("Click recorded successfully")
}

// GetLinkAnalytics retrieves analytics for a link
func (s *LinkService) GetLinkAnalytics(ctx context.Context, linkID uuid.UUID) (*models.LinkAnalytics, error) {
	analytics, err := s.clickRepo.GetAnalytics(ctx, linkID)
	if err != nil {
		return nil, fmt.Errorf("failed to get analytics: %w", err)
	}

	return analytics, nil
}

// generateUniqueShortCode generates a unique short code
func (s *LinkService) generateUniqueShortCode(ctx context.Context, customCode *string) (string, error) {
	// If custom code is provided, check if it's available
	if customCode != nil && *customCode != "" {
		exists, err := s.linkRepo.IsShortCodeExists(ctx, *customCode)
		if err != nil {
			return "", err
		}
		if exists {
			return "", fmt.Errorf("custom code already exists")
		}
		return *customCode, nil
	}

	// Generate random code
	maxAttempts := 10
	for i := 0; i < maxAttempts; i++ {
		code := s.generateRandomCode(defaultCodeLength)
		exists, err := s.linkRepo.IsShortCodeExists(ctx, code)
		if err != nil {
			return "", err
		}
		if !exists {
			return code, nil
		}
	}

	return "", fmt.Errorf("failed to generate unique short code after %d attempts", maxAttempts)
}

// generateRandomCode generates a random string of specified length
func (s *LinkService) generateRandomCode(length int) string {
	result := make([]byte, length)
	for i := range result {
		num, _ := rand.Int(rand.Reader, big.NewInt(int64(len(charset))))
		result[i] = charset[num.Int64()]
	}
	return string(result)
}

// generateTrackingID generates a unique tracking ID for attribution
func (s *LinkService) generateTrackingID() string {
	return fmt.Sprintf("trk_%d_%s", time.Now().Unix(), s.generateRandomCode(8))
}

// parseUserAgent extracts device type, browser, and OS from user agent
func (s *LinkService) parseUserAgent(userAgent string) (deviceType, browser, os string) {
	ua := strings.ToLower(userAgent)

	// Simple device type detection
	if strings.Contains(ua, "mobile") || strings.Contains(ua, "android") || strings.Contains(ua, "iphone") {
		deviceType = "mobile"
	} else if strings.Contains(ua, "tablet") || strings.Contains(ua, "ipad") {
		deviceType = "tablet"
	} else {
		deviceType = "desktop"
	}

	// Simple browser detection
	if strings.Contains(ua, "chrome") {
		browser = "Chrome"
	} else if strings.Contains(ua, "firefox") {
		browser = "Firefox"
	} else if strings.Contains(ua, "safari") && !strings.Contains(ua, "chrome") {
		browser = "Safari"
	} else if strings.Contains(ua, "edge") {
		browser = "Edge"
	} else {
		browser = "Other"
	}

	// Simple OS detection
	if strings.Contains(ua, "windows") {
		os = "Windows"
	} else if strings.Contains(ua, "mac") {
		os = "macOS"
	} else if strings.Contains(ua, "linux") {
		os = "Linux"
	} else if strings.Contains(ua, "android") {
		os = "Android"
	} else if strings.Contains(ua, "ios") || strings.Contains(ua, "iphone") || strings.Contains(ua, "ipad") {
		os = "iOS"
	} else {
		os = "Other"
	}

	return deviceType, browser, os
}

// parseIPAddress extracts country and city from IP address (simplified)
func (s *LinkService) parseIPAddress(ipAddress string) (country, city string) {
	// Parse the IP
	ip := net.ParseIP(ipAddress)
	if ip == nil {
		return "Unknown", "Unknown"
	}

	// For now, just return basic geo info
	// In production, you'd use a proper IP geolocation service
	if ip.IsLoopback() || ip.IsPrivate() {
		return "Local", "Local"
	}

	// This is a placeholder - implement proper geolocation
	return "Unknown", "Unknown"
}