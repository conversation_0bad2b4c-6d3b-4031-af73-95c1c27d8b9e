package service

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"link-tracking/internal/metrics"
	"link-tracking/internal/models"
	"link-tracking/internal/redis"
)

type CacheService struct {
	redis *redis.Client
}

// NewCacheService creates a new cache service
func NewCacheService(redisClient *redis.Client) *CacheService {
	return &CacheService{redis: redisClient}
}

// GetLink retrieves a link from cache
func (s *CacheService) GetLink(ctx context.Context, shortCode string) (*models.Link, error) {
	key := fmt.Sprintf("link:%s", shortCode)
	
	data, err := s.redis.Get(ctx, key)
	if err != nil {
		metrics.RecordCacheMiss("get_link")
		return nil, err
	}

	var link models.Link
	if err := json.Unmarshal([]byte(data), &link); err != nil {
		return nil, fmt.Errorf("failed to unmarshal link: %w", err)
	}

	metrics.RecordCacheHit("get_link")
	return &link, nil
}

// SetLink stores a link in cache
func (s *CacheService) SetLink(ctx context.Context, link *models.Link, expiration time.Duration) error {
	key := fmt.Sprintf("link:%s", link.ShortCode)
	
	data, err := json.Marshal(link)
	if err != nil {
		return fmt.Errorf("failed to marshal link: %w", err)
	}

	return s.redis.Set(ctx, key, string(data), expiration)
}

// DeleteLink removes a link from cache
func (s *CacheService) DeleteLink(ctx context.Context, shortCode string) error {
	key := fmt.Sprintf("link:%s", shortCode)
	return s.redis.Delete(ctx, key)
}

// IncrementClickCount increments the click count for a link
func (s *CacheService) IncrementClickCount(ctx context.Context, linkID string) error {
	key := fmt.Sprintf("clicks:%s", linkID)
	// Use Redis INCR command through the client
	return s.redis.Client.Incr(ctx, key).Err()
}

// GetClickCount retrieves the cached click count for a link
func (s *CacheService) GetClickCount(ctx context.Context, linkID string) (int64, error) {
	key := fmt.Sprintf("clicks:%s", linkID)
	return s.redis.Client.Get(ctx, key).Int64()
}

// SetClickCount sets the click count for a link
func (s *CacheService) SetClickCount(ctx context.Context, linkID string, count int64, expiration time.Duration) error {
	key := fmt.Sprintf("clicks:%s", linkID)
	return s.redis.Set(ctx, key, count, expiration)
}

// Health checks the cache service health
func (s *CacheService) Health() error {
	return s.redis.Health()
}