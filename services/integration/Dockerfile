# Multi-stage build for Integration Service
FROM node:18-alpine AS base

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apk add --no-cache \
    dumb-init \
    curl \
    && rm -rf /var/cache/apk/*

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S integration -u 1001

# Development stage
FROM base AS development

# Install development dependencies
RUN apk add --no-cache git

# Copy package files
COPY package*.json ./

# Install all dependencies
RUN npm ci --include=dev

# Copy source code
COPY . .

# Change ownership to non-root user
RUN chown -R integration:nodejs /app

USER integration

# Expose port
EXPOSE 3003

# Start development server
CMD ["dumb-init", "npm", "run", "dev"]

# Production stage
FROM base AS production

# Set production environment
ENV NODE_ENV=production

# Copy package files
COPY package*.json ./

# Install production dependencies only
RUN npm ci --only=production --no-audit --no-fund

# Copy source code
COPY . .

# Create necessary directories
RUN mkdir -p logs temp && \
    chown -R integration:nodejs /app

USER integration

# Expose port
EXPOSE 3001

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:3001/health || exit 1

# Start the application
CMD ["dumb-init", "node", "src/index.js"]

# Default target is production
FROM production