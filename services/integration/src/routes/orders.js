const express = require('express');
const router = express.Router();
const logger = require('../utils/logger');
const { validateOrderFilter } = require('../middleware/validation');

// GET /orders - List orders with filtering
router.get('/', validateOrderFilter, async (req, res, next) => {
  try {
    const {
      platform,
      status,
      date_from,
      date_to,
      limit,
      offset,
    } = req.query;

    logger.debug('Fetching orders', {
      platform,
      status,
      date_from,
      date_to,
      limit,
      offset,
    });

    // TODO: Implement order retrieval logic
    // 1. Build query based on filters
    // 2. Execute database query
    // 3. Return paginated results

    const mockOrders = [
      {
        id: '12345',
        platform: 'shopify',
        platform_order_id: 'sp_123456',
        customer_email: '<EMAIL>',
        total_amount: 99.99,
        currency: 'USD',
        status: 'completed',
        tracking_id: 'trk_1234567890_abc',
        created_at: '2025-06-16T10:30:00Z',
        items: [
          {
            product_id: 'prod_123',
            sku: 'TEST-SKU-001',
            name: 'Test Product',
            quantity: 2,
            price: 49.99,
          },
        ],
      },
    ];

    res.json({
      success: true,
      data: {
        orders: mockOrders,
        pagination: {
          limit,
          offset,
          total: mockOrders.length,
          has_more: false,
        },
        filters_applied: {
          platform,
          status,
          date_from,
          date_to,
        },
      },
    });
  } catch (error) {
    next(error);
  }
});

// GET /orders/:id - Get specific order
router.get('/:id', async (req, res, next) => {
  try {
    const { id } = req.params;

    logger.debug('Fetching order by ID', { id });

    // TODO: Implement order retrieval by ID
    res.json({
      success: true,
      data: null,
      message: 'Order not found',
    });
  } catch (error) {
    next(error);
  }
});

// GET /orders/tracking/:trackingId - Get order by tracking ID
router.get('/tracking/:trackingId', async (req, res, next) => {
  try {
    const { trackingId } = req.params;

    logger.debug('Fetching order by tracking ID', { trackingId });

    // TODO: Implement order retrieval by tracking ID
    // This is important for attribution matching
    res.json({
      success: true,
      data: null,
      message: 'Order not found for tracking ID',
    });
  } catch (error) {
    next(error);
  }
});

// GET /orders/stats - Get order statistics
router.get('/stats', async (req, res, next) => {
  try {
    const { platform, date_from, date_to, tenant_id } = req.query;

    if (!tenant_id) {
      return res.status(400).json({
        success: false,
        error: 'tenant_id is required'
      });
    }

    logger.debug('Fetching order statistics', { platform, date_from, date_to, tenant_id });

    const { query } = require('../database');

    // Build WHERE conditions
    const conditions = ['o.tenant_id = $1'];
    const params = [tenant_id];
    let paramIndex = 2;

    if (date_from) {
      conditions.push(`o.created_at >= $${paramIndex}`);
      params.push(date_from);
      paramIndex++;
    }

    if (date_to) {
      conditions.push(`o.created_at <= $${paramIndex}`);
      params.push(date_to);
      paramIndex++;
    }

    if (platform) {
      conditions.push(`i.platform = $${paramIndex}`);
      params.push(platform);
      paramIndex++;
    }

    const whereClause = `WHERE ${conditions.join(' AND ')}`;

    // Get overall statistics
    const overallStatsQuery = `
      SELECT
        COUNT(*) as total_orders,
        COALESCE(SUM(o.total_amount), 0) as total_revenue,
        COALESCE(AVG(o.total_amount), 0) as average_order_value
      FROM orders o
      LEFT JOIN integrations i ON o.integration_id = i.id
      ${whereClause}
    `;

    // Get orders by platform
    const platformStatsQuery = `
      SELECT
        i.platform,
        COUNT(*) as order_count
      FROM orders o
      LEFT JOIN integrations i ON o.integration_id = i.id
      ${whereClause}
      GROUP BY i.platform
    `;

    // Get orders by status
    const statusStatsQuery = `
      SELECT
        o.status,
        COUNT(*) as order_count
      FROM orders o
      LEFT JOIN integrations i ON o.integration_id = i.id
      ${whereClause}
      GROUP BY o.status
    `;

    // Get attribution statistics
    const attributionStatsQuery = `
      SELECT
        COUNT(CASE WHEN o.tracking_id IS NOT NULL THEN 1 END) as with_tracking,
        COUNT(CASE WHEN o.tracking_id IS NULL THEN 1 END) as without_tracking,
        COUNT(*) as total_orders
      FROM orders o
      LEFT JOIN integrations i ON o.integration_id = i.id
      ${whereClause}
    `;

    // Execute all queries in parallel
    const [overallResult, platformResult, statusResult, attributionResult] = await Promise.all([
      query(overallStatsQuery, params),
      query(platformStatsQuery, params),
      query(statusStatsQuery, params),
      query(attributionStatsQuery, params)
    ]);

    const overall = overallResult.rows[0];
    const platformStats = platformResult.rows.reduce((acc, row) => {
      acc[row.platform || 'unknown'] = parseInt(row.order_count);
      return acc;
    }, {});
    const statusStats = statusResult.rows.reduce((acc, row) => {
      acc[row.status || 'unknown'] = parseInt(row.order_count);
      return acc;
    }, {});
    const attribution = attributionResult.rows[0];

    const stats = {
      total_orders: parseInt(overall.total_orders) || 0,
      total_revenue: parseFloat(overall.total_revenue) || 0,
      average_order_value: parseFloat(overall.average_order_value) || 0,
      orders_by_platform: platformStats,
      orders_by_status: statusStats,
      conversion_attribution: {
        with_tracking: parseInt(attribution.with_tracking) || 0,
        without_tracking: parseInt(attribution.without_tracking) || 0,
        attribution_rate: attribution.total_orders > 0
          ? ((attribution.with_tracking / attribution.total_orders) * 100).toFixed(1)
          : 0
      },
    };

    res.json({
      success: true,
      data: stats,
    });
  } catch (error) {
    next(error);
  }
});

// POST /orders/attribution - Manual attribution linking
router.post('/attribution', async (req, res, next) => {
  try {
    const { order_id, tracking_id, attribution_method } = req.body;

    logger.info('Manual attribution linking', {
      order_id,
      tracking_id,
      attribution_method,
    });

    // TODO: Implement manual attribution logic
    // 1. Validate order and tracking ID exist
    // 2. Create attribution record
    // 3. Trigger analytics update

    res.json({
      success: true,
      message: 'Attribution linked successfully',
      data: {
        order_id,
        tracking_id,
        attribution_method,
        linked_at: new Date().toISOString(),
      },
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;