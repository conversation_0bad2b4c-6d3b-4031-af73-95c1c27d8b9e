const express = require('express');
const router = express.Router();
const crypto = require('crypto');
const logger = require('../utils/logger');
const { recordWebhook } = require('../metrics');
const { pool } = require('../database');
const ShopifyOrderProcessor = require('../platforms/shopify/orderProcessor');

// Shopify webhook signature verification
function verifyShopifySignature(req, res, next) {
  try {
    const signature = req.get('X-Shopify-Hmac-Sha256');
    const body = JSON.stringify(req.body);
    const webhookSecret = process.env.SHOPIFY_WEBHOOK_SECRET;
    
    if (!signature || !webhookSecret) {
      logger.warn('Missing Shopify webhook signature or secret', {
        hasSignature: !!signature,
        hasSecret: !!webhookSecret
      });
      return res.status(401).json({
        success: false,
        error: 'Missing webhook signature or secret',
      });
    }
    
    const computedSignature = crypto
      .createHmac('sha256', webhookSecret)
      .update(body, 'utf8')
      .digest('base64');
    
    if (!crypto.timingSafeEqual(Buffer.from(signature), Buffer.from(computedSignature))) {
      logger.error('Invalid Shopify webhook signature', {
        provided: signature,
        computed: computedSignature
      });
      return res.status(401).json({
        success: false,
        error: 'Invalid webhook signature',
      });
    }
    
    logger.debug('Shopify webhook signature verified successfully');
    next();
  } catch (error) {
    logger.error('Shopify webhook signature verification failed', error);
    res.status(401).json({
      success: false,
      error: 'Webhook signature verification failed',
    });
  }
}

// WooCommerce webhook signature verification
function verifyWooCommerceSignature(req, res, next) {
  try {
    const signature = req.get('X-WC-Webhook-Signature');
    const body = JSON.stringify(req.body);
    const webhookSecret = process.env.WOOCOMMERCE_WEBHOOK_SECRET;
    
    if (!signature || !webhookSecret) {
      logger.warn('Missing WooCommerce webhook signature or secret');
      return res.status(401).json({
        success: false,
        error: 'Missing webhook signature or secret',
      });
    }
    
    const computedSignature = crypto
      .createHmac('sha256', webhookSecret)
      .update(body, 'utf8')
      .digest('base64');
    
    if (signature !== computedSignature) {
      logger.error('Invalid WooCommerce webhook signature');
      return res.status(401).json({
        success: false,
        error: 'Invalid webhook signature',
      });
    }
    
    logger.debug('WooCommerce webhook signature verified successfully');
    next();
  } catch (error) {
    logger.error('WooCommerce webhook signature verification failed', error);
    res.status(401).json({
      success: false,
      error: 'Webhook signature verification failed',
    });
  }
}

// Shopify webhooks
router.post('/shopify/orders/create', verifyShopifySignature, async (req, res, next) => {
  try {
    const order = req.body;
    
    logger.info('Received Shopify order webhook', { 
      orderId: order.id, 
      orderNumber: order.order_number,
      totalPrice: order.total_price,
      customerEmail: order.email
    });

    recordWebhook('shopify', 'order_create', 'received');

    const processor = new ShopifyOrderProcessor();
    const result = await processor.processOrder(order, 'create');
    
    if (result.success) {
      recordWebhook('shopify', 'order_create', 'processed');
      logger.info('Shopify order processed successfully', {
        orderId: order.id,
        trackingId: result.trackingId,
        attributionFound: result.attributionFound
      });
    } else {
      recordWebhook('shopify', 'order_create', 'error');
      logger.error('Failed to process Shopify order', {
        orderId: order.id,
        error: result.error
      });
    }

    res.status(200).json({ success: true, processed: result.success });
  } catch (error) {
    recordWebhook('shopify', 'order_create', 'error');
    logger.error('Shopify order webhook processing failed', error);
    res.status(200).json({ success: true }); // Always return 200 to Shopify
  }
});

router.post('/shopify/orders/update', verifyShopifySignature, async (req, res, next) => {
  try {
    const order = req.body;
    
    logger.info('Received Shopify order update webhook', { 
      orderId: order.id, 
      fulfillmentStatus: order.fulfillment_status,
      financialStatus: order.financial_status
    });

    recordWebhook('shopify', 'order_update', 'received');

    const processor = new ShopifyOrderProcessor();
    const result = await processor.processOrder(order, 'update');
    
    if (result.success) {
      recordWebhook('shopify', 'order_update', 'processed');
      logger.info('Shopify order update processed successfully', {
        orderId: order.id,
        changes: result.changes
      });
    } else {
      recordWebhook('shopify', 'order_update', 'error');
      logger.error('Failed to process Shopify order update', {
        orderId: order.id,
        error: result.error
      });
    }

    res.status(200).json({ success: true, processed: result.success });
  } catch (error) {
    recordWebhook('shopify', 'order_update', 'error');
    logger.error('Shopify order update webhook processing failed', error);
    res.status(200).json({ success: true });
  }
});

router.post('/shopify/orders/delete', verifyShopifySignature, async (req, res, next) => {
  try {
    const order = req.body;
    
    logger.info('Received Shopify order delete webhook', { orderId: order.id });

    recordWebhook('shopify', 'order_delete', 'received');

    const processor = new ShopifyOrderProcessor();
    const result = await processor.processOrder(order, 'delete');
    
    if (result.success) {
      recordWebhook('shopify', 'order_delete', 'processed');
      logger.info('Shopify order deletion processed successfully', {
        orderId: order.id
      });
    } else {
      recordWebhook('shopify', 'order_delete', 'error');
      logger.error('Failed to process Shopify order deletion', {
        orderId: order.id,
        error: result.error
      });
    }

    res.status(200).json({ success: true, processed: result.success });
  } catch (error) {
    recordWebhook('shopify', 'order_delete', 'error');
    logger.error('Shopify order delete webhook processing failed', error);
    res.status(200).json({ success: true });
  }
});

// WooCommerce webhooks
const WooCommerceOrderProcessor = require('../platforms/woocommerce/orderProcessor');
const wooCommerceProcessor = new WooCommerceOrderProcessor();

router.post('/woocommerce/orders/create', verifyWooCommerceSignature, async (req, res, next) => {
  try {
    const order = req.body;

    logger.info('Received WooCommerce order webhook', {
      orderId: order.id,
      status: order.status,
      total: order.total,
      customerEmail: order.billing?.email
    });

    recordWebhook('woocommerce', 'order_create', 'received');

    // Find integration for this webhook
    const integration = await findIntegrationByWebhook(req, 'woocommerce');
    if (!integration) {
      logger.error('No WooCommerce integration found for webhook');
      return res.status(200).json({ success: true }); // Return 200 to avoid retries
    }

    // Process the order
    await wooCommerceProcessor.processOrder(order, integration);
    recordWebhook('woocommerce', 'order_create', 'processed');

    res.status(200).json({ success: true });
  } catch (error) {
    recordWebhook('woocommerce', 'order_create', 'error');
    logger.error('WooCommerce order webhook processing failed', error);
    res.status(200).json({ success: true });
  }
});

router.post('/woocommerce/orders/update', verifyWooCommerceSignature, async (req, res, next) => {
  try {
    const order = req.body;

    logger.info('Received WooCommerce order update webhook', {
      orderId: order.id,
      status: order.status,
      dateModified: order.date_modified
    });

    recordWebhook('woocommerce', 'order_update', 'received');

    // Find integration for this webhook
    const integration = await findIntegrationByWebhook(req, 'woocommerce');
    if (!integration) {
      logger.error('No WooCommerce integration found for webhook');
      return res.status(200).json({ success: true });
    }

    // Process the order update
    await wooCommerceProcessor.processOrder(order, integration);
    recordWebhook('woocommerce', 'order_update', 'processed');

    res.status(200).json({ success: true });
  } catch (error) {
    recordWebhook('woocommerce', 'order_update', 'error');
    logger.error('WooCommerce order update webhook processing failed', error);
    res.status(200).json({ success: true });
  }
});

// Amazon webhooks (if applicable)
router.post('/amazon/orders/update', async (req, res, next) => {
  try {
    const notification = req.body;
    
    logger.info('Received Amazon notification', { 
      notificationType: notification.notificationType 
    });

    recordWebhook('amazon', 'order_update', 'received');

    // TODO: Process Amazon notification
    recordWebhook('amazon', 'order_update', 'processed');

    res.status(200).json({ success: true });
  } catch (error) {
    recordWebhook('amazon', 'order_update', 'error');
    next(error);
  }
});

// Helper function to find integration by webhook
async function findIntegrationByWebhook(req, platform) {
  try {
    // Extract store identifier from request
    let storeIdentifier = null;

    if (platform === 'shopify') {
      storeIdentifier = req.get('X-Shopify-Shop-Domain');
    } else if (platform === 'woocommerce') {
      // For WooCommerce, we might need to extract from webhook URL or headers
      // This could be configured per integration
      const origin = req.get('origin') || req.get('referer');
      if (origin) {
        storeIdentifier = new URL(origin).hostname;
      }
    }

    if (!storeIdentifier) {
      logger.warn('Could not extract store identifier from webhook', { platform });
      return null;
    }

    const query = `
      SELECT id, tenant_id, platform, store_url, api_credentials, webhook_secret
      FROM integrations
      WHERE platform = $1
        AND is_active = true
        AND (store_url LIKE $2 OR store_url LIKE $3)
      LIMIT 1
    `;

    const result = await pool.query(query, [
      platform,
      `%${storeIdentifier}%`,
      `%${storeIdentifier.replace('www.', '')}%`
    ]);

    return result.rows[0] || null;
  } catch (error) {
    logger.error('Failed to find integration by webhook', {
      platform,
      error: error.message
    });
    return null;
  }
}

// Generic webhook endpoint for testing
router.post('/test', async (req, res, next) => {
  try {
    const payload = req.body;

    logger.info('Received test webhook', { payload });

    res.status(200).json({
      success: true,
      message: 'Test webhook received successfully',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;