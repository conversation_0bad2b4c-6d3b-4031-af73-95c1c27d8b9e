const express = require('express');
const router = express.Router();
const logger = require('../utils/logger');
const { validateCreateIntegration, validateUpdateIntegration } = require('../middleware/validation');
const { query, transaction } = require('../database');
const crypto = require('crypto');
const IntegrationManager = require('../services/integrationManager');

const integrationManager = new IntegrationManager();

// GET /integrations - List all integrations
router.get('/', async (req, res, next) => {
  try {
    const { tenant_id, limit = 20, offset = 0 } = req.query;
    
    if (!tenant_id) {
      return res.status(400).json({
        success: false,
        error: 'tenant_id is required',
      });
    }

    // Get total count
    const countResult = await query(
      'SELECT COUNT(*) FROM integrations WHERE tenant_id = $1',
      [tenant_id]
    );
    const total = parseInt(countResult.rows[0].count);

    // Get paginated integrations
    const result = await query(`
      SELECT id, tenant_id, platform, store_name, store_url, 
             is_active, last_sync_at, created_at, updated_at
      FROM integrations 
      WHERE tenant_id = $1 
      ORDER BY created_at DESC 
      LIMIT $2 OFFSET $3
    `, [tenant_id, limit, offset]);

    logger.info('Retrieved integrations', { 
      tenant_id, 
      count: result.rows.length,
      total 
    });

    res.json({
      success: true,
      data: result.rows,
      pagination: {
        total,
        limit: parseInt(limit),
        offset: parseInt(offset),
        page: Math.floor(offset / limit) + 1,
      },
      message: 'Integrations retrieved successfully',
    });
  } catch (error) {
    logger.error('Error retrieving integrations:', error);
    next(error);
  }
});

// POST /integrations - Create new integration
router.post('/', validateCreateIntegration, async (req, res, next) => {
  try {
    const { 
      tenant_id, 
      platform, 
      store_name, 
      store_url, 
      api_credentials,
 
    } = req.body;
    
    if (!tenant_id) {
      return res.status(400).json({
        success: false,
        error: 'tenant_id is required',
      });
    }

    logger.info('Creating new integration', { 
      tenant_id, 
      platform, 
      store_name 
    });

    // Generate unique ID and encrypt credentials
    const id = crypto.randomUUID();
    const encryptedCredentials = JSON.stringify(api_credentials); // In production, encrypt this

    const result = await query(`
      INSERT INTO integrations (
        id, tenant_id, platform, store_name, store_url, 
        api_credentials, is_active, created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW())
      RETURNING id, tenant_id, platform, store_name, store_url, 
                is_active, created_at, updated_at
    `, [
      id, tenant_id, platform, store_name, store_url,
      encryptedCredentials, true
    ]);

    const integration = result.rows[0];

    logger.info('Integration created successfully', { 
      id: integration.id,
      tenant_id: integration.tenant_id,
      platform: integration.platform 
    });

    res.status(201).json({
      success: true,
      data: integration,
      message: 'Integration created successfully',
    });
  } catch (error) {
    logger.error('Error creating integration:', error);
    next(error);
  }
});

// GET /integrations/:id - Get specific integration
router.get('/:id', async (req, res, next) => {
  try {
    const { id } = req.params;
    const { tenant_id } = req.query;

    if (!tenant_id) {
      return res.status(400).json({
        success: false,
        error: 'tenant_id is required',
      });
    }

    const result = await query(`
      SELECT id, tenant_id, platform, store_name, store_url, 
             is_active, last_sync_at, created_at, updated_at
      FROM integrations 
      WHERE id = $1 AND tenant_id = $2
    `, [id, tenant_id]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Integration not found',
      });
    }

    const integration = result.rows[0];

    logger.info('Retrieved integration', { 
      id: integration.id,
      tenant_id: integration.tenant_id,
      platform: integration.platform 
    });

    res.json({
      success: true,
      data: integration,
      message: 'Integration retrieved successfully',
    });
  } catch (error) {
    logger.error('Error retrieving integration:', error);
    next(error);
  }
});

// PUT /integrations/:id - Update integration
router.put('/:id', validateUpdateIntegration, async (req, res, next) => {
  try {
    const { id } = req.params;
    const { 
      tenant_id,
      store_name, 
      store_url, 
      api_credentials, 
      is_active 
    } = req.body;

    if (!tenant_id) {
      return res.status(400).json({
        success: false,
        error: 'tenant_id is required',
      });
    }

    logger.info('Updating integration', { id, tenant_id });

    // Build dynamic update query
    const updateFields = [];
    const values = [];
    let paramCount = 1;

    if (store_name !== undefined) {
      updateFields.push(`store_name = $${paramCount++}`);
      values.push(store_name);
    }
    if (store_url !== undefined) {
      updateFields.push(`store_url = $${paramCount++}`);
      values.push(store_url);
    }
    if (api_credentials !== undefined) {
      updateFields.push(`api_credentials = $${paramCount++}`);
      values.push(JSON.stringify(api_credentials));
    }
    if (is_active !== undefined) {
      updateFields.push(`is_active = $${paramCount++}`);
      values.push(is_active);
    }

    if (updateFields.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'No fields to update',
      });
    }

    updateFields.push(`updated_at = NOW()`);
    values.push(id, tenant_id);

    const updateQuery = `
      UPDATE integrations 
      SET ${updateFields.join(', ')} 
      WHERE id = $${paramCount++} AND tenant_id = $${paramCount++}
      RETURNING id, tenant_id, platform, store_name, store_url, 
                is_active, last_sync_at, created_at, updated_at
    `;

    const result = await query(updateQuery, values);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Integration not found',
      });
    }

    const integration = result.rows[0];

    logger.info('Integration updated successfully', { 
      id: integration.id,
      tenant_id: integration.tenant_id 
    });

    res.json({
      success: true,
      data: integration,
      message: 'Integration updated successfully',
    });
  } catch (error) {
    logger.error('Error updating integration:', error);
    next(error);
  }
});

// DELETE /integrations/:id - Delete integration
router.delete('/:id', async (req, res, next) => {
  try {
    const { id } = req.params;
    const { tenant_id } = req.query;

    if (!tenant_id) {
      return res.status(400).json({
        success: false,
        error: 'tenant_id is required',
      });
    }

    logger.info('Deleting integration', { id, tenant_id });

    // Soft delete by setting is_active to false
    const result = await query(`
      UPDATE integrations 
      SET is_active = false, updated_at = NOW() 
      WHERE id = $1 AND tenant_id = $2
      RETURNING id
    `, [id, tenant_id]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Integration not found',
      });
    }

    logger.info('Integration deleted successfully', { id, tenant_id });

    res.json({
      success: true,
      message: 'Integration deleted successfully',
    });
  } catch (error) {
    logger.error('Error deleting integration:', error);
    next(error);
  }
});

// POST /integrations/:id/test - Test integration connection
router.post('/:id/test', async (req, res, next) => {
  try {
    const { id } = req.params;
    const { tenant_id } = req.body;

    if (!tenant_id) {
      return res.status(400).json({
        success: false,
        error: 'tenant_id is required',
      });
    }

    logger.info('Testing integration connection', { id, tenant_id });

    // Get integration details
    const result = await query(`
      SELECT id, platform, store_url, api_credentials, is_active
      FROM integrations 
      WHERE id = $1 AND tenant_id = $2
    `, [id, tenant_id]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Integration not found',
      });
    }

    const integration = result.rows[0];

    if (!integration.is_active) {
      return res.status(400).json({
        success: false,
        error: 'Integration is not active',
      });
    }

    // Use integration manager for actual connection testing
    let connectionStatus = 'connected';
    let testDetails = {};

    try {
      const credentials = JSON.parse(integration.api_credentials);
      const testResult = await integrationManager.validateCredentials(
        integration.platform,
        credentials
      );

      testDetails = {
        platform: integration.platform,
        store_url: integration.store_url,
        api_accessible: testResult,
        test_timestamp: new Date().toISOString()
      };

      // Update last tested timestamp
      await query(`
        UPDATE integrations
        SET last_sync_at = NOW(), updated_at = NOW()
        WHERE id = $1
      `, [id]);

    } catch (testError) {
      logger.error('Connection test failed', { id, error: testError.message });
      connectionStatus = 'failed';
      testDetails = {
        platform: integration.platform,
        error: testError.message,
        test_timestamp: new Date().toISOString()
      };
    }

    logger.info('Integration test completed', { 
      id, 
      tenant_id, 
      status: connectionStatus 
    });

    res.json({
      success: true,
      data: {
        connection_status: connectionStatus,
        test_timestamp: new Date().toISOString(),
        details: testDetails,
      },
      message: 'Integration test completed',
    });
  } catch (error) {
    logger.error('Error testing integration:', error);
    next(error);
  }
});

// POST /integrations/:id/sync - Sync integration data
router.post('/:id/sync', async (req, res, next) => {
  try {
    const { id } = req.params;
    const { tenant_id } = req.body;
    const syncOptions = req.body.options || {};

    if (!tenant_id) {
      return res.status(400).json({
        success: false,
        error: 'tenant_id is required',
      });
    }

    // Verify integration exists and belongs to tenant
    const integrationResult = await query(`
      SELECT id, platform, is_active
      FROM integrations
      WHERE id = $1 AND tenant_id = $2
    `, [id, tenant_id]);

    if (integrationResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Integration not found',
      });
    }

    const integration = integrationResult.rows[0];

    if (!integration.is_active) {
      return res.status(400).json({
        success: false,
        error: 'Integration is not active',
      });
    }

    logger.info('Starting manual sync for integration', {
      integrationId: id,
      platform: integration.platform,
      tenantId: tenant_id
    });

    // Start sync asynchronously
    integrationManager.syncIntegration(id, syncOptions)
      .then(result => {
        logger.info('Manual sync completed', {
          integrationId: id,
          result
        });
      })
      .catch(error => {
        logger.error('Manual sync failed', {
          integrationId: id,
          error: error.message
        });
      });

    res.json({
      success: true,
      message: 'Sync started successfully',
      data: {
        integration_id: id,
        sync_started_at: new Date().toISOString()
      }
    });

  } catch (error) {
    logger.error('Failed to start integration sync', {
      error: error.message,
      integrationId: req.params.id
    });
    next(error);
  }
});

// GET /integrations/platforms/supported - Get supported platforms
router.get('/platforms/supported', async (req, res, next) => {
  try {
    const supportedPlatforms = [
      {
        id: 'shopify',
        name: 'Shopify',
        description: 'Connect your Shopify store for order and customer data',
        features: ['Real-time webhooks', 'Order tracking', 'Customer analytics'],
        requiredFields: ['shop_domain', 'access_token'],
        webhookSupport: true
      },
      {
        id: 'woocommerce',
        name: 'WooCommerce',
        description: 'Connect your WooCommerce store for comprehensive analytics',
        features: ['Order synchronization', 'Product tracking', 'Customer insights'],
        requiredFields: ['store_url', 'consumer_key', 'consumer_secret'],
        webhookSupport: true
      },
      {
        id: 'ebay',
        name: 'eBay',
        description: 'Connect your eBay seller account for sales analytics',
        features: ['Sales reporting', 'Traffic analytics', 'Performance metrics'],
        requiredFields: ['client_id', 'client_secret', 'refresh_token'],
        webhookSupport: false
      }
    ];

    res.json({
      success: true,
      data: supportedPlatforms,
      message: 'Supported platforms retrieved successfully'
    });

  } catch (error) {
    logger.error('Failed to get supported platforms', {
      error: error.message
    });
    next(error);
  }
});

module.exports = router;