const logger = require('../../utils/logger');
const { pool } = require('../../database');
const { v4: uuidv4 } = require('uuid');

class EbayOrderProcessor {
  constructor() {
    this.platform = 'ebay';
  }

  /**
   * Process eBay order
   */
  async processOrder(orderData, integration) {
    try {
      logger.info('Processing eBay order', {
        orderId: orderData.orderId,
        orderStatus: orderData.orderFulfillmentStatus,
        totalAmount: orderData.pricingSummary?.total?.value,
        integrationId: integration.id
      });

      // Normalize the order data
      const normalizedOrder = this.normalizeOrder(orderData, integration);

      // Check if order already exists
      const existingOrder = await this.findExistingOrder(
        integration.tenant_id,
        integration.id,
        orderData.orderId
      );

      if (existingOrder) {
        // Update existing order
        await this.updateOrder(existingOrder.id, normalizedOrder);
        logger.info('Updated existing eBay order', {
          orderId: existingOrder.id,
          platformOrderId: orderData.orderId
        });
      } else {
        // Create new order
        const orderId = await this.createOrder(normalizedOrder);
        logger.info('Created new eBay order', {
          orderId,
          platformOrderId: orderData.orderId
        });

        // Process order line items
        if (orderData.lineItems && orderData.lineItems.length > 0) {
          await this.processOrderItems(orderId, orderData.lineItems);
        }

        // Attempt attribution if tracking ID exists
        const trackingId = this.extractTrackingId(orderData);
        if (trackingId) {
          await this.processAttribution(orderId, trackingId, normalizedOrder);
        }
      }

      return { success: true };
    } catch (error) {
      logger.error('Failed to process eBay order', {
        error: error.message,
        orderId: orderData.orderId,
        integrationId: integration.id
      });
      throw error;
    }
  }

  /**
   * Normalize eBay order data to our schema
   */
  normalizeOrder(orderData, integration) {
    const buyer = orderData.buyer || {};
    const pricingSummary = orderData.pricingSummary || {};
    
    return {
      tenant_id: integration.tenant_id,
      integration_id: integration.id,
      platform_order_id: orderData.orderId,
      customer_email: buyer.username || null, // eBay doesn't always provide email
      customer_id: buyer.username || null,
      total_amount: parseFloat(pricingSummary.total?.value) || 0,
      currency: pricingSummary.total?.currency || 'USD',
      status: this.mapEbayStatus(orderData.orderFulfillmentStatus),
      tracking_id: this.extractTrackingId(orderData),
      order_data: orderData,
      platform_created_at: new Date(orderData.creationDate),
      created_at: new Date(),
      updated_at: new Date()
    };
  }

  /**
   * Map eBay order status to our standard status
   */
  mapEbayStatus(ebayStatus) {
    const statusMap = {
      'FULFILLED': 'completed',
      'IN_PROGRESS': 'processing',
      'NOT_STARTED': 'pending',
      'CANCELLED': 'cancelled'
    };

    return statusMap[ebayStatus] || 'pending';
  }

  /**
   * Extract tracking ID from eBay order
   */
  extractTrackingId(orderData) {
    // Check if there's a custom field or note with tracking ID
    // eBay orders might have tracking info in fulfillment data
    if (orderData.fulfillmentStartInstructions) {
      for (const instruction of orderData.fulfillmentStartInstructions) {
        if (instruction.fulfillmentInstructionsType === 'DIGITAL_DELIVERY') {
          // Check for tracking ID in digital delivery instructions
          const trackingMatch = instruction.minEstimatedDeliveryDate?.match(/tracking[_-]?id[:\s]*([a-zA-Z0-9-]+)/i);
          if (trackingMatch) {
            return trackingMatch[1];
          }
        }
      }
    }

    // Check line items for tracking data
    if (orderData.lineItems && Array.isArray(orderData.lineItems)) {
      for (const item of orderData.lineItems) {
        // Check item specifics or custom fields
        if (item.itemLocation && item.itemLocation.location) {
          const trackingMatch = item.itemLocation.location.match(/tracking[_-]?id[:\s]*([a-zA-Z0-9-]+)/i);
          if (trackingMatch) {
            return trackingMatch[1];
          }
        }
      }
    }

    // Check buyer checkout notes
    if (orderData.buyerCheckoutNotes) {
      const trackingMatch = orderData.buyerCheckoutNotes.match(/tracking[_-]?id[:\s]*([a-zA-Z0-9-]+)/i);
      if (trackingMatch) {
        return trackingMatch[1];
      }
    }

    return null;
  }

  /**
   * Find existing order in database
   */
  async findExistingOrder(tenantId, integrationId, platformOrderId) {
    const query = `
      SELECT id, platform_order_id, status, total_amount
      FROM orders 
      WHERE tenant_id = $1 
        AND integration_id = $2 
        AND platform_order_id = $3
    `;
    
    const result = await pool.query(query, [tenantId, integrationId, platformOrderId]);
    return result.rows[0] || null;
  }

  /**
   * Create new order in database
   */
  async createOrder(orderData) {
    const orderId = uuidv4();
    
    const query = `
      INSERT INTO orders (
        id, tenant_id, integration_id, platform_order_id, customer_email, 
        customer_id, total_amount, currency, status, tracking_id, 
        order_data, platform_created_at, created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
      RETURNING id
    `;

    const values = [
      orderId,
      orderData.tenant_id,
      orderData.integration_id,
      orderData.platform_order_id,
      orderData.customer_email,
      orderData.customer_id,
      orderData.total_amount,
      orderData.currency,
      orderData.status,
      orderData.tracking_id,
      JSON.stringify(orderData.order_data),
      orderData.platform_created_at,
      orderData.created_at,
      orderData.updated_at
    ];

    const result = await pool.query(query, values);
    return orderId;
  }

  /**
   * Update existing order
   */
  async updateOrder(orderId, orderData) {
    const query = `
      UPDATE orders 
      SET 
        customer_email = $2,
        customer_id = $3,
        total_amount = $4,
        currency = $5,
        status = $6,
        tracking_id = $7,
        order_data = $8,
        updated_at = $9
      WHERE id = $1
    `;

    const values = [
      orderId,
      orderData.customer_email,
      orderData.customer_id,
      orderData.total_amount,
      orderData.currency,
      orderData.status,
      orderData.tracking_id,
      JSON.stringify(orderData.order_data),
      new Date()
    ];

    await pool.query(query, values);
  }

  /**
   * Process order items
   */
  async processOrderItems(orderId, lineItems) {
    for (const item of lineItems) {
      const itemId = uuidv4();
      
      const query = `
        INSERT INTO order_items (
          id, order_id, product_id, sku, name, quantity, price, total, created_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
      `;

      const values = [
        itemId,
        orderId,
        item.legacyItemId || item.lineItemId || null,
        item.legacyVariationSku || null,
        item.title || null,
        parseInt(item.quantity) || 0,
        parseFloat(item.lineItemCost?.value) || 0,
        parseFloat(item.total?.value) || 0,
        new Date()
      ];

      await pool.query(query, values);
    }
  }

  /**
   * Process attribution for the order
   */
  async processAttribution(orderId, trackingId, orderData) {
    try {
      // Find matching click by tracking ID
      const clickQuery = `
        SELECT c.id, c.link_id, l.campaign_id, l.tenant_id
        FROM clicks c
        JOIN links l ON c.link_id = l.id
        WHERE c.tracking_id = $1 
          AND l.tenant_id = $2
        ORDER BY c.clicked_at DESC
        LIMIT 1
      `;

      const clickResult = await pool.query(clickQuery, [trackingId, orderData.tenant_id]);
      
      if (clickResult.rows.length > 0) {
        const click = clickResult.rows[0];
        
        // Create attribution record
        const attributionId = uuidv4();
        const attributionQuery = `
          INSERT INTO attributions (
            id, tenant_id, click_id, order_id, link_id, campaign_id,
            attribution_model, conversion_value, created_at
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
        `;

        const attributionValues = [
          attributionId,
          orderData.tenant_id,
          click.id,
          orderId,
          click.link_id,
          click.campaign_id,
          'last-click',
          orderData.total_amount,
          new Date()
        ];

        await pool.query(attributionQuery, attributionValues);

        logger.info('Created attribution for eBay order', {
          orderId,
          clickId: click.id,
          linkId: click.link_id,
          conversionValue: orderData.total_amount
        });
      } else {
        logger.debug('No matching click found for tracking ID', {
          trackingId,
          orderId,
          tenantId: orderData.tenant_id
        });
      }
    } catch (error) {
      logger.error('Failed to process attribution', {
        error: error.message,
        orderId,
        trackingId
      });
      // Don't throw - attribution failure shouldn't fail order processing
    }
  }
}

module.exports = EbayOrderProcessor;
