const logger = require('../../utils/logger');
const EbayClient = require('./client');
const EbayOrderProcessor = require('./orderProcessor');
const { pool } = require('../../database');

class EbaySyncService {
  constructor() {
    this.platform = 'ebay';
    this.orderProcessor = new EbayOrderProcessor();
  }

  /**
   * Sync orders from eBay
   */
  async syncOrders(integration, options = {}) {
    const {
      dateFrom = null,
      dateTo = null,
      syncType = 'incremental', // 'full' or 'incremental'
      batchSize = 50
    } = options;

    let totalProcessed = 0;
    let totalErrors = 0;
    const startTime = Date.now();

    try {
      logger.info('Starting eBay order sync', {
        integrationId: integration.id,
        syncType,
        dateFrom,
        dateTo,
        batchSize
      });

      // Initialize eBay client
      const client = new EbayClient({
        clientId: integration.api_credentials.client_id,
        clientSecret: integration.api_credentials.client_secret,
        refreshToken: integration.api_credentials.refresh_token,
        sandbox: integration.api_credentials.sandbox || false
      });

      // Test connection first
      const connectionTest = await client.testConnection();
      if (!connectionTest.success) {
        throw new Error(`eBay connection failed: ${connectionTest.error}`);
      }

      let offset = 0;
      let hasMore = true;

      // For incremental sync, get the last synchronized date
      let lastSyncDate = null;
      if (syncType === 'incremental') {
        lastSyncDate = await this.getLastSyncDate(integration.id);
      }

      // Build date filter
      const dateFilter = this.buildDateFilter(dateFrom, dateTo, lastSyncDate);

      while (hasMore) {
        try {
          // Fetch orders from eBay
          const ordersResponse = await client.getOrders({
            limit: batchSize,
            offset: offset,
            ...dateFilter
          });

          const orders = ordersResponse.orders || [];
          
          if (orders.length === 0) {
            hasMore = false;
            break;
          }

          // Process each order
          for (const order of orders) {
            try {
              await this.orderProcessor.processOrder(order, integration);
              totalProcessed++;
            } catch (error) {
              totalErrors++;
              logger.error('Failed to process eBay order', {
                orderId: order.orderId,
                error: error.message
              });
            }
          }

          // Check if we have more orders
          hasMore = orders.length === batchSize;
          offset += batchSize;

          // Rate limiting - eBay has strict limits
          await new Promise(resolve => setTimeout(resolve, 200));

        } catch (error) {
          logger.error('Failed to fetch eBay orders batch', {
            offset,
            error: error.message
          });
          
          // If it's a rate limit error, wait longer
          if (error.response?.status === 429) {
            await new Promise(resolve => setTimeout(resolve, 60000)); // Wait 1 minute
            continue;
          }
          
          throw error;
        }
      }

      // Update last sync timestamp
      await this.updateLastSyncDate(integration.id);

      const duration = Date.now() - startTime;
      
      logger.info('eBay order sync completed', {
        integrationId: integration.id,
        totalProcessed,
        totalErrors,
        duration: `${duration}ms`,
        syncType
      });

      return {
        success: true,
        totalProcessed,
        totalErrors,
        duration
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      
      logger.error('eBay order sync failed', {
        integrationId: integration.id,
        error: error.message,
        totalProcessed,
        totalErrors,
        duration: `${duration}ms`
      });

      throw error;
    }
  }

  /**
   * Build date filter for eBay API
   */
  buildDateFilter(dateFrom, dateTo, lastSyncDate) {
    const filter = {};

    if (dateFrom) {
      filter.creationdate_from = new Date(dateFrom).toISOString();
    } else if (lastSyncDate) {
      filter.creationdate_from = new Date(lastSyncDate).toISOString();
    }

    if (dateTo) {
      filter.creationdate_to = new Date(dateTo).toISOString();
    }

    return filter;
  }

  /**
   * Get last sync date for integration
   */
  async getLastSyncDate(integrationId) {
    try {
      const query = `
        SELECT last_sync_at 
        FROM integrations 
        WHERE id = $1
      `;
      
      const result = await pool.query(query, [integrationId]);
      return result.rows[0]?.last_sync_at || null;
    } catch (error) {
      logger.error('Failed to get last sync date', {
        integrationId,
        error: error.message
      });
      return null;
    }
  }

  /**
   * Update last sync date for integration
   */
  async updateLastSyncDate(integrationId) {
    try {
      const query = `
        UPDATE integrations 
        SET last_sync_at = $1, updated_at = $1
        WHERE id = $2
      `;
      
      await pool.query(query, [new Date(), integrationId]);
    } catch (error) {
      logger.error('Failed to update last sync date', {
        integrationId,
        error: error.message
      });
    }
  }

  /**
   * Sync analytics data from eBay
   */
  async syncAnalytics(integration, options = {}) {
    try {
      logger.info('Starting eBay analytics sync', {
        integrationId: integration.id
      });

      const client = new EbayClient({
        clientId: integration.api_credentials.client_id,
        clientSecret: integration.api_credentials.client_secret,
        refreshToken: integration.api_credentials.refresh_token,
        sandbox: integration.api_credentials.sandbox || false
      });

      // Get seller analytics
      const sellerAnalytics = await client.getSellerAnalytics();
      
      // Get traffic reports
      const trafficReports = await client.getTrafficReports({
        dimension: 'DAY',
        metric: 'CLICK_THROUGH_RATE,LISTING_IMPRESSION_TOTAL',
        ...options
      });

      // Get sales reports
      const salesReports = await client.getSalesReports({
        dimension: 'DAY',
        metric: 'TOTAL_SALES,TRANSACTION_COUNT',
        ...options
      });

      // Store analytics data (you might want to create a separate analytics table)
      await this.storeAnalyticsData(integration.id, {
        sellerAnalytics,
        trafficReports,
        salesReports,
        syncedAt: new Date()
      });

      logger.info('eBay analytics sync completed', {
        integrationId: integration.id
      });

      return {
        success: true,
        data: {
          sellerAnalytics,
          trafficReports,
          salesReports
        }
      };

    } catch (error) {
      logger.error('eBay analytics sync failed', {
        integrationId: integration.id,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Store analytics data
   */
  async storeAnalyticsData(integrationId, analyticsData) {
    try {
      // For now, we'll store in a simple JSON format
      // In production, you might want separate tables for different analytics types
      const query = `
        INSERT INTO sync_jobs (
          integration_id, job_type, status, result_data, created_at
        ) VALUES ($1, $2, $3, $4, $5)
      `;

      await pool.query(query, [
        integrationId,
        'analytics_sync',
        'completed',
        JSON.stringify(analyticsData),
        new Date()
      ]);

    } catch (error) {
      logger.error('Failed to store eBay analytics data', {
        integrationId,
        error: error.message
      });
    }
  }

  /**
   * Setup eBay notification preferences (if supported)
   */
  async setupNotifications(integration) {
    try {
      logger.info('Setting up eBay notifications', {
        integrationId: integration.id
      });

      // eBay doesn't have traditional webhooks like Shopify/WooCommerce
      // Instead, they use notification preferences and polling
      // This is a placeholder for future notification setup

      logger.info('eBay notifications setup completed', {
        integrationId: integration.id
      });

      return { success: true };

    } catch (error) {
      logger.error('Failed to setup eBay notifications', {
        integrationId: integration.id,
        error: error.message
      });
      throw error;
    }
  }
}

module.exports = EbaySyncService;
