const axios = require('axios');
const logger = require('../../utils/logger');
const { recordApiCall } = require('../../metrics');

class EbayClient {
  constructor(config) {
    this.clientId = config.clientId;
    this.clientSecret = config.clientSecret;
    this.refreshToken = config.refreshToken;
    this.sandbox = config.sandbox || false;
    
    // eBay API endpoints
    this.baseURL = this.sandbox 
      ? 'https://api.sandbox.ebay.com'
      : 'https://api.ebay.com';
    
    this.authURL = this.sandbox
      ? 'https://api.sandbox.ebay.com/identity/v1/oauth2/token'
      : 'https://api.ebay.com/identity/v1/oauth2/token';

    // Rate limiting - eBay has strict limits
    this.rateLimit = {
      sellAnalytics: 5000, // calls per day
      sellFulfillment: 5000, // calls per day
      sellInventory: 5000, // calls per day
      requestsPerSecond: 5,
      burstAllowance: 10
    };

    this.accessToken = null;
    this.tokenExpiry = null;

    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'EcommerceAnalytics/1.0'
      }
    });

    // Add request interceptor for authentication
    this.client.interceptors.request.use(
      async (config) => await this.addAuthentication(config),
      (error) => Promise.reject(error)
    );

    // Add response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => {
        recordApiCall('ebay', 'success');
        return response;
      },
      (error) => {
        recordApiCall('ebay', 'error');
        return this.handleError(error);
      }
    );
  }

  /**
   * Add authentication to request
   */
  async addAuthentication(config) {
    // Ensure we have a valid access token
    await this.ensureValidToken();
    
    if (this.accessToken) {
      config.headers.Authorization = `Bearer ${this.accessToken}`;
    }

    return config;
  }

  /**
   * Ensure we have a valid access token
   */
  async ensureValidToken() {
    if (this.accessToken && this.tokenExpiry && Date.now() < this.tokenExpiry) {
      return; // Token is still valid
    }

    await this.refreshAccessToken();
  }

  /**
   * Refresh access token using refresh token
   */
  async refreshAccessToken() {
    try {
      const auth = Buffer.from(`${this.clientId}:${this.clientSecret}`).toString('base64');
      
      const response = await axios.post(this.authURL, 
        'grant_type=refresh_token&refresh_token=' + encodeURIComponent(this.refreshToken),
        {
          headers: {
            'Authorization': `Basic ${auth}`,
            'Content-Type': 'application/x-www-form-urlencoded'
          }
        }
      );

      this.accessToken = response.data.access_token;
      this.tokenExpiry = Date.now() + (response.data.expires_in * 1000);

      logger.info('eBay access token refreshed successfully');
    } catch (error) {
      logger.error('Failed to refresh eBay access token', {
        error: error.message,
        status: error.response?.status
      });
      throw new Error('eBay authentication failed');
    }
  }

  /**
   * Handle API errors
   */
  async handleError(error) {
    if (error.response) {
      const { status, data } = error.response;
      
      logger.error('eBay API error', {
        status,
        message: data?.errors?.[0]?.message || 'Unknown error',
        errorId: data?.errors?.[0]?.errorId,
        domain: data?.errors?.[0]?.domain
      });

      // Handle rate limiting
      if (status === 429) {
        const retryAfter = error.response.headers['retry-after'] || 60;
        logger.warn('eBay rate limit hit, retrying after delay', { retryAfter });
        
        await new Promise(resolve => setTimeout(resolve, retryAfter * 1000));
        return this.client.request(error.config);
      }

      // Handle authentication errors
      if (status === 401) {
        logger.warn('eBay token expired, refreshing...');
        await this.refreshAccessToken();
        return this.client.request(error.config);
      }
    }

    throw error;
  }

  /**
   * Test connection to eBay
   */
  async testConnection() {
    try {
      // Test with a simple API call
      const response = await this.client.get('/sell/analytics/v1/seller_standards_profile');
      
      logger.info('eBay connection test successful');
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      logger.error('eBay connection test failed', {
        error: error.message
      });

      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get seller analytics
   */
  async getSellerAnalytics(params = {}) {
    try {
      const queryParams = {
        ...params
      };

      const response = await this.client.get('/sell/analytics/v1/seller_standards_profile', {
        params: queryParams
      });

      return response.data;
    } catch (error) {
      logger.error('Failed to fetch eBay seller analytics', {
        error: error.message,
        params
      });
      throw error;
    }
  }

  /**
   * Get orders (fulfillment)
   */
  async getOrders(params = {}) {
    try {
      const defaultParams = {
        limit: 50,
        offset: 0,
        orderby: 'creationdate',
        sort: 'DESC'
      };

      const queryParams = { ...defaultParams, ...params };

      const response = await this.client.get('/sell/fulfillment/v1/order', {
        params: queryParams
      });

      return {
        orders: response.data.orders || [],
        total: response.data.total || 0,
        limit: queryParams.limit,
        offset: queryParams.offset
      };
    } catch (error) {
      logger.error('Failed to fetch eBay orders', {
        error: error.message,
        params
      });
      throw error;
    }
  }

  /**
   * Get single order
   */
  async getOrder(orderId) {
    try {
      const response = await this.client.get(`/sell/fulfillment/v1/order/${orderId}`);
      return response.data;
    } catch (error) {
      logger.error('Failed to fetch eBay order', {
        orderId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Get inventory items
   */
  async getInventoryItems(params = {}) {
    try {
      const defaultParams = {
        limit: 100,
        offset: 0
      };

      const queryParams = { ...defaultParams, ...params };

      const response = await this.client.get('/sell/inventory/v1/inventory_item', {
        params: queryParams
      });

      return {
        inventoryItems: response.data.inventoryItems || [],
        total: response.data.total || 0,
        limit: queryParams.limit,
        offset: queryParams.offset
      };
    } catch (error) {
      logger.error('Failed to fetch eBay inventory items', {
        error: error.message,
        params
      });
      throw error;
    }
  }

  /**
   * Get traffic reports
   */
  async getTrafficReports(params = {}) {
    try {
      const response = await this.client.get('/sell/analytics/v1/traffic_report', {
        params
      });

      return response.data;
    } catch (error) {
      logger.error('Failed to fetch eBay traffic reports', {
        error: error.message,
        params
      });
      throw error;
    }
  }

  /**
   * Get sales reports
   */
  async getSalesReports(params = {}) {
    try {
      const response = await this.client.get('/sell/analytics/v1/sales_report', {
        params
      });

      return response.data;
    } catch (error) {
      logger.error('Failed to fetch eBay sales reports', {
        error: error.message,
        params
      });
      throw error;
    }
  }
}

module.exports = EbayClient;
