const axios = require('axios');
const crypto = require('crypto');
const logger = require('../../utils/logger');
const { recordApiCall } = require('../../metrics');

class WooCommerceClient {
  constructor(storeUrl, consumerKey, consumerSecret) {
    this.storeUrl = storeUrl.replace(/\/$/, ''); // Remove trailing slash
    this.consumerKey = consumerKey;
    this.consumerSecret = consumerSecret;
    this.apiVersion = 'wc/v3';
    
    // Rate limiting configuration
    this.rateLimit = {
      requestsPerSecond: 10, // WooCommerce is generally more lenient
      maxConcurrent: 5,
      retryDelay: 1000
    };

    this.client = axios.create({
      baseURL: `${this.storeUrl}/wp-json/${this.apiVersion}`,
      timeout: 30000,
      headers: {
        'User-Agent': 'EcommerceAnalytics/1.0',
        'Content-Type': 'application/json'
      }
    });

    // Add request interceptor for authentication
    this.client.interceptors.request.use(
      (config) => this.addAuthentication(config),
      (error) => Promise.reject(error)
    );

    // Add response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => {
        recordApiCall('woocommerce', 'success');
        return response;
      },
      (error) => {
        recordApiCall('woocommerce', 'error');
        return this.handleError(error);
      }
    );
  }

  /**
   * Add OAuth 1.0a authentication to request
   */
  addAuthentication(config) {
    const timestamp = Math.floor(Date.now() / 1000);
    const nonce = crypto.randomBytes(16).toString('hex');
    
    // For WooCommerce REST API, we can use basic auth with consumer key/secret
    // or OAuth 1.0a. Using basic auth for simplicity in HTTPS environments
    if (this.storeUrl.startsWith('https://')) {
      // Use basic auth for HTTPS
      const auth = Buffer.from(`${this.consumerKey}:${this.consumerSecret}`).toString('base64');
      config.headers.Authorization = `Basic ${auth}`;
    } else {
      // Use OAuth 1.0a for HTTP
      const oauthParams = {
        oauth_consumer_key: this.consumerKey,
        oauth_timestamp: timestamp,
        oauth_nonce: nonce,
        oauth_signature_method: 'HMAC-SHA1',
        oauth_version: '1.0'
      };

      // Generate signature
      const signature = this.generateOAuthSignature(config.method, config.url, oauthParams);
      oauthParams.oauth_signature = signature;

      // Add to query params
      config.params = { ...config.params, ...oauthParams };
    }

    return config;
  }

  /**
   * Generate OAuth 1.0a signature
   */
  generateOAuthSignature(method, url, params) {
    const baseString = this.createSignatureBaseString(method, url, params);
    const signingKey = `${this.consumerSecret}&`; // No token secret for 2-legged OAuth
    
    return crypto
      .createHmac('sha1', signingKey)
      .update(baseString)
      .digest('base64');
  }

  /**
   * Create OAuth signature base string
   */
  createSignatureBaseString(method, url, params) {
    const sortedParams = Object.keys(params)
      .sort()
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
      .join('&');

    return [
      method.toUpperCase(),
      encodeURIComponent(url),
      encodeURIComponent(sortedParams)
    ].join('&');
  }

  /**
   * Handle API errors
   */
  async handleError(error) {
    if (error.response) {
      const { status, data } = error.response;
      
      logger.error('WooCommerce API error', {
        status,
        message: data?.message || 'Unknown error',
        code: data?.code,
        storeUrl: this.storeUrl
      });

      // Handle rate limiting
      if (status === 429) {
        const retryAfter = error.response.headers['retry-after'] || this.rateLimit.retryDelay;
        logger.warn('WooCommerce rate limit hit, retrying after delay', { retryAfter });
        
        await new Promise(resolve => setTimeout(resolve, retryAfter * 1000));
        return this.client.request(error.config);
      }

      // Handle authentication errors
      if (status === 401) {
        throw new Error('WooCommerce authentication failed. Check consumer key and secret.');
      }

      // Handle not found
      if (status === 404) {
        throw new Error('WooCommerce resource not found. Check store URL and API version.');
      }
    }

    throw error;
  }

  /**
   * Test connection to WooCommerce store
   */
  async testConnection() {
    try {
      const response = await this.client.get('/system_status');
      
      logger.info('WooCommerce connection test successful', {
        storeUrl: this.storeUrl,
        version: response.data?.environment?.version
      });

      return {
        success: true,
        version: response.data?.environment?.version,
        environment: response.data?.environment
      };
    } catch (error) {
      logger.error('WooCommerce connection test failed', {
        storeUrl: this.storeUrl,
        error: error.message
      });

      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get orders with pagination
   */
  async getOrders(params = {}) {
    const defaultParams = {
      per_page: 100,
      page: 1,
      orderby: 'date',
      order: 'desc'
    };

    const queryParams = { ...defaultParams, ...params };

    try {
      const response = await this.client.get('/orders', { params: queryParams });
      
      return {
        orders: response.data,
        totalPages: parseInt(response.headers['x-wp-totalpages']) || 1,
        totalItems: parseInt(response.headers['x-wp-total']) || 0,
        currentPage: queryParams.page
      };
    } catch (error) {
      logger.error('Failed to fetch WooCommerce orders', {
        error: error.message,
        params: queryParams
      });
      throw error;
    }
  }

  /**
   * Get single order by ID
   */
  async getOrder(orderId) {
    try {
      const response = await this.client.get(`/orders/${orderId}`);
      return response.data;
    } catch (error) {
      logger.error('Failed to fetch WooCommerce order', {
        orderId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Get products with pagination
   */
  async getProducts(params = {}) {
    const defaultParams = {
      per_page: 100,
      page: 1,
      status: 'publish'
    };

    const queryParams = { ...defaultParams, ...params };

    try {
      const response = await this.client.get('/products', { params: queryParams });
      
      return {
        products: response.data,
        totalPages: parseInt(response.headers['x-wp-totalpages']) || 1,
        totalItems: parseInt(response.headers['x-wp-total']) || 0,
        currentPage: queryParams.page
      };
    } catch (error) {
      logger.error('Failed to fetch WooCommerce products', {
        error: error.message,
        params: queryParams
      });
      throw error;
    }
  }

  /**
   * Get customers with pagination
   */
  async getCustomers(params = {}) {
    const defaultParams = {
      per_page: 100,
      page: 1,
      orderby: 'registered_date',
      order: 'desc'
    };

    const queryParams = { ...defaultParams, ...params };

    try {
      const response = await this.client.get('/customers', { params: queryParams });
      
      return {
        customers: response.data,
        totalPages: parseInt(response.headers['x-wp-totalpages']) || 1,
        totalItems: parseInt(response.headers['x-wp-total']) || 0,
        currentPage: queryParams.page
      };
    } catch (error) {
      logger.error('Failed to fetch WooCommerce customers', {
        error: error.message,
        params: queryParams
      });
      throw error;
    }
  }

  /**
   * Create webhook
   */
  async createWebhook(webhook) {
    try {
      const response = await this.client.post('/webhooks', webhook);
      
      logger.info('Created WooCommerce webhook', {
        webhookId: response.data.id,
        topic: webhook.topic,
        deliveryUrl: webhook.delivery_url
      });

      return response.data;
    } catch (error) {
      logger.error('Failed to create WooCommerce webhook', {
        error: error.message,
        webhook
      });
      throw error;
    }
  }

  /**
   * List webhooks
   */
  async getWebhooks() {
    try {
      const response = await this.client.get('/webhooks');
      return response.data;
    } catch (error) {
      logger.error('Failed to fetch WooCommerce webhooks', {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Delete webhook
   */
  async deleteWebhook(webhookId) {
    try {
      await this.client.delete(`/webhooks/${webhookId}`, { params: { force: true } });
      
      logger.info('Deleted WooCommerce webhook', { webhookId });
      return true;
    } catch (error) {
      logger.error('Failed to delete WooCommerce webhook', {
        webhookId,
        error: error.message
      });
      throw error;
    }
  }
}

module.exports = WooCommerceClient;
