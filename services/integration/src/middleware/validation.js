const Joi = require('joi');

// Validation middleware factory
function validate(schema) {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.body, {
      abortEarly: false,
      stripUnknown: true,
    });

    if (error) {
      const validationError = new Error('Validation Error');
      validationError.name = 'ValidationError';
      validationError.details = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
      }));
      return next(validationError);
    }

    req.body = value;
    next();
  };
}

// Common validation schemas
const schemas = {
  // Integration schemas
  createIntegration: Joi.object({
    tenant_id: Joi.string().uuid().required(),
    platform: Joi.string().valid('shopify', 'woocommerce', 'ebay').required(),
    store_name: Joi.string().min(1).max(255).required(),
    store_url: Joi.string().uri().required(),
    api_credentials: Joi.object().required(),
  }),

  updateIntegration: Joi.object({
    tenant_id: Joi.string().uuid().required(),
    store_name: Joi.string().min(1).max(255),
    store_url: Joi.string().uri(),
    api_credentials: Joi.object(),
    is_active: Joi.boolean(),
  }),

  // Webhook schemas
  shopifyWebhook: Joi.object({
    id: Joi.number().required(),
    email: Joi.string().email(),
    created_at: Joi.string().isoDate().required(),
    updated_at: Joi.string().isoDate(),
    // Add more Shopify-specific fields as needed
  }),

  woocommerceWebhook: Joi.object({
    id: Joi.number().required(),
    status: Joi.string().required(),
    date_created: Joi.string().isoDate().required(),
    date_modified: Joi.string().isoDate(),
    // Add more WooCommerce-specific fields as needed
  }),

  ebayOrder: Joi.object({
    orderId: Joi.string().required(),
    orderFulfillmentStatus: Joi.string().required(),
    creationDate: Joi.string().isoDate().required(),
    pricingSummary: Joi.object(),
    buyer: Joi.object(),
    lineItems: Joi.array(),
  }),

  // Sync request schemas
  syncRequest: Joi.object({
    integration_id: Joi.string().uuid().required(),
    sync_type: Joi.string().valid('full', 'incremental').default('incremental'),
    date_from: Joi.string().isoDate(),
    date_to: Joi.string().isoDate(),
  }),

  // Order schemas
  orderFilter: Joi.object({
    platform: Joi.string().valid('shopify', 'woocommerce', 'ebay'),
    status: Joi.string(),
    date_from: Joi.string().isoDate(),
    date_to: Joi.string().isoDate(),
    limit: Joi.number().integer().min(1).max(1000).default(50),
    offset: Joi.number().integer().min(0).default(0),
  }),

  // Integration validation
  validateIntegration: Joi.object({
    platform: Joi.string().valid('shopify', 'woocommerce', 'ebay').required(),
    store_name: Joi.string().min(1).max(255).required(),
    store_url: Joi.string().uri().required(),
    api_credentials: Joi.when('platform', {
      is: 'shopify',
      then: Joi.object({
        shop_domain: Joi.string().required(),
        access_token: Joi.string().required()
      }).required(),
      otherwise: Joi.when('platform', {
        is: 'woocommerce',
        then: Joi.object({
          consumer_key: Joi.string().required(),
          consumer_secret: Joi.string().required()
        }).required(),
        otherwise: Joi.when('platform', {
          is: 'ebay',
          then: Joi.object({
            client_id: Joi.string().required(),
            client_secret: Joi.string().required(),
            refresh_token: Joi.string().required(),
            sandbox: Joi.boolean().default(false)
          }).required()
        })
      })
    })
  }),
};

// Validation middleware functions
const validateCreateIntegration = validate(schemas.createIntegration);
const validateUpdateIntegration = validate(schemas.updateIntegration);
const validateShopifyWebhook = validate(schemas.shopifyWebhook);
const validateWooCommerceWebhook = validate(schemas.woocommerceWebhook);
const validateEbayOrder = validate(schemas.ebayOrder);
const validateSyncRequest = validate(schemas.syncRequest);
const validateIntegration = validate(schemas.validateIntegration);

// Query parameter validation
function validateQuery(schema) {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.query, {
      abortEarly: false,
      stripUnknown: true,
    });

    if (error) {
      const validationError = new Error('Validation Error');
      validationError.name = 'ValidationError';
      validationError.details = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
      }));
      return next(validationError);
    }

    req.query = value;
    next();
  };
}

const validateOrderFilter = validateQuery(schemas.orderFilter);

module.exports = {
  validate,
  validateCreateIntegration,
  validateUpdateIntegration,
  validateShopifyWebhook,
  validateWooCommerceWebhook,
  validateEbayOrder,
  validateSyncRequest,
  validateOrderFilter,
  validateIntegration,
  schemas,
};