const jwt = require('jsonwebtoken');
const logger = require('../utils/logger');

/**
 * Middleware to authenticate JWT tokens
 */
function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

  if (!token) {
    return res.status(401).json({
      success: false,
      error: 'Access token required'
    });
  }

  jwt.verify(token, process.env.JWT_SECRET || 'default-jwt-secret', (err, user) => {
    if (err) {
      logger.warn('Invalid token provided', {
        error: err.message,
        ip: req.ip
      });
      
      return res.status(403).json({
        success: false,
        error: 'Invalid or expired token'
      });
    }

    req.user = user;
    next();
  });
}

/**
 * Middleware to check if user has required role
 */
function requireRole(role) {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    if (req.user.role !== role && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'Insufficient permissions'
      });
    }

    next();
  };
}

/**
 * Middleware to check if user belongs to tenant
 */
function requireTenant(req, res, next) {
  const tenantId = req.params.tenantId || req.body.tenant_id || req.query.tenant_id;
  
  if (!tenantId) {
    return res.status(400).json({
      success: false,
      error: 'Tenant ID required'
    });
  }

  if (!req.user) {
    return res.status(401).json({
      success: false,
      error: 'Authentication required'
    });
  }

  // Check if user belongs to the tenant
  if (req.user.tenantId !== tenantId && req.user.role !== 'admin') {
    return res.status(403).json({
      success: false,
      error: 'Access denied for this tenant'
    });
  }

  next();
}

/**
 * Optional authentication - sets user if token is valid but doesn't require it
 */
function optionalAuth(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return next();
  }

  jwt.verify(token, process.env.JWT_SECRET || 'default-jwt-secret', (err, user) => {
    if (!err) {
      req.user = user;
    }
    next();
  });
}

module.exports = {
  authenticateToken,
  requireRole,
  requireTenant,
  optionalAuth
};
