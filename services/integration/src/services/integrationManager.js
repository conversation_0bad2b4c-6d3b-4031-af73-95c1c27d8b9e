const logger = require('../utils/logger');
const { pool } = require('../database');
const { v4: uuidv4 } = require('uuid');

// Platform-specific services
const ShopifySyncService = require('../platforms/shopify/syncService');
const WooCommerceClient = require('../platforms/woocommerce/client');
const EbaySyncService = require('../platforms/ebay/syncService');

class IntegrationManager {
  constructor() {
    this.shopifySync = new ShopifySyncService();
    this.ebaySync = new EbaySyncService();
  }

  /**
   * Create new integration
   */
  async createIntegration(tenantId, integrationData) {
    try {
      const integrationId = uuidv4();
      
      // Validate platform-specific credentials
      await this.validateCredentials(integrationData.platform, integrationData.api_credentials);

      const query = `
        INSERT INTO integrations (
          id, tenant_id, platform, store_name, store_url, 
          api_credentials, webhook_url, webhook_secret, 
          is_active, created_at, updated_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
        RETURNING *
      `;

      const values = [
        integrationId,
        tenantId,
        integrationData.platform,
        integrationData.store_name,
        integrationData.store_url,
        JSON.stringify(integrationData.api_credentials),
        integrationData.webhook_url || null,
        integrationData.webhook_secret || null,
        true,
        new Date(),
        new Date()
      ];

      const result = await pool.query(query, values);
      const integration = result.rows[0];

      // Setup platform-specific configurations
      await this.setupPlatformIntegration(integration);

      logger.info('Integration created successfully', {
        integrationId,
        platform: integrationData.platform,
        tenantId
      });

      return integration;

    } catch (error) {
      logger.error('Failed to create integration', {
        error: error.message,
        platform: integrationData.platform,
        tenantId
      });
      throw error;
    }
  }

  /**
   * Validate platform credentials
   */
  async validateCredentials(platform, credentials) {
    try {
      switch (platform) {
        case 'shopify':
          return await this.validateShopifyCredentials(credentials);
        case 'woocommerce':
          return await this.validateWooCommerceCredentials(credentials);
        case 'ebay':
          return await this.validateEbayCredentials(credentials);
        default:
          throw new Error(`Unsupported platform: ${platform}`);
      }
    } catch (error) {
      logger.error('Credential validation failed', {
        platform,
        error: error.message
      });
      throw new Error(`Invalid ${platform} credentials: ${error.message}`);
    }
  }

  /**
   * Validate Shopify credentials
   */
  async validateShopifyCredentials(credentials) {
    const ShopifyClient = require('../platforms/shopify/client');
    const client = new ShopifyClient(credentials.shop_domain, credentials.access_token);
    
    const test = await client.testConnection();
    if (!test.success) {
      throw new Error(test.error);
    }
    
    return true;
  }

  /**
   * Validate WooCommerce credentials
   */
  async validateWooCommerceCredentials(credentials) {
    const client = new WooCommerceClient(
      credentials.store_url,
      credentials.consumer_key,
      credentials.consumer_secret
    );
    
    const test = await client.testConnection();
    if (!test.success) {
      throw new Error(test.error);
    }
    
    return true;
  }

  /**
   * Validate eBay credentials
   */
  async validateEbayCredentials(credentials) {
    const EbayClient = require('../platforms/ebay/client');
    const client = new EbayClient(credentials);
    
    const test = await client.testConnection();
    if (!test.success) {
      throw new Error(test.error);
    }
    
    return true;
  }

  /**
   * Setup platform-specific integration
   */
  async setupPlatformIntegration(integration) {
    try {
      switch (integration.platform) {
        case 'shopify':
          await this.setupShopifyIntegration(integration);
          break;
        case 'woocommerce':
          await this.setupWooCommerceIntegration(integration);
          break;
        case 'ebay':
          await this.setupEbayIntegration(integration);
          break;
        default:
          logger.warn('No specific setup required for platform', {
            platform: integration.platform
          });
      }
    } catch (error) {
      logger.error('Platform setup failed', {
        integrationId: integration.id,
        platform: integration.platform,
        error: error.message
      });
      // Don't throw - integration can still work without full setup
    }
  }

  /**
   * Setup Shopify integration
   */
  async setupShopifyIntegration(integration) {
    // Setup webhooks
    await this.shopifySync.setupWebhooks(integration);
    
    // Initial data sync
    await this.shopifySync.syncOrders(integration, { syncType: 'incremental' });
  }

  /**
   * Setup WooCommerce integration
   */
  async setupWooCommerceIntegration(integration) {
    const client = new WooCommerceClient(
      integration.store_url,
      integration.api_credentials.consumer_key,
      integration.api_credentials.consumer_secret
    );

    // Setup webhooks
    const webhookEndpoint = `${process.env.WEBHOOK_BASE_URL || 'https://your-domain.com'}/api/webhooks/woocommerce`;
    
    const webhooksToCreate = [
      {
        name: 'Order Created',
        topic: 'order.created',
        delivery_url: `${webhookEndpoint}/orders/create`,
        secret: integration.webhook_secret || process.env.WOOCOMMERCE_WEBHOOK_SECRET
      },
      {
        name: 'Order Updated',
        topic: 'order.updated',
        delivery_url: `${webhookEndpoint}/orders/update`,
        secret: integration.webhook_secret || process.env.WOOCOMMERCE_WEBHOOK_SECRET
      }
    ];

    for (const webhook of webhooksToCreate) {
      try {
        await client.createWebhook(webhook);
        logger.info('Created WooCommerce webhook', {
          integrationId: integration.id,
          topic: webhook.topic
        });
      } catch (error) {
        logger.error('Failed to create WooCommerce webhook', {
          integrationId: integration.id,
          topic: webhook.topic,
          error: error.message
        });
      }
    }
  }

  /**
   * Setup eBay integration
   */
  async setupEbayIntegration(integration) {
    // eBay doesn't have webhooks, so we setup periodic sync
    await this.ebaySync.setupNotifications(integration);
    
    // Initial data sync
    await this.ebaySync.syncOrders(integration, { syncType: 'incremental' });
  }

  /**
   * Sync data for integration
   */
  async syncIntegration(integrationId, options = {}) {
    try {
      const integration = await this.getIntegration(integrationId);
      if (!integration) {
        throw new Error('Integration not found');
      }

      logger.info('Starting integration sync', {
        integrationId,
        platform: integration.platform
      });

      let result;
      switch (integration.platform) {
        case 'shopify':
          result = await this.shopifySync.syncOrders(integration, options);
          break;
        case 'woocommerce':
          result = await this.syncWooCommerceOrders(integration, options);
          break;
        case 'ebay':
          result = await this.ebaySync.syncOrders(integration, options);
          break;
        default:
          throw new Error(`Sync not implemented for platform: ${integration.platform}`);
      }

      logger.info('Integration sync completed', {
        integrationId,
        platform: integration.platform,
        result
      });

      return result;

    } catch (error) {
      logger.error('Integration sync failed', {
        integrationId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Sync WooCommerce orders
   */
  async syncWooCommerceOrders(integration, options = {}) {
    const client = new WooCommerceClient(
      integration.store_url,
      integration.api_credentials.consumer_key,
      integration.api_credentials.consumer_secret
    );

    const WooCommerceOrderProcessor = require('../platforms/woocommerce/orderProcessor');
    const processor = new WooCommerceOrderProcessor();

    let totalProcessed = 0;
    let page = 1;
    const perPage = options.batchSize || 100;

    while (true) {
      const ordersResponse = await client.getOrders({
        page,
        per_page: perPage,
        modified_after: options.dateFrom || integration.last_sync_at
      });

      if (ordersResponse.orders.length === 0) {
        break;
      }

      for (const order of ordersResponse.orders) {
        try {
          await processor.processOrder(order, integration);
          totalProcessed++;
        } catch (error) {
          logger.error('Failed to process WooCommerce order', {
            orderId: order.id,
            error: error.message
          });
        }
      }

      if (ordersResponse.orders.length < perPage) {
        break;
      }

      page++;
    }

    // Update last sync timestamp
    await this.updateLastSyncDate(integration.id);

    return {
      success: true,
      totalProcessed
    };
  }

  /**
   * Get integration by ID
   */
  async getIntegration(integrationId) {
    const query = `
      SELECT * FROM integrations 
      WHERE id = $1 AND is_active = true
    `;
    
    const result = await pool.query(query, [integrationId]);
    const integration = result.rows[0];
    
    if (integration && integration.api_credentials) {
      integration.api_credentials = JSON.parse(integration.api_credentials);
    }
    
    return integration;
  }

  /**
   * Update last sync date
   */
  async updateLastSyncDate(integrationId) {
    const query = `
      UPDATE integrations 
      SET last_sync_at = $1, updated_at = $1
      WHERE id = $2
    `;
    
    await pool.query(query, [new Date(), integrationId]);
  }

  /**
   * Get all integrations for tenant
   */
  async getIntegrationsByTenant(tenantId) {
    const query = `
      SELECT * FROM integrations 
      WHERE tenant_id = $1 AND is_active = true
      ORDER BY created_at DESC
    `;
    
    const result = await pool.query(query, [tenantId]);
    return result.rows.map(integration => {
      if (integration.api_credentials) {
        integration.api_credentials = JSON.parse(integration.api_credentials);
      }
      return integration;
    });
  }

  /**
   * Delete integration
   */
  async deleteIntegration(integrationId) {
    const query = `
      UPDATE integrations 
      SET is_active = false, updated_at = $1
      WHERE id = $2
    `;
    
    await pool.query(query, [new Date(), integrationId]);
    
    logger.info('Integration deactivated', { integrationId });
  }
}

module.exports = IntegrationManager;
