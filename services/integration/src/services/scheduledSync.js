const cron = require('node-cron');
const logger = require('../utils/logger');
const { pool } = require('../database');
const IntegrationManager = require('./integrationManager');

class ScheduledSyncService {
  constructor() {
    this.integrationManager = new IntegrationManager();
    this.isRunning = false;
    this.jobs = new Map();
  }

  /**
   * Start all scheduled sync jobs
   */
  start() {
    if (this.isRunning) {
      logger.warn('Scheduled sync service is already running');
      return;
    }

    logger.info('Starting scheduled sync service');

    // Schedule different sync frequencies
    this.scheduleHourlySync();
    this.scheduleDailySync();
    this.scheduleWeeklySync();

    this.isRunning = true;
    logger.info('Scheduled sync service started successfully');
  }

  /**
   * Stop all scheduled sync jobs
   */
  stop() {
    if (!this.isRunning) {
      logger.warn('Scheduled sync service is not running');
      return;
    }

    logger.info('Stopping scheduled sync service');

    // Stop all cron jobs
    this.jobs.forEach((job, name) => {
      job.stop();
      logger.info(`Stopped scheduled job: ${name}`);
    });

    this.jobs.clear();
    this.isRunning = false;
    logger.info('Scheduled sync service stopped');
  }

  /**
   * Schedule hourly sync for high-priority integrations
   */
  scheduleHourlySync() {
    const job = cron.schedule('0 * * * *', async () => {
      try {
        logger.info('Starting hourly sync job');
        await this.runSyncForPriority('high');
        logger.info('Hourly sync job completed');
      } catch (error) {
        logger.error('Hourly sync job failed', { error: error.message });
      }
    }, {
      scheduled: false
    });

    this.jobs.set('hourly', job);
    job.start();
    logger.info('Scheduled hourly sync job');
  }

  /**
   * Schedule daily sync for normal integrations
   */
  scheduleDailySync() {
    const job = cron.schedule('0 2 * * *', async () => { // 2 AM daily
      try {
        logger.info('Starting daily sync job');
        await this.runSyncForPriority('normal');
        logger.info('Daily sync job completed');
      } catch (error) {
        logger.error('Daily sync job failed', { error: error.message });
      }
    }, {
      scheduled: false
    });

    this.jobs.set('daily', job);
    job.start();
    logger.info('Scheduled daily sync job');
  }

  /**
   * Schedule weekly sync for low-priority integrations
   */
  scheduleWeeklySync() {
    const job = cron.schedule('0 3 * * 0', async () => { // 3 AM on Sundays
      try {
        logger.info('Starting weekly sync job');
        await this.runSyncForPriority('low');
        logger.info('Weekly sync job completed');
      } catch (error) {
        logger.error('Weekly sync job failed', { error: error.message });
      }
    }, {
      scheduled: false
    });

    this.jobs.set('weekly', job);
    job.start();
    logger.info('Scheduled weekly sync job');
  }

  /**
   * Run sync for integrations with specific priority
   */
  async runSyncForPriority(priority) {
    try {
      const integrations = await this.getIntegrationsForSync(priority);
      
      logger.info(`Found ${integrations.length} integrations for ${priority} priority sync`);

      for (const integration of integrations) {
        try {
          await this.syncIntegrationWithRetry(integration);
        } catch (error) {
          logger.error('Failed to sync integration', {
            integrationId: integration.id,
            platform: integration.platform,
            error: error.message
          });
        }
      }

    } catch (error) {
      logger.error(`Failed to run ${priority} priority sync`, {
        error: error.message
      });
    }
  }

  /**
   * Get integrations that need syncing based on priority
   */
  async getIntegrationsForSync(priority) {
    try {
      let whereClause = '';
      let timeThreshold = '';

      switch (priority) {
        case 'high':
          // Sync if last sync was more than 1 hour ago
          timeThreshold = "NOW() - INTERVAL '1 hour'";
          break;
        case 'normal':
          // Sync if last sync was more than 24 hours ago
          timeThreshold = "NOW() - INTERVAL '24 hours'";
          break;
        case 'low':
          // Sync if last sync was more than 7 days ago
          timeThreshold = "NOW() - INTERVAL '7 days'";
          break;
        default:
          throw new Error(`Invalid priority: ${priority}`);
      }

      const query = `
        SELECT id, tenant_id, platform, store_url, api_credentials, last_sync_at
        FROM integrations 
        WHERE is_active = true 
          AND (last_sync_at IS NULL OR last_sync_at < ${timeThreshold})
        ORDER BY last_sync_at ASC NULLS FIRST
        LIMIT 50
      `;

      const result = await pool.query(query);
      
      return result.rows.map(integration => {
        if (integration.api_credentials) {
          integration.api_credentials = JSON.parse(integration.api_credentials);
        }
        return integration;
      });

    } catch (error) {
      logger.error('Failed to get integrations for sync', {
        priority,
        error: error.message
      });
      return [];
    }
  }

  /**
   * Sync integration with retry logic
   */
  async syncIntegrationWithRetry(integration, maxRetries = 3) {
    let lastError;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        logger.info('Syncing integration', {
          integrationId: integration.id,
          platform: integration.platform,
          attempt,
          maxRetries
        });

        const result = await this.integrationManager.syncIntegration(
          integration.id,
          { syncType: 'incremental' }
        );

        logger.info('Integration sync completed', {
          integrationId: integration.id,
          platform: integration.platform,
          result
        });

        return result;

      } catch (error) {
        lastError = error;
        
        logger.warn('Integration sync attempt failed', {
          integrationId: integration.id,
          platform: integration.platform,
          attempt,
          maxRetries,
          error: error.message
        });

        if (attempt < maxRetries) {
          // Wait before retry (exponential backoff)
          const delay = Math.pow(2, attempt) * 1000;
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    // All retries failed
    logger.error('Integration sync failed after all retries', {
      integrationId: integration.id,
      platform: integration.platform,
      maxRetries,
      error: lastError.message
    });

    // Record the failure
    await this.recordSyncFailure(integration.id, lastError.message);
    
    throw lastError;
  }

  /**
   * Record sync failure in database
   */
  async recordSyncFailure(integrationId, errorMessage) {
    try {
      const query = `
        INSERT INTO sync_jobs (
          integration_id, job_type, status, error_message, created_at
        ) VALUES ($1, $2, $3, $4, $5)
      `;

      await pool.query(query, [
        integrationId,
        'scheduled_sync',
        'failed',
        errorMessage,
        new Date()
      ]);

    } catch (error) {
      logger.error('Failed to record sync failure', {
        integrationId,
        error: error.message
      });
    }
  }

  /**
   * Get sync statistics
   */
  async getSyncStats() {
    try {
      const query = `
        SELECT 
          COUNT(*) as total_integrations,
          COUNT(CASE WHEN last_sync_at > NOW() - INTERVAL '1 hour' THEN 1 END) as synced_last_hour,
          COUNT(CASE WHEN last_sync_at > NOW() - INTERVAL '24 hours' THEN 1 END) as synced_last_day,
          COUNT(CASE WHEN last_sync_at IS NULL THEN 1 END) as never_synced
        FROM integrations 
        WHERE is_active = true
      `;

      const result = await pool.query(query);
      return result.rows[0];

    } catch (error) {
      logger.error('Failed to get sync stats', {
        error: error.message
      });
      return null;
    }
  }

  /**
   * Force sync for specific integration
   */
  async forceSyncIntegration(integrationId) {
    try {
      const integration = await this.integrationManager.getIntegration(integrationId);
      if (!integration) {
        throw new Error('Integration not found');
      }

      logger.info('Force syncing integration', {
        integrationId,
        platform: integration.platform
      });

      const result = await this.syncIntegrationWithRetry(integration);
      
      logger.info('Force sync completed', {
        integrationId,
        result
      });

      return result;

    } catch (error) {
      logger.error('Force sync failed', {
        integrationId,
        error: error.message
      });
      throw error;
    }
  }
}

module.exports = ScheduledSyncService;
