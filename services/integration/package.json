{"name": "integration-service", "version": "1.0.0", "description": "E-commerce platform integration service", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "axios": "^1.6.2", "pg": "^8.11.3", "redis": "^4.6.10", "joi": "^17.11.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "bull": "^4.12.2", "winston": "^3.11.0", "prom-client": "^15.0.0", "crypto": "^1.0.1", "moment": "^2.29.4", "uuid": "^9.0.1", "node-cron": "^3.0.3"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.55.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1"}, "keywords": ["ecommerce", "integration", "shopify", "woocommerce", "amazon", "api"], "author": "Your Name", "license": "MIT", "engines": {"node": ">=18.0.0"}}