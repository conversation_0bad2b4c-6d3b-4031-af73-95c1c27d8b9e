const jwt = require('jsonwebtoken');
const logger = require('../utils/logger');
const { pool } = require('../database');

const authMiddleware = async (req, res, next) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');

    if (!token) {
      return res.status(401).json({
        success: false,
        error: 'Access denied. No token provided.',
      });
    }

    // Verify JWT token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // Get user from database
    const client = await pool.connect();
    
    try {
      // Get user and their tenant information
      const userQuery = `
        SELECT
          u.id,
          u.email,
          u.role,
          u.is_active,
          ut.tenant_id,
          ut.role as tenant_role,
          t.name as tenant_name
        FROM users u
        LEFT JOIN user_tenants ut ON u.id = ut.user_id
        LEFT JOIN tenants t ON ut.tenant_id = t.id
        WHERE u.id = $1 AND (ut.tenant_id IS NULL OR t.is_active = true)
        LIMIT 1
      `;
      const userResult = await client.query(userQuery, [decoded.userId]);

      if (userResult.rows.length === 0) {
        return res.status(401).json({
          success: false,
          error: 'Invalid token. User not found.',
        });
      }

      const user = userResult.rows[0];

      if (!user.is_active) {
        return res.status(401).json({
          success: false,
          error: 'Account is deactivated.',
        });
      }

      // Add user to request object with tenant information
      req.user = {
        id: user.id,
        email: user.email,
        role: user.role,
        tenantId: user.tenant_id,
        tenantRole: user.tenant_role,
        tenantName: user.tenant_name
      };

      logger.debug('User authenticated', {
        userId: user.id,
        email: user.email,
        role: user.role,
        url: req.url,
        method: req.method,
      });

      next();

    } finally {
      client.release();
    }

  } catch (error) {
    logger.error('Authentication error', {
      error: error.message,
      url: req.url,
      method: req.method,
      ip: req.ip,
    });

    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        error: 'Invalid token.',
      });
    }

    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        error: 'Token expired.',
      });
    }

    res.status(500).json({
      success: false,
      error: 'Authentication failed.',
    });
  }
};

// Role-based authorization middleware
const requireRole = (roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required.',
      });
    }

    const userRoles = Array.isArray(req.user.role) ? req.user.role : [req.user.role];
    const requiredRoles = Array.isArray(roles) ? roles : [roles];
    
    const hasRole = requiredRoles.some(role => userRoles.includes(role));

    if (!hasRole) {
      return res.status(403).json({
        success: false,
        error: 'Insufficient permissions.',
      });
    }

    next();
  };
};

module.exports = authMiddleware;
module.exports.requireRole = requireRole;