const express = require('express');
const router = express.Router();
const logger = require('../utils/logger');
const DashboardService = require('../services/dashboardService');
const { recordHttpRequest } = require('../metrics');

const dashboardService = new DashboardService();

// Middleware for metrics recording
router.use((req, res, next) => {
  const originalSend = res.send;
  res.send = function(data) {
    const duration = (Date.now() - req.startTime) / 1000;
    recordHttpRequest(req.method, req.route?.path || req.path, res.statusCode, duration);
    return originalSend.call(this, data);
  };
  next();
});

// GET /dashboard/overview - Get dashboard overview
router.get('/overview', async (req, res, next) => {
  try {
    const {
      date_from,
      date_to,
      period = '30d'
    } = req.query;

    logger.debug('Fetching dashboard overview', {
      userId: req.user.id,
      date_from,
      date_to,
      period
    });

    const overview = await dashboardService.getOverview(req.user, {
      dateFrom: date_from,
      dateTo: date_to,
      period
    });

    res.json({
      success: true,
      data: overview
    });

  } catch (error) {
    next(error);
  }
});

// GET /dashboard/metrics - Get key metrics
router.get('/metrics', async (req, res, next) => {
  try {
    const {
      date_from,
      date_to,
      compare_period = false
    } = req.query;

    logger.debug('Fetching dashboard metrics', {
      userId: req.user.id,
      date_from,
      date_to,
      compare_period
    });

    const metrics = await dashboardService.getMetrics(req.user.id, {
      dateFrom: date_from,
      dateTo: date_to,
      comparePeriod: compare_period === 'true'
    });

    res.json({
      success: true,
      data: metrics
    });

  } catch (error) {
    next(error);
  }
});

// GET /dashboard/recent-activity - Get recent activity
router.get('/recent-activity', async (req, res, next) => {
  try {
    const { limit = 10 } = req.query;

    logger.debug('Fetching recent activity', {
      userId: req.user.id,
      limit
    });

    const activity = await dashboardService.getRecentActivity(req.user.id, {
      limit: parseInt(limit)
    });

    res.json({
      success: true,
      data: activity
    });

  } catch (error) {
    next(error);
  }
});

// GET /dashboard/top-links - Get top performing links
router.get('/top-links', async (req, res, next) => {
  try {
    const {
      metric = 'clicks',
      limit = 5,
      date_from,
      date_to
    } = req.query;

    logger.debug('Fetching top links', {
      userId: req.user.id,
      metric,
      limit,
      date_from,
      date_to
    });

    const topLinks = await dashboardService.getTopLinks(req.user.id, {
      metric,
      limit: parseInt(limit),
      dateFrom: date_from,
      dateTo: date_to
    });

    res.json({
      success: true,
      data: topLinks
    });

  } catch (error) {
    next(error);
  }
});

// GET /dashboard/conversion-funnel - Get conversion funnel data
router.get('/conversion-funnel', async (req, res, next) => {
  try {
    const {
      date_from,
      date_to,
      platform
    } = req.query;

    logger.debug('Fetching conversion funnel', {
      userId: req.user.id,
      date_from,
      date_to,
      platform
    });

    const funnel = await dashboardService.getConversionFunnel(req.user.id, {
      dateFrom: date_from,
      dateTo: date_to,
      platform
    });

    res.json({
      success: true,
      data: funnel
    });

  } catch (error) {
    next(error);
  }
});

// GET /dashboard/revenue-chart - Get revenue chart data
router.get('/revenue-chart', async (req, res, next) => {
  try {
    const {
      date_from,
      date_to,
      granularity = 'day',
      currency = 'USD'
    } = req.query;

    logger.debug('Fetching revenue chart', {
      userId: req.user.id,
      date_from,
      date_to,
      granularity,
      currency
    });

    const revenueChart = await dashboardService.getRevenueChart(req.user.id, {
      dateFrom: date_from,
      dateTo: date_to,
      granularity,
      currency
    });

    res.json({
      success: true,
      data: revenueChart
    });

  } catch (error) {
    next(error);
  }
});

// GET /dashboard/geographic-data - Get geographic performance data
router.get('/geographic-data', async (req, res, next) => {
  try {
    const {
      date_from,
      date_to,
      limit = 10
    } = req.query;

    logger.debug('Fetching geographic data', {
      userId: req.user.id,
      date_from,
      date_to,
      limit
    });

    const geoData = await dashboardService.getGeographicData(req.user.id, {
      dateFrom: date_from,
      dateTo: date_to,
      limit: parseInt(limit)
    });

    res.json({
      success: true,
      data: geoData
    });

  } catch (error) {
    next(error);
  }
});

// GET /dashboard/platform-performance - Get platform performance comparison
router.get('/platform-performance', async (req, res, next) => {
  try {
    const {
      date_from,
      date_to
    } = req.query;

    logger.debug('Fetching platform performance', {
      userId: req.user.id,
      date_from,
      date_to
    });

    const platformPerformance = await dashboardService.getPlatformPerformance(req.user.id, {
      dateFrom: date_from,
      dateTo: date_to
    });

    res.json({
      success: true,
      data: platformPerformance
    });

  } catch (error) {
    next(error);
  }
});

// GET /dashboard/alerts - Get dashboard alerts and notifications
router.get('/alerts', async (req, res, next) => {
  try {
    const { limit = 5 } = req.query;

    logger.debug('Fetching dashboard alerts', {
      userId: req.user.id,
      limit
    });

    const alerts = await dashboardService.getAlerts(req.user.id, {
      limit: parseInt(limit)
    });

    res.json({
      success: true,
      data: alerts
    });

  } catch (error) {
    next(error);
  }
});

module.exports = router;