const express = require('express');
const router = express.Router();
const axios = require('axios');
const logger = require('../utils/logger');
const { recordHttpRequest } = require('../metrics');

// Middleware for metrics recording
router.use((req, res, next) => {
  const originalSend = res.send;
  res.send = function(data) {
    const duration = (Date.now() - req.startTime) / 1000;
    recordHttpRequest(req.method, req.route?.path || req.path, res.statusCode, duration);
    return originalSend.call(this, data);
  };
  next();
});

// Helper function to make requests to link-tracking service
const linkTrackingService = axios.create({
  baseURL: process.env.LINK_TRACKING_SERVICE_URL || 'http://link-tracking:8080',
  timeout: 30000,
});

// GET /links - Get user's links
router.get('/', async (req, res, next) => {
  try {
    const {
      limit = 50,
      offset = 0,
      search,
      sort_by = 'created_at',
      sort_order = 'desc'
    } = req.query;

    logger.debug('Fetching user links', {
      userId: req.user.id,
      limit,
      offset,
      search,
      sort_by,
      sort_order
    });

    // Make request to link-tracking service
    const response = await linkTrackingService.get('/api/v1/links/list', {
      params: {
        tenant_id: req.user.tenant_id || req.user.id, // Use tenant_id from user context
        limit,
        offset,
        search,
        sort_by,
        sort_order
      }
    });

    res.json({
      success: true,
      data: response.data.data
    });

  } catch (error) {
    if (error.response) {
      logger.error('Link tracking service error', {
        status: error.response.status,
        data: error.response.data,
        userId: req.user.id
      });
      
      return res.status(error.response.status).json({
        success: false,
        error: error.response.data.error || 'Failed to fetch links'
      });
    }
    
    next(error);
  }
});

// POST /links - Create new link
router.post('/', async (req, res, next) => {
  try {
    const linkData = {
      ...req.body,
      tenant_id: req.user.tenant_id || req.user.id // Use tenant_id instead of user_id
    };

    logger.info('Creating new link', {
      userId: req.user.id,
      originalUrl: req.body.original_url,
      title: req.body.title
    });

    // Make request to link-tracking service
    const response = await linkTrackingService.post('/api/v1/links', linkData);

    res.status(201).json({
      success: true,
      data: response.data.data,
      message: 'Link created successfully'
    });

  } catch (error) {
    if (error.response) {
      logger.error('Link creation failed', {
        status: error.response.status,
        data: error.response.data,
        userId: req.user.id
      });
      
      return res.status(error.response.status).json({
        success: false,
        error: error.response.data.error || 'Failed to create link'
      });
    }
    
    next(error);
  }
});

// GET /links/:id - Get specific link
router.get('/:id', async (req, res, next) => {
  try {
    const { id } = req.params;

    logger.debug('Fetching link details', {
      userId: req.user.id,
      linkId: id
    });

    // Make request to link-tracking service
    const response = await linkTrackingService.get(`/api/v1/links/${id}`, {
      params: {
        tenant_id: req.user.tenant_id || req.user.id
      }
    });

    res.json({
      success: true,
      data: response.data.data
    });

  } catch (error) {
    if (error.response) {
      if (error.response.status === 404) {
        return res.status(404).json({
          success: false,
          error: 'Link not found'
        });
      }
      
      logger.error('Link fetch failed', {
        status: error.response.status,
        data: error.response.data,
        userId: req.user.id,
        linkId: req.params.id
      });
      
      return res.status(error.response.status).json({
        success: false,
        error: error.response.data.error || 'Failed to fetch link'
      });
    }
    
    next(error);
  }
});

// PUT /links/:id - Update link
router.put('/:id', async (req, res, next) => {
  try {
    const { id } = req.params;
    const updateData = {
      ...req.body,
      tenant_id: req.user.tenant_id || req.user.id
    };

    logger.info('Updating link', {
      userId: req.user.id,
      linkId: id,
      updates: Object.keys(req.body)
    });

    // Make request to link-tracking service
    const response = await linkTrackingService.put(`/api/v1/links/${id}`, updateData);

    res.json({
      success: true,
      data: response.data.data,
      message: 'Link updated successfully'
    });

  } catch (error) {
    if (error.response) {
      if (error.response.status === 404) {
        return res.status(404).json({
          success: false,
          error: 'Link not found'
        });
      }
      
      logger.error('Link update failed', {
        status: error.response.status,
        data: error.response.data,
        userId: req.user.id,
        linkId: req.params.id
      });
      
      return res.status(error.response.status).json({
        success: false,
        error: error.response.data.error || 'Failed to update link'
      });
    }
    
    next(error);
  }
});

// DELETE /links/:id - Delete link
router.delete('/:id', async (req, res, next) => {
  try {
    const { id } = req.params;

    logger.info('Deleting link', {
      userId: req.user.id,
      linkId: id
    });

    // Make request to link-tracking service
    await linkTrackingService.delete(`/api/v1/links/${id}`, {
      params: {
        tenant_id: req.user.tenant_id || req.user.id
      }
    });

    res.json({
      success: true,
      message: 'Link deleted successfully'
    });

  } catch (error) {
    if (error.response) {
      if (error.response.status === 404) {
        return res.status(404).json({
          success: false,
          error: 'Link not found'
        });
      }
      
      logger.error('Link deletion failed', {
        status: error.response.status,
        data: error.response.data,
        userId: req.user.id,
        linkId: req.params.id
      });
      
      return res.status(error.response.status).json({
        success: false,
        error: error.response.data.error || 'Failed to delete link'
      });
    }
    
    next(error);
  }
});

// GET /links/:id/analytics - Get link analytics
router.get('/:id/analytics', async (req, res, next) => {
  try {
    const { id } = req.params;
    const {
      date_from,
      date_to,
      granularity = 'day'
    } = req.query;

    logger.debug('Fetching link analytics', {
      userId: req.user.id,
      linkId: id,
      date_from,
      date_to,
      granularity
    });

    // Make request to analytics service
    const analyticsService = axios.create({
      baseURL: process.env.ANALYTICS_SERVICE_URL || 'http://analytics:3002',
      timeout: 30000,
    });

    const response = await analyticsService.get(`/api/analytics/links/${id}`, {
      params: {
        tenant_id: req.user.tenant_id || req.user.id,
        date_from,
        date_to,
        granularity
      }
    });

    res.json({
      success: true,
      data: response.data.data
    });

  } catch (error) {
    if (error.response) {
      logger.error('Analytics fetch failed', {
        status: error.response.status,
        data: error.response.data,
        userId: req.user.id,
        linkId: req.params.id
      });
      
      return res.status(error.response.status).json({
        success: false,
        error: error.response.data.error || 'Failed to fetch analytics'
      });
    }
    
    next(error);
  }
});

// POST /links/:id/qr-code - Generate QR code for link
router.post('/:id/qr-code', async (req, res, next) => {
  try {
    const { id } = req.params;
    const { size = 200, format = 'png' } = req.body;

    logger.debug('Generating QR code', {
      userId: req.user.id,
      linkId: id,
      size,
      format
    });

    // Make request to link-tracking service
    const response = await linkTrackingService.post(`/api/v1/links/${id}/qr-code`, {
      tenant_id: req.user.tenant_id || req.user.id,
      size,
      format
    });

    res.json({
      success: true,
      data: response.data.data
    });

  } catch (error) {
    if (error.response) {
      logger.error('QR code generation failed', {
        status: error.response.status,
        data: error.response.data,
        userId: req.user.id,
        linkId: req.params.id
      });
      
      return res.status(error.response.status).json({
        success: false,
        error: error.response.data.error || 'Failed to generate QR code'
      });
    }
    
    next(error);
  }
});

// POST /links/bulk - Create multiple links
router.post('/bulk', async (req, res, next) => {
  try {
    const { links } = req.body;

    if (!Array.isArray(links) || links.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Links array is required and must not be empty'
      });
    }

    logger.info('Creating bulk links', {
      userId: req.user.id,
      count: links.length
    });

    // Add tenant_id to each link
    const linksWithTenantId = links.map(link => ({
      ...link,
      tenant_id: req.user.tenant_id || req.user.id
    }));

    // Make request to link-tracking service
    const response = await linkTrackingService.post('/api/v1/links/bulk', {
      links: linksWithTenantId
    });

    res.status(201).json({
      success: true,
      data: response.data.data,
      message: 'Bulk links created successfully'
    });

  } catch (error) {
    if (error.response) {
      logger.error('Bulk link creation failed', {
        status: error.response.status,
        data: error.response.data,
        userId: req.user.id
      });
      
      return res.status(error.response.status).json({
        success: false,
        error: error.response.data.error || 'Failed to create bulk links'
      });
    }
    
    next(error);
  }
});

module.exports = router;