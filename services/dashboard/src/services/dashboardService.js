const logger = require('../utils/logger');
const redis = require('../redis');
const axios = require('axios');

// Database pool will be loaded when needed
function getPool() {
  const { pool } = require('../database');
  return pool;
}

class DashboardService {
  constructor() {
    this.analyticsService = axios.create({
      baseURL: process.env.ANALYTICS_SERVICE_URL || 'http://analytics:3002',
      timeout: 30000,
    });
    
    this.linkTrackingService = axios.create({
      baseURL: process.env.LINK_TRACKING_SERVICE_URL || 'http://link-tracking:8080',
      timeout: 30000,
    });
    
    this.integrationService = axios.create({
      baseURL: process.env.INTEGRATION_SERVICE_URL || 'http://integration:3001',
      timeout: 30000,
    });
  }

  async getOverview(user, options = {}) {
    try {
      const { period = '30d' } = options;

      // Calculate date range based on period
      const endDate = new Date();
      const startDate = new Date();

      switch (period) {
        case '7d':
          startDate.setDate(endDate.getDate() - 7);
          break;
        case '30d':
          startDate.setDate(endDate.getDate() - 30);
          break;
        case '90d':
          startDate.setDate(endDate.getDate() - 90);
          break;
        default:
          startDate.setDate(endDate.getDate() - 30);
      }

      const dateFrom = startDate.toISOString().split('T')[0];
      const dateTo = endDate.toISOString().split('T')[0];

      // Use tenant_id for proper multi-tenant isolation
      const tenantId = user.tenantId || user.id; // Fallback to user.id if no tenant

      // Fetch data from multiple services in parallel
      const [analyticsResponse, linksResponse, integrationsResponse] = await Promise.allSettled([
        this.analyticsService.get('/api/analytics/summary', {
          params: { tenant_id: tenantId, date_from: dateFrom, date_to: dateTo }
        }),
        this.linkTrackingService.get('/api/links', {
          params: { tenant_id: tenantId, limit: 1 }
        }),
        this.integrationService.get('/api/integrations', {
          params: { tenant_id: tenantId }
        })
      ]);

      // Extract data from successful responses
      const analytics = analyticsResponse.status === 'fulfilled' 
        ? analyticsResponse.value.data.data 
        : this.getDefaultAnalytics();

      const totalLinks = linksResponse.status === 'fulfilled'
        ? linksResponse.value.data.data.pagination?.total || 0
        : 0;

      const integrations = integrationsResponse.status === 'fulfilled'
        ? integrationsResponse.value.data.data || []
        : [];

      // Get recent activity
      const recentActivity = await this.getRecentActivity(tenantId, { limit: 5 });

      return {
        period,
        summary: {
          total_clicks: analytics.summary?.total_clicks || 0,
          total_conversions: analytics.summary?.total_conversions || 0,
          total_revenue: analytics.summary?.total_revenue || 0,
          conversion_rate: analytics.summary?.conversion_rate || 0,
          total_links: totalLinks,
          active_integrations: integrations.filter(i => i.is_active).length
        },
        trend: analytics.trend || [],
        platforms: analytics.platforms || [],
        recent_activity: recentActivity,
        quick_stats: {
          avg_order_value: analytics.summary?.avg_order_value || 0,
          top_country: this.getTopCountryFromTrend(analytics.geographic || []),
          best_performing_day: this.getBestPerformingDay(analytics.trend || [])
        }
      };

    } catch (error) {
      logger.error('Failed to get dashboard overview', {
        userId: user.id,
        tenantId: user.tenantId,
        error: error.message
      });
      throw error;
    }
  }

  async getMetrics(userId, options = {}) {
    try {
      const { dateFrom, dateTo, comparePeriod } = options;

      // Get current period metrics
      const currentMetrics = await this.analyticsService.get('/api/analytics/summary', {
        params: { user_id: userId, date_from: dateFrom, date_to: dateTo }
      });

      let comparison = null;

      if (comparePeriod && dateFrom && dateTo) {
        // Calculate previous period dates
        const start = new Date(dateFrom);
        const end = new Date(dateTo);
        const periodLength = end.getTime() - start.getTime();
        
        const prevEnd = new Date(start.getTime() - 1);
        const prevStart = new Date(prevEnd.getTime() - periodLength);

        try {
          const previousMetrics = await this.analyticsService.get('/api/analytics/summary', {
            params: {
              user_id: userId,
              date_from: prevStart.toISOString().split('T')[0],
              date_to: prevEnd.toISOString().split('T')[0]
            }
          });

          comparison = this.calculateMetricsComparison(
            currentMetrics.data.data.summary,
            previousMetrics.data.data.summary
          );
        } catch (error) {
          logger.warn('Failed to fetch comparison metrics', { userId, error: error.message });
        }
      }

      return {
        current: currentMetrics.data.data.summary,
        comparison,
        period: { from: dateFrom, to: dateTo }
      };

    } catch (error) {
      logger.error('Failed to get dashboard metrics', {
        userId,
        error: error.message
      });
      throw error;
    }
  }

  async getRecentActivity(tenantId, options = {}) {
    try {
      const { limit = 10 } = options;

      const client = await getPool().connect();

      try {
        // Get recent clicks and conversions using tenant_id for proper isolation
        const activityQuery = `
          SELECT
            'click' as type,
            c.id,
            c.clicked_at as created_at,
            c.country,
            c.device_type,
            l.title as link_title,
            l.short_code,
            null as revenue
          FROM clicks c
          JOIN links l ON c.link_id = l.id
          WHERE l.tenant_id = $1

          UNION ALL

          SELECT
            'conversion' as type,
            a.id,
            a.created_at,
            c.country,
            c.device_type,
            l.title as link_title,
            l.short_code,
            o.total_amount as revenue
          FROM attributions a
          JOIN clicks c ON a.click_id = c.id
          JOIN links l ON a.link_id = l.id
          JOIN orders o ON a.order_id = o.id
          WHERE l.tenant_id = $1

          ORDER BY created_at DESC
          LIMIT $2
        `;

        const result = await client.query(activityQuery, [tenantId, limit]);

        return result.rows.map(row => ({
          id: row.id,
          type: row.type,
          created_at: row.created_at,
          country: row.country,
          device_type: row.device_type,
          link: {
            title: row.link_title,
            short_code: row.short_code
          },
          ...(row.revenue && { revenue: parseFloat(row.revenue) })
        }));

      } finally {
        client.release();
      }

    } catch (error) {
      logger.error('Failed to get recent activity', {
        tenantId,
        error: error.message
      });
      return [];
    }
  }

  async getTopLinks(userId, options = {}) {
    try {
      const { metric = 'clicks', limit = 5, dateFrom, dateTo } = options;

      const response = await this.analyticsService.get('/api/analytics/top-performers', {
        params: {
          user_id: userId,
          metric,
          limit,
          date_from: dateFrom,
          date_to: dateTo
        }
      });

      return response.data.data;

    } catch (error) {
      logger.error('Failed to get top links', {
        userId,
        error: error.message
      });
      return [];
    }
  }

  async getConversionFunnel(userId, options = {}) {
    try {
      const { dateFrom, dateTo, platform } = options;

      const response = await this.analyticsService.get('/api/reports/conversion-funnel', {
        params: {
          user_id: userId,
          date_from: dateFrom,
          date_to: dateTo,
          platform
        }
      });

      return response.data.data;

    } catch (error) {
      logger.error('Failed to get conversion funnel', {
        userId,
        error: error.message
      });
      return this.getDefaultFunnel();
    }
  }

  async getRevenueChart(userId, options = {}) {
    try {
      const { dateFrom, dateTo, granularity = 'day', currency = 'USD' } = options;

      const response = await this.analyticsService.get('/api/analytics/time-series', {
        params: {
          user_id: userId,
          metric: 'revenue',
          granularity,
          date_from: dateFrom,
          date_to: dateTo
        }
      });

      return {
        currency,
        data: response.data.data,
        granularity
      };

    } catch (error) {
      logger.error('Failed to get revenue chart', {
        userId,
        error: error.message
      });
      return { currency, data: [], granularity };
    }
  }

  async getGeographicData(userId, options = {}) {
    try {
      const { dateFrom, dateTo, limit = 10 } = options;

      const response = await this.analyticsService.get('/api/analytics/geographic', {
        params: {
          user_id: userId,
          date_from: dateFrom,
          date_to: dateTo,
          level: 'country'
        }
      });

      return response.data.data.slice(0, limit);

    } catch (error) {
      logger.error('Failed to get geographic data', {
        userId,
        error: error.message
      });
      return [];
    }
  }

  async getPlatformPerformance(userId, options = {}) {
    try {
      const { dateFrom, dateTo } = options;

      const response = await this.analyticsService.get('/api/reports/platform-comparison', {
        params: {
          user_id: userId,
          date_from: dateFrom,
          date_to: dateTo,
          platforms: 'shopify,woocommerce,amazon',
          metrics: 'clicks,conversions,revenue'
        }
      });

      return response.data.data;

    } catch (error) {
      logger.error('Failed to get platform performance', {
        userId,
        error: error.message
      });
      return [];
    }
  }

  async getAlerts(userId, options = {}) {
    try {
      const { limit = 5 } = options;
      
      // This is a simplified version - in production you'd have a more sophisticated alerting system
      const alerts = [];

      // Check for low-performing links
      const topLinks = await this.getTopLinks(userId, { metric: 'conversion_rate', limit: 3 });
      if (topLinks.length > 0 && topLinks[0].conversion_rate < 1) {
        alerts.push({
          type: 'warning',
          title: 'Low Conversion Rate',
          message: 'Your top link has a conversion rate below 1%',
          created_at: new Date().toISOString(),
          action_url: '/analytics'
        });
      }

      // Check for inactive integrations
      try {
        const integrationsResponse = await this.integrationService.get('/api/integrations', {
          params: { user_id: userId }
        });
        
        const inactiveIntegrations = integrationsResponse.data.data.filter(i => !i.is_active);
        if (inactiveIntegrations.length > 0) {
          alerts.push({
            type: 'info',
            title: 'Inactive Integrations',
            message: `You have ${inactiveIntegrations.length} inactive integration(s)`,
            created_at: new Date().toISOString(),
            action_url: '/integrations'
          });
        }
      } catch (error) {
        // Ignore integration service errors for alerts
      }

      return alerts.slice(0, limit);

    } catch (error) {
      logger.error('Failed to get alerts', {
        userId,
        error: error.message
      });
      return [];
    }
  }

  // Helper methods
  getDefaultAnalytics() {
    return {
      summary: {
        total_clicks: 0,
        total_conversions: 0,
        total_revenue: 0,
        conversion_rate: 0,
        avg_order_value: 0
      },
      trend: [],
      platforms: [],
      geographic: []
    };
  }

  getDefaultFunnel() {
    return {
      steps: [
        { name: 'Clicks', value: 0 },
        { name: 'Visits', value: 0 },
        { name: 'Conversions', value: 0 }
      ]
    };
  }

  calculateMetricsComparison(current, previous) {
    const calculateChange = (curr, prev) => {
      if (prev === 0) return curr > 0 ? 100 : 0;
      return ((curr - prev) / prev) * 100;
    };

    return {
      clicks: {
        current: current.total_clicks,
        previous: previous.total_clicks,
        change: calculateChange(current.total_clicks, previous.total_clicks)
      },
      conversions: {
        current: current.total_conversions,
        previous: previous.total_conversions,
        change: calculateChange(current.total_conversions, previous.total_conversions)
      },
      revenue: {
        current: current.total_revenue,
        previous: previous.total_revenue,
        change: calculateChange(current.total_revenue, previous.total_revenue)
      },
      conversion_rate: {
        current: current.conversion_rate,
        previous: previous.conversion_rate,
        change: current.conversion_rate - previous.conversion_rate
      }
    };
  }

  getTopCountryFromTrend(geographic) {
    if (!geographic || geographic.length === 0) return 'Unknown';
    return geographic[0]?.country || 'Unknown';
  }

  getBestPerformingDay(trend) {
    if (!trend || trend.length === 0) return null;
    
    const bestDay = trend.reduce((best, current) => {
      return (current.clicks || 0) > (best.clicks || 0) ? current : best;
    }, trend[0]);

    return bestDay?.date || null;
  }
}

module.exports = DashboardService;