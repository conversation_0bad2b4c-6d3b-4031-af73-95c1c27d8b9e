const { expect } = require('chai');
const sinon = require('sinon');
const DashboardService = require('../services/dashboardService');

describe('DashboardService', () => {
  let dashboardService;
  let mockAnalyticsService;
  let mockLinkTrackingService;
  let mockIntegrationService;
  let mockPool;
  let mockClient;

  beforeEach(() => {
    // Mock axios instances
    mockAnalyticsService = {
      get: sinon.stub()
    };
    
    mockLinkTrackingService = {
      get: sinon.stub()
    };
    
    mockIntegrationService = {
      get: sinon.stub()
    };

    // Mock database
    mockClient = {
      query: sinon.stub(),
      release: sinon.stub()
    };
    
    mockPool = {
      connect: sinon.stub().resolves(mockClient)
    };

    dashboardService = new DashboardService();
    dashboardService.analyticsService = mockAnalyticsService;
    dashboardService.linkTrackingService = mockLinkTrackingService;
    dashboardService.integrationService = mockIntegrationService;
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('getOverview', () => {
    it('should return dashboard overview with tenant isolation', async () => {
      const user = {
        id: 'user-123',
        tenantId: 'tenant-123',
        email: '<EMAIL>'
      };

      const options = {
        period: '30d'
      };

      // Mock service responses
      const mockAnalyticsResponse = {
        status: 'fulfilled',
        value: {
          data: {
            data: {
              summary: {
                total_clicks: 1000,
                total_conversions: 50,
                total_revenue: 2500,
                conversion_rate: 5.0,
                avg_order_value: 50.0
              },
              trend: [
                { date: '2024-01-01', clicks: 100, conversions: 5, revenue: 250 }
              ],
              platforms: [
                { platform: 'shopify', clicks: 600, conversions: 30 }
              ]
            }
          }
        }
      };

      const mockLinksResponse = {
        status: 'fulfilled',
        value: {
          data: {
            data: {
              pagination: { total: 25 }
            }
          }
        }
      };

      const mockIntegrationsResponse = {
        status: 'fulfilled',
        value: {
          data: {
            data: [
              { id: 'int-1', platform: 'shopify', is_active: true },
              { id: 'int-2', platform: 'woocommerce', is_active: false }
            ]
          }
        }
      };

      // Mock recent activity query
      const mockRecentActivityResult = {
        rows: [
          {
            id: 'activity-1',
            type: 'click',
            created_at: new Date().toISOString(),
            country: 'US',
            device_type: 'desktop',
            link_title: 'Test Link',
            short_code: 'test123',
            revenue: null
          }
        ]
      };

      // Setup Promise.allSettled mock
      sinon.stub(Promise, 'allSettled').resolves([
        mockAnalyticsResponse,
        mockLinksResponse,
        mockIntegrationsResponse
      ]);

      // Mock database connection for recent activity
      const { getPool } = require('../database');
      sinon.stub({ getPool }, 'getPool').returns(mockPool);
      mockClient.query.resolves(mockRecentActivityResult);

      const result = await dashboardService.getOverview(user, options);

      expect(result).to.have.property('period', '30d');
      expect(result).to.have.property('summary');
      expect(result).to.have.property('trend');
      expect(result).to.have.property('platforms');
      expect(result).to.have.property('recent_activity');
      expect(result).to.have.property('quick_stats');

      expect(result.summary.total_clicks).to.equal(1000);
      expect(result.summary.total_conversions).to.equal(50);
      expect(result.summary.total_links).to.equal(25);
      expect(result.summary.active_integrations).to.equal(1);

      expect(result.recent_activity).to.have.length(1);
      expect(result.recent_activity[0].type).to.equal('click');

      // Verify tenant_id was used in service calls
      const analyticsCall = Promise.allSettled.getCall(0).args[0][0];
      // This would be the analytics service call - we'd need to verify tenant_id is used
    });

    it('should handle service failures gracefully', async () => {
      const user = {
        id: 'user-123',
        tenantId: 'tenant-123'
      };

      // Mock failed service responses
      const mockFailedResponse = {
        status: 'rejected',
        reason: new Error('Service unavailable')
      };

      sinon.stub(Promise, 'allSettled').resolves([
        mockFailedResponse,
        mockFailedResponse,
        mockFailedResponse
      ]);

      // Mock empty recent activity
      const { getPool } = require('../database');
      sinon.stub({ getPool }, 'getPool').returns(mockPool);
      mockClient.query.resolves({ rows: [] });

      const result = await dashboardService.getOverview(user, {});

      // Should return default values when services fail
      expect(result.summary.total_clicks).to.equal(0);
      expect(result.summary.total_conversions).to.equal(0);
      expect(result.summary.total_revenue).to.equal(0);
      expect(result.summary.total_links).to.equal(0);
      expect(result.summary.active_integrations).to.equal(0);
    });

    it('should use fallback when no tenant_id is provided', async () => {
      const user = {
        id: 'user-123'
        // No tenantId
      };

      sinon.stub(Promise, 'allSettled').resolves([
        { status: 'rejected', reason: new Error('No tenant') },
        { status: 'rejected', reason: new Error('No tenant') },
        { status: 'rejected', reason: new Error('No tenant') }
      ]);

      const { getPool } = require('../database');
      sinon.stub({ getPool }, 'getPool').returns(mockPool);
      mockClient.query.resolves({ rows: [] });

      const result = await dashboardService.getOverview(user, {});

      // Should still work with user.id as fallback
      expect(result).to.have.property('summary');
    });
  });

  describe('getRecentActivity', () => {
    it('should return recent activity for tenant', async () => {
      const tenantId = 'tenant-123';
      const options = { limit: 5 };

      const mockActivityResult = {
        rows: [
          {
            id: 'click-1',
            type: 'click',
            created_at: new Date().toISOString(),
            country: 'US',
            device_type: 'desktop',
            link_title: 'Test Link',
            short_code: 'test123',
            revenue: null
          },
          {
            id: 'conv-1',
            type: 'conversion',
            created_at: new Date().toISOString(),
            country: 'CA',
            device_type: 'mobile',
            link_title: 'Product Link',
            short_code: 'prod456',
            revenue: '99.99'
          }
        ]
      };

      const { getPool } = require('../database');
      sinon.stub({ getPool }, 'getPool').returns(mockPool);
      mockClient.query.resolves(mockActivityResult);

      const result = await dashboardService.getRecentActivity(tenantId, options);

      expect(result).to.have.length(2);
      expect(result[0].type).to.equal('click');
      expect(result[0].link.title).to.equal('Test Link');
      expect(result[1].type).to.equal('conversion');
      expect(result[1].revenue).to.equal(99.99);

      // Verify tenant_id was used in query
      const queryCall = mockClient.query.getCall(0);
      expect(queryCall.args[1][0]).to.equal(tenantId);
    });

    it('should handle database errors gracefully', async () => {
      const tenantId = 'tenant-123';
      
      const { getPool } = require('../database');
      sinon.stub({ getPool }, 'getPool').returns(mockPool);
      mockClient.query.rejects(new Error('Database error'));

      const result = await dashboardService.getRecentActivity(tenantId);

      expect(result).to.be.an('array');
      expect(result).to.have.length(0);
    });
  });
});
