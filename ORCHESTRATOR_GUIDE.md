# 🤖 Agent Orchestrator Complete Usage Guide

This guide shows you how to use the Agent Orchestrator system step by step.

## 📋 What You Just Saw in the Demo

The demo showed the orchestrator:
- ✅ **Parsed 4 tasks** from the test file
- ✅ **Analyzed dependencies** and found 2 tasks ready to run
- ✅ **Identified parallel groups** - both ready tasks can run simultaneously
- ✅ **Would spawn 2 agents** if tmux was available

## 🛠️ Complete Setup Instructions

### 1. Install Dependencies

```bash
# Create virtual environment
python -m venv orchestrator-env
source orchestrator-env/bin/activate

# Install Python dependencies
pip install click

# Install tmux (required for agent sessions)
# On Arch Linux:
sudo pacman -S tmux
# On Ubuntu/Debian:
sudo apt install tmux
# On macOS:
brew install tmux
```

### 2. Verify Git Repository

```bash
# Make sure you're in a git repository
git status

# If not initialized:
git init
git add .
git commit -m "Initial commit"
```

## 📝 Task File Format

Create a `tasks.md` file using this exact format:

```markdown
# Your Project Tasks

## Task 1: Your Task Title

- **Branch**: feature/your-branch-name
- **Status**: unclaimed
- **Session**: none
- **Priority**: high|medium|low
- **Dependencies**: none (or "Task Title 1, Task Title 2")
- **Estimated Time**: 2 hours
- **File Path**: path/to/relevant/files

Description of what needs to be implemented.

## Task 2: Another Task

- **Branch**: feature/another-branch
- **Status**: unclaimed
- **Session**: none
- **Priority**: medium
- **Dependencies**: Your Task Title
- **Estimated Time**: 1 hour
- **File Path**: another/path

This task depends on the first one being completed.
```

**Important Format Notes:**
- Task headers must be `## Task N: Title` (with number)
- All fields must use `- **Field**: value` format
- Dependencies use task titles, not IDs
- Status should be `unclaimed` for new tasks

## 🚀 Basic Usage

### Step 1: View Available Tasks
```bash
python run_orchestrator.py tasks
```

### Step 2: Check What's Ready to Run
```bash
python run_orchestrator.py spawn --dry-run
```

### Step 3: Spawn Agents
```bash
# Spawn up to 3 agents for ready tasks
python run_orchestrator.py spawn --max-agents 3
```

### Step 4: Monitor Progress
```bash
# Check overall status
python run_orchestrator.py status

# View agent details
python run_orchestrator.py agent --all

# Create monitoring dashboard
python run_orchestrator.py monitor --dashboard
```

## 📊 Monitoring Commands

### Real-time Monitoring
```bash
# Start background monitoring system
python run_orchestrator.py monitor --start

# Create tmux dashboard (splits screen to show all agents)
python run_orchestrator.py monitor --dashboard

# Generate comprehensive health report
python run_orchestrator.py monitor --health-report

# Export metrics to file
python run_orchestrator.py monitor --export metrics.json
```

### Status Information
```bash
# Basic status
python run_orchestrator.py status

# Detailed JSON status
python run_orchestrator.py status --format json

# Specific agent details
python run_orchestrator.py agent agent-create-dashboard-component-a1b2c3d4

# All agents summary
python run_orchestrator.py agent --all
```

## 🎛️ Task Management

### Completing Tasks
```bash
# Mark task as completed (automatically merges changes)
python run_orchestrator.py complete --task-id create-dashboard-component

# Complete by agent ID
python run_orchestrator.py complete --agent-id agent-xyz --no-merge
```

### Terminating Agents
```bash
# Terminate specific task
python run_orchestrator.py terminate --task-id stuck-task

# Terminate specific agent
python run_orchestrator.py terminate --agent-id agent-xyz --reason "Manual intervention"

# Terminate all agents (emergency stop)
python run_orchestrator.py terminate --all
```

## 🧹 Cleanup Operations

### Automatic Cleanup
```bash
# Smart automatic cleanup based on thresholds
python run_orchestrator.py cleanup --auto

# See what would be cleaned without doing it
python run_orchestrator.py cleanup --dry-run
```

### Targeted Cleanup
```bash
# Clean up completed tasks older than default retention period
python run_orchestrator.py cleanup --completed

# Clean up failed tasks
python run_orchestrator.py cleanup --failed

# Clean up orphaned resources (worktrees/sessions without active tasks)
python run_orchestrator.py cleanup --orphaned

# Clean up old log files
python run_orchestrator.py cleanup --logs
```

### Resource Management
```bash
# Free specific amount of disk space
python run_orchestrator.py cleanup --disk-space 2.5

# Force cleanup of all non-active resources (use with caution)
python run_orchestrator.py cleanup --force
```

## 🔍 Debugging and Troubleshooting

### Check Agent Health
```bash
# View detailed agent status
python run_orchestrator.py agent agent-id

# Generate health report
python run_orchestrator.py monitor --health-report

# Comprehensive system report
python run_orchestrator.py report
```

### Manual Intervention
```bash
# Attach to agent's tmux session for manual debugging
tmux attach -t agent-task-id

# List all tmux sessions
tmux list-sessions

# Kill stuck session manually
tmux kill-session -t agent-task-id
```

### Common Issues

1. **"No tasks found"**: Check task file format, ensure task numbers are sequential
2. **"tmux not found"**: Install tmux with your package manager
3. **"No ready tasks"**: Check task dependencies are satisfied
4. **Agent stuck**: Use `terminate` command or attach to session manually

## 🏆 Example Complete Workflow

Let's walk through a complete example:

### 1. Create tasks.md
```markdown
# E-commerce Feature Development

## Task 1: Create Product Catalog API

- **Branch**: feature/product-catalog-api
- **Status**: unclaimed
- **Session**: none
- **Priority**: high
- **Dependencies**: none
- **Estimated Time**: 3 hours
- **File Path**: backend/app/products

Implement REST API endpoints for product catalog with filtering, search, and pagination.

## Task 2: Build Product List Component

- **Branch**: feature/product-list-ui
- **Status**: unclaimed
- **Session**: none
- **Priority**: high
- **Dependencies**: none
- **Estimated Time**: 2 hours
- **File Path**: frontend/src/components/products

Create responsive product list component with grid/list view toggle.

## Task 3: Integrate Product List with API

- **Branch**: feature/product-integration
- **Status**: unclaimed
- **Session**: none
- **Priority**: medium
- **Dependencies**: Create Product Catalog API, Build Product List Component
- **Estimated Time**: 1 hour
- **File Path**: frontend/src/components/products

Connect the product list component to the catalog API with loading states and error handling.
```

### 2. Run the Orchestrator
```bash
# Check what tasks are ready
python run_orchestrator.py spawn --dry-run
# Output: Would spawn 2 agents (API and UI tasks can run in parallel)

# Spawn the agents
python run_orchestrator.py spawn --max-agents 3

# Monitor progress
python run_orchestrator.py monitor --dashboard
```

### 3. The System Automatically:
- Creates git worktrees: `worktrees/agent-create-product-catalog-api/`
- Spawns tmux sessions with specialized prompts
- Monitors agent progress and health
- Detects when both tasks complete
- Makes the integration task ready to run
- Spawns agent for integration task
- Merges all completed work
- Cleans up resources

### 4. You Can:
```bash
# Check final status
python run_orchestrator.py report

# Clean up
python run_orchestrator.py cleanup --completed
```

## ⚙️ Configuration

Key settings in `orchestrator/config/settings.py`:

- `MAX_CONCURRENT_AGENTS`: How many agents to run simultaneously (default: 5)
- `CLAUDE_CODE_COMMAND`: Command to invoke Claude Code (default: "claude-code")
- `STATUS_CHECK_INTERVAL`: Health check frequency in seconds (default: 60)
- `CLEANUP_RETENTION_DAYS`: How long to keep completed tasks (default: 14)

## 🎯 Pro Tips

1. **Start Small**: Begin with 1-2 simple tasks to get familiar
2. **Use Dry Run**: Always use `--dry-run` first to see what will happen
3. **Monitor Actively**: Use the dashboard to watch agent progress
4. **Clean Regularly**: Run automatic cleanup to maintain performance
5. **Save State**: The system saves state automatically, safe to restart

## 🚨 Safety Features

- **Isolated Environments**: Each agent works in separate git worktree
- **Automatic Cleanup**: Failed/stuck agents are cleaned up automatically
- **Resource Limits**: Built-in limits prevent resource exhaustion
- **State Recovery**: System can recover from crashes
- **Manual Override**: You can always intervene manually

This system transforms parallel development from complex coordination into simple task definition. Just write your tasks and let the orchestrator handle the rest!

## 📞 Getting Help

- Use `--help` on any command for detailed options
- Check `python run_orchestrator.py report` for system overview
- Use `--verbose` flag for detailed logging
- Attach to agent sessions with tmux for debugging

The Agent Orchestrator makes parallel AI development as easy as writing a todo list! 🎉