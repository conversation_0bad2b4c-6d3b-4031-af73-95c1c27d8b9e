/**
 * Advanced Log Analysis Service
 * Provides intelligent analysis, anomaly detection, and forensic capabilities
 */

const { EventEmitter } = require('events');

class LogAnalyzer extends EventEmitter {
  constructor(db, config = {}) {
    super();
    this.db = db;
    this.config = {
      anomalyThreshold: 2.0, // Standard deviations from normal
      patternWindow: 24 * 60 * 60 * 1000, // 24 hours
      minEventsForAnalysis: 10,
      enableMachineLearning: false, // Future ML integration
      analysisInterval: 60 * 60 * 1000, // 1 hour
      ...config
    };

    // Cache for pattern analysis
    this.patternCache = new Map();
    this.behaviorBaselines = new Map();
    this.anomalyPatterns = new Map();

    // Start periodic analysis
    this.startPeriodicAnalysis();
  }

  /**
   * Start periodic log analysis
   */
  startPeriodicAnalysis() {
    this.analysisInterval = setInterval(() => {
      this.performPeriodicAnalysis().catch(error => {
        console.error('Periodic analysis failed:', error);
      });
    }, this.config.analysisInterval);
  }

  /**
   * Perform comprehensive log analysis
   */
  async performPeriodicAnalysis() {
    console.log('Starting periodic log analysis...');

    try {
      const analysisResults = await Promise.all([
        this.detectAnomalies(),
        this.analyzeUserBehavior(),
        this.detectSecurityPatterns(),
        this.analyzePerformanceIssues(),
        this.detectComplianceViolations()
      ]);

      const [anomalies, behavior, security, performance, compliance] = analysisResults;

      const summary = {
        timestamp: new Date().toISOString(),
        anomalies: anomalies.length,
        behaviorAlerts: behavior.filter(b => b.riskLevel === 'high').length,
        securityIncidents: security.filter(s => s.severity >= 7).length,
        performanceIssues: performance.filter(p => p.impact === 'high').length,
        complianceViolations: compliance.length
      };

      // Emit analysis complete event
      this.emit('analysisComplete', {
        summary,
        details: { anomalies, behavior, security, performance, compliance }
      });

      console.log('Periodic analysis completed:', summary);
    } catch (error) {
      console.error('Analysis failed:', error);
      this.emit('analysisError', error);
    }
  }

  /**
   * Detect anomalies in audit logs
   */
  async detectAnomalies() {
    const timeWindow = new Date(Date.now() - this.config.patternWindow);
    const anomalies = [];

    // Detect unusual activity patterns
    const activityAnomalies = await this.detectActivityAnomalies(timeWindow);
    anomalies.push(...activityAnomalies);

    // Detect unusual user behavior
    const userAnomalies = await this.detectUserAnomalies(timeWindow);
    anomalies.push(...userAnomalies);

    // Detect unusual network patterns
    const networkAnomalies = await this.detectNetworkAnomalies(timeWindow);
    anomalies.push(...networkAnomalies);

    // Detect temporal anomalies
    const temporalAnomalies = await this.detectTemporalAnomalies(timeWindow);
    anomalies.push(...temporalAnomalies);

    return anomalies;
  }

  /**
   * Detect activity pattern anomalies
   */
  async detectActivityAnomalies(timeWindow) {
    const query = `
      SELECT 
        event_type,
        category,
        COUNT(*) as event_count,
        AVG(risk_score) as avg_risk_score,
        EXTRACT(hour FROM timestamp) as hour_of_day
      FROM audit_logs 
      WHERE timestamp > $1
      GROUP BY event_type, category, EXTRACT(hour FROM timestamp)
      HAVING COUNT(*) > $2
      ORDER BY event_count DESC
    `;

    const result = await this.db.query(query, [timeWindow, this.config.minEventsForAnalysis]);
    const anomalies = [];

    for (const row of result.rows) {
      const baseline = await this.getActivityBaseline(row.event_type, row.category, row.hour_of_day);
      
      if (baseline) {
        const deviation = Math.abs(row.event_count - baseline.avg_count) / baseline.std_dev;
        
        if (deviation > this.config.anomalyThreshold) {
          anomalies.push({
            type: 'activity_anomaly',
            event_type: row.event_type,
            category: row.category,
            hour: row.hour_of_day,
            current_count: row.event_count,
            expected_count: baseline.avg_count,
            deviation: deviation,
            severity: this.calculateAnomalySeverity(deviation),
            detected_at: new Date().toISOString()
          });
        }
      }
    }

    return anomalies;
  }

  /**
   * Detect user behavior anomalies
   */
  async detectUserAnomalies(timeWindow) {
    const query = `
      SELECT 
        user_id,
        COUNT(*) as total_events,
        COUNT(DISTINCT event_type) as unique_event_types,
        COUNT(DISTINCT ip_address) as unique_ips,
        AVG(risk_score) as avg_risk_score,
        MAX(risk_score) as max_risk_score,
        COUNT(*) FILTER (WHERE severity = 'critical') as critical_events,
        array_agg(DISTINCT category) as categories
      FROM audit_logs 
      WHERE timestamp > $1 
      AND user_id IS NOT NULL
      GROUP BY user_id
      HAVING COUNT(*) > $2
    `;

    const result = await this.db.query(query, [timeWindow, this.config.minEventsForAnalysis]);
    const anomalies = [];

    for (const row of result.rows) {
      const userBaseline = await this.getUserBaseline(row.user_id);
      const userAnomalies = [];

      // Check for unusual activity volume
      if (userBaseline && row.total_events > userBaseline.avg_daily_events * 3) {
        userAnomalies.push({
          type: 'high_activity_volume',
          description: `User generated ${row.total_events} events vs normal ${userBaseline.avg_daily_events}`
        });
      }

      // Check for unusual IP diversity
      if (row.unique_ips > 5) {
        userAnomalies.push({
          type: 'multiple_ip_addresses',
          description: `User accessed from ${row.unique_ips} different IP addresses`
        });
      }

      // Check for unusual risk score
      if (row.avg_risk_score > 6) {
        userAnomalies.push({
          type: 'high_risk_activity',
          description: `User's average risk score is ${row.avg_risk_score}`
        });
      }

      // Check for critical events
      if (row.critical_events > 0) {
        userAnomalies.push({
          type: 'critical_events',
          description: `User triggered ${row.critical_events} critical security events`
        });
      }

      if (userAnomalies.length > 0) {
        anomalies.push({
          type: 'user_behavior_anomaly',
          user_id: row.user_id,
          anomalies: userAnomalies,
          statistics: {
            total_events: row.total_events,
            unique_ips: row.unique_ips,
            avg_risk_score: row.avg_risk_score,
            max_risk_score: row.max_risk_score
          },
          severity: this.calculateUserAnomalySeverity(userAnomalies),
          detected_at: new Date().toISOString()
        });
      }
    }

    return anomalies;
  }

  /**
   * Detect network pattern anomalies
   */
  async detectNetworkAnomalies(timeWindow) {
    const query = `
      SELECT 
        ip_address,
        COUNT(*) as request_count,
        COUNT(DISTINCT user_id) as unique_users,
        COUNT(DISTINCT event_type) as unique_events,
        AVG(risk_score) as avg_risk_score,
        COUNT(*) FILTER (WHERE category = 'security') as security_events,
        array_agg(DISTINCT user_agent) as user_agents
      FROM audit_logs 
      WHERE timestamp > $1 
      AND ip_address IS NOT NULL
      GROUP BY ip_address
      HAVING COUNT(*) > $2
      ORDER BY request_count DESC
    `;

    const result = await this.db.query(query, [timeWindow, this.config.minEventsForAnalysis]);
    const anomalies = [];

    for (const row of result.rows) {
      const networkAnomalies = [];

      // Check for high request volume from single IP
      if (row.request_count > 1000) {
        networkAnomalies.push({
          type: 'high_request_volume',
          description: `IP generated ${row.request_count} requests`
        });
      }

      // Check for multiple users from same IP (potential shared/compromised connection)
      if (row.unique_users > 10) {
        networkAnomalies.push({
          type: 'multiple_users_same_ip',
          description: `${row.unique_users} different users from same IP`
        });
      }

      // Check for security events concentration
      if (row.security_events > row.request_count * 0.1) {
        networkAnomalies.push({
          type: 'high_security_events',
          description: `High ratio of security events (${row.security_events}/${row.request_count})`
        });
      }

      // Check for suspicious user agents
      const suspiciousAgents = row.user_agents.filter(agent => 
        this.isSuspiciousUserAgent(agent)
      );
      
      if (suspiciousAgents.length > 0) {
        networkAnomalies.push({
          type: 'suspicious_user_agents',
          description: `Suspicious user agents detected: ${suspiciousAgents.join(', ')}`
        });
      }

      if (networkAnomalies.length > 0) {
        anomalies.push({
          type: 'network_anomaly',
          ip_address: row.ip_address,
          anomalies: networkAnomalies,
          statistics: {
            request_count: row.request_count,
            unique_users: row.unique_users,
            security_events: row.security_events,
            avg_risk_score: row.avg_risk_score
          },
          severity: this.calculateNetworkAnomalySeverity(networkAnomalies),
          detected_at: new Date().toISOString()
        });
      }
    }

    return anomalies;
  }

  /**
   * Detect temporal anomalies (unusual time patterns)
   */
  async detectTemporalAnomalies(timeWindow) {
    const query = `
      SELECT 
        EXTRACT(hour FROM timestamp) as hour,
        EXTRACT(dow FROM timestamp) as day_of_week,
        COUNT(*) as event_count,
        COUNT(*) FILTER (WHERE severity IN ('high', 'critical')) as high_severity_count
      FROM audit_logs 
      WHERE timestamp > $1
      GROUP BY EXTRACT(hour FROM timestamp), EXTRACT(dow FROM timestamp)
      ORDER BY event_count DESC
    `;

    const result = await this.db.query(query, [timeWindow]);
    const anomalies = [];

    for (const row of result.rows) {
      // Check for activity during unusual hours (late night/early morning)
      if ((row.hour >= 23 || row.hour <= 5) && row.event_count > 100) {
        anomalies.push({
          type: 'unusual_time_activity',
          hour: row.hour,
          day_of_week: row.day_of_week,
          event_count: row.event_count,
          description: `High activity during off-hours: ${row.event_count} events at ${row.hour}:00`,
          severity: 'medium',
          detected_at: new Date().toISOString()
        });
      }

      // Check for high severity events during off-hours
      if ((row.hour >= 22 || row.hour <= 6) && row.high_severity_count > 10) {
        anomalies.push({
          type: 'off_hours_security_events',
          hour: row.hour,
          day_of_week: row.day_of_week,
          high_severity_count: row.high_severity_count,
          description: `${row.high_severity_count} high-severity events during off-hours`,
          severity: 'high',
          detected_at: new Date().toISOString()
        });
      }
    }

    return anomalies;
  }

  /**
   * Analyze user behavior patterns
   */
  async analyzeUserBehavior() {
    const timeWindow = new Date(Date.now() - this.config.patternWindow);
    const behaviorAnalysis = [];

    // Analyze login patterns
    const loginPatterns = await this.analyzeLoginPatterns(timeWindow);
    behaviorAnalysis.push(...loginPatterns);

    // Analyze access patterns
    const accessPatterns = await this.analyzeAccessPatterns(timeWindow);
    behaviorAnalysis.push(...accessPatterns);

    // Analyze privilege usage
    const privilegePatterns = await this.analyzePrivilegeUsage(timeWindow);
    behaviorAnalysis.push(...privilegePatterns);

    return behaviorAnalysis;
  }

  /**
   * Analyze login patterns for suspicious behavior
   */
  async analyzeLoginPatterns(timeWindow) {
    const query = `
      SELECT 
        user_id,
        COUNT(*) as login_attempts,
        COUNT(*) FILTER (WHERE metadata->>'statusCode' = '200') as successful_logins,
        COUNT(*) FILTER (WHERE metadata->>'statusCode' != '200') as failed_logins,
        COUNT(DISTINCT ip_address) as unique_ips,
        array_agg(DISTINCT ip_address) as ip_addresses,
        MIN(timestamp) as first_login,
        MAX(timestamp) as last_login
      FROM audit_logs 
      WHERE timestamp > $1 
      AND event_type LIKE '%login%'
      AND user_id IS NOT NULL
      GROUP BY user_id
    `;

    const result = await this.db.query(query, [timeWindow]);
    const patterns = [];

    for (const row of result.rows) {
      const analysis = {
        type: 'login_pattern_analysis',
        user_id: row.user_id,
        riskLevel: 'low',
        findings: []
      };

      // High failed login ratio
      const failureRate = row.failed_logins / row.login_attempts;
      if (failureRate > 0.3) {
        analysis.findings.push({
          type: 'high_failure_rate',
          description: `${(failureRate * 100).toFixed(1)}% login failure rate`,
          severity: failureRate > 0.5 ? 'high' : 'medium'
        });
        analysis.riskLevel = failureRate > 0.5 ? 'high' : 'medium';
      }

      // Multiple IP addresses
      if (row.unique_ips > 3) {
        analysis.findings.push({
          type: 'multiple_locations',
          description: `Logins from ${row.unique_ips} different IP addresses`,
          severity: row.unique_ips > 5 ? 'high' : 'medium'
        });
        if (row.unique_ips > 5) analysis.riskLevel = 'high';
      }

      // Rapid succession logins
      const timeDiff = new Date(row.last_login) - new Date(row.first_login);
      const loginRate = row.login_attempts / (timeDiff / (60 * 1000)); // logins per minute
      if (loginRate > 1) {
        analysis.findings.push({
          type: 'rapid_logins',
          description: `${loginRate.toFixed(2)} logins per minute`,
          severity: 'medium'
        });
      }

      if (analysis.findings.length > 0) {
        patterns.push({
          ...analysis,
          statistics: {
            total_attempts: row.login_attempts,
            successful: row.successful_logins,
            failed: row.failed_logins,
            unique_ips: row.unique_ips
          },
          detected_at: new Date().toISOString()
        });
      }
    }

    return patterns;
  }

  /**
   * Analyze access patterns for unusual behavior
   */
  async analyzeAccessPatterns(timeWindow) {
    const query = `
      SELECT 
        user_id,
        COUNT(*) as total_accesses,
        COUNT(DISTINCT resource) as unique_resources,
        array_agg(DISTINCT category) as categories,
        COUNT(*) FILTER (WHERE category = 'data_modification') as modifications,
        COUNT(*) FILTER (WHERE risk_score >= 7) as high_risk_accesses
      FROM audit_logs 
      WHERE timestamp > $1 
      AND user_id IS NOT NULL
      AND category IN ('data_access', 'data_modification')
      GROUP BY user_id
    `;

    const result = await this.db.query(query, [timeWindow]);
    const patterns = [];

    for (const row of result.rows) {
      const analysis = {
        type: 'access_pattern_analysis',
        user_id: row.user_id,
        riskLevel: 'low',
        findings: []
      };

      // High volume data access
      if (row.total_accesses > 1000) {
        analysis.findings.push({
          type: 'high_volume_access',
          description: `${row.total_accesses} data access events`,
          severity: 'medium'
        });
      }

      // Unusual resource diversity
      if (row.unique_resources > 50) {
        analysis.findings.push({
          type: 'broad_resource_access',
          description: `Accessed ${row.unique_resources} different resources`,
          severity: 'medium'
        });
      }

      // High modification rate
      const modificationRate = row.modifications / row.total_accesses;
      if (modificationRate > 0.3) {
        analysis.findings.push({
          type: 'high_modification_rate',
          description: `${(modificationRate * 100).toFixed(1)}% of accesses were modifications`,
          severity: 'medium'
        });
      }

      // High risk accesses
      if (row.high_risk_accesses > 0) {
        analysis.findings.push({
          type: 'high_risk_operations',
          description: `${row.high_risk_accesses} high-risk operations performed`,
          severity: 'high'
        });
        analysis.riskLevel = 'high';
      }

      if (analysis.findings.length > 0) {
        patterns.push({
          ...analysis,
          statistics: {
            total_accesses: row.total_accesses,
            unique_resources: row.unique_resources,
            modifications: row.modifications,
            high_risk_accesses: row.high_risk_accesses
          },
          detected_at: new Date().toISOString()
        });
      }
    }

    return patterns;
  }

  /**
   * Analyze privilege usage patterns
   */
  async analyzePrivilegeUsage(timeWindow) {
    const query = `
      SELECT 
        user_id,
        COUNT(*) as admin_actions,
        COUNT(DISTINCT action) as unique_actions,
        array_agg(DISTINCT action) as actions,
        COUNT(*) FILTER (WHERE category = 'system_admin') as system_actions
      FROM audit_logs 
      WHERE timestamp > $1 
      AND user_id IS NOT NULL
      AND (category = 'system_admin' OR action LIKE '%admin%')
      GROUP BY user_id
    `;

    const result = await this.db.query(query, [timeWindow]);
    const patterns = [];

    for (const row of result.rows) {
      const analysis = {
        type: 'privilege_usage_analysis',
        user_id: row.user_id,
        riskLevel: 'medium', // Admin actions are inherently higher risk
        findings: []
      };

      // High volume administrative actions
      if (row.admin_actions > 100) {
        analysis.findings.push({
          type: 'high_admin_activity',
          description: `${row.admin_actions} administrative actions performed`,
          severity: 'medium'
        });
      }

      // Diverse administrative actions
      if (row.unique_actions > 10) {
        analysis.findings.push({
          type: 'diverse_admin_actions',
          description: `Performed ${row.unique_actions} different types of admin actions`,
          severity: 'medium'
        });
      }

      // System-level actions
      if (row.system_actions > 0) {
        analysis.findings.push({
          type: 'system_level_actions',
          description: `${row.system_actions} system-level administrative actions`,
          severity: 'high'
        });
        analysis.riskLevel = 'high';
      }

      if (analysis.findings.length > 0) {
        patterns.push({
          ...analysis,
          statistics: {
            admin_actions: row.admin_actions,
            unique_actions: row.unique_actions,
            system_actions: row.system_actions
          },
          detected_at: new Date().toISOString()
        });
      }
    }

    return patterns;
  }

  /**
   * Detect security patterns and threats
   */
  async detectSecurityPatterns() {
    const timeWindow = new Date(Date.now() - this.config.patternWindow);
    const securityPatterns = [];

    // Attack pattern detection
    const attackPatterns = await this.detectAttackPatterns(timeWindow);
    securityPatterns.push(...attackPatterns);

    // Privilege escalation detection
    const privilegeEscalation = await this.detectPrivilegeEscalation(timeWindow);
    securityPatterns.push(...privilegeEscalation);

    // Data exfiltration detection
    const dataExfiltration = await this.detectDataExfiltration(timeWindow);
    securityPatterns.push(...dataExfiltration);

    return securityPatterns;
  }

  /**
   * Detect attack patterns
   */
  async detectAttackPatterns(timeWindow) {
    const query = `
      SELECT 
        ip_address,
        COUNT(*) as total_events,
        COUNT(*) FILTER (WHERE category = 'security') as security_events,
        COUNT(*) FILTER (WHERE event_type LIKE '%failed%') as failed_events,
        COUNT(*) FILTER (WHERE risk_score >= 7) as high_risk_events,
        array_agg(DISTINCT event_type) as event_types
      FROM audit_logs 
      WHERE timestamp > $1 
      AND ip_address IS NOT NULL
      GROUP BY ip_address
      HAVING COUNT(*) FILTER (WHERE category = 'security') > 5
      ORDER BY security_events DESC
    `;

    const result = await this.db.query(query, [timeWindow]);
    const patterns = [];

    for (const row of result.rows) {
      const pattern = {
        type: 'potential_attack_pattern',
        ip_address: row.ip_address,
        severity: this.calculateAttackSeverity(row),
        indicators: []
      };

      // High security event concentration
      const securityRate = row.security_events / row.total_events;
      if (securityRate > 0.5) {
        pattern.indicators.push({
          type: 'high_security_event_rate',
          description: `${(securityRate * 100).toFixed(1)}% of events are security-related`
        });
      }

      // High failure rate
      const failureRate = row.failed_events / row.total_events;
      if (failureRate > 0.3) {
        pattern.indicators.push({
          type: 'high_failure_rate',
          description: `${(failureRate * 100).toFixed(1)}% failure rate`
        });
      }

      // Multiple attack vectors
      const attackVectors = row.event_types.filter(type => 
        type.includes('failed') || type.includes('denied') || type.includes('suspicious')
      );
      
      if (attackVectors.length > 3) {
        pattern.indicators.push({
          type: 'multiple_attack_vectors',
          description: `${attackVectors.length} different attack patterns detected`
        });
      }

      if (pattern.indicators.length > 0) {
        patterns.push({
          ...pattern,
          statistics: {
            total_events: row.total_events,
            security_events: row.security_events,
            failed_events: row.failed_events,
            high_risk_events: row.high_risk_events
          },
          detected_at: new Date().toISOString()
        });
      }
    }

    return patterns;
  }

  /**
   * Detect privilege escalation attempts
   */
  async detectPrivilegeEscalation(timeWindow) {
    const query = `
      SELECT 
        user_id,
        COUNT(*) as total_events,
        COUNT(*) FILTER (WHERE action LIKE '%role%' OR action LIKE '%permission%') as privilege_changes,
        COUNT(*) FILTER (WHERE category = 'authorization' AND metadata->>'statusCode' != '200') as access_denied,
        array_agg(DISTINCT action) as actions
      FROM audit_logs 
      WHERE timestamp > $1 
      AND user_id IS NOT NULL
      AND (category = 'authorization' OR action LIKE '%role%' OR action LIKE '%permission%')
      GROUP BY user_id
      HAVING COUNT(*) FILTER (WHERE category = 'authorization' AND metadata->>'statusCode' != '200') > 5
    `;

    const result = await this.db.query(query, [timeWindow]);
    const patterns = [];

    for (const row of result.rows) {
      patterns.push({
        type: 'potential_privilege_escalation',
        user_id: row.user_id,
        severity: row.access_denied > 10 ? 8 : 6,
        indicators: [
          {
            type: 'repeated_access_denied',
            description: `${row.access_denied} authorization failures`
          },
          {
            type: 'privilege_manipulation_attempts',
            description: `${row.privilege_changes} privilege-related actions`
          }
        ],
        statistics: {
          total_events: row.total_events,
          privilege_changes: row.privilege_changes,
          access_denied: row.access_denied
        },
        detected_at: new Date().toISOString()
      });
    }

    return patterns;
  }

  /**
   * Detect potential data exfiltration
   */
  async detectDataExfiltration(timeWindow) {
    const query = `
      SELECT 
        user_id,
        COUNT(*) as total_accesses,
        COUNT(*) FILTER (WHERE action LIKE '%export%' OR action LIKE '%download%') as export_actions,
        COUNT(DISTINCT resource) as unique_resources,
        SUM(CASE WHEN metadata->>'responseSize' IS NOT NULL 
                 THEN (metadata->>'responseSize')::INTEGER 
                 ELSE 0 END) as total_data_size
      FROM audit_logs 
      WHERE timestamp > $1 
      AND user_id IS NOT NULL
      AND category = 'data_access'
      GROUP BY user_id
      HAVING COUNT(*) FILTER (WHERE action LIKE '%export%' OR action LIKE '%download%') > 10
         OR COUNT(DISTINCT resource) > 100
    `;

    const result = await this.db.query(query, [timeWindow]);
    const patterns = [];

    for (const row of result.rows) {
      const pattern = {
        type: 'potential_data_exfiltration',
        user_id: row.user_id,
        severity: 7,
        indicators: []
      };

      // High export activity
      if (row.export_actions > 20) {
        pattern.indicators.push({
          type: 'excessive_data_exports',
          description: `${row.export_actions} data export operations`
        });
      }

      // Broad data access
      if (row.unique_resources > 50) {
        pattern.indicators.push({
          type: 'broad_data_access',
          description: `Accessed ${row.unique_resources} different data sources`
        });
      }

      // Large data volume
      if (row.total_data_size > 100 * 1024 * 1024) { // 100MB
        pattern.indicators.push({
          type: 'large_data_volume',
          description: `Downloaded ${(row.total_data_size / 1024 / 1024).toFixed(2)}MB of data`
        });
      }

      if (pattern.indicators.length > 0) {
        patterns.push({
          ...pattern,
          statistics: {
            total_accesses: row.total_accesses,
            export_actions: row.export_actions,
            unique_resources: row.unique_resources,
            total_data_size: row.total_data_size
          },
          detected_at: new Date().toISOString()
        });
      }
    }

    return patterns;
  }

  /**
   * Analyze performance issues from audit logs
   */
  async analyzePerformanceIssues() {
    const timeWindow = new Date(Date.now() - this.config.patternWindow);
    
    const query = `
      SELECT 
        resource,
        action,
        COUNT(*) as request_count,
        AVG((metadata->>'duration')::NUMERIC) as avg_duration,
        MAX((metadata->>'duration')::NUMERIC) as max_duration,
        COUNT(*) FILTER (WHERE (metadata->>'duration')::NUMERIC > 5000) as slow_requests,
        COUNT(*) FILTER (WHERE (metadata->>'statusCode')::INTEGER >= 500) as server_errors
      FROM audit_logs 
      WHERE timestamp > $1 
      AND metadata->>'duration' IS NOT NULL
      AND (metadata->>'duration')::NUMERIC > 0
      GROUP BY resource, action
      HAVING AVG((metadata->>'duration')::NUMERIC) > 2000 
         OR COUNT(*) FILTER (WHERE (metadata->>'duration')::NUMERIC > 5000) > 10
      ORDER BY avg_duration DESC
    `;

    const result = await this.db.query(query, [timeWindow]);
    const issues = [];

    for (const row of result.rows) {
      const issue = {
        type: 'performance_issue',
        resource: row.resource,
        action: row.action,
        impact: 'medium',
        problems: []
      };

      // Slow average response time
      if (row.avg_duration > 3000) {
        issue.problems.push({
          type: 'slow_average_response',
          description: `Average response time: ${row.avg_duration.toFixed(0)}ms`
        });
        issue.impact = 'high';
      }

      // Many slow requests
      const slowRequestRate = row.slow_requests / row.request_count;
      if (slowRequestRate > 0.1) {
        issue.problems.push({
          type: 'frequent_slow_requests',
          description: `${(slowRequestRate * 100).toFixed(1)}% of requests are slow (>5s)`
        });
      }

      // Server errors
      if (row.server_errors > 0) {
        issue.problems.push({
          type: 'server_errors',
          description: `${row.server_errors} server errors occurred`
        });
        issue.impact = 'high';
      }

      if (issue.problems.length > 0) {
        issues.push({
          ...issue,
          statistics: {
            request_count: row.request_count,
            avg_duration: row.avg_duration,
            max_duration: row.max_duration,
            slow_requests: row.slow_requests,
            server_errors: row.server_errors
          },
          detected_at: new Date().toISOString()
        });
      }
    }

    return issues;
  }

  /**
   * Detect compliance violations
   */
  async detectComplianceViolations() {
    const timeWindow = new Date(Date.now() - this.config.patternWindow);
    const violations = [];

    // GDPR violations (personal data access without proper logging)
    const gdprViolations = await this.detectGDPRViolations(timeWindow);
    violations.push(...gdprViolations);

    // SOX violations (financial data modifications without approval)
    const soxViolations = await this.detectSOXViolations(timeWindow);
    violations.push(...soxViolations);

    // PCI DSS violations (payment data handling issues)
    const pciViolations = await this.detectPCIViolations(timeWindow);
    violations.push(...pciViolations);

    return violations;
  }

  /**
   * Detect GDPR compliance violations
   */
  async detectGDPRViolations(timeWindow) {
    const query = `
      SELECT 
        user_id,
        COUNT(*) as personal_data_accesses,
        COUNT(*) FILTER (WHERE old_values IS NULL OR new_values IS NULL) as undocumented_changes
      FROM audit_logs 
      WHERE timestamp > $1 
      AND 'gdpr' = ANY(compliance_tags)
      AND category = 'data_modification'
      GROUP BY user_id
      HAVING COUNT(*) FILTER (WHERE old_values IS NULL OR new_values IS NULL) > 0
    `;

    const result = await this.db.query(query, [timeWindow]);
    const violations = [];

    for (const row of result.rows) {
      violations.push({
        type: 'gdpr_violation',
        framework: 'GDPR',
        user_id: row.user_id,
        violation_type: 'insufficient_change_documentation',
        description: `${row.undocumented_changes} personal data modifications without proper change documentation`,
        severity: 'high',
        detected_at: new Date().toISOString()
      });
    }

    return violations;
  }

  /**
   * Detect SOX compliance violations
   */
  async detectSOXViolations(timeWindow) {
    const query = `
      SELECT 
        user_id,
        COUNT(*) as financial_modifications,
        COUNT(*) FILTER (WHERE metadata->>'approval' IS NULL) as unapproved_changes
      FROM audit_logs 
      WHERE timestamp > $1 
      AND 'sarbanes_oxley' = ANY(compliance_tags)
      AND category = 'data_modification'
      GROUP BY user_id
      HAVING COUNT(*) FILTER (WHERE metadata->>'approval' IS NULL) > 0
    `;

    const result = await this.db.query(query, [timeWindow]);
    const violations = [];

    for (const row of result.rows) {
      violations.push({
        type: 'sox_violation',
        framework: 'SOX',
        user_id: row.user_id,
        violation_type: 'unapproved_financial_changes',
        description: `${row.unapproved_changes} financial data modifications without approval documentation`,
        severity: 'critical',
        detected_at: new Date().toISOString()
      });
    }

    return violations;
  }

  /**
   * Detect PCI DSS compliance violations
   */
  async detectPCIViolations(timeWindow) {
    const query = `
      SELECT 
        user_id,
        COUNT(*) as payment_accesses,
        COUNT(*) FILTER (WHERE encrypted_data IS NULL) as unencrypted_accesses
      FROM audit_logs 
      WHERE timestamp > $1 
      AND 'pci_dss' = ANY(compliance_tags)
      GROUP BY user_id
      HAVING COUNT(*) FILTER (WHERE encrypted_data IS NULL) > 0
    `;

    const result = await this.db.query(query, [timeWindow]);
    const violations = [];

    for (const row of result.rows) {
      violations.push({
        type: 'pci_violation',
        framework: 'PCI DSS',
        user_id: row.user_id,
        violation_type: 'unencrypted_payment_data_access',
        description: `${row.unencrypted_accesses} payment data accesses without proper encryption`,
        severity: 'critical',
        detected_at: new Date().toISOString()
      });
    }

    return violations;
  }

  // Helper methods for calculations

  calculateAnomalySeverity(deviation) {
    if (deviation > 4) return 'critical';
    if (deviation > 3) return 'high';
    if (deviation > 2) return 'medium';
    return 'low';
  }

  calculateUserAnomalySeverity(anomalies) {
    const criticalCount = anomalies.filter(a => a.type === 'critical_events').length;
    const highRiskCount = anomalies.filter(a => a.type === 'high_risk_activity').length;
    
    if (criticalCount > 0 || highRiskCount > 0) return 'high';
    if (anomalies.length > 2) return 'medium';
    return 'low';
  }

  calculateNetworkAnomalySeverity(anomalies) {
    const suspiciousAgents = anomalies.filter(a => a.type === 'suspicious_user_agents').length;
    const highVolume = anomalies.filter(a => a.type === 'high_request_volume').length;
    
    if (suspiciousAgents > 0) return 'high';
    if (highVolume > 0) return 'medium';
    return 'low';
  }

  calculateAttackSeverity(data) {
    const securityRate = data.security_events / data.total_events;
    const riskRate = data.high_risk_events / data.total_events;
    
    if (securityRate > 0.7 || riskRate > 0.5) return 9;
    if (securityRate > 0.5 || riskRate > 0.3) return 7;
    if (securityRate > 0.3) return 5;
    return 3;
  }

  isSuspiciousUserAgent(userAgent) {
    const suspiciousPatterns = [
      'bot', 'crawler', 'spider', 'scraper', 'curl', 'wget',
      'python', 'java', 'go-http', 'postman', 'insomnia'
    ];
    
    return suspiciousPatterns.some(pattern => 
      userAgent.toLowerCase().includes(pattern)
    );
  }

  // Baseline management methods

  async getActivityBaseline(eventType, category, hour) {
    const cacheKey = `baseline_${eventType}_${category}_${hour}`;
    
    if (this.behaviorBaselines.has(cacheKey)) {
      return this.behaviorBaselines.get(cacheKey);
    }

    // Calculate baseline from historical data (30 days)
    const query = `
      SELECT 
        AVG(event_count) as avg_count,
        STDDEV(event_count) as std_dev
      FROM (
        SELECT 
          DATE(timestamp) as date,
          COUNT(*) as event_count
        FROM audit_logs 
        WHERE event_type = $1 
        AND category = $2
        AND EXTRACT(hour FROM timestamp) = $3
        AND timestamp > NOW() - INTERVAL '30 days'
        GROUP BY DATE(timestamp)
      ) daily_counts
    `;

    const result = await this.db.query(query, [eventType, category, hour]);
    const baseline = result.rows[0];

    if (baseline && baseline.avg_count > 0) {
      this.behaviorBaselines.set(cacheKey, baseline);
      return baseline;
    }

    return null;
  }

  async getUserBaseline(userId) {
    const cacheKey = `user_baseline_${userId}`;
    
    if (this.behaviorBaselines.has(cacheKey)) {
      return this.behaviorBaselines.get(cacheKey);
    }

    const query = `
      SELECT 
        AVG(daily_events) as avg_daily_events,
        AVG(daily_risk_score) as avg_daily_risk_score
      FROM (
        SELECT 
          DATE(timestamp) as date,
          COUNT(*) as daily_events,
          AVG(risk_score) as daily_risk_score
        FROM audit_logs 
        WHERE user_id = $1 
        AND timestamp > NOW() - INTERVAL '30 days'
        GROUP BY DATE(timestamp)
      ) daily_stats
    `;

    const result = await this.db.query(query, [userId]);
    const baseline = result.rows[0];

    if (baseline && baseline.avg_daily_events > 0) {
      this.behaviorBaselines.set(cacheKey, baseline);
      return baseline;
    }

    return null;
  }

  /**
   * Generate forensic analysis report
   */
  async generateForensicReport(incidentId, timeRange) {
    const { startTime, endTime } = timeRange;
    
    const report = {
      incident_id: incidentId,
      generated_at: new Date().toISOString(),
      time_range: { start: startTime, end: endTime },
      analysis: {}
    };

    // Get all relevant audit logs
    const auditLogs = await this.searchAuditLogs({
      startDate: startTime,
      endDate: endTime,
      limit: 10000
    });

    report.analysis.total_events = auditLogs.length;
    report.analysis.timeline = this.buildEventTimeline(auditLogs);
    report.analysis.user_involvement = this.analyzeUserInvolvement(auditLogs);
    report.analysis.network_analysis = this.analyzeNetworkPatterns(auditLogs);
    report.analysis.risk_assessment = this.calculateIncidentRisk(auditLogs);
    report.analysis.recommendations = this.generateRecommendations(auditLogs);

    return report;
  }

  buildEventTimeline(auditLogs) {
    return auditLogs
      .sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp))
      .map(log => ({
        timestamp: log.timestamp,
        event_type: log.event_type,
        user_id: log.user_id,
        ip_address: log.ip_address,
        risk_score: log.risk_score,
        action: log.action,
        resource: log.resource
      }));
  }

  analyzeUserInvolvement(auditLogs) {
    const userStats = {};
    
    auditLogs.forEach(log => {
      if (log.user_id) {
        if (!userStats[log.user_id]) {
          userStats[log.user_id] = {
            event_count: 0,
            high_risk_events: 0,
            unique_ips: new Set(),
            event_types: new Set()
          };
        }
        
        userStats[log.user_id].event_count++;
        if (log.risk_score >= 7) userStats[log.user_id].high_risk_events++;
        if (log.ip_address) userStats[log.user_id].unique_ips.add(log.ip_address);
        userStats[log.user_id].event_types.add(log.event_type);
      }
    });

    // Convert sets to arrays for JSON serialization
    Object.keys(userStats).forEach(userId => {
      userStats[userId].unique_ips = Array.from(userStats[userId].unique_ips);
      userStats[userId].event_types = Array.from(userStats[userId].event_types);
    });

    return userStats;
  }

  analyzeNetworkPatterns(auditLogs) {
    const ipStats = {};
    
    auditLogs.forEach(log => {
      if (log.ip_address) {
        if (!ipStats[log.ip_address]) {
          ipStats[log.ip_address] = {
            event_count: 0,
            unique_users: new Set(),
            risk_events: 0
          };
        }
        
        ipStats[log.ip_address].event_count++;
        if (log.user_id) ipStats[log.ip_address].unique_users.add(log.user_id);
        if (log.risk_score >= 7) ipStats[log.ip_address].risk_events++;
      }
    });

    // Convert sets to arrays
    Object.keys(ipStats).forEach(ip => {
      ipStats[ip].unique_users = Array.from(ipStats[ip].unique_users);
    });

    return ipStats;
  }

  calculateIncidentRisk(auditLogs) {
    const riskFactors = {
      high_risk_events: auditLogs.filter(log => log.risk_score >= 7).length,
      critical_events: auditLogs.filter(log => log.severity === 'critical').length,
      security_events: auditLogs.filter(log => log.category === 'security').length,
      admin_events: auditLogs.filter(log => log.category === 'system_admin').length,
      unique_users: new Set(auditLogs.map(log => log.user_id)).size,
      unique_ips: new Set(auditLogs.map(log => log.ip_address)).size
    };

    let riskScore = 0;
    riskScore += riskFactors.critical_events * 3;
    riskScore += riskFactors.high_risk_events * 2;
    riskScore += riskFactors.security_events * 1;
    riskScore += Math.min(riskFactors.unique_users * 0.5, 5);
    riskScore += Math.min(riskFactors.unique_ips * 0.3, 3);

    return {
      overall_risk_score: Math.min(riskScore, 10),
      risk_factors: riskFactors,
      risk_level: riskScore > 8 ? 'critical' : riskScore > 6 ? 'high' : riskScore > 4 ? 'medium' : 'low'
    };
  }

  generateRecommendations(auditLogs) {
    const recommendations = [];

    const highRiskEvents = auditLogs.filter(log => log.risk_score >= 7).length;
    if (highRiskEvents > 10) {
      recommendations.push({
        type: 'security',
        priority: 'high',
        description: 'Investigate high-risk events and implement additional security controls'
      });
    }

    const uniqueIPs = new Set(auditLogs.map(log => log.ip_address)).size;
    if (uniqueIPs > 20) {
      recommendations.push({
        type: 'network',
        priority: 'medium',
        description: 'Consider implementing IP-based access controls or geographic restrictions'
      });
    }

    const failedEvents = auditLogs.filter(log => 
      log.metadata && log.metadata.statusCode >= 400
    ).length;
    
    if (failedEvents > auditLogs.length * 0.3) {
      recommendations.push({
        type: 'operational',
        priority: 'medium',
        description: 'High failure rate detected, investigate system performance and user experience'
      });
    }

    return recommendations;
  }

  /**
   * Search audit logs with advanced filters
   */
  async searchAuditLogs(filters = {}) {
    // This method would interface with the AuditLogger's search method
    // Implementation depends on the specific search requirements
    return [];
  }

  /**
   * Shutdown the log analyzer
   */
  shutdown() {
    if (this.analysisInterval) {
      clearInterval(this.analysisInterval);
    }
    console.log('Log analyzer shutdown complete');
  }
}

module.exports = { LogAnalyzer };