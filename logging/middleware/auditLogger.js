/**
 * Advanced Audit Logging System
 * Comprehensive audit trail for security compliance and forensic analysis
 */

const crypto = require('crypto');
const { promisify } = require('util');

class AuditLogger {
  constructor(db, config = {}) {
    this.db = db;
    this.config = {
      enableDetailed: true,
      enableRealTime: true,
      sensitiveFields: ['password', 'token', 'secret', 'key', 'api_key'],
      maxLogSize: 50000, // Maximum log entry size in characters
      batchSize: 100,
      flushInterval: 5000, // 5 seconds
      enableEncryption: process.env.NODE_ENV === 'production',
      encryptionKey: process.env.AUDIT_ENCRYPTION_KEY || 'default-key-change-in-production',
      ...config
    };

    // Batch processing for performance
    this.logBatch = [];
    this.batchTimer = null;

    // Event categories for compliance
    this.eventCategories = {
      AUTHENTICATION: 'authentication',
      AUTHORIZATION: 'authorization',
      DATA_ACCESS: 'data_access',
      DATA_MODIFICATION: 'data_modification',
      SYSTEM_ADMIN: 'system_admin',
      SECURITY: 'security',
      COMPLIANCE: 'compliance',
      ERROR: 'error',
      PERFORMANCE: 'performance'
    };

    // Compliance frameworks
    this.complianceFrameworks = {
      SOX: 'sarbanes_oxley',
      GDPR: 'gdpr',
      HIPAA: 'hipaa',
      PCI_DSS: 'pci_dss',
      SOC2: 'soc2',
      ISO27001: 'iso27001'
    };

    this.initializeBatchProcessing();
  }

  /**
   * Initialize batch processing for performance optimization
   */
  initializeBatchProcessing() {
    this.batchTimer = setInterval(() => {
      this.flushBatch();
    }, this.config.flushInterval);

    // Graceful shutdown
    process.on('SIGINT', () => this.shutdown());
    process.on('SIGTERM', () => this.shutdown());
  }

  /**
   * Main audit logging method
   */
  async logAudit(event) {
    try {
      const auditEntry = await this.createAuditEntry(event);
      
      if (this.config.enableRealTime && this.isHighRiskEvent(event)) {
        // Log immediately for high-risk events
        await this.writeAuditEntry(auditEntry);
      } else {
        // Add to batch for normal events
        this.addToBatch(auditEntry);
      }

      // Emit real-time event for monitoring
      if (this.config.enableRealTime) {
        this.emitRealTimeEvent(auditEntry);
      }

    } catch (error) {
      console.error('Audit logging failed:', error);
      // Fallback logging to file system
      this.fallbackLog(event, error);
    }
  }

  /**
   * Create comprehensive audit entry
   */
  async createAuditEntry(event) {
    const timestamp = new Date().toISOString();
    const auditId = this.generateAuditId();

    const baseEntry = {
      audit_id: auditId,
      timestamp,
      event_type: event.eventType,
      category: event.category || this.detectCategory(event.eventType),
      severity: event.severity || this.determineSeverity(event),
      user_id: event.userId || null,
      session_id: event.sessionId || null,
      ip_address: event.ipAddress || null,
      user_agent: event.userAgent || null,
      request_id: event.requestId || null
    };

    // Add detailed event data
    const detailedEntry = {
      ...baseEntry,
      action: event.action || 'unknown',
      resource: event.resource || null,
      resource_id: event.resourceId || null,
      old_values: await this.sanitizeData(event.oldValues),
      new_values: await this.sanitizeData(event.newValues),
      request_data: await this.sanitizeData(event.requestData),
      response_data: await this.sanitizeData(event.responseData),
      metadata: event.metadata || {},
      compliance_tags: this.generateComplianceTags(event),
      risk_score: this.calculateRiskScore(event),
      geolocation: await this.getGeolocation(event.ipAddress),
      device_fingerprint: this.generateDeviceFingerprint(event.userAgent, event.ipAddress)
    };

    // Add application context
    detailedEntry.application_context = {
      service: 'ecommerce-analytics',
      version: process.env.APP_VERSION || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      hostname: require('os').hostname(),
      process_id: process.pid
    };

    // Encrypt sensitive data if enabled
    if (this.config.enableEncryption) {
      detailedEntry.encrypted_data = await this.encryptSensitiveData(detailedEntry);
    }

    // Add integrity hash
    detailedEntry.integrity_hash = this.generateIntegrityHash(detailedEntry);

    return detailedEntry;
  }

  /**
   * Sanitize sensitive data from logs
   */
  async sanitizeData(data) {
    if (!data || typeof data !== 'object') {
      return data;
    }

    const sanitized = JSON.parse(JSON.stringify(data));
    
    const sanitizeRecursive = (obj) => {
      for (const key in obj) {
        if (this.config.sensitiveFields.some(field => 
          key.toLowerCase().includes(field.toLowerCase())
        )) {
          obj[key] = '[REDACTED]';
        } else if (typeof obj[key] === 'object' && obj[key] !== null) {
          sanitizeRecursive(obj[key]);
        }
      }
    };

    sanitizeRecursive(sanitized);
    return sanitized;
  }

  /**
   * Generate compliance tags based on event type
   */
  generateComplianceTags(event) {
    const tags = [];

    // SOX compliance
    if (this.isFinancialData(event)) {
      tags.push(this.complianceFrameworks.SOX);
    }

    // GDPR compliance
    if (this.isPersonalData(event)) {
      tags.push(this.complianceFrameworks.GDPR);
    }

    // PCI DSS compliance
    if (this.isPaymentData(event)) {
      tags.push(this.complianceFrameworks.PCI_DSS);
    }

    // SOC2 compliance (all security events)
    if (event.category === this.eventCategories.SECURITY) {
      tags.push(this.complianceFrameworks.SOC2);
    }

    return tags;
  }

  /**
   * Calculate risk score for the event
   */
  calculateRiskScore(event) {
    let score = 0;

    // Base score by category
    const categoryScores = {
      [this.eventCategories.AUTHENTICATION]: 3,
      [this.eventCategories.AUTHORIZATION]: 4,
      [this.eventCategories.DATA_ACCESS]: 2,
      [this.eventCategories.DATA_MODIFICATION]: 5,
      [this.eventCategories.SYSTEM_ADMIN]: 6,
      [this.eventCategories.SECURITY]: 7,
      [this.eventCategories.ERROR]: 2
    };

    score += categoryScores[event.category] || 1;

    // Increase score for sensitive operations
    if (event.action?.includes('delete')) score += 3;
    if (event.action?.includes('admin')) score += 2;
    if (event.resource?.includes('user')) score += 1;
    if (event.resource?.includes('payment')) score += 3;

    // Increase score for failed operations
    if (event.success === false) score += 2;

    // Increase score for unusual times
    const hour = new Date().getHours();
    if (hour < 6 || hour > 22) score += 1;

    return Math.min(score, 10); // Max score of 10
  }

  /**
   * Determine if this is a high-risk event requiring immediate logging
   */
  isHighRiskEvent(event) {
    const highRiskPatterns = [
      'admin_login',
      'privilege_escalation',
      'data_breach',
      'security_alert',
      'unauthorized_access',
      'system_compromise',
      'data_deletion',
      'user_creation',
      'role_assignment'
    ];

    return highRiskPatterns.some(pattern => 
      event.eventType?.toLowerCase().includes(pattern) ||
      event.action?.toLowerCase().includes(pattern)
    ) || (event.riskScore || this.calculateRiskScore(event)) >= 7;
  }

  /**
   * Add audit entry to batch
   */
  addToBatch(auditEntry) {
    this.logBatch.push(auditEntry);

    if (this.logBatch.length >= this.config.batchSize) {
      this.flushBatch();
    }
  }

  /**
   * Flush batch to database
   */
  async flushBatch() {
    if (this.logBatch.length === 0) return;

    const batch = [...this.logBatch];
    this.logBatch = [];

    try {
      await this.writeBatchToDatabase(batch);
    } catch (error) {
      console.error('Failed to flush audit batch:', error);
      // Re-add to batch for retry
      this.logBatch.unshift(...batch);
    }
  }

  /**
   * Write batch to database
   */
  async writeBatchToDatabase(batch) {
    const values = batch.map(entry => [
      entry.audit_id,
      entry.timestamp,
      entry.event_type,
      entry.category,
      entry.severity,
      entry.user_id,
      entry.session_id,
      entry.ip_address,
      entry.user_agent,
      entry.request_id,
      entry.action,
      entry.resource,
      entry.resource_id,
      JSON.stringify(entry.old_values),
      JSON.stringify(entry.new_values),
      JSON.stringify(entry.request_data),
      JSON.stringify(entry.response_data),
      JSON.stringify(entry.metadata),
      entry.compliance_tags,
      entry.risk_score,
      JSON.stringify(entry.geolocation),
      entry.device_fingerprint,
      JSON.stringify(entry.application_context),
      entry.encrypted_data,
      entry.integrity_hash
    ]);

    const placeholders = values.map((_, i) => 
      `($${i * 25 + 1}, $${i * 25 + 2}, $${i * 25 + 3}, $${i * 25 + 4}, $${i * 25 + 5}, $${i * 25 + 6}, $${i * 25 + 7}, $${i * 25 + 8}, $${i * 25 + 9}, $${i * 25 + 10}, $${i * 25 + 11}, $${i * 25 + 12}, $${i * 25 + 13}, $${i * 25 + 14}, $${i * 25 + 15}, $${i * 25 + 16}, $${i * 25 + 17}, $${i * 25 + 18}, $${i * 25 + 19}, $${i * 25 + 20}, $${i * 25 + 21}, $${i * 25 + 22}, $${i * 25 + 23}, $${i * 25 + 24}, $${i * 25 + 25})`
    ).join(', ');

    const query = `
      INSERT INTO audit_logs (
        audit_id, timestamp, event_type, category, severity,
        user_id, session_id, ip_address, user_agent, request_id,
        action, resource, resource_id, old_values, new_values,
        request_data, response_data, metadata, compliance_tags,
        risk_score, geolocation, device_fingerprint, application_context,
        encrypted_data, integrity_hash
      ) VALUES ${placeholders}
    `;

    await this.db.query(query, values.flat());
  }

  /**
   * Write single audit entry immediately
   */
  async writeAuditEntry(auditEntry) {
    await this.writeBatchToDatabase([auditEntry]);
  }

  /**
   * Encrypt sensitive data
   */
  async encryptSensitiveData(data) {
    try {
      const sensitiveData = {
        old_values: data.old_values,
        new_values: data.new_values,
        request_data: data.request_data,
        response_data: data.response_data
      };

      const cipher = crypto.createCipher('aes-256-cbc', this.config.encryptionKey);
      let encrypted = cipher.update(JSON.stringify(sensitiveData), 'utf8', 'hex');
      encrypted += cipher.final('hex');

      return encrypted;
    } catch (error) {
      console.error('Encryption failed:', error);
      return null;
    }
  }

  /**
   * Generate device fingerprint
   */
  generateDeviceFingerprint(userAgent, ipAddress) {
    const data = `${userAgent || ''}|${ipAddress || ''}`;
    return crypto.createHash('sha256').update(data).digest('hex').substring(0, 16);
  }

  /**
   * Generate audit ID
   */
  generateAuditId() {
    return `audit_${Date.now()}_${crypto.randomBytes(8).toString('hex')}`;
  }

  /**
   * Generate integrity hash
   */
  generateIntegrityHash(entry) {
    const dataToHash = {
      audit_id: entry.audit_id,
      timestamp: entry.timestamp,
      event_type: entry.event_type,
      user_id: entry.user_id,
      action: entry.action,
      resource: entry.resource
    };

    return crypto.createHash('sha256')
      .update(JSON.stringify(dataToHash))
      .digest('hex');
  }

  /**
   * Detect event category
   */
  detectCategory(eventType) {
    const categoryMap = {
      login: this.eventCategories.AUTHENTICATION,
      logout: this.eventCategories.AUTHENTICATION,
      register: this.eventCategories.AUTHENTICATION,
      password_reset: this.eventCategories.AUTHENTICATION,
      mfa: this.eventCategories.AUTHENTICATION,
      
      permission_check: this.eventCategories.AUTHORIZATION,
      role_assignment: this.eventCategories.AUTHORIZATION,
      access_denied: this.eventCategories.AUTHORIZATION,
      
      data_view: this.eventCategories.DATA_ACCESS,
      data_export: this.eventCategories.DATA_ACCESS,
      api_call: this.eventCategories.DATA_ACCESS,
      
      data_create: this.eventCategories.DATA_MODIFICATION,
      data_update: this.eventCategories.DATA_MODIFICATION,
      data_delete: this.eventCategories.DATA_MODIFICATION,
      
      admin_action: this.eventCategories.SYSTEM_ADMIN,
      system_config: this.eventCategories.SYSTEM_ADMIN,
      user_management: this.eventCategories.SYSTEM_ADMIN,
      
      security_alert: this.eventCategories.SECURITY,
      suspicious_activity: this.eventCategories.SECURITY,
      brute_force: this.eventCategories.SECURITY
    };

    for (const [pattern, category] of Object.entries(categoryMap)) {
      if (eventType?.toLowerCase().includes(pattern)) {
        return category;
      }
    }

    return 'general';
  }

  /**
   * Determine severity based on event
   */
  determineSeverity(event) {
    if (event.severity) return event.severity;

    const riskScore = this.calculateRiskScore(event);
    
    if (riskScore >= 8) return 'critical';
    if (riskScore >= 6) return 'high';
    if (riskScore >= 4) return 'medium';
    if (riskScore >= 2) return 'low';
    return 'info';
  }

  /**
   * Check if event involves financial data
   */
  isFinancialData(event) {
    const financialKeywords = ['payment', 'transaction', 'billing', 'invoice', 'revenue'];
    const eventData = JSON.stringify(event).toLowerCase();
    return financialKeywords.some(keyword => eventData.includes(keyword));
  }

  /**
   * Check if event involves personal data
   */
  isPersonalData(event) {
    const personalKeywords = ['email', 'name', 'address', 'phone', 'user', 'profile'];
    const eventData = JSON.stringify(event).toLowerCase();
    return personalKeywords.some(keyword => eventData.includes(keyword));
  }

  /**
   * Check if event involves payment data
   */
  isPaymentData(event) {
    const paymentKeywords = ['card', 'payment', 'billing', 'checkout', 'transaction'];
    const eventData = JSON.stringify(event).toLowerCase();
    return paymentKeywords.some(keyword => eventData.includes(keyword));
  }

  /**
   * Get geolocation for IP address
   */
  async getGeolocation(ipAddress) {
    if (!ipAddress || ipAddress === '127.0.0.1' || ipAddress === '::1') {
      return { country: 'local', city: 'localhost' };
    }

    try {
      // Use a geolocation service (placeholder implementation)
      // In production, integrate with MaxMind, IP2Location, or similar
      return {
        country: 'Unknown',
        region: 'Unknown',
        city: 'Unknown',
        latitude: null,
        longitude: null
      };
    } catch (error) {
      return { country: 'Error', city: 'Error' };
    }
  }

  /**
   * Emit real-time event for monitoring
   */
  emitRealTimeEvent(auditEntry) {
    // Emit to event bus for real-time monitoring
    process.emit('auditEvent', auditEntry);
  }

  /**
   * Fallback logging to file system
   */
  fallbackLog(event, error) {
    try {
      const fs = require('fs');
      const path = require('path');
      
      const logDir = path.join(process.cwd(), 'logs', 'audit-fallback');
      if (!fs.existsSync(logDir)) {
        fs.mkdirSync(logDir, { recursive: true });
      }

      const logFile = path.join(logDir, `audit-fallback-${new Date().toISOString().split('T')[0]}.log`);
      const logEntry = {
        timestamp: new Date().toISOString(),
        original_event: event,
        error: error.message,
        stack: error.stack
      };

      fs.appendFileSync(logFile, JSON.stringify(logEntry) + '\n');
    } catch (fallbackError) {
      console.error('Fallback logging also failed:', fallbackError);
    }
  }

  /**
   * Search audit logs with filters
   */
  async searchAuditLogs(filters = {}) {
    const {
      startDate,
      endDate,
      userId,
      eventType,
      category,
      severity,
      resource,
      ipAddress,
      riskScoreMin,
      complianceTag,
      limit = 100,
      offset = 0
    } = filters;

    let whereClause = '1=1';
    const params = [];
    let paramIndex = 1;

    if (startDate) {
      whereClause += ` AND timestamp >= $${paramIndex}`;
      params.push(startDate);
      paramIndex++;
    }

    if (endDate) {
      whereClause += ` AND timestamp <= $${paramIndex}`;
      params.push(endDate);
      paramIndex++;
    }

    if (userId) {
      whereClause += ` AND user_id = $${paramIndex}`;
      params.push(userId);
      paramIndex++;
    }

    if (eventType) {
      whereClause += ` AND event_type ILIKE $${paramIndex}`;
      params.push(`%${eventType}%`);
      paramIndex++;
    }

    if (category) {
      whereClause += ` AND category = $${paramIndex}`;
      params.push(category);
      paramIndex++;
    }

    if (severity) {
      whereClause += ` AND severity = $${paramIndex}`;
      params.push(severity);
      paramIndex++;
    }

    if (resource) {
      whereClause += ` AND resource ILIKE $${paramIndex}`;
      params.push(`%${resource}%`);
      paramIndex++;
    }

    if (ipAddress) {
      whereClause += ` AND ip_address = $${paramIndex}`;
      params.push(ipAddress);
      paramIndex++;
    }

    if (riskScoreMin) {
      whereClause += ` AND risk_score >= $${paramIndex}`;
      params.push(riskScoreMin);
      paramIndex++;
    }

    if (complianceTag) {
      whereClause += ` AND $${paramIndex} = ANY(compliance_tags)`;
      params.push(complianceTag);
      paramIndex++;
    }

    const query = `
      SELECT * FROM audit_logs 
      WHERE ${whereClause}
      ORDER BY timestamp DESC 
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;

    params.push(limit, offset);

    const result = await this.db.query(query, params);
    return result.rows;
  }

  /**
   * Generate compliance report
   */
  async generateComplianceReport(framework, startDate, endDate) {
    const query = `
      SELECT 
        event_type,
        category,
        severity,
        COUNT(*) as event_count,
        AVG(risk_score) as avg_risk_score,
        MAX(risk_score) as max_risk_score
      FROM audit_logs 
      WHERE $1 = ANY(compliance_tags)
      AND timestamp BETWEEN $2 AND $3
      GROUP BY event_type, category, severity
      ORDER BY event_count DESC
    `;

    const result = await this.db.query(query, [framework, startDate, endDate]);
    
    return {
      framework,
      period: { start: startDate, end: endDate },
      summary: result.rows,
      total_events: result.rows.reduce((sum, row) => sum + parseInt(row.event_count), 0),
      generated_at: new Date().toISOString()
    };
  }

  /**
   * Shutdown audit logger gracefully
   */
  async shutdown() {
    console.log('Shutting down audit logger...');
    
    if (this.batchTimer) {
      clearInterval(this.batchTimer);
    }

    // Flush remaining logs
    await this.flushBatch();
    
    console.log('Audit logger shutdown complete');
  }
}

/**
 * Express middleware for automatic audit logging
 */
const auditMiddleware = (auditLogger, options = {}) => {
  return (req, res, next) => {
    const startTime = Date.now();
    
    // Store original res.json to capture response data
    const originalJson = res.json;
    res.json = function(data) {
      // Capture response data for audit
      req.auditResponseData = data;
      return originalJson.call(this, data);
    };

    // Store original res.end to trigger audit logging
    const originalEnd = res.end;
    res.end = function(...args) {
      const endTime = Date.now();
      const duration = endTime - startTime;

      // Create audit event
      const auditEvent = {
        eventType: `${req.method}_${req.route?.path || req.path}`,
        category: 'data_access',
        userId: req.user?.id,
        sessionId: req.sessionID,
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        requestId: req.id,
        action: `${req.method} ${req.path}`,
        resource: req.route?.path || req.path,
        requestData: req.body,
        responseData: req.auditResponseData,
        metadata: {
          statusCode: res.statusCode,
          duration,
          method: req.method,
          query: req.query,
          params: req.params
        },
        success: res.statusCode < 400
      };

      // Log audit event asynchronously
      setImmediate(() => {
        auditLogger.logAudit(auditEvent).catch(error => {
          console.error('Audit logging failed:', error);
        });
      });

      return originalEnd.apply(this, args);
    };

    next();
  };
};

module.exports = {
  AuditLogger,
  auditMiddleware
};