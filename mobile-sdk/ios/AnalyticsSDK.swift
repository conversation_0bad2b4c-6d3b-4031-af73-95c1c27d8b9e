/**
 * Analytics SDK for iOS with Edge Processing
 * High-performance mobile analytics with local processing, intelligent caching, and offline capabilities
 */

import Foundation
import UIKit
import Network
import CoreLocation
import UserNotifications
import WebKit

// MARK: - Configuration

public struct AnalyticsConfig {
    public let apiKey: String
    public let userId: String?
    public let environment: Environment
    public let edgeEnabled: Bool
    public let offlineEnabled: Bool
    public let encryptionEnabled: Bool
    public let batchSize: Int
    public let flushInterval: TimeInterval
    public let sessionTimeout: TimeInterval
    public let debugEnabled: Bool
    public let endpoints: [Region: String]
    public let features: Set<Feature>
    
    public enum Environment {
        case development
        case staging
        case production
    }
    
    public enum Region: String, CaseIterable {
        case usEast1 = "us-east-1"
        case usWest2 = "us-west-2"
        case euWest1 = "eu-west-1"
        case apSoutheast1 = "ap-southeast-1"
        case auto = "auto"
    }
    
    public enum Feature: String, CaseIterable {
        case userTracking = "user_tracking"
        case screenTracking = "screen_tracking"
        case performanceTracking = "performance_tracking"
        case crashTracking = "crash_tracking"
        case networkTracking = "network_tracking"
        case locationTracking = "location_tracking"
        case pushNotifications = "push_notifications"
        case heatmaps = "heatmaps"
        case sessionReplay = "session_replay"
        case abtesting = "ab_testing"
    }
    
    public init(
        apiKey: String,
        userId: String? = nil,
        environment: Environment = .production,
        edgeEnabled: Bool = true,
        offlineEnabled: Bool = true,
        encryptionEnabled: Bool = true,
        batchSize: Int = 20,
        flushInterval: TimeInterval = 30,
        sessionTimeout: TimeInterval = 1800,
        debugEnabled: Bool = false,
        endpoints: [Region: String] = [:],
        features: Set<Feature> = [.userTracking, .screenTracking, .performanceTracking]
    ) {
        self.apiKey = apiKey
        self.userId = userId
        self.environment = environment
        self.edgeEnabled = edgeEnabled
        self.offlineEnabled = offlineEnabled
        self.encryptionEnabled = encryptionEnabled
        self.batchSize = batchSize
        self.flushInterval = flushInterval
        self.sessionTimeout = sessionTimeout
        self.debugEnabled = debugEnabled
        self.features = features
        
        // Default endpoints
        var defaultEndpoints = endpoints
        if defaultEndpoints.isEmpty {
            defaultEndpoints = [
                .usEast1: "https://us-east-1.edge.analytics-platform.com",
                .usWest2: "https://us-west-2.edge.analytics-platform.com",
                .euWest1: "https://eu-west-1.edge.analytics-platform.com",
                .apSoutheast1: "https://ap-southeast-1.edge.analytics-platform.com",
                .auto: "https://api.analytics-platform.com"
            ]
        }
        self.endpoints = defaultEndpoints
    }
}

// MARK: - Event Models

public struct AnalyticsEvent: Codable {
    public let id: String
    public let type: String
    public let userId: String?
    public let sessionId: String
    public let timestamp: Date
    public let properties: [String: AnyCodable]
    public let context: EventContext
    public let edgeProcessed: Bool
    
    public init(
        type: String,
        userId: String? = nil,
        sessionId: String,
        properties: [String: Any] = [:],
        context: EventContext,
        edgeProcessed: Bool = false
    ) {
        self.id = UUID().uuidString
        self.type = type
        self.userId = userId
        self.sessionId = sessionId
        self.timestamp = Date()
        self.properties = properties.mapValues { AnyCodable($0) }
        self.context = context
        self.edgeProcessed = edgeProcessed
    }
}

public struct EventContext: Codable {
    public let app: AppContext
    public let device: DeviceContext
    public let network: NetworkContext
    public let location: LocationContext?
    public let session: SessionContext
    
    public struct AppContext: Codable {
        public let name: String
        public let version: String
        public let build: String
        public let namespace: String
    }
    
    public struct DeviceContext: Codable {
        public let id: String
        public let manufacturer: String
        public let model: String
        public let type: String
        public let os: String
        public let osVersion: String
        public let screenWidth: Int
        public let screenHeight: Int
        public let locale: String
        public let timezone: String
    }
    
    public struct NetworkContext: Codable {
        public let type: String
        public let carrier: String?
        public let wifi: Bool
        public let cellular: Bool
    }
    
    public struct LocationContext: Codable {
        public let latitude: Double
        public let longitude: Double
        public let accuracy: Double
        public let city: String?
        public let region: String?
        public let country: String?
    }
    
    public struct SessionContext: Codable {
        public let id: String
        public let startTime: Date
        public let duration: TimeInterval
        public let eventCount: Int
        public let isFirstSession: Bool
    }
}

// MARK: - Edge Processor

internal class EdgeProcessor {
    private let config: AnalyticsConfig
    private var mlModels: [String: Any] = [:]
    private let queue = DispatchQueue(label: "edge-processor", qos: .utility)
    
    init(config: AnalyticsConfig) {
        self.config = config
        setupMLModels()
    }
    
    private func setupMLModels() {
        queue.async { [weak self] in
            // Initialize lightweight ML models for edge processing
            self?.initializeUserSegmentationModel()
            self?.initializeAnomalyDetectionModel()
            self?.initializeSessionClassificationModel()
        }
    }
    
    private func initializeUserSegmentationModel() {
        // Simplified user segmentation model
        // In production, this would load a CoreML model
    }
    
    private func initializeAnomalyDetectionModel() {
        // Anomaly detection for unusual user behavior
    }
    
    private func initializeSessionClassificationModel() {
        // Session quality and engagement classification
    }
    
    func processEvent(_ event: AnalyticsEvent) -> AnalyticsEvent {
        guard config.edgeEnabled else { return event }
        
        var processedEvent = event
        var enhancedProperties = event.properties
        
        // User segmentation
        if let userSegment = classifyUser(event) {
            enhancedProperties["user_segment"] = AnyCodable(userSegment)
        }
        
        // Anomaly detection
        let anomalyScore = detectAnomalies(event)
        enhancedProperties["anomaly_score"] = AnyCodable(anomalyScore)
        
        // Session classification
        if let sessionQuality = classifySession(event) {
            enhancedProperties["session_quality"] = AnyCodable(sessionQuality)
        }
        
        // Performance insights
        if event.type == "screen_view" {
            enhancedProperties["predicted_engagement"] = AnyCodable(predictEngagement(event))
        }
        
        // Create processed event
        processedEvent = AnalyticsEvent(
            type: event.type,
            userId: event.userId,
            sessionId: event.sessionId,
            properties: enhancedProperties.mapValues { $0.value },
            context: event.context,
            edgeProcessed: true
        )
        
        return processedEvent
    }
    
    private func classifyUser(_ event: AnalyticsEvent) -> String? {
        // Simplified user classification based on behavior patterns
        guard let userId = event.userId else { return nil }
        
        // This would use ML model in production
        let behavioral_patterns = ["new_user", "active_user", "power_user", "churning_user"]
        return behavioral_patterns.randomElement()
    }
    
    private func detectAnomalies(_ event: AnalyticsEvent) -> Double {
        // Simplified anomaly detection
        // Returns score between 0.0 (normal) and 1.0 (highly anomalous)
        
        // Check for unusual timing patterns
        let hour = Calendar.current.component(.hour, from: event.timestamp)
        let isUnusualTime = hour < 6 || hour > 23
        
        // Check for rapid event frequency
        // This would be more sophisticated in production
        
        return isUnusualTime ? 0.8 : 0.1
    }
    
    private func classifySession(_ event: AnalyticsEvent) -> String? {
        let duration = event.context.session.duration
        let eventCount = event.context.session.eventCount
        
        if duration > 300 && eventCount > 10 {
            return "high_engagement"
        } else if duration > 60 && eventCount > 3 {
            return "medium_engagement"
        } else {
            return "low_engagement"
        }
    }
    
    private func predictEngagement(_ event: AnalyticsEvent) -> Double {
        // Predict user engagement score for current session
        let sessionDuration = event.context.session.duration
        let eventCount = Double(event.context.session.eventCount)
        
        // Simplified engagement prediction
        let engagementScore = min(1.0, (sessionDuration / 300.0) * 0.6 + (eventCount / 20.0) * 0.4)
        return engagementScore
    }
}

// MARK: - Network Layer

internal class NetworkManager {
    private let config: AnalyticsConfig
    private let session: URLSession
    private let monitor = NWPathMonitor()
    private let monitorQueue = DispatchQueue(label: "network-monitor")
    
    private(set) var isConnected = true
    private(set) var currentRegion: AnalyticsConfig.Region = .auto
    private(set) var optimalEndpoint: String = ""
    
    init(config: AnalyticsConfig) {
        self.config = config
        
        // Configure URL session for edge processing
        let configuration = URLSessionConfiguration.default
        configuration.timeoutIntervalForRequest = 30
        configuration.timeoutIntervalForResource = 60
        configuration.httpMaximumConnectionsPerHost = 6
        configuration.requestCachePolicy = .reloadIgnoringLocalCacheData
        
        self.session = URLSession(configuration: configuration)
        
        setupNetworkMonitoring()
        detectOptimalEndpoint()
    }
    
    private func setupNetworkMonitoring() {
        monitor.pathUpdateHandler = { [weak self] path in
            DispatchQueue.main.async {
                self?.isConnected = path.status == .satisfied
                self?.handleNetworkChange(path)
            }
        }
        monitor.start(queue: monitorQueue)
    }
    
    private func handleNetworkChange(_ path: NWPath) {
        if path.status == .satisfied {
            detectOptimalEndpoint()
        }
    }
    
    private func detectOptimalEndpoint() {
        // Detect optimal endpoint based on geolocation and latency
        Task {
            let detectedRegion = await detectRegion()
            let endpoint = config.endpoints[detectedRegion] ?? config.endpoints[.auto]!
            
            // Test endpoint latency
            if await testEndpointLatency(endpoint) < 2.0 {
                await MainActor.run {
                    self.currentRegion = detectedRegion
                    self.optimalEndpoint = endpoint
                }
            } else {
                await MainActor.run {
                    self.currentRegion = .auto
                    self.optimalEndpoint = config.endpoints[.auto]!
                }
            }
        }
    }
    
    private func detectRegion() async -> AnalyticsConfig.Region {
        // Use timezone and locale to detect region
        let timeZone = TimeZone.current
        let locale = Locale.current
        
        if let identifier = timeZone.identifier.split(separator: "/").first {
            switch identifier {
            case "America":
                return locale.regionCode?.contains("US") == true ? .usEast1 : .usWest2
            case "Europe":
                return .euWest1
            case "Asia", "Australia":
                return .apSoutheast1
            default:
                return .auto
            }
        }
        
        return .auto
    }
    
    private func testEndpointLatency(_ endpoint: String) async -> TimeInterval {
        guard let url = URL(string: "\(endpoint)/health") else { return 999.0 }
        
        let startTime = CFAbsoluteTimeGetCurrent()
        
        do {
            let (_, response) = try await session.data(from: url)
            if let httpResponse = response as? HTTPURLResponse,
               httpResponse.statusCode == 200 {
                return CFAbsoluteTimeGetCurrent() - startTime
            }
        } catch {
            // Handle error
        }
        
        return 999.0
    }
    
    func sendEvents(_ events: [AnalyticsEvent]) async throws {
        guard !events.isEmpty else { return }
        
        let endpoint = "\(optimalEndpoint)/analytics/events"
        guard let url = URL(string: endpoint) else {
            throw AnalyticsError.invalidEndpoint
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue(config.apiKey, forHTTPHeaderField: "X-API-Key")
        request.setValue("iOS", forHTTPHeaderField: "X-Client-Platform")
        request.setValue(currentRegion.rawValue, forHTTPHeaderField: "X-Edge-Region")
        
        let payload = EventBatch(
            apiKey: config.apiKey,
            events: events,
            batchId: UUID().uuidString,
            platform: "iOS",
            sdkVersion: "1.0.0"
        )
        
        let jsonData = try JSONEncoder().encode(payload)
        request.httpBody = jsonData
        
        let (data, response) = try await session.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw AnalyticsError.invalidResponse
        }
        
        if httpResponse.statusCode != 200 {
            throw AnalyticsError.serverError(httpResponse.statusCode)
        }
    }
}

// MARK: - Storage Layer

internal class StorageManager {
    private let config: AnalyticsConfig
    private let userDefaults = UserDefaults.standard
    private let fileManager = FileManager.default
    private let documentsURL: URL
    
    private let offlineEventsKey = "analytics.offline.events"
    private let sessionDataKey = "analytics.session.data"
    private let userDataKey = "analytics.user.data"
    
    init(config: AnalyticsConfig) {
        self.config = config
        
        documentsURL = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
        createDirectoriesIfNeeded()
    }
    
    private func createDirectoriesIfNeeded() {
        let analyticsDir = documentsURL.appendingPathComponent("analytics")
        
        if !fileManager.fileExists(atPath: analyticsDir.path) {
            try? fileManager.createDirectory(at: analyticsDir, withIntermediateDirectories: true)
        }
    }
    
    // MARK: - Offline Events
    
    func saveOfflineEvents(_ events: [AnalyticsEvent]) {
        guard config.offlineEnabled else { return }
        
        let existingEvents = loadOfflineEvents()
        let allEvents = existingEvents + events
        
        // Limit offline storage to prevent excessive disk usage
        let limitedEvents = Array(allEvents.suffix(1000))
        
        do {
            let data = try JSONEncoder().encode(limitedEvents)
            let url = documentsURL.appendingPathComponent("analytics/offline_events.json")
            
            if config.encryptionEnabled {
                let encryptedData = try encrypt(data)
                try encryptedData.write(to: url)
            } else {
                try data.write(to: url)
            }
        } catch {
            print("Failed to save offline events: \(error)")
        }
    }
    
    func loadOfflineEvents() -> [AnalyticsEvent] {
        let url = documentsURL.appendingPathComponent("analytics/offline_events.json")
        
        guard fileManager.fileExists(atPath: url.path) else { return [] }
        
        do {
            let data = try Data(contentsOf: url)
            
            let decodedData: Data
            if config.encryptionEnabled {
                decodedData = try decrypt(data)
            } else {
                decodedData = data
            }
            
            return try JSONDecoder().decode([AnalyticsEvent].self, from: decodedData)
        } catch {
            print("Failed to load offline events: \(error)")
            return []
        }
    }
    
    func clearOfflineEvents() {
        let url = documentsURL.appendingPathComponent("analytics/offline_events.json")
        try? fileManager.removeItem(at: url)
    }
    
    // MARK: - Session Data
    
    func saveSessionData(_ session: SessionData) {
        do {
            let data = try JSONEncoder().encode(session)
            userDefaults.set(data, forKey: sessionDataKey)
        } catch {
            print("Failed to save session data: \(error)")
        }
    }
    
    func loadSessionData() -> SessionData? {
        guard let data = userDefaults.data(forKey: sessionDataKey) else { return nil }
        
        do {
            return try JSONDecoder().decode(SessionData.self, from: data)
        } catch {
            print("Failed to load session data: \(error)")
            return nil
        }
    }
    
    // MARK: - User Data
    
    func saveUserData(_ user: UserData) {
        do {
            let data = try JSONEncoder().encode(user)
            userDefaults.set(data, forKey: userDataKey)
        } catch {
            print("Failed to save user data: \(error)")
        }
    }
    
    func loadUserData() -> UserData? {
        guard let data = userDefaults.data(forKey: userDataKey) else { return nil }
        
        do {
            return try JSONDecoder().decode(UserData.self, from: data)
        } catch {
            print("Failed to load user data: \(error)")
            return nil
        }
    }
    
    // MARK: - Encryption
    
    private func encrypt(_ data: Data) throws -> Data {
        // Simplified encryption - in production use proper AES encryption
        return data
    }
    
    private func decrypt(_ data: Data) throws -> Data {
        // Simplified decryption - in production use proper AES decryption
        return data
    }
}

// MARK: - Session Manager

internal class SessionManager {
    private let config: AnalyticsConfig
    private let storage: StorageManager
    
    private(set) var currentSession: SessionData
    private var sessionTimer: Timer?
    private var backgroundTime: Date?
    
    init(config: AnalyticsConfig, storage: StorageManager) {
        self.config = config
        self.storage = storage
        
        // Load existing session or create new one
        if let existingSession = storage.loadSessionData(),
           Date().timeIntervalSince(existingSession.lastActivity) < config.sessionTimeout {
            self.currentSession = existingSession
        } else {
            self.currentSession = SessionData()
        }
        
        setupSessionTracking()
    }
    
    private func setupSessionTracking() {
        // Track app lifecycle events
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appDidEnterBackground),
            name: UIApplication.didEnterBackgroundNotification,
            object: nil
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appWillEnterForeground),
            name: UIApplication.willEnterForegroundNotification,
            object: nil
        )
        
        startSessionTimer()
    }
    
    private func startSessionTimer() {
        sessionTimer = Timer.scheduledTimer(withTimeInterval: 30, repeats: true) { [weak self] _ in
            self?.updateSession()
        }
    }
    
    @objc private func appDidEnterBackground() {
        backgroundTime = Date()
        sessionTimer?.invalidate()
        updateSession()
        storage.saveSessionData(currentSession)
    }
    
    @objc private func appWillEnterForeground() {
        if let backgroundTime = backgroundTime {
            let backgroundDuration = Date().timeIntervalSince(backgroundTime)
            
            if backgroundDuration > config.sessionTimeout {
                // Start new session
                currentSession = SessionData()
            }
        }
        
        self.backgroundTime = nil
        startSessionTimer()
    }
    
    func trackEvent() {
        currentSession.eventCount += 1
        currentSession.lastActivity = Date()
        updateSession()
    }
    
    private func updateSession() {
        currentSession.duration = Date().timeIntervalSince(currentSession.startTime)
        storage.saveSessionData(currentSession)
    }
    
    func endSession() {
        sessionTimer?.invalidate()
        updateSession()
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
        sessionTimer?.invalidate()
    }
}

// MARK: - Main Analytics SDK

public class AnalyticsSDK {
    public static let shared = AnalyticsSDK()
    
    private var config: AnalyticsConfig?
    private var networkManager: NetworkManager?
    private var storageManager: StorageManager?
    private var sessionManager: SessionManager?
    private var edgeProcessor: EdgeProcessor?
    
    private var eventQueue: [AnalyticsEvent] = []
    private var flushTimer: Timer?
    private let queueLock = NSLock()
    
    private var deviceContext: EventContext.DeviceContext!
    private var appContext: EventContext.AppContext!
    
    private init() {}
    
    // MARK: - Initialization
    
    public func initialize(with config: AnalyticsConfig) {
        self.config = config
        
        setupContext()
        setupManagers()
        startFlushTimer()
        
        if config.features.contains(.screenTracking) {
            setupAutomaticScreenTracking()
        }
        
        if config.features.contains(.crashTracking) {
            setupCrashTracking()
        }
        
        track("sdk_initialized", properties: [
            "version": "1.0.0",
            "features": config.features.map { $0.rawValue }
        ])
    }
    
    private func setupContext() {
        guard let config = config else { return }
        
        // Device context
        let device = UIDevice.current
        let screen = UIScreen.main
        
        deviceContext = EventContext.DeviceContext(
            id: getDeviceId(),
            manufacturer: "Apple",
            model: device.model,
            type: device.userInterfaceIdiom == .pad ? "tablet" : "phone",
            os: device.systemName,
            osVersion: device.systemVersion,
            screenWidth: Int(screen.bounds.width),
            screenHeight: Int(screen.bounds.height),
            locale: Locale.current.identifier,
            timezone: TimeZone.current.identifier
        )
        
        // App context
        let bundle = Bundle.main
        appContext = EventContext.AppContext(
            name: bundle.object(forInfoDictionaryKey: "CFBundleName") as? String ?? "Unknown",
            version: bundle.object(forInfoDictionaryKey: "CFBundleShortVersionString") as? String ?? "Unknown",
            build: bundle.object(forInfoDictionaryKey: "CFBundleVersion") as? String ?? "Unknown",
            namespace: bundle.bundleIdentifier ?? "Unknown"
        )
    }
    
    private func setupManagers() {
        guard let config = config else { return }
        
        storageManager = StorageManager(config: config)
        networkManager = NetworkManager(config: config)
        sessionManager = SessionManager(config: config, storage: storageManager!)
        edgeProcessor = EdgeProcessor(config: config)
    }
    
    private func startFlushTimer() {
        guard let config = config else { return }
        
        flushTimer = Timer.scheduledTimer(withTimeInterval: config.flushInterval, repeats: true) { [weak self] _ in
            self?.flush()
        }
    }
    
    // MARK: - Event Tracking
    
    public func track(_ eventType: String, properties: [String: Any] = [:]) {
        guard let config = config,
              let sessionManager = sessionManager else { return }
        
        sessionManager.trackEvent()
        
        let context = EventContext(
            app: appContext,
            device: deviceContext,
            network: getNetworkContext(),
            location: getLocationContext(),
            session: EventContext.SessionContext(
                id: sessionManager.currentSession.id,
                startTime: sessionManager.currentSession.startTime,
                duration: sessionManager.currentSession.duration,
                eventCount: sessionManager.currentSession.eventCount,
                isFirstSession: sessionManager.currentSession.isFirstSession
            )
        )
        
        var event = AnalyticsEvent(
            type: eventType,
            userId: config.userId,
            sessionId: sessionManager.currentSession.id,
            properties: properties,
            context: context
        )
        
        // Edge processing
        if let edgeProcessor = edgeProcessor {
            event = edgeProcessor.processEvent(event)
        }
        
        addToQueue(event)
        
        // Auto-flush for critical events
        if isCriticalEvent(eventType) {
            flush()
        }
    }
    
    public func identify(_ userId: String, traits: [String: Any] = [:]) {
        config?.userId = userId
        
        track("identify", properties: [
            "user_id": userId,
            "traits": traits
        ])
    }
    
    public func screen(_ name: String, properties: [String: Any] = [:]) {
        var screenProperties = properties
        screenProperties["screen_name"] = name
        
        track("screen_view", properties: screenProperties)
    }
    
    // MARK: - Queue Management
    
    private func addToQueue(_ event: AnalyticsEvent) {
        queueLock.lock()
        defer { queueLock.unlock() }
        
        eventQueue.append(event)
        
        // Auto-flush if queue is full
        if eventQueue.count >= config?.batchSize ?? 20 {
            flush()
        }
    }
    
    public func flush() {
        queueLock.lock()
        let eventsToSend = Array(eventQueue)
        eventQueue.removeAll()
        queueLock.unlock()
        
        guard !eventsToSend.isEmpty else { return }
        
        Task {
            do {
                if networkManager?.isConnected == true {
                    try await networkManager?.sendEvents(eventsToSend)
                } else {
                    // Store offline
                    storageManager?.saveOfflineEvents(eventsToSend)
                }
            } catch {
                // Save to offline storage on failure
                storageManager?.saveOfflineEvents(eventsToSend)
                
                if config?.debugEnabled == true {
                    print("Analytics: Failed to send events, saved offline: \(error)")
                }
            }
        }
        
        // Process offline events when back online
        if networkManager?.isConnected == true {
            processOfflineEvents()
        }
    }
    
    private func processOfflineEvents() {
        guard let storageManager = storageManager else { return }
        
        let offlineEvents = storageManager.loadOfflineEvents()
        guard !offlineEvents.isEmpty else { return }
        
        Task {
            do {
                try await networkManager?.sendEvents(offlineEvents)
                storageManager.clearOfflineEvents()
                
                if config?.debugEnabled == true {
                    print("Analytics: Processed \(offlineEvents.count) offline events")
                }
            } catch {
                if config?.debugEnabled == true {
                    print("Analytics: Failed to process offline events: \(error)")
                }
            }
        }
    }
    
    // MARK: - Helper Methods
    
    private func getDeviceId() -> String {
        let key = "analytics.device.id"
        
        if let existingId = UserDefaults.standard.string(forKey: key) {
            return existingId
        }
        
        let newId = UUID().uuidString
        UserDefaults.standard.set(newId, forKey: key)
        return newId
    }
    
    private func getNetworkContext() -> EventContext.NetworkContext {
        // Simplified network detection
        return EventContext.NetworkContext(
            type: "wifi", // Would detect actual network type
            carrier: nil,
            wifi: true,
            cellular: false
        )
    }
    
    private func getLocationContext() -> EventContext.LocationContext? {
        // Location tracking would require proper permissions
        return nil
    }
    
    private func isCriticalEvent(_ eventType: String) -> Bool {
        let criticalEvents = ["error", "crash", "purchase", "signup", "login"]
        return criticalEvents.contains(eventType)
    }
    
    private func setupAutomaticScreenTracking() {
        // Implement automatic screen tracking using method swizzling
        // This would track UIViewController appearances automatically
    }
    
    private func setupCrashTracking() {
        // Implement crash tracking
        NSSetUncaughtExceptionHandler { exception in
            AnalyticsSDK.shared.track("crash", properties: [
                "exception_name": exception.name.rawValue,
                "exception_reason": exception.reason ?? "Unknown",
                "stack_trace": exception.callStackSymbols
            ])
            AnalyticsSDK.shared.flush()
        }
    }
    
    // MARK: - Cleanup
    
    deinit {
        flushTimer?.invalidate()
        flush()
    }
}

// MARK: - Supporting Models

public struct AnyCodable: Codable {
    public let value: Any
    
    public init(_ value: Any) {
        self.value = value
    }
    
    public init(from decoder: Decoder) throws {
        let container = try decoder.singleValueContainer()
        
        if let string = try? container.decode(String.self) {
            value = string
        } else if let int = try? container.decode(Int.self) {
            value = int
        } else if let double = try? container.decode(Double.self) {
            value = double
        } else if let bool = try? container.decode(Bool.self) {
            value = bool
        } else if container.decodeNil() {
            value = NSNull()
        } else {
            throw DecodingError.typeMismatch(AnyCodable.self, DecodingError.Context(codingPath: decoder.codingPath, debugDescription: "Unsupported type"))
        }
    }
    
    public func encode(to encoder: Encoder) throws {
        var container = encoder.singleValueContainer()
        
        if let string = value as? String {
            try container.encode(string)
        } else if let int = value as? Int {
            try container.encode(int)
        } else if let double = value as? Double {
            try container.encode(double)
        } else if let bool = value as? Bool {
            try container.encode(bool)
        } else if value is NSNull {
            try container.encodeNil()
        } else {
            throw EncodingError.invalidValue(value, EncodingError.Context(codingPath: encoder.codingPath, debugDescription: "Unsupported type"))
        }
    }
}

internal struct EventBatch: Codable {
    let apiKey: String
    let events: [AnalyticsEvent]
    let batchId: String
    let platform: String
    let sdkVersion: String
}

internal struct SessionData: Codable {
    let id: String
    let startTime: Date
    var duration: TimeInterval
    var eventCount: Int
    var lastActivity: Date
    let isFirstSession: Bool
    
    init() {
        self.id = UUID().uuidString
        self.startTime = Date()
        self.duration = 0
        self.eventCount = 0
        self.lastActivity = Date()
        self.isFirstSession = !UserDefaults.standard.bool(forKey: "analytics.has.session")
        
        UserDefaults.standard.set(true, forKey: "analytics.has.session")
    }
}

internal struct UserData: Codable {
    let id: String
    var traits: [String: AnyCodable]
    var firstSeen: Date
    var lastSeen: Date
}

public enum AnalyticsError: Error {
    case notInitialized
    case invalidEndpoint
    case invalidResponse
    case serverError(Int)
    case networkError(Error)
}