/**
 * Analytics SDK for Android with Edge Processing
 * High-performance mobile analytics with local processing, intelligent caching, and offline capabilities
 */

package com.analyticsplatform.sdk

import android.app.Application
import android.content.Context
import android.content.SharedPreferences
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkRequest
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.telephony.TelephonyManager
import android.util.Log
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ProcessLifecycleOwner
import com.google.gson.*
import com.google.gson.annotations.SerializedName
import kotlinx.coroutines.*
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody
import java.io.File
import java.io.IOException
import java.security.MessageDigest
import java.util.*
import java.util.concurrent.ConcurrentLinkedQueue
import java.util.concurrent.TimeUnit
import javax.crypto.Cipher
import javax.crypto.KeyGenerator
import javax.crypto.SecretKey
import javax.crypto.spec.SecretKeySpec
import kotlin.collections.HashMap
import kotlin.random.Random

// MARK: - Configuration

data class AnalyticsConfig(
    val apiKey: String,
    val userId: String? = null,
    val environment: Environment = Environment.PRODUCTION,
    val edgeEnabled: Boolean = true,
    val offlineEnabled: Boolean = true,
    val encryptionEnabled: Boolean = true,
    val batchSize: Int = 20,
    val flushInterval: Long = 30000L, // 30 seconds
    val sessionTimeout: Long = 1800000L, // 30 minutes
    val debugEnabled: Boolean = false,
    val endpoints: Map<Region, String> = emptyMap(),
    val features: Set<Feature> = setOf(Feature.USER_TRACKING, Feature.SCREEN_TRACKING, Feature.PERFORMANCE_TRACKING)
) {
    enum class Environment {
        DEVELOPMENT,
        STAGING,
        PRODUCTION
    }
    
    enum class Region(val value: String) {
        US_EAST_1("us-east-1"),
        US_WEST_2("us-west-2"),
        EU_WEST_1("eu-west-1"),
        AP_SOUTHEAST_1("ap-southeast-1"),
        AUTO("auto")
    }
    
    enum class Feature(val value: String) {
        USER_TRACKING("user_tracking"),
        SCREEN_TRACKING("screen_tracking"),
        PERFORMANCE_TRACKING("performance_tracking"),
        CRASH_TRACKING("crash_tracking"),
        NETWORK_TRACKING("network_tracking"),
        LOCATION_TRACKING("location_tracking"),
        PUSH_NOTIFICATIONS("push_notifications"),
        HEATMAPS("heatmaps"),
        SESSION_REPLAY("session_replay"),
        AB_TESTING("ab_testing")
    }
    
    fun getEndpoints(): Map<Region, String> {
        return if (endpoints.isEmpty()) {
            mapOf(
                Region.US_EAST_1 to "https://us-east-1.edge.analytics-platform.com",
                Region.US_WEST_2 to "https://us-west-2.edge.analytics-platform.com",
                Region.EU_WEST_1 to "https://eu-west-1.edge.analytics-platform.com",
                Region.AP_SOUTHEAST_1 to "https://ap-southeast-1.edge.analytics-platform.com",
                Region.AUTO to "https://api.analytics-platform.com"
            )
        } else {
            endpoints
        }
    }
}

// MARK: - Event Models

data class AnalyticsEvent(
    val id: String = UUID.randomUUID().toString(),
    val type: String,
    val userId: String? = null,
    val sessionId: String,
    val timestamp: Long = System.currentTimeMillis(),
    val properties: Map<String, Any> = emptyMap(),
    val context: EventContext,
    val edgeProcessed: Boolean = false
)

data class EventContext(
    val app: AppContext,
    val device: DeviceContext,
    val network: NetworkContext,
    val location: LocationContext? = null,
    val session: SessionContext
) {
    data class AppContext(
        val name: String,
        val version: String,
        val build: String,
        val namespace: String
    )
    
    data class DeviceContext(
        val id: String,
        val manufacturer: String,
        val model: String,
        val type: String,
        val os: String,
        val osVersion: String,
        val screenWidth: Int,
        val screenHeight: Int,
        val locale: String,
        val timezone: String
    )
    
    data class NetworkContext(
        val type: String,
        val carrier: String? = null,
        val wifi: Boolean,
        val cellular: Boolean
    )
    
    data class LocationContext(
        val latitude: Double,
        val longitude: Double,
        val accuracy: Double,
        val city: String? = null,
        val region: String? = null,
        val country: String? = null
    )
    
    data class SessionContext(
        val id: String,
        val startTime: Long,
        val duration: Long,
        val eventCount: Int,
        val isFirstSession: Boolean
    )
}

// MARK: - Edge Processor

internal class EdgeProcessor(private val config: AnalyticsConfig) {
    private val mlModels = mutableMapOf<String, Any>()
    private val coroutineScope = CoroutineScope(Dispatchers.Default + SupervisorJob())
    
    init {
        setupMLModels()
    }
    
    private fun setupMLModels() {
        coroutineScope.launch {
            // Initialize lightweight ML models for edge processing
            initializeUserSegmentationModel()
            initializeAnomalyDetectionModel()
            initializeSessionClassificationModel()
        }
    }
    
    private suspend fun initializeUserSegmentationModel() {
        // Simplified user segmentation model
        // In production, this would load a TensorFlow Lite model
        withContext(Dispatchers.IO) {
            mlModels["user_segmentation"] = "model_placeholder"
        }
    }
    
    private suspend fun initializeAnomalyDetectionModel() {
        // Anomaly detection for unusual user behavior
        withContext(Dispatchers.IO) {
            mlModels["anomaly_detection"] = "model_placeholder"
        }
    }
    
    private suspend fun initializeSessionClassificationModel() {
        // Session quality and engagement classification
        withContext(Dispatchers.IO) {
            mlModels["session_classification"] = "model_placeholder"
        }
    }
    
    suspend fun processEvent(event: AnalyticsEvent): AnalyticsEvent {
        if (!config.edgeEnabled) return event
        
        return withContext(Dispatchers.Default) {
            val enhancedProperties = event.properties.toMutableMap()
            
            // User segmentation
            classifyUser(event)?.let { userSegment ->
                enhancedProperties["user_segment"] = userSegment
            }
            
            // Anomaly detection
            val anomalyScore = detectAnomalies(event)
            enhancedProperties["anomaly_score"] = anomalyScore
            
            // Session classification
            classifySession(event)?.let { sessionQuality ->
                enhancedProperties["session_quality"] = sessionQuality
            }
            
            // Performance insights
            if (event.type == "screen_view") {
                enhancedProperties["predicted_engagement"] = predictEngagement(event)
            }
            
            event.copy(
                properties = enhancedProperties,
                edgeProcessed = true
            )
        }
    }
    
    private fun classifyUser(event: AnalyticsEvent): String? {
        // Simplified user classification based on behavior patterns
        val userId = event.userId ?: return null
        
        // This would use ML model in production
        val behavioralPatterns = listOf("new_user", "active_user", "power_user", "churning_user")
        return behavioralPatterns.random()
    }
    
    private fun detectAnomalies(event: AnalyticsEvent): Double {
        // Simplified anomaly detection
        // Returns score between 0.0 (normal) and 1.0 (highly anomalous)
        
        // Check for unusual timing patterns
        val calendar = Calendar.getInstance()
        calendar.timeInMillis = event.timestamp
        val hour = calendar.get(Calendar.HOUR_OF_DAY)
        val isUnusualTime = hour < 6 || hour > 23
        
        // Check for rapid event frequency
        // This would be more sophisticated in production
        
        return if (isUnusualTime) 0.8 else 0.1
    }
    
    private fun classifySession(event: AnalyticsEvent): String? {
        val duration = event.context.session.duration
        val eventCount = event.context.session.eventCount
        
        return when {
            duration > 300000 && eventCount > 10 -> "high_engagement"
            duration > 60000 && eventCount > 3 -> "medium_engagement"
            else -> "low_engagement"
        }
    }
    
    private fun predictEngagement(event: AnalyticsEvent): Double {
        // Predict user engagement score for current session
        val sessionDuration = event.context.session.duration
        val eventCount = event.context.session.eventCount.toDouble()
        
        // Simplified engagement prediction
        val engagementScore = minOf(1.0, (sessionDuration / 300000.0) * 0.6 + (eventCount / 20.0) * 0.4)
        return engagementScore
    }
    
    fun cleanup() {
        coroutineScope.cancel()
    }
}

// MARK: - Network Manager

internal class NetworkManager(
    private val context: Context,
    private val config: AnalyticsConfig
) {
    private val okHttpClient: OkHttpClient
    private val gson = Gson()
    private val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
    
    @Volatile
    var isConnected = true
        private set
    
    @Volatile
    var currentRegion = AnalyticsConfig.Region.AUTO
        private set
    
    @Volatile
    var optimalEndpoint = ""
        private set
    
    private val networkCallback = object : ConnectivityManager.NetworkCallback() {
        override fun onAvailable(network: Network) {
            isConnected = true
            detectOptimalEndpoint()
        }
        
        override fun onLost(network: Network) {
            isConnected = false
        }
    }
    
    init {
        okHttpClient = OkHttpClient.Builder()
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(60, TimeUnit.SECONDS)
            .writeTimeout(60, TimeUnit.SECONDS)
            .addInterceptor { chain ->
                val request = chain.request().newBuilder()
                    .addHeader("X-API-Key", config.apiKey)
                    .addHeader("X-Client-Platform", "Android")
                    .addHeader("X-Edge-Region", currentRegion.value)
                    .build()
                chain.proceed(request)
            }
            .build()
        
        setupNetworkMonitoring()
        detectOptimalEndpoint()
    }
    
    private fun setupNetworkMonitoring() {
        val request = NetworkRequest.Builder()
            .addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
            .build()
        
        connectivityManager.registerNetworkCallback(request, networkCallback)
    }
    
    private fun detectOptimalEndpoint() {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val detectedRegion = detectRegion()
                val endpoint = config.getEndpoints()[detectedRegion] ?: config.getEndpoints()[AnalyticsConfig.Region.AUTO]!!
                
                // Test endpoint latency
                if (testEndpointLatency(endpoint) < 2000) { // 2 seconds
                    currentRegion = detectedRegion
                    optimalEndpoint = endpoint
                } else {
                    currentRegion = AnalyticsConfig.Region.AUTO
                    optimalEndpoint = config.getEndpoints()[AnalyticsConfig.Region.AUTO]!!
                }
                
                if (config.debugEnabled) {
                    Log.d("AnalyticsSDK", "Optimal endpoint: $optimalEndpoint (region: ${currentRegion.value})")
                }
            } catch (e: Exception) {
                if (config.debugEnabled) {
                    Log.e("AnalyticsSDK", "Failed to detect optimal endpoint", e)
                }
            }
        }
    }
    
    private suspend fun detectRegion(): AnalyticsConfig.Region {
        return withContext(Dispatchers.IO) {
            // Use timezone and locale to detect region
            val timeZone = TimeZone.getDefault()
            val locale = Locale.getDefault()
            
            when {
                timeZone.id.startsWith("America/") -> {
                    if (locale.country == "US") AnalyticsConfig.Region.US_EAST_1 else AnalyticsConfig.Region.US_WEST_2
                }
                timeZone.id.startsWith("Europe/") -> AnalyticsConfig.Region.EU_WEST_1
                timeZone.id.startsWith("Asia/") || timeZone.id.startsWith("Australia/") -> AnalyticsConfig.Region.AP_SOUTHEAST_1
                else -> AnalyticsConfig.Region.AUTO
            }
        }
    }
    
    private suspend fun testEndpointLatency(endpoint: String): Long {
        return withContext(Dispatchers.IO) {
            try {
                val startTime = System.currentTimeMillis()
                val request = Request.Builder()
                    .url("$endpoint/health")
                    .build()
                
                okHttpClient.newCall(request).execute().use { response ->
                    if (response.isSuccessful) {
                        System.currentTimeMillis() - startTime
                    } else {
                        999000L
                    }
                }
            } catch (e: Exception) {
                999000L
            }
        }
    }
    
    suspend fun sendEvents(events: List<AnalyticsEvent>) {
        if (events.isEmpty()) return
        
        withContext(Dispatchers.IO) {
            try {
                val endpoint = "$optimalEndpoint/analytics/events"
                val payload = EventBatch(
                    apiKey = config.apiKey,
                    events = events,
                    batchId = UUID.randomUUID().toString(),
                    platform = "Android",
                    sdkVersion = "1.0.0"
                )
                
                val json = gson.toJson(payload)
                val requestBody = json.toRequestBody("application/json".toMediaType())
                
                val request = Request.Builder()
                    .url(endpoint)
                    .post(requestBody)
                    .build()
                
                okHttpClient.newCall(request).execute().use { response ->
                    if (!response.isSuccessful) {
                        throw IOException("Unexpected response: ${response.code}")
                    }
                }
                
                if (config.debugEnabled) {
                    Log.d("AnalyticsSDK", "Successfully sent ${events.size} events")
                }
            } catch (e: Exception) {
                if (config.debugEnabled) {
                    Log.e("AnalyticsSDK", "Failed to send events", e)
                }
                throw e
            }
        }
    }
    
    fun cleanup() {
        connectivityManager.unregisterNetworkCallback(networkCallback)
    }
}

// MARK: - Storage Manager

internal class StorageManager(
    private val context: Context,
    private val config: AnalyticsConfig
) {
    private val sharedPrefs: SharedPreferences = context.getSharedPreferences("analytics_sdk", Context.MODE_PRIVATE)
    private val gson = Gson()
    private val offlineEventsFile = File(context.filesDir, "analytics/offline_events.json")
    
    private val offlineEventsKey = "analytics.offline.events"
    private val sessionDataKey = "analytics.session.data"
    private val userDataKey = "analytics.user.data"
    
    init {
        // Create analytics directory if it doesn't exist
        val analyticsDir = File(context.filesDir, "analytics")
        if (!analyticsDir.exists()) {
            analyticsDir.mkdirs()
        }
    }
    
    // MARK: - Offline Events
    
    fun saveOfflineEvents(events: List<AnalyticsEvent>) {
        if (!config.offlineEnabled) return
        
        try {
            val existingEvents = loadOfflineEvents().toMutableList()
            existingEvents.addAll(events)
            
            // Limit offline storage to prevent excessive disk usage
            val limitedEvents = existingEvents.takeLast(1000)
            
            val json = gson.toJson(limitedEvents)
            val data = if (config.encryptionEnabled) {
                encrypt(json.toByteArray())
            } else {
                json.toByteArray()
            }
            
            offlineEventsFile.writeBytes(data)
            
            if (config.debugEnabled) {
                Log.d("AnalyticsSDK", "Saved ${events.size} events offline")
            }
        } catch (e: Exception) {
            if (config.debugEnabled) {
                Log.e("AnalyticsSDK", "Failed to save offline events", e)
            }
        }
    }
    
    fun loadOfflineEvents(): List<AnalyticsEvent> {
        if (!offlineEventsFile.exists()) return emptyList()
        
        return try {
            val data = offlineEventsFile.readBytes()
            val decryptedData = if (config.encryptionEnabled) {
                decrypt(data)
            } else {
                data
            }
            
            val json = String(decryptedData)
            val type = object : TypeToken<List<AnalyticsEvent>>() {}.type
            gson.fromJson<List<AnalyticsEvent>>(json, type) ?: emptyList()
        } catch (e: Exception) {
            if (config.debugEnabled) {
                Log.e("AnalyticsSDK", "Failed to load offline events", e)
            }
            emptyList()
        }
    }
    
    fun clearOfflineEvents() {
        try {
            if (offlineEventsFile.exists()) {
                offlineEventsFile.delete()
            }
        } catch (e: Exception) {
            if (config.debugEnabled) {
                Log.e("AnalyticsSDK", "Failed to clear offline events", e)
            }
        }
    }
    
    // MARK: - Session Data
    
    fun saveSessionData(session: SessionData) {
        try {
            val json = gson.toJson(session)
            sharedPrefs.edit().putString(sessionDataKey, json).apply()
        } catch (e: Exception) {
            if (config.debugEnabled) {
                Log.e("AnalyticsSDK", "Failed to save session data", e)
            }
        }
    }
    
    fun loadSessionData(): SessionData? {
        return try {
            val json = sharedPrefs.getString(sessionDataKey, null) ?: return null
            gson.fromJson(json, SessionData::class.java)
        } catch (e: Exception) {
            if (config.debugEnabled) {
                Log.e("AnalyticsSDK", "Failed to load session data", e)
            }
            null
        }
    }
    
    // MARK: - User Data
    
    fun saveUserData(user: UserData) {
        try {
            val json = gson.toJson(user)
            sharedPrefs.edit().putString(userDataKey, json).apply()
        } catch (e: Exception) {
            if (config.debugEnabled) {
                Log.e("AnalyticsSDK", "Failed to save user data", e)
            }
        }
    }
    
    fun loadUserData(): UserData? {
        return try {
            val json = sharedPrefs.getString(userDataKey, null) ?: return null
            gson.fromJson(json, UserData::class.java)
        } catch (e: Exception) {
            if (config.debugEnabled) {
                Log.e("AnalyticsSDK", "Failed to load user data", e)
            }
            null
        }
    }
    
    // MARK: - Encryption
    
    private fun encrypt(data: ByteArray): ByteArray {
        // Simplified encryption - in production use proper AES encryption
        return data
    }
    
    private fun decrypt(data: ByteArray): ByteArray {
        // Simplified decryption - in production use proper AES decryption
        return data
    }
}

// MARK: - Session Manager

internal class SessionManager(
    private val config: AnalyticsConfig,
    private val storage: StorageManager
) : DefaultLifecycleObserver {
    
    @Volatile
    var currentSession: SessionData
        private set
    
    private val handler = Handler(Looper.getMainLooper())
    private var sessionUpdateRunnable: Runnable? = null
    private var backgroundTime: Long = 0
    
    init {
        // Load existing session or create new one
        val existingSession = storage.loadSessionData()
        currentSession = if (existingSession != null && 
                             System.currentTimeMillis() - existingSession.lastActivity < config.sessionTimeout) {
            existingSession
        } else {
            SessionData()
        }
        
        ProcessLifecycleOwner.get().lifecycle.addObserver(this)
        startSessionTimer()
    }
    
    override fun onStart(owner: LifecycleOwner) {
        if (backgroundTime > 0) {
            val backgroundDuration = System.currentTimeMillis() - backgroundTime
            
            if (backgroundDuration > config.sessionTimeout) {
                // Start new session
                currentSession = SessionData()
            }
            backgroundTime = 0
        }
        
        startSessionTimer()
    }
    
    override fun onStop(owner: LifecycleOwner) {
        backgroundTime = System.currentTimeMillis()
        stopSessionTimer()
        updateSession()
        storage.saveSessionData(currentSession)
    }
    
    private fun startSessionTimer() {
        stopSessionTimer()
        sessionUpdateRunnable = object : Runnable {
            override fun run() {
                updateSession()
                handler.postDelayed(this, 30000) // Update every 30 seconds
            }
        }
        handler.post(sessionUpdateRunnable!!)
    }
    
    private fun stopSessionTimer() {
        sessionUpdateRunnable?.let { handler.removeCallbacks(it) }
        sessionUpdateRunnable = null
    }
    
    fun trackEvent() {
        synchronized(this) {
            currentSession = currentSession.copy(
                eventCount = currentSession.eventCount + 1,
                lastActivity = System.currentTimeMillis()
            )
        }
        updateSession()
    }
    
    private fun updateSession() {
        synchronized(this) {
            currentSession = currentSession.copy(
                duration = System.currentTimeMillis() - currentSession.startTime
            )
        }
        storage.saveSessionData(currentSession)
    }
    
    fun endSession() {
        stopSessionTimer()
        updateSession()
    }
}

// MARK: - Main Analytics SDK

class AnalyticsSDK private constructor() {
    companion object {
        @JvmStatic
        val instance: AnalyticsSDK by lazy { AnalyticsSDK() }
    }
    
    private var config: AnalyticsConfig? = null
    private var networkManager: NetworkManager? = null
    private var storageManager: StorageManager? = null
    private var sessionManager: SessionManager? = null
    private var edgeProcessor: EdgeProcessor? = null
    
    private val eventQueue = ConcurrentLinkedQueue<AnalyticsEvent>()
    private val handler = Handler(Looper.getMainLooper())
    private var flushRunnable: Runnable? = null
    
    private lateinit var deviceContext: EventContext.DeviceContext
    private lateinit var appContext: EventContext.AppContext
    private lateinit var application: Application
    
    // MARK: - Initialization
    
    fun initialize(application: Application, config: AnalyticsConfig) {
        this.application = application
        this.config = config
        
        setupContext(application)
        setupManagers(application)
        startFlushTimer()
        
        if (config.features.contains(AnalyticsConfig.Feature.SCREEN_TRACKING)) {
            setupAutomaticScreenTracking(application)
        }
        
        if (config.features.contains(AnalyticsConfig.Feature.CRASH_TRACKING)) {
            setupCrashTracking()
        }
        
        track("sdk_initialized", mapOf(
            "version" to "1.0.0",
            "features" to config.features.map { it.value }
        ))
    }
    
    private fun setupContext(context: Context) {
        val config = this.config ?: return
        
        // Device context
        val displayMetrics = context.resources.displayMetrics
        val deviceId = getDeviceId(context)
        
        deviceContext = EventContext.DeviceContext(
            id = deviceId,
            manufacturer = Build.MANUFACTURER,
            model = Build.MODEL,
            type = if (context.resources.configuration.smallestScreenWidthDp >= 600) "tablet" else "phone",
            os = "Android",
            osVersion = Build.VERSION.RELEASE,
            screenWidth = displayMetrics.widthPixels,
            screenHeight = displayMetrics.heightPixels,
            locale = Locale.getDefault().toString(),
            timezone = TimeZone.getDefault().id
        )
        
        // App context
        val packageManager = context.packageManager
        val packageInfo = packageManager.getPackageInfo(context.packageName, 0)
        
        appContext = EventContext.AppContext(
            name = packageManager.getApplicationLabel(packageInfo.applicationInfo).toString(),
            version = packageInfo.versionName ?: "Unknown",
            build = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                packageInfo.longVersionCode.toString()
            } else {
                @Suppress("DEPRECATION")
                packageInfo.versionCode.toString()
            },
            namespace = context.packageName
        )
    }
    
    private fun setupManagers(context: Context) {
        val config = this.config ?: return
        
        storageManager = StorageManager(context, config)
        networkManager = NetworkManager(context, config)
        sessionManager = SessionManager(config, storageManager!!)
        edgeProcessor = EdgeProcessor(config)
    }
    
    private fun startFlushTimer() {
        val config = this.config ?: return
        
        stopFlushTimer()
        flushRunnable = object : Runnable {
            override fun run() {
                flush()
                handler.postDelayed(this, config.flushInterval)
            }
        }
        handler.postDelayed(flushRunnable!!, config.flushInterval)
    }
    
    private fun stopFlushTimer() {
        flushRunnable?.let { handler.removeCallbacks(it) }
        flushRunnable = null
    }
    
    // MARK: - Event Tracking
    
    fun track(eventType: String, properties: Map<String, Any> = emptyMap()) {
        val config = this.config ?: return
        val sessionManager = this.sessionManager ?: return
        
        sessionManager.trackEvent()
        
        val context = EventContext(
            app = appContext,
            device = deviceContext,
            network = getNetworkContext(),
            location = getLocationContext(),
            session = EventContext.SessionContext(
                id = sessionManager.currentSession.id,
                startTime = sessionManager.currentSession.startTime,
                duration = sessionManager.currentSession.duration,
                eventCount = sessionManager.currentSession.eventCount,
                isFirstSession = sessionManager.currentSession.isFirstSession
            )
        )
        
        val event = AnalyticsEvent(
            type = eventType,
            userId = config.userId,
            sessionId = sessionManager.currentSession.id,
            properties = properties,
            context = context
        )
        
        // Edge processing
        CoroutineScope(Dispatchers.Main).launch {
            val processedEvent = edgeProcessor?.processEvent(event) ?: event
            addToQueue(processedEvent)
            
            // Auto-flush for critical events
            if (isCriticalEvent(eventType)) {
                flush()
            }
        }
    }
    
    fun identify(userId: String, traits: Map<String, Any> = emptyMap()) {
        config = config?.copy(userId = userId)
        
        track("identify", mapOf(
            "user_id" to userId,
            "traits" to traits
        ))
    }
    
    fun screen(name: String, properties: Map<String, Any> = emptyMap()) {
        val screenProperties = properties.toMutableMap()
        screenProperties["screen_name"] = name
        
        track("screen_view", screenProperties)
    }
    
    // MARK: - Queue Management
    
    private fun addToQueue(event: AnalyticsEvent) {
        eventQueue.offer(event)
        
        // Auto-flush if queue is full
        if (eventQueue.size >= (config?.batchSize ?: 20)) {
            flush()
        }
    }
    
    fun flush() {
        val eventsToSend = mutableListOf<AnalyticsEvent>()
        
        // Drain the queue
        while (eventQueue.isNotEmpty()) {
            eventQueue.poll()?.let { eventsToSend.add(it) }
        }
        
        if (eventsToSend.isEmpty()) return
        
        CoroutineScope(Dispatchers.IO).launch {
            try {
                if (networkManager?.isConnected == true) {
                    networkManager?.sendEvents(eventsToSend)
                } else {
                    // Store offline
                    storageManager?.saveOfflineEvents(eventsToSend)
                }
            } catch (e: Exception) {
                // Save to offline storage on failure
                storageManager?.saveOfflineEvents(eventsToSend)
                
                if (config?.debugEnabled == true) {
                    Log.e("AnalyticsSDK", "Failed to send events, saved offline", e)
                }
            }
        }
        
        // Process offline events when back online
        if (networkManager?.isConnected == true) {
            processOfflineEvents()
        }
    }
    
    private fun processOfflineEvents() {
        val storageManager = this.storageManager ?: return
        
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val offlineEvents = storageManager.loadOfflineEvents()
                if (offlineEvents.isNotEmpty()) {
                    networkManager?.sendEvents(offlineEvents)
                    storageManager.clearOfflineEvents()
                    
                    if (config?.debugEnabled == true) {
                        Log.d("AnalyticsSDK", "Processed ${offlineEvents.size} offline events")
                    }
                }
            } catch (e: Exception) {
                if (config?.debugEnabled == true) {
                    Log.e("AnalyticsSDK", "Failed to process offline events", e)
                }
            }
        }
    }
    
    // MARK: - Helper Methods
    
    private fun getDeviceId(context: Context): String {
        val key = "analytics.device.id"
        val sharedPrefs = context.getSharedPreferences("analytics_device", Context.MODE_PRIVATE)
        
        val existingId = sharedPrefs.getString(key, null)
        if (existingId != null) {
            return existingId
        }
        
        val newId = UUID.randomUUID().toString()
        sharedPrefs.edit().putString(key, newId).apply()
        return newId
    }
    
    private fun getNetworkContext(): EventContext.NetworkContext {
        val connectivityManager = application.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val activeNetwork = connectivityManager.activeNetwork
        val networkCapabilities = connectivityManager.getNetworkCapabilities(activeNetwork)
        
        val hasWifi = networkCapabilities?.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) ?: false
        val hasCellular = networkCapabilities?.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) ?: false
        
        val telephonyManager = application.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager
        val carrier = telephonyManager.networkOperatorName
        
        val networkType = when {
            hasWifi -> "wifi"
            hasCellular -> "cellular"
            else -> "unknown"
        }
        
        return EventContext.NetworkContext(
            type = networkType,
            carrier = if (carrier.isNotBlank()) carrier else null,
            wifi = hasWifi,
            cellular = hasCellular
        )
    }
    
    private fun getLocationContext(): EventContext.LocationContext? {
        // Location tracking would require proper permissions
        return null
    }
    
    private fun isCriticalEvent(eventType: String): Boolean {
        val criticalEvents = listOf("error", "crash", "purchase", "signup", "login")
        return criticalEvents.contains(eventType)
    }
    
    private fun setupAutomaticScreenTracking(application: Application) {
        // Implement automatic screen tracking using activity lifecycle callbacks
        application.registerActivityLifecycleCallbacks(object : Application.ActivityLifecycleCallbacks {
            override fun onActivityCreated(activity: android.app.Activity, savedInstanceState: android.os.Bundle?) {}
            override fun onActivityStarted(activity: android.app.Activity) {}
            override fun onActivityResumed(activity: android.app.Activity) {
                screen(activity::class.java.simpleName)
            }
            override fun onActivityPaused(activity: android.app.Activity) {}
            override fun onActivityStopped(activity: android.app.Activity) {}
            override fun onActivitySaveInstanceState(activity: android.app.Activity, outState: android.os.Bundle) {}
            override fun onActivityDestroyed(activity: android.app.Activity) {}
        })
    }
    
    private fun setupCrashTracking() {
        // Implement crash tracking
        val defaultHandler = Thread.getDefaultUncaughtExceptionHandler()
        
        Thread.setDefaultUncaughtExceptionHandler { thread, exception ->
            track("crash", mapOf(
                "exception_name" to exception::class.java.simpleName,
                "exception_message" to (exception.message ?: "Unknown"),
                "stack_trace" to exception.stackTrace.map { it.toString() },
                "thread_name" to thread.name
            ))
            flush()
            
            // Call original handler
            defaultHandler?.uncaughtException(thread, exception)
        }
    }
    
    // MARK: - Cleanup
    
    fun cleanup() {
        stopFlushTimer()
        flush()
        sessionManager?.endSession()
        edgeProcessor?.cleanup()
        networkManager?.cleanup()
    }
}

// MARK: - Supporting Models

internal data class EventBatch(
    @SerializedName("api_key") val apiKey: String,
    val events: List<AnalyticsEvent>,
    @SerializedName("batch_id") val batchId: String,
    val platform: String,
    @SerializedName("sdk_version") val sdkVersion: String
)

internal data class SessionData(
    val id: String = UUID.randomUUID().toString(),
    val startTime: Long = System.currentTimeMillis(),
    val duration: Long = 0,
    val eventCount: Int = 0,
    val lastActivity: Long = System.currentTimeMillis(),
    val isFirstSession: Boolean = !hasExistingSession()
) {
    companion object {
        private fun hasExistingSession(): Boolean {
            // This would check SharedPreferences for existing session history
            return false
        }
    }
}

internal data class UserData(
    val id: String,
    val traits: Map<String, Any>,
    val firstSeen: Long,
    val lastSeen: Long
)

sealed class AnalyticsError : Exception() {
    object NotInitialized : AnalyticsError()
    object InvalidEndpoint : AnalyticsError()
    object InvalidResponse : AnalyticsError()
    data class ServerError(val code: Int) : AnalyticsError()
    data class NetworkError(val cause: Throwable) : AnalyticsError()
}