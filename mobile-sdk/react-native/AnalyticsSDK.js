/**
 * Analytics SDK for React Native with Edge Processing
 * Cross-platform mobile analytics with local processing, intelligent caching, and offline capabilities
 */

import { NativeModules, NativeEventEmitter, Platform, AppState, NetInfo } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import DeviceInfo from 'react-native-device-info';
import Geolocation from '@react-native-community/geolocation';

// MARK: - Configuration

export class AnalyticsConfig {
  constructor({
    apiKey,
    userId = null,
    environment = 'production',
    edgeEnabled = true,
    offlineEnabled = true,
    encryptionEnabled = true,
    batchSize = 20,
    flushInterval = 30000, // 30 seconds
    sessionTimeout = 1800000, // 30 minutes
    debugEnabled = false,
    endpoints = {},
    features = ['user_tracking', 'screen_tracking', 'performance_tracking']
  } = {}) {
    this.apiKey = apiKey;
    this.userId = userId;
    this.environment = environment;
    this.edgeEnabled = edgeEnabled;
    this.offlineEnabled = offlineEnabled;
    this.encryptionEnabled = encryptionEnabled;
    this.batchSize = batchSize;
    this.flushInterval = flushInterval;
    this.sessionTimeout = sessionTimeout;
    this.debugEnabled = debugEnabled;
    this.features = new Set(features);
    
    // Default endpoints
    this.endpoints = Object.keys(endpoints).length > 0 ? endpoints : {
      'us-east-1': 'https://us-east-1.edge.analytics-platform.com',
      'us-west-2': 'https://us-west-2.edge.analytics-platform.com',
      'eu-west-1': 'https://eu-west-1.edge.analytics-platform.com',
      'ap-southeast-1': 'https://ap-southeast-1.edge.analytics-platform.com',
      'auto': 'https://api.analytics-platform.com'
    };
  }
  
  static get Environment() {
    return {
      DEVELOPMENT: 'development',
      STAGING: 'staging',
      PRODUCTION: 'production'
    };
  }
  
  static get Region() {
    return {
      US_EAST_1: 'us-east-1',
      US_WEST_2: 'us-west-2',
      EU_WEST_1: 'eu-west-1',
      AP_SOUTHEAST_1: 'ap-southeast-1',
      AUTO: 'auto'
    };
  }
  
  static get Feature() {
    return {
      USER_TRACKING: 'user_tracking',
      SCREEN_TRACKING: 'screen_tracking',
      PERFORMANCE_TRACKING: 'performance_tracking',
      CRASH_TRACKING: 'crash_tracking',
      NETWORK_TRACKING: 'network_tracking',
      LOCATION_TRACKING: 'location_tracking',
      PUSH_NOTIFICATIONS: 'push_notifications',
      HEATMAPS: 'heatmaps',
      SESSION_REPLAY: 'session_replay',
      AB_TESTING: 'ab_testing'
    };
  }
}

// MARK: - Event Models

export class AnalyticsEvent {
  constructor({
    type,
    userId = null,
    sessionId,
    properties = {},
    context,
    edgeProcessed = false
  }) {
    this.id = this._generateUUID();
    this.type = type;
    this.userId = userId;
    this.sessionId = sessionId;
    this.timestamp = Date.now();
    this.properties = properties;
    this.context = context;
    this.edgeProcessed = edgeProcessed;
  }
  
  _generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }
}

export class EventContext {
  constructor({ app, device, network, location = null, session }) {
    this.app = app;
    this.device = device;
    this.network = network;
    this.location = location;
    this.session = session;
  }
}

// MARK: - Edge Processor

class EdgeProcessor {
  constructor(config) {
    this.config = config;
    this.mlModels = new Map();
    this.setupMLModels();
  }
  
  async setupMLModels() {
    // Initialize lightweight ML models for edge processing
    try {
      await this.initializeUserSegmentationModel();
      await this.initializeAnomalyDetectionModel();
      await this.initializeSessionClassificationModel();
    } catch (error) {
      if (this.config.debugEnabled) {
        console.warn('Analytics: Failed to initialize ML models:', error);
      }
    }
  }
  
  async initializeUserSegmentationModel() {
    // Simplified user segmentation model
    // In production, this would load a TensorFlow.js model
    this.mlModels.set('user_segmentation', {
      segments: ['new_user', 'active_user', 'power_user', 'churning_user'],
      initialized: true
    });
  }
  
  async initializeAnomalyDetectionModel() {
    // Anomaly detection for unusual user behavior
    this.mlModels.set('anomaly_detection', {
      thresholds: { unusual_time: 0.8, rapid_events: 0.9 },
      initialized: true
    });
  }
  
  async initializeSessionClassificationModel() {
    // Session quality and engagement classification
    this.mlModels.set('session_classification', {
      engagement_levels: ['low_engagement', 'medium_engagement', 'high_engagement'],
      initialized: true
    });
  }
  
  async processEvent(event) {
    if (!this.config.edgeEnabled) return event;
    
    try {
      const enhancedProperties = { ...event.properties };
      
      // User segmentation
      const userSegment = this.classifyUser(event);
      if (userSegment) {
        enhancedProperties.user_segment = userSegment;
      }
      
      // Anomaly detection
      const anomalyScore = this.detectAnomalies(event);
      enhancedProperties.anomaly_score = anomalyScore;
      
      // Session classification
      const sessionQuality = this.classifySession(event);
      if (sessionQuality) {
        enhancedProperties.session_quality = sessionQuality;
      }
      
      // Performance insights
      if (event.type === 'screen_view') {
        enhancedProperties.predicted_engagement = this.predictEngagement(event);
      }
      
      return new AnalyticsEvent({
        ...event,
        properties: enhancedProperties,
        edgeProcessed: true
      });
    } catch (error) {
      if (this.config.debugEnabled) {
        console.warn('Analytics: Edge processing failed:', error);
      }
      return event;
    }
  }
  
  classifyUser(event) {
    if (!event.userId) return null;
    
    const model = this.mlModels.get('user_segmentation');
    if (!model?.initialized) return null;
    
    // Simplified user classification based on behavior patterns
    return model.segments[Math.floor(Math.random() * model.segments.length)];
  }
  
  detectAnomalies(event) {
    // Simplified anomaly detection
    // Returns score between 0.0 (normal) and 1.0 (highly anomalous)
    
    const hour = new Date(event.timestamp).getHours();
    const isUnusualTime = hour < 6 || hour > 23;
    
    // Check for rapid event frequency
    // This would be more sophisticated in production
    
    return isUnusualTime ? 0.8 : 0.1;
  }
  
  classifySession(event) {
    const duration = event.context.session.duration;
    const eventCount = event.context.session.eventCount;
    
    if (duration > 300000 && eventCount > 10) {
      return 'high_engagement';
    } else if (duration > 60000 && eventCount > 3) {
      return 'medium_engagement';
    } else {
      return 'low_engagement';
    }
  }
  
  predictEngagement(event) {
    // Predict user engagement score for current session
    const sessionDuration = event.context.session.duration;
    const eventCount = event.context.session.eventCount;
    
    // Simplified engagement prediction
    const engagementScore = Math.min(1.0, (sessionDuration / 300000) * 0.6 + (eventCount / 20) * 0.4);
    return engagementScore;
  }
}

// MARK: - Network Manager

class NetworkManager {
  constructor(config) {
    this.config = config;
    this.isConnected = true;
    this.currentRegion = AnalyticsConfig.Region.AUTO;
    this.optimalEndpoint = config.endpoints[AnalyticsConfig.Region.AUTO];
    
    this.setupNetworkMonitoring();
    this.detectOptimalEndpoint();
  }
  
  setupNetworkMonitoring() {
    // Monitor network connectivity
    const unsubscribe = NetInfo.addEventListener(state => {
      this.isConnected = state.isConnected;
      if (state.isConnected) {
        this.detectOptimalEndpoint();
      }
    });
    
    this.unsubscribeNetInfo = unsubscribe;
  }
  
  async detectOptimalEndpoint() {
    try {
      const detectedRegion = await this.detectRegion();
      const endpoint = this.config.endpoints[detectedRegion] || this.config.endpoints[AnalyticsConfig.Region.AUTO];
      
      // Test endpoint latency
      const latency = await this.testEndpointLatency(endpoint);
      if (latency < 2000) { // 2 seconds
        this.currentRegion = detectedRegion;
        this.optimalEndpoint = endpoint;
      } else {
        this.currentRegion = AnalyticsConfig.Region.AUTO;
        this.optimalEndpoint = this.config.endpoints[AnalyticsConfig.Region.AUTO];
      }
      
      if (this.config.debugEnabled) {
        console.log(`Analytics: Optimal endpoint: ${this.optimalEndpoint} (region: ${this.currentRegion})`);
      }
    } catch (error) {
      if (this.config.debugEnabled) {
        console.warn('Analytics: Failed to detect optimal endpoint:', error);
      }
    }
  }
  
  async detectRegion() {
    try {
      // Use timezone and locale to detect region
      const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      const locale = await DeviceInfo.getLocales();
      const countryCode = locale[0]?.countryCode;
      
      if (timeZone.startsWith('America/')) {
        return countryCode === 'US' ? AnalyticsConfig.Region.US_EAST_1 : AnalyticsConfig.Region.US_WEST_2;
      } else if (timeZone.startsWith('Europe/')) {
        return AnalyticsConfig.Region.EU_WEST_1;
      } else if (timeZone.startsWith('Asia/') || timeZone.startsWith('Australia/')) {
        return AnalyticsConfig.Region.AP_SOUTHEAST_1;
      } else {
        return AnalyticsConfig.Region.AUTO;
      }
    } catch (error) {
      return AnalyticsConfig.Region.AUTO;
    }
  }
  
  async testEndpointLatency(endpoint) {
    try {
      const startTime = Date.now();
      const response = await fetch(`${endpoint}/health`, {
        method: 'GET',
        timeout: 5000
      });
      
      if (response.ok) {
        return Date.now() - startTime;
      } else {
        return 999000;
      }
    } catch (error) {
      return 999000;
    }
  }
  
  async sendEvents(events) {
    if (events.length === 0) return;
    
    try {
      const endpoint = `${this.optimalEndpoint}/analytics/events`;
      const payload = {
        api_key: this.config.apiKey,
        events: events,
        batch_id: this._generateUUID(),
        platform: Platform.OS,
        sdk_version: '1.0.0'
      };
      
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': this.config.apiKey,
          'X-Client-Platform': `ReactNative-${Platform.OS}`,
          'X-Edge-Region': this.currentRegion
        },
        body: JSON.stringify(payload)
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      if (this.config.debugEnabled) {
        console.log(`Analytics: Successfully sent ${events.length} events`);
      }
    } catch (error) {
      if (this.config.debugEnabled) {
        console.warn('Analytics: Failed to send events:', error);
      }
      throw error;
    }
  }
  
  _generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }
  
  cleanup() {
    if (this.unsubscribeNetInfo) {
      this.unsubscribeNetInfo();
    }
  }
}

// MARK: - Storage Manager

class StorageManager {
  constructor(config) {
    this.config = config;
    this.offlineEventsKey = 'analytics.offline.events';
    this.sessionDataKey = 'analytics.session.data';
    this.userDataKey = 'analytics.user.data';
  }
  
  // MARK: - Offline Events
  
  async saveOfflineEvents(events) {
    if (!this.config.offlineEnabled) return;
    
    try {
      const existingEvents = await this.loadOfflineEvents();
      const allEvents = [...existingEvents, ...events];
      
      // Limit offline storage to prevent excessive disk usage
      const limitedEvents = allEvents.slice(-1000);
      
      const data = JSON.stringify(limitedEvents);
      const finalData = this.config.encryptionEnabled ? this.encrypt(data) : data;
      
      await AsyncStorage.setItem(this.offlineEventsKey, finalData);
      
      if (this.config.debugEnabled) {
        console.log(`Analytics: Saved ${events.length} events offline`);
      }
    } catch (error) {
      if (this.config.debugEnabled) {
        console.warn('Analytics: Failed to save offline events:', error);
      }
    }
  }
  
  async loadOfflineEvents() {
    try {
      const data = await AsyncStorage.getItem(this.offlineEventsKey);
      if (!data) return [];
      
      const decryptedData = this.config.encryptionEnabled ? this.decrypt(data) : data;
      return JSON.parse(decryptedData) || [];
    } catch (error) {
      if (this.config.debugEnabled) {
        console.warn('Analytics: Failed to load offline events:', error);
      }
      return [];
    }
  }
  
  async clearOfflineEvents() {
    try {
      await AsyncStorage.removeItem(this.offlineEventsKey);
    } catch (error) {
      if (this.config.debugEnabled) {
        console.warn('Analytics: Failed to clear offline events:', error);
      }
    }
  }
  
  // MARK: - Session Data
  
  async saveSessionData(session) {
    try {
      const data = JSON.stringify(session);
      await AsyncStorage.setItem(this.sessionDataKey, data);
    } catch (error) {
      if (this.config.debugEnabled) {
        console.warn('Analytics: Failed to save session data:', error);
      }
    }
  }
  
  async loadSessionData() {
    try {
      const data = await AsyncStorage.getItem(this.sessionDataKey);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      if (this.config.debugEnabled) {
        console.warn('Analytics: Failed to load session data:', error);
      }
      return null;
    }
  }
  
  // MARK: - User Data
  
  async saveUserData(user) {
    try {
      const data = JSON.stringify(user);
      await AsyncStorage.setItem(this.userDataKey, data);
    } catch (error) {
      if (this.config.debugEnabled) {
        console.warn('Analytics: Failed to save user data:', error);
      }
    }
  }
  
  async loadUserData() {
    try {
      const data = await AsyncStorage.getItem(this.userDataKey);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      if (this.config.debugEnabled) {
        console.warn('Analytics: Failed to load user data:', error);
      }
      return null;
    }
  }
  
  // MARK: - Encryption
  
  encrypt(data) {
    // Simplified encryption - in production use proper encryption
    return Buffer.from(data).toString('base64');
  }
  
  decrypt(data) {
    // Simplified decryption - in production use proper decryption
    return Buffer.from(data, 'base64').toString('utf-8');
  }
}

// MARK: - Session Manager

class SessionManager {
  constructor(config, storage) {
    this.config = config;
    this.storage = storage;
    this.currentSession = null;
    this.sessionTimer = null;
    this.backgroundTime = null;
    
    this.initializeSession();
    this.setupAppStateTracking();
  }
  
  async initializeSession() {
    // Load existing session or create new one
    const existingSession = await this.storage.loadSessionData();
    
    if (existingSession && Date.now() - existingSession.lastActivity < this.config.sessionTimeout) {
      this.currentSession = existingSession;
    } else {
      this.currentSession = await this.createNewSession();
    }
    
    this.startSessionTimer();
  }
  
  async createNewSession() {
    const hasExistingSession = await AsyncStorage.getItem('analytics.has.session');
    const session = {
      id: this._generateUUID(),
      startTime: Date.now(),
      duration: 0,
      eventCount: 0,
      lastActivity: Date.now(),
      isFirstSession: !hasExistingSession
    };
    
    await AsyncStorage.setItem('analytics.has.session', 'true');
    return session;
  }
  
  setupAppStateTracking() {
    AppState.addEventListener('change', this.handleAppStateChange.bind(this));
  }
  
  handleAppStateChange(nextAppState) {
    if (nextAppState === 'background') {
      this.backgroundTime = Date.now();
      this.stopSessionTimer();
      this.updateSession();
      this.storage.saveSessionData(this.currentSession);
    } else if (nextAppState === 'active') {
      if (this.backgroundTime) {
        const backgroundDuration = Date.now() - this.backgroundTime;
        
        if (backgroundDuration > this.config.sessionTimeout) {
          // Start new session
          this.createNewSession().then(session => {
            this.currentSession = session;
          });
        }
        this.backgroundTime = null;
      }
      
      this.startSessionTimer();
    }
  }
  
  startSessionTimer() {
    this.stopSessionTimer();
    this.sessionTimer = setInterval(() => {
      this.updateSession();
    }, 30000); // Update every 30 seconds
  }
  
  stopSessionTimer() {
    if (this.sessionTimer) {
      clearInterval(this.sessionTimer);
      this.sessionTimer = null;
    }
  }
  
  trackEvent() {
    if (this.currentSession) {
      this.currentSession.eventCount += 1;
      this.currentSession.lastActivity = Date.now();
      this.updateSession();
    }
  }
  
  updateSession() {
    if (this.currentSession) {
      this.currentSession.duration = Date.now() - this.currentSession.startTime;
      this.storage.saveSessionData(this.currentSession);
    }
  }
  
  endSession() {
    this.stopSessionTimer();
    this.updateSession();
  }
  
  _generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }
  
  cleanup() {
    this.stopSessionTimer();
    AppState.removeEventListener('change', this.handleAppStateChange);
  }
}

// MARK: - Main Analytics SDK

export class AnalyticsSDK {
  static _instance = null;
  
  static getInstance() {
    if (!AnalyticsSDK._instance) {
      AnalyticsSDK._instance = new AnalyticsSDK();
    }
    return AnalyticsSDK._instance;
  }
  
  constructor() {
    this.config = null;
    this.networkManager = null;
    this.storageManager = null;
    this.sessionManager = null;
    this.edgeProcessor = null;
    
    this.eventQueue = [];
    this.flushTimer = null;
    
    this.deviceContext = null;
    this.appContext = null;
    this.isInitialized = false;
  }
  
  // MARK: - Initialization
  
  async initialize(config) {
    if (this.isInitialized) {
      console.warn('Analytics: SDK already initialized');
      return;
    }
    
    try {
      this.config = config;
      
      await this.setupContext();
      await this.setupManagers();
      this.startFlushTimer();
      
      if (config.features.has(AnalyticsConfig.Feature.SCREEN_TRACKING)) {
        this.setupAutomaticScreenTracking();
      }
      
      if (config.features.has(AnalyticsConfig.Feature.CRASH_TRACKING)) {
        this.setupCrashTracking();
      }
      
      this.isInitialized = true;
      
      this.track('sdk_initialized', {
        version: '1.0.0',
        features: Array.from(config.features)
      });
      
      if (config.debugEnabled) {
        console.log('Analytics: SDK initialized successfully');
      }
    } catch (error) {
      console.error('Analytics: Failed to initialize SDK:', error);
      throw error;
    }
  }
  
  async setupContext() {
    try {
      // Device context
      const [
        deviceId,
        systemName,
        systemVersion,
        model,
        manufacturer,
        isTablet,
        screenData,
        locales
      ] = await Promise.all([
        this.getDeviceId(),
        DeviceInfo.getSystemName(),
        DeviceInfo.getSystemVersion(),
        DeviceInfo.getModel(),
        DeviceInfo.getManufacturer(),
        DeviceInfo.isTablet(),
        DeviceInfo.getScreenData?.() || { width: 0, height: 0 },
        DeviceInfo.getLocales()
      ]);
      
      this.deviceContext = {
        id: deviceId,
        manufacturer,
        model,
        type: isTablet ? 'tablet' : 'phone',
        os: systemName,
        osVersion: systemVersion,
        screenWidth: screenData.width,
        screenHeight: screenData.height,
        locale: locales[0]?.languageTag || 'en-US',
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
      };
      
      // App context
      const [appName, version, buildNumber, bundleId] = await Promise.all([
        DeviceInfo.getApplicationName(),
        DeviceInfo.getVersion(),
        DeviceInfo.getBuildNumber(),
        DeviceInfo.getBundleId()
      ]);
      
      this.appContext = {
        name: appName,
        version,
        build: buildNumber,
        namespace: bundleId
      };
    } catch (error) {
      console.warn('Analytics: Failed to setup context:', error);
      // Fallback context
      this.deviceContext = {
        id: await this.getDeviceId(),
        manufacturer: 'Unknown',
        model: 'Unknown',
        type: 'phone',
        os: Platform.OS,
        osVersion: Platform.Version,
        screenWidth: 0,
        screenHeight: 0,
        locale: 'en-US',
        timezone: 'UTC'
      };
      
      this.appContext = {
        name: 'Unknown',
        version: '1.0.0',
        build: '1',
        namespace: 'com.unknown.app'
      };
    }
  }
  
  async setupManagers() {
    this.storageManager = new StorageManager(this.config);
    this.networkManager = new NetworkManager(this.config);
    this.sessionManager = new SessionManager(this.config, this.storageManager);
    this.edgeProcessor = new EdgeProcessor(this.config);
    
    // Wait for session to be initialized
    await this.sessionManager.initializeSession();
  }
  
  startFlushTimer() {
    this.stopFlushTimer();
    this.flushTimer = setInterval(() => {
      this.flush();
    }, this.config.flushInterval);
  }
  
  stopFlushTimer() {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
      this.flushTimer = null;
    }
  }
  
  // MARK: - Event Tracking
  
  async track(eventType, properties = {}) {
    if (!this.isInitialized) {
      console.warn('Analytics: SDK not initialized');
      return;
    }
    
    try {
      this.sessionManager.trackEvent();
      
      const context = new EventContext({
        app: this.appContext,
        device: this.deviceContext,
        network: await this.getNetworkContext(),
        location: await this.getLocationContext(),
        session: {
          id: this.sessionManager.currentSession.id,
          startTime: this.sessionManager.currentSession.startTime,
          duration: this.sessionManager.currentSession.duration,
          eventCount: this.sessionManager.currentSession.eventCount,
          isFirstSession: this.sessionManager.currentSession.isFirstSession
        }
      });
      
      let event = new AnalyticsEvent({
        type: eventType,
        userId: this.config.userId,
        sessionId: this.sessionManager.currentSession.id,
        properties,
        context
      });
      
      // Edge processing
      event = await this.edgeProcessor.processEvent(event);
      
      this.addToQueue(event);
      
      // Auto-flush for critical events
      if (this.isCriticalEvent(eventType)) {
        this.flush();
      }
    } catch (error) {
      if (this.config.debugEnabled) {
        console.warn('Analytics: Failed to track event:', error);
      }
    }
  }
  
  identify(userId, traits = {}) {
    this.config.userId = userId;
    
    this.track('identify', {
      user_id: userId,
      traits
    });
  }
  
  screen(name, properties = {}) {
    const screenProperties = { ...properties };
    screenProperties.screen_name = name;
    
    this.track('screen_view', screenProperties);
  }
  
  // MARK: - Queue Management
  
  addToQueue(event) {
    this.eventQueue.push(event);
    
    // Auto-flush if queue is full
    if (this.eventQueue.length >= this.config.batchSize) {
      this.flush();
    }
  }
  
  async flush() {
    const eventsToSend = [...this.eventQueue];
    this.eventQueue = [];
    
    if (eventsToSend.length === 0) return;
    
    try {
      if (this.networkManager.isConnected) {
        await this.networkManager.sendEvents(eventsToSend);
      } else {
        // Store offline
        await this.storageManager.saveOfflineEvents(eventsToSend);
      }
    } catch (error) {
      // Save to offline storage on failure
      await this.storageManager.saveOfflineEvents(eventsToSend);
      
      if (this.config.debugEnabled) {
        console.warn('Analytics: Failed to send events, saved offline:', error);
      }
    }
    
    // Process offline events when back online
    if (this.networkManager.isConnected) {
      this.processOfflineEvents();
    }
  }
  
  async processOfflineEvents() {
    try {
      const offlineEvents = await this.storageManager.loadOfflineEvents();
      if (offlineEvents.length > 0) {
        await this.networkManager.sendEvents(offlineEvents);
        await this.storageManager.clearOfflineEvents();
        
        if (this.config.debugEnabled) {
          console.log(`Analytics: Processed ${offlineEvents.length} offline events`);
        }
      }
    } catch (error) {
      if (this.config.debugEnabled) {
        console.warn('Analytics: Failed to process offline events:', error);
      }
    }
  }
  
  // MARK: - Helper Methods
  
  async getDeviceId() {
    const key = 'analytics.device.id';
    
    try {
      let deviceId = await AsyncStorage.getItem(key);
      if (!deviceId) {
        deviceId = await DeviceInfo.getUniqueId();
        await AsyncStorage.setItem(key, deviceId);
      }
      return deviceId;
    } catch (error) {
      // Fallback to random UUID
      const fallbackId = this._generateUUID();
      try {
        await AsyncStorage.setItem(key, fallbackId);
      } catch (e) {
        // Ignore storage error
      }
      return fallbackId;
    }
  }
  
  async getNetworkContext() {
    try {
      const netInfo = await NetInfo.fetch();
      
      return {
        type: netInfo.type,
        carrier: netInfo.details?.carrier || null,
        wifi: netInfo.type === 'wifi',
        cellular: netInfo.type === 'cellular'
      };
    } catch (error) {
      return {
        type: 'unknown',
        carrier: null,
        wifi: false,
        cellular: false
      };
    }
  }
  
  async getLocationContext() {
    // Location tracking would require proper permissions
    if (!this.config.features.has(AnalyticsConfig.Feature.LOCATION_TRACKING)) {
      return null;
    }
    
    return new Promise((resolve) => {
      Geolocation.getCurrentPosition(
        (position) => {
          resolve({
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            accuracy: position.coords.accuracy,
            city: null,
            region: null,
            country: null
          });
        },
        (error) => {
          if (this.config.debugEnabled) {
            console.warn('Analytics: Failed to get location:', error);
          }
          resolve(null);
        },
        { timeout: 5000, maximumAge: 300000 }
      );
    });
  }
  
  isCriticalEvent(eventType) {
    const criticalEvents = ['error', 'crash', 'purchase', 'signup', 'login'];
    return criticalEvents.includes(eventType);
  }
  
  setupAutomaticScreenTracking() {
    // Implement automatic screen tracking using navigation listeners
    // This would depend on the navigation library being used (React Navigation, etc.)
    if (this.config.debugEnabled) {
      console.log('Analytics: Automatic screen tracking enabled');
    }
  }
  
  setupCrashTracking() {
    // Implement crash tracking
    const originalHandler = ErrorUtils.getGlobalHandler();
    
    ErrorUtils.setGlobalHandler((error, isFatal) => {
      this.track('crash', {
        error_name: error.name,
        error_message: error.message,
        stack_trace: error.stack,
        is_fatal: isFatal
      });
      this.flush();
      
      // Call original handler
      if (originalHandler) {
        originalHandler(error, isFatal);
      }
    });
  }
  
  _generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }
  
  // MARK: - Cleanup
  
  cleanup() {
    this.stopFlushTimer();
    this.flush();
    
    if (this.sessionManager) {
      this.sessionManager.cleanup();
    }
    
    if (this.networkManager) {
      this.networkManager.cleanup();
    }
    
    this.isInitialized = false;
  }
}

// MARK: - Convenience Export

export default AnalyticsSDK.getInstance();