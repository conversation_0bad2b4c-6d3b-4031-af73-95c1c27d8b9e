# Docker Compose configuration for e-commerce analytics platform
# Compatible with both <PERSON><PERSON> and <PERSON><PERSON>
version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: ecommerce-postgres
    restart: unless-stopped
    command: ["postgres", "-c", "shared_preload_libraries=pg_stat_statements", "-c", "pg_stat_statements.max=10000", "-c", "pg_stat_statements.track=all", "-c", "log_min_duration_statement=100", "-c", "track_io_timing=on"]
    environment:
      POSTGRES_DB: ecommerce_analytics
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${DB_PASSWORD:-password}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/01-init.sql:ro
      - ./database/enable-pg-stat-statements.sql:/docker-entrypoint-initdb.d/02-enable-pg-stat-statements.sql:ro
    ports:
      - "${DB_PORT:-5432}:5432"
    networks:
      - ecommerce-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d ecommerce_analytics"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: ecommerce-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-}
    volumes:
      - redis_data:/data
    ports:
      - "${REDIS_PORT:-6379}:6379"
    networks:
      - ecommerce-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Analytics Service
  analytics-service:
    build:
      context: ./services/analytics
      dockerfile: Dockerfile
      target: ${BUILD_TARGET:-production}
    container_name: ecommerce-analytics
    restart: unless-stopped
    environment:
      NODE_ENV: ${NODE_ENV:-production}
      PORT: 3002
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: ecommerce_analytics
      DB_USER: postgres
      DB_PASSWORD: ${DB_PASSWORD:-password}
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD:-}
      LOG_LEVEL: ${LOG_LEVEL:-info}
      CORS_ORIGIN: ${CORS_ORIGIN:-http://localhost:3000,http://localhost:3001}
    volumes:
      - analytics_logs:/app/logs
      - analytics_uploads:/app/uploads
      - analytics_exports:/app/exports
    ports:
      - "${ANALYTICS_PORT:-3002}:3002"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - ecommerce-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3002/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Dashboard Service
  dashboard-service:
    build:
      context: ./services/dashboard
      dockerfile: Dockerfile
      target: ${BUILD_TARGET:-production}
    container_name: ecommerce-dashboard
    restart: unless-stopped
    environment:
      NODE_ENV: ${NODE_ENV:-production}
      PORT: 3001
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: ecommerce_analytics
      DB_USER: postgres
      DB_PASSWORD: ${DB_PASSWORD:-password}
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD:-}
      ANALYTICS_SERVICE_URL: http://analytics-service:3002
      JWT_SECRET: ${JWT_SECRET:-change-in-production}
    ports:
      - "${DASHBOARD_PORT:-3001}:3001"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      analytics-service:
        condition: service_healthy
    networks:
      - ecommerce-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Integration Service
  integration-service:
    build:
      context: ./services/integration
      dockerfile: Dockerfile
      target: ${BUILD_TARGET:-production}
    container_name: ecommerce-integration
    restart: unless-stopped
    environment:
      NODE_ENV: ${NODE_ENV:-production}
      PORT: 3001
      POSTGRES_HOST: postgres
      POSTGRES_PORT: 5432
      POSTGRES_DB: ecommerce_analytics
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${DB_PASSWORD:-password}
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD:-}
      ANALYTICS_SERVICE_URL: http://analytics-service:3002
      SHOPIFY_API_KEY: ${SHOPIFY_API_KEY:-}
      SHOPIFY_SECRET: ${SHOPIFY_SECRET:-}
      WOOCOMMERCE_KEY: ${WOOCOMMERCE_KEY:-}
      WOOCOMMERCE_SECRET: ${WOOCOMMERCE_SECRET:-}
    ports:
      - "${INTEGRATION_PORT:-3001}:3001"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - ecommerce-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3003/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend Application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: ${BUILD_TARGET:-production}
    container_name: ecommerce-frontend
    restart: unless-stopped
    environment:
      NODE_ENV: ${NODE_ENV:-production}
      VITE_API_URL: ${VITE_API_URL:-http://localhost:3001/api}
      VITE_ANALYTICS_URL: ${VITE_ANALYTICS_URL:-http://localhost:3002/api}
      VITE_INTEGRATION_URL: ${VITE_INTEGRATION_URL:-http://localhost:3003/api}
    ports:
      - "${FRONTEND_PORT:-3000}:3000"
    depends_on:
      dashboard-service:
        condition: service_healthy
      analytics-service:
        condition: service_healthy
    networks:
      - ecommerce-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  analytics_logs:
    driver: local
  analytics_uploads:
    driver: local
  analytics_exports:
    driver: local

networks:
  ecommerce-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16