#!/usr/bin/env python3
"""
Command Line Interface for the Agent Orchestrator
Provides commands to manage and monitor the orchestrator system
"""

import click
import json
import sys
from pathlib import Path
from datetime import datetime
from typing import Optional

# Add the project root to the path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from orchestrator.core.agent_spawner import AgentSpawner
from orchestrator.core.task_parser import TaskParser, TaskStatus
from orchestrator.utils.status_monitor import StatusMonitor
from orchestrator.utils.cleanup_manager import CleanupManager
from orchestrator.config.settings import PROJECT_ROOT, MAX_CONCURRENT_AGENTS


@click.group()
@click.option('--project-root', type=click.Path(exists=True), help='Project root directory')
@click.option('--verbose', '-v', is_flag=True, help='Enable verbose logging')
@click.pass_context
def cli(ctx, project_root, verbose):
    """Agent Orchestrator CLI - Manage AI coding agents for parallel task execution"""
    ctx.ensure_object(dict)
    
    # Set up project root
    if project_root:
        ctx.obj['project_root'] = Path(project_root)
    else:
        ctx.obj['project_root'] = PROJECT_ROOT
    
    # Set up logging level
    import logging
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(level=level, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    # Initialize core components
    ctx.obj['agent_spawner'] = AgentSpawner(ctx.obj['project_root'])
    ctx.obj['task_parser'] = TaskParser()
    ctx.obj['status_monitor'] = StatusMonitor(ctx.obj['agent_spawner'])
    ctx.obj['cleanup_manager'] = CleanupManager(ctx.obj['project_root'])


@cli.command()
@click.option('--max-agents', '-m', default=MAX_CONCURRENT_AGENTS, help='Maximum number of concurrent agents')
@click.option('--dry-run', is_flag=True, help='Show what would be done without executing')
@click.pass_context
def spawn(ctx, max_agents, dry_run):
    """Analyze tasks and spawn agents for ready tasks"""
    agent_spawner = ctx.obj['agent_spawner']
    
    if dry_run:
        click.echo("🔍 Dry run mode - analyzing tasks without spawning agents")
        
        tasks = agent_spawner.task_parser.parse_tasks()
        ready_tasks = agent_spawner.task_parser.find_ready_tasks()
        
        click.echo(f"📋 Total tasks: {len(tasks)}")
        click.echo(f"✅ Ready tasks: {len(ready_tasks)}")
        click.echo(f"🤖 Would spawn up to {min(len(ready_tasks), max_agents)} agents")
        
        if ready_tasks:
            click.echo("\nTasks that would be assigned:")
            for task in ready_tasks[:max_agents]:
                click.echo(f"  • {task.id}: {task.title}")
        
        return
    
    click.echo(f"🚀 Spawning agents (max: {max_agents})")
    
    try:
        spawned_agents = agent_spawner.analyze_and_spawn_agents(max_agents)
        
        if spawned_agents:
            click.echo(f"✅ Successfully spawned {len(spawned_agents)} agents:")
            for agent in spawned_agents:
                click.echo(f"  • {agent.id} → {agent.task.title}")
        else:
            click.echo("ℹ️  No agents were spawned (no ready tasks or resources unavailable)")
            
    except Exception as e:
        click.echo(f"❌ Error spawning agents: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.option('--format', 'output_format', type=click.Choice(['table', 'json', 'summary']), default='table', help='Output format')
@click.option('--filter-status', type=click.Choice(['all', 'active', 'completed', 'failed']), default='all', help='Filter by task status')
@click.pass_context
def status(ctx, output_format, filter_status):
    """Show orchestrator and agent status"""
    agent_spawner = ctx.obj['agent_spawner']
    status_monitor = ctx.obj['status_monitor']
    
    try:
        orchestrator_status = agent_spawner.get_orchestrator_status()
        status_summary = status_monitor.get_status_summary()
        
        if output_format == 'json':
            combined_status = {
                'orchestrator': orchestrator_status,
                'monitoring': status_summary['monitoring'],
                'metrics': status_summary['metrics']
            }
            click.echo(json.dumps(combined_status, indent=2, default=str))
            return
        
        if output_format == 'summary':
            click.echo("📊 ORCHESTRATOR SUMMARY")
            click.echo(f"Active Agents: {orchestrator_status['active_agents']}")
            click.echo(f"Completed Agents: {orchestrator_status['completed_agents']}")
            click.echo(f"Task Summary: {orchestrator_status['task_summary']}")
            click.echo(f"Uptime: {status_summary['monitoring']['uptime_hours']:.1f} hours")
            return
        
        # Table format (default)
        click.echo("🤖 AGENT ORCHESTRATOR STATUS")
        click.echo("=" * 50)
        
        click.echo(f"Active Agents: {orchestrator_status['active_agents']}")
        click.echo(f"Completed Agents: {orchestrator_status['completed_agents']}")
        click.echo(f"Git Worktrees: {orchestrator_status['git_worktrees']}")
        click.echo(f"Tmux Sessions: {orchestrator_status['tmux_sessions']}")
        
        # Task breakdown
        task_summary = orchestrator_status['task_summary']
        click.echo(f"\n📋 TASK BREAKDOWN:")
        for status_type, count in task_summary.items():
            if filter_status == 'all' or filter_status == status_type:
                icon = {'completed': '✅', 'failed': '❌', 'in_progress': '🔄', 'claimed': '📝'}.get(status_type, '📋')
                click.echo(f"  {icon} {status_type.title()}: {count}")
        
        # Agent health
        if orchestrator_status['agent_health']:
            click.echo(f"\n🏥 AGENT HEALTH:")
            for agent_id, health in orchestrator_status['agent_health'].items():
                health_icon = {'healthy': '💚', 'idle': '💛', 'stuck': '🟠', 'critical': '🔴'}.get(health, '❓')
                click.echo(f"  {health_icon} {agent_id}: {health}")
        
        # Recent alerts
        recent_alerts = status_summary['monitoring']['recent_alerts']
        total_recent = sum(recent_alerts.values())
        if total_recent > 0:
            click.echo(f"\n⚠️  RECENT ALERTS:")
            for level, count in recent_alerts.items():
                if count > 0:
                    level_icon = {'critical': '🔴', 'error': '🟠', 'warning': '🟡', 'info': '🔵'}.get(level, '❓')
                    click.echo(f"  {level_icon} {level.title()}: {count}")
        
    except Exception as e:
        click.echo(f"❌ Error getting status: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.argument('agent_id', required=False)
@click.option('--all', 'show_all', is_flag=True, help='Show details for all agents')
@click.pass_context
def agent(ctx, agent_id, show_all):
    """Show detailed information about specific agent(s)"""
    agent_spawner = ctx.obj['agent_spawner']
    status_monitor = ctx.obj['status_monitor']
    
    if show_all:
        agents = list(agent_spawner.active_agents.keys())
        if not agents:
            click.echo("ℹ️  No active agents found")
            return
    elif agent_id:
        agents = [agent_id]
    else:
        # Show list of available agents
        active_agents = list(agent_spawner.active_agents.keys())
        if not active_agents:
            click.echo("ℹ️  No active agents found")
            return
        
        click.echo("🤖 ACTIVE AGENTS:")
        for aid in active_agents:
            agent_obj = agent_spawner.active_agents[aid]
            click.echo(f"  • {aid} → {agent_obj.task.title}")
        
        click.echo(f"\nUse 'orchestrator agent <agent_id>' for details")
        return
    
    for aid in agents:
        try:
            agent_status = status_monitor.get_agent_detailed_status(aid)
            
            if agent_status.get('status') == 'not_found':
                click.echo(f"❌ Agent {aid} not found")
                continue
            
            click.echo(f"\n🤖 AGENT: {aid}")
            click.echo("=" * 50)
            click.echo(f"Task: {agent_status['task_title']}")
            click.echo(f"Status: {agent_status['status']}")
            click.echo(f"Health: {agent_status['health']}")
            click.echo(f"Created: {agent_status['created_at']}")
            click.echo(f"Last Activity: {agent_status['last_activity']}")
            click.echo(f"Error Count: {agent_status['error_count']}")
            
            # Tmux info
            tmux_info = agent_status.get('tmux', {})
            if tmux_info:
                click.echo(f"\n💻 TMUX SESSION:")
                click.echo(f"  Status: {tmux_info.get('status', 'unknown')}")
                click.echo(f"  Active: {tmux_info.get('is_active', False)}")
                
                if tmux_info.get('recent_output'):
                    click.echo(f"\n📄 RECENT OUTPUT:")
                    output_lines = tmux_info['recent_output'].split('\n')[-5:]
                    for line in output_lines:
                        if line.strip():
                            click.echo(f"    {line}")
            
            # Git info
            git_info = agent_status.get('git', {})
            if git_info:
                click.echo(f"\n📂 GIT STATUS:")
                click.echo(f"  Branch: {git_info.get('branch', 'unknown')}")
                click.echo(f"  Has Changes: {git_info.get('has_changes', False)}")
                
                if git_info.get('modified_files'):
                    click.echo(f"  Modified Files: {len(git_info['modified_files'])}")
                    for file_path in git_info['modified_files'][:3]:
                        click.echo(f"    • {file_path}")
            
            # Monitoring info
            monitoring_info = agent_status.get('monitoring', {})
            if monitoring_info and monitoring_info.get('recent_alerts'):
                click.echo(f"\n⚠️  RECENT ALERTS:")
                for alert in monitoring_info['recent_alerts'][-3:]:
                    click.echo(f"  • [{alert['level'].upper()}] {alert['message']}")
        
        except Exception as e:
            click.echo(f"❌ Error getting agent status for {aid}: {e}", err=True)


@cli.command()
@click.option('--start', is_flag=True, help='Start monitoring')
@click.option('--stop', is_flag=True, help='Stop monitoring')
@click.option('--dashboard', is_flag=True, help='Create tmux monitoring dashboard')
@click.option('--health-report', is_flag=True, help='Generate health report')
@click.option('--export', type=click.Path(), help='Export metrics to file')
@click.pass_context
def monitor(ctx, start, stop, dashboard, health_report, export):
    """Control monitoring system"""
    status_monitor = ctx.obj['status_monitor']
    agent_spawner = ctx.obj['agent_spawner']
    
    if start:
        click.echo("🔍 Starting monitoring system...")
        status_monitor.start_monitoring()
        click.echo("✅ Monitoring started")
        
    elif stop:
        click.echo("⏹️  Stopping monitoring system...")
        status_monitor.stop_monitoring()
        click.echo("✅ Monitoring stopped")
        
    elif dashboard:
        click.echo("📊 Creating monitoring dashboard...")
        dashboard_session = agent_spawner.create_monitoring_dashboard()
        if dashboard_session:
            click.echo(f"✅ Monitoring dashboard created: {dashboard_session}")
            click.echo("Use 'tmux attach -t {dashboard_session}' to view")
        else:
            click.echo("❌ Failed to create monitoring dashboard")
            
    elif health_report:
        click.echo("🏥 Generating health report...")
        report = status_monitor.generate_health_report()
        click.echo(report)
        
    elif export:
        click.echo(f"📤 Exporting metrics to {export}...")
        status_monitor.export_metrics(Path(export))
        click.echo("✅ Metrics exported")
        
    else:
        # Show monitoring status
        status_summary = status_monitor.get_status_summary()
        monitoring_info = status_summary['monitoring']
        
        click.echo("🔍 MONITORING STATUS")
        click.echo("=" * 30)
        click.echo(f"Active: {monitoring_info['active']}")
        click.echo(f"Uptime: {monitoring_info['uptime_hours']:.1f} hours")
        click.echo(f"Total Alerts: {monitoring_info['total_alerts']}")
        
        recent_alerts = monitoring_info['recent_alerts']
        if sum(recent_alerts.values()) > 0:
            click.echo("\nRecent Alerts:")
            for level, count in recent_alerts.items():
                if count > 0:
                    click.echo(f"  {level}: {count}")


@cli.command()
@click.option('--completed', is_flag=True, help='Clean up completed tasks')
@click.option('--failed', is_flag=True, help='Clean up failed tasks')
@click.option('--orphaned', is_flag=True, help='Clean up orphaned resources')
@click.option('--logs', is_flag=True, help='Clean up old logs')
@click.option('--force', is_flag=True, help='Force cleanup of all non-active resources')
@click.option('--auto', is_flag=True, help='Run automatic scheduled cleanup')
@click.option('--disk-space', type=float, help='Free specified GB of disk space')
@click.option('--dry-run', is_flag=True, help='Show what would be cleaned without doing it')
@click.pass_context
def cleanup(ctx, completed, failed, orphaned, logs, force, auto, disk_space, dry_run):
    """Clean up orchestrator resources"""
    cleanup_manager = ctx.obj['cleanup_manager']
    
    if dry_run:
        click.echo("🔍 Dry run mode - showing cleanup recommendations")
        recommendations = cleanup_manager.get_cleanup_recommendations()
        
        if recommendations:
            for rec in recommendations:
                severity_icon = "⚠️" if rec['severity'] == 'warning' else "ℹ️"
                click.echo(f"{severity_icon} {rec['message']}")
                click.echo(f"   Action: {rec['action']}")
        else:
            click.echo("✅ No cleanup needed at this time")
        return
    
    try:
        if force:
            click.echo("⚠️  FORCE cleanup - removing all non-active resources")
            click.confirm('This will remove all non-active worktrees, sessions, and logs. Continue?', abort=True)
            stats = cleanup_manager.force_cleanup_all()
            
        elif auto:
            click.echo("🔄 Running automatic scheduled cleanup")
            cleanup_record = cleanup_manager.schedule_automatic_cleanup()
            stats = cleanup_record['stats']
            
        elif disk_space:
            click.echo(f"💾 Freeing {disk_space}GB of disk space")
            stats = cleanup_manager.cleanup_disk_space(disk_space)
            
        else:
            # Individual cleanup operations
            total_stats = cleanup_manager.CleanupStats()
            
            if completed:
                click.echo("🧹 Cleaning up completed tasks...")
                completed_stats = cleanup_manager.cleanup_completed_tasks()
                total_stats.worktrees_removed += completed_stats.worktrees_removed
                total_stats.tasks_archived += completed_stats.tasks_archived
                total_stats.disk_space_freed_mb += completed_stats.disk_space_freed_mb
                
            if failed:
                click.echo("🧹 Cleaning up failed tasks...")
                failed_stats = cleanup_manager.cleanup_failed_tasks()
                total_stats.worktrees_removed += failed_stats.worktrees_removed
                total_stats.tasks_archived += failed_stats.tasks_archived
                total_stats.disk_space_freed_mb += failed_stats.disk_space_freed_mb
                
            if orphaned:
                click.echo("🧹 Cleaning up orphaned resources...")
                orphaned_stats = cleanup_manager.cleanup_orphaned_resources()
                total_stats.worktrees_removed += orphaned_stats.worktrees_removed
                total_stats.tmux_sessions_killed += orphaned_stats.tmux_sessions_killed
                total_stats.disk_space_freed_mb += orphaned_stats.disk_space_freed_mb
                
            if logs:
                click.echo("🧹 Cleaning up old logs...")
                log_stats = cleanup_manager.cleanup_logs()
                total_stats.files_cleaned += log_stats.files_cleaned
                total_stats.disk_space_freed_mb += log_stats.disk_space_freed_mb
            
            if not any([completed, failed, orphaned, logs]):
                click.echo("ℹ️  No cleanup options specified. Use --help for options")
                return
                
            stats = total_stats.to_dict()
        
        # Display results
        click.echo("\n✅ CLEANUP COMPLETED")
        click.echo("=" * 30)
        click.echo(f"Worktrees Removed: {stats['worktrees_removed']}")
        click.echo(f"Tmux Sessions Killed: {stats['tmux_sessions_killed']}")
        click.echo(f"Files Cleaned: {stats['files_cleaned']}")
        click.echo(f"Tasks Archived: {stats['tasks_archived']}")
        click.echo(f"Disk Space Freed: {stats['disk_space_freed_mb']:.1f}MB")
        
    except Exception as e:
        click.echo(f"❌ Error during cleanup: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.option('--task-id', help='Complete specific task')
@click.option('--agent-id', help='Complete specific agent')
@click.option('--merge/--no-merge', default=True, help='Merge changes to main branch')
@click.pass_context
def complete(ctx, task_id, agent_id, merge):
    """Mark tasks or agents as completed"""
    agent_spawner = ctx.obj['agent_spawner']
    
    if agent_id:
        click.echo(f"🏁 Completing agent {agent_id}...")
        
        if agent_spawner.complete_agent_task(agent_id, merge_changes=merge):
            click.echo(f"✅ Agent {agent_id} completed successfully")
        else:
            click.echo(f"❌ Failed to complete agent {agent_id}")
            sys.exit(1)
            
    elif task_id:
        # Find agent by task ID
        target_agent = None
        for aid, agent in agent_spawner.active_agents.items():
            if agent.task_id == task_id:
                target_agent = aid
                break
        
        if target_agent:
            click.echo(f"🏁 Completing task {task_id} (agent {target_agent})...")
            if agent_spawner.complete_agent_task(target_agent, merge_changes=merge):
                click.echo(f"✅ Task {task_id} completed successfully")
            else:
                click.echo(f"❌ Failed to complete task {task_id}")
                sys.exit(1)
        else:
            click.echo(f"❌ No active agent found for task {task_id}")
            sys.exit(1)
    else:
        click.echo("❌ Must specify either --task-id or --agent-id")
        sys.exit(1)


@cli.command()
@click.option('--task-id', help='Terminate task')
@click.option('--agent-id', help='Terminate agent')
@click.option('--reason', default='Manual termination', help='Reason for termination')
@click.option('--all', 'terminate_all', is_flag=True, help='Terminate all active agents')
@click.pass_context
def terminate(ctx, task_id, agent_id, reason, terminate_all):
    """Terminate running tasks or agents"""
    agent_spawner = ctx.obj['agent_spawner']
    
    if terminate_all:
        click.echo("⚠️  Terminating ALL active agents")
        click.confirm('This will terminate all running agents. Continue?', abort=True)
        
        active_agents = list(agent_spawner.active_agents.keys())
        terminated_count = 0
        
        for aid in active_agents:
            if agent_spawner.terminate_agent(aid, reason):
                terminated_count += 1
                click.echo(f"🛑 Terminated agent {aid}")
            else:
                click.echo(f"❌ Failed to terminate agent {aid}")
        
        click.echo(f"✅ Terminated {terminated_count}/{len(active_agents)} agents")
        
    elif agent_id:
        click.echo(f"🛑 Terminating agent {agent_id}...")
        
        if agent_spawner.terminate_agent(agent_id, reason):
            click.echo(f"✅ Agent {agent_id} terminated")
        else:
            click.echo(f"❌ Failed to terminate agent {agent_id}")
            sys.exit(1)
            
    elif task_id:
        # Find agent by task ID
        target_agent = None
        for aid, agent in agent_spawner.active_agents.items():
            if agent.task_id == task_id:
                target_agent = aid
                break
        
        if target_agent:
            click.echo(f"🛑 Terminating task {task_id} (agent {target_agent})...")
            if agent_spawner.terminate_agent(target_agent, reason):
                click.echo(f"✅ Task {task_id} terminated")
            else:
                click.echo(f"❌ Failed to terminate task {task_id}")
                sys.exit(1)
        else:
            click.echo(f"❌ No active agent found for task {task_id}")
            sys.exit(1)
    else:
        click.echo("❌ Must specify --task-id, --agent-id, or --all")
        sys.exit(1)


@cli.command()
@click.pass_context
def tasks(ctx):
    """List and manage tasks"""
    task_parser = ctx.obj['task_parser']
    
    try:
        tasks = task_parser.parse_tasks()
        
        if not tasks:
            click.echo("ℹ️  No tasks found")
            return
        
        # Group tasks by status
        by_status = {}
        for task in tasks:
            status = task.status
            if status not in by_status:
                by_status[status] = []
            by_status[status].append(task)
        
        click.echo("📋 TASKS OVERVIEW")
        click.echo("=" * 50)
        
        for status, task_list in by_status.items():
            status_icon = {
                TaskStatus.UNCLAIMED: '📝',
                TaskStatus.CLAIMED: '📋',
                TaskStatus.IN_PROGRESS: '🔄',
                TaskStatus.COMPLETED: '✅',
                TaskStatus.FAILED: '❌',
                TaskStatus.INTERVENTION_REQUIRED: '⚠️'
            }.get(status, '❓')
            
            click.echo(f"\n{status_icon} {status.upper()} ({len(task_list)})")
            
            for task in task_list[:10]:  # Show up to 10 tasks per status
                priority_icon = {'high': '🔴', 'medium': '🟡', 'low': '🟢'}.get(task.priority, '⚪')
                click.echo(f"  {priority_icon} {task.id}: {task.title}")
                
                if task.dependencies and task.dependencies != ['none']:
                    click.echo(f"      Dependencies: {', '.join(task.dependencies)}")
            
            if len(task_list) > 10:
                click.echo(f"      ... and {len(task_list) - 10} more")
        
    except Exception as e:
        click.echo(f"❌ Error reading tasks: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.pass_context
def report(ctx):
    """Generate comprehensive orchestrator report"""
    status_monitor = ctx.obj['status_monitor']
    cleanup_manager = ctx.obj['cleanup_manager']
    
    try:
        # Generate health report
        health_report = status_monitor.generate_health_report()
        click.echo(health_report)
        
        click.echo("\n")
        
        # Generate cleanup report
        cleanup_report = cleanup_manager.generate_cleanup_report()
        click.echo(cleanup_report)
        
    except Exception as e:
        click.echo(f"❌ Error generating report: {e}", err=True)
        sys.exit(1)


if __name__ == '__main__':
    cli()