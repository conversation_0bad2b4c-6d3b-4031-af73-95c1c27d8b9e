"""
Task parser and analyzer for the Agent Orchestrator
Handles parsing tasks.md and analyzing task dependencies
"""

import re
import logging
from typing import List, Dict, Set, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime
from pathlib import Path

from ..config.settings import TaskStatus, TaskPriority, TASKS_FILE

logger = logging.getLogger(__name__)

@dataclass
class Task:
    """Represents a single task with all its metadata"""
    id: str
    title: str
    description: str = ""
    branch: str = ""
    status: str = TaskStatus.UNCLAIMED
    session: str = ""
    priority: str = TaskPriority.MEDIUM
    dependencies: List[str] = field(default_factory=list)
    estimated_time: str = ""
    assigned_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    agent_id: str = ""
    file_path: str = ""
    line_number: int = 0
    
    def __post_init__(self):
        """Validate and normalize task data"""
        if not self.branch and self.title:
            # Generate branch name from title
            self.branch = f"feature/{self._slugify(self.title)}"
        
        if not self.id:
            self.id = self._slugify(self.title)
    
    def _slugify(self, text: str) -> str:
        """Convert text to URL-friendly slug"""
        return re.sub(r'[^\w\-_]', '-', text.lower()).strip('-')
    
    def is_ready(self, completed_tasks: Set[str]) -> bool:
        """Check if task is ready to be assigned (all dependencies completed)"""
        if self.status != TaskStatus.UNCLAIMED:
            return False
        
        for dep in self.dependencies:
            if dep not in completed_tasks:
                return False
        
        return True
    
    def can_run_parallel(self, other: 'Task') -> bool:
        """Check if this task can run in parallel with another task"""
        # Tasks with same branch cannot run in parallel
        if self.branch == other.branch:
            return False
        
        # Tasks with dependencies cannot run in parallel
        if self.id in other.dependencies or other.id in self.dependencies:
            return False
        
        # Tasks working on same files cannot run in parallel
        if self.file_path and other.file_path:
            if Path(self.file_path).parts[:-1] == Path(other.file_path).parts[:-1]:
                return False
        
        return True


class TaskParser:
    """Parses and analyzes tasks from tasks.md file"""
    
    def __init__(self, tasks_file: Path = TASKS_FILE):
        self.tasks_file = tasks_file
        self.tasks: List[Task] = []
        self._task_pattern = re.compile(r'^## Task \d+: (.+)$', re.MULTILINE)
        self._field_pattern = re.compile(r'^- \*\*([^*]+)\*\*:\s*(.+)$', re.MULTILINE)
    
    def parse_tasks(self) -> List[Task]:
        """Parse all tasks from the tasks file"""
        if not self.tasks_file.exists():
            logger.warning(f"Tasks file not found: {self.tasks_file}")
            return []
        
        content = self.tasks_file.read_text()
        self.tasks = []
        
        # Find all task sections
        task_matches = list(self._task_pattern.finditer(content))
        
        for i, match in enumerate(task_matches):
            task_start = match.start()
            task_end = task_matches[i + 1].start() if i + 1 < len(task_matches) else len(content)
            task_content = content[task_start:task_end]
            
            task = self._parse_single_task(task_content, match.group(1), task_start)
            if task:
                self.tasks.append(task)
        
        logger.info(f"Parsed {len(self.tasks)} tasks from {self.tasks_file}")
        return self.tasks
    
    def _parse_single_task(self, content: str, title: str, start_pos: int) -> Optional[Task]:
        """Parse a single task from its content block"""
        try:
            task = Task(
                id="",
                title=title.strip(),
                line_number=content[:start_pos].count('\n') + 1
            )
            
            # Extract task fields
            fields = dict(self._field_pattern.findall(content))
            
            # Map fields to task attributes
            field_mapping = {
                'Branch': 'branch',
                'Status': 'status', 
                'Session': 'session',
                'Priority': 'priority',
                'Dependencies': 'dependencies',
                'Estimated Time': 'estimated_time',
                'Agent ID': 'agent_id',
                'File Path': 'file_path'
            }
            
            for field_name, attr_name in field_mapping.items():
                if field_name in fields:
                    value = fields[field_name].strip()
                    
                    if attr_name == 'dependencies':
                        # Parse comma-separated dependencies
                        if value and value.lower() != 'none':
                            task.dependencies = [dep.strip() for dep in value.split(',')]
                    elif attr_name == 'status':
                        # Validate status
                        if hasattr(TaskStatus, value.upper().replace('-', '_')):
                            task.status = value
                    elif attr_name == 'priority':
                        # Validate priority
                        if hasattr(TaskPriority, value.upper()):
                            task.priority = value
                    else:
                        setattr(task, attr_name, value)
            
            # Extract description (content after the title line but before fields)
            lines = content.split('\n')
            description_lines = []
            in_description = False
            
            for line in lines[1:]:  # Skip title line
                if line.startswith('- **'):
                    break
                if line.strip():
                    description_lines.append(line.strip())
                elif description_lines:
                    in_description = True
                    break
            
            task.description = ' '.join(description_lines)
            
            return task
            
        except Exception as e:
            logger.error(f"Error parsing task '{title}': {e}")
            return None
    
    def analyze_dependencies(self) -> Dict[str, List[str]]:
        """Analyze task dependencies and return dependency graph"""
        dependency_graph = {}
        
        for task in self.tasks:
            dependency_graph[task.id] = task.dependencies.copy()
        
        return dependency_graph
    
    def find_ready_tasks(self) -> List[Task]:
        """Find tasks that are ready to be assigned"""
        completed_tasks = {
            task.id for task in self.tasks 
            if task.status == TaskStatus.COMPLETED
        }
        
        ready_tasks = [
            task for task in self.tasks 
            if task.is_ready(completed_tasks)
        ]
        
        # Sort by priority
        priority_order = {
            TaskPriority.CRITICAL: 0,
            TaskPriority.HIGH: 1,
            TaskPriority.MEDIUM: 2,
            TaskPriority.LOW: 3
        }
        
        ready_tasks.sort(key=lambda t: priority_order.get(t.priority, 99))
        
        return ready_tasks
    
    def find_parallel_groups(self, ready_tasks: List[Task]) -> List[List[Task]]:
        """Group tasks that can be executed in parallel"""
        groups = []
        used_tasks = set()
        
        for task in ready_tasks:
            if task.id in used_tasks:
                continue
            
            # Start a new group with this task
            group = [task]
            used_tasks.add(task.id)
            
            # Find other tasks that can run in parallel
            for other_task in ready_tasks:
                if other_task.id in used_tasks:
                    continue
                
                # Check if this task can run with all tasks in the group
                can_parallel = all(task.can_run_parallel(other_task) for task in group)
                
                if can_parallel:
                    group.append(other_task)
                    used_tasks.add(other_task.id)
            
            groups.append(group)
        
        return groups
    
    def update_task_status(self, task_id: str, status: str, session: str = "", agent_id: str = ""):
        """Update task status in the tasks file"""
        if not self.tasks_file.exists():
            return False
        
        content = self.tasks_file.read_text()
        
        # Find the task section
        task_pattern = re.compile(rf'^## Task \d+: [^#]*?{re.escape(task_id)}[^#]*?(?=^## Task|\Z)', 
                                 re.MULTILINE | re.DOTALL)
        
        def update_field(match):
            task_content = match.group(0)
            
            # Update status
            status_pattern = re.compile(r'^- \*\*Status\*\*:\s*(.+)$', re.MULTILINE)
            if status_pattern.search(task_content):
                task_content = status_pattern.sub(f'- **Status**: {status}', task_content)
            else:
                # Add status field
                task_content += f'\n- **Status**: {status}'
            
            # Update session if provided
            if session:
                session_pattern = re.compile(r'^- \*\*Session\*\*:\s*(.*)$', re.MULTILINE)
                if session_pattern.search(task_content):
                    task_content = session_pattern.sub(f'- **Session**: {session}', task_content)
                else:
                    task_content += f'\n- **Session**: {session}'
            
            # Update agent ID if provided
            if agent_id:
                agent_pattern = re.compile(r'^- \*\*Agent ID\*\*:\s*(.*)$', re.MULTILINE)
                if agent_pattern.search(task_content):
                    task_content = agent_pattern.sub(f'- **Agent ID**: {agent_id}', task_content)
                else:
                    task_content += f'\n- **Agent ID**: {agent_id}'
            
            return task_content
        
        updated_content = task_pattern.sub(update_field, content)
        
        if updated_content != content:
            self.tasks_file.write_text(updated_content)
            logger.info(f"Updated task {task_id} status to {status}")
            return True
        
        return False
    
    def get_task_by_id(self, task_id: str) -> Optional[Task]:
        """Get a task by its ID"""
        for task in self.tasks:
            if task.id == task_id:
                return task
        return None


def create_sample_tasks_file(file_path: Path):
    """Create a sample tasks.md file for testing"""
    sample_content = """# Tasks

## Task 1: Implement Light Theme
- **Branch**: feature/light-theme
- **Status**: unclaimed
- **Session**: 
- **Priority**: high
- **Dependencies**: none
- **Estimated Time**: 2 hours
- **File Path**: frontend/src/styles/themes.css

Create a light theme for the application with proper color schemes and accessibility considerations.

## Task 2: Add Data Export Feature
- **Branch**: feature/data-export
- **Status**: unclaimed
- **Session**:
- **Priority**: medium
- **Dependencies**: none
- **Estimated Time**: 3 hours
- **File Path**: frontend/src/components/export/

Implement CSV and JSON export functionality for analytics data.

## Task 3: Enhance Filter Options
- **Branch**: feature/enhanced-filters
- **Status**: unclaimed
- **Session**:
- **Priority**: medium
- **Dependencies**: light-theme
- **Estimated Time**: 1.5 hours
- **File Path**: frontend/src/components/filters/

Add advanced filtering options that work well with the new light theme.

## Task 4: Mobile Responsive Design
- **Branch**: feature/mobile-responsive
- **Status**: unclaimed
- **Session**:
- **Priority**: low
- **Dependencies**: light-theme, enhanced-filters
- **Estimated Time**: 4 hours
- **File Path**: frontend/src/styles/responsive.css

Make the application fully responsive for mobile devices.
"""
    
    file_path.write_text(sample_content)
    logger.info(f"Created sample tasks file at {file_path}")