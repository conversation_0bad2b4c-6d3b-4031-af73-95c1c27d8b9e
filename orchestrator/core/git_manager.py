"""
Git worktree manager for the Agent Orchestrator
Handles git worktree creation, management, and cleanup
"""

import subprocess
import logging
from typing import List, Dict, Optional, Tuple
from pathlib import Path
from dataclasses import dataclass
from datetime import datetime

from ..config.settings import (
    PROJECT_ROOT, WORKTREES_DIR, DEFAULT_BRANCH, WORKTREE_PREFIX
)

logger = logging.getLogger(__name__)

@dataclass
class Worktree:
    """Represents a git worktree"""
    path: Path
    branch: str
    commit: str
    bare: bool = False
    created_at: Optional[datetime] = None
    
    @property
    def name(self) -> str:
        return self.path.name


class GitManager:
    """Manages git worktrees for parallel agent development"""
    
    def __init__(self, project_root: Path = PROJECT_ROOT, worktrees_dir: Path = WORKTREES_DIR):
        self.project_root = project_root
        self.worktrees_dir = worktrees_dir
        self.worktrees_dir.mkdir(exist_ok=True)
    
    def get_current_branch(self) -> str:
        """Get the current branch name"""
        try:
            result = subprocess.run(
                ['git', 'branch', '--show-current'],
                cwd=self.project_root,
                capture_output=True,
                text=True,
                check=True
            )
            return result.stdout.strip()
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to get current branch: {e}")
            return DEFAULT_BRANCH
    
    def branch_exists(self, branch_name: str) -> bool:
        """Check if a branch exists locally or remotely"""
        try:
            # Check local branches
            result = subprocess.run(
                ['git', 'branch', '--list', branch_name],
                cwd=self.project_root,
                capture_output=True,
                text=True,
                check=True
            )
            if result.stdout.strip():
                return True
            
            # Check remote branches
            result = subprocess.run(
                ['git', 'branch', '-r', '--list', f'*//{branch_name}'],
                cwd=self.project_root,
                capture_output=True,
                text=True,
                check=True
            )
            return bool(result.stdout.strip())
            
        except subprocess.CalledProcessError:
            return False
    
    def create_branch(self, branch_name: str, base_branch: str = None) -> bool:
        """Create a new branch"""
        if self.branch_exists(branch_name):
            logger.info(f"Branch {branch_name} already exists")
            return True
        
        try:
            base = base_branch or self.get_current_branch()
            subprocess.run(
                ['git', 'checkout', '-b', branch_name, base],
                cwd=self.project_root,
                check=True,
                capture_output=True
            )
            logger.info(f"Created branch {branch_name} from {base}")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to create branch {branch_name}: {e}")
            return False
    
    def create_worktree(self, branch_name: str, task_id: str) -> Optional[Worktree]:
        """Create a new git worktree for a task"""
        worktree_name = f"{WORKTREE_PREFIX}{task_id}"
        worktree_path = self.worktrees_dir / worktree_name
        
        # Remove existing worktree if it exists
        if worktree_path.exists():
            self.remove_worktree(worktree_path)
        
        try:
            # Create the worktree
            if self.branch_exists(branch_name):
                # Branch exists, create worktree from existing branch
                cmd = ['git', 'worktree', 'add', str(worktree_path), branch_name]
            else:
                # Create new branch and worktree
                cmd = ['git', 'worktree', 'add', '-b', branch_name, str(worktree_path)]
            
            result = subprocess.run(
                cmd,
                cwd=self.project_root,
                capture_output=True,
                text=True,
                check=True
            )
            
            # Get commit hash
            commit_result = subprocess.run(
                ['git', 'rev-parse', 'HEAD'],
                cwd=worktree_path,
                capture_output=True,
                text=True,
                check=True
            )
            
            worktree = Worktree(
                path=worktree_path,
                branch=branch_name,
                commit=commit_result.stdout.strip(),
                created_at=datetime.now()
            )
            
            logger.info(f"Created worktree at {worktree_path} for branch {branch_name}")
            return worktree
            
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to create worktree for {branch_name}: {e}")
            if worktree_path.exists():
                self.remove_worktree(worktree_path)
            return None
    
    def list_worktrees(self) -> List[Worktree]:
        """List all existing worktrees"""
        try:
            result = subprocess.run(
                ['git', 'worktree', 'list', '--porcelain'],
                cwd=self.project_root,
                capture_output=True,
                text=True,
                check=True
            )
            
            worktrees = []
            current_worktree = {}
            
            for line in result.stdout.split('\n'):
                if line.startswith('worktree '):
                    if current_worktree:
                        worktrees.append(self._parse_worktree_info(current_worktree))
                        current_worktree = {}
                    current_worktree['path'] = line[9:]  # Remove 'worktree ' prefix
                elif line.startswith('HEAD '):
                    current_worktree['commit'] = line[5:]
                elif line.startswith('branch '):
                    current_worktree['branch'] = line[7:]
                elif line == 'bare':
                    current_worktree['bare'] = True
            
            # Add the last worktree
            if current_worktree:
                worktrees.append(self._parse_worktree_info(current_worktree))
            
            # Filter for our managed worktrees
            managed_worktrees = [
                wt for wt in worktrees 
                if wt.path.parent == self.worktrees_dir
            ]
            
            return managed_worktrees
            
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to list worktrees: {e}")
            return []
    
    def _parse_worktree_info(self, info: dict) -> Worktree:
        """Parse worktree information from git output"""
        return Worktree(
            path=Path(info['path']),
            branch=info.get('branch', ''),
            commit=info.get('commit', ''),
            bare=info.get('bare', False)
        )
    
    def remove_worktree(self, worktree_path: Path) -> bool:
        """Remove a git worktree"""
        try:
            subprocess.run(
                ['git', 'worktree', 'remove', str(worktree_path), '--force'],
                cwd=self.project_root,
                check=True,
                capture_output=True
            )
            logger.info(f"Removed worktree at {worktree_path}")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to remove worktree {worktree_path}: {e}")
            # Try to remove directory manually if git command failed
            try:
                import shutil
                if worktree_path.exists():
                    shutil.rmtree(worktree_path)
                    logger.info(f"Manually removed worktree directory {worktree_path}")
                return True
            except Exception as manual_error:
                logger.error(f"Failed to manually remove worktree directory: {manual_error}")
                return False
    
    def get_worktree_status(self, worktree_path: Path) -> Dict:
        """Get the status of a worktree"""
        try:
            # Get git status
            status_result = subprocess.run(
                ['git', 'status', '--porcelain'],
                cwd=worktree_path,
                capture_output=True,
                text=True,
                check=True
            )
            
            # Get current commit
            commit_result = subprocess.run(
                ['git', 'rev-parse', 'HEAD'],
                cwd=worktree_path,
                capture_output=True,
                text=True,
                check=True
            )
            
            # Get current branch
            branch_result = subprocess.run(
                ['git', 'branch', '--show-current'],
                cwd=worktree_path,
                capture_output=True,
                text=True,
                check=True
            )
            
            # Parse status
            modified_files = []
            untracked_files = []
            
            for line in status_result.stdout.split('\n'):
                if line.strip():
                    status_code = line[:2]
                    file_path = line[3:]
                    
                    if status_code[0] in ['M', 'A', 'D', 'R', 'C']:
                        modified_files.append(file_path)
                    elif status_code == '??':
                        untracked_files.append(file_path)
            
            return {
                'branch': branch_result.stdout.strip(),
                'commit': commit_result.stdout.strip(),
                'modified_files': modified_files,
                'untracked_files': untracked_files,
                'has_changes': bool(modified_files or untracked_files)
            }
            
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to get worktree status for {worktree_path}: {e}")
            return {}
    
    def merge_worktree(self, branch_name: str, target_branch: str = None) -> bool:
        """Merge a worktree branch back to the main branch"""
        target = target_branch or self.get_current_branch()
        
        try:
            # Switch to target branch
            subprocess.run(
                ['git', 'checkout', target],
                cwd=self.project_root,
                check=True,
                capture_output=True
            )
            
            # Pull latest changes
            subprocess.run(
                ['git', 'pull'],
                cwd=self.project_root,
                check=True,
                capture_output=True
            )
            
            # Merge the branch
            subprocess.run(
                ['git', 'merge', branch_name, '--no-ff'],
                cwd=self.project_root,
                check=True,
                capture_output=True
            )
            
            logger.info(f"Successfully merged {branch_name} into {target}")
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to merge {branch_name}: {e}")
            return False
    
    def push_branch(self, branch_name: str) -> bool:
        """Push a branch to remote repository"""
        try:
            subprocess.run(
                ['git', 'push', 'origin', branch_name],
                cwd=self.project_root,
                check=True,
                capture_output=True
            )
            logger.info(f"Pushed branch {branch_name} to origin")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to push branch {branch_name}: {e}")
            return False
    
    def delete_branch(self, branch_name: str, force: bool = False) -> bool:
        """Delete a local branch"""
        try:
            flag = '-D' if force else '-d'
            subprocess.run(
                ['git', 'branch', flag, branch_name],
                cwd=self.project_root,
                check=True,
                capture_output=True
            )
            logger.info(f"Deleted branch {branch_name}")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to delete branch {branch_name}: {e}")
            return False
    
    def cleanup_completed_task(self, task_id: str, branch_name: str, merge: bool = True) -> bool:
        """Clean up worktree and branch for a completed task"""
        worktree_name = f"{WORKTREE_PREFIX}{task_id}"
        worktree_path = self.worktrees_dir / worktree_name
        
        success = True
        
        # Merge if requested and branch has changes
        if merge and self.branch_exists(branch_name):
            if not self.merge_worktree(branch_name):
                logger.warning(f"Failed to merge branch {branch_name}")
                success = False
        
        # Remove worktree
        if worktree_path.exists():
            if not self.remove_worktree(worktree_path):
                logger.warning(f"Failed to remove worktree for task {task_id}")
                success = False
        
        # Delete branch (optional)
        # Note: You might want to keep branches for audit purposes
        # if not self.delete_branch(branch_name):
        #     logger.warning(f"Failed to delete branch {branch_name}")
        #     success = False
        
        return success
    
    def get_changes_summary(self, worktree_path: Path) -> str:
        """Get a summary of changes in a worktree"""
        try:
            # Get diff stats
            result = subprocess.run(
                ['git', 'diff', '--stat', 'HEAD~1..HEAD'],
                cwd=worktree_path,
                capture_output=True,
                text=True,
                check=True
            )
            return result.stdout
        except subprocess.CalledProcessError:
            return "No changes or error getting diff"