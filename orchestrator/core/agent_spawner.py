"""
Agent spawner for the Agent Orchestrator
Handles creating and managing AI coding agents
"""

import logging
import uuid
from typing import List, Dict, Optional, Tu<PERSON>
from datetime import datetime
from pathlib import Path
from dataclasses import dataclass

from .task_parser import Task, TaskParser
from .git_manager import <PERSON><PERSON><PERSON><PERSON><PERSON>, Worktree
from ..utils.tmux_manager import Tmux<PERSON>anager, TmuxSession
from ..config.agent_templates import Agent<PERSON>romptTemplates
from ..config.settings import (
    MAX_CONCURRENT_AGENTS, AGENT_TIMEOUT, TaskStatus, TaskPriority
)

logger = logging.getLogger(__name__)

@dataclass
class Agent:
    """Represents an active AI agent"""
    id: str
    task_id: str
    task: Task
    worktree: Worktree
    tmux_session: TmuxSession
    created_at: datetime
    status: str = "starting"  # starting, running, idle, completed, failed
    last_activity: Optional[datetime] = None
    error_count: int = 0
    
    def __post_init__(self):
        if not self.last_activity:
            self.last_activity = self.created_at


class AgentSpawner:
    """Spawns and manages AI coding agents"""
    
    def __init__(self, project_root: Path = None):
        self.git_manager = GitManager(project_root)
        self.tmux_manager = TmuxManager()
        self.task_parser = TaskParser()
        self.active_agents: Dict[str, Agent] = {}
        self.completed_agents: List[Agent] = []
        self.project_context = {}
    
    def analyze_and_spawn_agents(self, max_agents: int = MAX_CONCURRENT_AGENTS) -> List[Agent]:
        """Analyze tasks and spawn agents for ready tasks"""
        logger.info("Starting task analysis and agent spawning")
        
        # Parse tasks
        tasks = self.task_parser.parse_tasks()
        if not tasks:
            logger.warning("No tasks found to process")
            return []
        
        # Find ready tasks
        ready_tasks = self.task_parser.find_ready_tasks()
        if not ready_tasks:
            logger.info("No tasks are ready to be assigned")
            return []
        
        logger.info(f"Found {len(ready_tasks)} ready tasks")
        
        # Group tasks that can run in parallel
        parallel_groups = self.task_parser.find_parallel_groups(ready_tasks)
        
        spawned_agents = []
        total_spawned = 0
        
        for group in parallel_groups:
            if total_spawned >= max_agents:
                logger.info(f"Reached maximum agent limit ({max_agents})")
                break
            
            for task in group:
                if total_spawned >= max_agents:
                    break
                
                agent = self.spawn_agent(task)
                if agent:
                    spawned_agents.append(agent)
                    total_spawned += 1
                    logger.info(f"Spawned agent {agent.id} for task {task.id}")
                else:
                    logger.error(f"Failed to spawn agent for task {task.id}")
        
        logger.info(f"Successfully spawned {len(spawned_agents)} agents")
        return spawned_agents
    
    def spawn_agent(self, task: Task) -> Optional[Agent]:
        """Spawn a single agent for a task"""
        try:
            # Generate unique agent ID
            agent_id = f"agent-{task.id}-{uuid.uuid4().hex[:8]}"
            
            # Create git worktree
            worktree = self.git_manager.create_worktree(task.branch, task.id)
            if not worktree:
                logger.error(f"Failed to create worktree for task {task.id}")
                return None
            
            # Generate agent prompt
            prompt = AgentPromptTemplates.generate_agent_prompt(task, self.project_context)
            
            # Create tmux session
            tmux_session = self.tmux_manager.create_session(
                task_id=task.id,
                working_directory=worktree.path,
                agent_prompt=prompt
            )
            
            if not tmux_session:
                logger.error(f"Failed to create tmux session for task {task.id}")
                self.git_manager.remove_worktree(worktree.path)
                return None
            
            # Create agent object
            agent = Agent(
                id=agent_id,
                task_id=task.id,
                task=task,
                worktree=worktree,
                tmux_session=tmux_session,
                created_at=datetime.now()
            )
            
            # Update task status
            self.task_parser.update_task_status(
                task_id=task.id,
                status=TaskStatus.CLAIMED,
                session=tmux_session.name,
                agent_id=agent_id
            )
            
            # Track the agent
            self.active_agents[agent_id] = agent
            
            logger.info(f"Successfully spawned agent {agent_id} for task {task.id}")
            return agent
            
        except Exception as e:
            logger.error(f"Error spawning agent for task {task.id}: {e}")
            return None
    
    def get_agent_status(self, agent_id: str) -> Dict:
        """Get comprehensive status of an agent"""
        if agent_id not in self.active_agents:
            return {'status': 'not_found'}
        
        agent = self.active_agents[agent_id]
        
        # Get tmux session status
        tmux_status = self.tmux_manager.monitor_session(agent.tmux_session.name)
        
        # Get git worktree status
        git_status = self.git_manager.get_worktree_status(agent.worktree.path)
        
        # Analyze agent health
        health = self._analyze_agent_health(agent, tmux_status, git_status)
        
        return {
            'agent_id': agent_id,
            'task_id': agent.task_id,
            'task_title': agent.task.title,
            'created_at': agent.created_at,
            'status': agent.status,
            'health': health,
            'tmux': tmux_status,
            'git': git_status,
            'error_count': agent.error_count,
            'last_activity': agent.last_activity
        }
    
    def _analyze_agent_health(self, agent: Agent, tmux_status: Dict, git_status: Dict) -> str:
        """Analyze agent health based on various indicators"""
        
        # Check if tmux session is dead
        if tmux_status.get('status') != 'running':
            return 'dead'
        
        # Check tmux health indicators
        tmux_health = tmux_status.get('health', 'unknown')
        if tmux_health == 'critical':
            return 'critical'
        elif tmux_health == 'intervention_needed':
            return 'stuck'
        elif tmux_health == 'idle':
            # Check how long it's been idle
            idle_time = datetime.now() - agent.last_activity
            if idle_time.total_seconds() > 600:  # 10 minutes
                return 'idle_too_long'
            return 'idle'
        
        # Check for git activity
        if git_status.get('has_changes'):
            agent.last_activity = datetime.now()
            return 'active'
        
        # Check error count
        if agent.error_count > 5:
            return 'error_prone'
        
        return 'healthy'
    
    def monitor_agents(self) -> Dict[str, str]:
        """Monitor all active agents and return their health status"""
        agent_health = {}
        
        for agent_id, agent in self.active_agents.items():
            status = self.get_agent_status(agent_id)
            agent_health[agent_id] = status.get('health', 'unknown')
            
            # Update agent status based on health
            health = status['health']
            if health in ['dead', 'critical']:
                agent.status = 'failed'
            elif health == 'stuck':
                agent.status = 'intervention_required'
            elif health in ['active', 'healthy']:
                agent.status = 'running'
            elif health == 'idle':
                agent.status = 'idle'
        
        return agent_health
    
    def handle_stuck_agent(self, agent_id: str) -> bool:
        """Handle an agent that appears to be stuck"""
        if agent_id not in self.active_agents:
            return False
        
        agent = self.active_agents[agent_id]
        
        try:
            # Try to send a gentle nudge first
            self.tmux_manager.send_command(
                agent.tmux_session.name,
                "# Agent orchestrator: Please continue or provide status update"
            )
            
            # Wait a bit and check again
            import time
            time.sleep(30)
            
            status = self.get_agent_status(agent_id)
            if status.get('health') != 'stuck':
                logger.info(f"Agent {agent_id} recovered from stuck state")
                return True
            
            # If still stuck, try more direct intervention
            self.tmux_manager.send_command(
                agent.tmux_session.name,
                "# Please summarize your current progress and next steps"
            )
            
            logger.warning(f"Attempted intervention for stuck agent {agent_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error handling stuck agent {agent_id}: {e}")
            return False
    
    def complete_agent_task(self, agent_id: str, merge_changes: bool = True) -> bool:
        """Mark an agent's task as completed and clean up"""
        if agent_id not in self.active_agents:
            logger.error(f"Agent {agent_id} not found")
            return False
        
        agent = self.active_agents[agent_id]
        
        try:
            # Get final status
            git_status = self.git_manager.get_worktree_status(agent.worktree.path)
            
            # Update task status
            self.task_parser.update_task_status(
                task_id=agent.task_id,
                status=TaskStatus.COMPLETED
            )
            
            # Clean up git worktree (optionally merge)
            if merge_changes and git_status.get('has_changes'):
                success = self.git_manager.cleanup_completed_task(
                    task_id=agent.task_id,
                    branch_name=agent.task.branch,
                    merge=True
                )
                if not success:
                    logger.warning(f"Failed to merge changes for agent {agent_id}")
            else:
                # Just remove worktree without merging
                self.git_manager.remove_worktree(agent.worktree.path)
            
            # Kill tmux session
            self.tmux_manager.kill_session(agent.tmux_session.name)
            
            # Move agent to completed list
            agent.status = 'completed'
            self.completed_agents.append(agent)
            del self.active_agents[agent_id]
            
            logger.info(f"Completed agent {agent_id} for task {agent.task_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error completing agent {agent_id}: {e}")
            return False
    
    def terminate_agent(self, agent_id: str, reason: str = "manual termination") -> bool:
        """Forcefully terminate an agent"""
        if agent_id not in self.active_agents:
            return False
        
        agent = self.active_agents[agent_id]
        
        try:
            # Update task status
            self.task_parser.update_task_status(
                task_id=agent.task_id,
                status=TaskStatus.FAILED
            )
            
            # Kill tmux session
            self.tmux_manager.kill_session(agent.tmux_session.name)
            
            # Remove worktree
            self.git_manager.remove_worktree(agent.worktree.path)
            
            # Update agent status
            agent.status = 'terminated'
            self.completed_agents.append(agent)
            del self.active_agents[agent_id]
            
            logger.info(f"Terminated agent {agent_id}: {reason}")
            return True
            
        except Exception as e:
            logger.error(f"Error terminating agent {agent_id}: {e}")
            return False
    
    def cleanup_dead_agents(self) -> List[str]:
        """Clean up agents whose tmux sessions have died"""
        dead_agents = []
        
        for agent_id, agent in list(self.active_agents.items()):
            if not self.tmux_manager.session_exists(agent.tmux_session.name):
                logger.info(f"Found dead agent {agent_id}")
                
                # Clean up worktree
                if agent.worktree.path.exists():
                    self.git_manager.remove_worktree(agent.worktree.path)
                
                # Update task status
                self.task_parser.update_task_status(
                    task_id=agent.task_id,
                    status=TaskStatus.FAILED
                )
                
                # Move to completed list
                agent.status = 'died'
                self.completed_agents.append(agent)
                del self.active_agents[agent_id]
                
                dead_agents.append(agent_id)
        
        return dead_agents
    
    def get_orchestrator_status(self) -> Dict:
        """Get overall orchestrator status"""
        # Clean up dead agents first
        dead_agents = self.cleanup_dead_agents()
        
        # Monitor remaining agents
        agent_health = self.monitor_agents()
        
        # Get task summary
        tasks = self.task_parser.parse_tasks()
        task_summary = {
            'total': len(tasks),
            'unclaimed': len([t for t in tasks if t.status == TaskStatus.UNCLAIMED]),
            'claimed': len([t for t in tasks if t.status == TaskStatus.CLAIMED]),
            'in_progress': len([t for t in tasks if t.status == TaskStatus.IN_PROGRESS]),
            'completed': len([t for t in tasks if t.status == TaskStatus.COMPLETED]),
            'failed': len([t for t in tasks if t.status == TaskStatus.FAILED]),
            'intervention_required': len([t for t in tasks if t.status == TaskStatus.INTERVENTION_REQUIRED])
        }
        
        return {
            'timestamp': datetime.now(),
            'active_agents': len(self.active_agents),
            'completed_agents': len(self.completed_agents),
            'dead_agents_cleaned': len(dead_agents),
            'agent_health': agent_health,
            'task_summary': task_summary,
            'git_worktrees': len(self.git_manager.list_worktrees()),
            'tmux_sessions': len(self.tmux_manager.list_sessions())
        }
    
    def create_monitoring_dashboard(self) -> str:
        """Create a tmux monitoring dashboard for all agents"""
        active_sessions = [agent.tmux_session.name for agent in self.active_agents.values()]
        if not active_sessions:
            logger.info("No active agents to monitor")
            return ""
        
        monitor_session = self.tmux_manager.create_monitoring_session(active_sessions)
        return monitor_session
    
    def save_state(self, file_path: Path):
        """Save orchestrator state to file"""
        state = {
            'active_agents': {
                agent_id: {
                    'task_id': agent.task_id,
                    'created_at': agent.created_at.isoformat(),
                    'status': agent.status,
                    'error_count': agent.error_count
                }
                for agent_id, agent in self.active_agents.items()
            },
            'project_context': self.project_context,
            'saved_at': datetime.now().isoformat()
        }
        
        import json
        with open(file_path, 'w') as f:
            json.dump(state, f, indent=2)
        
        # Also save tmux session state
        tmux_state_file = file_path.parent / f"{file_path.stem}_tmux.json"
        self.tmux_manager.save_session_state(tmux_state_file)