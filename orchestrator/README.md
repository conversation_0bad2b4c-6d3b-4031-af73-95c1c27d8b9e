# Agent Orchestrator

An intelligent system for spawning and managing AI coding agents to work on tasks in parallel using git worktrees and tmux sessions.

## 🎯 Overview

The Agent Orchestrator reads tasks from a `tasks.md` file, analyzes dependencies, and automatically spawns AI coding agents to work on ready tasks in isolated git worktrees. Each agent runs in its own tmux session and can work independently while the orchestrator monitors progress and manages resources.

## ✨ Features

- **Task Parsing**: Reads and analyzes tasks from Markdown files
- **Dependency Management**: Automatically determines which tasks are ready to execute
- **Git Worktrees**: Creates isolated development environments for each agent
- **Tmux Sessions**: Spawns agents in separate tmux sessions for monitoring
- **Health Monitoring**: Tracks agent progress and health status
- **Resource Cleanup**: Automatic cleanup of completed/failed tasks
- **CLI Interface**: Comprehensive command-line interface for management

## 🏗️ Architecture

```
orchestrator/
├── cli.py                  # Main CLI interface
├── core/                   # Core orchestrator logic
│   ├── task_parser.py     # Task parsing and dependency analysis
│   ├── git_manager.py     # Git worktree management
│   └── agent_spawner.py   # Agent spawning and lifecycle
├── utils/                  # Utility modules
│   ├── tmux_manager.py    # Tmux session management
│   ├── status_monitor.py  # Health monitoring and alerts
│   └── cleanup_manager.py # Resource cleanup utilities
├── config/                 # Configuration
│   ├── settings.py        # Main configuration
│   └── agent_templates.py # Agent prompt templates
└── README.md              # This file
```

## 🚀 Quick Start

1. **Create a tasks.md file** in your project root (see example in repository)

2. **Install dependencies**:
   ```bash
   pip install click
   # Ensure tmux is installed: sudo apt install tmux (Ubuntu) or brew install tmux (macOS)
   ```

3. **Spawn agents**:
   ```bash
   ./orchestrator/cli.py spawn --max-agents 3
   ```

4. **Monitor progress**:
   ```bash
   ./orchestrator/cli.py status
   ./orchestrator/cli.py monitor --dashboard
   ```

5. **View agent details**:
   ```bash
   ./orchestrator/cli.py agent --all
   ```

## 🎛️ CLI Commands

### Spawning Agents

```bash
# Spawn agents for ready tasks
./orchestrator/cli.py spawn --max-agents 5

# Dry run to see what would be spawned
./orchestrator/cli.py spawn --dry-run
```

### Monitoring

```bash
# Show overall status
./orchestrator/cli.py status

# Show detailed status in JSON format
./orchestrator/cli.py status --format json

# Show specific agent details
./orchestrator/cli.py agent agent-123

# Show all agent details
./orchestrator/cli.py agent --all

# Start monitoring system
./orchestrator/cli.py monitor --start

# Create tmux monitoring dashboard
./orchestrator/cli.py monitor --dashboard

# Generate health report
./orchestrator/cli.py monitor --health-report
```

### Task Management

```bash
# List all tasks
./orchestrator/cli.py tasks

# Complete a task/agent
./orchestrator/cli.py complete --task-id example-task
./orchestrator/cli.py complete --agent-id agent-123

# Terminate a task/agent
./orchestrator/cli.py terminate --task-id example-task --reason "Manual stop"
./orchestrator/cli.py terminate --all
```

### Cleanup

```bash
# Clean up completed tasks
./orchestrator/cli.py cleanup --completed

# Clean up failed tasks
./orchestrator/cli.py cleanup --failed

# Clean up orphaned resources
./orchestrator/cli.py cleanup --orphaned

# Free disk space
./orchestrator/cli.py cleanup --disk-space 2.5

# Automatic cleanup
./orchestrator/cli.py cleanup --auto

# Show cleanup recommendations
./orchestrator/cli.py cleanup --dry-run
```

## 📋 Task Definition Format

Tasks are defined in a `tasks.md` file in the project root:

```markdown
## Task: unique-task-id

**Title**: Brief description of the task
**Priority**: high|medium|low
**Estimated Time**: 1-2 hours
**Dependencies**: task-1, task-2 (or "none")
**File Path**: path/to/relevant/files
**Branch**: feature/task-branch-name

### Description
Detailed description of what needs to be implemented.

### Acceptance Criteria
- [ ] Criterion 1
- [ ] Criterion 2
- [ ] Criterion 3

---
```

## ⚙️ Configuration

Configuration is handled in `config/settings.py`. Key settings include:

- `MAX_CONCURRENT_AGENTS`: Maximum number of agents to run simultaneously
- `CLAUDE_CODE_COMMAND`: Command to invoke Claude Code
- `DEFAULT_TOOLS`: Default tools available to agents
- `STATUS_CHECK_INTERVAL`: How often to check agent health
- `CLEANUP_RETENTION_DAYS`: How long to keep completed tasks

## 📊 Monitoring and Alerts

The orchestrator includes comprehensive monitoring:

- **Agent Health**: Tracks if agents are active, idle, stuck, or in error state
- **Resource Usage**: Monitors disk space, worktree count, tmux sessions
- **Alerts**: Generates alerts for various conditions (agent failures, resource limits)
- **Metrics**: Collects performance and usage metrics

## 🧹 Resource Management

The system automatically manages resources:

- **Git Worktrees**: Creates isolated environments for each task
- **Tmux Sessions**: Manages agent sessions with automatic cleanup
- **Disk Space**: Monitors usage and provides cleanup recommendations
- **Branch Management**: Handles branch creation and optional merging

## 🔧 Troubleshooting

### Common Issues

1. **tmux not found**: Install tmux with your package manager
2. **Permission denied**: Ensure the CLI script is executable (`chmod +x cli.py`)
3. **No tasks spawned**: Check that tasks.md exists and has properly formatted tasks
4. **Agent stuck**: Use `./orchestrator/cli.py agent <agent-id>` to check status

### Debugging

- Use `--verbose` flag for detailed logging
- Check tmux sessions with `tmux list-sessions`
- Monitor agent output with `tmux attach -t agent-<task-id>`
- Use `./orchestrator/cli.py monitor --dashboard` for real-time monitoring

## 🔄 Workflow

1. **Task Analysis** - Parse tasks.md and identify dependencies
2. **Agent Spawning** - Create worktrees and tmux sessions for ready tasks
3. **Progress Monitoring** - Track agent output and health status
4. **Intervention Handling** - Detect and resolve stuck agents
5. **Work Merging** - Integrate completed features automatically
6. **Cleanup** - Remove worktrees and sessions when tasks complete