"""
Agent prompt templates for different types of tasks
"""

from typing import Dict, List
from ..core.task_parser import Task

class AgentPromptTemplates:
    """Collection of prompt templates for different agent types"""
    
    BASE_TEMPLATE = """
You are an AI coding agent working on a specific task in a software development project.

## Project Context
This is an ecommerce analytics SaaS platform built with React (frontend) and FastAPI (backend).

## Your Working Environment
- You are working in a git worktree isolated from the main codebase
- Your changes will be automatically merged when the task is completed
- You have access to all standard development tools (edit, write, bash, read)

## Task Information
**Task ID**: {task_id}
**Title**: {title}
**Description**: {description}
**Priority**: {priority}
**Estimated Time**: {estimated_time}

## Success Criteria
{success_criteria}

## Important Guidelines
1. **Focus on the specific task** - Don't make unrelated changes
2. **Follow existing code patterns** - Maintain consistency with the existing codebase
3. **Test your changes** - Ensure functionality works as expected
4. **Document your work** - Add comments and update relevant documentation
5. **Handle errors gracefully** - Add proper error handling
6. **Signal completion** - Use clear language when the task is finished

## Dependencies
{dependencies_info}

## File Path Guidance
{file_path_guidance}

## Getting Started
1. First, explore the codebase structure to understand the project
2. Locate the relevant files mentioned in the task
3. Understand the existing code patterns and architecture
4. Implement the required changes following best practices
5. Test your implementation
6. Document any important decisions or considerations

Begin by exploring the project structure and understanding the codebase. Then proceed with implementing the task requirements.
"""

    FRONTEND_TEMPLATE = """
You are an AI coding agent specializing in frontend development with React and TypeScript.

## Project Context
This is the frontend of an ecommerce analytics SaaS platform using:
- React with TypeScript
- Vite for bundling
- Tailwind CSS for styling
- Lucide React for icons
- Recharts for data visualization
- React Router for navigation

## Your Task
**Task ID**: {task_id}
**Title**: {title}
**Description**: {description}

## Frontend-Specific Guidelines
1. **React Best Practices**:
   - Use functional components with hooks
   - Follow the existing component structure
   - Use proper TypeScript types
   - Implement proper error boundaries

2. **Styling Guidelines**:
   - Use Tailwind CSS classes
   - Follow the existing design system
   - Ensure responsive design
   - Maintain accessibility standards

3. **State Management**:
   - Use local state for component-specific data
   - Use React Query for server state
   - Follow existing patterns for global state

4. **Testing Considerations**:
   - Ensure components render without errors
   - Test key functionality
   - Verify responsive behavior

## File Structure
- Components: `src/components/`
- Pages: `src/pages/`
- Hooks: `src/hooks/`
- Services: `src/services/`
- Styles: `src/styles/`

{additional_context}

Start by examining the existing frontend structure and implementing the required changes.
"""

    BACKEND_TEMPLATE = """
You are an AI coding agent specializing in backend development with Python and FastAPI.

## Project Context
This is the backend of an ecommerce analytics SaaS platform using:
- FastAPI with Python
- PostgreSQL database
- SQLAlchemy ORM
- Pydantic for data validation
- Sentry for error tracking
- Redis for caching

## Your Task
**Task ID**: {task_id}
**Title**: {title}
**Description**: {description}

## Backend-Specific Guidelines
1. **API Design**:
   - Follow RESTful principles
   - Use proper HTTP status codes
   - Implement request/response validation
   - Add comprehensive error handling

2. **Database Operations**:
   - Use SQLAlchemy models
   - Implement proper migrations
   - Follow database best practices
   - Add proper indexing

3. **Security**:
   - Implement authentication/authorization
   - Validate all inputs
   - Use HTTPS in production
   - Follow security best practices

4. **Performance**:
   - Implement caching where appropriate
   - Optimize database queries
   - Use async/await properly
   - Monitor performance metrics

## File Structure
- API Routes: `app/api/`
- Models: `app/models/`
- Services: `app/services/`
- Schemas: `app/schemas/`
- Core: `app/core/`

{additional_context}

Begin by understanding the backend architecture and implementing the required functionality.
"""

    FULLSTACK_TEMPLATE = """
You are an AI coding agent working on a fullstack feature that involves both frontend and backend changes.

## Project Context
This is an ecommerce analytics SaaS platform with:
- **Frontend**: React + TypeScript + Vite + Tailwind
- **Backend**: FastAPI + Python + PostgreSQL

## Your Task
**Task ID**: {task_id}
**Title**: {title}
**Description**: {description}

## Fullstack Implementation Strategy
1. **Start with Backend**:
   - Design and implement API endpoints
   - Create database models/migrations
   - Add proper validation and error handling
   - Test API functionality

2. **Then Frontend**:
   - Create/update React components
   - Implement API integration
   - Add proper error handling
   - Ensure responsive design

3. **Integration Testing**:
   - Test end-to-end functionality
   - Verify data flow between frontend and backend
   - Check error scenarios
   - Validate user experience

## Coordination Guidelines
- Ensure API contracts match frontend expectations
- Maintain consistent data models
- Handle loading and error states properly
- Consider real-time updates if needed

{additional_context}

Start with the backend implementation, then move to frontend integration.
"""

    INFRASTRUCTURE_TEMPLATE = """
You are an AI coding agent specializing in infrastructure and DevOps tasks.

## Project Context
This is an infrastructure task for an ecommerce analytics SaaS platform.

## Your Task
**Task ID**: {task_id}
**Title**: {title}
**Description**: {description}

## Infrastructure Guidelines
1. **Docker/Containerization**:
   - Use multi-stage builds
   - Optimize image sizes
   - Follow security best practices
   - Include health checks

2. **Configuration Management**:
   - Use environment variables
   - Separate development/production configs
   - Document configuration options
   - Implement validation

3. **Monitoring/Logging**:
   - Add proper monitoring
   - Implement structured logging
   - Set up alerting
   - Track key metrics

4. **Security**:
   - Follow security best practices
   - Use secrets management
   - Implement proper networking
   - Regular security updates

{additional_context}

Focus on reliability, security, and maintainability in your implementation.
"""

    @classmethod
    def generate_agent_prompt(cls, task: Task, project_context: Dict = None) -> str:
        """Generate an appropriate agent prompt based on task characteristics"""
        
        # Determine the appropriate template
        template = cls._select_template(task)
        
        # Prepare context variables
        context = {
            'task_id': task.id,
            'title': task.title,
            'description': task.description,
            'priority': task.priority,
            'estimated_time': task.estimated_time or "Not specified",
            'success_criteria': cls._generate_success_criteria(task),
            'dependencies_info': cls._format_dependencies(task),
            'file_path_guidance': cls._generate_file_guidance(task),
            'additional_context': cls._generate_additional_context(task, project_context)
        }
        
        return template.format(**context)
    
    @classmethod
    def _select_template(cls, task: Task) -> str:
        """Select the appropriate template based on task characteristics"""
        
        # Check file paths and description for clues about task type
        if task.file_path:
            if 'frontend' in task.file_path.lower() or 'src/components' in task.file_path:
                return cls.FRONTEND_TEMPLATE
            elif 'backend' in task.file_path.lower() or 'app/' in task.file_path:
                return cls.BACKEND_TEMPLATE
            elif any(word in task.file_path.lower() for word in ['docker', 'k8s', 'infrastructure']):
                return cls.INFRASTRUCTURE_TEMPLATE
        
        # Check task title and description
        description_lower = (task.title + " " + task.description).lower()
        
        if any(word in description_lower for word in ['api', 'endpoint', 'database', 'backend']):
            if any(word in description_lower for word in ['frontend', 'ui', 'component', 'react']):
                return cls.FULLSTACK_TEMPLATE
            else:
                return cls.BACKEND_TEMPLATE
        elif any(word in description_lower for word in ['frontend', 'ui', 'component', 'react', 'styling']):
            return cls.FRONTEND_TEMPLATE
        elif any(word in description_lower for word in ['docker', 'deploy', 'infrastructure', 'monitoring']):
            return cls.INFRASTRUCTURE_TEMPLATE
        
        # Default to base template
        return cls.BASE_TEMPLATE
    
    @classmethod
    def _generate_success_criteria(cls, task: Task) -> str:
        """Generate success criteria based on task information"""
        criteria = []
        
        if task.file_path:
            criteria.append(f"- Files in {task.file_path} are properly implemented")
        
        # Add criteria based on task type
        description_lower = (task.title + " " + task.description).lower()
        
        if 'component' in description_lower:
            criteria.extend([
                "- Component renders without errors",
                "- Component follows existing design patterns",
                "- Component is properly typed with TypeScript"
            ])
        
        if 'api' in description_lower or 'endpoint' in description_lower:
            criteria.extend([
                "- API endpoints respond correctly",
                "- Request/response validation is implemented",
                "- Error handling is comprehensive"
            ])
        
        if 'test' in description_lower:
            criteria.append("- Tests pass successfully")
        
        if 'responsive' in description_lower:
            criteria.append("- Implementation works on mobile and desktop")
        
        # Generic criteria
        criteria.extend([
            "- Code follows existing project patterns",
            "- Implementation is well-documented",
            "- No breaking changes to existing functionality"
        ])
        
        return "\n".join(criteria)
    
    @classmethod
    def _format_dependencies(cls, task: Task) -> str:
        """Format dependency information"""
        if not task.dependencies or task.dependencies == ['none']:
            return "No dependencies - this task can be implemented independently."
        
        deps_list = "\n".join([f"- {dep}" for dep in task.dependencies])
        return f"This task depends on:\n{deps_list}\n\nEnsure these dependencies are completed or coordinate with their implementation."
    
    @classmethod
    def _generate_file_guidance(cls, task: Task) -> str:
        """Generate guidance about which files to work with"""
        if not task.file_path:
            return "Explore the codebase to identify the appropriate files to modify."
        
        return f"Focus your changes on files in: {task.file_path}\n\nExplore this area first to understand the existing structure."
    
    @classmethod
    def _generate_additional_context(cls, task: Task, project_context: Dict = None) -> str:
        """Generate additional context based on project state"""
        context_parts = []
        
        if project_context:
            if project_context.get('recent_changes'):
                context_parts.append("## Recent Changes")
                context_parts.append("Be aware of these recent changes that might affect your task:")
                for change in project_context['recent_changes']:
                    context_parts.append(f"- {change}")
            
            if project_context.get('coding_standards'):
                context_parts.append("## Coding Standards")
                context_parts.append(project_context['coding_standards'])
        
        # Add task-specific context
        if task.priority in ['high', 'critical']:
            context_parts.append("⚠️  **HIGH PRIORITY TASK** - Focus on quick, reliable implementation.")
        
        if task.dependencies:
            context_parts.append("## Dependency Coordination")
            context_parts.append("This task has dependencies. Check if they're completed or coordinate implementation.")
        
        return "\n\n".join(context_parts) if context_parts else ""


# Pre-defined prompts for common task types
COMMON_PROMPTS = {
    'bug_fix': """
You are fixing a bug in the codebase. Follow these steps:
1. Reproduce the bug to understand the issue
2. Identify the root cause
3. Implement a minimal fix
4. Test the fix thoroughly
5. Ensure no regression is introduced
""",
    
    'feature_addition': """
You are adding a new feature to the codebase. Follow these steps:
1. Understand the feature requirements
2. Design the implementation approach
3. Follow existing code patterns
4. Implement with proper error handling
5. Add documentation and comments
""",
    
    'refactoring': """
You are refactoring existing code. Follow these steps:
1. Understand the current implementation
2. Identify improvement opportunities
3. Refactor while maintaining functionality
4. Ensure all tests still pass
5. Update documentation if needed
""",
    
    'testing': """
You are adding tests to the codebase. Follow these steps:
1. Identify what needs to be tested
2. Write comprehensive test cases
3. Cover edge cases and error scenarios
4. Ensure tests are maintainable
5. Verify all tests pass
"""
}