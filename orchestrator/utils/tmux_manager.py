"""
Tmux session manager for the Agent Orchestrator
Handles creating, monitoring, and managing tmux sessions for agents
"""

import subprocess
import logging
import time
import json
from typing import List, Dict, Optional, Tuple
from pathlib import Path
from dataclasses import dataclass, asdict
from datetime import datetime

from ..config.settings import (
    TMUX_SESSION_PREFIX, TMUX_PANE_CAPTURE_INTERVAL, 
    CLAUDE_CODE_COMMAND, DEFAULT_TOOLS, AGENT_TIMEOUT
)

logger = logging.getLogger(__name__)

@dataclass
class TmuxSession:
    """Represents a tmux session"""
    name: str
    created_at: datetime
    working_directory: str
    agent_command: str
    task_id: str
    status: str = "running"  # running, idle, completed, failed
    last_activity: Optional[datetime] = None
    pane_count: int = 1
    
    def to_dict(self) -> dict:
        """Convert to dictionary for JSON serialization"""
        data = asdict(self)
        data['created_at'] = self.created_at.isoformat()
        data['last_activity'] = self.last_activity.isoformat() if self.last_activity else None
        return data
    
    @classmethod
    def from_dict(cls, data: dict) -> 'TmuxSession':
        """Create from dictionary"""
        data['created_at'] = datetime.fromisoformat(data['created_at'])
        if data['last_activity']:
            data['last_activity'] = datetime.fromisoformat(data['last_activity'])
        return cls(**data)


class TmuxManager:
    """Manages tmux sessions for AI agents"""
    
    def __init__(self):
        self.sessions: Dict[str, TmuxSession] = {}
        self._ensure_tmux_available()
    
    def _ensure_tmux_available(self):
        """Check if tmux is available on the system"""
        try:
            subprocess.run(['tmux', '-V'], capture_output=True, check=True)
        except (subprocess.CalledProcessError, FileNotFoundError):
            raise RuntimeError("tmux is not installed or not available in PATH")
    
    def _run_tmux_command(self, cmd: List[str], capture_output: bool = True) -> subprocess.CompletedProcess:
        """Run a tmux command and return the result"""
        full_cmd = ['tmux'] + cmd
        try:
            result = subprocess.run(
                full_cmd,
                capture_output=capture_output,
                text=True,
                check=True
            )
            return result
        except subprocess.CalledProcessError as e:
            logger.error(f"Tmux command failed: {' '.join(full_cmd)}")
            logger.error(f"Error: {e.stderr if e.stderr else e}")
            raise
    
    def session_exists(self, session_name: str) -> bool:
        """Check if a tmux session exists"""
        try:
            self._run_tmux_command(['has-session', '-t', session_name])
            return True
        except subprocess.CalledProcessError:
            return False
    
    def create_session(self, task_id: str, working_directory: Path, agent_prompt: str, 
                      tools: List[str] = None) -> Optional[TmuxSession]:
        """Create a new tmux session for an agent"""
        session_name = f"{TMUX_SESSION_PREFIX}{task_id}"
        
        # Kill existing session if it exists
        if self.session_exists(session_name):
            self.kill_session(session_name)
        
        tools = tools or DEFAULT_TOOLS
        tool_args = ' '.join([f'--tool {tool}' for tool in tools])
        
        # Build the agent command
        agent_command = f'{CLAUDE_CODE_COMMAND} --prompt "{agent_prompt}" {tool_args}'
        
        try:
            # Create the session in detached mode
            self._run_tmux_command([
                'new-session',
                '-d',
                '-s', session_name,
                '-c', str(working_directory),
                agent_command
            ])
            
            # Create session object
            session = TmuxSession(
                name=session_name,
                created_at=datetime.now(),
                working_directory=str(working_directory),
                agent_command=agent_command,
                task_id=task_id,
                last_activity=datetime.now()
            )
            
            self.sessions[session_name] = session
            logger.info(f"Created tmux session {session_name} for task {task_id}")
            
            return session
            
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to create tmux session {session_name}: {e}")
            return None
    
    def list_sessions(self) -> List[TmuxSession]:
        """List all managed tmux sessions"""
        try:
            result = self._run_tmux_command(['list-sessions', '-F', '#{session_name}'])
            active_sessions = result.stdout.strip().split('\n') if result.stdout.strip() else []
            
            # Filter for our managed sessions
            managed_sessions = [
                name for name in active_sessions 
                if name.startswith(TMUX_SESSION_PREFIX)
            ]
            
            # Update our session tracking
            current_sessions = {}
            for session_name in managed_sessions:
                if session_name in self.sessions:
                    current_sessions[session_name] = self.sessions[session_name]
                else:
                    # Discovered session not in our tracking - add it
                    session_info = self.get_session_info(session_name)
                    if session_info:
                        current_sessions[session_name] = session_info
            
            self.sessions = current_sessions
            return list(self.sessions.values())
            
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to list tmux sessions: {e}")
            return []
    
    def get_session_info(self, session_name: str) -> Optional[TmuxSession]:
        """Get detailed information about a tmux session"""
        try:
            # Get session info
            result = self._run_tmux_command([
                'list-sessions', '-t', session_name,
                '-F', '#{session_name}|#{session_created}|#{session_path}|#{session_windows}'
            ])
            
            if not result.stdout.strip():
                return None
            
            parts = result.stdout.strip().split('|')
            name = parts[0]
            created_timestamp = int(parts[1])
            working_dir = parts[2]
            window_count = int(parts[3])
            
            # Extract task_id from session name
            task_id = name[len(TMUX_SESSION_PREFIX):]
            
            return TmuxSession(
                name=name,
                created_at=datetime.fromtimestamp(created_timestamp),
                working_directory=working_dir,
                agent_command="",  # Would need to capture this separately
                task_id=task_id,
                pane_count=window_count
            )
            
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to get session info for {session_name}: {e}")
            return None
    
    def capture_pane_output(self, session_name: str, lines: int = 100) -> str:
        """Capture output from a tmux pane"""
        try:
            result = self._run_tmux_command([
                'capture-pane', '-t', session_name, '-p', '-S', f'-{lines}'
            ])
            return result.stdout
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to capture pane output for {session_name}: {e}")
            return ""
    
    def send_keys(self, session_name: str, keys: str) -> bool:
        """Send keys to a tmux session"""
        try:
            self._run_tmux_command(['send-keys', '-t', session_name, keys])
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to send keys to {session_name}: {e}")
            return False
    
    def send_command(self, session_name: str, command: str) -> bool:
        """Send a command to a tmux session"""
        try:
            self._run_tmux_command(['send-keys', '-t', session_name, command, 'Enter'])
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to send command to {session_name}: {e}")
            return False
    
    def kill_session(self, session_name: str) -> bool:
        """Kill a tmux session"""
        try:
            self._run_tmux_command(['kill-session', '-t', session_name])
            if session_name in self.sessions:
                del self.sessions[session_name]
            logger.info(f"Killed tmux session {session_name}")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to kill session {session_name}: {e}")
            return False
    
    def attach_to_session(self, session_name: str) -> bool:
        """Attach to a tmux session (for interactive debugging)"""
        try:
            # This will take over the current terminal
            subprocess.run(['tmux', 'attach-session', '-t', session_name])
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to attach to session {session_name}: {e}")
            return False
    
    def is_session_active(self, session_name: str) -> bool:
        """Check if a session is actively running (not idle)"""
        try:
            # Check if session exists
            if not self.session_exists(session_name):
                return False
            
            # Capture recent output to check for activity
            recent_output = self.capture_pane_output(session_name, lines=5)
            
            # Simple heuristic: if there's a prompt or active content, it's active
            # You might want to make this more sophisticated
            active_indicators = ['$', '>', 'waiting', 'processing', 'error', 'warning']
            
            return any(indicator in recent_output.lower() for indicator in active_indicators)
            
        except Exception as e:
            logger.error(f"Error checking session activity for {session_name}: {e}")
            return False
    
    def get_session_status(self, session_name: str) -> Dict:
        """Get comprehensive status of a tmux session"""
        if not self.session_exists(session_name):
            return {'status': 'not_found'}
        
        try:
            # Get session info
            info_result = self._run_tmux_command([
                'list-sessions', '-t', session_name,
                '-F', '#{session_name}|#{session_created}|#{session_last_attached}|#{session_windows}'
            ])
            
            # Get pane info
            pane_result = self._run_tmux_command([
                'list-panes', '-t', session_name,
                '-F', '#{pane_id}|#{pane_current_command}|#{pane_active}'
            ])
            
            # Parse info
            info_parts = info_result.stdout.strip().split('|')
            created_time = datetime.fromtimestamp(int(info_parts[1]))
            last_attached = int(info_parts[2]) if info_parts[2] else 0
            window_count = int(info_parts[3])
            
            # Parse pane info
            panes = []
            for line in pane_result.stdout.strip().split('\n'):
                if line:
                    pane_parts = line.split('|')
                    panes.append({
                        'id': pane_parts[0],
                        'command': pane_parts[1],
                        'active': pane_parts[2] == '1'
                    })
            
            # Capture recent output
            recent_output = self.capture_pane_output(session_name, lines=20)
            
            return {
                'status': 'running',
                'created_at': created_time,
                'last_attached': datetime.fromtimestamp(last_attached) if last_attached else None,
                'window_count': window_count,
                'panes': panes,
                'recent_output': recent_output,
                'is_active': self.is_session_active(session_name)
            }
            
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to get session status for {session_name}: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def monitor_session(self, session_name: str) -> Dict:
        """Monitor a session for health and progress indicators"""
        status = self.get_session_status(session_name)
        
        if status.get('status') != 'running':
            return status
        
        recent_output = status.get('recent_output', '')
        
        # Analyze output for health indicators
        health_indicators = {
            'errors': len([line for line in recent_output.split('\n') if 'error' in line.lower()]),
            'warnings': len([line for line in recent_output.split('\n') if 'warning' in line.lower()]),
            'success_messages': len([line for line in recent_output.split('\n') if any(word in line.lower() for word in ['success', 'complete', 'done'])]),
            'is_stuck': 'waiting' in recent_output.lower() and recent_output.count('\n') < 3,
            'is_idle': not status.get('is_active', False)
        }
        
        # Determine overall health
        if health_indicators['errors'] > 5:
            health = 'critical'
        elif health_indicators['is_stuck']:
            health = 'intervention_needed'
        elif health_indicators['is_idle']:
            health = 'idle'
        elif health_indicators['success_messages'] > 0:
            health = 'good'
        else:
            health = 'running'
        
        status['health'] = health
        status['health_indicators'] = health_indicators
        
        return status
    
    def cleanup_dead_sessions(self) -> List[str]:
        """Clean up sessions that are no longer running"""
        cleaned_sessions = []
        
        for session_name in list(self.sessions.keys()):
            if not self.session_exists(session_name):
                del self.sessions[session_name]
                cleaned_sessions.append(session_name)
                logger.info(f"Cleaned up dead session tracking for {session_name}")
        
        return cleaned_sessions
    
    def save_session_state(self, file_path: Path):
        """Save current session state to file"""
        state = {
            'sessions': {name: session.to_dict() for name, session in self.sessions.items()},
            'saved_at': datetime.now().isoformat()
        }
        
        with open(file_path, 'w') as f:
            json.dump(state, f, indent=2)
    
    def load_session_state(self, file_path: Path):
        """Load session state from file"""
        if not file_path.exists():
            return
        
        try:
            with open(file_path, 'r') as f:
                state = json.load(f)
            
            for session_data in state.get('sessions', {}).values():
                session = TmuxSession.from_dict(session_data)
                if self.session_exists(session.name):
                    self.sessions[session.name] = session
                    
        except Exception as e:
            logger.error(f"Failed to load session state: {e}")
    
    def create_monitoring_session(self, agent_sessions: List[str]) -> str:
        """Create a tmux session that monitors all agent sessions"""
        monitor_session_name = f"{TMUX_SESSION_PREFIX}monitor"
        
        # Kill existing monitor session
        if self.session_exists(monitor_session_name):
            self.kill_session(monitor_session_name)
        
        try:
            # Create monitor session
            self._run_tmux_command([
                'new-session', '-d', '-s', monitor_session_name
            ])
            
            # Split window for each agent session (up to 4)
            for i, agent_session in enumerate(agent_sessions[:4]):
                if i > 0:
                    if i <= 2:
                        self._run_tmux_command([
                            'split-window', '-t', monitor_session_name, '-h'
                        ])
                    else:
                        self._run_tmux_command([
                            'split-window', '-t', monitor_session_name, '-v'
                        ])
                
                # Send command to monitor the agent session
                self._run_tmux_command([
                    'send-keys', '-t', f"{monitor_session_name}.{i}",
                    f'watch -n 5 "tmux capture-pane -t {agent_session} -p | tail -10"',
                    'Enter'
                ])
            
            # Arrange panes nicely
            if len(agent_sessions) > 1:
                self._run_tmux_command([
                    'select-layout', '-t', monitor_session_name, 'tiled'
                ])
            
            logger.info(f"Created monitoring session {monitor_session_name}")
            return monitor_session_name
            
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to create monitoring session: {e}")
            return ""