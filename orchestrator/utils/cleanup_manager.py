"""
Cleanup manager for the Agent Orchestrator
Handles cleanup of completed/failed tasks, old files, and resource management
"""

import logging
import shutil
from typing import List, Dict, Optional, Tuple
from datetime import datetime, timedelta
from pathlib import Path
from dataclasses import dataclass

from ..core.task_parser import TaskParser, TaskStatus
from ..core.git_manager import GitManager
from ..utils.tmux_manager import TmuxManager
from ..config.settings import (
    CLEANUP_RETENTION_DAYS, MAX_WORKTREES, MAX_DISK_USAGE_GB,
    CLEANUP_LOG_RETENTION_DAYS
)

logger = logging.getLogger(__name__)

@dataclass
class CleanupStats:
    """Statistics from cleanup operations"""
    worktrees_removed: int = 0
    tmux_sessions_killed: int = 0
    branches_deleted: int = 0
    files_cleaned: int = 0
    disk_space_freed_mb: float = 0.0
    tasks_archived: int = 0
    logs_cleaned: int = 0
    
    def to_dict(self) -> dict:
        return {
            'worktrees_removed': self.worktrees_removed,
            'tmux_sessions_killed': self.tmux_sessions_killed,
            'branches_deleted': self.branches_deleted,
            'files_cleaned': self.files_cleaned,
            'disk_space_freed_mb': self.disk_space_freed_mb,
            'tasks_archived': self.tasks_archived,
            'logs_cleaned': self.logs_cleaned,
            'total_resources_cleaned': (
                self.worktrees_removed + self.tmux_sessions_killed + 
                self.branches_deleted + self.files_cleaned
            )
        }


class CleanupManager:
    """Manages cleanup operations for the orchestrator"""
    
    def __init__(self, project_root: Path = None):
        self.task_parser = TaskParser()
        self.git_manager = GitManager(project_root)
        self.tmux_manager = TmuxManager()
        self.project_root = project_root or Path.cwd()
        
        # Cleanup tracking
        self.last_cleanup = None
        self.cleanup_history: List[Dict] = []
    
    def cleanup_completed_tasks(self, max_age_days: int = CLEANUP_RETENTION_DAYS) -> CleanupStats:
        """Clean up resources from completed tasks older than specified days"""
        logger.info(f"Starting cleanup of completed tasks older than {max_age_days} days")
        
        stats = CleanupStats()
        cutoff_date = datetime.now() - timedelta(days=max_age_days)
        
        # Get all tasks
        tasks = self.task_parser.parse_tasks()
        completed_tasks = [
            task for task in tasks 
            if task.status == TaskStatus.COMPLETED and 
            task.completed_at and 
            task.completed_at < cutoff_date
        ]
        
        logger.info(f"Found {len(completed_tasks)} completed tasks to clean up")
        
        for task in completed_tasks:
            try:
                task_stats = self._cleanup_single_task(task)
                stats.worktrees_removed += task_stats.worktrees_removed
                stats.tmux_sessions_killed += task_stats.tmux_sessions_killed
                stats.branches_deleted += task_stats.branches_deleted
                stats.disk_space_freed_mb += task_stats.disk_space_freed_mb
                stats.tasks_archived += 1
                
                logger.info(f"Cleaned up completed task {task.id}")
                
            except Exception as e:
                logger.error(f"Error cleaning up task {task.id}: {e}")
        
        return stats
    
    def cleanup_failed_tasks(self, max_age_days: int = 7) -> CleanupStats:
        """Clean up resources from failed tasks"""
        logger.info(f"Starting cleanup of failed tasks older than {max_age_days} days")
        
        stats = CleanupStats()
        cutoff_date = datetime.now() - timedelta(days=max_age_days)
        
        # Get failed tasks
        tasks = self.task_parser.parse_tasks()
        failed_tasks = [
            task for task in tasks 
            if task.status == TaskStatus.FAILED and 
            task.updated_at and 
            task.updated_at < cutoff_date
        ]
        
        logger.info(f"Found {len(failed_tasks)} failed tasks to clean up")
        
        for task in failed_tasks:
            try:
                task_stats = self._cleanup_single_task(task, preserve_branch=False)
                stats.worktrees_removed += task_stats.worktrees_removed
                stats.tmux_sessions_killed += task_stats.tmux_sessions_killed
                stats.branches_deleted += task_stats.branches_deleted
                stats.disk_space_freed_mb += task_stats.disk_space_freed_mb
                stats.tasks_archived += 1
                
                # Mark task as archived
                self.task_parser.update_task_status(task.id, TaskStatus.ARCHIVED)
                
                logger.info(f"Cleaned up failed task {task.id}")
                
            except Exception as e:
                logger.error(f"Error cleaning up failed task {task.id}: {e}")
        
        return stats
    
    def cleanup_orphaned_resources(self) -> CleanupStats:
        """Clean up orphaned worktrees and tmux sessions without corresponding tasks"""
        logger.info("Starting cleanup of orphaned resources")
        
        stats = CleanupStats()
        
        # Get all current tasks
        tasks = self.task_parser.parse_tasks()
        active_task_ids = {task.id for task in tasks if task.status in [
            TaskStatus.CLAIMED, TaskStatus.IN_PROGRESS
        ]}
        
        # Clean up orphaned worktrees
        worktrees = self.git_manager.list_worktrees()
        for worktree in worktrees:
            # Extract task ID from worktree name
            if worktree.name.startswith('task-'):
                task_id = worktree.name[5:]  # Remove 'task-' prefix
                
                if task_id not in active_task_ids:
                    size_before = self._get_directory_size(worktree.path)
                    
                    if self.git_manager.remove_worktree(worktree.path):
                        stats.worktrees_removed += 1
                        stats.disk_space_freed_mb += size_before / (1024 * 1024)
                        logger.info(f"Removed orphaned worktree: {worktree.name}")
        
        # Clean up orphaned tmux sessions
        sessions = self.tmux_manager.list_sessions()
        for session in sessions:
            if session.name.startswith('agent-'):
                # Extract task ID from session name
                task_id = session.task_id
                
                if task_id not in active_task_ids:
                    if self.tmux_manager.kill_session(session.name):
                        stats.tmux_sessions_killed += 1
                        logger.info(f"Killed orphaned tmux session: {session.name}")
        
        return stats
    
    def cleanup_disk_space(self, target_free_space_gb: float = 5.0) -> CleanupStats:
        """Clean up resources to free disk space"""
        logger.info(f"Starting cleanup to free {target_free_space_gb}GB of disk space")
        
        stats = CleanupStats()
        
        # Check current disk usage
        total_usage = self._get_total_disk_usage()
        if total_usage < target_free_space_gb * 1024:  # Convert GB to MB
            logger.info("Sufficient disk space available, no cleanup needed")
            return stats
        
        # Clean up in order of priority:
        # 1. Completed tasks (oldest first)
        # 2. Failed tasks
        # 3. Temporary files
        
        tasks = self.task_parser.parse_tasks()
        
        # Sort completed tasks by completion date (oldest first)
        completed_tasks = [
            task for task in tasks 
            if task.status == TaskStatus.COMPLETED and task.completed_at
        ]
        completed_tasks.sort(key=lambda t: t.completed_at)
        
        target_freed_mb = target_free_space_gb * 1024
        
        for task in completed_tasks:
            if stats.disk_space_freed_mb >= target_freed_mb:
                break
                
            task_stats = self._cleanup_single_task(task)
            stats.worktrees_removed += task_stats.worktrees_removed
            stats.disk_space_freed_mb += task_stats.disk_space_freed_mb
            stats.tasks_archived += 1
        
        # Clean up failed tasks if needed
        if stats.disk_space_freed_mb < target_freed_mb:
            failed_stats = self.cleanup_failed_tasks(max_age_days=1)
            stats.worktrees_removed += failed_stats.worktrees_removed
            stats.disk_space_freed_mb += failed_stats.disk_space_freed_mb
            stats.tasks_archived += failed_stats.tasks_archived
        
        logger.info(f"Freed {stats.disk_space_freed_mb:.1f}MB of disk space")
        return stats
    
    def cleanup_logs(self, max_age_days: int = CLEANUP_LOG_RETENTION_DAYS) -> CleanupStats:
        """Clean up old log files"""
        logger.info(f"Starting cleanup of log files older than {max_age_days} days")
        
        stats = CleanupStats()
        cutoff_date = datetime.now() - timedelta(days=max_age_days)
        
        # Find log directories
        log_dirs = [
            self.project_root / 'logs',
            self.project_root / 'orchestrator' / 'logs',
            Path('/tmp/orchestrator-logs')
        ]
        
        for log_dir in log_dirs:
            if not log_dir.exists():
                continue
                
            for log_file in log_dir.rglob('*.log'):
                try:
                    file_mtime = datetime.fromtimestamp(log_file.stat().st_mtime)
                    if file_mtime < cutoff_date:
                        file_size = log_file.stat().st_size
                        log_file.unlink()
                        stats.files_cleaned += 1
                        stats.disk_space_freed_mb += file_size / (1024 * 1024)
                        logger.debug(f"Removed old log file: {log_file}")
                        
                except Exception as e:
                    logger.error(f"Error removing log file {log_file}: {e}")
        
        logger.info(f"Cleaned up {stats.files_cleaned} log files")
        return stats
    
    def force_cleanup_all(self) -> CleanupStats:
        """Force cleanup of all non-active resources"""
        logger.warning("Starting FORCE cleanup of all non-active resources")
        
        stats = CleanupStats()
        
        # Get active tasks
        tasks = self.task_parser.parse_tasks()
        active_task_ids = {
            task.id for task in tasks 
            if task.status in [TaskStatus.CLAIMED, TaskStatus.IN_PROGRESS]
        }
        
        # Clean up all non-active worktrees
        worktrees = self.git_manager.list_worktrees()
        for worktree in worktrees:
            if worktree.name.startswith('task-'):
                task_id = worktree.name[5:]
                if task_id not in active_task_ids:
                    size_before = self._get_directory_size(worktree.path)
                    if self.git_manager.remove_worktree(worktree.path):
                        stats.worktrees_removed += 1
                        stats.disk_space_freed_mb += size_before / (1024 * 1024)
        
        # Clean up all non-active tmux sessions
        sessions = self.tmux_manager.list_sessions()
        for session in sessions:
            if session.name.startswith('agent-'):
                task_id = session.task_id
                if task_id not in active_task_ids:
                    if self.tmux_manager.kill_session(session.name):
                        stats.tmux_sessions_killed += 1
        
        # Clean up all logs
        log_stats = self.cleanup_logs(max_age_days=0)  # Clean all logs
        stats.files_cleaned += log_stats.files_cleaned
        stats.disk_space_freed_mb += log_stats.disk_space_freed_mb
        
        logger.warning(f"Force cleanup completed: {stats.to_dict()}")
        return stats
    
    def _cleanup_single_task(self, task, preserve_branch: bool = True) -> CleanupStats:
        """Clean up resources for a single task"""
        stats = CleanupStats()
        
        # Remove worktree
        worktree_name = f"task-{task.id}"
        worktree_path = self.git_manager.worktrees_dir / worktree_name
        
        if worktree_path.exists():
            size_before = self._get_directory_size(worktree_path)
            
            if self.git_manager.remove_worktree(worktree_path):
                stats.worktrees_removed += 1
                stats.disk_space_freed_mb += size_before / (1024 * 1024)
        
        # Kill tmux session
        session_name = f"agent-{task.id}"
        if self.tmux_manager.session_exists(session_name):
            if self.tmux_manager.kill_session(session_name):
                stats.tmux_sessions_killed += 1
        
        # Optionally delete branch
        if not preserve_branch and task.branch:
            if self.git_manager.delete_branch(task.branch, force=True):
                stats.branches_deleted += 1
        
        return stats
    
    def _get_directory_size(self, path: Path) -> int:
        """Get total size of directory in bytes"""
        if not path.exists():
            return 0
            
        total_size = 0
        try:
            for item in path.rglob('*'):
                if item.is_file():
                    total_size += item.stat().st_size
        except Exception as e:
            logger.error(f"Error calculating directory size for {path}: {e}")
        
        return total_size
    
    def _get_total_disk_usage(self) -> float:
        """Get total disk usage by orchestrator in MB"""
        total_size = 0
        
        # Check worktrees directory
        if self.git_manager.worktrees_dir.exists():
            total_size += self._get_directory_size(self.git_manager.worktrees_dir)
        
        # Check logs directory
        logs_dir = self.project_root / 'logs'
        if logs_dir.exists():
            total_size += self._get_directory_size(logs_dir)
        
        return total_size / (1024 * 1024)  # Convert to MB
    
    def schedule_automatic_cleanup(self) -> Dict:
        """Schedule and perform automatic cleanup based on configured thresholds"""
        logger.info("Starting scheduled automatic cleanup")
        
        total_stats = CleanupStats()
        operations_performed = []
        
        # Check disk usage
        current_usage_mb = self._get_total_disk_usage()
        max_usage_mb = MAX_DISK_USAGE_GB * 1024
        
        if current_usage_mb > max_usage_mb:
            logger.warning(f"Disk usage ({current_usage_mb:.1f}MB) exceeds limit ({max_usage_mb}MB)")
            disk_stats = self.cleanup_disk_space(target_free_space_gb=1.0)
            total_stats.disk_space_freed_mb += disk_stats.disk_space_freed_mb
            total_stats.worktrees_removed += disk_stats.worktrees_removed
            operations_performed.append("disk_space_cleanup")
        
        # Check worktree count
        worktree_count = len(self.git_manager.list_worktrees())
        if worktree_count > MAX_WORKTREES:
            logger.warning(f"Worktree count ({worktree_count}) exceeds limit ({MAX_WORKTREES})")
            completed_stats = self.cleanup_completed_tasks(max_age_days=7)
            total_stats.worktrees_removed += completed_stats.worktrees_removed
            total_stats.tasks_archived += completed_stats.tasks_archived
            operations_performed.append("worktree_limit_cleanup")
        
        # Regular maintenance cleanup
        if not self.last_cleanup or (datetime.now() - self.last_cleanup).days >= 1:
            # Daily cleanup of old completed tasks
            daily_stats = self.cleanup_completed_tasks()
            total_stats.worktrees_removed += daily_stats.worktrees_removed
            total_stats.tasks_archived += daily_stats.tasks_archived
            
            # Clean up orphaned resources
            orphan_stats = self.cleanup_orphaned_resources()
            total_stats.worktrees_removed += orphan_stats.worktrees_removed
            total_stats.tmux_sessions_killed += orphan_stats.tmux_sessions_killed
            
            # Clean up old logs
            log_stats = self.cleanup_logs()
            total_stats.files_cleaned += log_stats.files_cleaned
            total_stats.disk_space_freed_mb += log_stats.disk_space_freed_mb
            
            operations_performed.extend(["daily_cleanup", "orphan_cleanup", "log_cleanup"])
            self.last_cleanup = datetime.now()
        
        # Record cleanup history
        cleanup_record = {
            'timestamp': datetime.now().isoformat(),
            'operations': operations_performed,
            'stats': total_stats.to_dict()
        }
        self.cleanup_history.append(cleanup_record)
        
        # Keep only last 30 cleanup records
        if len(self.cleanup_history) > 30:
            self.cleanup_history = self.cleanup_history[-30:]
        
        logger.info(f"Automatic cleanup completed: {total_stats.to_dict()}")
        return cleanup_record
    
    def get_cleanup_recommendations(self) -> List[Dict]:
        """Get recommendations for manual cleanup actions"""
        recommendations = []
        
        # Check disk usage
        current_usage_mb = self._get_total_disk_usage()
        if current_usage_mb > 1000:  # 1GB
            recommendations.append({
                'type': 'disk_usage',
                'severity': 'warning',
                'message': f"High disk usage: {current_usage_mb:.1f}MB",
                'action': 'Run cleanup_disk_space() or cleanup_completed_tasks()',
                'potential_savings_mb': current_usage_mb * 0.5
            })
        
        # Check worktree count
        worktree_count = len(self.git_manager.list_worktrees())
        if worktree_count > 20:
            recommendations.append({
                'type': 'worktree_count',
                'severity': 'info',
                'message': f"High worktree count: {worktree_count}",
                'action': 'Run cleanup_completed_tasks() or cleanup_orphaned_resources()',
                'potential_savings_count': worktree_count // 2
            })
        
        # Check for old completed tasks
        tasks = self.task_parser.parse_tasks()
        old_completed = [
            task for task in tasks 
            if task.status == TaskStatus.COMPLETED and 
            task.completed_at and 
            (datetime.now() - task.completed_at).days > 7
        ]
        
        if old_completed:
            recommendations.append({
                'type': 'old_completed_tasks',
                'severity': 'info',
                'message': f"{len(old_completed)} completed tasks older than 7 days",
                'action': 'Run cleanup_completed_tasks()',
                'potential_savings_count': len(old_completed)
            })
        
        # Check for failed tasks
        failed_tasks = [task for task in tasks if task.status == TaskStatus.FAILED]
        if failed_tasks:
            recommendations.append({
                'type': 'failed_tasks',
                'severity': 'warning',
                'message': f"{len(failed_tasks)} failed tasks found",
                'action': 'Review and run cleanup_failed_tasks()',
                'potential_savings_count': len(failed_tasks)
            })
        
        return recommendations
    
    def generate_cleanup_report(self) -> str:
        """Generate a human-readable cleanup report"""
        current_usage = self._get_total_disk_usage()
        worktree_count = len(self.git_manager.list_worktrees())
        session_count = len(self.tmux_manager.list_sessions())
        
        recommendations = self.get_cleanup_recommendations()
        
        report_lines = [
            "=" * 60,
            "ORCHESTRATOR CLEANUP REPORT",
            "=" * 60,
            f"Current Disk Usage: {current_usage:.1f}MB",
            f"Active Worktrees: {worktree_count}",
            f"Active Tmux Sessions: {session_count}",
            f"Last Cleanup: {self.last_cleanup.isoformat() if self.last_cleanup else 'Never'}",
            "",
        ]
        
        if recommendations:
            report_lines.append("RECOMMENDATIONS:")
            for rec in recommendations:
                severity_marker = "⚠️" if rec['severity'] == 'warning' else "ℹ️"
                report_lines.append(f"{severity_marker} {rec['message']}")
                report_lines.append(f"   Action: {rec['action']}")
            report_lines.append("")
        else:
            report_lines.append("✅ No cleanup recommendations at this time")
            report_lines.append("")
        
        if self.cleanup_history:
            report_lines.append("RECENT CLEANUP HISTORY:")
            for record in self.cleanup_history[-5:]:  # Last 5 cleanups
                timestamp = datetime.fromisoformat(record['timestamp'])
                stats = record['stats']
                report_lines.append(f"  {timestamp.strftime('%Y-%m-%d %H:%M')} - "
                                  f"Freed {stats['disk_space_freed_mb']:.1f}MB, "
                                  f"Removed {stats['total_resources_cleaned']} resources")
        
        report_lines.append("=" * 60)
        
        return "\n".join(report_lines)