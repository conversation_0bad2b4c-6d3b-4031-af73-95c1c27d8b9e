"""
Status monitoring system for the Agent Orchestrator
Provides real-time monitoring, alerts, and health checks
"""

import logging
import time
import threading
from typing import Dict, List, Callable, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass
from pathlib import Path
import json

from ..core.agent_spawner import AgentSpawner
from ..config.settings import STATUS_CHECK_INTERVAL, MAX_IDLE_TIME

logger = logging.getLogger(__name__)

@dataclass
class Alert:
    """Represents a monitoring alert"""
    id: str
    level: str  # info, warning, error, critical
    message: str
    agent_id: Optional[str]
    timestamp: datetime
    acknowledged: bool = False
    resolved: bool = False
    
    def to_dict(self) -> dict:
        return {
            'id': self.id,
            'level': self.level,
            'message': self.message,
            'agent_id': self.agent_id,
            'timestamp': self.timestamp.isoformat(),
            'acknowledged': self.acknowledged,
            'resolved': self.resolved
        }


class StatusMonitor:
    """Monitors agent health and orchestrator status"""
    
    def __init__(self, agent_spawner: AgentSpawner):
        self.agent_spawner = agent_spawner
        self.alerts: List[Alert] = []
        self.metrics: Dict = {}
        self.monitoring_active = False
        self.monitor_thread: Optional[threading.Thread] = None
        self.alert_callbacks: List[Callable] = []
        
        # Initialize metrics
        self._initialize_metrics()
    
    def _initialize_metrics(self):
        """Initialize monitoring metrics"""
        self.metrics = {
            'orchestrator_start_time': datetime.now(),
            'total_agents_spawned': 0,
            'total_tasks_completed': 0,
            'total_tasks_failed': 0,
            'total_alerts_generated': 0,
            'current_active_agents': 0,
            'average_task_completion_time': 0,
            'health_check_intervals': [],
            'resource_usage': {
                'worktrees': 0,
                'tmux_sessions': 0,
                'disk_usage_mb': 0
            }
        }
    
    def start_monitoring(self, interval: int = STATUS_CHECK_INTERVAL):
        """Start continuous monitoring in a background thread"""
        if self.monitoring_active:
            logger.warning("Monitoring is already active")
            return
        
        self.monitoring_active = True
        self.monitor_thread = threading.Thread(
            target=self._monitoring_loop,
            args=(interval,),
            daemon=True
        )
        self.monitor_thread.start()
        logger.info(f"Started monitoring with {interval}s interval")
    
    def stop_monitoring(self):
        """Stop continuous monitoring"""
        self.monitoring_active = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        logger.info("Stopped monitoring")
    
    def _monitoring_loop(self, interval: int):
        """Main monitoring loop"""
        while self.monitoring_active:
            try:
                check_start = time.time()
                
                # Perform health checks
                self._check_agent_health()
                self._check_resource_usage()
                self._check_orchestrator_health()
                self._update_metrics()
                
                check_duration = time.time() - check_start
                self.metrics['health_check_intervals'].append(check_duration)
                
                # Keep only last 100 check intervals
                if len(self.metrics['health_check_intervals']) > 100:
                    self.metrics['health_check_intervals'] = \
                        self.metrics['health_check_intervals'][-100:]
                
                # Sleep for the remaining interval
                sleep_time = max(0, interval - check_duration)
                time.sleep(sleep_time)
                
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                time.sleep(interval)
    
    def _check_agent_health(self):
        """Check health of all active agents"""
        agent_health = self.agent_spawner.monitor_agents()
        
        for agent_id, health in agent_health.items():
            if health == 'critical':
                self._create_alert(
                    level='critical',
                    message=f"Agent {agent_id} is in critical state",
                    agent_id=agent_id
                )
            elif health == 'stuck':
                self._create_alert(
                    level='warning',
                    message=f"Agent {agent_id} appears to be stuck",
                    agent_id=agent_id
                )
                # Attempt automatic intervention
                self.agent_spawner.handle_stuck_agent(agent_id)
            elif health == 'idle_too_long':
                self._create_alert(
                    level='warning',
                    message=f"Agent {agent_id} has been idle for too long",
                    agent_id=agent_id
                )
            elif health == 'error_prone':
                self._create_alert(
                    level='error',
                    message=f"Agent {agent_id} has encountered multiple errors",
                    agent_id=agent_id
                )
    
    def _check_resource_usage(self):
        """Check system resource usage"""
        try:
            # Check worktree count
            worktrees = self.agent_spawner.git_manager.list_worktrees()
            self.metrics['resource_usage']['worktrees'] = len(worktrees)
            
            # Check tmux sessions
            sessions = self.agent_spawner.tmux_manager.list_sessions()
            self.metrics['resource_usage']['tmux_sessions'] = len(sessions)
            
            # Check disk usage of worktrees
            total_size = 0
            worktrees_dir = self.agent_spawner.git_manager.worktrees_dir
            if worktrees_dir.exists():
                for item in worktrees_dir.rglob('*'):
                    if item.is_file():
                        total_size += item.stat().st_size
            
            self.metrics['resource_usage']['disk_usage_mb'] = total_size / (1024 * 1024)
            
            # Alert on high resource usage
            if len(worktrees) > 10:
                self._create_alert(
                    level='warning',
                    message=f"High number of worktrees: {len(worktrees)}",
                    agent_id=None
                )
            
            if total_size > 1024 * 1024 * 1024:  # 1GB
                self._create_alert(
                    level='warning',
                    message=f"High disk usage: {total_size / (1024*1024*1024):.2f}GB",
                    agent_id=None
                )
            
        except Exception as e:
            logger.error(f"Error checking resource usage: {e}")
    
    def _check_orchestrator_health(self):
        """Check overall orchestrator health"""
        try:
            status = self.agent_spawner.get_orchestrator_status()
            
            # Check for failed tasks
            failed_tasks = status['task_summary'].get('failed', 0)
            if failed_tasks > 0:
                self._create_alert(
                    level='error',
                    message=f"{failed_tasks} tasks have failed",
                    agent_id=None
                )
            
            # Check for tasks requiring intervention
            intervention_tasks = status['task_summary'].get('intervention_required', 0)
            if intervention_tasks > 0:
                self._create_alert(
                    level='warning',
                    message=f"{intervention_tasks} tasks require intervention",
                    agent_id=None
                )
            
            # Check for dead agents
            if status.get('dead_agents_cleaned', 0) > 0:
                self._create_alert(
                    level='warning',
                    message=f"Cleaned up {status['dead_agents_cleaned']} dead agents",
                    agent_id=None
                )
            
        except Exception as e:
            logger.error(f"Error checking orchestrator health: {e}")
    
    def _update_metrics(self):
        """Update orchestrator metrics"""
        try:
            status = self.agent_spawner.get_orchestrator_status()
            
            self.metrics['current_active_agents'] = status['active_agents']
            self.metrics['total_tasks_completed'] = status['task_summary'].get('completed', 0)
            self.metrics['total_tasks_failed'] = status['task_summary'].get('failed', 0)
            
            # Calculate completion time average (simplified)
            completed_agents = self.agent_spawner.completed_agents
            if completed_agents:
                total_time = sum([
                    (datetime.now() - agent.created_at).total_seconds()
                    for agent in completed_agents
                    if agent.status == 'completed'
                ])
                if completed_agents:
                    self.metrics['average_task_completion_time'] = \
                        total_time / len(completed_agents)
            
        except Exception as e:
            logger.error(f"Error updating metrics: {e}")
    
    def _create_alert(self, level: str, message: str, agent_id: Optional[str] = None):
        """Create a new alert"""
        # Check for duplicate alerts
        for alert in self.alerts:
            if (alert.message == message and 
                alert.agent_id == agent_id and 
                not alert.resolved and
                (datetime.now() - alert.timestamp).total_seconds() < 300):  # 5 minutes
                return  # Duplicate alert within 5 minutes
        
        alert = Alert(
            id=f"alert-{len(self.alerts)}",
            level=level,
            message=message,
            agent_id=agent_id,
            timestamp=datetime.now()
        )
        
        self.alerts.append(alert)
        self.metrics['total_alerts_generated'] += 1
        
        logger.log(
            getattr(logging, level.upper(), logging.INFO),
            f"Alert: {message} (Agent: {agent_id})"
        )
        
        # Trigger alert callbacks
        for callback in self.alert_callbacks:
            try:
                callback(alert)
            except Exception as e:
                logger.error(f"Error in alert callback: {e}")
    
    def get_status_summary(self) -> Dict:
        """Get comprehensive status summary"""
        orchestrator_status = self.agent_spawner.get_orchestrator_status()
        
        # Get recent alerts
        recent_alerts = [
            alert for alert in self.alerts
            if (datetime.now() - alert.timestamp).total_seconds() < 3600  # Last hour
        ]
        
        # Alert summary by level
        alert_summary = {
            'critical': len([a for a in recent_alerts if a.level == 'critical']),
            'error': len([a for a in recent_alerts if a.level == 'error']),
            'warning': len([a for a in recent_alerts if a.level == 'warning']),
            'info': len([a for a in recent_alerts if a.level == 'info'])
        }
        
        return {
            'orchestrator': orchestrator_status,
            'monitoring': {
                'active': self.monitoring_active,
                'uptime_hours': (datetime.now() - self.metrics['orchestrator_start_time']).total_seconds() / 3600,
                'health_check_avg_time': sum(self.metrics['health_check_intervals']) / max(1, len(self.metrics['health_check_intervals'])),
                'total_alerts': len(self.alerts),
                'recent_alerts': alert_summary
            },
            'metrics': self.metrics,
            'alerts': [alert.to_dict() for alert in recent_alerts],
            'resource_usage': self.metrics['resource_usage']
        }
    
    def get_agent_detailed_status(self, agent_id: str) -> Dict:
        """Get detailed status for a specific agent"""
        base_status = self.agent_spawner.get_agent_status(agent_id)
        
        if base_status.get('status') == 'not_found':
            return base_status
        
        # Add monitoring-specific information
        agent_alerts = [
            alert for alert in self.alerts
            if alert.agent_id == agent_id and
            (datetime.now() - alert.timestamp).total_seconds() < 3600
        ]
        
        return {
            **base_status,
            'monitoring': {
                'recent_alerts': [alert.to_dict() for alert in agent_alerts],
                'alert_count': len(agent_alerts),
                'last_health_check': datetime.now()
            }
        }
    
    def acknowledge_alert(self, alert_id: str) -> bool:
        """Acknowledge an alert"""
        for alert in self.alerts:
            if alert.id == alert_id:
                alert.acknowledged = True
                logger.info(f"Alert {alert_id} acknowledged")
                return True
        return False
    
    def resolve_alert(self, alert_id: str) -> bool:
        """Mark an alert as resolved"""
        for alert in self.alerts:
            if alert.id == alert_id:
                alert.resolved = True
                alert.acknowledged = True
                logger.info(f"Alert {alert_id} resolved")
                return True
        return False
    
    def add_alert_callback(self, callback: Callable[[Alert], None]):
        """Add a callback function to be called when alerts are created"""
        self.alert_callbacks.append(callback)
    
    def export_metrics(self, file_path: Path):
        """Export metrics to a JSON file"""
        export_data = {
            'timestamp': datetime.now().isoformat(),
            'status_summary': self.get_status_summary(),
            'all_alerts': [alert.to_dict() for alert in self.alerts],
            'metrics_history': self.metrics
        }
        
        with open(file_path, 'w') as f:
            json.dump(export_data, f, indent=2)
        
        logger.info(f"Metrics exported to {file_path}")
    
    def cleanup_old_alerts(self, max_age_hours: int = 24):
        """Clean up alerts older than specified hours"""
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
        initial_count = len(self.alerts)
        
        self.alerts = [
            alert for alert in self.alerts
            if alert.timestamp > cutoff_time or not alert.resolved
        ]
        
        cleaned_count = initial_count - len(self.alerts)
        if cleaned_count > 0:
            logger.info(f"Cleaned up {cleaned_count} old alerts")
        
        return cleaned_count
    
    def generate_health_report(self) -> str:
        """Generate a human-readable health report"""
        status = self.get_status_summary()
        
        uptime = status['monitoring']['uptime_hours']
        active_agents = status['orchestrator']['active_agents']
        completed_tasks = status['orchestrator']['task_summary']['completed']
        failed_tasks = status['orchestrator']['task_summary']['failed']
        
        recent_alerts = status['monitoring']['recent_alerts']
        critical_alerts = recent_alerts['critical']
        error_alerts = recent_alerts['error']
        warning_alerts = recent_alerts['warning']
        
        report_lines = [
            "=" * 60,
            "AGENT ORCHESTRATOR HEALTH REPORT",
            "=" * 60,
            f"Uptime: {uptime:.1f} hours",
            f"Active Agents: {active_agents}",
            f"Completed Tasks: {completed_tasks}",
            f"Failed Tasks: {failed_tasks}",
            "",
            "ALERTS (Last Hour):",
            f"  Critical: {critical_alerts}",
            f"  Error: {error_alerts}",
            f"  Warning: {warning_alerts}",
            "",
            "RESOURCE USAGE:",
            f"  Worktrees: {status['resource_usage']['worktrees']}",
            f"  Tmux Sessions: {status['resource_usage']['tmux_sessions']}",
            f"  Disk Usage: {status['resource_usage']['disk_usage_mb']:.1f} MB",
            "",
        ]
        
        # Add recent critical/error alerts
        if critical_alerts > 0 or error_alerts > 0:
            report_lines.append("RECENT CRITICAL/ERROR ALERTS:")
            for alert in status['alerts']:
                if alert['level'] in ['critical', 'error']:
                    report_lines.append(f"  [{alert['level'].upper()}] {alert['message']}")
            report_lines.append("")
        
        # Overall health assessment
        if critical_alerts > 0:
            health_status = "CRITICAL - Immediate attention required"
        elif error_alerts > 0:
            health_status = "DEGRADED - Errors detected"
        elif warning_alerts > 5:
            health_status = "WARNING - Multiple warnings"
        elif active_agents == 0 and completed_tasks == 0:
            health_status = "IDLE - No active work"
        else:
            health_status = "HEALTHY - Operating normally"
        
        report_lines.extend([
            f"OVERALL HEALTH: {health_status}",
            "=" * 60
        ])
        
        return "\n".join(report_lines)