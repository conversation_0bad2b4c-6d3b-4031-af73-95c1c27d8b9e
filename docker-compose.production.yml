# Production environment overrides for Docker Compose
# Includes SSL termination, security headers, and production optimizations
version: '3.8'

services:
  # Nginx Reverse Proxy with SSL
  nginx:
    image: nginx:alpine
    container_name: ecommerce-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./nginx/logs:/var/log/nginx
    depends_on:
      - frontend
      - dashboard-service
      - analytics-service
      - integration-service
    networks:
      - ecommerce-network
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Production overrides for services
  postgres:
    command: ["postgres", "-c", "shared_preload_libraries=pg_stat_statements", "-c", "pg_stat_statements.max=10000", "-c", "pg_stat_statements.track=all", "-c", "log_min_duration_statement=50", "-c", "track_io_timing=on", "-c", "max_connections=200", "-c", "work_mem=4MB", "-c", "maintenance_work_mem=64MB"]
    environment:
      POSTGRES_SHARED_PRELOAD_LIBRARIES: pg_stat_statements
      POSTGRES_MAX_CONNECTIONS: 200
      POSTGRES_WORK_MEM: 4MB
      POSTGRES_MAINTENANCE_WORK_MEM: 64MB

  redis:
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD} --maxmemory 1gb --maxmemory-policy allkeys-lru

  analytics-service:
    environment:
      NODE_ENV: production
      LOG_LEVEL: warn
      CORS_ORIGIN: ${CORS_ORIGIN}
      RATE_LIMIT_WINDOW: 900000
      RATE_LIMIT_MAX: 1000
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'

  dashboard-service:
    environment:
      NODE_ENV: production
      LOG_LEVEL: warn
      RATE_LIMIT_WINDOW: 900000
      RATE_LIMIT_MAX: 1000
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'

  integration-service:
    environment:
      NODE_ENV: production
      LOG_LEVEL: warn
      RATE_LIMIT_WINDOW: 900000
      RATE_LIMIT_MAX: 500
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'

  frontend:
    environment:
      NODE_ENV: production
      VITE_API_URL: ${VITE_API_URL}
      VITE_ANALYTICS_URL: ${VITE_ANALYTICS_URL}
      VITE_INTEGRATION_URL: ${VITE_INTEGRATION_URL}
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.25'
        reservations:
          memory: 256M
          cpus: '0.125'

volumes:
  ssl_certs:
    driver: local