/**
 * Advanced Performance Monitoring System
 * Real-time performance tracking, alerting, and optimization recommendations
 */

const EventEmitter = require('events');
const { performance, PerformanceObserver } = require('perf_hooks');
const v8 = require('v8');
const cluster = require('cluster');

class PerformanceMonitor extends EventEmitter {
  constructor(config = {}) {
    super();
    
    this.config = {
      alertThresholds: {
        responseTime: {
          warning: 1000,   // 1 second
          critical: 3000   // 3 seconds
        },
        memoryUsage: {
          warning: 80,     // 80% of heap limit
          critical: 90     // 90% of heap limit
        },
        cpuUsage: {
          warning: 70,     // 70% CPU usage
          critical: 85     // 85% CPU usage
        },
        errorRate: {
          warning: 5,      // 5% error rate
          critical: 10     // 10% error rate
        }
      },
      samplingInterval: 5000,  // 5 seconds
      retentionPeriod: 24 * 60 * 60 * 1000, // 24 hours
      enableDetailedMetrics: true,
      enableProfiling: process.env.NODE_ENV !== 'production',
      ...config
    };

    // Metrics storage
    this.metrics = {
      requests: new Map(),
      responses: new Map(),
      errors: new Map(),
      memory: [],
      cpu: [],
      gc: [],
      httpMetrics: {
        totalRequests: 0,
        totalErrors: 0,
        totalResponseTime: 0,
        statusCodes: new Map(),
        endpoints: new Map(),
        userAgents: new Map()
      }
    };

    // Performance observers
    this.observers = new Map();
    
    // Alert state
    this.alerts = {
      active: new Map(),
      history: []
    };

    // Initialize monitoring
    this.initialize();
  }

  /**
   * Initialize performance monitoring
   */
  initialize() {
    console.log('Initializing performance monitoring...');
    
    // Set up performance observers
    this.setupPerformanceObservers();
    
    // Start system metrics collection
    this.startSystemMetricsCollection();
    
    // Start garbage collection monitoring
    this.startGCMonitoring();
    
    // Start alert checking
    this.startAlertMonitoring();
    
    // Set up cleanup
    this.startCleanup();

    console.log('Performance monitoring initialized');
  }

  /**
   * Set up performance observers
   */
  setupPerformanceObservers() {
    if (!this.config.enableDetailedMetrics) return;

    try {
      // HTTP timing observer
      const httpObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.recordHttpTiming(entry);
        }
      });
      httpObserver.observe({ entryTypes: ['measure'] });
      this.observers.set('http', httpObserver);

      // Function timing observer
      const functionObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.recordFunctionTiming(entry);
        }
      });
      functionObserver.observe({ entryTypes: ['function'] });
      this.observers.set('function', functionObserver);

    } catch (error) {
      console.warn('Failed to set up performance observers:', error.message);
    }
  }

  /**
   * Record HTTP request timing
   */
  recordHttpTiming(entry) {
    const endpoint = entry.detail?.endpoint || 'unknown';
    const method = entry.detail?.method || 'unknown';
    const statusCode = entry.detail?.statusCode || 0;

    // Update endpoint metrics
    const endpointKey = `${method} ${endpoint}`;
    if (!this.metrics.httpMetrics.endpoints.has(endpointKey)) {
      this.metrics.httpMetrics.endpoints.set(endpointKey, {
        count: 0,
        totalTime: 0,
        avgTime: 0,
        minTime: Infinity,
        maxTime: 0,
        errors: 0
      });
    }

    const endpointMetric = this.metrics.httpMetrics.endpoints.get(endpointKey);
    endpointMetric.count++;
    endpointMetric.totalTime += entry.duration;
    endpointMetric.avgTime = endpointMetric.totalTime / endpointMetric.count;
    endpointMetric.minTime = Math.min(endpointMetric.minTime, entry.duration);
    endpointMetric.maxTime = Math.max(endpointMetric.maxTime, entry.duration);

    if (statusCode >= 400) {
      endpointMetric.errors++;
    }

    // Update status code metrics
    const statusKey = Math.floor(statusCode / 100) * 100;
    const currentCount = this.metrics.httpMetrics.statusCodes.get(statusKey) || 0;
    this.metrics.httpMetrics.statusCodes.set(statusKey, currentCount + 1);

    // Check for slow requests
    if (entry.duration > this.config.alertThresholds.responseTime.warning) {
      this.emit('slowRequest', {
        endpoint: endpointKey,
        duration: entry.duration,
        timestamp: Date.now()
      });
    }
  }

  /**
   * Record function timing
   */
  recordFunctionTiming(entry) {
    // Store function performance data
    const functionMetrics = {
      name: entry.name,
      duration: entry.duration,
      timestamp: Date.now()
    };

    // Emit event for slow functions
    if (entry.duration > 100) { // 100ms threshold
      this.emit('slowFunction', functionMetrics);
    }
  }

  /**
   * Start system metrics collection
   */
  startSystemMetricsCollection() {
    const collectMetrics = () => {
      const timestamp = Date.now();
      
      // Memory metrics
      const memoryUsage = process.memoryUsage();
      const memoryMetric = {
        timestamp,
        heapUsed: memoryUsage.heapUsed,
        heapTotal: memoryUsage.heapTotal,
        external: memoryUsage.external,
        rss: memoryUsage.rss,
        heapUsedPercent: (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100
      };
      
      this.metrics.memory.push(memoryMetric);
      
      // CPU metrics
      const cpuUsage = process.cpuUsage();
      const cpuMetric = {
        timestamp,
        user: cpuUsage.user,
        system: cpuUsage.system,
        total: cpuUsage.user + cpuUsage.system
      };
      
      this.metrics.cpu.push(cpuMetric);

      // V8 heap statistics
      if (this.config.enableDetailedMetrics) {
        const heapStats = v8.getHeapStatistics();
        memoryMetric.v8 = {
          totalHeapSize: heapStats.total_heap_size,
          totalHeapSizeExecutable: heapStats.total_heap_size_executable,
          totalPhysicalSize: heapStats.total_physical_size,
          totalAvailableSize: heapStats.total_available_size,
          usedHeapSize: heapStats.used_heap_size,
          heapSizeLimit: heapStats.heap_size_limit,
          mallocedMemory: heapStats.malloced_memory,
          peakMallocedMemory: heapStats.peak_malloced_memory
        };
      }

      // Emit metrics events
      this.emit('memoryMetric', memoryMetric);
      this.emit('cpuMetric', cpuMetric);

      // Check thresholds
      this.checkMemoryThresholds(memoryMetric);
      this.checkCPUThresholds(cpuMetric);
    };

    // Collect metrics immediately and then on interval
    collectMetrics();
    this.metricsInterval = setInterval(collectMetrics, this.config.samplingInterval);
  }

  /**
   * Start garbage collection monitoring
   */
  startGCMonitoring() {
    if (!this.config.enableDetailedMetrics) return;

    try {
      const gcObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          const gcMetric = {
            timestamp: Date.now(),
            kind: entry.detail?.kind || entry.kind,
            duration: entry.duration,
            flags: entry.detail?.flags || entry.flags
          };

          this.metrics.gc.push(gcMetric);
          this.emit('gcEvent', gcMetric);

          // Alert on long GC pauses
          if (entry.duration > 100) { // 100ms GC pause
            this.emit('longGC', gcMetric);
          }
        }
      });

      gcObserver.observe({ entryTypes: ['gc'] });
      this.observers.set('gc', gcObserver);
    } catch (error) {
      console.warn('GC monitoring not available:', error.message);
    }
  }

  /**
   * Start alert monitoring
   */
  startAlertMonitoring() {
    this.alertInterval = setInterval(() => {
      this.checkAlerts();
    }, this.config.samplingInterval);
  }

  /**
   * Check memory thresholds
   */
  checkMemoryThresholds(memoryMetric) {
    const heapUsedPercent = memoryMetric.heapUsedPercent;
    
    if (heapUsedPercent >= this.config.alertThresholds.memoryUsage.critical) {
      this.createAlert('MEMORY_CRITICAL', {
        heapUsedPercent,
        heapUsed: memoryMetric.heapUsed,
        heapTotal: memoryMetric.heapTotal
      });
    } else if (heapUsedPercent >= this.config.alertThresholds.memoryUsage.warning) {
      this.createAlert('MEMORY_WARNING', {
        heapUsedPercent,
        heapUsed: memoryMetric.heapUsed,
        heapTotal: memoryMetric.heapTotal
      });
    }
  }

  /**
   * Check CPU thresholds
   */
  checkCPUThresholds(cpuMetric) {
    // Calculate CPU usage percentage (simplified)
    const recentCpuMetrics = this.metrics.cpu.slice(-5); // Last 5 samples
    if (recentCpuMetrics.length < 2) return;

    const totalCpuTime = recentCpuMetrics.reduce((sum, metric) => sum + metric.total, 0);
    const avgCpuTime = totalCpuTime / recentCpuMetrics.length;
    
    // This is a simplified CPU usage calculation
    const cpuUsagePercent = Math.min((avgCpuTime / 1000000) * 100, 100); // Convert microseconds to percentage

    if (cpuUsagePercent >= this.config.alertThresholds.cpuUsage.critical) {
      this.createAlert('CPU_CRITICAL', {
        cpuUsagePercent,
        avgCpuTime
      });
    } else if (cpuUsagePercent >= this.config.alertThresholds.cpuUsage.warning) {
      this.createAlert('CPU_WARNING', {
        cpuUsagePercent,
        avgCpuTime
      });
    }
  }

  /**
   * Check all alerts
   */
  checkAlerts() {
    // Check error rate
    this.checkErrorRateThresholds();
    
    // Check response time trends
    this.checkResponseTimeTrends();
    
    // Clean up resolved alerts
    this.cleanupResolvedAlerts();
  }

  /**
   * Check error rate thresholds
   */
  checkErrorRateThresholds() {
    const totalRequests = this.metrics.httpMetrics.totalRequests;
    const totalErrors = this.metrics.httpMetrics.totalErrors;
    
    if (totalRequests === 0) return;
    
    const errorRate = (totalErrors / totalRequests) * 100;
    
    if (errorRate >= this.config.alertThresholds.errorRate.critical) {
      this.createAlert('ERROR_RATE_CRITICAL', {
        errorRate,
        totalErrors,
        totalRequests
      });
    } else if (errorRate >= this.config.alertThresholds.errorRate.warning) {
      this.createAlert('ERROR_RATE_WARNING', {
        errorRate,
        totalErrors,
        totalRequests
      });
    }
  }

  /**
   * Check response time trends
   */
  checkResponseTimeTrends() {
    for (const [endpoint, metrics] of this.metrics.httpMetrics.endpoints.entries()) {
      if (metrics.avgTime >= this.config.alertThresholds.responseTime.critical) {
        this.createAlert('RESPONSE_TIME_CRITICAL', {
          endpoint,
          avgResponseTime: metrics.avgTime,
          maxResponseTime: metrics.maxTime,
          requestCount: metrics.count
        });
      } else if (metrics.avgTime >= this.config.alertThresholds.responseTime.warning) {
        this.createAlert('RESPONSE_TIME_WARNING', {
          endpoint,
          avgResponseTime: metrics.avgTime,
          maxResponseTime: metrics.maxTime,
          requestCount: metrics.count
        });
      }
    }
  }

  /**
   * Create alert
   */
  createAlert(type, data) {
    const alertKey = `${type}_${JSON.stringify(data)}`;
    
    // Prevent duplicate alerts
    if (this.alerts.active.has(alertKey)) {
      return;
    }

    const alert = {
      id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type,
      severity: type.includes('CRITICAL') ? 'critical' : 'warning',
      data,
      timestamp: Date.now(),
      resolved: false
    };

    this.alerts.active.set(alertKey, alert);
    this.alerts.history.push(alert);

    // Emit alert event
    this.emit('alert', alert);

    console.warn(`Performance Alert [${alert.severity.toUpperCase()}]: ${type}`, data);
  }

  /**
   * Resolve alert
   */
  resolveAlert(alertKey) {
    const alert = this.alerts.active.get(alertKey);
    if (alert) {
      alert.resolved = true;
      alert.resolvedAt = Date.now();
      this.alerts.active.delete(alertKey);
      
      this.emit('alertResolved', alert);
    }
  }

  /**
   * Clean up resolved alerts
   */
  cleanupResolvedAlerts() {
    const now = Date.now();
    const maxAge = 60 * 60 * 1000; // 1 hour

    // Clean up alert history
    this.alerts.history = this.alerts.history.filter(
      alert => now - alert.timestamp < maxAge
    );
  }

  /**
   * Record HTTP request
   */
  recordRequest(req, res) {
    const startTime = performance.now();
    const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Store request start time
    this.metrics.requests.set(requestId, {
      method: req.method,
      url: req.url,
      userAgent: req.get('User-Agent'),
      startTime,
      timestamp: Date.now()
    });

    // Update total requests
    this.metrics.httpMetrics.totalRequests++;

    // Override res.end to capture response metrics
    const originalEnd = res.end;
    res.end = (...args) => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // Record response metrics
      this.recordResponse(requestId, res.statusCode, duration);
      
      // Create performance mark for detailed timing
      if (this.config.enableDetailedMetrics) {
        performance.mark(`request-end-${requestId}`);
        performance.measure(`request-${requestId}`, `request-start-${requestId}`, `request-end-${requestId}`);
      }

      // Call original end method
      originalEnd.apply(res, args);
    };

    // Create performance mark for request start
    if (this.config.enableDetailedMetrics) {
      performance.mark(`request-start-${requestId}`);
    }

    return requestId;
  }

  /**
   * Record HTTP response
   */
  recordResponse(requestId, statusCode, duration) {
    const request = this.metrics.requests.get(requestId);
    if (!request) return;

    const response = {
      requestId,
      statusCode,
      duration,
      timestamp: Date.now(),
      ...request
    };

    this.metrics.responses.set(requestId, response);

    // Update HTTP metrics
    this.metrics.httpMetrics.totalResponseTime += duration;
    
    if (statusCode >= 400) {
      this.metrics.httpMetrics.totalErrors++;
    }

    // Update user agent metrics
    const userAgent = request.userAgent || 'unknown';
    const currentCount = this.metrics.httpMetrics.userAgents.get(userAgent) || 0;
    this.metrics.httpMetrics.userAgents.set(userAgent, currentCount + 1);

    // Clean up request data
    this.metrics.requests.delete(requestId);

    // Emit response event
    this.emit('response', response);
  }

  /**
   * Record error
   */
  recordError(error, context = {}) {
    const errorMetric = {
      message: error.message,
      stack: error.stack,
      name: error.name,
      timestamp: Date.now(),
      context
    };

    const errorKey = `${error.name}_${error.message}`;
    this.metrics.errors.set(errorKey, errorMetric);

    this.emit('error', errorMetric);
  }

  /**
   * Get performance summary
   */
  getPerformanceSummary() {
    const now = Date.now();
    const oneHourAgo = now - 60 * 60 * 1000;

    // Filter recent metrics
    const recentMemory = this.metrics.memory.filter(m => m.timestamp > oneHourAgo);
    const recentCpu = this.metrics.cpu.filter(c => c.timestamp > oneHourAgo);
    const recentGc = this.metrics.gc.filter(g => g.timestamp > oneHourAgo);

    // Calculate averages
    const avgMemoryUsage = recentMemory.length > 0
      ? recentMemory.reduce((sum, m) => sum + m.heapUsedPercent, 0) / recentMemory.length
      : 0;

    const avgResponseTime = this.metrics.httpMetrics.totalRequests > 0
      ? this.metrics.httpMetrics.totalResponseTime / this.metrics.httpMetrics.totalRequests
      : 0;

    const errorRate = this.metrics.httpMetrics.totalRequests > 0
      ? (this.metrics.httpMetrics.totalErrors / this.metrics.httpMetrics.totalRequests) * 100
      : 0;

    // Get top slow endpoints
    const slowEndpoints = Array.from(this.metrics.httpMetrics.endpoints.entries())
      .map(([endpoint, metrics]) => ({ endpoint, ...metrics }))
      .sort((a, b) => b.avgTime - a.avgTime)
      .slice(0, 10);

    return {
      timestamp: now,
      uptime: process.uptime() * 1000,
      performance: {
        totalRequests: this.metrics.httpMetrics.totalRequests,
        totalErrors: this.metrics.httpMetrics.totalErrors,
        errorRate,
        avgResponseTime,
        avgMemoryUsage,
        gcEvents: recentGc.length
      },
      memory: {
        current: process.memoryUsage(),
        trend: recentMemory.slice(-10) // Last 10 samples
      },
      cpu: {
        current: process.cpuUsage(),
        trend: recentCpu.slice(-10) // Last 10 samples
      },
      gc: {
        recentEvents: recentGc.slice(-5), // Last 5 GC events
        totalEvents: recentGc.length
      },
      slowEndpoints,
      activeAlerts: Array.from(this.alerts.active.values()),
      recommendations: this.getPerformanceRecommendations()
    };
  }

  /**
   * Get performance recommendations
   */
  getPerformanceRecommendations() {
    const recommendations = [];
    
    // Memory recommendations
    const avgMemoryUsage = this.metrics.memory.slice(-10).reduce((sum, m) => sum + m.heapUsedPercent, 0) / 10;
    if (avgMemoryUsage > 70) {
      recommendations.push({
        type: 'memory',
        severity: 'warning',
        message: 'High memory usage detected. Consider implementing caching or optimizing memory usage.'
      });
    }

    // Response time recommendations
    const slowEndpoints = Array.from(this.metrics.httpMetrics.endpoints.entries())
      .filter(([, metrics]) => metrics.avgTime > 1000);
    
    if (slowEndpoints.length > 0) {
      recommendations.push({
        type: 'response_time',
        severity: 'warning',
        message: `${slowEndpoints.length} endpoints have slow response times. Consider optimization.`,
        endpoints: slowEndpoints.map(([endpoint]) => endpoint)
      });
    }

    // Error rate recommendations
    const errorRate = this.metrics.httpMetrics.totalRequests > 0
      ? (this.metrics.httpMetrics.totalErrors / this.metrics.httpMetrics.totalRequests) * 100
      : 0;
    
    if (errorRate > 5) {
      recommendations.push({
        type: 'error_rate',
        severity: 'critical',
        message: `High error rate (${errorRate.toFixed(2)}%). Investigate and fix errors.`
      });
    }

    // GC recommendations
    const recentGc = this.metrics.gc.slice(-10);
    const longGcEvents = recentGc.filter(gc => gc.duration > 100);
    
    if (longGcEvents.length > 2) {
      recommendations.push({
        type: 'garbage_collection',
        severity: 'warning',
        message: 'Frequent long GC pauses detected. Consider heap tuning or memory optimization.'
      });
    }

    return recommendations;
  }

  /**
   * Start cleanup process
   */
  startCleanup() {
    this.cleanupInterval = setInterval(() => {
      const now = Date.now();
      const maxAge = this.config.retentionPeriod;

      // Clean up old metrics
      this.metrics.memory = this.metrics.memory.filter(m => now - m.timestamp < maxAge);
      this.metrics.cpu = this.metrics.cpu.filter(c => now - c.timestamp < maxAge);
      this.metrics.gc = this.metrics.gc.filter(g => now - g.timestamp < maxAge);

      // Clean up old requests/responses
      for (const [key, response] of this.metrics.responses.entries()) {
        if (now - response.timestamp > maxAge) {
          this.metrics.responses.delete(key);
        }
      }

      // Clean up old errors
      for (const [key, error] of this.metrics.errors.entries()) {
        if (now - error.timestamp > maxAge) {
          this.metrics.errors.delete(key);
        }
      }
    }, 60 * 60 * 1000); // Run cleanup every hour
  }

  /**
   * Generate performance report
   */
  generateReport() {
    const summary = this.getPerformanceSummary();
    
    return {
      generatedAt: new Date().toISOString(),
      summary,
      metrics: {
        memory: this.metrics.memory.slice(-100), // Last 100 samples
        cpu: this.metrics.cpu.slice(-100),
        gc: this.metrics.gc.slice(-50),
        httpMetrics: {
          ...this.metrics.httpMetrics,
          endpoints: Object.fromEntries(this.metrics.httpMetrics.endpoints),
          statusCodes: Object.fromEntries(this.metrics.httpMetrics.statusCodes),
          userAgents: Object.fromEntries(this.metrics.httpMetrics.userAgents)
        }
      },
      alerts: {
        active: Array.from(this.alerts.active.values()),
        history: this.alerts.history.slice(-50) // Last 50 alerts
      }
    };
  }

  /**
   * Shutdown monitoring
   */
  shutdown() {
    console.log('Shutting down performance monitoring...');
    
    // Clear intervals
    if (this.metricsInterval) clearInterval(this.metricsInterval);
    if (this.alertInterval) clearInterval(this.alertInterval);
    if (this.cleanupInterval) clearInterval(this.cleanupInterval);
    
    // Disconnect observers
    for (const observer of this.observers.values()) {
      observer.disconnect();
    }
    this.observers.clear();

    console.log('Performance monitoring shut down');
  }
}

/**
 * Express middleware for performance monitoring
 */
const performanceMiddleware = (monitor) => {
  return (req, res, next) => {
    const requestId = monitor.recordRequest(req, res);
    req.performanceId = requestId;
    next();
  };
};

module.exports = {
  PerformanceMonitor,
  performanceMiddleware
};