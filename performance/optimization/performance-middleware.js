/**
 * Performance Optimization Middleware
 * Implements caching, compression, and performance monitoring
 */

const compression = require('compression');
const NodeCache = require('node-cache');
const { promisify } = require('util');
const crypto = require('crypto');

class PerformanceOptimizer {
  constructor() {
    // Initialize caches with different TTLs
    this.memoryCache = new NodeCache({ 
      stdTTL: 300, // 5 minutes default
      checkperiod: 60, // Check for expired keys every minute
      maxKeys: 10000 // Maximum number of keys
    });
    
    this.queryCache = new NodeCache({
      stdTTL: 900, // 15 minutes for query results
      checkperiod: 120,
      maxKeys: 5000
    });
    
    this.userCache = new NodeCache({
      stdTTL: 1800, // 30 minutes for user data
      checkperiod: 300,
      maxKeys: 2000
    });

    // Performance metrics
    this.metrics = {
      requests: 0,
      cacheHits: 0,
      cacheMisses: 0,
      responseTime: [],
      memoryUsage: [],
      startTime: Date.now()
    };

    // Start metrics collection
    this.startMetricsCollection();
  }

  /**
   * Configure compression middleware
   */
  configureCompression() {
    return compression({
      level: 6, // Compression level (0-9)
      threshold: 1024, // Only compress responses larger than 1KB
      filter: (req, res) => {
        // Don't compress already compressed responses
        if (res.getHeader('Content-Encoding')) {
          return false;
        }

        // Don't compress images, videos, etc.
        const contentType = res.getHeader('Content-Type');
        if (contentType && (
          contentType.includes('image/') ||
          contentType.includes('video/') ||
          contentType.includes('audio/') ||
          contentType.includes('application/octet-stream')
        )) {
          return false;
        }

        return compression.filter(req, res);
      }
    });
  }

  /**
   * Response caching middleware
   */
  cacheMiddleware(options = {}) {
    const {
      ttl = 300, // 5 minutes default
      keyGenerator = this.defaultKeyGenerator.bind(this),
      skipCache = this.defaultSkipCache.bind(this),
      cacheType = 'memory' // memory, query, user
    } = options;

    return async (req, res, next) => {
      // Skip caching for certain conditions
      if (skipCache(req, res)) {
        return next();
      }

      const cacheKey = keyGenerator(req);
      const cache = this.getCache(cacheType);

      // Try to get from cache
      const cachedResponse = cache.get(cacheKey);
      if (cachedResponse) {
        this.metrics.cacheHits++;
        
        // Set cache headers
        res.set('X-Cache', 'HIT');
        res.set('Cache-Control', `max-age=${ttl}`);
        
        return res.json(cachedResponse);
      }

      this.metrics.cacheMisses++;

      // Store original res.json method
      const originalJson = res.json;

      // Override res.json to cache the response
      res.json = function(data) {
        // Cache the response
        cache.set(cacheKey, data, ttl);
        
        // Set cache headers
        res.set('X-Cache', 'MISS');
        res.set('Cache-Control', `max-age=${ttl}`);
        
        // Call original json method
        return originalJson.call(this, data);
      };

      next();
    };
  }

  /**
   * Performance monitoring middleware
   */
  performanceMonitoring() {
    return (req, res, next) => {
      const startTime = process.hrtime();
      const startMemory = process.memoryUsage();

      // Increment request counter
      this.metrics.requests++;

      // Override res.end to measure performance
      const originalEnd = res.end;
      res.end = (...args) => {
        // Calculate response time
        const [seconds, nanoseconds] = process.hrtime(startTime);
        const responseTime = seconds * 1000 + nanoseconds / 1000000; // Convert to milliseconds
        
        // Calculate memory usage difference
        const endMemory = process.memoryUsage();
        const memoryDiff = endMemory.heapUsed - startMemory.heapUsed;

        // Store metrics
        this.metrics.responseTime.push(responseTime);
        this.metrics.memoryUsage.push(memoryDiff);

        // Keep only recent metrics (last 1000 requests)
        if (this.metrics.responseTime.length > 1000) {
          this.metrics.responseTime = this.metrics.responseTime.slice(-1000);
          this.metrics.memoryUsage = this.metrics.memoryUsage.slice(-1000);
        }

        // Add performance headers
        res.set('X-Response-Time', `${responseTime.toFixed(2)}ms`);
        res.set('X-Memory-Usage', `${(memoryDiff / 1024 / 1024).toFixed(2)}MB`);

        // Log slow requests
        if (responseTime > 2000) { // 2 seconds
          console.warn(`Slow request detected: ${req.method} ${req.path} - ${responseTime.toFixed(2)}ms`);
        }

        // Call original end method
        originalEnd.apply(res, args);
      };

      next();
    };
  }

  /**
   * Database query optimization middleware
   */
  queryOptimization() {
    return (req, res, next) => {
      // Add query optimization helpers to request
      req.optimizeQuery = (query, params = []) => {
        return this.optimizeQuery(query, params);
      };

      req.cacheQuery = (query, params = [], ttl = 900) => {
        return this.cacheQuery(query, params, ttl);
      };

      next();
    };
  }

  /**
   * Optimize database query
   */
  optimizeQuery(query, params = []) {
    // Add query hints and optimizations
    let optimizedQuery = query;

    // Add LIMIT if not present for SELECT queries
    if (optimizedQuery.trim().toLowerCase().startsWith('select') && 
        !optimizedQuery.toLowerCase().includes('limit')) {
      optimizedQuery += ' LIMIT 1000';
    }

    // Add indexes hints for common patterns
    if (optimizedQuery.includes('WHERE') && optimizedQuery.includes('created_at')) {
      // Suggest using date indexes
      console.debug('Query uses created_at filter - ensure date indexes exist');
    }

    return { query: optimizedQuery, params };
  }

  /**
   * Cache database query results
   */
  async cacheQuery(queryFn, cacheKey, ttl = 900) {
    // Check cache first
    const cached = this.queryCache.get(cacheKey);
    if (cached) {
      this.metrics.cacheHits++;
      return cached;
    }

    // Execute query
    const result = await queryFn();
    
    // Cache result
    this.queryCache.set(cacheKey, result, ttl);
    this.metrics.cacheMisses++;

    return result;
  }

  /**
   * Static asset optimization
   */
  staticAssetOptimization() {
    return (req, res, next) => {
      // Set caching headers for static assets
      if (req.url.match(/\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$/)) {
        // Cache static assets for 1 year
        res.set('Cache-Control', 'public, max-age=31536000, immutable');
        res.set('Expires', new Date(Date.now() + 31536000000).toUTCString());
      }

      // Set security headers for assets
      if (req.url.match(/\.(js|css)$/)) {
        res.set('X-Content-Type-Options', 'nosniff');
      }

      next();
    };
  }

  /**
   * Request/Response optimization
   */
  requestOptimization() {
    return (req, res, next) => {
      // Parse and validate request size
      const contentLength = parseInt(req.get('Content-Length') || '0');
      
      if (contentLength > 10 * 1024 * 1024) { // 10MB limit
        return res.status(413).json({
          error: 'payload_too_large',
          message: 'Request payload exceeds size limit'
        });
      }

      // Add ETag support
      const originalJson = res.json;
      res.json = function(data) {
        const etag = crypto
          .createHash('md5')
          .update(JSON.stringify(data))
          .digest('hex');
        
        res.set('ETag', `"${etag}"`);
        
        // Check if client has cached version
        if (req.get('If-None-Match') === `"${etag}"`) {
          return res.status(304).end();
        }

        return originalJson.call(this, data);
      };

      next();
    };
  }

  /**
   * Connection pooling optimization
   */
  connectionPooling(pool) {
    return (req, res, next) => {
      // Add optimized database connection to request
      req.getDbConnection = async () => {
        const client = await pool.connect();
        
        // Set up connection optimization
        await client.query('SET statement_timeout = 30000'); // 30 second timeout
        await client.query('SET lock_timeout = 10000'); // 10 second lock timeout
        
        return client;
      };

      next();
    };
  }

  /**
   * Default cache key generator
   */
  defaultKeyGenerator(req) {
    const user = req.user ? req.user.id : 'anonymous';
    const query = JSON.stringify(req.query);
    const body = req.method === 'POST' ? JSON.stringify(req.body) : '';
    
    return crypto
      .createHash('md5')
      .update(`${req.method}-${req.path}-${user}-${query}-${body}`)
      .digest('hex');
  }

  /**
   * Default skip cache conditions
   */
  defaultSkipCache(req, res) {
    // Skip cache for non-GET requests
    if (req.method !== 'GET') {
      return true;
    }

    // Skip cache for authenticated requests with sensitive data
    if (req.path.includes('/admin/') || req.path.includes('/auth/')) {
      return true;
    }

    // Skip cache if no-cache header is present
    if (req.get('Cache-Control') === 'no-cache') {
      return true;
    }

    return false;
  }

  /**
   * Get appropriate cache instance
   */
  getCache(type) {
    switch (type) {
      case 'query':
        return this.queryCache;
      case 'user':
        return this.userCache;
      default:
        return this.memoryCache;
    }
  }

  /**
   * Clear cache by pattern
   */
  clearCache(pattern, cacheType = 'memory') {
    const cache = this.getCache(cacheType);
    const keys = cache.keys();
    
    const keysToDelete = keys.filter(key => {
      if (typeof pattern === 'string') {
        return key.includes(pattern);
      } else if (pattern instanceof RegExp) {
        return pattern.test(key);
      }
      return false;
    });

    keysToDelete.forEach(key => cache.del(key));
    
    return keysToDelete.length;
  }

  /**
   * Get performance metrics
   */
  getMetrics() {
    const now = Date.now();
    const uptime = now - this.metrics.startTime;
    
    const avgResponseTime = this.metrics.responseTime.length > 0 
      ? this.metrics.responseTime.reduce((a, b) => a + b, 0) / this.metrics.responseTime.length 
      : 0;

    const p95ResponseTime = this.metrics.responseTime.length > 0
      ? this.percentile(this.metrics.responseTime, 0.95)
      : 0;

    const p99ResponseTime = this.metrics.responseTime.length > 0
      ? this.percentile(this.metrics.responseTime, 0.99)
      : 0;

    const avgMemoryUsage = this.metrics.memoryUsage.length > 0
      ? this.metrics.memoryUsage.reduce((a, b) => a + b, 0) / this.metrics.memoryUsage.length
      : 0;

    const cacheHitRate = this.metrics.requests > 0 
      ? (this.metrics.cacheHits / (this.metrics.cacheHits + this.metrics.cacheMisses)) * 100 
      : 0;

    return {
      uptime: uptime,
      requests: this.metrics.requests,
      requestsPerSecond: this.metrics.requests / (uptime / 1000),
      responseTime: {
        average: avgResponseTime,
        p95: p95ResponseTime,
        p99: p99ResponseTime
      },
      memory: {
        averageUsagePerRequest: avgMemoryUsage,
        currentUsage: process.memoryUsage()
      },
      cache: {
        hitRate: cacheHitRate,
        hits: this.metrics.cacheHits,
        misses: this.metrics.cacheMisses,
        memoryKeys: this.memoryCache.keys().length,
        queryKeys: this.queryCache.keys().length,
        userKeys: this.userCache.keys().length
      }
    };
  }

  /**
   * Calculate percentile
   */
  percentile(arr, p) {
    const sorted = [...arr].sort((a, b) => a - b);
    const index = Math.ceil(sorted.length * p) - 1;
    return sorted[index] || 0;
  }

  /**
   * Start metrics collection
   */
  startMetricsCollection() {
    // Collect metrics every 30 seconds
    setInterval(() => {
      const metrics = this.getMetrics();
      
      // Log metrics if response time is concerning
      if (metrics.responseTime.p95 > 2000) {
        console.warn('High response times detected:', {
          p95: metrics.responseTime.p95,
          p99: metrics.responseTime.p99,
          cacheHitRate: metrics.cache.hitRate
        });
      }

      // Log memory usage if high
      const memoryUsage = process.memoryUsage();
      if (memoryUsage.heapUsed > 512 * 1024 * 1024) { // 512MB
        console.warn('High memory usage detected:', {
          heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024) + 'MB',
          heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024) + 'MB'
        });
      }
    }, 30000);
  }

  /**
   * Warmup cache with common queries
   */
  async warmupCache(db) {
    console.log('Warming up cache...');
    
    try {
      // Warmup common queries
      const commonQueries = [
        'SELECT COUNT(*) FROM users WHERE is_active = true',
        'SELECT * FROM roles WHERE active = true ORDER BY name',
        // Add more common queries here
      ];

      for (const query of commonQueries) {
        await this.cacheQuery(
          () => db.query(query),
          `warmup_${crypto.createHash('md5').update(query).digest('hex')}`,
          1800 // 30 minutes
        );
      }

      console.log('Cache warmup completed');
    } catch (error) {
      console.error('Cache warmup failed:', error);
    }
  }
}

/**
 * Performance monitoring endpoint
 */
const performanceEndpoint = (optimizer) => {
  return (req, res) => {
    const metrics = optimizer.getMetrics();
    
    res.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      performance: metrics,
      system: {
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch,
        cpus: require('os').cpus().length,
        totalMemory: require('os').totalmem(),
        freeMemory: require('os').freemem(),
        loadAverage: require('os').loadavg()
      }
    });
  };
};

module.exports = {
  PerformanceOptimizer,
  performanceEndpoint
};