import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate, Trend, Counter } from 'k6/metrics';
import { randomString, randomIntBetween } from 'https://jslib.k6.io/k6-utils/1.2.0/index.js';

// Custom metrics
export const errorRate = new Rate('error_rate');
export const responseTime = new Trend('response_time');
export const throughput = new Counter('throughput');
export const authFailures = new Counter('auth_failures');
export const apiKeyUsage = new Counter('api_key_usage');

// Test configuration
export const options = {
  scenarios: {
    // Load testing scenario - normal user behavior
    load_test: {
      executor: 'ramping-vus',
      startVUs: 0,
      stages: [
        { duration: '2m', target: 20 },   // Ramp up to 20 users
        { duration: '5m', target: 20 },   // Stay at 20 users
        { duration: '2m', target: 50 },   // Ramp up to 50 users
        { duration: '5m', target: 50 },   // Stay at 50 users
        { duration: '2m', target: 100 },  // Ramp up to 100 users
        { duration: '5m', target: 100 },  // Stay at 100 users
        { duration: '2m', target: 0 },    // Ramp down
      ],
    },

    // Stress testing scenario - high load
    stress_test: {
      executor: 'ramping-vus',
      startVUs: 0,
      stages: [
        { duration: '2m', target: 100 },  // Ramp up to 100 users
        { duration: '5m', target: 100 },  // Stay at 100 users
        { duration: '2m', target: 200 },  // Ramp up to 200 users
        { duration: '5m', target: 200 },  // Stay at 200 users
        { duration: '2m', target: 300 },  // Ramp up to 300 users
        { duration: '5m', target: 300 },  // Stay at 300 users
        { duration: '2m', target: 0 },    // Ramp down
      ],
      startTime: '23m', // Start after load test
    },

    // Spike testing scenario - sudden traffic spike
    spike_test: {
      executor: 'ramping-vus',
      startVUs: 0,
      stages: [
        { duration: '1m', target: 20 },   // Normal load
        { duration: '1m', target: 500 },  // Sudden spike
        { duration: '3m', target: 500 },  // Stay at spike
        { duration: '1m', target: 20 },   // Return to normal
        { duration: '2m', target: 0 },    // Ramp down
      ],
      startTime: '46m', // Start after stress test
    },

    // Volume testing scenario - sustained high load
    volume_test: {
      executor: 'constant-vus',
      vus: 150,
      duration: '30m',
      startTime: '54m', // Start after spike test
    },

    // API key testing scenario
    api_key_test: {
      executor: 'constant-arrival-rate',
      rate: 100, // 100 requests per second
      timeUnit: '1s',
      duration: '10m',
      preAllocatedVUs: 50,
      maxVUs: 100,
      startTime: '84m',
    },
  },

  thresholds: {
    // Performance thresholds
    http_req_duration: ['p(95)<2000'], // 95% of requests should be below 2s
    http_req_failed: ['rate<0.1'],     // Error rate should be below 10%
    error_rate: ['rate<0.05'],         // Custom error rate below 5%
    response_time: ['p(99)<5000'],     // 99% response time below 5s
    
    // Load testing thresholds
    'http_req_duration{scenario:load_test}': ['p(95)<1500'],
    'http_req_failed{scenario:load_test}': ['rate<0.05'],
    
    // Stress testing thresholds (more relaxed)
    'http_req_duration{scenario:stress_test}': ['p(95)<3000'],
    'http_req_failed{scenario:stress_test}': ['rate<0.15'],
    
    // Spike testing thresholds
    'http_req_duration{scenario:spike_test}': ['p(95)<5000'],
    'http_req_failed{scenario:spike_test}': ['rate<0.2'],
  },
};

// Base URL configuration
const BASE_URL = __ENV.BASE_URL || 'http://localhost:3000';
const API_BASE_URL = `${BASE_URL}/api`;

// Test data
const TEST_USERS = [
  { email: '<EMAIL>', password: 'TestPassword123!' },
  { email: '<EMAIL>', password: 'TestPassword123!' },
  { email: '<EMAIL>', password: 'TestPassword123!' },
  { email: '<EMAIL>', password: 'TestPassword123!' },
  { email: '<EMAIL>', password: 'TestPassword123!' },
];

const API_KEYS = [
  'ek_test1234567890abcdef_1234567890abcdef1234567890abcdef12345678',
  'ek_test2345678901bcdefg_2345678901bcdefg2345678901bcdefg23456789',
  'ek_test3456789012cdefgh_3456789012cdefgh3456789012cdefgh34567890',
];

// Authentication helper
function authenticate(userIndex = 0) {
  const user = TEST_USERS[userIndex % TEST_USERS.length];
  
  const loginPayload = JSON.stringify({
    email: user.email,
    password: user.password,
  });

  const params = {
    headers: {
      'Content-Type': 'application/json',
    },
  };

  const response = http.post(`${API_BASE_URL}/auth/login`, loginPayload, params);
  
  const success = check(response, {
    'login successful': (r) => r.status === 200,
    'login response has token': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.token !== undefined;
      } catch {
        return false;
      }
    },
  });

  if (!success) {
    authFailures.add(1);
    return null;
  }

  try {
    const body = JSON.parse(response.body);
    return body.token;
  } catch {
    return null;
  }
}

// API Key authentication helper
function getAPIKeyHeaders(keyIndex = 0) {
  const apiKey = API_KEYS[keyIndex % API_KEYS.length];
  return {
    'X-API-Key': apiKey,
    'Content-Type': 'application/json',
  };
}

// Test scenarios
export default function () {
  const scenario = __ENV.K6_SCENARIO || 'load_test';
  
  switch (scenario) {
    case 'load_test':
      loadTestScenario();
      break;
    case 'stress_test':
      stressTestScenario();
      break;
    case 'spike_test':
      spikeTestScenario();
      break;
    case 'volume_test':
      volumeTestScenario();
      break;
    case 'api_key_test':
      apiKeyTestScenario();
      break;
    default:
      mixedScenario();
  }
}

// Load test scenario - normal user behavior
function loadTestScenario() {
  const userIndex = randomIntBetween(0, TEST_USERS.length - 1);
  const token = authenticate(userIndex);
  
  if (!token) {
    sleep(1);
    return;
  }

  const authHeaders = {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json',
  };

  // Simulate typical user workflow
  const workflows = [
    () => dashboardWorkflow(authHeaders),
    () => analyticsWorkflow(authHeaders),
    () => integrationsWorkflow(authHeaders),
    () => settingsWorkflow(authHeaders),
  ];

  const workflow = workflows[randomIntBetween(0, workflows.length - 1)];
  workflow();

  sleep(randomIntBetween(1, 3));
}

// Stress test scenario - high load with more intensive operations
function stressTestScenario() {
  const userIndex = randomIntBetween(0, TEST_USERS.length - 1);
  const token = authenticate(userIndex);
  
  if (!token) {
    sleep(0.5);
    return;
  }

  const authHeaders = {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json',
  };

  // More intensive operations for stress testing
  bulkAnalyticsOperations(authHeaders);
  
  sleep(randomIntBetween(0.5, 1.5));
}

// Spike test scenario - simulates sudden traffic spikes
function spikeTestScenario() {
  // Quick operations during spike
  const response = http.get(`${API_BASE_URL}/health`);
  
  check(response, {
    'health check ok': (r) => r.status === 200,
  });

  // Some users try to authenticate during spike
  if (randomIntBetween(1, 100) <= 30) { // 30% try to authenticate
    authenticate(randomIntBetween(0, TEST_USERS.length - 1));
  }

  sleep(0.1); // Very short sleep during spike
}

// Volume test scenario - sustained operations
function volumeTestScenario() {
  const operations = [
    () => {
      const response = http.get(`${API_BASE_URL}/health`);
      check(response, { 'health check ok': (r) => r.status === 200 });
    },
    () => {
      const response = http.get(`${BASE_URL}/ping`);
      check(response, { 'ping ok': (r) => r.status === 200 });
    },
    () => {
      const token = authenticate(randomIntBetween(0, TEST_USERS.length - 1));
      if (token) {
        const response = http.get(`${API_BASE_URL}/user/profile`, {
          headers: { 'Authorization': `Bearer ${token}` }
        });
        check(response, { 'profile fetch ok': (r) => r.status === 200 });
      }
    },
  ];

  const operation = operations[randomIntBetween(0, operations.length - 1)];
  operation();

  sleep(randomIntBetween(0.5, 2));
}

// API key test scenario
function apiKeyTestScenario() {
  const keyIndex = randomIntBetween(0, API_KEYS.length - 1);
  const headers = getAPIKeyHeaders(keyIndex);

  const endpoints = [
    '/analytics/events',
    '/analytics/metrics',
    '/integrations/status',
    '/user/profile',
  ];

  const endpoint = endpoints[randomIntBetween(0, endpoints.length - 1)];
  const response = http.get(`${API_BASE_URL}${endpoint}`, { headers });

  const success = check(response, {
    'api key request successful': (r) => r.status === 200 || r.status === 401,
    'response time acceptable': (r) => r.timings.duration < 2000,
  });

  if (success) {
    apiKeyUsage.add(1);
  }

  throughput.add(1);
  sleep(0.1);
}

// Mixed scenario - combination of different operations
function mixedScenario() {
  const scenarios = [loadTestScenario, apiKeyTestScenario];
  const scenario = scenarios[randomIntBetween(0, scenarios.length - 1)];
  scenario();
}

// Workflow functions
function dashboardWorkflow(headers) {
  const startTime = new Date();

  // Get dashboard data
  let response = http.get(`${API_BASE_URL}/dashboard`, { headers });
  check(response, {
    'dashboard load successful': (r) => r.status === 200,
    'dashboard response time ok': (r) => r.timings.duration < 1500,
  });

  // Get recent activity
  response = http.get(`${API_BASE_URL}/dashboard/activity`, { headers });
  check(response, {
    'activity load successful': (r) => r.status === 200,
  });

  // Get quick stats
  response = http.get(`${API_BASE_URL}/dashboard/stats`, { headers });
  check(response, {
    'stats load successful': (r) => r.status === 200,
  });

  recordWorkflowMetrics('dashboard', startTime);
}

function analyticsWorkflow(headers) {
  const startTime = new Date();

  // Get analytics overview
  let response = http.get(`${API_BASE_URL}/analytics/overview`, { headers });
  check(response, {
    'analytics overview successful': (r) => r.status === 200,
    'analytics response time ok': (r) => r.timings.duration < 2000,
  });

  // Get events data
  response = http.get(`${API_BASE_URL}/analytics/events?limit=100`, { headers });
  check(response, {
    'events data successful': (r) => r.status === 200,
  });

  // Create a simple query
  const queryPayload = JSON.stringify({
    metric: 'page_views',
    timeRange: '7d',
    filters: {}
  });

  response = http.post(`${API_BASE_URL}/analytics/query`, queryPayload, { headers });
  check(response, {
    'analytics query successful': (r) => r.status === 200,
    'query response time acceptable': (r) => r.timings.duration < 3000,
  });

  recordWorkflowMetrics('analytics', startTime);
}

function integrationsWorkflow(headers) {
  const startTime = new Date();

  // Get integrations list
  let response = http.get(`${API_BASE_URL}/integrations`, { headers });
  check(response, {
    'integrations list successful': (r) => r.status === 200,
  });

  // Get integration status
  response = http.get(`${API_BASE_URL}/integrations/status`, { headers });
  check(response, {
    'integration status successful': (r) => r.status === 200,
  });

  recordWorkflowMetrics('integrations', startTime);
}

function settingsWorkflow(headers) {
  const startTime = new Date();

  // Get user settings
  let response = http.get(`${API_BASE_URL}/user/settings`, { headers });
  check(response, {
    'settings load successful': (r) => r.status === 200,
  });

  // Get user profile
  response = http.get(`${API_BASE_URL}/user/profile`, { headers });
  check(response, {
    'profile load successful': (r) => r.status === 200,
  });

  recordWorkflowMetrics('settings', startTime);
}

function bulkAnalyticsOperations(headers) {
  const startTime = new Date();

  // Simulate bulk data operations
  const operations = [
    () => http.get(`${API_BASE_URL}/analytics/events?limit=1000`, { headers }),
    () => http.get(`${API_BASE_URL}/analytics/metrics?timeRange=30d`, { headers }),
    () => http.get(`${API_BASE_URL}/analytics/export?format=csv`, { headers }),
  ];

  operations.forEach(operation => {
    const response = operation();
    check(response, {
      'bulk operation successful': (r) => r.status === 200,
      'bulk operation response time': (r) => r.timings.duration < 5000,
    });
  });

  recordWorkflowMetrics('bulk_analytics', startTime);
}

// Helper function to record workflow metrics
function recordWorkflowMetrics(workflowName, startTime) {
  const duration = new Date() - startTime;
  responseTime.add(duration, { workflow: workflowName });
  throughput.add(1, { workflow: workflowName });
}

// Setup function - runs once before the test
export function setup() {
  console.log('Starting performance tests...');
  console.log(`Base URL: ${BASE_URL}`);
  console.log(`API Base URL: ${API_BASE_URL}`);
  
  // Health check before starting tests
  const healthResponse = http.get(`${API_BASE_URL}/health`);
  if (healthResponse.status !== 200) {
    throw new Error('Application health check failed');
  }

  return {
    baseUrl: BASE_URL,
    apiBaseUrl: API_BASE_URL,
  };
}

// Teardown function - runs once after the test
export function teardown(data) {
  console.log('Performance tests completed');
  console.log('Check the results and metrics for performance analysis');
}