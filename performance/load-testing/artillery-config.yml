# Artillery Load Testing Configuration
# Comprehensive load testing for the e-commerce analytics platform

config:
  target: 'http://localhost:3000'
  phases:
    # Warm-up phase
    - duration: 60
      arrivalRate: 5
      name: "Warm-up"
    
    # Load testing phase
    - duration: 300
      arrivalRate: 20
      name: "Load Test"
    
    # Stress testing phase
    - duration: 300
      arrivalRate: 50
      name: "Stress Test"
    
    # Peak testing phase
    - duration: 180
      arrivalRate: 100
      name: "Peak Load"
    
    # Cool-down phase
    - duration: 60
      arrivalRate: 5
      name: "Cool-down"

  defaults:
    headers:
      User-Agent: "Artillery Load Test"
      Accept: "application/json"
      Content-Type: "application/json"

  # Payload definitions
  payload:
    - path: "./test-data/users.csv"
      fields:
        - "email"
        - "password"
    - path: "./test-data/api-keys.csv"
      fields:
        - "apiKey"

  # Performance metrics collection
  metrics:
    - name: "response_time_dashboard"
      type: "histogram"
    - name: "response_time_analytics" 
      type: "histogram"
    - name: "response_time_api"
      type: "histogram"
    - name: "auth_success_rate"
      type: "rate"
    - name: "api_key_success_rate"
      type: "rate"

  # Plugins for enhanced monitoring
  plugins:
    statsd:
      host: "localhost"
      port: 8125
      prefix: "artillery.ecommerce_analytics"
    
    cloudwatch:
      region: "us-east-1"
      namespace: "Artillery/EcommerceAnalytics"

  # HTTP configuration
  http:
    timeout: 30
    pool: 50
    maxSockets: 50

# Test scenarios
scenarios:
  # Authentication and basic user flows
  - name: "User Authentication Flow"
    weight: 30
    flow:
      - post:
          url: "/api/auth/login"
          json:
            email: "{{ email }}"
            password: "{{ password }}"
          capture:
            - json: "$.token"
              as: "authToken"
          expect:
            - statusCode: [200, 401]
            - hasHeader: "content-type"
      
      - think: 2
      
      - get:
          url: "/api/user/profile"
          headers:
            Authorization: "Bearer {{ authToken }}"
          expect:
            - statusCode: [200, 401]
          measure:
            - "response_time_api"

  # Dashboard and analytics workflows
  - name: "Dashboard Workflow"
    weight: 25
    flow:
      - post:
          url: "/api/auth/login"
          json:
            email: "{{ email }}"
            password: "{{ password }}"
          capture:
            - json: "$.token"
              as: "authToken"
          expect:
            - statusCode: 200
      
      - think: 1
      
      - get:
          url: "/api/dashboard"
          headers:
            Authorization: "Bearer {{ authToken }}"
          expect:
            - statusCode: 200
          measure:
            - "response_time_dashboard"
      
      - think: 3
      
      - get:
          url: "/api/dashboard/stats"
          headers:
            Authorization: "Bearer {{ authToken }}"
          expect:
            - statusCode: 200
      
      - think: 2
      
      - get:
          url: "/api/dashboard/activity"
          headers:
            Authorization: "Bearer {{ authToken }}"
          expect:
            - statusCode: 200

  # Analytics-heavy workflows
  - name: "Analytics Workflow"
    weight: 20
    flow:
      - post:
          url: "/api/auth/login"
          json:
            email: "{{ email }}"
            password: "{{ password }}"
          capture:
            - json: "$.token"
              as: "authToken"
          expect:
            - statusCode: 200
      
      - think: 1
      
      - get:
          url: "/api/analytics/overview"
          headers:
            Authorization: "Bearer {{ authToken }}"
          expect:
            - statusCode: 200
          measure:
            - "response_time_analytics"
      
      - think: 5
      
      - get:
          url: "/api/analytics/events?limit=100&timeRange=7d"
          headers:
            Authorization: "Bearer {{ authToken }}"
          expect:
            - statusCode: 200
      
      - think: 3
      
      - post:
          url: "/api/analytics/query"
          headers:
            Authorization: "Bearer {{ authToken }}"
          json:
            metric: "page_views"
            timeRange: "7d"
            filters: {}
          expect:
            - statusCode: 200
      
      - think: 4
      
      - get:
          url: "/api/analytics/metrics?timeRange=30d"
          headers:
            Authorization: "Bearer {{ authToken }}"
          expect:
            - statusCode: 200

  # API key authentication workflows
  - name: "API Key Workflow"
    weight: 15
    flow:
      - get:
          url: "/api/analytics/events"
          headers:
            X-API-Key: "{{ apiKey }}"
          expect:
            - statusCode: [200, 401, 403]
          measure:
            - "response_time_api"
      
      - think: 2
      
      - get:
          url: "/api/analytics/metrics"
          headers:
            X-API-Key: "{{ apiKey }}"
          expect:
            - statusCode: [200, 401, 403]
      
      - think: 1
      
      - get:
          url: "/api/integrations/status"
          headers:
            X-API-Key: "{{ apiKey }}"
          expect:
            - statusCode: [200, 401, 403]

  # Admin operations workflows
  - name: "Admin Workflow"
    weight: 5
    flow:
      - post:
          url: "/api/auth/login"
          json:
            email: "<EMAIL>"
            password: "AdminPassword123!"
          capture:
            - json: "$.token"
              as: "adminToken"
          expect:
            - statusCode: [200, 401]
      
      - think: 2
      
      - get:
          url: "/admin/users"
          headers:
            Authorization: "Bearer {{ adminToken }}"
          expect:
            - statusCode: [200, 401, 403]
      
      - think: 3
      
      - get:
          url: "/admin/security/dashboard"
          headers:
            Authorization: "Bearer {{ adminToken }}"
          expect:
            - statusCode: [200, 401, 403]
      
      - think: 2
      
      - get:
          url: "/admin/roles"
          headers:
            Authorization: "Bearer {{ adminToken }}"
          expect:
            - statusCode: [200, 401, 403]

  # Integration testing workflows
  - name: "Integration Workflow"
    weight: 5
    flow:
      - post:
          url: "/api/auth/login"
          json:
            email: "{{ email }}"
            password: "{{ password }}"
          capture:
            - json: "$.token"
              as: "authToken"
          expect:
            - statusCode: 200
      
      - think: 1
      
      - get:
          url: "/api/integrations"
          headers:
            Authorization: "Bearer {{ authToken }}"
          expect:
            - statusCode: 200
      
      - think: 3
      
      - get:
          url: "/api/integrations/shopify/status"
          headers:
            Authorization: "Bearer {{ authToken }}"
          expect:
            - statusCode: [200, 404]
      
      - think: 2
      
      - post:
          url: "/api/integrations/webhook"
          headers:
            Authorization: "Bearer {{ authToken }}"
          json:
            type: "order_created"
            data: {
              "order_id": "test-{{ $randomNumber }}", 
              "amount": 99.99,
              "currency": "USD"
            }
          expect:
            - statusCode: [200, 400]

# Performance expectations and thresholds
expectations:
  # Response time expectations
  - http.response_time:
      p95: 2000  # 95th percentile should be under 2 seconds
      p99: 5000  # 99th percentile should be under 5 seconds
      max: 10000 # Maximum response time 10 seconds

  # Success rate expectations
  - http.response_code:
      200: 80    # At least 80% of requests should be successful
      4xx: 15    # At most 15% client errors
      5xx: 5     # At most 5% server errors

  # Custom metric expectations
  - response_time_dashboard.p95: 1500
  - response_time_analytics.p95: 3000
  - response_time_api.p95: 1000
  - auth_success_rate: 0.9

# Custom functions for advanced scenarios
before:
  flow:
    - log: "Starting Artillery load test for E-commerce Analytics Platform"

after:
  flow:
    - log: "Artillery load test completed"

# Custom reporting configuration
reporting:
  - name: "console"
    options:
      showErrorDetails: true
  
  - name: "json"
    options:
      outputPath: "./reports/artillery-report-{{ timestamp }}.json"
  
  - name: "html"
    options:
      outputPath: "./reports/artillery-report-{{ timestamp }}.html"