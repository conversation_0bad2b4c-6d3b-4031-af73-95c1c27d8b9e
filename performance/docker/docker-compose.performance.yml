# Docker Compose for Performance Testing Environment
# Includes monitoring tools and load testing infrastructure

version: '3.8'

services:
  # Main application
  app:
    build:
      context: ../../
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=ecommerce_analytics
      - DB_USER=postgres
      - DB_PASSWORD=postgres
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=performance-test-secret
    depends_on:
      - postgres
      - redis
    networks:
      - performance-net
    volumes:
      - ../../logs:/app/logs
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=ecommerce_analytics
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
    ports:
      - "5432:5432"
    networks:
      - performance-net
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ../../database/migrations:/docker-entrypoint-initdb.d
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'

  # Redis Cache
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    networks:
      - performance-net
    command: redis-server --maxmemory 512mb --maxmemory-policy allkeys-lru
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.25'

  # Nginx Load Balancer
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - app
    networks:
      - performance-net
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'

  # Prometheus for metrics collection
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=24h'
      - '--web.enable-lifecycle'
    networks:
      - performance-net
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.25'

  # Grafana for visualization
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./grafana/datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      - prometheus
    networks:
      - performance-net
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'

  # Node Exporter for system metrics
  node-exporter:
    image: prom/node-exporter:latest
    ports:
      - "9100:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    networks:
      - performance-net
    deploy:
      resources:
        limits:
          memory: 128M
          cpus: '0.1'

  # Postgres Exporter for database metrics
  postgres-exporter:
    image: prometheuscommunity/postgres-exporter:latest
    environment:
      - DATA_SOURCE_NAME=********************************************/ecommerce_analytics?sslmode=disable
    ports:
      - "9187:9187"
    depends_on:
      - postgres
    networks:
      - performance-net
    deploy:
      resources:
        limits:
          memory: 128M
          cpus: '0.1'

  # Redis Exporter for cache metrics
  redis-exporter:
    image: oliver006/redis_exporter:latest
    environment:
      - REDIS_ADDR=redis://redis:6379
    ports:
      - "9121:9121"
    depends_on:
      - redis
    networks:
      - performance-net
    deploy:
      resources:
        limits:
          memory: 64M
          cpus: '0.1'

  # InfluxDB for time-series data
  influxdb:
    image: influxdb:2.0-alpine
    ports:
      - "8086:8086"
    environment:
      - INFLUXDB_DB=performance
      - INFLUXDB_ADMIN_USER=admin
      - INFLUXDB_ADMIN_PASSWORD=admin123
      - INFLUXDB_USER=performance
      - INFLUXDB_USER_PASSWORD=performance123
    volumes:
      - influxdb_data:/var/lib/influxdb2
    networks:
      - performance-net
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.25'

  # K6 Load Testing Service
  k6:
    image: grafana/k6:latest
    volumes:
      - ../load-testing:/scripts
      - ../results:/results
    environment:
      - BASE_URL=http://nginx
    networks:
      - performance-net
    profiles:
      - testing
    command: ['sleep', '3600'] # Keep container running for manual test execution
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'

  # Artillery Load Testing Service
  artillery:
    image: artilleryio/artillery:latest
    volumes:
      - ../load-testing:/scripts
      - ../results:/results
    environment:
      - TARGET=http://nginx
    networks:
      - performance-net
    profiles:
      - testing
    command: ['sleep', '3600'] # Keep container running for manual test execution
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'

  # Elasticsearch for log aggregation
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.5.0
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - performance-net
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'

  # Kibana for log visualization
  kibana:
    image: docker.elastic.co/kibana/kibana:8.5.0
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
    networks:
      - performance-net
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.25'

  # Jaeger for distributed tracing
  jaeger:
    image: jaegertracing/all-in-one:latest
    ports:
      - "14268:14268"
      - "16686:16686"
    environment:
      - COLLECTOR_OTLP_ENABLED=true
    networks:
      - performance-net
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'

  # StatsD for custom metrics
  statsd:
    image: graphiteapp/graphite-statsd:latest
    ports:
      - "8080:80"
      - "8125:8125/udp"
      - "8126:8126"
    networks:
      - performance-net
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'

networks:
  performance-net:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  postgres_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  influxdb_data:
    driver: local
  elasticsearch_data:
    driver: local