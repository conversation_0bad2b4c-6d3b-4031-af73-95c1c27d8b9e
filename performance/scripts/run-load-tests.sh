#!/bin/bash

# Load Testing Script for E-commerce Analytics Platform
# This script runs comprehensive load tests using multiple tools

set -euo pipefail

# Configuration
BASE_URL="${BASE_URL:-http://localhost:3000}"
RESULTS_DIR="./performance/results"
REPORTS_DIR="./performance/reports"
TEST_DATA_DIR="./performance/load-testing/test-data"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check dependencies
check_dependencies() {
    log_info "Checking dependencies..."
    
    local missing_deps=()
    
    if ! command -v k6 &> /dev/null; then
        missing_deps+=("k6")
    fi
    
    if ! command -v artillery &> /dev/null; then
        missing_deps+=("artillery")
    fi
    
    if ! command -v docker &> /dev/null; then
        missing_deps+=("docker")
    fi
    
    if ! command -v curl &> /dev/null; then
        missing_deps+=("curl")
    fi
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        log_error "Missing dependencies: ${missing_deps[*]}"
        log_info "Install missing dependencies:"
        echo "  k6: https://k6.io/docs/getting-started/installation/"
        echo "  artillery: npm install -g artillery"
        echo "  docker: https://docs.docker.com/get-docker/"
        exit 1
    fi
    
    log_success "All dependencies are installed"
}

# Create necessary directories
setup_directories() {
    log_info "Setting up directories..."
    
    mkdir -p "$RESULTS_DIR"
    mkdir -p "$REPORTS_DIR"
    mkdir -p "$TEST_DATA_DIR"
    
    log_success "Directories created"
}

# Generate test data
generate_test_data() {
    log_info "Generating test data..."
    
    # Generate users CSV for Artillery
    cat > "$TEST_DATA_DIR/users.csv" << EOF
email,password
<EMAIL>,TestPassword123!
<EMAIL>,TestPassword123!
<EMAIL>,TestPassword123!
<EMAIL>,TestPassword123!
<EMAIL>,TestPassword123!
<EMAIL>,AdminPassword123!
<EMAIL>,ManagerPassword123!
<EMAIL>,AnalystPassword123!
<EMAIL>,UserPassword123!
<EMAIL>,ViewerPassword123!
EOF
    
    # Generate API keys CSV for Artillery
    cat > "$TEST_DATA_DIR/api-keys.csv" << EOF
apiKey
ek_test1234567890abcdef_1234567890abcdef1234567890abcdef12345678
ek_test2345678901bcdefg_2345678901bcdefg2345678901bcdefg23456789
ek_test3456789012cdefgh_3456789012cdefgh3456789012cdefgh34567890
ek_test4567890123defghi_4567890123defghi4567890123defghi45678901
ek_test5678901234efghij_5678901234efghij5678901234efghij56789012
EOF
    
    log_success "Test data generated"
}

# Check if application is running
check_application() {
    log_info "Checking if application is running at $BASE_URL..."
    
    local max_retries=5
    local retry_count=0
    
    while [ $retry_count -lt $max_retries ]; do
        if curl -s "$BASE_URL/health" > /dev/null; then
            log_success "Application is running"
            return 0
        fi
        
        log_warning "Application not responding, retry $((retry_count + 1))/$max_retries..."
        sleep 5
        ((retry_count++))
    done
    
    log_error "Application is not responding at $BASE_URL"
    log_info "Please start the application before running load tests"
    exit 1
}

# Start monitoring
start_monitoring() {
    log_info "Starting system monitoring..."
    
    # Start resource monitoring in background
    {
        while true; do
            echo "$(date),$(ps -o pid,ppid,cmd,%mem,%cpu --sort=-%mem -e | head -10)" >> "$RESULTS_DIR/system_resources_$TIMESTAMP.csv"
            sleep 5
        done
    } &
    MONITORING_PID=$!
    
    log_info "System monitoring started (PID: $MONITORING_PID)"
}

# Stop monitoring
stop_monitoring() {
    if [ -n "${MONITORING_PID:-}" ]; then
        log_info "Stopping system monitoring..."
        kill $MONITORING_PID 2>/dev/null || true
        wait $MONITORING_PID 2>/dev/null || true
        log_success "System monitoring stopped"
    fi
}

# Run K6 load tests
run_k6_tests() {
    log_info "Running K6 load tests..."
    
    local k6_config="./performance/load-testing/k6-config.js"
    local output_file="$RESULTS_DIR/k6_results_$TIMESTAMP.json"
    
    # Run different K6 scenarios
    local scenarios=("load_test" "stress_test" "spike_test" "volume_test" "api_key_test")
    
    for scenario in "${scenarios[@]}"; do
        log_info "Running K6 $scenario scenario..."
        
        K6_SCENARIO="$scenario" BASE_URL="$BASE_URL" k6 run \
            --out json="$output_file" \
            --summary-export="$RESULTS_DIR/k6_summary_${scenario}_$TIMESTAMP.json" \
            "$k6_config" || {
                log_error "K6 $scenario failed"
                return 1
            }
        
        log_success "K6 $scenario completed"
        
        # Wait between scenarios
        if [ "$scenario" != "api_key_test" ]; then
            log_info "Waiting 30 seconds before next scenario..."
            sleep 30
        fi
    done
    
    log_success "All K6 tests completed"
}

# Run Artillery tests
run_artillery_tests() {
    log_info "Running Artillery load tests..."
    
    local artillery_config="./performance/load-testing/artillery-config.yml"
    local output_file="$RESULTS_DIR/artillery_results_$TIMESTAMP.json"
    local report_file="$REPORTS_DIR/artillery_report_$TIMESTAMP.html"
    
    # Set environment variables
    export TARGET="$BASE_URL"
    
    # Run Artillery test
    artillery run \
        --target "$BASE_URL" \
        --output "$output_file" \
        "$artillery_config" || {
            log_error "Artillery test failed"
            return 1
        }
    
    # Generate HTML report
    artillery report \
        --output "$report_file" \
        "$output_file" || {
            log_warning "Failed to generate Artillery HTML report"
        }
    
    log_success "Artillery test completed"
    log_info "Report generated: $report_file"
}

# Run database performance tests
run_database_tests() {
    log_info "Running database performance tests..."
    
    # Check if PostgreSQL is available
    if ! command -v psql &> /dev/null; then
        log_warning "psql not found, skipping database performance tests"
        return 0
    fi
    
    local db_host="${DB_HOST:-localhost}"
    local db_port="${DB_PORT:-5432}"
    local db_name="${DB_NAME:-ecommerce_analytics}"
    local db_user="${DB_USER:-postgres}"
    
    # Create database test script
    local db_test_script="$RESULTS_DIR/db_test_$TIMESTAMP.sql"
    
    cat > "$db_test_script" << 'EOF'
-- Database Performance Test Queries

-- Test 1: User authentication query performance
EXPLAIN ANALYZE SELECT * FROM users WHERE email = '<EMAIL>';

-- Test 2: Analytics query performance
EXPLAIN ANALYZE SELECT COUNT(*) FROM security_events WHERE created_at > NOW() - INTERVAL '24 HOURS';

-- Test 3: Role permissions query performance
EXPLAIN ANALYZE SELECT r.*, ur.assigned_at FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = (SELECT id FROM users LIMIT 1);

-- Test 4: API key validation performance
EXPLAIN ANALYZE SELECT * FROM api_keys WHERE key_id = 'test123' AND revoked = FALSE AND expires_at > NOW();

-- Test 5: Security events aggregation
EXPLAIN ANALYZE SELECT event_type, COUNT(*) FROM security_events WHERE created_at > NOW() - INTERVAL '7 DAYS' GROUP BY event_type;

-- Test 6: OAuth token validation
EXPLAIN ANALYZE SELECT * FROM oauth_access_tokens WHERE expires_at > NOW() AND revoked = FALSE LIMIT 100;

-- Test 7: Complex join query
EXPLAIN ANALYZE 
SELECT u.email, r.name, COUNT(se.id) as event_count
FROM users u
LEFT JOIN user_roles ur ON u.id = ur.user_id
LEFT JOIN roles r ON ur.role_id = r.id
LEFT JOIN security_events se ON u.id = se.user_id
WHERE u.created_at > NOW() - INTERVAL '30 DAYS'
GROUP BY u.id, u.email, r.name
ORDER BY event_count DESC
LIMIT 50;
EOF
    
    # Run database tests if connection is available
    if PGPASSWORD="${DB_PASSWORD:-}" psql -h "$db_host" -p "$db_port" -U "$db_user" -d "$db_name" -c "SELECT 1;" &> /dev/null; then
        log_info "Running database performance analysis..."
        
        PGPASSWORD="${DB_PASSWORD:-}" psql -h "$db_host" -p "$db_port" -U "$db_user" -d "$db_name" \
            -f "$db_test_script" > "$RESULTS_DIR/db_performance_$TIMESTAMP.txt" 2>&1
        
        log_success "Database performance tests completed"
    else
        log_warning "Could not connect to database, skipping database performance tests"
    fi
}

# Analyze results
analyze_results() {
    log_info "Analyzing test results..."
    
    local analysis_file="$REPORTS_DIR/analysis_$TIMESTAMP.md"
    
    cat > "$analysis_file" << EOF
# Load Test Analysis Report

**Generated:** $(date)
**Base URL:** $BASE_URL
**Test Duration:** $(date)

## Test Summary

### K6 Results
EOF

    # Analyze K6 results if available
    if ls "$RESULTS_DIR"/k6_summary_*_"$TIMESTAMP".json 1> /dev/null 2>&1; then
        log_info "Processing K6 results..."
        
        for summary_file in "$RESULTS_DIR"/k6_summary_*_"$TIMESTAMP".json; do
            scenario=$(basename "$summary_file" | sed "s/k6_summary_\(.*\)_$TIMESTAMP.json/\1/")
            
            echo "" >> "$analysis_file"
            echo "#### $scenario Scenario" >> "$analysis_file"
            echo '```json' >> "$analysis_file"
            cat "$summary_file" >> "$analysis_file"
            echo '```' >> "$analysis_file"
        done
    fi
    
    # Add system resource analysis
    if [ -f "$RESULTS_DIR/system_resources_$TIMESTAMP.csv" ]; then
        echo "" >> "$analysis_file"
        echo "### System Resources" >> "$analysis_file"
        echo "" >> "$analysis_file"
        echo "Resource monitoring data saved to: \`$RESULTS_DIR/system_resources_$TIMESTAMP.csv\`" >> "$analysis_file"
    fi
    
    # Add recommendations
    cat >> "$analysis_file" << 'EOF'

## Performance Recommendations

### Response Time Optimization
- Implement caching for frequently accessed data
- Optimize database queries with proper indexing
- Consider implementing connection pooling
- Use compression for API responses

### Scalability Improvements
- Implement horizontal scaling with load balancers
- Consider implementing microservices architecture
- Optimize memory usage to handle more concurrent users
- Implement database read replicas for read-heavy operations

### Monitoring Recommendations
- Set up real-time performance monitoring
- Implement automated alerting for performance degradation
- Monitor database performance metrics
- Track user experience metrics

### Security Performance
- Optimize authentication and authorization flows
- Implement efficient session management
- Consider caching for permission checks
- Monitor security event processing performance

EOF
    
    log_success "Analysis complete: $analysis_file"
}

# Generate HTML report
generate_html_report() {
    log_info "Generating comprehensive HTML report..."
    
    local html_report="$REPORTS_DIR/load_test_report_$TIMESTAMP.html"
    
    cat > "$html_report" << 'EOF'
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Load Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #f4f4f4; padding: 20px; border-radius: 8px; }
        .section { margin: 20px 0; padding: 15px; border-left: 4px solid #007cba; }
        .success { border-left-color: #28a745; }
        .warning { border-left-color: #ffc107; }
        .error { border-left-color: #dc3545; }
        .metric { display: inline-block; margin: 10px; padding: 10px; background-color: #f8f9fa; border-radius: 4px; }
        pre { background-color: #f4f4f4; padding: 10px; border-radius: 4px; overflow-x: auto; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <div class="header">
        <h1>E-commerce Analytics Platform - Load Test Report</h1>
        <p><strong>Generated:</strong> TIMESTAMP_PLACEHOLDER</p>
        <p><strong>Base URL:</strong> BASE_URL_PLACEHOLDER</p>
    </div>

    <div class="section success">
        <h2>Test Summary</h2>
        <p>Comprehensive load testing completed for the e-commerce analytics platform.</p>
        <div class="metric">
            <strong>K6 Tests:</strong> Multiple scenarios executed
        </div>
        <div class="metric">
            <strong>Artillery Tests:</strong> Completed
        </div>
        <div class="metric">
            <strong>Database Tests:</strong> Performance analyzed
        </div>
    </div>

    <div class="section">
        <h2>Test Results</h2>
        <p>Detailed results are available in the following files:</p>
        <ul>
            <li>K6 Results: <code>RESULTS_DIR_PLACEHOLDER/k6_results_TIMESTAMP_PLACEHOLDER.json</code></li>
            <li>Artillery Results: <code>RESULTS_DIR_PLACEHOLDER/artillery_results_TIMESTAMP_PLACEHOLDER.json</code></li>
            <li>System Resources: <code>RESULTS_DIR_PLACEHOLDER/system_resources_TIMESTAMP_PLACEHOLDER.csv</code></li>
        </ul>
    </div>

    <div class="section warning">
        <h2>Performance Recommendations</h2>
        <ul>
            <li>Monitor response times and implement caching where appropriate</li>
            <li>Optimize database queries for better performance</li>
            <li>Consider implementing horizontal scaling for high load scenarios</li>
            <li>Set up real-time monitoring and alerting</li>
        </ul>
    </div>

    <div class="section">
        <h2>Next Steps</h2>
        <ol>
            <li>Review detailed test results in the generated files</li>
            <li>Implement performance optimizations based on findings</li>
            <li>Set up continuous performance monitoring</li>
            <li>Schedule regular load testing</li>
        </ol>
    </div>
</body>
</html>
EOF
    
    # Replace placeholders
    sed -i "s/TIMESTAMP_PLACEHOLDER/$(date)/g" "$html_report"
    sed -i "s/BASE_URL_PLACEHOLDER/$BASE_URL/g" "$html_report"
    sed -i "s/RESULTS_DIR_PLACEHOLDER/$RESULTS_DIR/g" "$html_report"
    
    log_success "HTML report generated: $html_report"
}

# Cleanup function
cleanup() {
    log_info "Cleaning up..."
    stop_monitoring
    
    # Kill any remaining background processes
    jobs -p | xargs -r kill 2>/dev/null || true
    
    log_success "Cleanup complete"
}

# Main execution
main() {
    log_info "Starting load test execution..."
    
    # Set up trap for cleanup
    trap cleanup EXIT INT TERM
    
    # Run all checks and tests
    check_dependencies
    setup_directories
    generate_test_data
    check_application
    
    start_monitoring
    
    # Run tests based on arguments
    case "${1:-all}" in
        "k6")
            run_k6_tests
            ;;
        "artillery")
            run_artillery_tests
            ;;
        "database")
            run_database_tests
            ;;
        "all"|"")
            run_k6_tests
            sleep 10
            run_artillery_tests
            sleep 10
            run_database_tests
            ;;
        *)
            log_error "Unknown test type: $1"
            log_info "Usage: $0 [k6|artillery|database|all]"
            exit 1
            ;;
    esac
    
    stop_monitoring
    
    # Analyze and report
    analyze_results
    generate_html_report
    
    log_success "Load testing completed successfully!"
    log_info "Results available in: $RESULTS_DIR"
    log_info "Reports available in: $REPORTS_DIR"
}

# Execute main function with all arguments
main "$@"
EOF