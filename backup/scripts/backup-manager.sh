#!/bin/bash

# Comprehensive Backup and Disaster Recovery Manager
# Handles database backups, file backups, and disaster recovery procedures

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"
CONFIG_FILE="${CONFIG_FILE:-$SCRIPT_DIR/backup.conf}"
LOG_FILE="${LOG_FILE:-/var/log/backup-manager.log}"

# Default configuration
DEFAULT_CONFIG="
# Backup Configuration
BACKUP_BASE_DIR=/opt/backups/ecommerce-analytics
RETENTION_DAYS=30
COMPRESSION_LEVEL=6
ENCRYPTION_ENABLED=true
ENCRYPTION_KEY_FILE=/etc/backup/encryption.key

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=ecommerce_analytics
DB_USER=postgres
DB_PASSWORD=
DB_BACKUP_FORMAT=custom

# Storage Configuration
STORAGE_TYPE=local  # local, s3, gcs, azure
S3_BUCKET=
S3_REGION=us-east-1
S3_ACCESS_KEY=
S3_SECRET_KEY=
GCS_BUCKET=
GCS_PROJECT=
AZURE_ACCOUNT=
AZURE_CONTAINER=

# Notification Configuration
NOTIFICATION_ENABLED=true
NOTIFICATION_TYPE=email  # email, slack, webhook
EMAIL_TO=<EMAIL>
EMAIL_FROM=<EMAIL>
SMTP_SERVER=localhost
SMTP_PORT=587
SLACK_WEBHOOK_URL=
WEBHOOK_URL=

# Application Configuration
APP_NAME=ecommerce-analytics
APP_DATA_DIR=/opt/ecommerce-analytics/data
APP_CONFIG_DIR=/opt/ecommerce-analytics/config
APP_LOGS_DIR=/opt/ecommerce-analytics/logs

# Health Check Configuration
HEALTH_CHECK_URL=http://localhost:3000/health
HEALTH_CHECK_TIMEOUT=30
"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging functions
log() {
    echo -e "$(date '+%Y-%m-%d %H:%M:%S') $1" | tee -a "$LOG_FILE"
}

log_info() {
    log "${BLUE}[INFO]${NC} $1"
}

log_success() {
    log "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    log "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    log "${RED}[ERROR]${NC} $1"
}

# Load configuration
load_config() {
    if [ ! -f "$CONFIG_FILE" ]; then
        log_warning "Configuration file not found. Creating default configuration at $CONFIG_FILE"
        echo "$DEFAULT_CONFIG" > "$CONFIG_FILE"
        log_info "Please edit $CONFIG_FILE with your specific configuration"
    fi
    
    # Source the configuration
    # shellcheck source=/dev/null
    source "$CONFIG_FILE"
    
    # Create backup directory structure
    mkdir -p "$BACKUP_BASE_DIR"/{database,files,logs,temp}
    mkdir -p "$(dirname "$LOG_FILE")"
    
    log_info "Configuration loaded from $CONFIG_FILE"
}

# Check dependencies
check_dependencies() {
    local missing_deps=()
    
    # Check required commands
    local required_commands=("pg_dump" "psql" "tar" "gzip" "openssl")
    
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" &> /dev/null; then
            missing_deps+=("$cmd")
        fi
    done
    
    # Check storage-specific dependencies
    case "$STORAGE_TYPE" in
        "s3")
            if ! command -v aws &> /dev/null; then
                missing_deps+=("aws-cli")
            fi
            ;;
        "gcs")
            if ! command -v gsutil &> /dev/null; then
                missing_deps+=("google-cloud-sdk")
            fi
            ;;
        "azure")
            if ! command -v az &> /dev/null; then
                missing_deps+=("azure-cli")
            fi
            ;;
    esac
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        log_error "Missing dependencies: ${missing_deps[*]}"
        exit 1
    fi
    
    log_success "All dependencies are available"
}

# Generate encryption key if needed
setup_encryption() {
    if [ "$ENCRYPTION_ENABLED" = "true" ]; then
        if [ ! -f "$ENCRYPTION_KEY_FILE" ]; then
            log_info "Generating encryption key..."
            mkdir -p "$(dirname "$ENCRYPTION_KEY_FILE")"
            openssl rand -base64 32 > "$ENCRYPTION_KEY_FILE"
            chmod 600 "$ENCRYPTION_KEY_FILE"
            log_success "Encryption key generated at $ENCRYPTION_KEY_FILE"
        fi
    fi
}

# Test database connection
test_database_connection() {
    log_info "Testing database connection..."
    
    export PGPASSWORD="$DB_PASSWORD"
    
    if psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT 1;" &> /dev/null; then
        log_success "Database connection successful"
        return 0
    else
        log_error "Database connection failed"
        return 1
    fi
}

# Create database backup
backup_database() {
    local timestamp="$1"
    local backup_file="$BACKUP_BASE_DIR/database/${APP_NAME}_db_${timestamp}.dump"
    local compressed_file="${backup_file}.gz"
    local final_file="$compressed_file"
    
    log_info "Starting database backup..."
    
    export PGPASSWORD="$DB_PASSWORD"
    
    # Create database dump
    if pg_dump -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
        --format="$DB_BACKUP_FORMAT" \
        --no-password \
        --verbose \
        --file="$backup_file" 2>> "$LOG_FILE"; then
        
        log_success "Database dump created: $backup_file"
    else
        log_error "Database dump failed"
        return 1
    fi
    
    # Compress the backup
    log_info "Compressing database backup..."
    if gzip -"$COMPRESSION_LEVEL" "$backup_file"; then
        log_success "Database backup compressed: $compressed_file"
    else
        log_error "Database compression failed"
        return 1
    fi
    
    # Encrypt if enabled
    if [ "$ENCRYPTION_ENABLED" = "true" ]; then
        log_info "Encrypting database backup..."
        local encrypted_file="${compressed_file}.enc"
        
        if openssl enc -aes-256-cbc -salt -in "$compressed_file" -out "$encrypted_file" -pass file:"$ENCRYPTION_KEY_FILE"; then
            rm "$compressed_file"
            final_file="$encrypted_file"
            log_success "Database backup encrypted: $encrypted_file"
        else
            log_error "Database encryption failed"
            return 1
        fi
    fi
    
    # Store backup metadata
    local metadata_file="$BACKUP_BASE_DIR/database/${APP_NAME}_db_${timestamp}.metadata"
    cat > "$metadata_file" << EOF
{
    "timestamp": "$timestamp",
    "database": "$DB_NAME",
    "host": "$DB_HOST",
    "format": "$DB_BACKUP_FORMAT",
    "compressed": true,
    "encrypted": $ENCRYPTION_ENABLED,
    "size": $(stat -f%z "$final_file" 2>/dev/null || stat -c%s "$final_file"),
    "checksum": "$(sha256sum "$final_file" | awk '{print $1}')",
    "backup_file": "$(basename "$final_file")"
}
EOF
    
    echo "$final_file"
}

# Create files backup
backup_files() {
    local timestamp="$1"
    local backup_file="$BACKUP_BASE_DIR/files/${APP_NAME}_files_${timestamp}.tar.gz"
    local final_file="$backup_file"
    
    log_info "Starting files backup..."
    
    # Create list of directories to backup
    local backup_dirs=()
    
    if [ -d "$APP_DATA_DIR" ]; then
        backup_dirs+=("$APP_DATA_DIR")
    fi
    
    if [ -d "$APP_CONFIG_DIR" ]; then
        backup_dirs+=("$APP_CONFIG_DIR")
    fi
    
    if [ -d "$APP_LOGS_DIR" ]; then
        backup_dirs+=("$APP_LOGS_DIR")
    fi
    
    if [ ${#backup_dirs[@]} -eq 0 ]; then
        log_warning "No application directories found to backup"
        return 0
    fi
    
    # Create tar archive
    if tar -czf "$backup_file" -C / "${backup_dirs[@]#/}" 2>> "$LOG_FILE"; then
        log_success "Files backup created: $backup_file"
    else
        log_error "Files backup failed"
        return 1
    fi
    
    # Encrypt if enabled
    if [ "$ENCRYPTION_ENABLED" = "true" ]; then
        log_info "Encrypting files backup..."
        local encrypted_file="${backup_file}.enc"
        
        if openssl enc -aes-256-cbc -salt -in "$backup_file" -out "$encrypted_file" -pass file:"$ENCRYPTION_KEY_FILE"; then
            rm "$backup_file"
            final_file="$encrypted_file"
            log_success "Files backup encrypted: $encrypted_file"
        else
            log_error "Files encryption failed"
            return 1
        fi
    fi
    
    # Store backup metadata
    local metadata_file="$BACKUP_BASE_DIR/files/${APP_NAME}_files_${timestamp}.metadata"
    cat > "$metadata_file" << EOF
{
    "timestamp": "$timestamp",
    "directories": [$(printf '"%s",' "${backup_dirs[@]}" | sed 's/,$//')],
    "compressed": true,
    "encrypted": $ENCRYPTION_ENABLED,
    "size": $(stat -f%z "$final_file" 2>/dev/null || stat -c%s "$final_file"),
    "checksum": "$(sha256sum "$final_file" | awk '{print $1}')",
    "backup_file": "$(basename "$final_file")"
}
EOF
    
    echo "$final_file"
}

# Upload backup to cloud storage
upload_backup() {
    local backup_file="$1"
    local backup_type="$2"  # database or files
    
    case "$STORAGE_TYPE" in
        "local")
            log_info "Using local storage, no upload needed"
            return 0
            ;;
        "s3")
            upload_to_s3 "$backup_file" "$backup_type"
            ;;
        "gcs")
            upload_to_gcs "$backup_file" "$backup_type"
            ;;
        "azure")
            upload_to_azure "$backup_file" "$backup_type"
            ;;
        *)
            log_error "Unknown storage type: $STORAGE_TYPE"
            return 1
            ;;
    esac
}

# Upload to AWS S3
upload_to_s3() {
    local backup_file="$1"
    local backup_type="$2"
    
    log_info "Uploading backup to S3..."
    
    export AWS_ACCESS_KEY_ID="$S3_ACCESS_KEY"
    export AWS_SECRET_ACCESS_KEY="$S3_SECRET_KEY"
    export AWS_DEFAULT_REGION="$S3_REGION"
    
    local s3_path="s3://$S3_BUCKET/$APP_NAME/$backup_type/$(basename "$backup_file")"
    
    if aws s3 cp "$backup_file" "$s3_path" --storage-class STANDARD_IA; then
        log_success "Backup uploaded to S3: $s3_path"
        
        # Upload metadata file too
        local metadata_file="${backup_file%.*}.metadata"
        if [ -f "$metadata_file" ]; then
            aws s3 cp "$metadata_file" "s3://$S3_BUCKET/$APP_NAME/$backup_type/$(basename "$metadata_file")"
        fi
        
        return 0
    else
        log_error "S3 upload failed"
        return 1
    fi
}

# Upload to Google Cloud Storage
upload_to_gcs() {
    local backup_file="$1"
    local backup_type="$2"
    
    log_info "Uploading backup to Google Cloud Storage..."
    
    local gcs_path="gs://$GCS_BUCKET/$APP_NAME/$backup_type/$(basename "$backup_file")"
    
    if gsutil cp "$backup_file" "$gcs_path"; then
        log_success "Backup uploaded to GCS: $gcs_path"
        
        # Upload metadata file too
        local metadata_file="${backup_file%.*}.metadata"
        if [ -f "$metadata_file" ]; then
            gsutil cp "$metadata_file" "gs://$GCS_BUCKET/$APP_NAME/$backup_type/$(basename "$metadata_file")"
        fi
        
        return 0
    else
        log_error "GCS upload failed"
        return 1
    fi
}

# Upload to Azure Blob Storage
upload_to_azure() {
    local backup_file="$1"
    local backup_type="$2"
    
    log_info "Uploading backup to Azure Blob Storage..."
    
    local blob_name="$APP_NAME/$backup_type/$(basename "$backup_file")"
    
    if az storage blob upload \
        --account-name "$AZURE_ACCOUNT" \
        --container-name "$AZURE_CONTAINER" \
        --name "$blob_name" \
        --file "$backup_file" \
        --tier Cool; then
        
        log_success "Backup uploaded to Azure: $blob_name"
        
        # Upload metadata file too
        local metadata_file="${backup_file%.*}.metadata"
        if [ -f "$metadata_file" ]; then
            az storage blob upload \
                --account-name "$AZURE_ACCOUNT" \
                --container-name "$AZURE_CONTAINER" \
                --name "$APP_NAME/$backup_type/$(basename "$metadata_file")" \
                --file "$metadata_file"
        fi
        
        return 0
    else
        log_error "Azure upload failed"
        return 1
    fi
}

# Clean up old backups
cleanup_old_backups() {
    log_info "Cleaning up old backups (older than $RETENTION_DAYS days)..."
    
    # Clean local backups
    find "$BACKUP_BASE_DIR" -type f -mtime +$RETENTION_DAYS -delete 2>/dev/null || true
    
    # Clean cloud storage backups
    case "$STORAGE_TYPE" in
        "s3")
            cleanup_s3_backups
            ;;
        "gcs")
            cleanup_gcs_backups
            ;;
        "azure")
            cleanup_azure_backups
            ;;
    esac
    
    log_success "Old backups cleaned up"
}

# Clean up S3 backups
cleanup_s3_backups() {
    local cutoff_date=$(date -d "$RETENTION_DAYS days ago" '+%Y-%m-%d')
    
    aws s3api list-objects-v2 \
        --bucket "$S3_BUCKET" \
        --prefix "$APP_NAME/" \
        --query "Contents[?LastModified<='$cutoff_date'].Key" \
        --output text | while read -r key; do
        
        if [ -n "$key" ] && [ "$key" != "None" ]; then
            aws s3 rm "s3://$S3_BUCKET/$key"
            log_info "Deleted old S3 backup: $key"
        fi
    done
}

# Clean up GCS backups
cleanup_gcs_backups() {
    local cutoff_date=$(date -d "$RETENTION_DAYS days ago" '+%Y-%m-%d')
    
    gsutil ls -l "gs://$GCS_BUCKET/$APP_NAME/**" | awk -v cutoff="$cutoff_date" '
        $1 != "TOTAL:" && $2 < cutoff {print $3}
    ' | while read -r object; do
        if [ -n "$object" ]; then
            gsutil rm "$object"
            log_info "Deleted old GCS backup: $object"
        fi
    done
}

# Clean up Azure backups
cleanup_azure_backups() {
    local cutoff_date=$(date -d "$RETENTION_DAYS days ago" --iso-8601)
    
    az storage blob list \
        --account-name "$AZURE_ACCOUNT" \
        --container-name "$AZURE_CONTAINER" \
        --prefix "$APP_NAME/" \
        --query "[?properties.lastModified<='$cutoff_date'].name" \
        --output tsv | while read -r blob_name; do
        
        if [ -n "$blob_name" ]; then
            az storage blob delete \
                --account-name "$AZURE_ACCOUNT" \
                --container-name "$AZURE_CONTAINER" \
                --name "$blob_name"
            log_info "Deleted old Azure backup: $blob_name"
        fi
    done
}

# Send notification
send_notification() {
    local status="$1"
    local message="$2"
    
    if [ "$NOTIFICATION_ENABLED" != "true" ]; then
        return 0
    fi
    
    case "$NOTIFICATION_TYPE" in
        "email")
            send_email_notification "$status" "$message"
            ;;
        "slack")
            send_slack_notification "$status" "$message"
            ;;
        "webhook")
            send_webhook_notification "$status" "$message"
            ;;
    esac
}

# Send email notification
send_email_notification() {
    local status="$1"
    local message="$2"
    local subject="Backup $status - $APP_NAME"
    
    if command -v mail &> /dev/null; then
        echo "$message" | mail -s "$subject" "$EMAIL_TO"
        log_info "Email notification sent"
    else
        log_warning "Mail command not available, skipping email notification"
    fi
}

# Send Slack notification
send_slack_notification() {
    local status="$1"
    local message="$2"
    
    if [ -n "$SLACK_WEBHOOK_URL" ]; then
        local color="good"
        if [ "$status" = "FAILED" ]; then
            color="danger"
        elif [ "$status" = "WARNING" ]; then
            color="warning"
        fi
        
        local payload=$(cat << EOF
{
    "attachments": [
        {
            "color": "$color",
            "title": "Backup $status - $APP_NAME",
            "text": "$message",
            "ts": $(date +%s)
        }
    ]
}
EOF
)
        
        if curl -X POST -H 'Content-type: application/json' \
            --data "$payload" \
            "$SLACK_WEBHOOK_URL" &> /dev/null; then
            log_info "Slack notification sent"
        else
            log_warning "Failed to send Slack notification"
        fi
    fi
}

# Send webhook notification
send_webhook_notification() {
    local status="$1"
    local message="$2"
    
    if [ -n "$WEBHOOK_URL" ]; then
        local payload=$(cat << EOF
{
    "app": "$APP_NAME",
    "status": "$status",
    "message": "$message",
    "timestamp": "$(date --iso-8601=seconds)"
}
EOF
)
        
        if curl -X POST -H 'Content-type: application/json' \
            --data "$payload" \
            "$WEBHOOK_URL" &> /dev/null; then
            log_info "Webhook notification sent"
        else
            log_warning "Failed to send webhook notification"
        fi
    fi
}

# Verify backup integrity
verify_backup() {
    local backup_file="$1"
    local metadata_file="${backup_file%.*}.metadata"
    
    if [ ! -f "$metadata_file" ]; then
        log_warning "Metadata file not found for backup verification"
        return 1
    fi
    
    log_info "Verifying backup integrity..."
    
    # Extract expected checksum from metadata
    local expected_checksum=$(grep '"checksum"' "$metadata_file" | sed 's/.*": "\([^"]*\)".*/\1/')
    
    # Calculate actual checksum
    local actual_checksum=$(sha256sum "$backup_file" | awk '{print $1}')
    
    if [ "$expected_checksum" = "$actual_checksum" ]; then
        log_success "Backup integrity verified"
        return 0
    else
        log_error "Backup integrity check failed"
        log_error "Expected: $expected_checksum"
        log_error "Actual: $actual_checksum"
        return 1
    fi
}

# Perform application health check
health_check() {
    if [ -n "$HEALTH_CHECK_URL" ]; then
        log_info "Performing application health check..."
        
        if curl -s --max-time "$HEALTH_CHECK_TIMEOUT" "$HEALTH_CHECK_URL" > /dev/null; then
            log_success "Application health check passed"
            return 0
        else
            log_warning "Application health check failed"
            return 1
        fi
    fi
    
    return 0
}

# Main backup function
perform_backup() {
    local timestamp=$(date '+%Y%m%d_%H%M%S')
    local backup_start_time=$(date '+%s')
    local database_backup=""
    local files_backup=""
    local backup_status="SUCCESS"
    local backup_message=""
    
    log_info "Starting backup process for $APP_NAME at $(date)"
    
    # Perform health check before backup
    if ! health_check; then
        log_warning "Application health check failed, but continuing with backup"
    fi
    
    # Test database connection
    if ! test_database_connection; then
        backup_status="FAILED"
        backup_message="Database connection failed"
        send_notification "$backup_status" "$backup_message"
        return 1
    fi
    
    # Create database backup
    if database_backup=$(backup_database "$timestamp"); then
        log_success "Database backup completed: $database_backup"
        
        # Verify database backup
        if ! verify_backup "$database_backup"; then
            backup_status="FAILED"
            backup_message="Database backup verification failed"
        else
            # Upload database backup
            if ! upload_backup "$database_backup" "database"; then
                backup_status="WARNING"
                backup_message="Database backup created but upload failed"
            fi
        fi
    else
        backup_status="FAILED"
        backup_message="Database backup failed"
    fi
    
    # Create files backup if database backup succeeded
    if [ "$backup_status" != "FAILED" ]; then
        if files_backup=$(backup_files "$timestamp"); then
            log_success "Files backup completed: $files_backup"
            
            # Verify files backup
            if ! verify_backup "$files_backup"; then
                backup_status="WARNING"
                backup_message="Files backup verification failed"
            else
                # Upload files backup
                if ! upload_backup "$files_backup" "files"; then
                    if [ "$backup_status" = "SUCCESS" ]; then
                        backup_status="WARNING"
                        backup_message="Files backup created but upload failed"
                    fi
                fi
            fi
        else
            if [ "$backup_status" = "SUCCESS" ]; then
                backup_status="WARNING"
                backup_message="Database backup succeeded but files backup failed"
            fi
        fi
    fi
    
    # Clean up old backups
    cleanup_old_backups
    
    # Calculate backup duration
    local backup_end_time=$(date '+%s')
    local backup_duration=$((backup_end_time - backup_start_time))
    
    # Prepare final message
    if [ "$backup_status" = "SUCCESS" ]; then
        backup_message="Backup completed successfully in ${backup_duration}s"
        if [ -n "$database_backup" ]; then
            backup_message="$backup_message\nDatabase: $(basename "$database_backup")"
        fi
        if [ -n "$files_backup" ]; then
            backup_message="$backup_message\nFiles: $(basename "$files_backup")"
        fi
    fi
    
    log_info "Backup process completed with status: $backup_status"
    log_info "Duration: ${backup_duration} seconds"
    
    # Send notification
    send_notification "$backup_status" "$backup_message"
    
    # Return appropriate exit code
    case "$backup_status" in
        "SUCCESS") return 0 ;;
        "WARNING") return 1 ;;
        "FAILED") return 2 ;;
    esac
}

# List available backups
list_backups() {
    log_info "Available backups:"
    
    echo "Database backups:"
    find "$BACKUP_BASE_DIR/database" -name "*.dump*" -o -name "*.metadata" | sort
    
    echo ""
    echo "Files backups:"
    find "$BACKUP_BASE_DIR/files" -name "*.tar.gz*" -o -name "*.metadata" | sort
}

# Restore from backup
restore_backup() {
    local backup_type="$1"
    local backup_file="$2"
    
    if [ ! -f "$backup_file" ]; then
        log_error "Backup file not found: $backup_file"
        return 1
    fi
    
    case "$backup_type" in
        "database")
            restore_database "$backup_file"
            ;;
        "files")
            restore_files "$backup_file"
            ;;
        *)
            log_error "Unknown backup type: $backup_type"
            return 1
            ;;
    esac
}

# Restore database from backup
restore_database() {
    local backup_file="$1"
    local temp_file="$BACKUP_BASE_DIR/temp/restore_$(basename "$backup_file")"
    
    log_info "Starting database restore from: $backup_file"
    
    # Decrypt if needed
    if [[ "$backup_file" == *.enc ]]; then
        log_info "Decrypting backup file..."
        if ! openssl enc -d -aes-256-cbc -in "$backup_file" -out "${temp_file}.gz" -pass file:"$ENCRYPTION_KEY_FILE"; then
            log_error "Failed to decrypt backup file"
            return 1
        fi
        backup_file="${temp_file}.gz"
    fi
    
    # Decompress if needed
    if [[ "$backup_file" == *.gz ]]; then
        log_info "Decompressing backup file..."
        if ! gunzip -c "$backup_file" > "$temp_file"; then
            log_error "Failed to decompress backup file"
            return 1
        fi
        backup_file="$temp_file"
    fi
    
    # Verify backup integrity before restore
    if ! verify_backup "$backup_file"; then
        log_error "Backup integrity check failed, aborting restore"
        return 1
    fi
    
    # Confirm restore operation
    log_warning "This will replace the current database. Continue? (y/N)"
    read -r confirm
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        log_info "Database restore cancelled"
        return 1
    fi
    
    # Perform database restore
    export PGPASSWORD="$DB_PASSWORD"
    
    log_info "Dropping existing database connections..."
    psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d postgres -c "
        SELECT pg_terminate_backend(pg_stat_activity.pid)
        FROM pg_stat_activity
        WHERE pg_stat_activity.datname = '$DB_NAME'
        AND pid <> pg_backend_pid();
    " 2>/dev/null || true
    
    log_info "Restoring database..."
    if pg_restore -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
        --clean --if-exists --no-owner --no-privileges \
        --verbose "$backup_file" 2>> "$LOG_FILE"; then
        
        log_success "Database restore completed"
        
        # Clean up temporary files
        rm -f "$temp_file" "${temp_file}.gz"
        
        return 0
    else
        log_error "Database restore failed"
        return 1
    fi
}

# Restore files from backup
restore_files() {
    local backup_file="$1"
    local temp_file="$BACKUP_BASE_DIR/temp/restore_$(basename "$backup_file")"
    
    log_info "Starting files restore from: $backup_file"
    
    # Decrypt if needed
    if [[ "$backup_file" == *.enc ]]; then
        log_info "Decrypting backup file..."
        if ! openssl enc -d -aes-256-cbc -in "$backup_file" -out "$temp_file" -pass file:"$ENCRYPTION_KEY_FILE"; then
            log_error "Failed to decrypt backup file"
            return 1
        fi
        backup_file="$temp_file"
    fi
    
    # Verify backup integrity before restore
    if ! verify_backup "$backup_file"; then
        log_error "Backup integrity check failed, aborting restore"
        return 1
    fi
    
    # Confirm restore operation
    log_warning "This will replace current application files. Continue? (y/N)"
    read -r confirm
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        log_info "Files restore cancelled"
        return 1
    fi
    
    # Perform files restore
    log_info "Restoring files..."
    if tar -xzf "$backup_file" -C /; then
        log_success "Files restore completed"
        
        # Clean up temporary files
        rm -f "$temp_file"
        
        return 0
    else
        log_error "Files restore failed"
        return 1
    fi
}

# Show help
show_help() {
    cat << EOF
Backup Manager for $APP_NAME

Usage: $0 [COMMAND] [OPTIONS]

Commands:
    backup          Perform full backup (database + files)
    backup-db       Backup database only
    backup-files    Backup files only
    restore         Restore from backup
    list            List available backups
    verify          Verify backup integrity
    cleanup         Clean up old backups
    health          Check application health
    config          Show current configuration
    help            Show this help message

Examples:
    $0 backup
    $0 restore database /path/to/backup.dump
    $0 list
    $0 verify /path/to/backup.dump

Configuration:
    Edit $CONFIG_FILE to customize backup settings.

EOF
}

# Main function
main() {
    local command="${1:-backup}"
    
    # Load configuration first
    load_config
    
    case "$command" in
        "backup")
            check_dependencies
            setup_encryption
            perform_backup
            ;;
        "backup-db")
            check_dependencies
            setup_encryption
            timestamp=$(date '+%Y%m%d_%H%M%S')
            database_backup=$(backup_database "$timestamp")
            upload_backup "$database_backup" "database"
            ;;
        "backup-files")
            check_dependencies
            setup_encryption
            timestamp=$(date '+%Y%m%d_%H%M%S')
            files_backup=$(backup_files "$timestamp")
            upload_backup "$files_backup" "files"
            ;;
        "restore")
            check_dependencies
            setup_encryption
            restore_backup "$2" "$3"
            ;;
        "list")
            list_backups
            ;;
        "verify")
            verify_backup "$2"
            ;;
        "cleanup")
            cleanup_old_backups
            ;;
        "health")
            health_check
            ;;
        "config")
            echo "Current configuration:"
            cat "$CONFIG_FILE"
            ;;
        "help")
            show_help
            ;;
        *)
            log_error "Unknown command: $command"
            show_help
            exit 1
            ;;
    esac
}

# Execute main function with all arguments
main "$@"