version: '3.8'

# Podman-specific overrides
services:
  # Database Services - Using docker.io explicitly for Podman
  postgres:
    image: docker.io/postgres:15-alpine
    user: "1001:1001"
    
  redis:
    image: docker.io/redis:7-alpine
    user: "1001:1001"

  # Application Services - Enhanced for Podman security
  link-tracking:
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    cap_add:
      - NET_BIND_SERVICE
    
  integration:
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
      
  analytics:
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
      
  dashboard:
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
      
  frontend:
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL

  # Monitoring Services
  prometheus:
    image: docker.io/prom/prometheus:latest
    user: "1001:1001"
    
  grafana:
    image: docker.io/grafana/grafana:latest
    user: "1001:1001"
