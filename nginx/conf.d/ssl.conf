# SSL configuration for e-commerce analytics platform
# Additional SSL settings and security configurations

# SSL session settings
ssl_session_cache shared:SSL:50m;
ssl_session_timeout 1d;
ssl_session_tickets off;

# Modern SSL configuration
ssl_protocols TLSv1.2 TLSv1.3;
ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
ssl_prefer_server_ciphers off;

# OCSP stapling
ssl_stapling on;
ssl_stapling_verify on;
ssl_trusted_certificate /etc/nginx/ssl/chain.pem;

# DNS resolver (use <PERSON><PERSON>'s DNS)
resolver 127.0.0.11 valid=300s;
resolver_timeout 5s;

# DH parameters for perfect forward secrecy
ssl_dhparam /etc/nginx/ssl/dhparam.pem;

# SSL buffer size
ssl_buffer_size 4k;

# Early data (0-RTT) for TLS 1.3 (use with caution)
# ssl_early_data on;