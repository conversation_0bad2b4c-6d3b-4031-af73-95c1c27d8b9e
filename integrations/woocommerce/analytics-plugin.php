<?php
/**
 * Plugin Name: Advanced Analytics for WooCommerce
 * Plugin URI: https://analytics-platform.com/woocommerce
 * Description: Advanced AI-powered analytics and insights for WooCommerce stores with real-time tracking, ML predictions, and comprehensive reporting.
 * Version: 1.0.0
 * Author: Analytics Platform
 * Author URI: https://analytics-platform.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: advanced-analytics-woocommerce
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * WC requires at least: 4.0
 * WC tested up to: 8.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('AAW_VERSION', '1.0.0');
define('AAW_PLUGIN_FILE', __FILE__);
define('AAW_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('AAW_PLUGIN_URL', plugin_dir_url(__FILE__));
define('AAW_PLUGIN_BASENAME', plugin_basename(__FILE__));

// Check if WooCommerce is active
if (!in_array('woocommerce/woocommerce.php', apply_filters('active_plugins', get_option('active_plugins')))) {
    add_action('admin_notices', function() {
        echo '<div class="notice notice-error"><p>' . __('Advanced Analytics for WooCommerce requires WooCommerce to be installed and active.', 'advanced-analytics-woocommerce') . '</p></div>';
    });
    return;
}

/**
 * Main plugin class
 */
class AdvancedAnalyticsWooCommerce {
    
    private static $instance = null;
    private $api_client;
    private $event_tracker;
    private $settings;
    
    /**
     * Get singleton instance
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init();
    }
    
    /**
     * Initialize plugin
     */
    private function init() {
        // Load dependencies
        $this->loadDependencies();
        
        // Initialize components
        $this->settings = new AAW_Settings();
        $this->api_client = new AAW_API_Client($this->settings);
        $this->event_tracker = new AAW_Event_Tracker($this->api_client, $this->settings);
        
        // Hook into WordPress
        add_action('init', array($this, 'initHooks'));
        add_action('admin_init', array($this, 'adminInit'));
        add_action('wp_enqueue_scripts', array($this, 'enqueueScripts'));
        add_action('admin_enqueue_scripts', array($this, 'adminEnqueueScripts'));
        
        // Plugin activation/deactivation hooks
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
        
        // WooCommerce hooks
        $this->initWooCommerceHooks();
    }
    
    /**
     * Load plugin dependencies
     */
    private function loadDependencies() {
        require_once AAW_PLUGIN_DIR . 'includes/class-settings.php';
        require_once AAW_PLUGIN_DIR . 'includes/class-api-client.php';
        require_once AAW_PLUGIN_DIR . 'includes/class-event-tracker.php';
        require_once AAW_PLUGIN_DIR . 'includes/class-data-sync.php';
        require_once AAW_PLUGIN_DIR . 'includes/class-admin-dashboard.php';
        require_once AAW_PLUGIN_DIR . 'includes/class-webhook-handler.php';
        require_once AAW_PLUGIN_DIR . 'admin/admin-pages.php';
    }
    
    /**
     * Initialize hooks
     */
    public function initHooks() {
        // Add admin menu
        add_action('admin_menu', array($this, 'addAdminMenu'));
        
        // AJAX handlers
        add_action('wp_ajax_aaw_save_settings', array($this, 'ajaxSaveSettings'));
        add_action('wp_ajax_aaw_test_connection', array($this, 'ajaxTestConnection'));
        add_action('wp_ajax_aaw_sync_data', array($this, 'ajaxSyncData'));
        add_action('wp_ajax_aaw_get_insights', array($this, 'ajaxGetInsights'));
        
        // REST API endpoints
        add_action('rest_api_init', array($this, 'registerRestRoutes'));
        
        // Cron jobs
        add_action('aaw_sync_historical_data', array($this, 'syncHistoricalData'));
        add_action('aaw_generate_insights', array($this, 'generateInsights'));
    }
    
    /**
     * Initialize WooCommerce specific hooks
     */
    private function initWooCommerceHooks() {
        // Order events
        add_action('woocommerce_new_order', array($this->event_tracker, 'trackOrderCreated'));
        add_action('woocommerce_order_status_changed', array($this->event_tracker, 'trackOrderStatusChanged'), 10, 4);
        add_action('woocommerce_payment_complete', array($this->event_tracker, 'trackPaymentComplete'));
        add_action('woocommerce_order_refunded', array($this->event_tracker, 'trackOrderRefunded'), 10, 2);
        
        // Cart events
        add_action('woocommerce_add_to_cart', array($this->event_tracker, 'trackAddToCart'), 10, 6);
        add_action('woocommerce_cart_item_removed', array($this->event_tracker, 'trackRemoveFromCart'), 10, 2);
        add_action('woocommerce_cart_updated', array($this->event_tracker, 'trackCartUpdated'));
        add_action('woocommerce_cart_emptied', array($this->event_tracker, 'trackCartEmptied'));
        
        // Checkout events
        add_action('woocommerce_checkout_process', array($this->event_tracker, 'trackCheckoutStarted'));
        add_action('woocommerce_checkout_order_processed', array($this->event_tracker, 'trackCheckoutCompleted'), 10, 3);
        
        // Product events
        add_action('woocommerce_single_product_summary', array($this->event_tracker, 'trackProductView'));
        add_action('woocommerce_after_shop_loop_item', array($this->event_tracker, 'trackProductListView'));
        
        // User events
        add_action('wp_login', array($this->event_tracker, 'trackUserLogin'), 10, 2);
        add_action('user_register', array($this->event_tracker, 'trackUserRegistration'));
        add_action('woocommerce_customer_save_address', array($this->event_tracker, 'trackAddressUpdate'), 10, 2);
        
        // Search events
        add_action('pre_get_posts', array($this->event_tracker, 'trackSearch'));
        
        // Wishlist events (if plugin exists)
        if (function_exists('YITH_WCWL')) {
            add_action('yith_wcwl_added_to_wishlist', array($this->event_tracker, 'trackWishlistAdd'));
            add_action('yith_wcwl_removed_from_wishlist', array($this->event_tracker, 'trackWishlistRemove'));
        }
        
        // Review events
        add_action('comment_post', array($this->event_tracker, 'trackProductReview'), 10, 2);
        
        // Coupon events
        add_action('woocommerce_applied_coupon', array($this->event_tracker, 'trackCouponApplied'));
        add_action('woocommerce_removed_coupon', array($this->event_tracker, 'trackCouponRemoved'));
    }
    
    /**
     * Admin initialization
     */
    public function adminInit() {
        // Register settings
        $this->settings->registerSettings();
        
        // Add settings link to plugins page
        add_filter('plugin_action_links_' . AAW_PLUGIN_BASENAME, array($this, 'addSettingsLink'));
    }
    
    /**
     * Enqueue frontend scripts
     */
    public function enqueueScripts() {
        if (!$this->settings->isConfigured()) {
            return;
        }
        
        wp_enqueue_script(
            'aaw-frontend',
            AAW_PLUGIN_URL . 'assets/js/frontend.js',
            array('jquery'),
            AAW_VERSION,
            true
        );
        
        // Localize script with settings
        wp_localize_script('aaw-frontend', 'aaw_config', array(
            'api_key' => $this->settings->getApiKey(),
            'endpoint' => $this->settings->getApiEndpoint(),
            'user_id' => get_current_user_id(),
            'session_id' => $this->generateSessionId(),
            'site_url' => home_url(),
            'currency' => get_woocommerce_currency(),
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('aaw_frontend_nonce')
        ));
    }
    
    /**
     * Enqueue admin scripts
     */
    public function adminEnqueueScripts($hook) {
        if (strpos($hook, 'advanced-analytics') === false) {
            return;
        }
        
        wp_enqueue_script(
            'aaw-admin',
            AAW_PLUGIN_URL . 'assets/js/admin.js',
            array('jquery', 'wp-api'),
            AAW_VERSION,
            true
        );
        
        wp_enqueue_style(
            'aaw-admin',
            AAW_PLUGIN_URL . 'assets/css/admin.css',
            array(),
            AAW_VERSION
        );
        
        wp_localize_script('aaw-admin', 'aaw_admin', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('aaw_admin_nonce'),
            'api_endpoint' => $this->settings->getApiEndpoint()
        ));
    }
    
    /**
     * Add admin menu
     */
    public function addAdminMenu() {
        add_menu_page(
            __('Advanced Analytics', 'advanced-analytics-woocommerce'),
            __('Analytics', 'advanced-analytics-woocommerce'),
            'manage_woocommerce',
            'advanced-analytics',
            array($this, 'renderDashboardPage'),
            'dashicons-chart-line',
            25
        );
        
        add_submenu_page(
            'advanced-analytics',
            __('Dashboard', 'advanced-analytics-woocommerce'),
            __('Dashboard', 'advanced-analytics-woocommerce'),
            'manage_woocommerce',
            'advanced-analytics',
            array($this, 'renderDashboardPage')
        );
        
        add_submenu_page(
            'advanced-analytics',
            __('Insights', 'advanced-analytics-woocommerce'),
            __('Insights', 'advanced-analytics-woocommerce'),
            'manage_woocommerce',
            'advanced-analytics-insights',
            array($this, 'renderInsightsPage')
        );
        
        add_submenu_page(
            'advanced-analytics',
            __('Settings', 'advanced-analytics-woocommerce'),
            __('Settings', 'advanced-analytics-woocommerce'),
            'manage_woocommerce',
            'advanced-analytics-settings',
            array($this, 'renderSettingsPage')
        );
    }
    
    /**
     * Render dashboard page
     */
    public function renderDashboardPage() {
        include AAW_PLUGIN_DIR . 'admin/dashboard.php';
    }
    
    /**
     * Render insights page
     */
    public function renderInsightsPage() {
        include AAW_PLUGIN_DIR . 'admin/insights.php';
    }
    
    /**
     * Render settings page
     */
    public function renderSettingsPage() {
        include AAW_PLUGIN_DIR . 'admin/settings.php';
    }
    
    /**
     * Register REST API routes
     */
    public function registerRestRoutes() {
        register_rest_route('advanced-analytics/v1', '/webhook', array(
            'methods' => 'POST',
            'callback' => array($this, 'handleWebhook'),
            'permission_callback' => array($this, 'verifyWebhookSignature')
        ));
        
        register_rest_route('advanced-analytics/v1', '/insights', array(
            'methods' => 'GET',
            'callback' => array($this, 'getInsights'),
            'permission_callback' => array($this, 'checkApiPermissions')
        ));
        
        register_rest_route('advanced-analytics/v1', '/recommendations', array(
            'methods' => 'GET',
            'callback' => array($this, 'getRecommendations'),
            'permission_callback' => array($this, 'checkApiPermissions')
        ));
    }
    
    /**
     * Handle webhook from analytics platform
     */
    public function handleWebhook(WP_REST_Request $request) {
        $webhook_handler = new AAW_Webhook_Handler($this->settings);
        return $webhook_handler->handle($request);
    }
    
    /**
     * Get insights from analytics platform
     */
    public function getInsights(WP_REST_Request $request) {
        $insights = $this->api_client->getInsights(array(
            'store_id' => $this->settings->getStoreId(),
            'timeframe' => $request->get_param('timeframe') ?: '30d',
            'metrics' => $request->get_param('metrics') ?: array('revenue', 'orders', 'customers')
        ));
        
        return new WP_REST_Response($insights, 200);
    }
    
    /**
     * Get product recommendations
     */
    public function getRecommendations(WP_REST_Request $request) {
        $user_id = $request->get_param('user_id');
        $product_id = $request->get_param('product_id');
        $type = $request->get_param('type') ?: 'related';
        
        $recommendations = $this->api_client->getRecommendations(array(
            'user_id' => $user_id,
            'product_id' => $product_id,
            'type' => $type,
            'limit' => $request->get_param('limit') ?: 10
        ));
        
        return new WP_REST_Response($recommendations, 200);
    }
    
    /**
     * AJAX: Save settings
     */
    public function ajaxSaveSettings() {
        check_ajax_referer('aaw_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_woocommerce')) {
            wp_die('Unauthorized');
        }
        
        $settings = $_POST['settings'];
        $result = $this->settings->saveSettings($settings);
        
        wp_send_json_success($result);
    }
    
    /**
     * AJAX: Test API connection
     */
    public function ajaxTestConnection() {
        check_ajax_referer('aaw_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_woocommerce')) {
            wp_die('Unauthorized');
        }
        
        $result = $this->api_client->testConnection();
        
        if ($result['success']) {
            wp_send_json_success($result);
        } else {
            wp_send_json_error($result);
        }
    }
    
    /**
     * AJAX: Sync historical data
     */
    public function ajaxSyncData() {
        check_ajax_referer('aaw_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_woocommerce')) {
            wp_die('Unauthorized');
        }
        
        $data_sync = new AAW_Data_Sync($this->api_client, $this->settings);
        $result = $data_sync->syncHistoricalData();
        
        wp_send_json_success($result);
    }
    
    /**
     * AJAX: Get insights
     */
    public function ajaxGetInsights() {
        check_ajax_referer('aaw_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_woocommerce')) {
            wp_die('Unauthorized');
        }
        
        $timeframe = $_POST['timeframe'] ?: '30d';
        $insights = $this->api_client->getInsights(array(
            'timeframe' => $timeframe,
            'store_id' => $this->settings->getStoreId()
        ));
        
        wp_send_json_success($insights);
    }
    
    /**
     * Verify webhook signature
     */
    public function verifyWebhookSignature(WP_REST_Request $request) {
        $signature = $request->get_header('X-Analytics-Signature');
        $payload = $request->get_body();
        $webhook_secret = $this->settings->getWebhookSecret();
        
        if (!$signature || !$webhook_secret) {
            return false;
        }
        
        $expected_signature = 'sha256=' . hash_hmac('sha256', $payload, $webhook_secret);
        
        return hash_equals($expected_signature, $signature);
    }
    
    /**
     * Check API permissions
     */
    public function checkApiPermissions(WP_REST_Request $request) {
        $api_key = $request->get_header('X-API-Key');
        return $api_key === $this->settings->getApiKey();
    }
    
    /**
     * Add settings link to plugins page
     */
    public function addSettingsLink($links) {
        $settings_link = '<a href="' . admin_url('admin.php?page=advanced-analytics-settings') . '">' . __('Settings', 'advanced-analytics-woocommerce') . '</a>';
        array_unshift($links, $settings_link);
        return $links;
    }
    
    /**
     * Generate session ID
     */
    private function generateSessionId() {
        if (!session_id()) {
            session_start();
        }
        return session_id();
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Create database tables if needed
        $this->createTables();
        
        // Schedule cron jobs
        if (!wp_next_scheduled('aaw_sync_historical_data')) {
            wp_schedule_event(time(), 'daily', 'aaw_sync_historical_data');
        }
        
        if (!wp_next_scheduled('aaw_generate_insights')) {
            wp_schedule_event(time(), 'hourly', 'aaw_generate_insights');
        }
        
        // Set default settings
        $this->settings->setDefaults();
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Clear scheduled events
        wp_clear_scheduled_hook('aaw_sync_historical_data');
        wp_clear_scheduled_hook('aaw_generate_insights');
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Create database tables
     */
    private function createTables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Events log table
        $table_name = $wpdb->prefix . 'aaw_events_log';
        $sql = "CREATE TABLE $table_name (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            event_type varchar(100) NOT NULL,
            event_data longtext,
            user_id bigint(20),
            session_id varchar(100),
            ip_address varchar(45),
            user_agent text,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            synced_at datetime NULL,
            PRIMARY KEY (id),
            KEY event_type (event_type),
            KEY user_id (user_id),
            KEY session_id (session_id),
            KEY created_at (created_at)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
        
        // Insights cache table
        $table_name = $wpdb->prefix . 'aaw_insights_cache';
        $sql = "CREATE TABLE $table_name (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            cache_key varchar(255) NOT NULL,
            cache_data longtext,
            expires_at datetime,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY cache_key (cache_key),
            KEY expires_at (expires_at)
        ) $charset_collate;";
        
        dbDelta($sql);
    }
    
    /**
     * Sync historical data (cron job)
     */
    public function syncHistoricalData() {
        if (!$this->settings->isConfigured()) {
            return;
        }
        
        $data_sync = new AAW_Data_Sync($this->api_client, $this->settings);
        $data_sync->syncHistoricalData();
    }
    
    /**
     * Generate insights (cron job)
     */
    public function generateInsights() {
        if (!$this->settings->isConfigured()) {
            return;
        }
        
        // Fetch latest insights from API
        $insights = $this->api_client->getInsights(array(
            'store_id' => $this->settings->getStoreId(),
            'refresh' => true
        ));
        
        // Cache insights locally
        if ($insights) {
            update_option('aaw_cached_insights', $insights);
            update_option('aaw_insights_updated', current_time('mysql'));
        }
    }
}

// Initialize plugin
add_action('plugins_loaded', function() {
    AdvancedAnalyticsWooCommerce::getInstance();
});

/**
 * Helper functions
 */

/**
 * Get plugin instance
 */
function aaw_get_instance() {
    return AdvancedAnalyticsWooCommerce::getInstance();
}

/**
 * Track custom event
 */
function aaw_track_event($event_type, $data = array()) {
    $instance = aaw_get_instance();
    if ($instance && $instance->event_tracker) {
        $instance->event_tracker->trackCustomEvent($event_type, $data);
    }
}

/**
 * Get product recommendations
 */
function aaw_get_recommendations($args = array()) {
    $instance = aaw_get_instance();
    if ($instance && $instance->api_client) {
        return $instance->api_client->getRecommendations($args);
    }
    return array();
}

/**
 * Get insights
 */
function aaw_get_insights($args = array()) {
    $instance = aaw_get_instance();
    if ($instance && $instance->api_client) {
        return $instance->api_client->getInsights($args);
    }
    return array();
}

/**
 * Check if analytics is configured
 */
function aaw_is_configured() {
    $instance = aaw_get_instance();
    return $instance && $instance->settings && $instance->settings->isConfigured();
}
?>