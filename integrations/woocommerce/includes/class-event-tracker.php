<?php
/**
 * Event Tracker Class
 * Handles tracking of WooCommerce events and sending them to the analytics platform
 */

if (!defined('ABSPATH')) {
    exit;
}

class AAW_Event_Tracker {
    
    private $api_client;
    private $settings;
    private $batch_events = array();
    private $batch_size = 50;
    
    public function __construct($api_client, $settings) {
        $this->api_client = $api_client;
        $this->settings = $settings;
        
        // Send batch events on shutdown
        add_action('shutdown', array($this, 'sendBatchEvents'));
        
        // Schedule batch send every 5 minutes
        add_action('aaw_send_batch_events', array($this, 'sendBatchEvents'));
        if (!wp_next_scheduled('aaw_send_batch_events')) {
            wp_schedule_event(time(), 'aaw_5min', 'aaw_send_batch_events');
        }
    }
    
    /**
     * Track order created
     */
    public function trackOrderCreated($order_id) {
        $order = wc_get_order($order_id);
        if (!$order) return;
        
        $event_data = array(
            'order_id' => $order_id,
            'order_number' => $order->get_order_number(),
            'status' => $order->get_status(),
            'total' => $order->get_total(),
            'subtotal' => $order->get_subtotal(),
            'tax_total' => $order->get_total_tax(),
            'shipping_total' => $order->get_shipping_total(),
            'discount_total' => $order->get_discount_total(),
            'currency' => $order->get_currency(),
            'payment_method' => $order->get_payment_method(),
            'payment_method_title' => $order->get_payment_method_title(),
            'billing_country' => $order->get_billing_country(),
            'shipping_country' => $order->get_shipping_country(),
            'customer_id' => $order->get_customer_id(),
            'customer_email' => $order->get_billing_email(),
            'customer_phone' => $order->get_billing_phone(),
            'items' => $this->getOrderItems($order),
            'coupons' => $this->getOrderCoupons($order),
            'customer_note' => $order->get_customer_note(),
            'order_key' => $order->get_order_key(),
            'date_created' => $order->get_date_created()->format('Y-m-d H:i:s'),
            'created_via' => $order->get_created_via(),
            'cart_hash' => $order->get_cart_hash()
        );
        
        $this->trackEvent('order_created', $event_data, $order->get_customer_id());
    }
    
    /**
     * Track order status change
     */
    public function trackOrderStatusChanged($order_id, $old_status, $new_status, $order) {
        $event_data = array(
            'order_id' => $order_id,
            'order_number' => $order->get_order_number(),
            'old_status' => $old_status,
            'new_status' => $new_status,
            'total' => $order->get_total(),
            'currency' => $order->get_currency(),
            'customer_id' => $order->get_customer_id(),
            'customer_email' => $order->get_billing_email(),
            'date_modified' => $order->get_date_modified()->format('Y-m-d H:i:s')
        );
        
        $this->trackEvent('order_status_changed', $event_data, $order->get_customer_id());
    }
    
    /**
     * Track payment complete
     */
    public function trackPaymentComplete($order_id) {
        $order = wc_get_order($order_id);
        if (!$order) return;
        
        $event_data = array(
            'order_id' => $order_id,
            'order_number' => $order->get_order_number(),
            'total' => $order->get_total(),
            'currency' => $order->get_currency(),
            'payment_method' => $order->get_payment_method(),
            'transaction_id' => $order->get_transaction_id(),
            'customer_id' => $order->get_customer_id(),
            'customer_email' => $order->get_billing_email(),
            'date_paid' => $order->get_date_paid() ? $order->get_date_paid()->format('Y-m-d H:i:s') : null
        );
        
        $this->trackEvent('payment_completed', $event_data, $order->get_customer_id());
    }
    
    /**
     * Track order refunded
     */
    public function trackOrderRefunded($order_id, $refund_id) {
        $order = wc_get_order($order_id);
        $refund = wc_get_order($refund_id);
        
        if (!$order || !$refund) return;
        
        $event_data = array(
            'order_id' => $order_id,
            'refund_id' => $refund_id,
            'order_number' => $order->get_order_number(),
            'refund_amount' => $refund->get_amount(),
            'refund_reason' => $refund->get_reason(),
            'currency' => $order->get_currency(),
            'customer_id' => $order->get_customer_id(),
            'customer_email' => $order->get_billing_email(),
            'date_created' => $refund->get_date_created()->format('Y-m-d H:i:s')
        );
        
        $this->trackEvent('order_refunded', $event_data, $order->get_customer_id());
    }
    
    /**
     * Track add to cart
     */
    public function trackAddToCart($cart_item_key, $product_id, $quantity, $variation_id, $variation, $cart_item_data) {
        $product = wc_get_product($variation_id ?: $product_id);
        if (!$product) return;
        
        $event_data = array(
            'product_id' => $product_id,
            'variation_id' => $variation_id,
            'product_name' => $product->get_name(),
            'product_sku' => $product->get_sku(),
            'product_type' => $product->get_type(),
            'quantity' => $quantity,
            'price' => $product->get_price(),
            'regular_price' => $product->get_regular_price(),
            'sale_price' => $product->get_sale_price(),
            'categories' => $this->getProductCategories($product_id),
            'tags' => $this->getProductTags($product_id),
            'variation' => $variation,
            'cart_item_key' => $cart_item_key,
            'cart_total' => WC()->cart->get_cart_contents_total(),
            'cart_count' => WC()->cart->get_cart_contents_count(),
            'currency' => get_woocommerce_currency()
        );
        
        $this->trackEvent('add_to_cart', $event_data);
    }
    
    /**
     * Track remove from cart
     */
    public function trackRemoveFromCart($cart_item_key, $cart) {
        $cart_item = $cart->removed_cart_contents[$cart_item_key];
        if (!$cart_item) return;
        
        $product = wc_get_product($cart_item['product_id']);
        if (!$product) return;
        
        $event_data = array(
            'product_id' => $cart_item['product_id'],
            'variation_id' => $cart_item['variation_id'],
            'product_name' => $product->get_name(),
            'quantity' => $cart_item['quantity'],
            'price' => $product->get_price(),
            'cart_item_key' => $cart_item_key,
            'cart_total' => WC()->cart->get_cart_contents_total(),
            'cart_count' => WC()->cart->get_cart_contents_count(),
            'currency' => get_woocommerce_currency()
        );
        
        $this->trackEvent('remove_from_cart', $event_data);
    }
    
    /**
     * Track cart updated
     */
    public function trackCartUpdated() {
        $cart_data = array(
            'cart_total' => WC()->cart->get_cart_contents_total(),
            'cart_count' => WC()->cart->get_cart_contents_count(),
            'cart_items' => $this->getCartItems(),
            'currency' => get_woocommerce_currency(),
            'coupons' => WC()->cart->get_applied_coupons()
        );
        
        $this->trackEvent('cart_updated', $cart_data);
    }
    
    /**
     * Track cart emptied
     */
    public function trackCartEmptied() {
        $this->trackEvent('cart_emptied', array(
            'currency' => get_woocommerce_currency()
        ));
    }
    
    /**
     * Track checkout started
     */
    public function trackCheckoutStarted() {
        $checkout_data = array(
            'cart_total' => WC()->cart->get_cart_contents_total(),
            'cart_count' => WC()->cart->get_cart_contents_count(),
            'cart_items' => $this->getCartItems(),
            'shipping_total' => WC()->cart->get_shipping_total(),
            'tax_total' => WC()->cart->get_taxes_total(),
            'discount_total' => WC()->cart->get_discount_total(),
            'currency' => get_woocommerce_currency(),
            'coupons' => WC()->cart->get_applied_coupons(),
            'chosen_shipping_methods' => WC()->session->get('chosen_shipping_methods'),
            'chosen_payment_method' => WC()->session->get('chosen_payment_method')
        );
        
        $this->trackEvent('checkout_started', $checkout_data);
    }
    
    /**
     * Track checkout completed
     */
    public function trackCheckoutCompleted($order_id, $posted_data, $order) {
        $event_data = array(
            'order_id' => $order_id,
            'order_number' => $order->get_order_number(),
            'total' => $order->get_total(),
            'currency' => $order->get_currency(),
            'payment_method' => $order->get_payment_method(),
            'customer_id' => $order->get_customer_id(),
            'customer_email' => $order->get_billing_email(),
            'billing_country' => $order->get_billing_country(),
            'shipping_country' => $order->get_shipping_country(),
            'items' => $this->getOrderItems($order),
            'checkout_duration' => $this->getCheckoutDuration()
        );
        
        $this->trackEvent('checkout_completed', $event_data, $order->get_customer_id());
    }
    
    /**
     * Track product view
     */
    public function trackProductView() {
        global $product;
        
        if (!$product || !is_product()) return;
        
        $event_data = array(
            'product_id' => $product->get_id(),
            'product_name' => $product->get_name(),
            'product_sku' => $product->get_sku(),
            'product_type' => $product->get_type(),
            'price' => $product->get_price(),
            'regular_price' => $product->get_regular_price(),
            'sale_price' => $product->get_sale_price(),
            'in_stock' => $product->is_in_stock(),
            'stock_quantity' => $product->get_stock_quantity(),
            'categories' => $this->getProductCategories($product->get_id()),
            'tags' => $this->getProductTags($product->get_id()),
            'average_rating' => $product->get_average_rating(),
            'review_count' => $product->get_review_count(),
            'currency' => get_woocommerce_currency(),
            'referrer' => wp_get_referer(),
            'page_url' => get_permalink($product->get_id())
        );
        
        $this->trackEvent('product_view', $event_data);
    }
    
    /**
     * Track product list view
     */
    public function trackProductListView() {
        global $product, $woocommerce_loop;
        
        if (!$product) return;
        
        $event_data = array(
            'product_id' => $product->get_id(),
            'product_name' => $product->get_name(),
            'price' => $product->get_price(),
            'position' => $woocommerce_loop['loop'],
            'list_name' => $this->getListName(),
            'categories' => $this->getProductCategories($product->get_id()),
            'currency' => get_woocommerce_currency()
        );
        
        $this->trackEvent('product_list_view', $event_data);
    }
    
    /**
     * Track user login
     */
    public function trackUserLogin($user_login, $user) {
        $event_data = array(
            'user_id' => $user->ID,
            'user_login' => $user_login,
            'user_email' => $user->user_email,
            'user_registered' => $user->user_registered,
            'login_method' => 'standard'
        );
        
        $this->trackEvent('user_login', $event_data, $user->ID);
    }
    
    /**
     * Track user registration
     */
    public function trackUserRegistration($user_id) {
        $user = get_user_by('id', $user_id);
        if (!$user) return;
        
        $event_data = array(
            'user_id' => $user_id,
            'user_login' => $user->user_login,
            'user_email' => $user->user_email,
            'registration_source' => $this->getRegistrationSource()
        );
        
        $this->trackEvent('user_registration', $event_data, $user_id);
    }
    
    /**
     * Track address update
     */
    public function trackAddressUpdate($user_id, $load_address) {
        $event_data = array(
            'user_id' => $user_id,
            'address_type' => $load_address,
            'country' => WC()->customer->{"get_{$load_address}_country"}(),
            'state' => WC()->customer->{"get_{$load_address}_state"}(),
            'city' => WC()->customer->{"get_{$load_address}_city"}(),
            'postcode' => WC()->customer->{"get_{$load_address}_postcode"}()
        );
        
        $this->trackEvent('address_updated', $event_data, $user_id);
    }
    
    /**
     * Track search
     */
    public function trackSearch($query) {
        if (!$query->is_search() || is_admin()) return;
        
        $search_term = get_search_query();
        if (empty($search_term)) return;
        
        $event_data = array(
            'search_term' => $search_term,
            'search_type' => $query->is_product_category() ? 'category' : 'product',
            'results_count' => $query->found_posts,
            'page_number' => get_query_var('paged') ?: 1
        );
        
        $this->trackEvent('search', $event_data);
    }
    
    /**
     * Track wishlist add
     */
    public function trackWishlistAdd($data) {
        $product = wc_get_product($data['product_id']);
        if (!$product) return;
        
        $event_data = array(
            'product_id' => $data['product_id'],
            'product_name' => $product->get_name(),
            'price' => $product->get_price(),
            'categories' => $this->getProductCategories($data['product_id']),
            'wishlist_id' => $data['wishlist_id'],
            'user_id' => $data['user_id']
        );
        
        $this->trackEvent('wishlist_add', $event_data, $data['user_id']);
    }
    
    /**
     * Track wishlist remove
     */
    public function trackWishlistRemove($data) {
        $product = wc_get_product($data['product_id']);
        if (!$product) return;
        
        $event_data = array(
            'product_id' => $data['product_id'],
            'product_name' => $product->get_name(),
            'wishlist_id' => $data['wishlist_id'],
            'user_id' => $data['user_id']
        );
        
        $this->trackEvent('wishlist_remove', $event_data, $data['user_id']);
    }
    
    /**
     * Track product review
     */
    public function trackProductReview($comment_id, $comment_approved) {
        $comment = get_comment($comment_id);
        if (!$comment || $comment->comment_type !== 'review') return;
        
        $product = wc_get_product($comment->comment_post_ID);
        if (!$product) return;
        
        $rating = get_comment_meta($comment_id, 'rating', true);
        
        $event_data = array(
            'comment_id' => $comment_id,
            'product_id' => $comment->comment_post_ID,
            'product_name' => $product->get_name(),
            'user_id' => $comment->user_id,
            'rating' => $rating,
            'review_text' => $comment->comment_content,
            'approved' => $comment_approved,
            'author_name' => $comment->comment_author,
            'author_email' => $comment->comment_author_email
        );
        
        $this->trackEvent('product_review', $event_data, $comment->user_id);
    }
    
    /**
     * Track coupon applied
     */
    public function trackCouponApplied($coupon_code) {
        $coupon = new WC_Coupon($coupon_code);
        
        $event_data = array(
            'coupon_code' => $coupon_code,
            'coupon_type' => $coupon->get_discount_type(),
            'coupon_amount' => $coupon->get_amount(),
            'cart_total' => WC()->cart->get_cart_contents_total(),
            'discount_amount' => WC()->cart->get_discount_total(),
            'currency' => get_woocommerce_currency()
        );
        
        $this->trackEvent('coupon_applied', $event_data);
    }
    
    /**
     * Track coupon removed
     */
    public function trackCouponRemoved($coupon_code) {
        $event_data = array(
            'coupon_code' => $coupon_code,
            'cart_total' => WC()->cart->get_cart_contents_total(),
            'currency' => get_woocommerce_currency()
        );
        
        $this->trackEvent('coupon_removed', $event_data);
    }
    
    /**
     * Track custom event
     */
    public function trackCustomEvent($event_type, $data = array()) {
        $this->trackEvent($event_type, $data);
    }
    
    /**
     * Generic event tracking method
     */
    private function trackEvent($event_type, $data = array(), $user_id = null) {
        if (!$this->settings->isConfigured()) {
            return;
        }
        
        // Get user ID if not provided
        if (!$user_id) {
            $user_id = get_current_user_id();
        }
        
        // Build event data
        $event = array(
            'event_type' => $event_type,
            'event_id' => uniqid('', true),
            'timestamp' => current_time('timestamp'),
            'user_id' => $user_id,
            'session_id' => $this->getSessionId(),
            'store_id' => $this->settings->getStoreId(),
            'store_url' => home_url(),
            'store_name' => get_bloginfo('name'),
            'currency' => get_woocommerce_currency(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'ip_address' => $this->getClientIP(),
            'page_url' => $this->getCurrentUrl(),
            'referrer' => wp_get_referer(),
            'data' => $data
        );
        
        // Add to batch
        $this->batch_events[] = $event;
        
        // Log event locally
        $this->logEventLocally($event);
        
        // Send immediately for critical events
        if ($this->isCriticalEvent($event_type)) {
            $this->sendBatchEvents();
        } elseif (count($this->batch_events) >= $this->batch_size) {
            $this->sendBatchEvents();
        }
    }
    
    /**
     * Send batch events to analytics platform
     */
    public function sendBatchEvents() {
        if (empty($this->batch_events)) {
            return;
        }
        
        $events = $this->batch_events;
        $this->batch_events = array();
        
        try {
            $result = $this->api_client->sendEvents($events);
            
            if ($result['success']) {
                // Mark events as synced
                $this->markEventsSynced($events);
            } else {
                // Re-add events to batch for retry
                $this->batch_events = array_merge($this->batch_events, $events);
            }
        } catch (Exception $e) {
            // Log error and re-add events
            error_log('AAW: Failed to send events: ' . $e->getMessage());
            $this->batch_events = array_merge($this->batch_events, $events);
        }
    }
    
    /**
     * Log event locally for backup
     */
    private function logEventLocally($event) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'aaw_events_log';
        
        $wpdb->insert(
            $table_name,
            array(
                'event_type' => $event['event_type'],
                'event_data' => json_encode($event),
                'user_id' => $event['user_id'],
                'session_id' => $event['session_id'],
                'ip_address' => $event['ip_address'],
                'user_agent' => $event['user_agent'],
                'created_at' => current_time('mysql')
            ),
            array('%s', '%s', '%d', '%s', '%s', '%s', '%s')
        );
    }
    
    /**
     * Mark events as synced
     */
    private function markEventsSynced($events) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'aaw_events_log';
        $event_ids = array_column($events, 'event_id');
        
        if (!empty($event_ids)) {
            $placeholders = implode(',', array_fill(0, count($event_ids), '%s'));
            
            $wpdb->query($wpdb->prepare(
                "UPDATE $table_name SET synced_at = %s WHERE JSON_EXTRACT(event_data, '$.event_id') IN ($placeholders)",
                array_merge([current_time('mysql')], $event_ids)
            ));
        }
    }
    
    /**
     * Get order items
     */
    private function getOrderItems($order) {
        $items = array();
        
        foreach ($order->get_items() as $item) {
            $product = $item->get_product();
            
            $items[] = array(
                'product_id' => $item->get_product_id(),
                'variation_id' => $item->get_variation_id(),
                'name' => $item->get_name(),
                'quantity' => $item->get_quantity(),
                'price' => $product ? $product->get_price() : 0,
                'total' => $item->get_total(),
                'sku' => $product ? $product->get_sku() : '',
                'categories' => $product ? $this->getProductCategories($item->get_product_id()) : array()
            );
        }
        
        return $items;
    }
    
    /**
     * Get order coupons
     */
    private function getOrderCoupons($order) {
        $coupons = array();
        
        foreach ($order->get_coupon_codes() as $coupon_code) {
            $coupon = new WC_Coupon($coupon_code);
            $coupons[] = array(
                'code' => $coupon_code,
                'type' => $coupon->get_discount_type(),
                'amount' => $coupon->get_amount(),
                'discount' => $order->get_discount_total()
            );
        }
        
        return $coupons;
    }
    
    /**
     * Get cart items
     */
    private function getCartItems() {
        $items = array();
        
        foreach (WC()->cart->get_cart() as $cart_item_key => $cart_item) {
            $product = $cart_item['data'];
            
            $items[] = array(
                'product_id' => $cart_item['product_id'],
                'variation_id' => $cart_item['variation_id'],
                'name' => $product->get_name(),
                'quantity' => $cart_item['quantity'],
                'price' => $product->get_price(),
                'total' => $cart_item['line_total'],
                'sku' => $product->get_sku(),
                'categories' => $this->getProductCategories($cart_item['product_id'])
            );
        }
        
        return $items;
    }
    
    /**
     * Get product categories
     */
    private function getProductCategories($product_id) {
        $categories = get_the_terms($product_id, 'product_cat');
        
        if (!$categories || is_wp_error($categories)) {
            return array();
        }
        
        return array_map(function($category) {
            return array(
                'id' => $category->term_id,
                'name' => $category->name,
                'slug' => $category->slug
            );
        }, $categories);
    }
    
    /**
     * Get product tags
     */
    private function getProductTags($product_id) {
        $tags = get_the_terms($product_id, 'product_tag');
        
        if (!$tags || is_wp_error($tags)) {
            return array();
        }
        
        return array_map(function($tag) {
            return array(
                'id' => $tag->term_id,
                'name' => $tag->name,
                'slug' => $tag->slug
            );
        }, $tags);
    }
    
    /**
     * Get session ID
     */
    private function getSessionId() {
        if (!session_id()) {
            session_start();
        }
        return session_id();
    }
    
    /**
     * Get client IP address
     */
    private function getClientIP() {
        $ip_keys = array('HTTP_CF_CONNECTING_IP', 'HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'HTTP_X_FORWARDED', 'HTTP_FORWARDED_FOR', 'HTTP_FORWARDED', 'REMOTE_ADDR');
        
        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    $ip = trim($ip);
                    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
    }
    
    /**
     * Get current URL
     */
    private function getCurrentUrl() {
        $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https://' : 'http://';
        return $protocol . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
    }
    
    /**
     * Get list name for product list views
     */
    private function getListName() {
        if (is_shop()) {
            return 'Shop';
        } elseif (is_product_category()) {
            $category = get_queried_object();
            return 'Category: ' . $category->name;
        } elseif (is_product_tag()) {
            $tag = get_queried_object();
            return 'Tag: ' . $tag->name;
        } elseif (is_search()) {
            return 'Search Results';
        } else {
            return 'Product List';
        }
    }
    
    /**
     * Get registration source
     */
    private function getRegistrationSource() {
        $referer = wp_get_referer();
        
        if (strpos($referer, 'checkout') !== false) {
            return 'checkout';
        } elseif (strpos($referer, 'my-account') !== false) {
            return 'my_account';
        } else {
            return 'direct';
        }
    }
    
    /**
     * Get checkout duration
     */
    private function getCheckoutDuration() {
        $checkout_start = WC()->session->get('checkout_start_time');
        
        if ($checkout_start) {
            return time() - $checkout_start;
        }
        
        return null;
    }
    
    /**
     * Check if event is critical and should be sent immediately
     */
    private function isCriticalEvent($event_type) {
        $critical_events = array(
            'order_created',
            'payment_completed',
            'checkout_completed',
            'user_registration'
        );
        
        return in_array($event_type, $critical_events);
    }
}

// Add custom cron schedule for 5 minutes
add_filter('cron_schedules', function($schedules) {
    $schedules['aaw_5min'] = array(
        'interval' => 300,
        'display' => __('Every 5 Minutes', 'advanced-analytics-woocommerce')
    );
    return $schedules;
});
?>