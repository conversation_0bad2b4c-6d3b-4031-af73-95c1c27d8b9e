"""
Amazon Marketplace Analytics Connector
Comprehensive Amazon Seller Central and Advertising API integration with real-time data sync
"""

import asyncio
import json
import logging
import time
import hashlib
import hmac
import base64
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
import aiohttp
import asyncpg
import redis.asyncio as redis
from xml.etree import ElementTree as ET
import gzip
import io
import pandas as pd
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives import serialization
import boto3
from botocore.exceptions import ClientError
import requests_aws4auth
from sqlalchemy import create_engine, Column, String, Integer, DateTime, Text, Boolean, Float
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

Base = declarative_base()

@dataclass
class AmazonCredentials:
    """Amazon API credentials"""
    refresh_token: str
    client_id: str
    client_secret: str
    aws_access_key: str
    aws_secret_key: str
    region: str = "us-east-1"
    marketplace_id: str = "ATVPDKIKX0DER"  # US marketplace
    
@dataclass
class AmazonOrder:
    """Amazon order data structure"""
    order_id: str
    purchase_date: datetime
    order_status: str
    fulfillment_channel: str
    sales_channel: str
    order_total: float
    currency: str
    number_of_items_shipped: int
    number_of_items_unshipped: int
    payment_method: str
    marketplace_id: str
    shipment_service_level_category: str
    order_type: str
    earliest_ship_date: Optional[datetime] = None
    latest_ship_date: Optional[datetime] = None
    earliest_delivery_date: Optional[datetime] = None
    latest_delivery_date: Optional[datetime] = None
    is_business_order: bool = False
    is_prime: bool = False
    is_premium_order: bool = False
    is_global_express_enabled: bool = False
    buyer_info: Optional[Dict[str, Any]] = None
    shipping_address: Optional[Dict[str, Any]] = None

@dataclass
class AmazonProduct:
    """Amazon product data structure"""
    asin: str
    sku: str
    title: str
    brand: str
    manufacturer: str
    product_group: str
    product_type: str
    current_price: float
    currency: str
    availability: str
    sales_rank: Optional[int] = None
    parent_asin: Optional[str] = None
    variations: List[str] = None
    images: List[str] = None
    features: List[str] = None
    dimensions: Optional[Dict[str, Any]] = None
    weight: Optional[float] = None

@dataclass
class AmazonInventory:
    """Amazon inventory data structure"""
    sku: str
    asin: str
    condition: str
    total_quantity: int
    inbound_working_quantity: int
    inbound_shipped_quantity: int
    inbound_receiving_quantity: int
    reserved_quantity: int
    fulfillable_quantity: int
    unfulfillable_quantity: int
    last_updated: datetime

# Database models
class AmazonOrderModel(Base):
    __tablename__ = 'amazon_orders'
    
    order_id = Column(String, primary_key=True)
    seller_id = Column(String, index=True)
    purchase_date = Column(DateTime)
    order_status = Column(String)
    fulfillment_channel = Column(String)
    sales_channel = Column(String)
    order_total = Column(Float)
    currency = Column(String)
    number_of_items_shipped = Column(Integer)
    number_of_items_unshipped = Column(Integer)
    payment_method = Column(String)
    marketplace_id = Column(String)
    raw_data = Column(Text)
    synced_at = Column(DateTime)
    created_at = Column(DateTime, default=datetime.utcnow)

class AmazonProductModel(Base):
    __tablename__ = 'amazon_products'
    
    asin = Column(String, primary_key=True)
    seller_id = Column(String, index=True)
    sku = Column(String)
    title = Column(String)
    brand = Column(String)
    current_price = Column(Float)
    currency = Column(String)
    availability = Column(String)
    sales_rank = Column(Integer)
    raw_data = Column(Text)
    last_updated = Column(DateTime, default=datetime.utcnow)

class AmazonInventoryModel(Base):
    __tablename__ = 'amazon_inventory'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    seller_id = Column(String, index=True)
    sku = Column(String)
    asin = Column(String)
    condition = Column(String)
    total_quantity = Column(Integer)
    fulfillable_quantity = Column(Integer)
    unfulfillable_quantity = Column(Integer)
    snapshot_date = Column(DateTime, default=datetime.utcnow)

class AmazonAPIClient:
    """Amazon Selling Partner API client"""
    
    def __init__(self, credentials: AmazonCredentials):
        self.credentials = credentials
        self.base_url = "https://sellingpartnerapi-na.amazon.com"
        self.access_token = None
        self.token_expires_at = None
        self.session = None
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        await self.authenticate()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def authenticate(self):
        """Authenticate with Amazon LWA (Login with Amazon)"""
        try:
            auth_url = "https://api.amazon.com/auth/o2/token"
            
            payload = {
                'grant_type': 'refresh_token',
                'refresh_token': self.credentials.refresh_token,
                'client_id': self.credentials.client_id,
                'client_secret': self.credentials.client_secret
            }
            
            async with self.session.post(auth_url, data=payload) as response:
                if response.status == 200:
                    data = await response.json()
                    self.access_token = data['access_token']
                    self.token_expires_at = time.time() + data['expires_in'] - 60  # 60s buffer
                    logger.info("Amazon API authentication successful")
                else:
                    error_text = await response.text()
                    logger.error(f"Amazon API authentication failed: {response.status} - {error_text}")
                    raise Exception(f"Authentication failed: {response.status}")
                    
        except Exception as e:
            logger.error(f"Authentication error: {e}")
            raise
    
    async def ensure_authenticated(self):
        """Ensure we have a valid access token"""
        if not self.access_token or time.time() >= self.token_expires_at:
            await self.authenticate()
    
    def get_auth_headers(self):
        """Get authentication headers for API requests"""
        return {
            'Authorization': f'Bearer {self.access_token}',
            'x-amz-access-token': self.access_token,
            'Content-Type': 'application/json'
        }
    
    async def make_request(self, method: str, endpoint: str, params: Dict = None, data: Dict = None) -> Dict:
        """Make authenticated request to Amazon API"""
        await self.ensure_authenticated()
        
        url = f"{self.base_url}{endpoint}"
        headers = self.get_auth_headers()
        
        try:
            async with self.session.request(method, url, headers=headers, params=params, json=data) as response:
                if response.status == 200:
                    return await response.json()
                elif response.status == 429:
                    # Rate limited, wait and retry
                    await asyncio.sleep(1)
                    return await self.make_request(method, endpoint, params, data)
                else:
                    error_text = await response.text()
                    logger.error(f"API request failed: {response.status} - {error_text}")
                    raise Exception(f"API request failed: {response.status}")
                    
        except Exception as e:
            logger.error(f"Request error: {e}")
            raise
    
    async def get_orders(self, created_after: datetime, created_before: Optional[datetime] = None) -> List[AmazonOrder]:
        """Fetch orders from Amazon"""
        params = {
            'MarketplaceIds': self.credentials.marketplace_id,
            'CreatedAfter': created_after.isoformat(),
            'MaxResultsPerPage': 100
        }
        
        if created_before:
            params['CreatedBefore'] = created_before.isoformat()
        
        orders = []
        next_token = None
        
        while True:
            if next_token:
                params['NextToken'] = next_token
            
            response = await self.make_request('GET', '/orders/v0/orders', params=params)
            
            if 'Orders' in response:
                for order_data in response['Orders']:
                    order = self.parse_order(order_data)
                    orders.append(order)
            
            next_token = response.get('NextToken')
            if not next_token:
                break
                
            # Rate limiting
            await asyncio.sleep(0.5)
        
        return orders
    
    async def get_order_items(self, order_id: str) -> List[Dict]:
        """Fetch order items for a specific order"""
        endpoint = f"/orders/v0/orders/{order_id}/orderItems"
        response = await self.make_request('GET', endpoint)
        return response.get('OrderItems', [])
    
    async def get_products(self, asins: List[str]) -> List[AmazonProduct]:
        """Fetch product information"""
        products = []
        
        # Amazon API limits to 20 ASINs per request
        for i in range(0, len(asins), 20):
            batch_asins = asins[i:i+20]
            params = {
                'MarketplaceId': self.credentials.marketplace_id,
                'Asins': ','.join(batch_asins)
            }
            
            response = await self.make_request('GET', '/catalog/v0/items', params=params)
            
            if 'Items' in response:
                for item_data in response['Items']:
                    product = self.parse_product(item_data)
                    products.append(product)
            
            await asyncio.sleep(0.5)  # Rate limiting
        
        return products
    
    async def get_inventory(self) -> List[AmazonInventory]:
        """Fetch inventory information"""
        params = {
            'MarketplaceIds': self.credentials.marketplace_id,
            'details': True
        }
        
        inventory_items = []
        next_token = None
        
        while True:
            if next_token:
                params['NextToken'] = next_token
            
            response = await self.make_request('GET', '/fba/inventory/v1/summaries', params=params)
            
            if 'InventorySummaries' in response:
                for item_data in response['InventorySummaries']:
                    inventory = self.parse_inventory(item_data)
                    inventory_items.append(inventory)
            
            next_token = response.get('NextToken')
            if not next_token:
                break
                
            await asyncio.sleep(0.5)
        
        return inventory_items
    
    async def get_sales_reports(self, start_date: datetime, end_date: datetime) -> Dict:
        """Fetch sales and traffic reports"""
        params = {
            'reportType': 'GET_MERCHANT_LISTINGS_ALL_DATA',
            'dataStartTime': start_date.isoformat(),
            'dataEndTime': end_date.isoformat(),
            'marketplaceIds': self.credentials.marketplace_id
        }
        
        # Request report
        response = await self.make_request('POST', '/reports/2021-06-30/reports', data=params)
        report_id = response.get('reportId')
        
        if not report_id:
            return {}
        
        # Wait for report to be ready
        while True:
            status_response = await self.make_request('GET', f'/reports/2021-06-30/reports/{report_id}')
            
            if status_response.get('processingStatus') == 'DONE':
                document_id = status_response.get('reportDocumentId')
                break
            elif status_response.get('processingStatus') in ['CANCELLED', 'FATAL']:
                logger.error(f"Report generation failed: {status_response}")
                return {}
            
            await asyncio.sleep(30)  # Check every 30 seconds
        
        # Download report
        document_response = await self.make_request('GET', f'/reports/2021-06-30/documents/{document_id}')
        
        if 'url' in document_response:
            # Download and parse the report
            async with self.session.get(document_response['url']) as response:
                content = await response.read()
                
                # Handle compression if needed
                if document_response.get('compressionAlgorithm') == 'GZIP':
                    content = gzip.decompress(content)
                
                # Parse CSV data
                df = pd.read_csv(io.StringIO(content.decode('utf-8')), sep='\t')
                return df.to_dict('records')
        
        return {}
    
    def parse_order(self, order_data: Dict) -> AmazonOrder:
        """Parse order data from API response"""
        return AmazonOrder(
            order_id=order_data.get('AmazonOrderId'),
            purchase_date=datetime.fromisoformat(order_data.get('PurchaseDate', '').replace('Z', '+00:00')),
            order_status=order_data.get('OrderStatus'),
            fulfillment_channel=order_data.get('FulfillmentChannel'),
            sales_channel=order_data.get('SalesChannel'),
            order_total=float(order_data.get('OrderTotal', {}).get('Amount', 0)),
            currency=order_data.get('OrderTotal', {}).get('CurrencyCode', 'USD'),
            number_of_items_shipped=int(order_data.get('NumberOfItemsShipped', 0)),
            number_of_items_unshipped=int(order_data.get('NumberOfItemsUnshipped', 0)),
            payment_method=order_data.get('PaymentMethod'),
            marketplace_id=order_data.get('MarketplaceId'),
            shipment_service_level_category=order_data.get('ShipmentServiceLevelCategory'),
            order_type=order_data.get('OrderType'),
            is_business_order=order_data.get('IsBusinessOrder', False),
            is_prime=order_data.get('IsPrime', False),
            is_premium_order=order_data.get('IsPremiumOrder', False)
        )
    
    def parse_product(self, product_data: Dict) -> AmazonProduct:
        """Parse product data from API response"""
        attributes = product_data.get('AttributeSets', [{}])[0]
        
        return AmazonProduct(
            asin=product_data.get('Identifiers', {}).get('MarketplaceASIN', {}).get('ASIN'),
            sku=product_data.get('SellerSKU'),
            title=attributes.get('Title', ''),
            brand=attributes.get('Brand', ''),
            manufacturer=attributes.get('Manufacturer', ''),
            product_group=attributes.get('ProductGroup', ''),
            product_type=attributes.get('ProductTypeName', ''),
            current_price=0.0,  # Price needs to be fetched separately
            currency='USD',
            availability='Unknown'
        )
    
    def parse_inventory(self, inventory_data: Dict) -> AmazonInventory:
        """Parse inventory data from API response"""
        return AmazonInventory(
            sku=inventory_data.get('sellerSku'),
            asin=inventory_data.get('asin'),
            condition=inventory_data.get('condition'),
            total_quantity=inventory_data.get('totalQuantity', 0),
            inbound_working_quantity=inventory_data.get('inboundWorkingQuantity', 0),
            inbound_shipped_quantity=inventory_data.get('inboundShippedQuantity', 0),
            inbound_receiving_quantity=inventory_data.get('inboundReceivingQuantity', 0),
            reserved_quantity=inventory_data.get('reservedQuantity', 0),
            fulfillable_quantity=inventory_data.get('fulfillableQuantity', 0),
            unfulfillable_quantity=inventory_data.get('unfulfillableQuantity', 0),
            last_updated=datetime.utcnow()
        )

class AmazonAnalyticsConnector:
    """Main Amazon analytics connector"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.credentials = AmazonCredentials(**config['amazon_credentials'])
        self.analytics_api_url = config['analytics_api_url']
        self.analytics_api_key = config['analytics_api_key']
        
        # Database setup
        self.db_engine = create_engine(config['database_url'])
        Base.metadata.create_all(self.db_engine)
        self.db_session = sessionmaker(bind=self.db_engine)
        
        # Redis setup
        self.redis_client = None
        
        # API client
        self.api_client = None
        
    async def initialize(self):
        """Initialize async components"""
        self.redis_client = redis.from_url(self.config['redis_url'])
        self.api_client = AmazonAPIClient(self.credentials)
        
        logger.info("Amazon Analytics Connector initialized")
    
    async def sync_orders(self, days_back: int = 7) -> int:
        """Sync orders from Amazon"""
        start_date = datetime.utcnow() - timedelta(days=days_back)
        
        try:
            async with self.api_client as client:
                orders = await client.get_orders(start_date)
                
                session = self.db_session()
                synced_count = 0
                
                for order in orders:
                    # Check if order already exists
                    existing = session.query(AmazonOrderModel).filter_by(order_id=order.order_id).first()
                    
                    if not existing:
                        # Create new order record
                        order_model = AmazonOrderModel(
                            order_id=order.order_id,
                            seller_id=self.credentials.client_id,
                            purchase_date=order.purchase_date,
                            order_status=order.order_status,
                            fulfillment_channel=order.fulfillment_channel,
                            sales_channel=order.sales_channel,
                            order_total=order.order_total,
                            currency=order.currency,
                            number_of_items_shipped=order.number_of_items_shipped,
                            number_of_items_unshipped=order.number_of_items_unshipped,
                            payment_method=order.payment_method,
                            marketplace_id=order.marketplace_id,
                            raw_data=json.dumps(asdict(order), default=str),
                            synced_at=datetime.utcnow()
                        )
                        
                        session.add(order_model)
                        synced_count += 1
                        
                        # Send to analytics platform
                        await self.send_order_event(order)
                        
                        # Get order items
                        try:
                            order_items = await client.get_order_items(order.order_id)
                            await self.send_order_items_event(order.order_id, order_items)
                        except Exception as e:
                            logger.warning(f"Failed to fetch order items for {order.order_id}: {e}")
                
                session.commit()
                session.close()
                
                logger.info(f"Synced {synced_count} new orders from Amazon")
                return synced_count
                
        except Exception as e:
            logger.error(f"Failed to sync orders: {e}")
            return 0
    
    async def sync_products(self, asins: List[str] = None) -> int:
        """Sync product information"""
        if not asins:
            # Get ASINs from recent orders
            session = self.db_session()
            recent_orders = session.query(AmazonOrderModel).filter(
                AmazonOrderModel.purchase_date >= datetime.utcnow() - timedelta(days=30)
            ).all()
            session.close()
            
            asins = []
            for order in recent_orders:
                try:
                    order_data = json.loads(order.raw_data)
                    # Extract ASINs from order items (would need to be stored)
                except:
                    continue
        
        if not asins:
            logger.warning("No ASINs found to sync")
            return 0
        
        try:
            async with self.api_client as client:
                products = await client.get_products(asins)
                
                session = self.db_session()
                synced_count = 0
                
                for product in products:
                    if not product.asin:
                        continue
                        
                    # Update or create product record
                    existing = session.query(AmazonProductModel).filter_by(asin=product.asin).first()
                    
                    if existing:
                        existing.title = product.title
                        existing.brand = product.brand
                        existing.current_price = product.current_price
                        existing.availability = product.availability
                        existing.sales_rank = product.sales_rank
                        existing.raw_data = json.dumps(asdict(product), default=str)
                        existing.last_updated = datetime.utcnow()
                    else:
                        product_model = AmazonProductModel(
                            asin=product.asin,
                            seller_id=self.credentials.client_id,
                            sku=product.sku,
                            title=product.title,
                            brand=product.brand,
                            current_price=product.current_price,
                            currency=product.currency,
                            availability=product.availability,
                            sales_rank=product.sales_rank,
                            raw_data=json.dumps(asdict(product), default=str)
                        )
                        session.add(product_model)
                        synced_count += 1
                    
                    # Send to analytics platform
                    await self.send_product_event(product)
                
                session.commit()
                session.close()
                
                logger.info(f"Synced {synced_count} products from Amazon")
                return synced_count
                
        except Exception as e:
            logger.error(f"Failed to sync products: {e}")
            return 0
    
    async def sync_inventory(self) -> int:
        """Sync inventory information"""
        try:
            async with self.api_client as client:
                inventory_items = await client.get_inventory()
                
                session = self.db_session()
                synced_count = 0
                
                for inventory in inventory_items:
                    inventory_model = AmazonInventoryModel(
                        seller_id=self.credentials.client_id,
                        sku=inventory.sku,
                        asin=inventory.asin,
                        condition=inventory.condition,
                        total_quantity=inventory.total_quantity,
                        fulfillable_quantity=inventory.fulfillable_quantity,
                        unfulfillable_quantity=inventory.unfulfillable_quantity
                    )
                    
                    session.add(inventory_model)
                    synced_count += 1
                    
                    # Send to analytics platform
                    await self.send_inventory_event(inventory)
                
                session.commit()
                session.close()
                
                logger.info(f"Synced {synced_count} inventory items from Amazon")
                return synced_count
                
        except Exception as e:
            logger.error(f"Failed to sync inventory: {e}")
            return 0
    
    async def send_order_event(self, order: AmazonOrder):
        """Send order event to analytics platform"""
        event_data = {
            'event_type': 'amazon_order_created',
            'platform': 'amazon',
            'seller_id': self.credentials.client_id,
            'marketplace_id': self.credentials.marketplace_id,
            'timestamp': order.purchase_date.isoformat(),
            'data': asdict(order)
        }
        
        await self.send_to_analytics(event_data)
    
    async def send_order_items_event(self, order_id: str, order_items: List[Dict]):
        """Send order items event to analytics platform"""
        event_data = {
            'event_type': 'amazon_order_items',
            'platform': 'amazon',
            'seller_id': self.credentials.client_id,
            'order_id': order_id,
            'timestamp': datetime.utcnow().isoformat(),
            'data': {
                'order_id': order_id,
                'items': order_items
            }
        }
        
        await self.send_to_analytics(event_data)
    
    async def send_product_event(self, product: AmazonProduct):
        """Send product event to analytics platform"""
        event_data = {
            'event_type': 'amazon_product_updated',
            'platform': 'amazon',
            'seller_id': self.credentials.client_id,
            'timestamp': datetime.utcnow().isoformat(),
            'data': asdict(product)
        }
        
        await self.send_to_analytics(event_data)
    
    async def send_inventory_event(self, inventory: AmazonInventory):
        """Send inventory event to analytics platform"""
        event_data = {
            'event_type': 'amazon_inventory_updated',
            'platform': 'amazon',
            'seller_id': self.credentials.client_id,
            'timestamp': inventory.last_updated.isoformat(),
            'data': asdict(inventory)
        }
        
        await self.send_to_analytics(event_data)
    
    async def send_to_analytics(self, event_data: Dict):
        """Send event to analytics platform"""
        try:
            headers = {
                'X-API-Key': self.analytics_api_key,
                'Content-Type': 'application/json'
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.analytics_api_url}/events",
                    json=event_data,
                    headers=headers
                ) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        logger.error(f"Failed to send event to analytics: {response.status} - {error_text}")
                        
                        # Store failed event for retry
                        await self.redis_client.lpush(
                            'failed_events',
                            json.dumps(event_data)
                        )
        
        except Exception as e:
            logger.error(f"Error sending event to analytics: {e}")
            
            # Store failed event for retry
            await self.redis_client.lpush(
                'failed_events',
                json.dumps(event_data)
            )
    
    async def retry_failed_events(self):
        """Retry sending failed events"""
        retry_count = 0
        
        while True:
            event_json = await self.redis_client.rpop('failed_events')
            if not event_json:
                break
            
            try:
                event_data = json.loads(event_json)
                await self.send_to_analytics(event_data)
                retry_count += 1
            except Exception as e:
                logger.error(f"Failed to retry event: {e}")
                # Put back in queue for later
                await self.redis_client.lpush('failed_events', event_json)
                break
        
        if retry_count > 0:
            logger.info(f"Retried {retry_count} failed events")
    
    async def run_sync_cycle(self):
        """Run a complete sync cycle"""
        logger.info("Starting Amazon sync cycle")
        
        try:
            # Sync orders
            orders_synced = await self.sync_orders()
            
            # Sync inventory
            inventory_synced = await self.sync_inventory()
            
            # Retry failed events
            await self.retry_failed_events()
            
            logger.info(f"Sync cycle completed: {orders_synced} orders, {inventory_synced} inventory items")
            
        except Exception as e:
            logger.error(f"Sync cycle failed: {e}")
    
    async def start_continuous_sync(self, interval_minutes: int = 30):
        """Start continuous sync process"""
        logger.info(f"Starting continuous sync every {interval_minutes} minutes")
        
        while True:
            await self.run_sync_cycle()
            await asyncio.sleep(interval_minutes * 60)
    
    async def cleanup(self):
        """Cleanup resources"""
        if self.redis_client:
            await self.redis_client.close()
        
        logger.info("Amazon Analytics Connector cleaned up")

# Main execution
async def main():
    # Configuration
    config = {
        'amazon_credentials': {
            'refresh_token': 'your_refresh_token',
            'client_id': 'your_client_id',
            'client_secret': 'your_client_secret',
            'aws_access_key': 'your_aws_access_key',
            'aws_secret_key': 'your_aws_secret_key',
            'region': 'us-east-1',
            'marketplace_id': 'ATVPDKIKX0DER'
        },
        'analytics_api_url': 'https://api.analytics-platform.com',
        'analytics_api_key': 'your_analytics_api_key',
        'database_url': 'postgresql://user:password@localhost/amazon_analytics',
        'redis_url': 'redis://localhost:6379'
    }
    
    # Initialize connector
    connector = AmazonAnalyticsConnector(config)
    await connector.initialize()
    
    try:
        # Run initial sync
        await connector.run_sync_cycle()
        
        # Start continuous sync (uncomment for production)
        # await connector.start_continuous_sync(interval_minutes=30)
        
    finally:
        await connector.cleanup()

if __name__ == "__main__":
    asyncio.run(main())