"""
eBay Marketplace Analytics Connector
Comprehensive eBay API integration with real-time data sync and analytics
"""

import asyncio
import json
import logging
import time
import hashlib
import base64
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
import aiohttp
import asyncpg
import redis.asyncio as redis
from xml.etree import ElementTree as ET
import pandas as pd
from sqlalchemy import create_engine, Column, String, Integer, DateTime, Text, Boolean, Float
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import requests
from requests_oauthlib import OAuth1Session

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

Base = declarative_base()

@dataclass
class eBayCredentials:
    """eBay API credentials"""
    app_id: str
    dev_id: str
    cert_id: str
    user_token: str
    site_id: str = "0"  # US site
    sandbox: bool = False
    
    @property
    def base_url(self) -> str:
        if self.sandbox:
            return "https://api.sandbox.ebay.com"
        return "https://api.ebay.com"

@dataclass
class eBayListing:
    """eBay listing data structure"""
    item_id: str
    title: str
    sku: str
    category_id: str
    listing_type: str
    quantity: int
    quantity_sold: int
    start_price: float
    current_price: float
    currency: str
    condition: str
    listing_status: str
    start_time: datetime
    end_time: datetime
    site_id: str
    seller_id: str
    view_count: int = 0
    watch_count: int = 0
    best_offer_enabled: bool = False
    buy_it_now_price: Optional[float] = None
    reserve_price: Optional[float] = None
    shipping_cost: Optional[float] = None
    handling_time: Optional[int] = None
    return_policy: Optional[Dict[str, Any]] = None
    pictures: List[str] = None

@dataclass
class eBayOrder:
    """eBay order data structure"""
    order_id: str
    order_status: str
    creation_time: datetime
    payment_status: str
    shipping_status: str
    buyer_user_id: str
    buyer_email: str
    total_amount: float
    currency: str
    payment_method: str
    shipping_address: Dict[str, Any]
    billing_address: Optional[Dict[str, Any]]
    line_items: List[Dict[str, Any]]
    shipping_cost: float
    tax_amount: float
    order_fulfillment_status: str
    last_modified_time: datetime
    checkout_status: str

@dataclass
class eBaySale:
    """eBay sale transaction data"""
    transaction_id: str
    item_id: str
    order_id: Optional[str]
    buyer_user_id: str
    quantity_purchased: int
    transaction_price: float
    currency: str
    transaction_site_id: str
    platform: str
    creation_time: datetime
    paid_time: Optional[datetime]
    shipped_time: Optional[datetime]
    feedback_left: bool
    feedback_received: bool
    seller_id: str

# Database models
class eBayListingModel(Base):
    __tablename__ = 'ebay_listings'
    
    item_id = Column(String, primary_key=True)
    seller_id = Column(String, index=True)
    title = Column(String)
    sku = Column(String)
    category_id = Column(String)
    listing_type = Column(String)
    quantity = Column(Integer)
    quantity_sold = Column(Integer)
    start_price = Column(Float)
    current_price = Column(Float)
    currency = Column(String)
    condition = Column(String)
    listing_status = Column(String)
    start_time = Column(DateTime)
    end_time = Column(DateTime)
    site_id = Column(String)
    view_count = Column(Integer, default=0)
    watch_count = Column(Integer, default=0)
    raw_data = Column(Text)
    last_updated = Column(DateTime, default=datetime.utcnow)

class eBayOrderModel(Base):
    __tablename__ = 'ebay_orders'
    
    order_id = Column(String, primary_key=True)
    seller_id = Column(String, index=True)
    order_status = Column(String)
    creation_time = Column(DateTime)
    payment_status = Column(String)
    shipping_status = Column(String)
    buyer_user_id = Column(String)
    buyer_email = Column(String)
    total_amount = Column(Float)
    currency = Column(String)
    payment_method = Column(String)
    shipping_cost = Column(Float)
    tax_amount = Column(Float)
    raw_data = Column(Text)
    synced_at = Column(DateTime)
    created_at = Column(DateTime, default=datetime.utcnow)

class eBaySaleModel(Base):
    __tablename__ = 'ebay_sales'
    
    transaction_id = Column(String, primary_key=True)
    seller_id = Column(String, index=True)
    item_id = Column(String)
    order_id = Column(String)
    buyer_user_id = Column(String)
    quantity_purchased = Column(Integer)
    transaction_price = Column(Float)
    currency = Column(String)
    creation_time = Column(DateTime)
    paid_time = Column(DateTime)
    shipped_time = Column(DateTime)
    raw_data = Column(Text)
    synced_at = Column(DateTime)

class eBayAPIClient:
    """eBay API client"""
    
    def __init__(self, credentials: eBayCredentials):
        self.credentials = credentials
        self.session = None
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    def get_headers(self, call_name: str) -> Dict[str, str]:
        """Get headers for eBay API request"""
        return {
            'X-EBAY-API-COMPATIBILITY-LEVEL': '967',
            'X-EBAY-API-DEV-NAME': self.credentials.dev_id,
            'X-EBAY-API-APP-NAME': self.credentials.app_id,
            'X-EBAY-API-CERT-NAME': self.credentials.cert_id,
            'X-EBAY-API-CALL-NAME': call_name,
            'X-EBAY-API-SITEID': self.credentials.site_id,
            'Content-Type': 'text/xml'
        }
    
    def build_request_xml(self, call_name: str, request_data: Dict) -> str:
        """Build XML request for eBay API"""
        xml_parts = [
            '<?xml version="1.0" encoding="utf-8"?>',
            f'<{call_name}Request xmlns="urn:ebay:apis:eBLBaseComponents">',
            '<RequesterCredentials>',
            f'<eBayAuthToken>{self.credentials.user_token}</eBayAuthToken>',
            '</RequesterCredentials>'
        ]
        
        # Add request-specific data
        xml_parts.extend(self._dict_to_xml(request_data))
        
        xml_parts.append(f'</{call_name}Request>')
        
        return ''.join(xml_parts)
    
    def _dict_to_xml(self, data: Dict, parent_key: str = '') -> List[str]:
        """Convert dictionary to XML elements"""
        xml_parts = []
        
        for key, value in data.items():
            if isinstance(value, dict):
                xml_parts.append(f'<{key}>')
                xml_parts.extend(self._dict_to_xml(value))
                xml_parts.append(f'</{key}>')
            elif isinstance(value, list):
                for item in value:
                    if isinstance(item, dict):
                        xml_parts.append(f'<{key}>')
                        xml_parts.extend(self._dict_to_xml(item))
                        xml_parts.append(f'</{key}>')
                    else:
                        xml_parts.append(f'<{key}>{item}</{key}>')
            else:
                xml_parts.append(f'<{key}>{value}</{key}>')
        
        return xml_parts
    
    async def make_request(self, call_name: str, request_data: Dict) -> Dict:
        """Make request to eBay API"""
        url = f"{self.credentials.base_url}/ws/api.dll"
        headers = self.get_headers(call_name)
        xml_data = self.build_request_xml(call_name, request_data)
        
        try:
            async with self.session.post(url, headers=headers, data=xml_data) as response:
                if response.status == 200:
                    xml_content = await response.text()
                    return self._parse_xml_response(xml_content)
                else:
                    error_text = await response.text()
                    logger.error(f"eBay API request failed: {response.status} - {error_text}")
                    raise Exception(f"API request failed: {response.status}")
                    
        except Exception as e:
            logger.error(f"Request error: {e}")
            raise
    
    def _parse_xml_response(self, xml_content: str) -> Dict:
        """Parse XML response from eBay API"""
        try:
            root = ET.fromstring(xml_content)
            return self._xml_to_dict(root)
        except ET.ParseError as e:
            logger.error(f"XML parsing error: {e}")
            raise
    
    def _xml_to_dict(self, element) -> Union[Dict, str, List]:
        """Convert XML element to dictionary"""
        if len(element) == 0:
            return element.text or ''
        
        result = {}
        for child in element:
            tag = child.tag.split('}')[-1] if '}' in child.tag else child.tag
            
            if tag in result:
                # Handle multiple elements with same tag
                if not isinstance(result[tag], list):
                    result[tag] = [result[tag]]
                result[tag].append(self._xml_to_dict(child))
            else:
                result[tag] = self._xml_to_dict(child)
        
        return result
    
    async def get_my_ebay_selling(self, page_number: int = 1, per_page: int = 200) -> Dict:
        """Get seller's active listings"""
        request_data = {
            'ActiveList': {
                'Include': 'true',
                'Pagination': {
                    'EntriesPerPage': str(per_page),
                    'PageNumber': str(page_number)
                }
            },
            'SoldList': {
                'Include': 'true',
                'Pagination': {
                    'EntriesPerPage': str(per_page),
                    'PageNumber': str(page_number)
                }
            },
            'DetailLevel': 'ReturnAll'
        }
        
        return await self.make_request('GetMyeBaySelling', request_data)
    
    async def get_orders(self, days_back: int = 7, page_number: int = 1) -> Dict:
        """Get orders from eBay"""
        end_time = datetime.utcnow()
        start_time = end_time - timedelta(days=days_back)
        
        request_data = {
            'CreateTimeFrom': start_time.strftime('%Y-%m-%dT%H:%M:%S.000Z'),
            'CreateTimeTo': end_time.strftime('%Y-%m-%dT%H:%M:%S.000Z'),
            'OrderRole': 'Seller',
            'OrderStatus': 'All',
            'Pagination': {
                'EntriesPerPage': '100',
                'PageNumber': str(page_number)
            }
        }
        
        return await self.make_request('GetOrders', request_data)
    
    async def get_seller_transactions(self, days_back: int = 7, page_number: int = 1) -> Dict:
        """Get seller transactions"""
        end_time = datetime.utcnow()
        start_time = end_time - timedelta(days=days_back)
        
        request_data = {
            'ModTimeFrom': start_time.strftime('%Y-%m-%dT%H:%M:%S.000Z'),
            'ModTimeTo': end_time.strftime('%Y-%m-%dT%H:%M:%S.000Z'),
            'Pagination': {
                'EntriesPerPage': '200',
                'PageNumber': str(page_number)
            },
            'IncludeFinalValueFee': 'true',
            'IncludeContainingOrder': 'true'
        }
        
        return await self.make_request('GetSellerTransactions', request_data)
    
    async def get_item_details(self, item_id: str) -> Dict:
        """Get detailed item information"""
        request_data = {
            'ItemID': item_id,
            'DetailLevel': 'ReturnAll',
            'IncludeItemSpecifics': 'true'
        }
        
        return await self.make_request('GetItem', request_data)
    
    async def get_seller_events(self, days_back: int = 1) -> Dict:
        """Get seller events (listing changes, etc.)"""
        end_time = datetime.utcnow()
        start_time = end_time - timedelta(days=days_back)
        
        request_data = {
            'StartTimeFrom': start_time.strftime('%Y-%m-%dT%H:%M:%S.000Z'),
            'StartTimeTo': end_time.strftime('%Y-%m-%dT%H:%M:%S.000Z'),
            'UserID': 'seller_user_id'  # Would be configured
        }
        
        return await self.make_request('GetSellerEvents', request_data)

class eBayAnalyticsConnector:
    """Main eBay analytics connector"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.credentials = eBayCredentials(**config['ebay_credentials'])
        self.analytics_api_url = config['analytics_api_url']
        self.analytics_api_key = config['analytics_api_key']
        
        # Database setup
        self.db_engine = create_engine(config['database_url'])
        Base.metadata.create_all(self.db_engine)
        self.db_session = sessionmaker(bind=self.db_engine)
        
        # Redis setup
        self.redis_client = None
        
        # API client
        self.api_client = None
        
    async def initialize(self):
        """Initialize async components"""
        self.redis_client = redis.from_url(self.config['redis_url'])
        self.api_client = eBayAPIClient(self.credentials)
        
        logger.info("eBay Analytics Connector initialized")
    
    async def sync_listings(self) -> int:
        """Sync active and sold listings"""
        try:
            async with self.api_client as client:
                synced_count = 0
                page = 1
                
                while True:
                    response = await client.get_my_ebay_selling(page_number=page)
                    
                    # Process active listings
                    active_list = response.get('ActiveList', {})
                    if 'ItemArray' in active_list and 'Item' in active_list['ItemArray']:
                        items = active_list['ItemArray']['Item']
                        if not isinstance(items, list):
                            items = [items]
                        
                        for item in items:
                            listing = self.parse_listing(item)
                            await self.save_listing(listing)
                            await self.send_listing_event(listing)
                            synced_count += 1
                    
                    # Process sold listings
                    sold_list = response.get('SoldList', {})
                    if 'OrderTransactionArray' in sold_list and 'OrderTransaction' in sold_list['OrderTransactionArray']:
                        transactions = sold_list['OrderTransactionArray']['OrderTransaction']
                        if not isinstance(transactions, list):
                            transactions = [transactions]
                        
                        for transaction in transactions:
                            if 'Transaction' in transaction:
                                sale = self.parse_sale(transaction['Transaction'])
                                await self.save_sale(sale)
                                await self.send_sale_event(sale)
                                synced_count += 1
                    
                    # Check if there are more pages
                    pagination = active_list.get('PaginationResult', {})
                    total_pages = int(pagination.get('TotalNumberOfPages', 1))
                    
                    if page >= total_pages:
                        break
                    
                    page += 1
                    await asyncio.sleep(1)  # Rate limiting
                
                logger.info(f"Synced {synced_count} listings from eBay")
                return synced_count
                
        except Exception as e:
            logger.error(f"Failed to sync listings: {e}")
            return 0
    
    async def sync_orders(self, days_back: int = 7) -> int:
        """Sync orders from eBay"""
        try:
            async with self.api_client as client:
                synced_count = 0
                page = 1
                
                while True:
                    response = await client.get_orders(days_back=days_back, page_number=page)
                    
                    if 'OrderArray' in response and 'Order' in response['OrderArray']:
                        orders = response['OrderArray']['Order']
                        if not isinstance(orders, list):
                            orders = [orders]
                        
                        for order_data in orders:
                            order = self.parse_order(order_data)
                            await self.save_order(order)
                            await self.send_order_event(order)
                            synced_count += 1
                    
                    # Check pagination
                    pagination = response.get('PaginationResult', {})
                    total_pages = int(pagination.get('TotalNumberOfPages', 1))
                    
                    if page >= total_pages:
                        break
                    
                    page += 1
                    await asyncio.sleep(1)  # Rate limiting
                
                logger.info(f"Synced {synced_count} orders from eBay")
                return synced_count
                
        except Exception as e:
            logger.error(f"Failed to sync orders: {e}")
            return 0
    
    async def sync_transactions(self, days_back: int = 7) -> int:
        """Sync seller transactions"""
        try:
            async with self.api_client as client:
                synced_count = 0
                page = 1
                
                while True:
                    response = await client.get_seller_transactions(days_back=days_back, page_number=page)
                    
                    if 'TransactionArray' in response and 'Transaction' in response['TransactionArray']:
                        transactions = response['TransactionArray']['Transaction']
                        if not isinstance(transactions, list):
                            transactions = [transactions]
                        
                        for transaction_data in transactions:
                            sale = self.parse_sale(transaction_data)
                            await self.save_sale(sale)
                            await self.send_sale_event(sale)
                            synced_count += 1
                    
                    # Check pagination
                    pagination = response.get('PaginationResult', {})
                    total_pages = int(pagination.get('TotalNumberOfPages', 1))
                    
                    if page >= total_pages:
                        break
                    
                    page += 1
                    await asyncio.sleep(1)  # Rate limiting
                
                logger.info(f"Synced {synced_count} transactions from eBay")
                return synced_count
                
        except Exception as e:
            logger.error(f"Failed to sync transactions: {e}")
            return 0
    
    def parse_listing(self, item_data: Dict) -> eBayListing:
        """Parse listing data from API response"""
        return eBayListing(
            item_id=item_data.get('ItemID', ''),
            title=item_data.get('Title', ''),
            sku=item_data.get('SKU', ''),
            category_id=item_data.get('PrimaryCategory', {}).get('CategoryID', ''),
            listing_type=item_data.get('ListingType', ''),
            quantity=int(item_data.get('Quantity', 0)),
            quantity_sold=int(item_data.get('QuantitySold', 0)),
            start_price=float(item_data.get('StartPrice', {}).get('_', 0)),
            current_price=float(item_data.get('ConvertedCurrentPrice', {}).get('_', 0)),
            currency=item_data.get('ConvertedCurrentPrice', {}).get('currencyID', 'USD'),
            condition=item_data.get('ConditionDisplayName', ''),
            listing_status=item_data.get('SellingStatus', {}).get('ListingStatus', ''),
            start_time=self.parse_datetime(item_data.get('ListingDetails', {}).get('StartTime', '')),
            end_time=self.parse_datetime(item_data.get('ListingDetails', {}).get('EndTime', '')),
            site_id=item_data.get('Site', ''),
            seller_id=item_data.get('Seller', {}).get('UserID', ''),
            view_count=int(item_data.get('HitCount', 0)),
            watch_count=int(item_data.get('WatchCount', 0)),
            best_offer_enabled=item_data.get('BestOfferDetails', {}).get('BestOfferEnabled', False),
            buy_it_now_price=float(item_data.get('BuyItNowPrice', {}).get('_', 0)) or None,
            pictures=[img.get('_', '') for img in item_data.get('PictureDetails', {}).get('PictureURL', [])]
        )
    
    def parse_order(self, order_data: Dict) -> eBayOrder:
        """Parse order data from API response"""
        return eBayOrder(
            order_id=order_data.get('OrderID', ''),
            order_status=order_data.get('OrderStatus', ''),
            creation_time=self.parse_datetime(order_data.get('CreatedTime', '')),
            payment_status=order_data.get('CheckoutStatus', {}).get('PaymentMethod', ''),
            shipping_status=order_data.get('ShippingDetails', {}).get('ShippingServiceOptions', {}).get('ShippingServicePriority', ''),
            buyer_user_id=order_data.get('BuyerUserID', ''),
            buyer_email=order_data.get('TransactionArray', {}).get('Transaction', {}).get('Buyer', {}).get('Email', ''),
            total_amount=float(order_data.get('Total', {}).get('_', 0)),
            currency=order_data.get('Total', {}).get('currencyID', 'USD'),
            payment_method=order_data.get('CheckoutStatus', {}).get('PaymentMethod', ''),
            shipping_address=order_data.get('ShippingAddress', {}),
            billing_address=order_data.get('CheckoutStatus', {}).get('eBayPaymentStatus', {}),
            line_items=self.parse_line_items(order_data.get('TransactionArray', {})),
            shipping_cost=float(order_data.get('ShippingServiceSelected', {}).get('ShippingServiceCost', {}).get('_', 0)),
            tax_amount=float(order_data.get('SalesTax', {}).get('SalesTaxAmount', {}).get('_', 0)),
            order_fulfillment_status=order_data.get('OrderStatus', ''),
            last_modified_time=self.parse_datetime(order_data.get('ModifiedTime', '')),
            checkout_status=order_data.get('CheckoutStatus', {}).get('Status', '')
        )
    
    def parse_sale(self, transaction_data: Dict) -> eBaySale:
        """Parse sale transaction data"""
        return eBaySale(
            transaction_id=transaction_data.get('TransactionID', ''),
            item_id=transaction_data.get('Item', {}).get('ItemID', ''),
            order_id=transaction_data.get('ContainingOrder', {}).get('OrderID'),
            buyer_user_id=transaction_data.get('Buyer', {}).get('UserID', ''),
            quantity_purchased=int(transaction_data.get('QuantityPurchased', 0)),
            transaction_price=float(transaction_data.get('TransactionPrice', {}).get('_', 0)),
            currency=transaction_data.get('TransactionPrice', {}).get('currencyID', 'USD'),
            transaction_site_id=transaction_data.get('TransactionSiteID', ''),
            platform=transaction_data.get('Platform', 'eBay'),
            creation_time=self.parse_datetime(transaction_data.get('CreatedDate', '')),
            paid_time=self.parse_datetime(transaction_data.get('PaidTime', '')),
            shipped_time=self.parse_datetime(transaction_data.get('ShippedTime', '')),
            feedback_left=transaction_data.get('FeedbackLeft', False),
            feedback_received=transaction_data.get('FeedbackReceived', False),
            seller_id=transaction_data.get('Item', {}).get('Seller', {}).get('UserID', '')
        )
    
    def parse_line_items(self, transaction_array: Dict) -> List[Dict[str, Any]]:
        """Parse line items from transaction array"""
        line_items = []
        
        transactions = transaction_array.get('Transaction', [])
        if not isinstance(transactions, list):
            transactions = [transactions]
        
        for transaction in transactions:
            item = transaction.get('Item', {})
            line_items.append({
                'item_id': item.get('ItemID', ''),
                'title': item.get('Title', ''),
                'sku': item.get('SKU', ''),
                'quantity': int(transaction.get('QuantityPurchased', 0)),
                'price': float(transaction.get('TransactionPrice', {}).get('_', 0)),
                'currency': transaction.get('TransactionPrice', {}).get('currencyID', 'USD')
            })
        
        return line_items
    
    def parse_datetime(self, date_str: str) -> datetime:
        """Parse eBay datetime format"""
        if not date_str:
            return datetime.utcnow()
        
        try:
            # eBay format: 2023-12-25T10:30:45.000Z
            return datetime.fromisoformat(date_str.replace('Z', '+00:00'))
        except:
            return datetime.utcnow()
    
    async def save_listing(self, listing: eBayListing):
        """Save listing to database"""
        session = self.db_session()
        try:
            existing = session.query(eBayListingModel).filter_by(item_id=listing.item_id).first()
            
            if existing:
                # Update existing listing
                existing.title = listing.title
                existing.sku = listing.sku
                existing.quantity = listing.quantity
                existing.quantity_sold = listing.quantity_sold
                existing.current_price = listing.current_price
                existing.listing_status = listing.listing_status
                existing.view_count = listing.view_count
                existing.watch_count = listing.watch_count
                existing.raw_data = json.dumps(asdict(listing), default=str)
                existing.last_updated = datetime.utcnow()
            else:
                # Create new listing
                listing_model = eBayListingModel(
                    item_id=listing.item_id,
                    seller_id=listing.seller_id,
                    title=listing.title,
                    sku=listing.sku,
                    category_id=listing.category_id,
                    listing_type=listing.listing_type,
                    quantity=listing.quantity,
                    quantity_sold=listing.quantity_sold,
                    start_price=listing.start_price,
                    current_price=listing.current_price,
                    currency=listing.currency,
                    condition=listing.condition,
                    listing_status=listing.listing_status,
                    start_time=listing.start_time,
                    end_time=listing.end_time,
                    site_id=listing.site_id,
                    view_count=listing.view_count,
                    watch_count=listing.watch_count,
                    raw_data=json.dumps(asdict(listing), default=str)
                )
                session.add(listing_model)
            
            session.commit()
        finally:
            session.close()
    
    async def save_order(self, order: eBayOrder):
        """Save order to database"""
        session = self.db_session()
        try:
            existing = session.query(eBayOrderModel).filter_by(order_id=order.order_id).first()
            
            if not existing:
                order_model = eBayOrderModel(
                    order_id=order.order_id,
                    seller_id=order.buyer_user_id,  # This would be the actual seller ID
                    order_status=order.order_status,
                    creation_time=order.creation_time,
                    payment_status=order.payment_status,
                    shipping_status=order.shipping_status,
                    buyer_user_id=order.buyer_user_id,
                    buyer_email=order.buyer_email,
                    total_amount=order.total_amount,
                    currency=order.currency,
                    payment_method=order.payment_method,
                    shipping_cost=order.shipping_cost,
                    tax_amount=order.tax_amount,
                    raw_data=json.dumps(asdict(order), default=str),
                    synced_at=datetime.utcnow()
                )
                session.add(order_model)
                session.commit()
        finally:
            session.close()
    
    async def save_sale(self, sale: eBaySale):
        """Save sale to database"""
        session = self.db_session()
        try:
            existing = session.query(eBaySaleModel).filter_by(transaction_id=sale.transaction_id).first()
            
            if not existing:
                sale_model = eBaySaleModel(
                    transaction_id=sale.transaction_id,
                    seller_id=sale.seller_id,
                    item_id=sale.item_id,
                    order_id=sale.order_id,
                    buyer_user_id=sale.buyer_user_id,
                    quantity_purchased=sale.quantity_purchased,
                    transaction_price=sale.transaction_price,
                    currency=sale.currency,
                    creation_time=sale.creation_time,
                    paid_time=sale.paid_time,
                    shipped_time=sale.shipped_time,
                    raw_data=json.dumps(asdict(sale), default=str),
                    synced_at=datetime.utcnow()
                )
                session.add(sale_model)
                session.commit()
        finally:
            session.close()
    
    async def send_listing_event(self, listing: eBayListing):
        """Send listing event to analytics platform"""
        event_data = {
            'event_type': 'ebay_listing_updated',
            'platform': 'ebay',
            'seller_id': listing.seller_id,
            'site_id': listing.site_id,
            'timestamp': datetime.utcnow().isoformat(),
            'data': asdict(listing)
        }
        
        await self.send_to_analytics(event_data)
    
    async def send_order_event(self, order: eBayOrder):
        """Send order event to analytics platform"""
        event_data = {
            'event_type': 'ebay_order_created',
            'platform': 'ebay',
            'seller_id': order.buyer_user_id,  # Would be actual seller ID
            'timestamp': order.creation_time.isoformat(),
            'data': asdict(order)
        }
        
        await self.send_to_analytics(event_data)
    
    async def send_sale_event(self, sale: eBaySale):
        """Send sale event to analytics platform"""
        event_data = {
            'event_type': 'ebay_sale_completed',
            'platform': 'ebay',
            'seller_id': sale.seller_id,
            'timestamp': sale.creation_time.isoformat(),
            'data': asdict(sale)
        }
        
        await self.send_to_analytics(event_data)
    
    async def send_to_analytics(self, event_data: Dict):
        """Send event to analytics platform"""
        try:
            headers = {
                'X-API-Key': self.analytics_api_key,
                'Content-Type': 'application/json'
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.analytics_api_url}/events",
                    json=event_data,
                    headers=headers
                ) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        logger.error(f"Failed to send event to analytics: {response.status} - {error_text}")
                        
                        # Store failed event for retry
                        await self.redis_client.lpush(
                            'failed_events',
                            json.dumps(event_data)
                        )
        
        except Exception as e:
            logger.error(f"Error sending event to analytics: {e}")
            
            # Store failed event for retry
            await self.redis_client.lpush(
                'failed_events',
                json.dumps(event_data)
            )
    
    async def retry_failed_events(self):
        """Retry sending failed events"""
        retry_count = 0
        
        while True:
            event_json = await self.redis_client.rpop('failed_events')
            if not event_json:
                break
            
            try:
                event_data = json.loads(event_json)
                await self.send_to_analytics(event_data)
                retry_count += 1
            except Exception as e:
                logger.error(f"Failed to retry event: {e}")
                # Put back in queue for later
                await self.redis_client.lpush('failed_events', event_json)
                break
        
        if retry_count > 0:
            logger.info(f"Retried {retry_count} failed events")
    
    async def run_sync_cycle(self):
        """Run a complete sync cycle"""
        logger.info("Starting eBay sync cycle")
        
        try:
            # Sync listings
            listings_synced = await self.sync_listings()
            
            # Sync orders
            orders_synced = await self.sync_orders()
            
            # Sync transactions
            transactions_synced = await self.sync_transactions()
            
            # Retry failed events
            await self.retry_failed_events()
            
            logger.info(f"Sync cycle completed: {listings_synced} listings, {orders_synced} orders, {transactions_synced} transactions")
            
        except Exception as e:
            logger.error(f"Sync cycle failed: {e}")
    
    async def start_continuous_sync(self, interval_minutes: int = 30):
        """Start continuous sync process"""
        logger.info(f"Starting continuous sync every {interval_minutes} minutes")
        
        while True:
            await self.run_sync_cycle()
            await asyncio.sleep(interval_minutes * 60)
    
    async def cleanup(self):
        """Cleanup resources"""
        if self.redis_client:
            await self.redis_client.close()
        
        logger.info("eBay Analytics Connector cleaned up")

# Main execution
async def main():
    # Configuration
    config = {
        'ebay_credentials': {
            'app_id': 'your_app_id',
            'dev_id': 'your_dev_id',
            'cert_id': 'your_cert_id',
            'user_token': 'your_user_token',
            'site_id': '0',
            'sandbox': False
        },
        'analytics_api_url': 'https://api.analytics-platform.com',
        'analytics_api_key': 'your_analytics_api_key',
        'database_url': 'postgresql://user:password@localhost/ebay_analytics',
        'redis_url': 'redis://localhost:6379'
    }
    
    # Initialize connector
    connector = eBayAnalyticsConnector(config)
    await connector.initialize()
    
    try:
        # Run initial sync
        await connector.run_sync_cycle()
        
        # Start continuous sync (uncomment for production)
        # await connector.start_continuous_sync(interval_minutes=30)
        
    finally:
        await connector.cleanup()

if __name__ == "__main__":
    asyncio.run(main())