/**
 * Advanced Analytics for Shopify
 * Shopify app with comprehensive analytics tracking, ML insights, and real-time monitoring
 */

const express = require('express');
const bodyParser = require('body-parser');
const crypto = require('crypto');
const axios = require('axios');
const redis = require('redis');
const { Shopify } = require('@shopify/shopify-api');
const mongoose = require('mongoose');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const winston = require('winston');

// Initialize logger
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' }),
    new winston.transports.Console({
      format: winston.format.simple()
    })
  ]
});

// Environment configuration
const config = {
  port: process.env.PORT || 3000,
  shopifyApiKey: process.env.SHOPIFY_API_KEY,
  shopifyApiSecret: process.env.SHOPIFY_API_SECRET,
  shopifyScopes: 'read_orders,read_products,read_customers,read_analytics,write_script_tags,read_checkouts',
  analyticsApiUrl: process.env.ANALYTICS_API_URL || 'https://api.analytics-platform.com',
  analyticsApiKey: process.env.ANALYTICS_API_KEY,
  redisUrl: process.env.REDIS_URL || 'redis://localhost:6379',
  mongoUrl: process.env.MONGO_URL || 'mongodb://localhost:27017/shopify_analytics',
  webhookSecret: process.env.SHOPIFY_WEBHOOK_SECRET,
  appUrl: process.env.APP_URL || 'https://analytics-app.herokuapp.com'
};

// Initialize Redis client
const redisClient = redis.createClient({ url: config.redisUrl });
redisClient.on('error', (err) => logger.error('Redis Client Error', err));
redisClient.connect();

// Initialize MongoDB
mongoose.connect(config.mongoUrl);

// Shopify API setup
Shopify.Context.initialize({
  API_KEY: config.shopifyApiKey,
  API_SECRET_KEY: config.shopifyApiSecret,
  SCOPES: config.shopifyScopes.split(','),
  HOST_NAME: config.appUrl.replace(/https?:\/\//, ''),
  API_VERSION: '2023-10',
  IS_EMBEDDED_APP: true,
  SESSION_STORAGE: new Shopify.Session.MemorySessionStorage()
});

// MongoDB Schemas
const ShopSchema = new mongoose.Schema({
  shopDomain: { type: String, required: true, unique: true },
  accessToken: { type: String, required: true },
  analyticsEnabled: { type: Boolean, default: true },
  webhooksInstalled: { type: Boolean, default: false },
  scriptTagInstalled: { type: Boolean, default: false },
  settings: {
    trackingEnabled: { type: Boolean, default: true },
    realTimeAlerts: { type: Boolean, default: true },
    mlInsights: { type: Boolean, default: true },
    fraudDetection: { type: Boolean, default: true },
    customerSegmentation: { type: Boolean, default: true }
  },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

const EventSchema = new mongoose.Schema({
  shopDomain: { type: String, required: true, index: true },
  eventType: { type: String, required: true, index: true },
  eventId: { type: String, required: true, unique: true },
  timestamp: { type: Date, default: Date.now, index: true },
  data: { type: mongoose.Schema.Types.Mixed },
  synced: { type: Boolean, default: false, index: true },
  retryCount: { type: Number, default: 0 }
});

const Shop = mongoose.model('Shop', ShopSchema);
const Event = mongoose.model('Event', EventSchema);

// Express app setup
const app = express();

// Security middleware
app.use(helmet());

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000, // limit each IP to 1000 requests per windowMs
  message: 'Too many requests from this IP, please try again later.'
});
app.use(limiter);

// Body parser middleware
app.use(bodyParser.json({ limit: '10mb' }));
app.use(bodyParser.urlencoded({ extended: true }));

// Analytics API client
class AnalyticsClient {
  constructor() {
    this.apiUrl = config.analyticsApiUrl;
    this.apiKey = config.analyticsApiKey;
    this.client = axios.create({
      baseURL: this.apiUrl,
      headers: {
        'X-API-Key': this.apiKey,
        'Content-Type': 'application/json',
        'User-Agent': 'Shopify-Analytics-App/1.0.0'
      },
      timeout: 30000
    });
  }

  async sendEvents(events) {
    try {
      const response = await this.client.post('/events/batch', {
        events,
        platform: 'shopify',
        source: 'shopify_app'
      });
      return { success: true, data: response.data };
    } catch (error) {
      logger.error('Failed to send events to analytics platform', {
        error: error.message,
        events: events.length
      });
      throw error;
    }
  }

  async getInsights(shopDomain, params = {}) {
    try {
      const response = await this.client.get('/insights', {
        params: {
          shop_domain: shopDomain,
          platform: 'shopify',
          ...params
        }
      });
      return response.data;
    } catch (error) {
      logger.error('Failed to fetch insights', {
        error: error.message,
        shopDomain
      });
      throw error;
    }
  }

  async getRecommendations(shopDomain, params = {}) {
    try {
      const response = await this.client.get('/recommendations', {
        params: {
          shop_domain: shopDomain,
          platform: 'shopify',
          ...params
        }
      });
      return response.data;
    } catch (error) {
      logger.error('Failed to fetch recommendations', {
        error: error.message,
        shopDomain
      });
      throw error;
    }
  }

  async testConnection() {
    try {
      const response = await this.client.get('/health');
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }
}

const analyticsClient = new AnalyticsClient();

// Event tracker
class EventTracker {
  constructor() {
    this.batchSize = 50;
    this.batchInterval = 30000; // 30 seconds
    this.eventQueue = new Map(); // shopDomain -> events[]
    
    // Start batch processing
    setInterval(() => this.processBatches(), this.batchInterval);
  }

  async trackEvent(shopDomain, eventType, data) {
    const event = {
      eventId: this.generateEventId(),
      eventType,
      shopDomain,
      timestamp: new Date(),
      data: {
        ...data,
        platform: 'shopify',
        appVersion: '1.0.0'
      }
    };

    // Store in database
    try {
      const eventDoc = new Event(event);
      await eventDoc.save();
    } catch (error) {
      logger.error('Failed to store event in database', {
        error: error.message,
        event: event.eventId
      });
    }

    // Add to queue for batching
    if (!this.eventQueue.has(shopDomain)) {
      this.eventQueue.set(shopDomain, []);
    }
    
    this.eventQueue.get(shopDomain).push(event);

    // Send immediately for critical events
    if (this.isCriticalEvent(eventType)) {
      await this.sendEvents(shopDomain);
    } else if (this.eventQueue.get(shopDomain).length >= this.batchSize) {
      await this.sendEvents(shopDomain);
    }
  }

  async sendEvents(shopDomain) {
    const events = this.eventQueue.get(shopDomain) || [];
    if (events.length === 0) return;

    this.eventQueue.set(shopDomain, []);

    try {
      await analyticsClient.sendEvents(events);
      
      // Mark events as synced
      const eventIds = events.map(e => e.eventId);
      await Event.updateMany(
        { eventId: { $in: eventIds } },
        { synced: true }
      );

      logger.info('Successfully sent events', {
        shopDomain,
        eventCount: events.length
      });
    } catch (error) {
      // Return events to queue for retry
      this.eventQueue.set(shopDomain, [
        ...events,
        ...(this.eventQueue.get(shopDomain) || [])
      ]);

      // Update retry count
      const eventIds = events.map(e => e.eventId);
      await Event.updateMany(
        { eventId: { $in: eventIds } },
        { $inc: { retryCount: 1 } }
      );

      logger.error('Failed to send events', {
        error: error.message,
        shopDomain,
        eventCount: events.length
      });
    }
  }

  async processBatches() {
    const promises = [];
    for (const shopDomain of this.eventQueue.keys()) {
      promises.push(this.sendEvents(shopDomain));
    }
    await Promise.allSettled(promises);
  }

  generateEventId() {
    return `shopify_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  isCriticalEvent(eventType) {
    const criticalEvents = [
      'orders/paid',
      'orders/cancelled',
      'orders/refunded',
      'app/uninstalled'
    ];
    return criticalEvents.includes(eventType);
  }
}

const eventTracker = new EventTracker();

// Webhook verification middleware
function verifyWebhook(req, res, next) {
  const hmac = req.get('X-Shopify-Hmac-Sha256');
  const body = req.body;
  const hash = crypto
    .createHmac('sha256', config.webhookSecret)
    .update(JSON.stringify(body))
    .digest('base64');

  if (hash !== hmac) {
    logger.warn('Webhook verification failed', {
      receivedHmac: hmac,
      calculatedHash: hash,
      topic: req.get('X-Shopify-Topic')
    });
    return res.status(401).send('Unauthorized');
  }

  next();
}

// Routes

// App installation
app.get('/auth', async (req, res) => {
  try {
    const authRoute = await Shopify.Auth.beginAuth(
      req,
      res,
      req.query.shop,
      '/auth/callback',
      false
    );
    return res.redirect(authRoute);
  } catch (error) {
    logger.error('Auth initiation failed', { error: error.message });
    res.status(500).send('Authentication failed');
  }
});

// Auth callback
app.get('/auth/callback', async (req, res) => {
  try {
    const session = await Shopify.Auth.validateAuthCallback(req, res, req.query);
    
    // Store shop information
    const shop = await Shop.findOneAndUpdate(
      { shopDomain: session.shop },
      {
        shopDomain: session.shop,
        accessToken: session.accessToken,
        updatedAt: new Date()
      },
      { upsert: true, new: true }
    );

    // Install webhooks and script tags
    await installWebhooks(session);
    await installScriptTag(session);

    // Track installation
    await eventTracker.trackEvent(session.shop, 'app/installed', {
      shopId: session.shop,
      installationTime: new Date()
    });

    res.redirect(`/dashboard?shop=${session.shop}`);
  } catch (error) {
    logger.error('Auth callback failed', { error: error.message });
    res.status(500).send('Authentication callback failed');
  }
});

// Dashboard
app.get('/dashboard', async (req, res) => {
  const shopDomain = req.query.shop;
  
  if (!shopDomain) {
    return res.status(400).send('Shop parameter required');
  }

  try {
    const shop = await Shop.findOne({ shopDomain });
    if (!shop) {
      return res.redirect(`/auth?shop=${shopDomain}`);
    }

    // Fetch insights
    const insights = await analyticsClient.getInsights(shopDomain, {
      timeframe: '30d'
    });

    res.send(renderDashboard(shop, insights));
  } catch (error) {
    logger.error('Dashboard loading failed', {
      error: error.message,
      shopDomain
    });
    res.status(500).send('Dashboard loading failed');
  }
});

// API endpoint for insights
app.get('/api/insights', async (req, res) => {
  try {
    const shopDomain = req.query.shop;
    const timeframe = req.query.timeframe || '30d';
    
    const insights = await analyticsClient.getInsights(shopDomain, {
      timeframe,
      metrics: req.query.metrics?.split(',')
    });

    res.json(insights);
  } catch (error) {
    logger.error('Failed to fetch insights', { error: error.message });
    res.status(500).json({ error: 'Failed to fetch insights' });
  }
});

// API endpoint for recommendations
app.get('/api/recommendations', async (req, res) => {
  try {
    const shopDomain = req.query.shop;
    
    const recommendations = await analyticsClient.getRecommendations(shopDomain, {
      customerId: req.query.customer_id,
      productId: req.query.product_id,
      type: req.query.type || 'related',
      limit: parseInt(req.query.limit) || 10
    });

    res.json(recommendations);
  } catch (error) {
    logger.error('Failed to fetch recommendations', { error: error.message });
    res.status(500).json({ error: 'Failed to fetch recommendations' });
  }
});

// Settings endpoint
app.post('/api/settings', async (req, res) => {
  try {
    const shopDomain = req.body.shop;
    const settings = req.body.settings;

    await Shop.findOneAndUpdate(
      { shopDomain },
      { settings, updatedAt: new Date() }
    );

    res.json({ success: true });
  } catch (error) {
    logger.error('Failed to update settings', { error: error.message });
    res.status(500).json({ error: 'Failed to update settings' });
  }
});

// Webhook endpoints

// Order webhooks
app.post('/webhooks/orders/create', verifyWebhook, async (req, res) => {
  try {
    const order = req.body;
    const shopDomain = req.get('X-Shopify-Shop-Domain');

    await eventTracker.trackEvent(shopDomain, 'orders/create', {
      orderId: order.id,
      orderNumber: order.order_number,
      totalPrice: parseFloat(order.total_price),
      currency: order.currency,
      customerId: order.customer?.id,
      customerEmail: order.customer?.email,
      lineItems: order.line_items.map(item => ({
        productId: item.product_id,
        variantId: item.variant_id,
        quantity: item.quantity,
        price: parseFloat(item.price),
        title: item.title
      })),
      shippingAddress: order.shipping_address,
      billingAddress: order.billing_address,
      financialStatus: order.financial_status,
      fulfillmentStatus: order.fulfillment_status,
      tags: order.tags,
      source: order.source_name,
      createdAt: order.created_at
    });

    res.status(200).send('OK');
  } catch (error) {
    logger.error('Order create webhook failed', { error: error.message });
    res.status(500).send('Internal Server Error');
  }
});

app.post('/webhooks/orders/paid', verifyWebhook, async (req, res) => {
  try {
    const order = req.body;
    const shopDomain = req.get('X-Shopify-Shop-Domain');

    await eventTracker.trackEvent(shopDomain, 'orders/paid', {
      orderId: order.id,
      orderNumber: order.order_number,
      totalPrice: parseFloat(order.total_price),
      currency: order.currency,
      customerId: order.customer?.id,
      paymentGateway: order.gateway,
      paymentMethod: order.payment_gateway_names,
      processedAt: order.processed_at
    });

    res.status(200).send('OK');
  } catch (error) {
    logger.error('Order paid webhook failed', { error: error.message });
    res.status(500).send('Internal Server Error');
  }
});

app.post('/webhooks/orders/cancelled', verifyWebhook, async (req, res) => {
  try {
    const order = req.body;
    const shopDomain = req.get('X-Shopify-Shop-Domain');

    await eventTracker.trackEvent(shopDomain, 'orders/cancelled', {
      orderId: order.id,
      orderNumber: order.order_number,
      totalPrice: parseFloat(order.total_price),
      currency: order.currency,
      customerId: order.customer?.id,
      cancelReason: order.cancel_reason,
      cancelledAt: order.cancelled_at
    });

    res.status(200).send('OK');
  } catch (error) {
    logger.error('Order cancelled webhook failed', { error: error.message });
    res.status(500).send('Internal Server Error');
  }
});

app.post('/webhooks/orders/refunded', verifyWebhook, async (req, res) => {
  try {
    const order = req.body;
    const shopDomain = req.get('X-Shopify-Shop-Domain');

    await eventTracker.trackEvent(shopDomain, 'orders/refunded', {
      orderId: order.id,
      orderNumber: order.order_number,
      totalPrice: parseFloat(order.total_price),
      currency: order.currency,
      customerId: order.customer?.id,
      refunds: order.refunds.map(refund => ({
        id: refund.id,
        amount: parseFloat(refund.transactions[0]?.amount || 0),
        reason: refund.note,
        createdAt: refund.created_at
      }))
    });

    res.status(200).send('OK');
  } catch (error) {
    logger.error('Order refunded webhook failed', { error: error.message });
    res.status(500).send('Internal Server Error');
  }
});

// Customer webhooks
app.post('/webhooks/customers/create', verifyWebhook, async (req, res) => {
  try {
    const customer = req.body;
    const shopDomain = req.get('X-Shopify-Shop-Domain');

    await eventTracker.trackEvent(shopDomain, 'customers/create', {
      customerId: customer.id,
      email: customer.email,
      firstName: customer.first_name,
      lastName: customer.last_name,
      phone: customer.phone,
      defaultAddress: customer.default_address,
      ordersCount: customer.orders_count,
      totalSpent: parseFloat(customer.total_spent),
      tags: customer.tags,
      acceptsMarketing: customer.accepts_marketing,
      createdAt: customer.created_at
    });

    res.status(200).send('OK');
  } catch (error) {
    logger.error('Customer create webhook failed', { error: error.message });
    res.status(500).send('Internal Server Error');
  }
});

// Product webhooks
app.post('/webhooks/products/create', verifyWebhook, async (req, res) => {
  try {
    const product = req.body;
    const shopDomain = req.get('X-Shopify-Shop-Domain');

    await eventTracker.trackEvent(shopDomain, 'products/create', {
      productId: product.id,
      title: product.title,
      handle: product.handle,
      productType: product.product_type,
      vendor: product.vendor,
      tags: product.tags,
      variants: product.variants.map(variant => ({
        id: variant.id,
        price: parseFloat(variant.price),
        compareAtPrice: parseFloat(variant.compare_at_price || 0),
        sku: variant.sku,
        inventoryQuantity: variant.inventory_quantity
      })),
      images: product.images.map(image => image.src),
      createdAt: product.created_at
    });

    res.status(200).send('OK');
  } catch (error) {
    logger.error('Product create webhook failed', { error: error.message });
    res.status(500).send('Internal Server Error');
  }
});

// App uninstall webhook
app.post('/webhooks/app/uninstalled', verifyWebhook, async (req, res) => {
  try {
    const shopDomain = req.get('X-Shopify-Shop-Domain');

    // Track uninstall event
    await eventTracker.trackEvent(shopDomain, 'app/uninstalled', {
      shopDomain,
      uninstalledAt: new Date()
    });

    // Clean up shop data
    await Shop.findOneAndUpdate(
      { shopDomain },
      { 
        analyticsEnabled: false,
        webhooksInstalled: false,
        scriptTagInstalled: false,
        updatedAt: new Date()
      }
    );

    res.status(200).send('OK');
  } catch (error) {
    logger.error('App uninstall webhook failed', { error: error.message });
    res.status(500).send('Internal Server Error');
  }
});

// Helper functions

async function installWebhooks(session) {
  const client = new Shopify.Clients.Rest(session.shop, session.accessToken);
  
  const webhooks = [
    { topic: 'orders/create', address: `${config.appUrl}/webhooks/orders/create` },
    { topic: 'orders/paid', address: `${config.appUrl}/webhooks/orders/paid` },
    { topic: 'orders/cancelled', address: `${config.appUrl}/webhooks/orders/cancelled` },
    { topic: 'orders/refunded', address: `${config.appUrl}/webhooks/orders/refunded` },
    { topic: 'customers/create', address: `${config.appUrl}/webhooks/customers/create` },
    { topic: 'products/create', address: `${config.appUrl}/webhooks/products/create` },
    { topic: 'app/uninstalled', address: `${config.appUrl}/webhooks/app/uninstalled` }
  ];

  for (const webhook of webhooks) {
    try {
      await client.post({
        path: 'webhooks',
        data: {
          webhook: {
            topic: webhook.topic,
            address: webhook.address,
            format: 'json'
          }
        }
      });
      logger.info('Webhook installed', { topic: webhook.topic, shop: session.shop });
    } catch (error) {
      logger.error('Failed to install webhook', {
        topic: webhook.topic,
        shop: session.shop,
        error: error.message
      });
    }
  }

  // Update shop record
  await Shop.findOneAndUpdate(
    { shopDomain: session.shop },
    { webhooksInstalled: true }
  );
}

async function installScriptTag(session) {
  const client = new Shopify.Clients.Rest(session.shop, session.accessToken);
  
  try {
    await client.post({
      path: 'script_tags',
      data: {
        script_tag: {
          event: 'onload',
          src: `${config.appUrl}/analytics.js?shop=${session.shop}`
        }
      }
    });

    // Update shop record
    await Shop.findOneAndUpdate(
      { shopDomain: session.shop },
      { scriptTagInstalled: true }
    );

    logger.info('Script tag installed', { shop: session.shop });
  } catch (error) {
    logger.error('Failed to install script tag', {
      shop: session.shop,
      error: error.message
    });
  }
}

function renderDashboard(shop, insights) {
  return `
    <!DOCTYPE html>
    <html>
    <head>
        <title>Advanced Analytics Dashboard</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: #f6f6f7; }
            .container { max-width: 1200px; margin: 0 auto; }
            .header { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }
            .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 20px; }
            .stat-card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }
            .stat-value { font-size: 2em; font-weight: bold; color: #333; }
            .stat-label { color: #666; margin-top: 5px; }
            .chart-container { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }
        </style>
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>Advanced Analytics Dashboard</h1>
                <p>Shop: ${shop.shopDomain}</p>
            </div>
            
            <div class="stats">
                <div class="stat-card">
                    <div class="stat-value">${insights?.revenue?.total || '$0'}</div>
                    <div class="stat-label">Total Revenue (30d)</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${insights?.orders?.total || '0'}</div>
                    <div class="stat-label">Total Orders (30d)</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${insights?.customers?.total || '0'}</div>
                    <div class="stat-label">Total Customers</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${insights?.conversionRate || '0%'}</div>
                    <div class="stat-label">Conversion Rate</div>
                </div>
            </div>
            
            <div class="chart-container">
                <h3>Revenue Trend</h3>
                <canvas id="revenueChart" width="400" height="200"></canvas>
            </div>
        </div>
        
        <script>
            // Initialize charts with insights data
            const ctx = document.getElementById('revenueChart').getContext('2d');
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ${JSON.stringify(insights?.revenue?.timeline?.labels || [])},
                    datasets: [{
                        label: 'Revenue',
                        data: ${JSON.stringify(insights?.revenue?.timeline?.data || [])},
                        borderColor: 'rgb(75, 192, 192)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Revenue Over Time'
                        }
                    }
                }
            });
        </script>
    </body>
    </html>
  `;
}

// Analytics script endpoint
app.get('/analytics.js', async (req, res) => {
  const shopDomain = req.query.shop;
  
  if (!shopDomain) {
    return res.status(400).send('Shop parameter required');
  }

  const analyticsScript = `
    (function() {
      var analytics = {
        shop: '${shopDomain}',
        apiUrl: '${config.appUrl}',
        
        track: function(event, data) {
          fetch(this.apiUrl + '/api/track', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              shop: this.shop,
              event: event,
              data: data,
              timestamp: new Date().toISOString(),
              url: window.location.href,
              referrer: document.referrer
            })
          });
        },
        
        init: function() {
          // Track page view
          this.track('page_view', {
            page: window.location.pathname,
            title: document.title
          });
          
          // Track product views
          if (window.location.pathname.includes('/products/')) {
            this.trackProductView();
          }
          
          // Track cart events
          this.trackCartEvents();
        },
        
        trackProductView: function() {
          if (window.ShopifyAnalytics && window.ShopifyAnalytics.meta.product) {
            var product = window.ShopifyAnalytics.meta.product;
            this.track('product_view', {
              productId: product.id,
              productTitle: product.title,
              productType: product.type,
              vendor: product.vendor,
              price: product.price
            });
          }
        },
        
        trackCartEvents: function() {
          // Track add to cart
          document.addEventListener('click', function(e) {
            var button = e.target.closest('[data-action="add-to-cart"], .btn-cart');
            if (button) {
              analytics.track('add_to_cart', {
                productId: button.dataset.productId,
                variantId: button.dataset.variantId
              });
            }
          });
        }
      };
      
      // Initialize analytics
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
          analytics.init();
        });
      } else {
        analytics.init();
      }
      
      // Make analytics globally available
      window.AdvancedAnalytics = analytics;
    })();
  `;

  res.setHeader('Content-Type', 'application/javascript');
  res.send(analyticsScript);
});

// Client-side tracking endpoint
app.post('/api/track', async (req, res) => {
  try {
    const { shop, event, data, timestamp, url, referrer } = req.body;
    
    await eventTracker.trackEvent(shop, `frontend/${event}`, {
      ...data,
      url,
      referrer,
      userAgent: req.get('User-Agent'),
      ip: req.ip,
      timestamp
    });

    res.json({ success: true });
  } catch (error) {
    logger.error('Client tracking failed', { error: error.message });
    res.status(500).json({ error: 'Tracking failed' });
  }
});

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// Error handling middleware
app.use((error, req, res, next) => {
  logger.error('Unhandled error', { error: error.message, stack: error.stack });
  res.status(500).json({ error: 'Internal server error' });
});

// Start server
app.listen(config.port, () => {
  logger.info(`Shopify Analytics App running on port ${config.port}`);
});

// Graceful shutdown
process.on('SIGTERM', async () => {
  logger.info('SIGTERM received, shutting down gracefully');
  
  // Send any remaining events
  await eventTracker.processBatches();
  
  // Close connections
  await redisClient.quit();
  await mongoose.connection.close();
  
  process.exit(0);
});

module.exports = app;