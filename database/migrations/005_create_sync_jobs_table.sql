-- Create sync_jobs table for tracking synchronization tasks

CREATE TABLE sync_jobs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    integration_id UUID NOT NULL REFERENCES integrations(id) ON DELETE CASCADE,
    sync_type VARCHAR(20) NOT NULL CHECK (sync_type IN ('full', 'incremental')),
    data_types JSONB NOT NULL DEFAULT '["orders"]',
    status VARCHAR(20) NOT NULL DEFAULT 'queued' CHECK (status IN ('queued', 'in_progress', 'completed', 'failed', 'cancelled')),
    
    -- Date range for sync
    date_from TIMESTAMP WITH TIME ZONE,
    date_to TIMESTAMP WITH TIME ZONE,
    
    -- Progress tracking
    records_processed INTEGER DEFAULT 0,
    error_count INTEGER DEFAULT 0,
    
    -- Timing
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    failed_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Duration in seconds
    duration_seconds INTEGER,
    
    -- Error details
    error_message TEXT,
    
    -- Additional metadata
    metadata JSONB DEFAULT '{}'
);

-- Create indexes for performance
CREATE INDEX idx_sync_jobs_integration_id ON sync_jobs(integration_id);
CREATE INDEX idx_sync_jobs_status ON sync_jobs(status);
CREATE INDEX idx_sync_jobs_created_at ON sync_jobs(created_at DESC);
CREATE INDEX idx_sync_jobs_integration_status ON sync_jobs(integration_id, status);

-- Create trigger to automatically update updated_at
CREATE OR REPLACE FUNCTION update_sync_jobs_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER trigger_sync_jobs_updated_at
    BEFORE UPDATE ON sync_jobs
    FOR EACH ROW
    EXECUTE FUNCTION update_sync_jobs_updated_at();

-- Add comments
COMMENT ON TABLE sync_jobs IS 'Tracks synchronization jobs for platform integrations';
COMMENT ON COLUMN sync_jobs.sync_type IS 'Type of sync: full (all data) or incremental (changes since last sync)';
COMMENT ON COLUMN sync_jobs.data_types IS 'Array of data types to sync (orders, customers, products)';
COMMENT ON COLUMN sync_jobs.status IS 'Current status of the sync job';
COMMENT ON COLUMN sync_jobs.metadata IS 'Additional job configuration and progress data';