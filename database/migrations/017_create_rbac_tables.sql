-- Roles Table
CREATE TABLE IF NOT EXISTS roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    permissions TEXT[] NOT NULL DEFAULT '{}',
    parent_role_id UUID REFERENCES roles(id) ON DELETE SET NULL,
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User Roles Junction Table
CREATE TABLE IF NOT EXISTS user_roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
    assigned_by UUID REFERENCES users(id) ON DELETE SET NULL,
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, role_id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_roles_name ON roles(name);
CREATE INDEX IF NOT EXISTS idx_roles_parent ON roles(parent_role_id);
CREATE INDEX IF NOT EXISTS idx_roles_active ON roles(active);
CREATE INDEX IF NOT EXISTS idx_user_roles_user_id ON user_roles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_roles_role_id ON user_roles(role_id);
CREATE INDEX IF NOT EXISTS idx_user_roles_assigned_by ON user_roles(assigned_by);

-- Composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_roles_active_name ON roles(active, name);
CREATE INDEX IF NOT EXISTS idx_user_roles_user_role ON user_roles(user_id, role_id);

-- Add trigger for updating updated_at on roles
DROP TRIGGER IF EXISTS update_roles_updated_at ON roles;
CREATE TRIGGER update_roles_updated_at
    BEFORE UPDATE ON roles
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Add constraints
ALTER TABLE roles ADD CONSTRAINT chk_roles_name_not_empty 
    CHECK (name IS NOT NULL AND length(trim(name)) > 0);

ALTER TABLE roles ADD CONSTRAINT chk_roles_no_self_parent 
    CHECK (id != parent_role_id);

-- Add default roles
INSERT INTO roles (name, description, permissions, active) VALUES 
    ('super_admin', 'Super Administrator with full access', ARRAY[
        'users.read', 'users.create', 'users.update', 'users.delete',
        'analytics.read', 'analytics.create', 'analytics.update', 'analytics.delete', 'analytics.export',
        'dashboard.read', 'dashboard.create', 'dashboard.update', 'dashboard.delete',
        'integrations.read', 'integrations.create', 'integrations.update', 'integrations.delete', 'integrations.configure',
        'settings.read', 'settings.update',
        'admin.users', 'admin.roles', 'admin.system', 'admin.security',
        'api.read', 'api.write', 'api.keys.manage',
        'billing.read', 'billing.manage',
        'support.read', 'support.respond'
    ], TRUE),
    
    ('admin', 'Administrator with most permissions', ARRAY[
        'users.read', 'users.create', 'users.update',
        'analytics.read', 'analytics.create', 'analytics.update', 'analytics.delete', 'analytics.export',
        'dashboard.read', 'dashboard.create', 'dashboard.update', 'dashboard.delete',
        'integrations.read', 'integrations.create', 'integrations.update', 'integrations.delete', 'integrations.configure',
        'settings.read', 'settings.update',
        'admin.users', 'admin.security',
        'api.read', 'api.write', 'api.keys.manage',
        'billing.read',
        'support.read', 'support.respond'
    ], TRUE),
    
    ('manager', 'Manager with team management permissions', ARRAY[
        'users.read', 'users.update',
        'analytics.read', 'analytics.create', 'analytics.update', 'analytics.export',
        'dashboard.read', 'dashboard.create', 'dashboard.update', 'dashboard.delete',
        'integrations.read', 'integrations.create', 'integrations.update', 'integrations.configure',
        'settings.read',
        'api.read', 'api.write', 'api.keys.manage',
        'billing.read',
        'support.read'
    ], TRUE),
    
    ('analyst', 'Data Analyst with analytics permissions', ARRAY[
        'analytics.read', 'analytics.create', 'analytics.update', 'analytics.export',
        'dashboard.read', 'dashboard.create', 'dashboard.update',
        'integrations.read',
        'api.read',
        'support.read'
    ], TRUE),
    
    ('user', 'Regular user with basic permissions', ARRAY[
        'analytics.read',
        'dashboard.read',
        'integrations.read',
        'api.read'
    ], TRUE),
    
    ('viewer', 'Read-only user', ARRAY[
        'analytics.read',
        'dashboard.read'
    ], TRUE)
ON CONFLICT (name) DO NOTHING;

-- Set up role hierarchy (admin inherits from manager, manager from analyst, etc.)
UPDATE roles SET parent_role_id = (SELECT id FROM roles WHERE name = 'super_admin') 
WHERE name = 'admin';

UPDATE roles SET parent_role_id = (SELECT id FROM roles WHERE name = 'admin') 
WHERE name = 'manager';

UPDATE roles SET parent_role_id = (SELECT id FROM roles WHERE name = 'manager') 
WHERE name = 'analyst';

UPDATE roles SET parent_role_id = (SELECT id FROM roles WHERE name = 'analyst') 
WHERE name = 'user';

UPDATE roles SET parent_role_id = (SELECT id FROM roles WHERE name = 'user') 
WHERE name = 'viewer';

-- Create function to prevent circular role dependencies
CREATE OR REPLACE FUNCTION check_role_hierarchy()
RETURNS TRIGGER AS $$
DECLARE
    parent_id UUID;
    current_id UUID;
    max_depth INTEGER := 10;
    depth INTEGER := 0;
BEGIN
    -- Check for circular dependency
    current_id := NEW.parent_role_id;
    
    WHILE current_id IS NOT NULL AND depth < max_depth LOOP
        IF current_id = NEW.id THEN
            RAISE EXCEPTION 'Circular role dependency detected';
        END IF;
        
        SELECT parent_role_id INTO parent_id FROM roles WHERE id = current_id;
        current_id := parent_id;
        depth := depth + 1;
    END LOOP;
    
    IF depth >= max_depth THEN
        RAISE EXCEPTION 'Role hierarchy too deep (max depth: %)', max_depth;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add trigger to check role hierarchy
DROP TRIGGER IF EXISTS check_role_hierarchy_trigger ON roles;
CREATE TRIGGER check_role_hierarchy_trigger
    BEFORE INSERT OR UPDATE ON roles
    FOR EACH ROW
    WHEN (NEW.parent_role_id IS NOT NULL)
    EXECUTE FUNCTION check_role_hierarchy();

-- Add Row Level Security
ALTER TABLE roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_roles ENABLE ROW LEVEL SECURITY;

-- Create policies for roles (only admins can manage roles)
CREATE POLICY roles_admin_policy ON roles
    FOR ALL TO authenticated_users
    USING (
        current_user_id() IN (
            SELECT ur.user_id 
            FROM user_roles ur 
            JOIN roles r ON ur.role_id = r.id 
            WHERE r.permissions @> ARRAY['admin.roles']
        )
    );

-- Create policies for user_roles
CREATE POLICY user_roles_admin_policy ON user_roles
    FOR ALL TO authenticated_users
    USING (
        current_user_id() IN (
            SELECT ur.user_id 
            FROM user_roles ur 
            JOIN roles r ON ur.role_id = r.id 
            WHERE r.permissions @> ARRAY['admin.roles', 'admin.users']
        )
        OR user_id = current_user_id() -- Users can see their own roles
    );

-- Grant permissions to authenticated users
GRANT SELECT ON roles TO authenticated_users;
GRANT SELECT ON user_roles TO authenticated_users;

-- Create function to get user's effective permissions
CREATE OR REPLACE FUNCTION get_user_permissions(user_uuid UUID)
RETURNS TEXT[] AS $$
DECLARE
    permissions TEXT[] := '{}';
    role_record RECORD;
    parent_permissions TEXT[];
BEGIN
    -- Get all roles assigned to the user
    FOR role_record IN 
        SELECT r.* FROM roles r
        JOIN user_roles ur ON r.id = ur.role_id
        WHERE ur.user_id = user_uuid AND r.active = TRUE
    LOOP
        -- Add direct permissions
        permissions := permissions || role_record.permissions;
        
        -- Get parent role permissions recursively
        parent_permissions := get_role_permissions_recursive(role_record.parent_role_id);
        permissions := permissions || parent_permissions;
    END LOOP;
    
    -- Remove duplicates
    SELECT ARRAY_AGG(DISTINCT perm) INTO permissions
    FROM unnest(permissions) AS perm;
    
    RETURN COALESCE(permissions, '{}');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to get role permissions recursively
CREATE OR REPLACE FUNCTION get_role_permissions_recursive(role_uuid UUID)
RETURNS TEXT[] AS $$
DECLARE
    permissions TEXT[] := '{}';
    role_record RECORD;
    parent_permissions TEXT[];
BEGIN
    -- If role_uuid is NULL, return empty array
    IF role_uuid IS NULL THEN
        RETURN '{}';
    END IF;
    
    -- Get the role
    SELECT * INTO role_record FROM roles WHERE id = role_uuid AND active = TRUE;
    
    -- If role doesn't exist or is inactive, return empty array
    IF NOT FOUND THEN
        RETURN '{}';
    END IF;
    
    -- Add this role's permissions
    permissions := role_record.permissions;
    
    -- Get parent role permissions recursively
    IF role_record.parent_role_id IS NOT NULL THEN
        parent_permissions := get_role_permissions_recursive(role_record.parent_role_id);
        permissions := permissions || parent_permissions;
    END IF;
    
    RETURN permissions;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to check if user has permission
CREATE OR REPLACE FUNCTION user_has_permission(user_uuid UUID, permission_name TEXT)
RETURNS BOOLEAN AS $$
DECLARE
    user_permissions TEXT[];
BEGIN
    user_permissions := get_user_permissions(user_uuid);
    RETURN permission_name = ANY(user_permissions);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to get role statistics
CREATE OR REPLACE FUNCTION get_role_stats()
RETURNS TABLE(
    total_roles INTEGER,
    active_roles INTEGER,
    inactive_roles INTEGER,
    total_assignments INTEGER,
    users_with_roles INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*)::INTEGER as total_roles,
        COUNT(CASE WHEN active THEN 1 END)::INTEGER as active_roles,
        COUNT(CASE WHEN NOT active THEN 1 END)::INTEGER as inactive_roles,
        (SELECT COUNT(*)::INTEGER FROM user_roles) as total_assignments,
        (SELECT COUNT(DISTINCT user_id)::INTEGER FROM user_roles) as users_with_roles
    FROM roles;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create view for role hierarchy
CREATE OR REPLACE VIEW role_hierarchy AS
WITH RECURSIVE role_tree AS (
    -- Base case: root roles (no parent)
    SELECT 
        id,
        name,
        description,
        permissions,
        parent_role_id,
        active,
        created_at,
        updated_at,
        0 as level,
        ARRAY[name] as path
    FROM roles
    WHERE parent_role_id IS NULL
    
    UNION ALL
    
    -- Recursive case: child roles
    SELECT 
        r.id,
        r.name,
        r.description,
        r.permissions,
        r.parent_role_id,
        r.active,
        r.created_at,
        r.updated_at,
        rt.level + 1,
        rt.path || r.name
    FROM roles r
    JOIN role_tree rt ON r.parent_role_id = rt.id
    WHERE rt.level < 10 -- Prevent infinite recursion
)
SELECT * FROM role_tree
ORDER BY level, name;

-- Grant access to the view
GRANT SELECT ON role_hierarchy TO authenticated_users;

-- Create indexes for the new functions
CREATE INDEX IF NOT EXISTS idx_roles_permissions_gin ON roles USING GIN(permissions);

-- Add comments for documentation
COMMENT ON TABLE roles IS 'System roles with hierarchical permissions';
COMMENT ON TABLE user_roles IS 'Junction table linking users to roles';
COMMENT ON COLUMN roles.permissions IS 'Array of permission strings for this role';
COMMENT ON COLUMN roles.parent_role_id IS 'Parent role ID for inheritance';
COMMENT ON COLUMN user_roles.assigned_by IS 'User who assigned this role';

COMMENT ON FUNCTION get_user_permissions(UUID) IS 'Get all effective permissions for a user including inherited permissions';
COMMENT ON FUNCTION get_role_permissions_recursive(UUID) IS 'Get permissions for a role including inherited permissions';
COMMENT ON FUNCTION user_has_permission(UUID, TEXT) IS 'Check if user has a specific permission';
COMMENT ON FUNCTION get_role_stats() IS 'Get statistics about roles and role assignments';

-- Create cleanup function for role assignments
CREATE OR REPLACE FUNCTION cleanup_role_assignments()
RETURNS INTEGER AS $$
DECLARE
    cleaned_count INTEGER := 0;
BEGIN
    -- Remove assignments for inactive roles
    DELETE FROM user_roles 
    WHERE role_id IN (SELECT id FROM roles WHERE active = FALSE);
    
    GET DIAGNOSTICS cleaned_count = ROW_COUNT;
    
    RETURN cleaned_count;
END;
$$ LANGUAGE plpgsql;

COMMENT ON FUNCTION cleanup_role_assignments() IS 'Clean up assignments for inactive roles';