-- OAuth2 Applications Table
CREATE TABLE IF NOT EXISTS oauth_applications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    client_id VARCHAR(50) UNIQUE NOT NULL,
    client_secret_hash TEXT NOT NULL,
    redirect_uris TEXT[] NOT NULL,
    scopes TEXT[] NOT NULL DEFAULT '{"read"}',
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- OAuth2 Authorization Codes Table
CREATE TABLE IF NOT EXISTS oauth_authorization_codes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    code VARCHAR(64) UNIQUE NOT NULL,
    client_id VARCHAR(50) NOT NULL REFERENCES oauth_applications(client_id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    scopes TEXT[] NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    used BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- OAuth2 Access Tokens Table
CREATE TABLE IF NOT EXISTS oauth_access_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    token_hash TEXT NOT NULL,
    client_id VARCHAR(50) NOT NULL REFERENCES oauth_applications(client_id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    scopes TEXT[] NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    revoked BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- API Keys Table
CREATE TABLE IF NOT EXISTS api_keys (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    key_id VARCHAR(16) UNIQUE NOT NULL,
    key_hash TEXT NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    scopes TEXT[] NOT NULL DEFAULT '{"read"}',
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    ip_whitelist INET[],
    rate_limit_override INTEGER,
    last_used_at TIMESTAMP WITH TIME ZONE,
    usage_count INTEGER DEFAULT 0,
    revoked BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- API Key Requests Table (for rate limiting)
CREATE TABLE IF NOT EXISTS api_key_requests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    key_id VARCHAR(16) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for OAuth2 tables
CREATE INDEX IF NOT EXISTS idx_oauth_applications_user_id ON oauth_applications(user_id);
CREATE INDEX IF NOT EXISTS idx_oauth_applications_client_id ON oauth_applications(client_id);
CREATE INDEX IF NOT EXISTS idx_oauth_auth_codes_client_id ON oauth_authorization_codes(client_id);
CREATE INDEX IF NOT EXISTS idx_oauth_auth_codes_user_id ON oauth_authorization_codes(user_id);
CREATE INDEX IF NOT EXISTS idx_oauth_auth_codes_code ON oauth_authorization_codes(code);
CREATE INDEX IF NOT EXISTS idx_oauth_auth_codes_expires_at ON oauth_authorization_codes(expires_at);
CREATE INDEX IF NOT EXISTS idx_oauth_access_tokens_token_hash ON oauth_access_tokens(token_hash);
CREATE INDEX IF NOT EXISTS idx_oauth_access_tokens_client_id ON oauth_access_tokens(client_id);
CREATE INDEX IF NOT EXISTS idx_oauth_access_tokens_user_id ON oauth_access_tokens(user_id);
CREATE INDEX IF NOT EXISTS idx_oauth_access_tokens_expires_at ON oauth_access_tokens(expires_at);

-- Indexes for API Keys tables
CREATE INDEX IF NOT EXISTS idx_api_keys_user_id ON api_keys(user_id);
CREATE INDEX IF NOT EXISTS idx_api_keys_key_id ON api_keys(key_id);
CREATE INDEX IF NOT EXISTS idx_api_keys_expires_at ON api_keys(expires_at);
CREATE INDEX IF NOT EXISTS idx_api_keys_revoked ON api_keys(revoked);
CREATE INDEX IF NOT EXISTS idx_api_key_requests_key_id ON api_key_requests(key_id);
CREATE INDEX IF NOT EXISTS idx_api_key_requests_created_at ON api_key_requests(created_at);

-- Composite indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_oauth_auth_codes_client_user ON oauth_authorization_codes(client_id, user_id);
CREATE INDEX IF NOT EXISTS idx_oauth_access_tokens_client_user ON oauth_access_tokens(client_id, user_id);
CREATE INDEX IF NOT EXISTS idx_api_keys_user_active ON api_keys(user_id, revoked, expires_at);
CREATE INDEX IF NOT EXISTS idx_api_key_requests_key_time ON api_key_requests(key_id, created_at);

-- Add triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers to relevant tables
DROP TRIGGER IF EXISTS update_oauth_applications_updated_at ON oauth_applications;
CREATE TRIGGER update_oauth_applications_updated_at
    BEFORE UPDATE ON oauth_applications
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_api_keys_updated_at ON api_keys;
CREATE TRIGGER update_api_keys_updated_at
    BEFORE UPDATE ON api_keys
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Add constraints
ALTER TABLE oauth_applications ADD CONSTRAINT chk_oauth_app_scopes
    CHECK (array_length(scopes, 1) > 0 AND 
           scopes <@ ARRAY['read', 'write', 'analytics', 'integrations', 'admin']);

ALTER TABLE oauth_applications ADD CONSTRAINT chk_oauth_app_redirect_uris
    CHECK (array_length(redirect_uris, 1) > 0);

ALTER TABLE oauth_authorization_codes ADD CONSTRAINT chk_oauth_code_scopes
    CHECK (array_length(scopes, 1) > 0);

ALTER TABLE oauth_access_tokens ADD CONSTRAINT chk_oauth_token_scopes
    CHECK (array_length(scopes, 1) > 0);

ALTER TABLE api_keys ADD CONSTRAINT chk_api_key_scopes
    CHECK (array_length(scopes, 1) > 0 AND 
           scopes <@ ARRAY['read', 'write', 'analytics', 'integrations', 'admin']);

ALTER TABLE api_keys ADD CONSTRAINT chk_api_key_rate_limit
    CHECK (rate_limit_override IS NULL OR rate_limit_override > 0);

-- Add security policies (Row Level Security)
ALTER TABLE oauth_applications ENABLE ROW LEVEL SECURITY;
ALTER TABLE oauth_authorization_codes ENABLE ROW LEVEL SECURITY;
ALTER TABLE oauth_access_tokens ENABLE ROW LEVEL SECURITY;
ALTER TABLE api_keys ENABLE ROW LEVEL SECURITY;
ALTER TABLE api_key_requests ENABLE ROW LEVEL SECURITY;

-- Create policies for OAuth applications (users can only access their own)
CREATE POLICY oauth_applications_user_policy ON oauth_applications
    FOR ALL TO authenticated_users
    USING (user_id = current_user_id());

-- Create policies for authorization codes
CREATE POLICY oauth_auth_codes_user_policy ON oauth_authorization_codes
    FOR ALL TO authenticated_users
    USING (user_id = current_user_id());

-- Create policies for access tokens
CREATE POLICY oauth_access_tokens_user_policy ON oauth_access_tokens
    FOR ALL TO authenticated_users
    USING (user_id = current_user_id());

-- Create policies for API keys
CREATE POLICY api_keys_user_policy ON api_keys
    FOR ALL TO authenticated_users
    USING (user_id = current_user_id());

-- Create policies for API key requests
CREATE POLICY api_key_requests_policy ON api_key_requests
    FOR ALL TO authenticated_users
    USING (key_id IN (SELECT key_id FROM api_keys WHERE user_id = current_user_id()));

-- Create function to get current user ID (placeholder - implement based on your auth system)
CREATE OR REPLACE FUNCTION current_user_id()
RETURNS UUID AS $$
BEGIN
    -- This should return the ID of the currently authenticated user
    -- Implementation depends on your authentication system
    RETURN COALESCE(
        current_setting('app.current_user_id', true)::UUID,
        '00000000-0000-0000-0000-000000000000'::UUID
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create role for authenticated users if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'authenticated_users') THEN
        CREATE ROLE authenticated_users;
    END IF;
END
$$;

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON oauth_applications TO authenticated_users;
GRANT SELECT, INSERT, UPDATE, DELETE ON oauth_authorization_codes TO authenticated_users;
GRANT SELECT, INSERT, UPDATE, DELETE ON oauth_access_tokens TO authenticated_users;
GRANT SELECT, INSERT, UPDATE, DELETE ON api_keys TO authenticated_users;
GRANT SELECT, INSERT, UPDATE, DELETE ON api_key_requests TO authenticated_users;

-- Grant sequence permissions
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated_users;

-- Create cleanup functions
CREATE OR REPLACE FUNCTION cleanup_expired_oauth_data()
RETURNS INTEGER AS $$
DECLARE
    cleaned_count INTEGER := 0;
BEGIN
    -- Delete expired authorization codes
    DELETE FROM oauth_authorization_codes WHERE expires_at < NOW();
    GET DIAGNOSTICS cleaned_count = ROW_COUNT;
    
    -- Mark expired access tokens as revoked
    UPDATE oauth_access_tokens 
    SET revoked = TRUE 
    WHERE expires_at < NOW() AND revoked = FALSE;
    
    -- Clean up old API key request logs (older than 7 days)
    DELETE FROM api_key_requests 
    WHERE created_at < NOW() - INTERVAL '7 days';
    
    RETURN cleaned_count;
END;
$$ LANGUAGE plpgsql;

-- Create function to get API key statistics
CREATE OR REPLACE FUNCTION get_api_key_stats(user_uuid UUID)
RETURNS TABLE(
    total_keys INTEGER,
    active_keys INTEGER,
    expired_keys INTEGER,
    revoked_keys INTEGER,
    total_requests_last_30_days BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*)::INTEGER as total_keys,
        COUNT(CASE WHEN NOT revoked AND expires_at > NOW() THEN 1 END)::INTEGER as active_keys,
        COUNT(CASE WHEN NOT revoked AND expires_at <= NOW() THEN 1 END)::INTEGER as expired_keys,
        COUNT(CASE WHEN revoked THEN 1 END)::INTEGER as revoked_keys,
        COALESCE((
            SELECT COUNT(*)
            FROM api_key_requests r
            JOIN api_keys k ON r.key_id = k.key_id
            WHERE k.user_id = user_uuid 
            AND r.created_at > NOW() - INTERVAL '30 days'
        ), 0) as total_requests_last_30_days
    FROM api_keys
    WHERE user_id = user_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add comments for documentation
COMMENT ON TABLE oauth_applications IS 'OAuth2 client applications registered by users';
COMMENT ON TABLE oauth_authorization_codes IS 'Temporary authorization codes for OAuth2 flow';
COMMENT ON TABLE oauth_access_tokens IS 'OAuth2 access tokens for API access';
COMMENT ON TABLE api_keys IS 'User-generated API keys for direct API access';
COMMENT ON TABLE api_key_requests IS 'Log of API key usage for rate limiting';

COMMENT ON COLUMN oauth_applications.client_id IS 'Public client identifier';
COMMENT ON COLUMN oauth_applications.client_secret_hash IS 'Hashed client secret';
COMMENT ON COLUMN oauth_applications.redirect_uris IS 'Allowed redirect URIs for OAuth flow';
COMMENT ON COLUMN oauth_applications.scopes IS 'Allowed scopes for this application';

COMMENT ON COLUMN api_keys.key_id IS 'Public key identifier (first part of API key)';
COMMENT ON COLUMN api_keys.key_hash IS 'Hashed full API key';
COMMENT ON COLUMN api_keys.ip_whitelist IS 'Allowed IP addresses for this key';
COMMENT ON COLUMN api_keys.rate_limit_override IS 'Custom rate limit for this key (requests per minute)';

-- Create indexes for cleanup operations
CREATE INDEX IF NOT EXISTS idx_oauth_auth_codes_cleanup ON oauth_authorization_codes(expires_at) WHERE expires_at < NOW();
CREATE INDEX IF NOT EXISTS idx_oauth_access_tokens_cleanup ON oauth_access_tokens(expires_at) WHERE expires_at < NOW() AND revoked = FALSE;
CREATE INDEX IF NOT EXISTS idx_api_key_requests_cleanup ON api_key_requests(created_at) WHERE created_at < NOW() - INTERVAL '7 days';