-- Comprehensive Audit Logging Tables
-- Designed for compliance with SOX, GDPR, HIPAA, PCI DSS, SOC2, and ISO27001

-- Main audit logs table
CREATE TABLE IF NOT EXISTS audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    audit_id VARCHAR(50) UNIQUE NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    event_type VARCHAR(100) NOT NULL,
    category VARCHAR(50) NOT NULL,
    severity VARCHAR(20) NOT NULL CHECK (severity IN ('info', 'low', 'medium', 'high', 'critical')),
    
    -- User and session information
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    session_id VARCHAR(100),
    ip_address INET,
    user_agent TEXT,
    request_id VARCHAR(100),
    
    -- Action details
    action VARCHAR(200),
    resource VARCHAR(200),
    resource_id VARCHAR(100),
    
    -- Data changes (JSONB for flexible structure)
    old_values JSONB,
    new_values JSONB,
    request_data JSONB,
    response_data JSONB,
    metadata JSONB DEFAULT '{}',
    
    -- Compliance and risk assessment
    compliance_tags TEXT[] DEFAULT '{}',
    risk_score INTEGER CHECK (risk_score BETWEEN 0 AND 10),
    
    -- Geolocation and device information
    geolocation JSONB,
    device_fingerprint VARCHAR(32),
    
    -- System context
    application_context JSONB,
    
    -- Security features
    encrypted_data TEXT,
    integrity_hash VARCHAR(64) NOT NULL,
    
    -- Indexing and partitioning support
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Audit log archive table for old records
CREATE TABLE IF NOT EXISTS audit_logs_archive (
    LIKE audit_logs INCLUDING ALL
);

-- Audit log summary table for reporting
CREATE TABLE IF NOT EXISTS audit_log_summaries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    summary_date DATE NOT NULL,
    event_type VARCHAR(100) NOT NULL,
    category VARCHAR(50) NOT NULL,
    severity VARCHAR(20) NOT NULL,
    total_events INTEGER NOT NULL DEFAULT 0,
    unique_users INTEGER NOT NULL DEFAULT 0,
    unique_ips INTEGER NOT NULL DEFAULT 0,
    avg_risk_score DECIMAL(3,2),
    max_risk_score INTEGER,
    compliance_events JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(summary_date, event_type, category, severity)
);

-- Failed audit logs table (for troubleshooting)
CREATE TABLE IF NOT EXISTS audit_log_failures (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    original_event JSONB NOT NULL,
    error_message TEXT NOT NULL,
    error_stack TEXT,
    retry_count INTEGER DEFAULT 0,
    resolved BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    resolved_at TIMESTAMP WITH TIME ZONE
);

-- Audit configuration table
CREATE TABLE IF NOT EXISTS audit_configuration (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    config_key VARCHAR(100) UNIQUE NOT NULL,
    config_value JSONB NOT NULL,
    description TEXT,
    updated_by UUID REFERENCES users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Data retention policies table
CREATE TABLE IF NOT EXISTS audit_retention_policies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    policy_name VARCHAR(100) UNIQUE NOT NULL,
    event_categories TEXT[] NOT NULL,
    retention_days INTEGER NOT NULL,
    archive_after_days INTEGER,
    compliance_requirement VARCHAR(50),
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Compliance reports table
CREATE TABLE IF NOT EXISTS compliance_reports (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    report_type VARCHAR(50) NOT NULL,
    framework VARCHAR(50) NOT NULL,
    period_start TIMESTAMP WITH TIME ZONE NOT NULL,
    period_end TIMESTAMP WITH TIME ZONE NOT NULL,
    report_data JSONB NOT NULL,
    generated_by UUID REFERENCES users(id) ON DELETE SET NULL,
    file_path TEXT,
    checksum VARCHAR(64),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Performance indexes for audit_logs
CREATE INDEX IF NOT EXISTS idx_audit_logs_timestamp ON audit_logs(timestamp);
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_event_type ON audit_logs(event_type);
CREATE INDEX IF NOT EXISTS idx_audit_logs_category ON audit_logs(category);
CREATE INDEX IF NOT EXISTS idx_audit_logs_severity ON audit_logs(severity);
CREATE INDEX IF NOT EXISTS idx_audit_logs_ip_address ON audit_logs(ip_address);
CREATE INDEX IF NOT EXISTS idx_audit_logs_resource ON audit_logs(resource);
CREATE INDEX IF NOT EXISTS idx_audit_logs_risk_score ON audit_logs(risk_score);
CREATE INDEX IF NOT EXISTS idx_audit_logs_session_id ON audit_logs(session_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_audit_id ON audit_logs(audit_id);

-- Composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_timestamp ON audit_logs(user_id, timestamp);
CREATE INDEX IF NOT EXISTS idx_audit_logs_category_timestamp ON audit_logs(category, timestamp);
CREATE INDEX IF NOT EXISTS idx_audit_logs_severity_timestamp ON audit_logs(severity, timestamp);
CREATE INDEX IF NOT EXISTS idx_audit_logs_risk_timestamp ON audit_logs(risk_score, timestamp);

-- GIN indexes for JSONB columns
CREATE INDEX IF NOT EXISTS idx_audit_logs_old_values_gin ON audit_logs USING GIN(old_values);
CREATE INDEX IF NOT EXISTS idx_audit_logs_new_values_gin ON audit_logs USING GIN(new_values);
CREATE INDEX IF NOT EXISTS idx_audit_logs_metadata_gin ON audit_logs USING GIN(metadata);
CREATE INDEX IF NOT EXISTS idx_audit_logs_compliance_tags_gin ON audit_logs USING GIN(compliance_tags);

-- Indexes for summary table
CREATE INDEX IF NOT EXISTS idx_audit_summaries_date ON audit_log_summaries(summary_date);
CREATE INDEX IF NOT EXISTS idx_audit_summaries_type_date ON audit_log_summaries(event_type, summary_date);

-- Indexes for archive table
CREATE INDEX IF NOT EXISTS idx_audit_archive_timestamp ON audit_logs_archive(timestamp);
CREATE INDEX IF NOT EXISTS idx_audit_archive_user_id ON audit_logs_archive(user_id);

-- Add triggers for updated_at timestamps
DROP TRIGGER IF EXISTS update_audit_configuration_updated_at ON audit_configuration;
CREATE TRIGGER update_audit_configuration_updated_at
    BEFORE UPDATE ON audit_configuration
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_audit_retention_policies_updated_at ON audit_retention_policies;
CREATE TRIGGER update_audit_retention_policies_updated_at
    BEFORE UPDATE ON audit_retention_policies
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Insert default audit configuration
INSERT INTO audit_configuration (config_key, config_value, description) VALUES 
    ('retention_default_days', '2555', 'Default retention period (7 years for SOX compliance)'),
    ('high_risk_retention_days', '3650', 'Retention for high-risk events (10 years)'),
    ('archive_after_days', '365', 'Archive audit logs after 1 year'),
    ('batch_size', '100', 'Batch size for bulk operations'),
    ('enable_encryption', 'true', 'Enable encryption for sensitive audit data'),
    ('enable_real_time_alerts', 'true', 'Enable real-time alerting for critical events'),
    ('max_log_size', '50000', 'Maximum size of individual audit log entries'),
    ('compliance_frameworks', '["SOX", "GDPR", "PCI_DSS", "SOC2"]', 'Active compliance frameworks')
ON CONFLICT (config_key) DO NOTHING;

-- Insert default retention policies
INSERT INTO audit_retention_policies (policy_name, event_categories, retention_days, archive_after_days, compliance_requirement) VALUES 
    ('Financial Data', '["data_modification", "data_access"]', 2555, 365, 'SOX'),
    ('Personal Data', '["authentication", "data_access", "data_modification"]', 2555, 365, 'GDPR'),
    ('Payment Data', '["data_access", "data_modification"]', 3650, 365, 'PCI_DSS'),
    ('Security Events', '["security", "authentication", "authorization"]', 2555, 365, 'SOC2'),
    ('System Admin', '["system_admin"]', 2555, 365, 'SOC2'),
    ('General Events', '["general", "performance"]', 365, 90, 'Internal')
ON CONFLICT (policy_name) DO NOTHING;

-- Create function to archive old audit logs
CREATE OR REPLACE FUNCTION archive_old_audit_logs()
RETURNS INTEGER AS $$
DECLARE
    archive_threshold TIMESTAMP WITH TIME ZONE;
    archived_count INTEGER := 0;
BEGIN
    -- Get archive threshold from configuration
    SELECT (config_value::TEXT)::INTEGER INTO archive_threshold
    FROM audit_configuration 
    WHERE config_key = 'archive_after_days';
    
    archive_threshold := NOW() - (COALESCE(archive_threshold, 365) || ' days')::INTERVAL;
    
    -- Move old records to archive table
    INSERT INTO audit_logs_archive 
    SELECT * FROM audit_logs 
    WHERE timestamp < archive_threshold;
    
    GET DIAGNOSTICS archived_count = ROW_COUNT;
    
    -- Delete archived records from main table
    DELETE FROM audit_logs 
    WHERE timestamp < archive_threshold;
    
    RETURN archived_count;
END;
$$ LANGUAGE plpgsql;

-- Create function to generate daily audit summaries
CREATE OR REPLACE FUNCTION generate_audit_summary(summary_date DATE DEFAULT CURRENT_DATE - INTERVAL '1 day')
RETURNS VOID AS $$
BEGIN
    INSERT INTO audit_log_summaries (
        summary_date, event_type, category, severity,
        total_events, unique_users, unique_ips,
        avg_risk_score, max_risk_score, compliance_events
    )
    SELECT 
        summary_date,
        event_type,
        category,
        severity,
        COUNT(*) as total_events,
        COUNT(DISTINCT user_id) as unique_users,
        COUNT(DISTINCT ip_address) as unique_ips,
        AVG(risk_score) as avg_risk_score,
        MAX(risk_score) as max_risk_score,
        jsonb_object_agg(
            unnested_tag, 
            COUNT(*) FILTER (WHERE unnested_tag IS NOT NULL)
        ) as compliance_events
    FROM audit_logs
    CROSS JOIN LATERAL unnest(compliance_tags) AS unnested_tag
    WHERE DATE(timestamp) = summary_date
    GROUP BY event_type, category, severity
    ON CONFLICT (summary_date, event_type, category, severity) 
    DO UPDATE SET
        total_events = EXCLUDED.total_events,
        unique_users = EXCLUDED.unique_users,
        unique_ips = EXCLUDED.unique_ips,
        avg_risk_score = EXCLUDED.avg_risk_score,
        max_risk_score = EXCLUDED.max_risk_score,
        compliance_events = EXCLUDED.compliance_events;
END;
$$ LANGUAGE plpgsql;

-- Create function to clean up old audit data based on retention policies
CREATE OR REPLACE FUNCTION cleanup_audit_data()
RETURNS TABLE(policy_name TEXT, deleted_count INTEGER) AS $$
DECLARE
    policy_record RECORD;
    delete_count INTEGER;
BEGIN
    FOR policy_record IN 
        SELECT * FROM audit_retention_policies WHERE active = TRUE
    LOOP
        -- Delete records older than retention period for this policy
        DELETE FROM audit_logs 
        WHERE category = ANY(policy_record.event_categories)
        AND timestamp < NOW() - (policy_record.retention_days || ' days')::INTERVAL;
        
        GET DIAGNOSTICS delete_count = ROW_COUNT;
        
        RETURN QUERY SELECT policy_record.policy_name, delete_count;
    END LOOP;
    
    -- Clean up archive table for very old data (beyond any retention policy)
    DELETE FROM audit_logs_archive 
    WHERE timestamp < NOW() - INTERVAL '10 years';
END;
$$ LANGUAGE plpgsql;

-- Create function to validate audit log integrity
CREATE OR REPLACE FUNCTION validate_audit_integrity(check_date DATE DEFAULT CURRENT_DATE)
RETURNS TABLE(
    total_logs BIGINT,
    integrity_failures BIGINT,
    validation_result TEXT
) AS $$
DECLARE
    total_count BIGINT;
    failed_count BIGINT;
BEGIN
    -- Count total logs for the date
    SELECT COUNT(*) INTO total_count
    FROM audit_logs 
    WHERE DATE(timestamp) = check_date;
    
    -- Count logs with potential integrity issues
    SELECT COUNT(*) INTO failed_count
    FROM audit_logs 
    WHERE DATE(timestamp) = check_date
    AND (
        integrity_hash IS NULL 
        OR LENGTH(integrity_hash) != 64
        OR audit_id IS NULL
    );
    
    RETURN QUERY SELECT 
        total_count,
        failed_count,
        CASE 
            WHEN failed_count = 0 THEN 'PASS'
            WHEN failed_count < total_count * 0.01 THEN 'WARNING'
            ELSE 'FAIL'
        END;
END;
$$ LANGUAGE plpgsql;

-- Create function to get audit statistics
CREATE OR REPLACE FUNCTION get_audit_statistics(
    start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW() - INTERVAL '30 days',
    end_date TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
RETURNS TABLE(
    metric_name TEXT,
    metric_value BIGINT,
    metric_description TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        'total_events'::TEXT,
        COUNT(*)::BIGINT,
        'Total audit events in period'::TEXT
    FROM audit_logs 
    WHERE timestamp BETWEEN start_date AND end_date
    
    UNION ALL
    
    SELECT 
        'unique_users'::TEXT,
        COUNT(DISTINCT user_id)::BIGINT,
        'Unique users with audit events'::TEXT
    FROM audit_logs 
    WHERE timestamp BETWEEN start_date AND end_date
    
    UNION ALL
    
    SELECT 
        'high_risk_events'::TEXT,
        COUNT(*)::BIGINT,
        'Events with risk score >= 7'::TEXT
    FROM audit_logs 
    WHERE timestamp BETWEEN start_date AND end_date
    AND risk_score >= 7
    
    UNION ALL
    
    SELECT 
        'security_events'::TEXT,
        COUNT(*)::BIGINT,
        'Security-related events'::TEXT
    FROM audit_logs 
    WHERE timestamp BETWEEN start_date AND end_date
    AND category = 'security'
    
    UNION ALL
    
    SELECT 
        'failed_operations'::TEXT,
        COUNT(*)::BIGINT,
        'Failed operations (response errors)'::TEXT
    FROM audit_logs 
    WHERE timestamp BETWEEN start_date AND end_date
    AND (metadata->>'statusCode')::INTEGER >= 400;
END;
$$ LANGUAGE plpgsql;

-- Add Row Level Security
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_log_summaries ENABLE ROW LEVEL SECURITY;
ALTER TABLE compliance_reports ENABLE ROW LEVEL SECURITY;

-- Create policies for audit logs (admins and auditors only)
CREATE POLICY audit_logs_admin_policy ON audit_logs
    FOR ALL TO authenticated_users
    USING (
        current_user_id() IN (
            SELECT ur.user_id 
            FROM user_roles ur 
            JOIN roles r ON ur.role_id = r.id 
            WHERE r.permissions @> ARRAY['admin.security', 'audit.read']
        )
    );

-- Create policies for compliance reports
CREATE POLICY compliance_reports_admin_policy ON compliance_reports
    FOR ALL TO authenticated_users
    USING (
        current_user_id() IN (
            SELECT ur.user_id 
            FROM user_roles ur 
            JOIN roles r ON ur.role_id = r.id 
            WHERE r.permissions @> ARRAY['admin.security', 'compliance.read']
        )
    );

-- Grant necessary permissions
GRANT SELECT, INSERT ON audit_logs TO authenticated_users;
GRANT SELECT ON audit_log_summaries TO authenticated_users;
GRANT SELECT ON compliance_reports TO authenticated_users;

-- Create view for recent high-risk events
CREATE OR REPLACE VIEW recent_high_risk_events AS
SELECT 
    audit_id,
    timestamp,
    event_type,
    category,
    severity,
    user_id,
    ip_address,
    action,
    resource,
    risk_score,
    compliance_tags
FROM audit_logs 
WHERE risk_score >= 7
AND timestamp > NOW() - INTERVAL '24 hours'
ORDER BY timestamp DESC, risk_score DESC;

-- Create view for compliance event summary
CREATE OR REPLACE VIEW compliance_event_summary AS
SELECT 
    unnest(compliance_tags) as framework,
    category,
    severity,
    COUNT(*) as event_count,
    AVG(risk_score) as avg_risk_score,
    MAX(timestamp) as last_event
FROM audit_logs 
WHERE compliance_tags IS NOT NULL 
AND array_length(compliance_tags, 1) > 0
AND timestamp > NOW() - INTERVAL '30 days'
GROUP BY unnest(compliance_tags), category, severity
ORDER BY event_count DESC;

-- Grant access to views
GRANT SELECT ON recent_high_risk_events TO authenticated_users;
GRANT SELECT ON compliance_event_summary TO authenticated_users;

-- Add comments for documentation
COMMENT ON TABLE audit_logs IS 'Comprehensive audit trail for all system activities';
COMMENT ON TABLE audit_log_summaries IS 'Daily summaries of audit events for reporting';
COMMENT ON TABLE compliance_reports IS 'Generated compliance reports for various frameworks';
COMMENT ON TABLE audit_retention_policies IS 'Data retention policies for different event types';

COMMENT ON FUNCTION archive_old_audit_logs() IS 'Archives old audit logs to separate table';
COMMENT ON FUNCTION generate_audit_summary(DATE) IS 'Generates daily audit event summaries';
COMMENT ON FUNCTION cleanup_audit_data() IS 'Cleans up audit data based on retention policies';
COMMENT ON FUNCTION validate_audit_integrity(DATE) IS 'Validates integrity of audit logs';
COMMENT ON FUNCTION get_audit_statistics(TIMESTAMP WITH TIME ZONE, TIMESTAMP WITH TIME ZONE) IS 'Returns audit statistics for a time period';

-- Create indexes for performance on archive table
CREATE INDEX IF NOT EXISTS idx_audit_archive_category_timestamp ON audit_logs_archive(category, timestamp);
CREATE INDEX IF NOT EXISTS idx_audit_archive_user_timestamp ON audit_logs_archive(user_id, timestamp);

-- Partitioning setup (for high-volume environments)
-- Note: This is commented out as it requires manual setup per deployment
/*
-- Enable partitioning by timestamp (monthly partitions)
-- This should be done during initial setup if expecting high volume

-- Create parent table (rename existing table first)
-- ALTER TABLE audit_logs RENAME TO audit_logs_old;

-- CREATE TABLE audit_logs (
--     -- Same structure as above
-- ) PARTITION BY RANGE (timestamp);

-- Create monthly partitions for current and future months
-- CREATE TABLE audit_logs_2024_01 PARTITION OF audit_logs
--     FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');
*/