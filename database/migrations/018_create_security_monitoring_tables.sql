-- Security Alerts Table
CREATE TABLE IF NOT EXISTS security_alerts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    alert_type VARCHAR(100) NOT NULL,
    severity VARCHAR(20) NOT NULL CHECK (severity IN ('LOW', 'MEDIUM', 'HIGH', 'CRITICAL')),
    details JSONB NOT NULL DEFAULT '{}',
    resolved BOOLEAN DEFAULT FALSE,
    resolved_by UUID REFERENCES users(id) ON DELETE SET NULL,
    resolved_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Blocked IPs Table
CREATE TABLE IF NOT EXISTS blocked_ips (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    ip_address INET UNIQUE NOT NULL,
    reason VARCHAR(255) NOT NULL,
    details JSONB DEFAULT '{}',
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Security Configuration Table
CREATE TABLE IF NOT EXISTS security_config (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    config_key VARCHAR(100) UNIQUE NOT NULL,
    config_value JSONB NOT NULL,
    description TEXT,
    updated_by UUID REFERENCES users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Failed Login Attempts Table (for persistent storage)
CREATE TABLE IF NOT EXISTS failed_login_attempts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    ip_address INET NOT NULL,
    user_email VARCHAR(255),
    user_agent TEXT,
    attempts INTEGER DEFAULT 1,
    last_attempt TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    blocked_until TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Security Metrics Table (for aggregated statistics)
CREATE TABLE IF NOT EXISTS security_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    metric_type VARCHAR(100) NOT NULL,
    metric_value NUMERIC NOT NULL,
    metadata JSONB DEFAULT '{}',
    period_start TIMESTAMP WITH TIME ZONE NOT NULL,
    period_end TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Threat Intelligence Table
CREATE TABLE IF NOT EXISTS threat_intelligence (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    indicator_type VARCHAR(50) NOT NULL CHECK (indicator_type IN ('IP', 'DOMAIN', 'URL', 'HASH', 'EMAIL')),
    indicator_value TEXT NOT NULL,
    threat_type VARCHAR(100) NOT NULL,
    confidence_level INTEGER CHECK (confidence_level BETWEEN 1 AND 100),
    source VARCHAR(255),
    description TEXT,
    first_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_security_alerts_type ON security_alerts(alert_type);
CREATE INDEX IF NOT EXISTS idx_security_alerts_severity ON security_alerts(severity);
CREATE INDEX IF NOT EXISTS idx_security_alerts_created_at ON security_alerts(created_at);
CREATE INDEX IF NOT EXISTS idx_security_alerts_resolved ON security_alerts(resolved);

CREATE INDEX IF NOT EXISTS idx_blocked_ips_ip ON blocked_ips(ip_address);
CREATE INDEX IF NOT EXISTS idx_blocked_ips_expires_at ON blocked_ips(expires_at);
CREATE INDEX IF NOT EXISTS idx_blocked_ips_created_at ON blocked_ips(created_at);

CREATE INDEX IF NOT EXISTS idx_security_config_key ON security_config(config_key);

CREATE INDEX IF NOT EXISTS idx_failed_logins_ip ON failed_login_attempts(ip_address);
CREATE INDEX IF NOT EXISTS idx_failed_logins_email ON failed_login_attempts(user_email);
CREATE INDEX IF NOT EXISTS idx_failed_logins_last_attempt ON failed_login_attempts(last_attempt);
CREATE INDEX IF NOT EXISTS idx_failed_logins_blocked_until ON failed_login_attempts(blocked_until);

CREATE INDEX IF NOT EXISTS idx_security_metrics_type ON security_metrics(metric_type);
CREATE INDEX IF NOT EXISTS idx_security_metrics_period ON security_metrics(period_start, period_end);
CREATE INDEX IF NOT EXISTS idx_security_metrics_created_at ON security_metrics(created_at);

CREATE INDEX IF NOT EXISTS idx_threat_intel_type ON threat_intelligence(indicator_type);
CREATE INDEX IF NOT EXISTS idx_threat_intel_value ON threat_intelligence(indicator_value);
CREATE INDEX IF NOT EXISTS idx_threat_intel_threat_type ON threat_intelligence(threat_type);
CREATE INDEX IF NOT EXISTS idx_threat_intel_active ON threat_intelligence(active);
CREATE INDEX IF NOT EXISTS idx_threat_intel_expires_at ON threat_intelligence(expires_at);

-- Composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_security_alerts_type_severity ON security_alerts(alert_type, severity);
CREATE INDEX IF NOT EXISTS idx_security_alerts_created_resolved ON security_alerts(created_at, resolved);
CREATE INDEX IF NOT EXISTS idx_blocked_ips_active ON blocked_ips(expires_at) WHERE expires_at > NOW();
CREATE INDEX IF NOT EXISTS idx_threat_intel_active_type ON threat_intelligence(active, indicator_type) WHERE active = TRUE;

-- Add GIN indexes for JSONB columns
CREATE INDEX IF NOT EXISTS idx_security_alerts_details_gin ON security_alerts USING GIN(details);
CREATE INDEX IF NOT EXISTS idx_blocked_ips_details_gin ON blocked_ips USING GIN(details);
CREATE INDEX IF NOT EXISTS idx_security_config_value_gin ON security_config USING GIN(config_value);
CREATE INDEX IF NOT EXISTS idx_security_metrics_metadata_gin ON security_metrics USING GIN(metadata);

-- Add triggers for updated_at timestamps
DROP TRIGGER IF EXISTS update_blocked_ips_updated_at ON blocked_ips;
CREATE TRIGGER update_blocked_ips_updated_at
    BEFORE UPDATE ON blocked_ips
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_security_config_updated_at ON security_config;
CREATE TRIGGER update_security_config_updated_at
    BEFORE UPDATE ON security_config
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_threat_intelligence_updated_at ON threat_intelligence;
CREATE TRIGGER update_threat_intelligence_updated_at
    BEFORE UPDATE ON threat_intelligence
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Insert default security configuration
INSERT INTO security_config (config_key, config_value, description) VALUES 
    ('max_failed_logins', '5', 'Maximum failed login attempts before blocking'),
    ('brute_force_window', '900', 'Time window for brute force detection (seconds)'),
    ('ip_block_duration', '3600', 'Duration to block IPs (seconds)'),
    ('suspicious_activity_threshold', '10', 'Threshold for suspicious activity alerts'),
    ('geo_anomaly_detection', 'true', 'Enable geographical anomaly detection'),
    ('auto_block_enabled', 'true', 'Enable automatic IP blocking'),
    ('alert_cooldown', '300', 'Cooldown between alerts (seconds)'),
    ('rate_limit_window', '900', 'Rate limit time window (seconds)'),
    ('rate_limit_max', '1000', 'Maximum requests per rate limit window'),
    ('session_timeout', '3600', 'Session timeout duration (seconds)')
ON CONFLICT (config_key) DO NOTHING;

-- Create function to check if IP is blocked
CREATE OR REPLACE FUNCTION is_ip_blocked(check_ip INET)
RETURNS TABLE(blocked BOOLEAN, reason VARCHAR(255), expires_at TIMESTAMP WITH TIME ZONE) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        TRUE as blocked,
        bi.reason,
        bi.expires_at
    FROM blocked_ips bi
    WHERE bi.ip_address = check_ip 
    AND bi.expires_at > NOW()
    LIMIT 1;
    
    -- If no active block found, return false
    IF NOT FOUND THEN
        RETURN QUERY SELECT FALSE, NULL::VARCHAR(255), NULL::TIMESTAMP WITH TIME ZONE;
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to get security dashboard stats
CREATE OR REPLACE FUNCTION get_security_dashboard_stats(time_period INTERVAL DEFAULT '24 HOURS')
RETURNS TABLE(
    total_events BIGINT,
    failed_logins BIGINT,
    successful_logins BIGINT,
    blocked_ips BIGINT,
    active_alerts BIGINT,
    suspicious_activities BIGINT,
    rate_limit_violations BIGINT,
    geographical_anomalies BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        (SELECT COUNT(*) FROM security_events WHERE created_at > NOW() - time_period) as total_events,
        (SELECT COUNT(*) FROM security_events WHERE event_type = 'FAILED_LOGIN' AND created_at > NOW() - time_period) as failed_logins,
        (SELECT COUNT(*) FROM security_events WHERE event_type = 'SUCCESSFUL_LOGIN' AND created_at > NOW() - time_period) as successful_logins,
        (SELECT COUNT(*) FROM blocked_ips WHERE created_at > NOW() - time_period) as blocked_ips,
        (SELECT COUNT(*) FROM security_alerts WHERE resolved = FALSE AND created_at > NOW() - time_period) as active_alerts,
        (SELECT COUNT(*) FROM security_events WHERE event_type = 'SUSPICIOUS_ACTIVITY' AND created_at > NOW() - time_period) as suspicious_activities,
        (SELECT COUNT(*) FROM security_events WHERE event_type = 'RATE_LIMIT_VIOLATION' AND created_at > NOW() - time_period) as rate_limit_violations,
        (SELECT COUNT(*) FROM security_events WHERE event_type = 'GEOGRAPHICAL_ANOMALY' AND created_at > NOW() - time_period) as geographical_anomalies;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to get top attacking IPs
CREATE OR REPLACE FUNCTION get_top_attacking_ips(time_period INTERVAL DEFAULT '24 HOURS', ip_limit INTEGER DEFAULT 10)
RETURNS TABLE(
    ip_address INET,
    attack_count BIGINT,
    attack_types TEXT[],
    last_attack TIMESTAMP WITH TIME ZONE,
    is_blocked BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        (se.details->>'ip')::INET as ip_address,
        COUNT(*) as attack_count,
        ARRAY_AGG(DISTINCT se.event_type) as attack_types,
        MAX(se.created_at) as last_attack,
        EXISTS(SELECT 1 FROM blocked_ips bi WHERE bi.ip_address = (se.details->>'ip')::INET AND bi.expires_at > NOW()) as is_blocked
    FROM security_events se
    WHERE se.created_at > NOW() - time_period
    AND se.event_type IN ('FAILED_LOGIN', 'SUSPICIOUS_ACTIVITY', 'RATE_LIMIT_VIOLATION', 'BRUTE_FORCE_ATTACK')
    AND se.details->>'ip' IS NOT NULL
    GROUP BY (se.details->>'ip')::INET
    ORDER BY attack_count DESC
    LIMIT ip_limit;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to cleanup old security data
CREATE OR REPLACE FUNCTION cleanup_security_data()
RETURNS TABLE(
    expired_blocks_removed INTEGER,
    old_events_removed INTEGER,
    old_alerts_removed INTEGER,
    old_metrics_removed INTEGER
) AS $$
DECLARE
    expired_blocks INTEGER := 0;
    old_events INTEGER := 0;
    old_alerts INTEGER := 0;
    old_metrics INTEGER := 0;
BEGIN
    -- Remove expired IP blocks
    DELETE FROM blocked_ips WHERE expires_at < NOW();
    GET DIAGNOSTICS expired_blocks = ROW_COUNT;
    
    -- Remove old security events (older than 90 days)
    DELETE FROM security_events WHERE created_at < NOW() - INTERVAL '90 DAYS';
    GET DIAGNOSTICS old_events = ROW_COUNT;
    
    -- Remove old resolved alerts (older than 30 days)
    DELETE FROM security_alerts WHERE resolved = TRUE AND resolved_at < NOW() - INTERVAL '30 DAYS';
    GET DIAGNOSTICS old_alerts = ROW_COUNT;
    
    -- Remove old security metrics (older than 1 year)
    DELETE FROM security_metrics WHERE created_at < NOW() - INTERVAL '1 YEAR';
    GET DIAGNOSTICS old_metrics = ROW_COUNT;
    
    -- Clean up old failed login attempts
    DELETE FROM failed_login_attempts WHERE last_attempt < NOW() - INTERVAL '7 DAYS';
    
    -- Clean up expired threat intelligence
    UPDATE threat_intelligence SET active = FALSE WHERE expires_at < NOW() AND active = TRUE;
    
    RETURN QUERY SELECT expired_blocks, old_events, old_alerts, old_metrics;
END;
$$ LANGUAGE plpgsql;

-- Create function to generate security metrics
CREATE OR REPLACE FUNCTION generate_security_metrics(start_time TIMESTAMP WITH TIME ZONE, end_time TIMESTAMP WITH TIME ZONE)
RETURNS VOID AS $$
DECLARE
    metric_record RECORD;
BEGIN
    -- Generate metrics for various event types
    FOR metric_record IN
        SELECT 
            event_type as metric_type,
            COUNT(*) as metric_value
        FROM security_events 
        WHERE created_at BETWEEN start_time AND end_time
        GROUP BY event_type
    LOOP
        INSERT INTO security_metrics (metric_type, metric_value, period_start, period_end)
        VALUES (metric_record.metric_type, metric_record.metric_value, start_time, end_time);
    END LOOP;
    
    -- Generate alert metrics
    FOR metric_record IN
        SELECT 
            'ALERTS_' || severity as metric_type,
            COUNT(*) as metric_value
        FROM security_alerts
        WHERE created_at BETWEEN start_time AND end_time
        GROUP BY severity
    LOOP
        INSERT INTO security_metrics (metric_type, metric_value, period_start, period_end)
        VALUES (metric_record.metric_type, metric_record.metric_value, start_time, end_time);
    END LOOP;
    
    -- Generate blocked IP metrics
    INSERT INTO security_metrics (metric_type, metric_value, period_start, period_end)
    SELECT 
        'BLOCKED_IPS' as metric_type,
        COUNT(*) as metric_value,
        start_time,
        end_time
    FROM blocked_ips
    WHERE created_at BETWEEN start_time AND end_time;
END;
$$ LANGUAGE plpgsql;

-- Add Row Level Security
ALTER TABLE security_alerts ENABLE ROW LEVEL SECURITY;
ALTER TABLE blocked_ips ENABLE ROW LEVEL SECURITY;
ALTER TABLE security_config ENABLE ROW LEVEL SECURITY;
ALTER TABLE failed_login_attempts ENABLE ROW LEVEL SECURITY;
ALTER TABLE security_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE threat_intelligence ENABLE ROW LEVEL SECURITY;

-- Create policies (only admins can access security data)
CREATE POLICY security_alerts_admin_policy ON security_alerts
    FOR ALL TO authenticated_users
    USING (
        current_user_id() IN (
            SELECT ur.user_id 
            FROM user_roles ur 
            JOIN roles r ON ur.role_id = r.id 
            WHERE r.permissions @> ARRAY['admin.security']
        )
    );

CREATE POLICY blocked_ips_admin_policy ON blocked_ips
    FOR ALL TO authenticated_users
    USING (
        current_user_id() IN (
            SELECT ur.user_id 
            FROM user_roles ur 
            JOIN roles r ON ur.role_id = r.id 
            WHERE r.permissions @> ARRAY['admin.security']
        )
    );

CREATE POLICY security_config_admin_policy ON security_config
    FOR ALL TO authenticated_users
    USING (
        current_user_id() IN (
            SELECT ur.user_id 
            FROM user_roles ur 
            JOIN roles r ON ur.role_id = r.id 
            WHERE r.permissions @> ARRAY['admin.security']
        )
    );

CREATE POLICY failed_login_attempts_admin_policy ON failed_login_attempts
    FOR ALL TO authenticated_users
    USING (
        current_user_id() IN (
            SELECT ur.user_id 
            FROM user_roles ur 
            JOIN roles r ON ur.role_id = r.id 
            WHERE r.permissions @> ARRAY['admin.security']
        )
    );

CREATE POLICY security_metrics_admin_policy ON security_metrics
    FOR ALL TO authenticated_users
    USING (
        current_user_id() IN (
            SELECT ur.user_id 
            FROM user_roles ur 
            JOIN roles r ON ur.role_id = r.id 
            WHERE r.permissions @> ARRAY['admin.security']
        )
    );

CREATE POLICY threat_intelligence_admin_policy ON threat_intelligence
    FOR ALL TO authenticated_users
    USING (
        current_user_id() IN (
            SELECT ur.user_id 
            FROM user_roles ur 
            JOIN roles r ON ur.role_id = r.id 
            WHERE r.permissions @> ARRAY['admin.security']
        )
    );

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON security_alerts TO authenticated_users;
GRANT SELECT, INSERT, UPDATE, DELETE ON blocked_ips TO authenticated_users;
GRANT SELECT, INSERT, UPDATE, DELETE ON security_config TO authenticated_users;
GRANT SELECT, INSERT, UPDATE, DELETE ON failed_login_attempts TO authenticated_users;
GRANT SELECT, INSERT, UPDATE, DELETE ON security_metrics TO authenticated_users;
GRANT SELECT, INSERT, UPDATE, DELETE ON threat_intelligence TO authenticated_users;

-- Add comments for documentation
COMMENT ON TABLE security_alerts IS 'Security alerts and incidents';
COMMENT ON TABLE blocked_ips IS 'Blocked IP addresses with expiration times';
COMMENT ON TABLE security_config IS 'Security configuration parameters';
COMMENT ON TABLE failed_login_attempts IS 'Failed login attempt tracking';
COMMENT ON TABLE security_metrics IS 'Aggregated security metrics';
COMMENT ON TABLE threat_intelligence IS 'Threat intelligence indicators';

COMMENT ON FUNCTION is_ip_blocked(INET) IS 'Check if an IP address is currently blocked';
COMMENT ON FUNCTION get_security_dashboard_stats(INTERVAL) IS 'Get security dashboard statistics for a time period';
COMMENT ON FUNCTION get_top_attacking_ips(INTERVAL, INTEGER) IS 'Get top attacking IP addresses';
COMMENT ON FUNCTION cleanup_security_data() IS 'Clean up old security data';
COMMENT ON FUNCTION generate_security_metrics(TIMESTAMP WITH TIME ZONE, TIMESTAMP WITH TIME ZONE) IS 'Generate security metrics for a time period';