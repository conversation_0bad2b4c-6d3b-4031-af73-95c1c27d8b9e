-- Advanced Authentication and Security Tables
-- Migration 015: Create tables for enhanced authentication and authorization

BEGIN;

-- Add MFA columns to users table
ALTER TABLE users ADD COLUMN IF NOT EXISTS mfa_enabled BOOLEAN DEFAULT FALSE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS mfa_secret TEXT;
ALTER TABLE users ADD COLUMN IF NOT EXISTS mfa_backup_codes JSONB;
ALTER TABLE users ADD COLUMN IF NOT EXISTS mfa_temp_secret TEXT;
ALTER TABLE users ADD COLUMN IF NOT EXISTS mfa_temp_secret_expires TIMESTAMP;
ALTER TABLE users ADD COLUMN IF NOT EXISTS mfa_activated_at TIMESTAMP;
ALTER TABLE users ADD COLUMN IF NOT EXISTS mfa_last_used_at TIMESTAMP;

-- Add password security columns
ALTER TABLE users ADD COLUMN IF NOT EXISTS password_changed_at TIMESTAMP DEFAULT NOW();
ALTER TABLE users ADD COLUMN IF NOT EXISTS password_expires_at TIMESTAMP;
ALTER TABLE users ADD COLUMN IF NOT EXISTS force_password_change BOOLEAN DEFAULT FALSE;

-- Add account lockout columns
ALTER TABLE users ADD COLUMN IF NOT EXISTS account_locked BOOLEAN DEFAULT FALSE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS account_locked_at TIMESTAMP;
ALTER TABLE users ADD COLUMN IF NOT EXISTS account_locked_until TIMESTAMP;
ALTER TABLE users ADD COLUMN IF NOT EXISTS failed_login_attempts INTEGER DEFAULT 0;

-- Add additional security columns
ALTER TABLE users ADD COLUMN IF NOT EXISTS security_questions JSONB;
ALTER TABLE users ADD COLUMN IF NOT EXISTS recovery_email TEXT;
ALTER TABLE users ADD COLUMN IF NOT EXISTS phone_number TEXT;
ALTER TABLE users ADD COLUMN IF NOT EXISTS phone_verified BOOLEAN DEFAULT FALSE;

-- Password history table
CREATE TABLE IF NOT EXISTS password_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    password_hash TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Create index on user_id for password history
CREATE INDEX IF NOT EXISTS idx_password_history_user_id ON password_history(user_id);
CREATE INDEX IF NOT EXISTS idx_password_history_created_at ON password_history(created_at);

-- User sessions table for advanced session management
CREATE TABLE IF NOT EXISTS user_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id TEXT UNIQUE NOT NULL,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    device_fingerprint TEXT,
    ip_address INET,
    user_agent TEXT,
    device_type TEXT,
    browser TEXT,
    os TEXT,
    location TEXT,
    country_code TEXT,
    suspicious BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW(),
    last_activity_at TIMESTAMP DEFAULT NOW(),
    expires_at TIMESTAMP NOT NULL,
    active BOOLEAN DEFAULT TRUE,
    terminated_at TIMESTAMP,
    termination_reason TEXT
);

-- Create indexes for sessions
CREATE INDEX IF NOT EXISTS idx_user_sessions_session_id ON user_sessions(session_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_active ON user_sessions(active) WHERE active = TRUE;
CREATE INDEX IF NOT EXISTS idx_user_sessions_expires_at ON user_sessions(expires_at);
CREATE INDEX IF NOT EXISTS idx_user_sessions_device_fingerprint ON user_sessions(device_fingerprint);

-- Login attempts table for tracking failed logins
CREATE TABLE IF NOT EXISTS login_attempts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    email TEXT,
    ip_address INET,
    user_agent TEXT,
    success BOOLEAN DEFAULT FALSE,
    failure_reason TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Create indexes for login attempts
CREATE INDEX IF NOT EXISTS idx_login_attempts_user_id ON login_attempts(user_id);
CREATE INDEX IF NOT EXISTS idx_login_attempts_email ON login_attempts(email);
CREATE INDEX IF NOT EXISTS idx_login_attempts_ip_address ON login_attempts(ip_address);
CREATE INDEX IF NOT EXISTS idx_login_attempts_created_at ON login_attempts(created_at);
CREATE INDEX IF NOT EXISTS idx_login_attempts_success ON login_attempts(success);

-- Security events table for audit logging
CREATE TABLE IF NOT EXISTS security_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    event_type TEXT NOT NULL,
    details JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Create indexes for security events
CREATE INDEX IF NOT EXISTS idx_security_events_user_id ON security_events(user_id);
CREATE INDEX IF NOT EXISTS idx_security_events_event_type ON security_events(event_type);
CREATE INDEX IF NOT EXISTS idx_security_events_created_at ON security_events(created_at);
CREATE INDEX IF NOT EXISTS idx_security_events_details ON security_events USING GIN(details);

-- API keys table for API authentication
CREATE TABLE IF NOT EXISTS api_keys (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    key_hash TEXT NOT NULL,
    key_prefix TEXT NOT NULL,
    scopes JSONB DEFAULT '[]'::jsonb,
    rate_limit INTEGER DEFAULT 1000,
    rate_limit_window INTEGER DEFAULT 3600, -- seconds
    allowed_ips INET[],
    expires_at TIMESTAMP,
    last_used_at TIMESTAMP,
    usage_count INTEGER DEFAULT 0,
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Create indexes for API keys
CREATE INDEX IF NOT EXISTS idx_api_keys_user_id ON api_keys(user_id);
CREATE INDEX IF NOT EXISTS idx_api_keys_tenant_id ON api_keys(tenant_id);
CREATE INDEX IF NOT EXISTS idx_api_keys_key_prefix ON api_keys(key_prefix);
CREATE INDEX IF NOT EXISTS idx_api_keys_active ON api_keys(active) WHERE active = TRUE;
CREATE INDEX IF NOT EXISTS idx_api_keys_expires_at ON api_keys(expires_at);

-- Permissions table for fine-grained access control
CREATE TABLE IF NOT EXISTS permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT UNIQUE NOT NULL,
    description TEXT,
    resource TEXT NOT NULL,
    action TEXT NOT NULL,
    conditions JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Role permissions junction table
CREATE TABLE IF NOT EXISTS role_permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    role_name TEXT NOT NULL,
    permission_id UUID NOT NULL REFERENCES permissions(id) ON DELETE CASCADE,
    granted BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(role_name, permission_id)
);

-- User permissions junction table (for user-specific permissions)
CREATE TABLE IF NOT EXISTS user_permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    permission_id UUID NOT NULL REFERENCES permissions(id) ON DELETE CASCADE,
    granted BOOLEAN DEFAULT TRUE,
    expires_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(user_id, permission_id)
);

-- Create indexes for permissions
CREATE INDEX IF NOT EXISTS idx_permissions_resource ON permissions(resource);
CREATE INDEX IF NOT EXISTS idx_permissions_action ON permissions(action);
CREATE INDEX IF NOT EXISTS idx_role_permissions_role_name ON role_permissions(role_name);
CREATE INDEX IF NOT EXISTS idx_role_permissions_permission_id ON role_permissions(permission_id);
CREATE INDEX IF NOT EXISTS idx_user_permissions_user_id ON user_permissions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_permissions_permission_id ON user_permissions(permission_id);

-- OAuth applications table for third-party integrations
CREATE TABLE IF NOT EXISTS oauth_applications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    description TEXT,
    client_id TEXT UNIQUE NOT NULL,
    client_secret_hash TEXT NOT NULL,
    redirect_uris TEXT[] NOT NULL,
    scopes TEXT[] DEFAULT ARRAY['read'],
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- OAuth authorization codes table
CREATE TABLE IF NOT EXISTS oauth_authorization_codes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    code TEXT UNIQUE NOT NULL,
    client_id TEXT NOT NULL REFERENCES oauth_applications(client_id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    scopes TEXT[],
    expires_at TIMESTAMP NOT NULL,
    used BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW()
);

-- OAuth access tokens table
CREATE TABLE IF NOT EXISTS oauth_access_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    token_hash TEXT UNIQUE NOT NULL,
    client_id TEXT NOT NULL REFERENCES oauth_applications(client_id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    scopes TEXT[],
    expires_at TIMESTAMP NOT NULL,
    revoked BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Create indexes for OAuth tables
CREATE INDEX IF NOT EXISTS idx_oauth_applications_client_id ON oauth_applications(client_id);
CREATE INDEX IF NOT EXISTS idx_oauth_applications_user_id ON oauth_applications(user_id);
CREATE INDEX IF NOT EXISTS idx_oauth_authorization_codes_code ON oauth_authorization_codes(code);
CREATE INDEX IF NOT EXISTS idx_oauth_authorization_codes_expires_at ON oauth_authorization_codes(expires_at);
CREATE INDEX IF NOT EXISTS idx_oauth_access_tokens_token_hash ON oauth_access_tokens(token_hash);
CREATE INDEX IF NOT EXISTS idx_oauth_access_tokens_expires_at ON oauth_access_tokens(expires_at);

-- Device trust table for trusted devices
CREATE TABLE IF NOT EXISTS trusted_devices (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    device_fingerprint TEXT NOT NULL,
    device_name TEXT,
    device_type TEXT,
    browser TEXT,
    os TEXT,
    location TEXT,
    trusted BOOLEAN DEFAULT FALSE,
    trust_expires_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    last_seen_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(user_id, device_fingerprint)
);

-- Create indexes for trusted devices
CREATE INDEX IF NOT EXISTS idx_trusted_devices_user_id ON trusted_devices(user_id);
CREATE INDEX IF NOT EXISTS idx_trusted_devices_fingerprint ON trusted_devices(device_fingerprint);
CREATE INDEX IF NOT EXISTS idx_trusted_devices_trusted ON trusted_devices(trusted) WHERE trusted = TRUE;

-- Rate limiting table for API and login attempts
CREATE TABLE IF NOT EXISTS rate_limits (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    identifier TEXT NOT NULL, -- IP address, user ID, API key, etc.
    action TEXT NOT NULL, -- login, api_call, password_reset, etc.
    count INTEGER DEFAULT 1,
    window_start TIMESTAMP DEFAULT NOW(),
    expires_at TIMESTAMP NOT NULL,
    UNIQUE(identifier, action, window_start)
);

-- Create indexes for rate limiting
CREATE INDEX IF NOT EXISTS idx_rate_limits_identifier ON rate_limits(identifier);
CREATE INDEX IF NOT EXISTS idx_rate_limits_action ON rate_limits(action);
CREATE INDEX IF NOT EXISTS idx_rate_limits_expires_at ON rate_limits(expires_at);

-- Insert default permissions
INSERT INTO permissions (name, description, resource, action) VALUES
    ('users.read', 'Read user information', 'users', 'read'),
    ('users.write', 'Create and update users', 'users', 'write'),
    ('users.delete', 'Delete users', 'users', 'delete'),
    ('users.admin', 'Full user administration', 'users', 'admin'),
    
    ('links.read', 'Read link information', 'links', 'read'),
    ('links.write', 'Create and update links', 'links', 'write'),
    ('links.delete', 'Delete links', 'links', 'delete'),
    ('links.admin', 'Full link administration', 'links', 'admin'),
    
    ('analytics.read', 'Read analytics data', 'analytics', 'read'),
    ('analytics.export', 'Export analytics data', 'analytics', 'export'),
    ('analytics.admin', 'Full analytics administration', 'analytics', 'admin'),
    
    ('integrations.read', 'Read integration settings', 'integrations', 'read'),
    ('integrations.write', 'Create and update integrations', 'integrations', 'write'),
    ('integrations.delete', 'Delete integrations', 'integrations', 'delete'),
    
    ('system.admin', 'System administration', 'system', 'admin'),
    ('system.logs', 'View system logs', 'system', 'logs'),
    ('system.metrics', 'View system metrics', 'system', 'metrics')
ON CONFLICT (name) DO NOTHING;

-- Insert default role permissions
INSERT INTO role_permissions (role_name, permission_id) 
SELECT 'user', id FROM permissions WHERE name IN (
    'users.read', 'links.read', 'links.write', 'analytics.read'
) ON CONFLICT DO NOTHING;

INSERT INTO role_permissions (role_name, permission_id) 
SELECT 'admin', id FROM permissions WHERE name IN (
    'users.read', 'users.write', 'links.read', 'links.write', 'links.delete',
    'analytics.read', 'analytics.export', 'integrations.read', 'integrations.write'
) ON CONFLICT DO NOTHING;

INSERT INTO role_permissions (role_name, permission_id) 
SELECT 'super_admin', id FROM permissions 
ON CONFLICT DO NOTHING;

-- Create function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at columns
DROP TRIGGER IF EXISTS update_api_keys_updated_at ON api_keys;
CREATE TRIGGER update_api_keys_updated_at 
    BEFORE UPDATE ON api_keys 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_oauth_applications_updated_at ON oauth_applications;
CREATE TRIGGER update_oauth_applications_updated_at 
    BEFORE UPDATE ON oauth_applications 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create function to clean up expired records
CREATE OR REPLACE FUNCTION cleanup_expired_auth_records()
RETURNS void AS $$
BEGIN
    -- Clean up expired sessions
    UPDATE user_sessions 
    SET active = FALSE, terminated_at = NOW(), termination_reason = 'EXPIRED'
    WHERE active = TRUE AND expires_at < NOW();
    
    -- Clean up expired OAuth authorization codes
    DELETE FROM oauth_authorization_codes 
    WHERE expires_at < NOW() OR used = TRUE;
    
    -- Clean up expired OAuth access tokens
    UPDATE oauth_access_tokens 
    SET revoked = TRUE 
    WHERE expires_at < NOW() AND revoked = FALSE;
    
    -- Clean up old login attempts (keep last 30 days)
    DELETE FROM login_attempts 
    WHERE created_at < NOW() - INTERVAL '30 days';
    
    -- Clean up old security events (keep last 90 days)
    DELETE FROM security_events 
    WHERE created_at < NOW() - INTERVAL '90 days';
    
    -- Clean up expired rate limits
    DELETE FROM rate_limits 
    WHERE expires_at < NOW();
    
    -- Clean up old password history (keep last 24 entries per user)
    DELETE FROM password_history ph1
    WHERE ph1.id NOT IN (
        SELECT ph2.id 
        FROM password_history ph2 
        WHERE ph2.user_id = ph1.user_id 
        ORDER BY ph2.created_at DESC 
        LIMIT 24
    );
END;
$$ LANGUAGE plpgsql;

-- Add constraints for data integrity
ALTER TABLE users ADD CONSTRAINT check_email_format 
    CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$');

ALTER TABLE api_keys ADD CONSTRAINT check_rate_limit_positive 
    CHECK (rate_limit > 0);

ALTER TABLE api_keys ADD CONSTRAINT check_rate_limit_window_positive 
    CHECK (rate_limit_window > 0);

-- Add comments for documentation
COMMENT ON TABLE password_history IS 'Stores password history to prevent reuse';
COMMENT ON TABLE user_sessions IS 'Advanced session management with device tracking';
COMMENT ON TABLE login_attempts IS 'Tracks login attempts for security monitoring';
COMMENT ON TABLE security_events IS 'Audit log for security-related events';
COMMENT ON TABLE api_keys IS 'API key management for programmatic access';
COMMENT ON TABLE permissions IS 'Fine-grained permission definitions';
COMMENT ON TABLE oauth_applications IS 'OAuth2 client applications for third-party access';
COMMENT ON TABLE trusted_devices IS 'Device trust management for enhanced security';
COMMENT ON TABLE rate_limits IS 'Rate limiting data for various actions';

COMMIT;