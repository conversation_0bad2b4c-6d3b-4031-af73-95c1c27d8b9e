# PostgreSQL Performance Monitoring Configuration
# This configuration enables pg_stat_statements for query performance tracking

# pg_stat_statements extension for query statistics
shared_preload_libraries = 'pg_stat_statements'

# pg_stat_statements settings
pg_stat_statements.max = 10000
pg_stat_statements.track = all
pg_stat_statements.track_utility = on
pg_stat_statements.save = on

# Performance monitoring settings
log_statement = 'all'
log_duration = on
log_min_duration_statement = 100  # Log queries taking longer than 100ms
log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '
log_checkpoints = on
log_connections = on
log_disconnections = on
log_lock_waits = on

# Memory settings for better performance
shared_buffers = 256MB
work_mem = 16MB
maintenance_work_mem = 64MB
effective_cache_size = 1GB

# Checkpoint settings
checkpoint_completion_target = 0.9
wal_buffers = 16MB

# Connection settings
max_connections = 200

# Query planner settings
random_page_cost = 1.1
effective_io_concurrency = 200

# Autovacuum settings
autovacuum = on
autovacuum_analyze_scale_factor = 0.1
autovacuum_vacuum_scale_factor = 0.2

# Statistics settings
track_activities = on
track_counts = on
track_io_timing = on
track_functions = all