const autocannon = require('autocannon');
const { performance } = require('perf_hooks');
const request = require('supertest');

describe('Performance and Load Tests', () => {
  let apps = {};
  let authToken;
  let testBaseUrl;
  let performanceResults = {};
  
  beforeAll(async () => {
    // Initialize apps
    apps.dashboard = require('../../services/dashboard/src/index');
    apps.analytics = require('../../services/analytics/src/app');
    apps.integration = require('../../services/integration/src/index');
    
    // Get base URL for external testing
    testBaseUrl = process.env.TEST_BASE_URL || 'http://localhost:3000';
    
    // Setup authentication
    await setupTestAuth();
    
    console.log('🚀 Starting performance tests...');
  });
  
  afterAll(async () => {
    // Generate performance report
    generatePerformanceReport();
  });
  
  describe('API Response Time Tests', () => {
    test('Authentication endpoint performance', async () => {
      const startTime = performance.now();
      
      const response = await request(apps.dashboard)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'test_password_123'
        });
      
      const endTime = performance.now();
      const responseTime = endTime - startTime;
      
      expect(response.status).toBe(200);
      expect(responseTime).toBeLessThan(500); // Should respond within 500ms
      
      performanceResults.authResponseTime = responseTime;
    });
    
    test('Link creation performance', async () => {
      const measurements = [];
      const iterations = 10;
      
      for (let i = 0; i < iterations; i++) {
        const startTime = performance.now();
        
        const response = await request(apps.dashboard)
          .post('/api/links')
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            originalUrl: `https://example.com/product/${i}`,
            title: `Performance Test Link ${i}`
          });
        
        const endTime = performance.now();
        const responseTime = endTime - startTime;
        
        expect(response.status).toBe(201);
        measurements.push(responseTime);
      }
      
      const avgResponseTime = measurements.reduce((a, b) => a + b, 0) / measurements.length;
      const maxResponseTime = Math.max(...measurements);
      const minResponseTime = Math.min(...measurements);
      
      expect(avgResponseTime).toBeLessThan(300); // Average should be under 300ms
      expect(maxResponseTime).toBeLessThan(1000); // Max should be under 1s
      
      performanceResults.linkCreation = {
        average: avgResponseTime,
        max: maxResponseTime,
        min: minResponseTime,
        measurements
      };
    });
    
    test('Analytics query performance', async () => {
      const queries = [
        { endpoint: '/api/analytics/overview', params: { timeRange: '7d' } },
        { endpoint: '/api/analytics/links/performance', params: { timeRange: '30d', limit: 100 } },
        { endpoint: '/api/analytics/cohorts', params: { type: 'user', timeRange: '30d' } },
        { endpoint: '/api/analytics/funnels', params: { events: JSON.stringify(['click', 'view']) } }
      ];
      
      const results = [];
      
      for (const query of queries) {
        const startTime = performance.now();
        
        const response = await request(apps.analytics)
          .get(query.endpoint)
          .query(query.params);
        
        const endTime = performance.now();
        const responseTime = endTime - startTime;
        
        expect(response.status).toBe(200);
        expect(responseTime).toBeLessThan(2000); // Analytics queries should be under 2s
        
        results.push({
          endpoint: query.endpoint,
          responseTime,
          dataSize: JSON.stringify(response.body).length
        });
      }
      
      performanceResults.analyticsQueries = results;
    });
    
    test('Database query optimization', async () => {
      // Test complex analytics query performance
      const complexQuery = {
        timeRange: '90d',
        groupBy: 'day',
        metrics: ['clicks', 'conversions', 'revenue'],
        dimensions: ['source', 'medium', 'campaign'],
        filters: {
          country: ['US', 'CA', 'GB'],
          device: ['desktop', 'mobile']
        }
      };
      
      const startTime = performance.now();
      
      const response = await request(apps.analytics)
        .post('/api/analytics/query')
        .send(complexQuery);
      
      const endTime = performance.now();
      const responseTime = endTime - startTime;
      
      expect(response.status).toBe(200);
      expect(responseTime).toBeLessThan(5000); // Complex queries should be under 5s
      
      performanceResults.complexQuery = responseTime;
    });
  });
  
  describe('Load Testing', () => {
    test('Link redirect endpoint load test', async () => {
      console.log('🔗 Testing link redirect performance...');
      
      const result = await autocannon({
        url: `${testBaseUrl}/api/r/test123`, // Mock short code
        connections: 100,
        pipelining: 1,
        duration: 30, // 30 seconds
        headers: {
          'User-Agent': 'LoadTester/1.0'
        }
      });
      
      expect(result.errors).toBe(0);
      expect(result.timeouts).toBe(0);
      expect(result.non2xx).toBeLessThan(result.requests.total * 0.01); // Less than 1% errors
      expect(result.latency.average).toBeLessThan(100); // Average latency under 100ms
      
      performanceResults.linkRedirectLoad = {
        requestsPerSecond: result.requests.average,
        averageLatency: result.latency.average,
        p99Latency: result.latency.p99,
        totalRequests: result.requests.total,
        errors: result.errors
      };
    });
    
    test('API authentication load test', async () => {
      console.log('🔐 Testing authentication performance...');
      
      const result = await autocannon({
        url: `${testBaseUrl}/api/auth/verify`,
        connections: 50,
        pipelining: 1,
        duration: 20,
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      });
      
      expect(result.errors).toBe(0);
      expect(result.non2xx).toBeLessThan(result.requests.total * 0.05); // Less than 5% errors
      expect(result.latency.average).toBeLessThan(200); // Average latency under 200ms
      
      performanceResults.authLoad = {
        requestsPerSecond: result.requests.average,
        averageLatency: result.latency.average,
        p95Latency: result.latency.p95,
        totalRequests: result.requests.total
      };
    });
    
    test('Analytics API load test', async () => {
      console.log('📊 Testing analytics API performance...');
      
      const result = await autocannon({
        url: `${testBaseUrl}/api/analytics/overview?timeRange=7d`,
        connections: 20,
        pipelining: 1,
        duration: 30,
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      });
      
      expect(result.errors).toBe(0);
      expect(result.non2xx).toBeLessThan(result.requests.total * 0.1); // Less than 10% errors
      expect(result.latency.average).toBeLessThan(500); // Average latency under 500ms
      
      performanceResults.analyticsLoad = {
        requestsPerSecond: result.requests.average,
        averageLatency: result.latency.average,
        p95Latency: result.latency.p95,
        throughput: result.throughput.average
      };
    });
  });
  
  describe('Memory and Resource Usage Tests', () => {
    test('Memory usage under load', async () => {
      const initialMemory = process.memoryUsage();
      
      // Simulate high load operations
      const promises = [];
      for (let i = 0; i < 1000; i++) {
        promises.push(
          request(apps.dashboard)
            .get('/api/links')
            .set('Authorization', `Bearer ${authToken}`)
        );
      }
      
      await Promise.all(promises);
      
      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }
      
      const finalMemory = process.memoryUsage();
      const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;
      
      // Memory increase should be reasonable (less than 100MB)
      expect(memoryIncrease).toBeLessThan(100 * 1024 * 1024);
      
      performanceResults.memoryUsage = {
        initial: initialMemory,
        final: finalMemory,
        increase: memoryIncrease
      };
    });
    
    test('Database connection pool performance', async () => {
      const startTime = performance.now();
      
      // Simulate many concurrent database operations
      const promises = [];
      for (let i = 0; i < 100; i++) {
        promises.push(
          request(apps.analytics)
            .get('/api/analytics/health')
        );
      }
      
      const results = await Promise.all(promises);
      const endTime = performance.now();
      
      const totalTime = endTime - startTime;
      const successfulRequests = results.filter(r => r.status === 200).length;
      
      expect(successfulRequests).toBe(100);
      expect(totalTime).toBeLessThan(5000); // Should complete within 5 seconds
      
      performanceResults.dbConnectionPool = {
        totalTime,
        successfulRequests,
        averageTime: totalTime / 100
      };
    });
    
    test('Redis cache performance', async () => {
      const cacheOperations = [];
      
      // Test cache write performance
      const writeStart = performance.now();
      for (let i = 0; i < 1000; i++) {
        cacheOperations.push(
          request(apps.dashboard)
            .post('/api/cache/test')
            .send({ key: `test_key_${i}`, value: `test_value_${i}` })
        );
      }
      await Promise.all(cacheOperations);
      const writeEnd = performance.now();
      
      // Test cache read performance
      const readStart = performance.now();
      const readOperations = [];
      for (let i = 0; i < 1000; i++) {
        readOperations.push(
          request(apps.dashboard)
            .get(`/api/cache/test_key_${i}`)
        );
      }
      await Promise.all(readOperations);
      const readEnd = performance.now();
      
      const writeTime = writeEnd - writeStart;
      const readTime = readEnd - readStart;
      
      expect(writeTime).toBeLessThan(2000); // Writes should be under 2s
      expect(readTime).toBeLessThan(1000); // Reads should be under 1s
      
      performanceResults.redisCache = {
        writeTime,
        readTime,
        writeOpsPerSecond: 1000 / (writeTime / 1000),
        readOpsPerSecond: 1000 / (readTime / 1000)
      };
    });
  });
  
  describe('Concurrent User Simulation', () => {
    test('Simulate 100 concurrent users', async () => {
      console.log('👥 Simulating 100 concurrent users...');
      
      const userSessions = [];
      
      for (let i = 0; i < 100; i++) {
        userSessions.push(simulateUserSession(i));
      }
      
      const startTime = performance.now();
      const results = await Promise.all(userSessions);
      const endTime = performance.now();
      
      const totalTime = endTime - startTime;
      const successfulSessions = results.filter(r => r.success).length;
      const averageSessionTime = results.reduce((sum, r) => sum + r.duration, 0) / results.length;
      
      expect(successfulSessions).toBeGreaterThanOrEqual(95); // At least 95% success rate
      expect(totalTime).toBeLessThan(30000); // Complete within 30 seconds
      
      performanceResults.concurrentUsers = {
        totalUsers: 100,
        successfulSessions,
        totalTime,
        averageSessionTime,
        successRate: (successfulSessions / 100) * 100
      };
    });
  });
  
  describe('Stress Testing', () => {
    test('API breaking point test', async () => {
      console.log('💥 Testing API breaking point...');
      
      let successfulRequests = 0;
      let totalRequests = 0;
      let errors = 0;
      
      // Gradually increase load until we hit breaking point
      for (let connections = 10; connections <= 200; connections += 10) {
        try {
          const result = await autocannon({
            url: `${testBaseUrl}/api/health`,
            connections,
            pipelining: 1,
            duration: 10 // Short duration for stress test
          });
          
          totalRequests += result.requests.total;
          successfulRequests += (result.requests.total - result.non2xx);
          errors += result.non2xx;
          
          // If error rate exceeds 50%, we've found the breaking point
          const errorRate = result.non2xx / result.requests.total;
          if (errorRate > 0.5) {
            console.log(`Breaking point reached at ${connections} connections`);
            break;
          }
        } catch (error) {
          console.log(`Error at ${connections} connections:`, error.message);
          break;
        }
      }
      
      const overallSuccessRate = (successfulRequests / totalRequests) * 100;
      
      performanceResults.stressTest = {
        totalRequests,
        successfulRequests,
        errors,
        successRate: overallSuccessRate
      };
      
      // We should handle at least moderate load gracefully
      expect(overallSuccessRate).toBeGreaterThan(80);
    });
  });
  
  // Helper functions
  async function setupTestAuth() {
    try {
      const response = await request(apps.dashboard)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'test_password_123'
        });
      
      if (response.status === 200) {
        authToken = response.body.token;
      } else {
        // Create test user if login fails
        await request(apps.dashboard)
          .post('/api/auth/register')
          .send({
            email: '<EMAIL>',
            password: 'test_password_123',
            firstName: 'Test',
            lastName: 'User',
            tenantName: 'Test Tenant'
          });
        
        const loginResponse = await request(apps.dashboard)
          .post('/api/auth/login')
          .send({
            email: '<EMAIL>',
            password: 'test_password_123'
          });
        
        authToken = loginResponse.body.token;
      }
    } catch (error) {
      console.error('Failed to setup test auth:', error);
      throw error;
    }
  }
  
  async function simulateUserSession(userId) {
    const sessionStart = performance.now();
    
    try {
      // Simulate typical user workflow
      
      // 1. Login (already have token)
      
      // 2. View dashboard
      await request(apps.dashboard)
        .get('/api/dashboard')
        .set('Authorization', `Bearer ${authToken}`);
      
      // 3. Create a link
      await request(apps.dashboard)
        .post('/api/links')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          originalUrl: `https://example.com/user/${userId}`,
          title: `User ${userId} Link`
        });
      
      // 4. View analytics
      await request(apps.analytics)
        .get('/api/analytics/overview?timeRange=7d');
      
      // 5. Update profile
      await request(apps.dashboard)
        .put('/api/users/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          preferences: { theme: 'dark' }
        });
      
      const sessionEnd = performance.now();
      
      return {
        success: true,
        duration: sessionEnd - sessionStart,
        userId
      };
    } catch (error) {
      const sessionEnd = performance.now();
      
      return {
        success: false,
        duration: sessionEnd - sessionStart,
        userId,
        error: error.message
      };
    }
  }
  
  function generatePerformanceReport() {
    console.log('\n📊 Performance Test Report');
    console.log('='.repeat(50));
    
    if (performanceResults.authResponseTime) {
      console.log(`🔐 Authentication Response Time: ${performanceResults.authResponseTime.toFixed(2)}ms`);
    }
    
    if (performanceResults.linkCreation) {
      console.log(`🔗 Link Creation Average: ${performanceResults.linkCreation.average.toFixed(2)}ms`);
      console.log(`    Max: ${performanceResults.linkCreation.max.toFixed(2)}ms`);
      console.log(`    Min: ${performanceResults.linkCreation.min.toFixed(2)}ms`);
    }
    
    if (performanceResults.linkRedirectLoad) {
      console.log(`🔀 Link Redirect Load Test:`);
      console.log(`    Requests/sec: ${performanceResults.linkRedirectLoad.requestsPerSecond.toFixed(2)}`);
      console.log(`    Avg Latency: ${performanceResults.linkRedirectLoad.averageLatency.toFixed(2)}ms`);
      console.log(`    P99 Latency: ${performanceResults.linkRedirectLoad.p99Latency.toFixed(2)}ms`);
    }
    
    if (performanceResults.concurrentUsers) {
      console.log(`👥 Concurrent Users Test:`);
      console.log(`    Success Rate: ${performanceResults.concurrentUsers.successRate.toFixed(2)}%`);
      console.log(`    Avg Session Time: ${performanceResults.concurrentUsers.averageSessionTime.toFixed(2)}ms`);
    }
    
    if (performanceResults.stressTest) {
      console.log(`💥 Stress Test:`);
      console.log(`    Total Requests: ${performanceResults.stressTest.totalRequests}`);
      console.log(`    Success Rate: ${performanceResults.stressTest.successRate.toFixed(2)}%`);
    }
    
    console.log('='.repeat(50));
    
    // Save report to file
    const fs = require('fs');
    const reportPath = './testing/reports/performance-report.json';
    fs.writeFileSync(reportPath, JSON.stringify(performanceResults, null, 2));
    console.log(`📄 Detailed report saved to: ${reportPath}`);
  }
});