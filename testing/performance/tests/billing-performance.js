import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate, Counter } from 'k6/metrics';

// Custom metrics for billing service
export const billingErrors = new Rate('billing_errors');
export const webhookProcessed = new Counter('webhooks_processed');
export const subscriptionOps = new Counter('subscription_operations');

// Billing-focused performance test
export const options = {
  stages: [
    { duration: '2m', target: 25 },
    { duration: '8m', target: 25 },
    { duration: '2m', target: 0 },
  ],
  thresholds: {
    http_req_duration: ['p(95)<500'],
    http_req_failed: ['rate<0.02'],
    billing_errors: ['rate<0.02'],
  },
};

const BASE_URL = __ENV.BILLING_URL || 'http://localhost:3003';
const testApiKey = __ENV.TEST_API_KEY || 'test-api-key';

export default function () {
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${testApiKey}`,
  };

  // Test different billing workloads
  testSubscriptionOperations(headers);
  testWebhookProcessing(headers);
  testUsageTracking(headers);
  testBillingQueries(headers);
  testPaymentOperations(headers);
  
  sleep(1);
}

function testSubscriptionOperations(headers) {
  // List subscriptions with pagination
  let response = http.get(`${BASE_URL}/api/subscriptions?limit=50&offset=0`, {
    headers,
    tags: { name: 'subscription_list' },
  });
  
  let success = check(response, {
    'subscription list status 200': (r) => r.status === 200,
    'subscription list fast': (r) => r.timings.duration < 200,
    'subscription list has data': (r) => {
      try {
        const data = JSON.parse(r.body);
        return data.success && Array.isArray(data.data.subscriptions);
      } catch {
        return false;
      }
    }
  });
  if (!success) billingErrors.add(1);
  subscriptionOps.add(1);

  // Get subscription metrics
  response = http.get(`${BASE_URL}/api/subscriptions/metrics`, {
    headers,
    tags: { name: 'subscription_metrics' },
  });
  
  success = check(response, {
    'subscription metrics status 200': (r) => r.status === 200,
    'subscription metrics time': (r) => r.timings.duration < 300,
  });
  if (!success) billingErrors.add(1);

  // Validate subscription (frequently called for API access control)
  response = http.get(`${BASE_URL}/api/subscriptions/validate`, {
    headers,
    tags: { name: 'subscription_validation' },
  });
  
  success = check(response, {
    'subscription validation status 200': (r) => r.status === 200,
    'subscription validation very fast': (r) => r.timings.duration < 100,
  });
  if (!success) billingErrors.add(1);
  subscriptionOps.add(1);

  // Test subscription filtering
  const statuses = ['active', 'trialing', 'past_due'];
  const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];
  
  response = http.get(`${BASE_URL}/api/subscriptions?status=${randomStatus}&limit=20`, {
    headers,
    tags: { name: 'subscription_filter' },
  });
  
  success = check(response, {
    'subscription filter status 200': (r) => r.status === 200,
    'subscription filter time': (r) => r.timings.duration < 250,
  });
  if (!success) billingErrors.add(1);
  subscriptionOps.add(1);
}

function testWebhookProcessing(headers) {
  // Stripe webhook events that need fast processing
  const webhookEvents = [
    {
      type: 'customer.subscription.created',
      data: {
        object: {
          id: `sub_test_${Math.random().toString(36).substr(2, 9)}`,
          customer: `cus_test_${Math.random().toString(36).substr(2, 9)}`,
          status: 'active',
          current_period_start: Math.floor(Date.now() / 1000),
          current_period_end: Math.floor(Date.now() / 1000) + 86400 * 30
        }
      }
    },
    {
      type: 'invoice.payment_succeeded',
      data: {
        object: {
          id: `in_test_${Math.random().toString(36).substr(2, 9)}`,
          customer: `cus_test_${Math.random().toString(36).substr(2, 9)}`,
          subscription: `sub_test_${Math.random().toString(36).substr(2, 9)}`,
          amount_paid: 2900,
          currency: 'usd',
          status: 'paid'
        }
      }
    },
    {
      type: 'invoice.payment_failed',
      data: {
        object: {
          id: `in_test_${Math.random().toString(36).substr(2, 9)}`,
          customer: `cus_test_${Math.random().toString(36).substr(2, 9)}`,
          subscription: `sub_test_${Math.random().toString(36).substr(2, 9)}`,
          amount_due: 2900,
          currency: 'usd',
          status: 'open'
        }
      }
    },
    {
      type: 'customer.subscription.updated',
      data: {
        object: {
          id: `sub_test_${Math.random().toString(36).substr(2, 9)}`,
          customer: `cus_test_${Math.random().toString(36).substr(2, 9)}`,
          status: 'active',
          cancel_at_period_end: false
        }
      }
    }
  ];

  const event = webhookEvents[Math.floor(Math.random() * webhookEvents.length)];
  const webhookPayload = JSON.stringify({
    id: `evt_test_${Math.random().toString(36).substr(2, 9)}`,
    type: event.type,
    data: event.data,
    created: Math.floor(Date.now() / 1000),
    livemode: false
  });

  const response = http.post(`${BASE_URL}/webhooks/stripe`, webhookPayload, {
    headers: {
      ...headers,
      'stripe-signature': 'test-signature-' + Math.random().toString(36).substr(2, 9)
    },
    tags: { name: `webhook_${event.type.replace('.', '_')}` },
  });
  
  const success = check(response, {
    'webhook processing status ok': (r) => r.status >= 200 && r.status < 400,
    'webhook processing fast': (r) => r.timings.duration < 150,
    'webhook response valid': (r) => {
      try {
        const data = JSON.parse(r.body);
        return data.success !== undefined;
      } catch {
        return false;
      }
    }
  });
  if (!success) billingErrors.add(1);
  webhookProcessed.add(1);
}

function testUsageTracking(headers) {
  // Track API usage (called very frequently)
  const usagePayload = JSON.stringify({
    metric: 'api_requests',
    quantity: Math.floor(Math.random() * 10) + 1,
    timestamp: new Date().toISOString(),
    metadata: {
      endpoint: '/api/analytics/query',
      response_time: Math.floor(Math.random() * 500) + 50
    }
  });

  let response = http.post(`${BASE_URL}/api/usage/track`, usagePayload, {
    headers,
    tags: { name: 'usage_tracking' },
  });
  
  let success = check(response, {
    'usage tracking status ok': (r) => r.status >= 200 && r.status < 300,
    'usage tracking very fast': (r) => r.timings.duration < 50,
  });
  if (!success) billingErrors.add(1);

  // Track different metrics
  const metrics = ['api_requests', 'data_export_mb', 'webhook_events', 'dashboard_views'];
  const randomMetric = metrics[Math.floor(Math.random() * metrics.length)];
  
  const metricPayload = JSON.stringify({
    metric: randomMetric,
    quantity: Math.floor(Math.random() * 100) + 1,
    timestamp: new Date().toISOString()
  });

  response = http.post(`${BASE_URL}/api/usage/track`, metricPayload, {
    headers,
    tags: { name: `usage_${randomMetric}` },
  });
  
  success = check(response, {
    [`${randomMetric} tracking status ok`]: (r) => r.status >= 200 && r.status < 300,
    [`${randomMetric} tracking fast`]: (r) => r.timings.duration < 100,
  });
  if (!success) billingErrors.add(1);

  // Check usage limits (called periodically)
  if (Math.random() < 0.3) {
    response = http.get(`${BASE_URL}/api/usage/limits`, {
      headers,
      tags: { name: 'usage_limits_check' },
    });
    
    success = check(response, {
      'usage limits check status 200': (r) => r.status === 200,
      'usage limits check time': (r) => r.timings.duration < 200,
    });
    if (!success) billingErrors.add(1);
  }
}

function testBillingQueries(headers) {
  // Revenue analytics queries
  response = http.get(`${BASE_URL}/api/analytics/revenue?period=30d`, {
    headers,
    tags: { name: 'revenue_analytics' },
  });
  
  let success = check(response, {
    'revenue analytics status 200': (r) => r.status === 200,
    'revenue analytics time': (r) => r.timings.duration < 400,
  });
  if (!success) billingErrors.add(1);

  // Subscription analytics
  response = http.get(`${BASE_URL}/api/analytics/subscriptions?period=90d`, {
    headers,
    tags: { name: 'subscription_analytics' },
  });
  
  success = check(response, {
    'subscription analytics status 200': (r) => r.status === 200,
    'subscription analytics time': (r) => r.timings.duration < 500,
  });
  if (!success) billingErrors.add(1);

  // Churn analysis
  response = http.get(`${BASE_URL}/api/analytics/churn?period=6m`, {
    headers,
    tags: { name: 'churn_analysis' },
  });
  
  success = check(response, {
    'churn analysis status 200': (r) => r.status === 200,
    'churn analysis time': (r) => r.timings.duration < 800,
  });
  if (!success) billingErrors.add(1);

  // Invoice queries
  response = http.get(`${BASE_URL}/api/invoices?status=paid&limit=25`, {
    headers,
    tags: { name: 'invoice_queries' },
  });
  
  success = check(response, {
    'invoice queries status 200': (r) => r.status === 200,
    'invoice queries time': (r) => r.timings.duration < 300,
  });
  if (!success) billingErrors.add(1);
}

function testPaymentOperations(headers) {
  // Payment method operations
  let response = http.get(`${BASE_URL}/api/payment-methods`, {
    headers,
    tags: { name: 'payment_methods_list' },
  });
  
  let success = check(response, {
    'payment methods list status 200': (r) => r.status === 200,
    'payment methods list time': (r) => r.timings.duration < 200,
  });
  if (!success) billingErrors.add(1);

  // Payment processing simulation
  const paymentIntentPayload = JSON.stringify({
    amount: 2900,
    currency: 'usd',
    customer_id: `cus_test_${Math.random().toString(36).substr(2, 9)}`,
    payment_method_id: `pm_test_${Math.random().toString(36).substr(2, 9)}`
  });

  response = http.post(`${BASE_URL}/api/payment-intents`, paymentIntentPayload, {
    headers,
    tags: { name: 'payment_intent_creation' },
  });
  
  success = check(response, {
    'payment intent creation status ok': (r) => r.status >= 200 && r.status < 300,
    'payment intent creation time': (r) => r.timings.duration < 400,
  });
  if (!success) billingErrors.add(1);

  // Failed payment handling
  if (Math.random() < 0.2) {
    response = http.get(`${BASE_URL}/api/payments/failed?limit=10`, {
      headers,
      tags: { name: 'failed_payments_query' },
    });
    
    success = check(response, {
      'failed payments query status 200': (r) => r.status === 200,
      'failed payments query time': (r) => r.timings.duration < 250,
    });
    if (!success) billingErrors.add(1);
  }

  // Dunning management
  if (Math.random() < 0.1) {
    response = http.get(`${BASE_URL}/api/dunning/attempts?status=pending`, {
      headers,
      tags: { name: 'dunning_attempts_query' },
    });
    
    success = check(response, {
      'dunning attempts query status 200': (r) => r.status === 200,
      'dunning attempts query time': (r) => r.timings.duration < 300,
    });
    if (!success) billingErrors.add(1);
  }
}

export function handleSummary(data) {
  const billingSummary = {
    test_type: 'billing_performance',
    service: 'billing',
    test_categories: [
      'subscription_operations',
      'webhook_processing', 
      'usage_tracking',
      'billing_queries',
      'payment_operations'
    ],
    performance_targets: {
      webhook_processing: '< 150ms',
      usage_tracking: '< 50ms',
      subscription_validation: '< 100ms',
      subscription_queries: '< 200ms',
      billing_analytics: '< 500ms'
    },
    billing_specific_metrics: {
      webhooks_processed: data.metrics.webhooks_processed?.count || 0,
      subscription_operations: data.metrics.subscription_operations?.count || 0,
      critical_path_performance: 'subscription_validation + usage_tracking'
    },
    results: data
  };

  return {
    'billing-performance-results.json': JSON.stringify(billingSummary, null, 2),
    stdout: `
Billing Service Performance Test Summary:
========================================
Test Duration: 12 minutes
Concurrent Users: 25
Critical Path: Subscription validation + Usage tracking

Performance Targets:
- Webhook processing: < 150ms (revenue critical)
- Usage tracking: < 50ms (called frequently)  
- Subscription validation: < 100ms (API gating)
- Subscription queries: < 200ms
- Billing analytics: < 500ms

Operations Processed:
- Webhooks processed: ${data.metrics.webhooks_processed?.count || 0}
- Subscription operations: ${data.metrics.subscription_operations?.count || 0}

Error Rate: ${data.metrics.billing_errors ? (data.metrics.billing_errors.rate * 100).toFixed(2) + '%' : 'N/A'}

Critical Performance Notes:
- Usage tracking must be very fast (< 50ms) as it's called on every API request
- Webhook processing is revenue-critical and must be reliable
- Subscription validation gates API access and must be fast

Check detailed results in billing-performance-results.json
    `
  };
}