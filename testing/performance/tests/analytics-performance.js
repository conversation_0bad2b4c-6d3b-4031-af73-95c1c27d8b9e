import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate, Counter } from 'k6/metrics';

// Custom metrics for analytics service
export const analyticsErrors = new Rate('analytics_errors');
export const queryComplexityCounter = new Counter('query_complexity_total');

// Analytics-focused performance test
export const options = {
  stages: [
    { duration: '2m', target: 30 },
    { duration: '8m', target: 30 },
    { duration: '2m', target: 0 },
  ],
  thresholds: {
    http_req_duration: ['p(95)<1000'],
    http_req_failed: ['rate<0.05'],
    analytics_errors: ['rate<0.05'],
  },
};

const BASE_URL = __ENV.ANALYTICS_URL || 'http://localhost:3001';
const testApiKey = __ENV.TEST_API_KEY || 'test-api-key';

export default function () {
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${testApiKey}`,
  };

  // Test different analytics workloads
  testRealTimeAnalytics(headers);
  testHistoricalQueries(headers);
  testComplexAggregations(headers);
  testDataExport(headers);
  testCustomMetrics(headers);
  
  sleep(1);
}

function testRealTimeAnalytics(headers) {
  // Real-time metrics - should be very fast
  let response = http.get(`${BASE_URL}/api/analytics/realtime`, {
    headers,
    tags: { name: 'realtime_metrics' },
  });
  
  let success = check(response, {
    'realtime metrics status 200': (r) => r.status === 200,
    'realtime metrics fast': (r) => r.timings.duration < 100,
    'realtime data present': (r) => {
      try {
        const data = JSON.parse(r.body);
        return data.active_users !== undefined;
      } catch {
        return false;
      }
    }
  });
  if (!success) analyticsErrors.add(1);

  // Current hour metrics
  response = http.get(`${BASE_URL}/api/analytics/current-hour`, {
    headers,
    tags: { name: 'current_hour_metrics' },
  });
  
  success = check(response, {
    'current hour status 200': (r) => r.status === 200,
    'current hour response fast': (r) => r.timings.duration < 200,
  });
  if (!success) analyticsErrors.add(1);

  // Live dashboard feed
  response = http.get(`${BASE_URL}/api/analytics/live-feed`, {
    headers,
    tags: { name: 'live_feed' },
  });
  
  success = check(response, {
    'live feed status 200': (r) => r.status === 200,
    'live feed very fast': (r) => r.timings.duration < 50,
  });
  if (!success) analyticsErrors.add(1);
}

function testHistoricalQueries(headers) {
  // Simple historical query
  const simpleQuery = JSON.stringify({
    metrics: ['revenue'],
    dimensions: ['date'],
    filters: {
      date_range: {
        start: '2024-06-01',
        end: '2024-06-07'
      }
    }
  });

  let response = http.post(`${BASE_URL}/api/analytics/query`, simpleQuery, {
    headers,
    tags: { name: 'simple_historical_query' },
  });
  
  let success = check(response, {
    'simple query status 200': (r) => r.status === 200,
    'simple query reasonable time': (r) => r.timings.duration < 300,
  });
  if (!success) analyticsErrors.add(1);
  queryComplexityCounter.add(1);

  // Medium complexity query
  const mediumQuery = JSON.stringify({
    metrics: ['revenue', 'orders', 'sessions'],
    dimensions: ['date', 'country'],
    filters: {
      date_range: {
        start: '2024-05-01',
        end: '2024-06-01'
      },
      countries: ['US', 'CA', 'UK']
    }
  });

  response = http.post(`${BASE_URL}/api/analytics/query`, mediumQuery, {
    headers,
    tags: { name: 'medium_historical_query' },
  });
  
  success = check(response, {
    'medium query status 200': (r) => r.status === 200,
    'medium query acceptable time': (r) => r.timings.duration < 800,
  });
  if (!success) analyticsErrors.add(1);
  queryComplexityCounter.add(2);

  // Complex historical query with multiple dimensions
  const complexQuery = JSON.stringify({
    metrics: ['revenue', 'orders', 'sessions', 'pageviews', 'conversion_rate'],
    dimensions: ['date', 'country', 'device', 'channel', 'product_category'],
    filters: {
      date_range: {
        start: '2024-01-01',
        end: '2024-06-01'
      }
    },
    granularity: 'day'
  });

  response = http.post(`${BASE_URL}/api/analytics/query`, complexQuery, {
    headers,
    tags: { name: 'complex_historical_query' },
    timeout: '10s',
  });
  
  success = check(response, {
    'complex query status 200': (r) => r.status === 200,
    'complex query completes': (r) => r.timings.duration < 5000,
  });
  if (!success) analyticsErrors.add(1);
  queryComplexityCounter.add(5);
}

function testComplexAggregations(headers) {
  // Revenue aggregation with grouping
  const revenueAgg = JSON.stringify({
    metrics: ['revenue'],
    dimensions: ['product_category'],
    filters: {
      date_range: {
        start: '2024-01-01',
        end: '2024-06-30'
      }
    },
    aggregations: ['sum', 'avg', 'count', 'min', 'max'],
    sort: [{ field: 'revenue', direction: 'desc' }],
    limit: 100
  });

  let response = http.post(`${BASE_URL}/api/analytics/aggregate`, revenueAgg, {
    headers,
    tags: { name: 'revenue_aggregation' },
  });
  
  let success = check(response, {
    'revenue aggregation status 200': (r) => r.status === 200,
    'revenue aggregation time': (r) => r.timings.duration < 2000,
  });
  if (!success) analyticsErrors.add(1);

  // Customer cohort analysis
  const cohortQuery = JSON.stringify({
    analysis_type: 'cohort',
    cohort_type: 'customer_acquisition',
    date_range: {
      start: '2024-01-01',
      end: '2024-06-30'
    },
    cohort_size: 'monthly'
  });

  response = http.post(`${BASE_URL}/api/analytics/cohort`, cohortQuery, {
    headers,
    tags: { name: 'cohort_analysis' },
    timeout: '15s',
  });
  
  success = check(response, {
    'cohort analysis status 200': (r) => r.status === 200,
    'cohort analysis completes': (r) => r.timings.duration < 8000,
  });
  if (!success) analyticsErrors.add(1);

  // Funnel analysis
  const funnelQuery = JSON.stringify({
    funnel_steps: [
      { event: 'page_view', filters: { page_type: 'product' } },
      { event: 'add_to_cart' },
      { event: 'checkout_started' },
      { event: 'purchase_completed' }
    ],
    date_range: {
      start: '2024-06-01',
      end: '2024-06-30'
    },
    conversion_window: '7d'
  });

  response = http.post(`${BASE_URL}/api/analytics/funnel`, funnelQuery, {
    headers,
    tags: { name: 'funnel_analysis' },
  });
  
  success = check(response, {
    'funnel analysis status 200': (r) => r.status === 200,
    'funnel analysis time': (r) => r.timings.duration < 3000,
  });
  if (!success) analyticsErrors.add(1);
}

function testDataExport(headers) {
  // Export raw data
  const exportQuery = JSON.stringify({
    export_type: 'raw_events',
    date_range: {
      start: '2024-06-29',
      end: '2024-06-30'
    },
    events: ['page_view', 'purchase'],
    format: 'json',
    limit: 10000
  });

  let response = http.post(`${BASE_URL}/api/analytics/export`, exportQuery, {
    headers,
    tags: { name: 'data_export' },
    timeout: '30s',
  });
  
  let success = check(response, {
    'export request accepted': (r) => r.status === 200 || r.status === 202,
    'export response time': (r) => r.timings.duration < 5000,
  });
  if (!success) analyticsErrors.add(1);

  // CSV export for reporting
  const csvExportQuery = JSON.stringify({
    metrics: ['revenue', 'orders'],
    dimensions: ['date', 'country'],
    date_range: {
      start: '2024-06-01',
      end: '2024-06-30'
    },
    format: 'csv'
  });

  response = http.post(`${BASE_URL}/api/analytics/export/csv`, csvExportQuery, {
    headers,
    tags: { name: 'csv_export' },
  });
  
  success = check(response, {
    'csv export status 200': (r) => r.status === 200,
    'csv export time': (r) => r.timings.duration < 3000,
    'csv content type': (r) => r.headers['Content-Type'] && r.headers['Content-Type'].includes('text/csv'),
  });
  if (!success) analyticsErrors.add(1);
}

function testCustomMetrics(headers) {
  // Custom metric calculation
  const customMetric = JSON.stringify({
    metric_definition: {
      name: 'average_order_value',
      formula: 'revenue / orders',
      dimensions: ['date', 'country']
    },
    date_range: {
      start: '2024-06-01',
      end: '2024-06-30'
    }
  });

  let response = http.post(`${BASE_URL}/api/analytics/custom-metric`, customMetric, {
    headers,
    tags: { name: 'custom_metric' },
  });
  
  let success = check(response, {
    'custom metric status 200': (r) => r.status === 200,
    'custom metric time': (r) => r.timings.duration < 1500,
  });
  if (!success) analyticsErrors.add(1);

  // Attribution analysis
  const attributionQuery = JSON.stringify({
    conversion_event: 'purchase',
    attribution_model: 'last_click',
    attribution_window: '30d',
    date_range: {
      start: '2024-06-01',
      end: '2024-06-30'
    },
    dimensions: ['channel', 'campaign']
  });

  response = http.post(`${BASE_URL}/api/analytics/attribution`, attributionQuery, {
    headers,
    tags: { name: 'attribution_analysis' },
  });
  
  success = check(response, {
    'attribution analysis status 200': (r) => r.status === 200,
    'attribution analysis time': (r) => r.timings.duration < 4000,
  });
  if (!success) analyticsErrors.add(1);

  // Predictive analytics
  const predictionQuery = JSON.stringify({
    prediction_type: 'revenue_forecast',
    historical_period: '12m',
    forecast_period: '3m',
    confidence_interval: 0.95
  });

  response = http.post(`${BASE_URL}/api/analytics/predict`, predictionQuery, {
    headers,
    tags: { name: 'predictive_analytics' },
    timeout: '20s',
  });
  
  success = check(response, {
    'predictive analytics accepted': (r) => r.status === 200 || r.status === 202,
    'prediction request time': (r) => r.timings.duration < 10000,
  });
  if (!success) analyticsErrors.add(1);
}

export function handleSummary(data) {
  const analyticsSummary = {
    test_type: 'analytics_performance',
    service: 'analytics',
    test_categories: [
      'real_time_analytics',
      'historical_queries',
      'complex_aggregations',
      'data_export',
      'custom_metrics'
    ],
    query_complexity_distribution: {
      simple: 'queries with 1-2 metrics, single dimension',
      medium: 'queries with 3-5 metrics, 2-3 dimensions',
      complex: 'queries with 5+ metrics, 4+ dimensions, large date ranges'
    },
    performance_targets: {
      realtime_queries: '< 100ms',
      simple_queries: '< 300ms',
      medium_queries: '< 800ms',
      complex_queries: '< 5000ms',
      exports: '< 30s'
    },
    results: data
  };

  return {
    'analytics-performance-results.json': JSON.stringify(analyticsSummary, null, 2),
    stdout: `
Analytics Service Performance Test Summary:
==========================================
Test Duration: 12 minutes
Concurrent Users: 30
Query Types Tested: 5 categories

Performance Targets:
- Real-time queries: < 100ms
- Simple queries: < 300ms  
- Medium queries: < 800ms
- Complex queries: < 5000ms
- Data exports: < 30s

Query Complexity Distribution:
- Simple: ${data.metrics.query_complexity_total ? Math.floor(data.metrics.query_complexity_total.count * 0.4) : 'N/A'} queries
- Medium: ${data.metrics.query_complexity_total ? Math.floor(data.metrics.query_complexity_total.count * 0.4) : 'N/A'} queries  
- Complex: ${data.metrics.query_complexity_total ? Math.floor(data.metrics.query_complexity_total.count * 0.2) : 'N/A'} queries

Error Rate: ${data.metrics.analytics_errors ? (data.metrics.analytics_errors.rate * 100).toFixed(2) + '%' : 'N/A'}

Check detailed results in analytics-performance-results.json
    `
  };
}