import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate } from 'k6/metrics';

// Custom metrics
export const errorRate = new Rate('errors');

// Stress test configuration - aggressive load
export const options = {
  stages: [
    { duration: '5m', target: 50 },  // Ramp up to 50 users
    { duration: '5m', target: 100 }, // Ramp up to 100 users
    { duration: '5m', target: 200 }, // Ramp up to 200 users
    { duration: '10m', target: 200 }, // Stay at 200 users
    { duration: '5m', target: 300 }, // Spike to 300 users
    { duration: '2m', target: 300 }, // Stay at spike
    { duration: '5m', target: 0 },  // Ramp down
  ],
  thresholds: {
    http_req_duration: ['p(95)<1000'], // 95% of requests must complete below 1s
    http_req_failed: ['rate<0.2'],     // Error rate must be below 20%
    errors: ['rate<0.2'],              // Custom error rate below 20%
  },
};

// Base URLs for services
const BASE_URLS = {
  analytics: __ENV.ANALYTICS_URL || 'http://localhost:3001',
  dashboard: __ENV.DASHBOARD_URL || 'http://localhost:3000',
  billing: __ENV.BILLING_URL || 'http://localhost:3003',
  integration: __ENV.INTEGRATION_URL || 'http://localhost:3002',
};

const testApiKey = __ENV.TEST_API_KEY || 'test-api-key';

export default function () {
  // Simulate heavy analytics workload
  stressAnalyticsService();
  
  // Simulate concurrent dashboard access
  stressDashboardService();
  
  // Simulate billing operations under load
  stressBillingService();
  
  // Simulate high-volume webhook processing
  stressIntegrationService();
  
  // Shorter sleep to increase load
  sleep(0.5);
}

function stressAnalyticsService() {
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${testApiKey}`,
  };

  // Heavy analytics query with large date range
  const heavyQueryPayload = JSON.stringify({
    metrics: ['revenue', 'orders', 'customers', 'sessions', 'pageviews'],
    dimensions: ['date', 'country', 'device', 'channel'],
    filters: {
      date_range: {
        start: '2023-01-01',
        end: '2024-12-31'
      }
    },
    granularity: 'day'
  });

  let response = http.post(`${BASE_URLS.analytics}/api/analytics/query`, heavyQueryPayload, {
    headers,
    tags: { name: 'analytics_heavy_query' },
    timeout: '10s',
  });
  
  check(response, {
    'heavy analytics query completes': (r) => r.status === 200 || r.status === 202,
    'heavy analytics query response time < 5s': (r) => r.timings.duration < 5000,
  }) || errorRate.add(1);

  // Multiple concurrent real-time requests
  for (let i = 0; i < 3; i++) {
    response = http.get(`${BASE_URLS.analytics}/api/analytics/realtime`, {
      headers,
      tags: { name: 'analytics_realtime_concurrent' },
    });
    
    check(response, {
      'concurrent realtime request succeeds': (r) => r.status === 200,
    }) || errorRate.add(1);
  }

  // Complex aggregation query
  const aggregationPayload = JSON.stringify({
    metrics: ['revenue'],
    dimensions: ['product_category'],
    filters: {
      date_range: {
        start: '2024-01-01',
        end: '2024-06-30'
      }
    },
    aggregations: ['sum', 'avg', 'count', 'min', 'max']
  });

  response = http.post(`${BASE_URLS.analytics}/api/analytics/aggregate`, aggregationPayload, {
    headers,
    tags: { name: 'analytics_aggregation' },
  });
  
  check(response, {
    'aggregation query completes': (r) => r.status === 200,
    'aggregation response time < 2s': (r) => r.timings.duration < 2000,
  }) || errorRate.add(1);
}

function stressDashboardService() {
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${testApiKey}`,
  };

  // Simulate loading multiple dashboard widgets simultaneously
  const widgets = ['revenue', 'orders', 'customers', 'traffic', 'conversion'];
  
  widgets.forEach(widget => {
    const response = http.get(`${BASE_URLS.dashboard}/api/widgets/${widget}`, {
      headers,
      tags: { name: `dashboard_widget_${widget}` },
    });
    
    check(response, {
      [`${widget} widget loads`]: (r) => r.status === 200,
      [`${widget} widget response time < 500ms`]: (r) => r.timings.duration < 500,
    }) || errorRate.add(1);
  });

  // Test dashboard with complex filters
  const filtersPayload = JSON.stringify({
    date_range: { start: '2024-01-01', end: '2024-06-30' },
    countries: ['US', 'CA', 'UK', 'DE', 'FR'],
    devices: ['desktop', 'mobile', 'tablet'],
    channels: ['organic', 'paid', 'direct', 'social', 'email']
  });

  const response = http.post(`${BASE_URLS.dashboard}/api/dashboard/filtered`, filtersPayload, {
    headers,
    tags: { name: 'dashboard_complex_filters' },
  });
  
  check(response, {
    'complex dashboard filter request succeeds': (r) => r.status === 200,
    'complex dashboard response time < 1s': (r) => r.timings.duration < 1000,
  }) || errorRate.add(1);
}

function stressBillingService() {
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${testApiKey}`,
  };

  // Stress test subscription queries with pagination
  for (let page = 0; page < 5; page++) {
    const response = http.get(`${BASE_URLS.billing}/api/subscriptions?limit=100&offset=${page * 100}`, {
      headers,
      tags: { name: 'billing_pagination' },
    });
    
    check(response, {
      'subscription pagination works': (r) => r.status === 200,
      'pagination response time < 300ms': (r) => r.timings.duration < 300,
    }) || errorRate.add(1);
  }

  // Test billing metrics under load
  const response = http.get(`${BASE_URLS.billing}/api/subscriptions/metrics`, {
    headers,
    tags: { name: 'billing_metrics_stress' },
  });
  
  check(response, {
    'billing metrics under stress': (r) => r.status === 200,
    'metrics calculation time < 1s': (r) => r.timings.duration < 1000,
  }) || errorRate.add(1);

  // Simulate webhook processing load
  const webhookPayload = JSON.stringify({
    id: `evt_${Math.random().toString(36).substr(2, 9)}`,
    type: 'invoice.payment_succeeded',
    data: {
      object: {
        id: `in_${Math.random().toString(36).substr(2, 9)}`,
        amount_paid: 2900,
        currency: 'usd',
        customer: `cus_${Math.random().toString(36).substr(2, 9)}`,
        subscription: `sub_${Math.random().toString(36).substr(2, 9)}`
      }
    }
  });

  const webhookResponse = http.post(`${BASE_URLS.billing}/webhooks/stripe`, webhookPayload, {
    headers: {
      ...headers,
      'stripe-signature': 'test-signature'
    },
    tags: { name: 'billing_webhook_stress' },
  });
  
  check(webhookResponse, {
    'webhook processes under stress': (r) => r.status >= 200 && r.status < 400,
    'webhook processing time < 200ms': (r) => r.timings.duration < 200,
  }) || errorRate.add(1);
}

function stressIntegrationService() {
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${testApiKey}`,
  };

  // Simulate high-volume data ingestion
  const platforms = ['shopify', 'woocommerce', 'magento'];
  const eventTypes = ['order.created', 'order.updated', 'customer.created', 'product.updated'];
  
  platforms.forEach(platform => {
    eventTypes.forEach(eventType => {
      const webhookData = generateWebhookData(platform, eventType);
      
      const response = http.post(`${BASE_URLS.integration}/webhooks/${platform}`, JSON.stringify(webhookData), {
        headers: {
          ...headers,
          'X-Platform-Event': eventType,
          'X-Platform-Signature': 'test-signature'
        },
        tags: { name: `integration_${platform}_${eventType}` },
      });
      
      check(response, {
        [`${platform} ${eventType} webhook succeeds`]: (r) => r.status >= 200 && r.status < 400,
        [`${platform} ${eventType} response time < 100ms`]: (r) => r.timings.duration < 100,
      }) || errorRate.add(1);
    });
  });

  // Test data synchronization endpoint under load
  const syncPayload = JSON.stringify({
    platform: 'shopify',
    data_types: ['orders', 'customers', 'products'],
    date_range: {
      start: '2024-06-01T00:00:00Z',
      end: '2024-06-30T23:59:59Z'
    }
  });

  const syncResponse = http.post(`${BASE_URLS.integration}/api/sync`, syncPayload, {
    headers,
    tags: { name: 'integration_sync_stress' },
    timeout: '30s',
  });
  
  check(syncResponse, {
    'data sync initiates under stress': (r) => r.status === 200 || r.status === 202,
    'sync response time < 1s': (r) => r.timings.duration < 1000,
  }) || errorRate.add(1);
}

function generateWebhookData(platform, eventType) {
  const baseData = {
    id: Math.random().toString(36).substr(2, 9),
    timestamp: new Date().toISOString(),
  };

  switch (eventType) {
    case 'order.created':
    case 'order.updated':
      return {
        ...baseData,
        event: eventType,
        data: {
          id: `order_${baseData.id}`,
          total_price: (Math.random() * 500 + 10).toFixed(2),
          currency: 'USD',
          customer_id: `customer_${Math.random().toString(36).substr(2, 9)}`,
          line_items: [
            {
              product_id: `product_${Math.random().toString(36).substr(2, 9)}`,
              quantity: Math.floor(Math.random() * 3) + 1,
              price: (Math.random() * 100 + 5).toFixed(2)
            }
          ]
        }
      };
    
    case 'customer.created':
      return {
        ...baseData,
        event: eventType,
        data: {
          id: `customer_${baseData.id}`,
          email: `test${baseData.id}@example.com`,
          first_name: 'Test',
          last_name: 'Customer',
          created_at: new Date().toISOString()
        }
      };
    
    case 'product.updated':
      return {
        ...baseData,
        event: eventType,
        data: {
          id: `product_${baseData.id}`,
          title: `Test Product ${baseData.id}`,
          price: (Math.random() * 200 + 5).toFixed(2),
          inventory_quantity: Math.floor(Math.random() * 100)
        }
      };
    
    default:
      return baseData;
  }
}

export function handleSummary(data) {
  return {
    'stress-test-results.json': JSON.stringify(data, null, 2),
  };
}