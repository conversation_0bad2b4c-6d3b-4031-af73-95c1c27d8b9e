import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate, Trend } from 'k6/metrics';

// Custom metrics for endurance testing
export const errorRate = new Rate('errors');
export const memoryLeakIndicator = new Trend('response_time_trend');

// Endurance test configuration - long duration, moderate load
export const options = {
  stages: [
    { duration: '5m', target: 50 },   // Ramp up to moderate load
    { duration: '2h', target: 50 },   // Maintain load for 2 hours
    { duration: '5m', target: 0 },    // Ramp down
  ],
  thresholds: {
    http_req_duration: ['p(95)<800'],  // Response times shouldn't degrade over time
    http_req_failed: ['rate<0.05'],    // Very low error rate for endurance
    errors: ['rate<0.05'],             // Custom error rate
    response_time_trend: ['p(95)<1000'], // Monitor for memory leaks
  },
};

// Base URLs for services
const BASE_URLS = {
  analytics: __ENV.ANALYTICS_URL || 'http://localhost:3001',
  dashboard: __ENV.DASHBOARD_URL || 'http://localhost:3000',
  billing: __ENV.BILLING_URL || 'http://localhost:3003',
  integration: __ENV.INTEGRATION_URL || 'http://localhost:3002',
};

const testApiKey = __ENV.TEST_API_KEY || 'test-api-key';

// Track test progression for memory leak detection
let iterationCount = 0;

export default function () {
  iterationCount++;
  
  // Rotate through different test scenarios to simulate real usage patterns
  const scenario = iterationCount % 4;
  
  switch (scenario) {
    case 0:
      testAnalyticsWorkflow();
      break;
    case 1:
      testDashboardWorkflow();
      break;
    case 2:
      testBillingWorkflow();
      break;
    case 3:
      testIntegrationWorkflow();
      break;
  }
  
  // Track response time trends for memory leak detection
  if (iterationCount % 100 === 0) {
    checkSystemHealth();
  }
  
  // Realistic user pause between requests
  sleep(2 + Math.random() * 3);
}

function testAnalyticsWorkflow() {
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${testApiKey}`,
  };

  // Simulate typical analytics workflow
  
  // 1. Check real-time metrics (frequent)
  let response = http.get(`${BASE_URLS.analytics}/api/analytics/realtime`, {
    headers,
    tags: { name: 'analytics_realtime_endurance' },
  });
  
  let success = check(response, {
    'realtime analytics works': (r) => r.status === 200,
    'realtime response time stable': (r) => r.timings.duration < 300,
  });
  if (!success) errorRate.add(1);
  memoryLeakIndicator.add(response.timings.duration);

  sleep(1);

  // 2. Load dashboard data (common)
  response = http.get(`${BASE_URLS.analytics}/api/analytics/overview`, {
    headers,
    tags: { name: 'analytics_overview_endurance' },
  });
  
  success = check(response, {
    'analytics overview works': (r) => r.status === 200,
    'overview response time stable': (r) => r.timings.duration < 500,
  });
  if (!success) errorRate.add(1);
  memoryLeakIndicator.add(response.timings.duration);

  sleep(1);

  // 3. Run custom query (periodic)
  if (iterationCount % 5 === 0) {
    const queryPayload = JSON.stringify({
      metrics: ['revenue', 'orders'],
      dimensions: ['date'],
      filters: {
        date_range: {
          start: '2024-06-01',
          end: '2024-06-30'
        }
      }
    });

    response = http.post(`${BASE_URLS.analytics}/api/analytics/query`, queryPayload, {
      headers,
      tags: { name: 'analytics_query_endurance' },
    });
    
    success = check(response, {
      'custom query works': (r) => r.status === 200,
      'query response time stable': (r) => r.timings.duration < 1000,
    });
    if (!success) errorRate.add(1);
    memoryLeakIndicator.add(response.timings.duration);
  }
}

function testDashboardWorkflow() {
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${testApiKey}`,
  };

  // Simulate typical dashboard usage pattern
  
  // 1. Load main dashboard
  let response = http.get(`${BASE_URLS.dashboard}/api/dashboard`, {
    headers,
    tags: { name: 'dashboard_main_endurance' },
  });
  
  let success = check(response, {
    'dashboard loads': (r) => r.status === 200,
    'dashboard response time stable': (r) => r.timings.duration < 600,
  });
  if (!success) errorRate.add(1);
  memoryLeakIndicator.add(response.timings.duration);

  sleep(2);

  // 2. Load individual widgets (simulate user interaction)
  const widgets = ['revenue', 'orders', 'customers', 'traffic'];
  const selectedWidget = widgets[Math.floor(Math.random() * widgets.length)];
  
  response = http.get(`${BASE_URLS.dashboard}/api/widgets/${selectedWidget}`, {
    headers,
    tags: { name: `dashboard_widget_${selectedWidget}_endurance` },
  });
  
  success = check(response, {
    [`${selectedWidget} widget works`]: (r) => r.status === 200,
    [`${selectedWidget} widget response time stable`]: (r) => r.timings.duration < 400,
  });
  if (!success) errorRate.add(1);
  memoryLeakIndicator.add(response.timings.duration);

  sleep(1);

  // 3. Apply filters (occasional)
  if (iterationCount % 8 === 0) {
    const filtersPayload = JSON.stringify({
      date_range: { start: '2024-06-01', end: '2024-06-30' },
      country: 'US'
    });

    response = http.post(`${BASE_URLS.dashboard}/api/dashboard/filtered`, filtersPayload, {
      headers,
      tags: { name: 'dashboard_filters_endurance' },
    });
    
    success = check(response, {
      'dashboard filters work': (r) => r.status === 200,
      'filters response time stable': (r) => r.timings.duration < 800,
    });
    if (!success) errorRate.add(1);
    memoryLeakIndicator.add(response.timings.duration);
  }
}

function testBillingWorkflow() {
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${testApiKey}`,
  };

  // Simulate billing operations over time
  
  // 1. Check subscription status (frequent for API validation)
  let response = http.get(`${BASE_URLS.billing}/api/subscriptions/validate`, {
    headers,
    tags: { name: 'billing_validation_endurance' },
  });
  
  let success = check(response, {
    'subscription validation works': (r) => r.status === 200,
    'validation response time stable': (r) => r.timings.duration < 200,
  });
  if (!success) errorRate.add(1);
  memoryLeakIndicator.add(response.timings.duration);

  sleep(1);

  // 2. Track usage (continuous)
  const usagePayload = JSON.stringify({
    metric: 'api_requests',
    quantity: 1,
    timestamp: new Date().toISOString()
  });

  response = http.post(`${BASE_URLS.billing}/api/usage/track`, usagePayload, {
    headers,
    tags: { name: 'billing_usage_endurance' },
  });
  
  success = check(response, {
    'usage tracking works': (r) => r.status >= 200 && r.status < 300,
    'usage tracking response time stable': (r) => r.timings.duration < 150,
  });
  if (!success) errorRate.add(1);
  memoryLeakIndicator.add(response.timings.duration);

  sleep(1);

  // 3. List subscriptions (periodic admin task)
  if (iterationCount % 10 === 0) {
    response = http.get(`${BASE_URLS.billing}/api/subscriptions?limit=50`, {
      headers,
      tags: { name: 'billing_list_endurance' },
    });
    
    success = check(response, {
      'subscription list works': (r) => r.status === 200,
      'list response time stable': (r) => r.timings.duration < 400,
    });
    if (!success) errorRate.add(1);
    memoryLeakIndicator.add(response.timings.duration);
  }

  // 4. Process webhook (simulated external events)
  if (iterationCount % 15 === 0) {
    const webhookPayload = JSON.stringify({
      id: `evt_endurance_${Math.random().toString(36).substr(2, 9)}`,
      type: 'invoice.payment_succeeded',
      data: {
        object: {
          id: `in_endurance_${Math.random().toString(36).substr(2, 9)}`,
          amount_paid: 2900,
          currency: 'usd'
        }
      }
    });

    response = http.post(`${BASE_URLS.billing}/webhooks/stripe`, webhookPayload, {
      headers: {
        ...headers,
        'stripe-signature': 'endurance-test-signature'
      },
      tags: { name: 'billing_webhook_endurance' },
    });
    
    success = check(response, {
      'billing webhook works': (r) => r.status >= 200 && r.status < 400,
      'webhook response time stable': (r) => r.timings.duration < 250,
    });
    if (!success) errorRate.add(1);
    memoryLeakIndicator.add(response.timings.duration);
  }
}

function testIntegrationWorkflow() {
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${testApiKey}`,
  };

  // Simulate continuous data integration
  
  // 1. Process incoming webhooks (continuous)
  const platforms = ['shopify', 'woocommerce'];
  const platform = platforms[Math.floor(Math.random() * platforms.length)];
  
  const webhookData = {
    event: 'order.created',
    data: {
      id: `endurance_order_${Math.random().toString(36).substr(2, 9)}`,
      total: (Math.random() * 500 + 10).toFixed(2),
      currency: 'USD',
      customer_id: `endurance_customer_${Math.random().toString(36).substr(2, 9)}`,
      created_at: new Date().toISOString()
    }
  };

  let response = http.post(`${BASE_URLS.integration}/webhooks/${platform}`, JSON.stringify(webhookData), {
    headers: {
      ...headers,
      'X-Platform-Event': 'order.created',
      'X-Platform-Signature': 'endurance-test-signature'
    },
    tags: { name: `integration_webhook_${platform}_endurance` },
  });
  
  let success = check(response, {
    [`${platform} webhook works`]: (r) => r.status >= 200 && r.status < 400,
    [`${platform} webhook response time stable`]: (r) => r.timings.duration < 200,
  });
  if (!success) errorRate.add(1);
  memoryLeakIndicator.add(response.timings.duration);

  sleep(1);

  // 2. Check integration status (monitoring)
  response = http.get(`${BASE_URLS.integration}/api/integrations/status`, {
    headers,
    tags: { name: 'integration_status_endurance' },
  });
  
  success = check(response, {
    'integration status works': (r) => r.status === 200,
    'status response time stable': (r) => r.timings.duration < 150,
  });
  if (!success) errorRate.add(1);
  memoryLeakIndicator.add(response.timings.duration);

  sleep(1);

  // 3. Data sync operations (periodic)
  if (iterationCount % 20 === 0) {
    const syncPayload = JSON.stringify({
      platform: platform,
      data_types: ['orders'],
      date_range: {
        start: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // Last 24 hours
        end: new Date().toISOString()
      }
    });

    response = http.post(`${BASE_URLS.integration}/api/sync`, syncPayload, {
      headers,
      tags: { name: 'integration_sync_endurance' },
    });
    
    success = check(response, {
      'data sync works': (r) => r.status === 200 || r.status === 202,
      'sync response time stable': (r) => r.timings.duration < 1000,
    });
    if (!success) errorRate.add(1);
    memoryLeakIndicator.add(response.timings.duration);
  }
}

function checkSystemHealth() {
  // Monitor system health indicators during endurance test
  console.log(`Endurance Test Health Check - Iteration ${iterationCount}`);
  
  const services = ['analytics', 'dashboard', 'billing', 'integration'];
  
  services.forEach(service => {
    const response = http.get(`${BASE_URLS[service]}/health`, {
      tags: { name: `health_check_${service}_endurance` },
    });
    
    const healthy = check(response, {
      [`${service} health check passes`]: (r) => r.status === 200,
      [`${service} health response fast`]: (r) => r.timings.duration < 100,
    });
    
    if (!healthy) {
      console.error(`WARNING: ${service} health check failed at iteration ${iterationCount}`);
      errorRate.add(1);
    }
  });
}

export function handleSummary(data) {
  // Generate comprehensive endurance test report
  const enduranceSummary = {
    test_type: 'endurance_test',
    duration: '2h 10m',
    concurrent_users: 50,
    total_iterations: iterationCount,
    memory_leak_detection: {
      response_time_trend: data.metrics.response_time_trend || {},
      degradation_detected: false // Would be calculated based on trend analysis
    },
    stability_metrics: {
      error_rate: data.metrics.errors || {},
      response_time_p95: data.metrics.http_req_duration || {},
      availability: data.metrics.http_req_failed || {}
    },
    results: data
  };

  return {
    'endurance-test-results.json': JSON.stringify(enduranceSummary, null, 2),
    stdout: `
Endurance Test Summary:
======================
Duration: 2h 10m
Concurrent Users: 50
Total Iterations: ${iterationCount}

Stability Indicators:
- Error Rate: ${data.metrics.errors ? (data.metrics.errors.rate * 100).toFixed(2) + '%' : 'N/A'}
- 95th Percentile Response Time: ${data.metrics.http_req_duration ? data.metrics.http_req_duration.p95.toFixed(2) + 'ms' : 'N/A'}
- Request Failure Rate: ${data.metrics.http_req_failed ? (data.metrics.http_req_failed.rate * 100).toFixed(2) + '%' : 'N/A'}

Memory Leak Detection:
- Response Time Trend: ${data.metrics.response_time_trend ? 'MONITORED' : 'NOT AVAILABLE'}
- System Health Checks: Performed every 100 iterations

Check detailed results in endurance-test-results.json
    `
  };
}