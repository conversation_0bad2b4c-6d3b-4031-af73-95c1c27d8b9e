import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate } from 'k6/metrics';

// Custom metrics
export const errorRate = new Rate('errors');

// Spike test configuration - sudden traffic spikes
export const options = {
  stages: [
    { duration: '1m', target: 20 },   // Normal load
    { duration: '30s', target: 500 }, // Sudden spike
    { duration: '1m', target: 500 },  // Maintain spike
    { duration: '30s', target: 20 },  // Drop back to normal
    { duration: '1m', target: 20 },   // Maintain normal
    { duration: '30s', target: 1000 }, // Extreme spike
    { duration: '1m', target: 1000 }, // Maintain extreme spike
    { duration: '1m', target: 0 },    // Scale down
  ],
  thresholds: {
    http_req_duration: ['p(95)<2000'], // Allow higher response times during spikes
    http_req_failed: ['rate<0.3'],     // Allow higher error rate during extreme spikes
    errors: ['rate<0.3'],              // Custom error rate threshold
  },
};

// Base URLs for services
const BASE_URLS = {
  analytics: __ENV.ANALYTICS_URL || 'http://localhost:3001',
  dashboard: __ENV.DASHBOARD_URL || 'http://localhost:3000',
  billing: __ENV.BILLING_URL || 'http://localhost:3003',
  integration: __ENV.INTEGRATION_URL || 'http://localhost:3002',
};

const testApiKey = __ENV.TEST_API_KEY || 'test-api-key';

export default function () {
  // Test critical endpoints during traffic spikes
  testCriticalEndpoints();
  
  // Minimal sleep to maximize load during spikes
  sleep(0.1);
}

function testCriticalEndpoints() {
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${testApiKey}`,
  };

  // Test the most critical endpoints that must remain functional during spikes
  
  // 1. Health checks - must always work
  testHealthChecks();
  
  // 2. Real-time analytics - business critical
  testRealTimeAnalytics(headers);
  
  // 3. Dashboard overview - most accessed endpoint
  testDashboardOverview(headers);
  
  // 4. Webhook processing - revenue critical
  testWebhookProcessing(headers);
  
  // 5. Authentication/billing checks - security critical
  testBillingOperations(headers);
}

function testHealthChecks() {
  const services = ['analytics', 'dashboard', 'billing', 'integration'];
  
  services.forEach(service => {
    const response = http.get(`${BASE_URLS[service]}/health`, {
      tags: { name: `health_${service}` },
      timeout: '5s',
    });
    
    check(response, {
      [`${service} health check responds`]: (r) => r.status === 200,
      [`${service} health check fast`]: (r) => r.timings.duration < 100,
    }) || errorRate.add(1);
  });
}

function testRealTimeAnalytics(headers) {
  // Real-time metrics - must be fast even during spikes
  const response = http.get(`${BASE_URLS.analytics}/api/analytics/realtime`, {
    headers,
    tags: { name: 'realtime_analytics_spike' },
    timeout: '2s',
  });
  
  check(response, {
    'realtime analytics during spike': (r) => r.status === 200,
    'realtime analytics response time < 500ms': (r) => r.timings.duration < 500,
  }) || errorRate.add(1);

  // Quick overview metrics
  const overviewResponse = http.get(`${BASE_URLS.analytics}/api/analytics/overview?quick=true`, {
    headers,
    tags: { name: 'quick_overview_spike' },
    timeout: '3s',
  });
  
  check(overviewResponse, {
    'quick overview during spike': (r) => r.status === 200,
    'quick overview response time < 1s': (r) => r.timings.duration < 1000,
  }) || errorRate.add(1);
}

function testDashboardOverview(headers) {
  // Main dashboard - most accessed during traffic spikes
  const response = http.get(`${BASE_URLS.dashboard}/api/dashboard`, {
    headers,
    tags: { name: 'dashboard_main_spike' },
    timeout: '5s',
  });
  
  check(response, {
    'dashboard loads during spike': (r) => r.status === 200,
    'dashboard response time < 2s': (r) => r.timings.duration < 2000,
  }) || errorRate.add(1);

  // Essential widgets only
  const essentialWidgets = ['revenue', 'orders'];
  essentialWidgets.forEach(widget => {
    const widgetResponse = http.get(`${BASE_URLS.dashboard}/api/widgets/${widget}?cache=true`, {
      headers,
      tags: { name: `essential_widget_${widget}_spike` },
      timeout: '3s',
    });
    
    check(widgetResponse, {
      [`${widget} widget during spike`]: (r) => r.status === 200,
      [`${widget} widget response time < 1s`]: (r) => r.timings.duration < 1000,
    }) || errorRate.add(1);
  });
}

function testWebhookProcessing(headers) {
  // Critical webhook processing - revenue depends on this
  const criticalWebhookPayload = JSON.stringify({
    event: 'order.created',
    data: {
      id: `spike_order_${Math.random().toString(36).substr(2, 9)}`,
      total: 99.99,
      currency: 'USD',
      customer_id: `spike_customer_${Math.random().toString(36).substr(2, 9)}`,
      created_at: new Date().toISOString()
    }
  });

  const platforms = ['shopify', 'woocommerce'];
  platforms.forEach(platform => {
    const response = http.post(`${BASE_URLS.integration}/webhooks/${platform}`, criticalWebhookPayload, {
      headers: {
        ...headers,
        'X-Platform-Event': 'order.created',
        'X-Platform-Signature': 'spike-test-signature'
      },
      tags: { name: `webhook_${platform}_spike` },
      timeout: '3s',
    });
    
    check(response, {
      [`${platform} webhook processes during spike`]: (r) => r.status >= 200 && r.status < 400,
      [`${platform} webhook response time < 300ms`]: (r) => r.timings.duration < 300,
    }) || errorRate.add(1);
  });

  // Test webhook queue status
  const queueStatusResponse = http.get(`${BASE_URLS.integration}/api/webhooks/queue/status`, {
    headers,
    tags: { name: 'webhook_queue_status_spike' },
    timeout: '2s',
  });
  
  check(queueStatusResponse, {
    'webhook queue status during spike': (r) => r.status === 200,
    'queue status response time < 200ms': (r) => r.timings.duration < 200,
  }) || errorRate.add(1);
}

function testBillingOperations(headers) {
  // Critical billing operations that must work during spikes
  
  // Subscription validation (for API access control)
  const subscriptionResponse = http.get(`${BASE_URLS.billing}/api/subscriptions/validate`, {
    headers,
    tags: { name: 'subscription_validation_spike' },
    timeout: '2s',
  });
  
  check(subscriptionResponse, {
    'subscription validation during spike': (r) => r.status === 200,
    'validation response time < 200ms': (r) => r.timings.duration < 200,
  }) || errorRate.add(1);

  // Usage tracking (critical for billing)
  const usagePayload = JSON.stringify({
    metric: 'api_requests',
    quantity: 1,
    timestamp: new Date().toISOString()
  });

  const usageResponse = http.post(`${BASE_URLS.billing}/api/usage/track`, usagePayload, {
    headers,
    tags: { name: 'usage_tracking_spike' },
    timeout: '1s',
  });
  
  check(usageResponse, {
    'usage tracking during spike': (r) => r.status >= 200 && r.status < 400,
    'usage tracking response time < 100ms': (r) => r.timings.duration < 100,
  }) || errorRate.add(1);

  // Billing webhook processing (Stripe events)
  const billingWebhookPayload = JSON.stringify({
    id: `evt_spike_${Math.random().toString(36).substr(2, 9)}`,
    type: 'customer.subscription.updated',
    data: {
      object: {
        id: `sub_spike_${Math.random().toString(36).substr(2, 9)}`,
        status: 'active',
        current_period_end: Math.floor(Date.now() / 1000) + 86400 * 30
      }
    }
  });

  const billingWebhookResponse = http.post(`${BASE_URLS.billing}/webhooks/stripe`, billingWebhookPayload, {
    headers: {
      ...headers,
      'stripe-signature': 'spike-test-signature'
    },
    tags: { name: 'billing_webhook_spike' },
    timeout: '2s',
  });
  
  check(billingWebhookResponse, {
    'billing webhook processes during spike': (r) => r.status >= 200 && r.status < 400,
    'billing webhook response time < 200ms': (r) => r.timings.duration < 200,
  }) || errorRate.add(1);
}

export function handleSummary(data) {
  // Generate detailed spike test report
  const spikeSummary = {
    test_type: 'spike_test',
    peak_users: 1000,
    spike_duration: '1m',
    critical_endpoints_tested: [
      'health_checks',
      'realtime_analytics',
      'dashboard_overview',
      'webhook_processing',
      'billing_operations'
    ],
    results: data
  };

  return {
    'spike-test-results.json': JSON.stringify(spikeSummary, null, 2),
    stdout: `
Spike Test Summary:
==================
Peak Users: 1000
Critical Endpoints Tested: 5 categories
Max Response Time Threshold: 2000ms
Max Error Rate Threshold: 30%

Key Metrics:
- Health Checks: ${data.metrics.http_req_duration ? 'TESTED' : 'FAILED'}
- Real-time Analytics: ${data.metrics.http_req_duration ? 'TESTED' : 'FAILED'}
- Dashboard Overview: ${data.metrics.http_req_duration ? 'TESTED' : 'FAILED'}
- Webhook Processing: ${data.metrics.http_req_duration ? 'TESTED' : 'FAILED'}
- Billing Operations: ${data.metrics.http_req_duration ? 'TESTED' : 'FAILED'}

Check detailed results in spike-test-results.json
    `
  };
}