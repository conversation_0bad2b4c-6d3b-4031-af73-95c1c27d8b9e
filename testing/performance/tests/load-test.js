import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate } from 'k6/metrics';

// Custom metrics
export const errorRate = new Rate('errors');

// Test configuration
export const options = {
  stages: [
    { duration: '2m', target: 10 }, // Ramp up to 10 users
    { duration: '5m', target: 10 }, // Stay at 10 users
    { duration: '2m', target: 20 }, // Ramp up to 20 users
    { duration: '5m', target: 20 }, // Stay at 20 users
    { duration: '2m', target: 0 },  // Ramp down to 0 users
  ],
  thresholds: {
    http_req_duration: ['p(95)<500'], // 95% of requests must complete below 500ms
    http_req_failed: ['rate<0.1'],    // Error rate must be below 10%
    errors: ['rate<0.1'],             // Custom error rate below 10%
  },
};

// Base URLs for services
const BASE_URLS = {
  analytics: __ENV.ANALYTICS_URL || 'http://localhost:3001',
  dashboard: __ENV.DASHBOARD_URL || 'http://localhost:3000',
  billing: __ENV.BILLING_URL || 'http://localhost:3003',
  integration: __ENV.INTEGRATION_URL || 'http://localhost:3002',
};

// Test data
const testTenantId = __ENV.TEST_TENANT_ID || 'test-tenant-123';
const testApiKey = __ENV.TEST_API_KEY || 'test-api-key';

export default function () {
  // Test Analytics Service
  testAnalyticsEndpoints();
  
  // Test Dashboard Service
  testDashboardEndpoints();
  
  // Test Billing Service
  testBillingEndpoints();
  
  // Test Integration Service
  testIntegrationEndpoints();
  
  sleep(1);
}

function testAnalyticsEndpoints() {
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${testApiKey}`,
  };

  // Test analytics data retrieval
  let response = http.get(`${BASE_URLS.analytics}/api/analytics/overview`, {
    headers,
    tags: { name: 'analytics_overview' },
  });
  
  check(response, {
    'analytics overview status is 200': (r) => r.status === 200,
    'analytics overview response time < 200ms': (r) => r.timings.duration < 200,
  }) || errorRate.add(1);

  // Test real-time metrics
  response = http.get(`${BASE_URLS.analytics}/api/analytics/realtime`, {
    headers,
    tags: { name: 'analytics_realtime' },
  });
  
  check(response, {
    'analytics realtime status is 200': (r) => r.status === 200,
    'analytics realtime response time < 100ms': (r) => r.timings.duration < 100,
  }) || errorRate.add(1);

  // Test custom query
  const queryPayload = JSON.stringify({
    metrics: ['revenue', 'orders'],
    dimensions: ['date'],
    filters: {
      date_range: {
        start: '2024-01-01',
        end: '2024-01-31'
      }
    }
  });

  response = http.post(`${BASE_URLS.analytics}/api/analytics/query`, queryPayload, {
    headers,
    tags: { name: 'analytics_query' },
  });
  
  check(response, {
    'analytics query status is 200': (r) => r.status === 200,
    'analytics query response time < 500ms': (r) => r.timings.duration < 500,
  }) || errorRate.add(1);
}

function testDashboardEndpoints() {
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${testApiKey}`,
  };

  // Test dashboard data
  let response = http.get(`${BASE_URLS.dashboard}/api/dashboard`, {
    headers,
    tags: { name: 'dashboard_main' },
  });
  
  check(response, {
    'dashboard status is 200': (r) => r.status === 200,
    'dashboard response time < 300ms': (r) => r.timings.duration < 300,
  }) || errorRate.add(1);

  // Test widgets
  response = http.get(`${BASE_URLS.dashboard}/api/widgets/revenue`, {
    headers,
    tags: { name: 'dashboard_widget' },
  });
  
  check(response, {
    'dashboard widget status is 200': (r) => r.status === 200,
    'dashboard widget response time < 150ms': (r) => r.timings.duration < 150,
  }) || errorRate.add(1);
}

function testBillingEndpoints() {
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${testApiKey}`,
  };

  // Test subscription list
  let response = http.get(`${BASE_URLS.billing}/api/subscriptions?limit=10`, {
    headers,
    tags: { name: 'billing_subscriptions' },
  });
  
  check(response, {
    'billing subscriptions status is 200': (r) => r.status === 200,
    'billing subscriptions response time < 200ms': (r) => r.timings.duration < 200,
  }) || errorRate.add(1);

  // Test subscription metrics
  response = http.get(`${BASE_URLS.billing}/api/subscriptions/metrics`, {
    headers,
    tags: { name: 'billing_metrics' },
  });
  
  check(response, {
    'billing metrics status is 200': (r) => r.status === 200,
    'billing metrics response time < 300ms': (r) => r.timings.duration < 300,
  }) || errorRate.add(1);

  // Test health check
  response = http.get(`${BASE_URLS.billing}/health`, {
    tags: { name: 'billing_health' },
  });
  
  check(response, {
    'billing health status is 200': (r) => r.status === 200,
    'billing health response time < 50ms': (r) => r.timings.duration < 50,
  }) || errorRate.add(1);
}

function testIntegrationEndpoints() {
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${testApiKey}`,
  };

  // Test integrations list
  let response = http.get(`${BASE_URLS.integration}/api/integrations`, {
    headers,
    tags: { name: 'integration_list' },
  });
  
  check(response, {
    'integration list status is 200': (r) => r.status === 200,
    'integration list response time < 150ms': (r) => r.timings.duration < 150,
  }) || errorRate.add(1);

  // Test webhook endpoint (simulated)
  const webhookPayload = JSON.stringify({
    event: 'order.created',
    data: {
      id: 'order_' + Math.random().toString(36).substr(2, 9),
      total: 99.99,
      currency: 'USD',
      customer_id: 'customer_123'
    }
  });

  response = http.post(`${BASE_URLS.integration}/webhooks/shopify`, webhookPayload, {
    headers: {
      ...headers,
      'X-Shopify-Topic': 'orders/create',
      'X-Shopify-Hmac-Sha256': 'test-signature'
    },
    tags: { name: 'integration_webhook' },
  });
  
  check(response, {
    'integration webhook accepts request': (r) => r.status >= 200 && r.status < 400,
    'integration webhook response time < 100ms': (r) => r.timings.duration < 100,
  }) || errorRate.add(1);
}

export function handleSummary(data) {
  return {
    'load-test-results.json': JSON.stringify(data, null, 2),
  };
}