const { Client } = require('pg');
const redis = require('redis');
const { performance } = require('perf_hooks');

// Database performance testing configuration
const config = {
  postgres: {
    host: process.env.POSTGRES_HOST || 'localhost',
    port: process.env.POSTGRES_PORT || 5432,
    database: process.env.POSTGRES_DB || 'ecommerce_analytics',
    user: process.env.POSTGRES_USER || 'postgres',
    password: process.env.POSTGRES_PASSWORD || 'password',
    max: 20,
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 2000,
  },
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: process.env.REDIS_PORT || 6379,
    password: process.env.REDIS_PASSWORD || undefined,
  }
};

class DatabasePerformanceTest {
  constructor() {
    this.results = {
      postgres: {
        connection_time: [],
        simple_queries: [],
        complex_queries: [],
        insert_operations: [],
        update_operations: [],
        concurrent_operations: [],
        errors: 0
      },
      redis: {
        connection_time: [],
        get_operations: [],
        set_operations: [],
        complex_operations: [],
        concurrent_operations: [],
        errors: 0
      }
    };
  }

  async runTests() {
    console.log('Starting Database Performance Tests...');
    console.log('=====================================');

    try {
      await this.testPostgreSQL();
      await this.testRedis();
      this.generateReport();
    } catch (error) {
      console.error('Test execution failed:', error);
      process.exit(1);
    }
  }

  async testPostgreSQL() {
    console.log('\n📊 Testing PostgreSQL Performance...');
    
    const client = new Client(config.postgres);
    
    try {
      // Test connection time
      const connectStart = performance.now();
      await client.connect();
      const connectTime = performance.now() - connectStart;
      this.results.postgres.connection_time.push(connectTime);
      console.log(`✓ PostgreSQL connection time: ${connectTime.toFixed(2)}ms`);

      // Test simple queries
      await this.testSimpleQueries(client);
      
      // Test complex queries
      await this.testComplexQueries(client);
      
      // Test insert operations
      await this.testInsertOperations(client);
      
      // Test update operations
      await this.testUpdateOperations(client);
      
      // Test concurrent operations
      await this.testConcurrentPostgreSQLOps(client);

    } catch (error) {
      console.error('PostgreSQL test error:', error);
      this.results.postgres.errors++;
    } finally {
      await client.end();
    }
  }

  async testSimpleQueries(client) {
    console.log('  → Testing simple queries...');
    
    const simpleQueries = [
      'SELECT COUNT(*) FROM analytics_events WHERE created_at > NOW() - INTERVAL \'1 hour\'',
      'SELECT * FROM subscription_plans WHERE is_active = true',
      'SELECT COUNT(*) FROM subscriptions WHERE status = \'active\'',
      'SELECT * FROM tenants WHERE created_at > NOW() - INTERVAL \'1 day\' LIMIT 10',
      'SELECT COUNT(*) FROM invoices WHERE status = \'paid\' AND created_at > NOW() - INTERVAL \'30 days\''
    ];

    for (const query of simpleQueries) {
      for (let i = 0; i < 5; i++) {
        try {
          const start = performance.now();
          await client.query(query);
          const duration = performance.now() - start;
          this.results.postgres.simple_queries.push(duration);
        } catch (error) {
          console.error(`Simple query failed: ${query}`, error.message);
          this.results.postgres.errors++;
        }
      }
    }

    const avgSimple = this.results.postgres.simple_queries.reduce((a, b) => a + b, 0) / this.results.postgres.simple_queries.length;
    console.log(`    Average simple query time: ${avgSimple.toFixed(2)}ms`);
  }

  async testComplexQueries(client) {
    console.log('  → Testing complex queries...');
    
    const complexQueries = [
      `
        SELECT 
          DATE_TRUNC('day', ae.created_at) as date,
          COUNT(*) as events,
          COUNT(DISTINCT ae.user_id) as unique_users,
          SUM(CASE WHEN ae.event_type = 'purchase' THEN ae.revenue ELSE 0 END) as revenue
        FROM analytics_events ae 
        WHERE ae.created_at > NOW() - INTERVAL '30 days'
        GROUP BY DATE_TRUNC('day', ae.created_at)
        ORDER BY date DESC
      `,
      `
        SELECT 
          s.plan_id,
          COUNT(*) as subscription_count,
          SUM(sp.monthly_price) as mrr,
          AVG(sp.monthly_price) as avg_price
        FROM subscriptions s
        JOIN subscription_plans sp ON s.plan_id = sp.id
        WHERE s.status IN ('active', 'trialing')
        GROUP BY s.plan_id, sp.monthly_price
      `,
      `
        SELECT 
          t.id,
          t.name,
          s.status,
          s.current_period_end,
          COUNT(ae.id) as event_count
        FROM tenants t
        LEFT JOIN subscriptions s ON t.id = s.tenant_id
        LEFT JOIN analytics_events ae ON t.id = ae.tenant_id 
          AND ae.created_at > NOW() - INTERVAL '7 days'
        GROUP BY t.id, t.name, s.status, s.current_period_end
        HAVING COUNT(ae.id) > 100
        ORDER BY event_count DESC
        LIMIT 50
      `,
      `
        WITH revenue_by_month AS (
          SELECT 
            DATE_TRUNC('month', i.created_at) as month,
            SUM(i.amount_paid) as monthly_revenue
          FROM invoices i
          WHERE i.status = 'paid' 
            AND i.created_at > NOW() - INTERVAL '12 months'
          GROUP BY DATE_TRUNC('month', i.created_at)
        )
        SELECT 
          month,
          monthly_revenue,
          LAG(monthly_revenue) OVER (ORDER BY month) as prev_month,
          CASE 
            WHEN LAG(monthly_revenue) OVER (ORDER BY month) > 0 
            THEN ((monthly_revenue - LAG(monthly_revenue) OVER (ORDER BY month)) / LAG(monthly_revenue) OVER (ORDER BY month) * 100)
            ELSE 0 
          END as growth_rate
        FROM revenue_by_month
        ORDER BY month DESC
      `
    ];

    for (const query of complexQueries) {
      try {
        const start = performance.now();
        await client.query(query);
        const duration = performance.now() - start;
        this.results.postgres.complex_queries.push(duration);
      } catch (error) {
        console.error('Complex query failed:', error.message);
        this.results.postgres.errors++;
      }
    }

    if (this.results.postgres.complex_queries.length > 0) {
      const avgComplex = this.results.postgres.complex_queries.reduce((a, b) => a + b, 0) / this.results.postgres.complex_queries.length;
      console.log(`    Average complex query time: ${avgComplex.toFixed(2)}ms`);
    }
  }

  async testInsertOperations(client) {
    console.log('  → Testing insert operations...');
    
    // Test single inserts
    for (let i = 0; i < 10; i++) {
      try {
        const start = performance.now();
        await client.query(`
          INSERT INTO analytics_events (
            id, tenant_id, user_id, session_id, event_type, 
            event_data, created_at
          ) VALUES ($1, $2, $3, $4, $5, $6, NOW())
        `, [
          `test_${Date.now()}_${i}`,
          '550e8400-e29b-41d4-a716-446655440000',
          `user_${i}`,
          `session_${i}`,
          'page_view',
          JSON.stringify({ page: '/test', duration: 1000 + i })
        ]);
        const duration = performance.now() - start;
        this.results.postgres.insert_operations.push(duration);
      } catch (error) {
        console.error('Insert operation failed:', error.message);
        this.results.postgres.errors++;
      }
    }

    // Test batch insert
    try {
      const batchStart = performance.now();
      const values = [];
      const params = [];
      for (let i = 0; i < 100; i++) {
        const offset = params.length;
        values.push(`($${offset + 1}, $${offset + 2}, $${offset + 3}, $${offset + 4}, $${offset + 5}, $${offset + 6}, NOW())`);
        params.push(
          `batch_${Date.now()}_${i}`,
          '550e8400-e29b-41d4-a716-446655440000',
          `batch_user_${i}`,
          `batch_session_${i}`,
          'batch_event',
          JSON.stringify({ batch: true, index: i })
        );
      }
      
      await client.query(`
        INSERT INTO analytics_events (
          id, tenant_id, user_id, session_id, event_type, event_data, created_at
        ) VALUES ${values.join(', ')}
      `, params);
      
      const batchDuration = performance.now() - batchStart;
      this.results.postgres.insert_operations.push(batchDuration);
      console.log(`    Batch insert (100 records): ${batchDuration.toFixed(2)}ms`);
    } catch (error) {
      console.error('Batch insert failed:', error.message);
      this.results.postgres.errors++;
    }

    if (this.results.postgres.insert_operations.length > 0) {
      const avgInsert = this.results.postgres.insert_operations.reduce((a, b) => a + b, 0) / this.results.postgres.insert_operations.length;
      console.log(`    Average insert time: ${avgInsert.toFixed(2)}ms`);
    }
  }

  async testUpdateOperations(client) {
    console.log('  → Testing update operations...');
    
    const updateQueries = [
      `UPDATE analytics_events SET event_data = event_data || '{"updated": true}' 
       WHERE id LIKE 'test_%' AND created_at > NOW() - INTERVAL '1 minute'`,
      `UPDATE subscriptions SET metadata = metadata || '{"performance_test": true}' 
       WHERE updated_at > NOW() - INTERVAL '1 day' LIMIT 10`,
      `UPDATE tenants SET updated_at = NOW() 
       WHERE created_at > NOW() - INTERVAL '1 hour' LIMIT 5`
    ];

    for (const query of updateQueries) {
      try {
        const start = performance.now();
        const result = await client.query(query);
        const duration = performance.now() - start;
        this.results.postgres.update_operations.push(duration);
        console.log(`    Updated ${result.rowCount} rows in ${duration.toFixed(2)}ms`);
      } catch (error) {
        console.error('Update operation failed:', error.message);
        this.results.postgres.errors++;
      }
    }
  }

  async testConcurrentPostgreSQLOps(client) {
    console.log('  → Testing concurrent operations...');
    
    const concurrentOperations = [];
    
    // Create multiple concurrent queries
    for (let i = 0; i < 10; i++) {
      concurrentOperations.push(
        this.executeTimedQuery(client, `
          SELECT COUNT(*) FROM analytics_events 
          WHERE created_at > NOW() - INTERVAL '${i + 1} hours'
        `)
      );
    }

    try {
      const start = performance.now();
      const results = await Promise.all(concurrentOperations);
      const totalDuration = performance.now() - start;
      
      results.forEach(duration => {
        this.results.postgres.concurrent_operations.push(duration);
      });
      
      console.log(`    Concurrent operations (10 queries): ${totalDuration.toFixed(2)}ms total`);
    } catch (error) {
      console.error('Concurrent operations failed:', error.message);
      this.results.postgres.errors++;
    }
  }

  async executeTimedQuery(client, query) {
    const start = performance.now();
    await client.query(query);
    return performance.now() - start;
  }

  async testRedis() {
    console.log('\n🔴 Testing Redis Performance...');
    
    const client = redis.createClient(config.redis);
    
    try {
      const connectStart = performance.now();
      await client.connect();
      const connectTime = performance.now() - connectStart;
      this.results.redis.connection_time.push(connectTime);
      console.log(`✓ Redis connection time: ${connectTime.toFixed(2)}ms`);

      await this.testRedisGetSet(client);
      await this.testRedisComplexOps(client);
      await this.testConcurrentRedisOps(client);

    } catch (error) {
      console.error('Redis test error:', error);
      this.results.redis.errors++;
    } finally {
      await client.quit();
    }
  }

  async testRedisGetSet(client) {
    console.log('  → Testing Redis GET/SET operations...');
    
    // Test SET operations
    for (let i = 0; i < 50; i++) {
      try {
        const start = performance.now();
        await client.set(`test:key:${i}`, JSON.stringify({
          id: i,
          timestamp: Date.now(),
          data: `test data ${i}`,
          metadata: { test: true }
        }));
        const duration = performance.now() - start;
        this.results.redis.set_operations.push(duration);
      } catch (error) {
        console.error('Redis SET failed:', error.message);
        this.results.redis.errors++;
      }
    }

    // Test GET operations
    for (let i = 0; i < 50; i++) {
      try {
        const start = performance.now();
        await client.get(`test:key:${i}`);
        const duration = performance.now() - start;
        this.results.redis.get_operations.push(duration);
      } catch (error) {
        console.error('Redis GET failed:', error.message);
        this.results.redis.errors++;
      }
    }

    const avgSet = this.results.redis.set_operations.reduce((a, b) => a + b, 0) / this.results.redis.set_operations.length;
    const avgGet = this.results.redis.get_operations.reduce((a, b) => a + b, 0) / this.results.redis.get_operations.length;
    
    console.log(`    Average SET time: ${avgSet.toFixed(2)}ms`);
    console.log(`    Average GET time: ${avgGet.toFixed(2)}ms`);
  }

  async testRedisComplexOps(client) {
    console.log('  → Testing Redis complex operations...');
    
    const complexOps = [
      // Hash operations
      async () => {
        const start = performance.now();
        await client.hSet('analytics:session:123', {
          'user_id': 'user123',
          'started_at': Date.now().toString(),
          'page_views': '5',
          'events': JSON.stringify(['login', 'page_view', 'click'])
        });
        return performance.now() - start;
      },
      
      // List operations
      async () => {
        const start = performance.now();
        await client.lPush('events:queue', JSON.stringify({
          type: 'page_view',
          user_id: 'user123',
          timestamp: Date.now()
        }));
        return performance.now() - start;
      },
      
      // Set operations
      async () => {
        const start = performance.now();
        await client.sAdd('active:users', 'user123', 'user456', 'user789');
        return performance.now() - start;
      },
      
      // Sorted set operations
      async () => {
        const start = performance.now();
        await client.zAdd('leaderboard:revenue', {
          score: 1000,
          value: 'tenant123'
        });
        return performance.now() - start;
      },
      
      // Pipeline operations
      async () => {
        const start = performance.now();
        const pipeline = client.multi();
        pipeline.set('cache:user:123', JSON.stringify({ name: 'Test User' }));
        pipeline.expire('cache:user:123', 3600);
        pipeline.incr('stats:api_calls');
        await pipeline.exec();
        return performance.now() - start;
      }
    ];

    for (const op of complexOps) {
      try {
        const duration = await op();
        this.results.redis.complex_operations.push(duration);
      } catch (error) {
        console.error('Redis complex operation failed:', error.message);
        this.results.redis.errors++;
      }
    }

    if (this.results.redis.complex_operations.length > 0) {
      const avgComplex = this.results.redis.complex_operations.reduce((a, b) => a + b, 0) / this.results.redis.complex_operations.length;
      console.log(`    Average complex operation time: ${avgComplex.toFixed(2)}ms`);
    }
  }

  async testConcurrentRedisOps(client) {
    console.log('  → Testing concurrent Redis operations...');
    
    const concurrentOps = [];
    
    for (let i = 0; i < 20; i++) {
      concurrentOps.push(
        this.executeTimedRedisOp(client, async () => {
          await client.set(`concurrent:${i}`, `value_${i}`);
          await client.get(`concurrent:${i}`);
        })
      );
    }

    try {
      const start = performance.now();
      const results = await Promise.all(concurrentOps);
      const totalDuration = performance.now() - start;
      
      results.forEach(duration => {
        this.results.redis.concurrent_operations.push(duration);
      });
      
      console.log(`    Concurrent operations (20 ops): ${totalDuration.toFixed(2)}ms total`);
    } catch (error) {
      console.error('Concurrent Redis operations failed:', error.message);
      this.results.redis.errors++;
    }
  }

  async executeTimedRedisOp(client, operation) {
    const start = performance.now();
    await operation();
    return performance.now() - start;
  }

  generateReport() {
    console.log('\n📋 Database Performance Test Report');
    console.log('====================================');
    
    // PostgreSQL Report
    console.log('\n📊 PostgreSQL Performance:');
    console.log(`   Connection Time: ${this.results.postgres.connection_time[0]?.toFixed(2) || 'N/A'}ms`);
    
    if (this.results.postgres.simple_queries.length > 0) {
      const stats = this.calculateStats(this.results.postgres.simple_queries);
      console.log(`   Simple Queries - Avg: ${stats.avg.toFixed(2)}ms, P95: ${stats.p95.toFixed(2)}ms, Max: ${stats.max.toFixed(2)}ms`);
    }
    
    if (this.results.postgres.complex_queries.length > 0) {
      const stats = this.calculateStats(this.results.postgres.complex_queries);
      console.log(`   Complex Queries - Avg: ${stats.avg.toFixed(2)}ms, P95: ${stats.p95.toFixed(2)}ms, Max: ${stats.max.toFixed(2)}ms`);
    }
    
    if (this.results.postgres.insert_operations.length > 0) {
      const stats = this.calculateStats(this.results.postgres.insert_operations);
      console.log(`   Insert Operations - Avg: ${stats.avg.toFixed(2)}ms, P95: ${stats.p95.toFixed(2)}ms`);
    }
    
    console.log(`   Errors: ${this.results.postgres.errors}`);

    // Redis Report
    console.log('\n🔴 Redis Performance:');
    console.log(`   Connection Time: ${this.results.redis.connection_time[0]?.toFixed(2) || 'N/A'}ms`);
    
    if (this.results.redis.get_operations.length > 0) {
      const getStats = this.calculateStats(this.results.redis.get_operations);
      console.log(`   GET Operations - Avg: ${getStats.avg.toFixed(2)}ms, P95: ${getStats.p95.toFixed(2)}ms`);
    }
    
    if (this.results.redis.set_operations.length > 0) {
      const setStats = this.calculateStats(this.results.redis.set_operations);
      console.log(`   SET Operations - Avg: ${setStats.avg.toFixed(2)}ms, P95: ${setStats.p95.toFixed(2)}ms`);
    }
    
    if (this.results.redis.complex_operations.length > 0) {
      const complexStats = this.calculateStats(this.results.redis.complex_operations);
      console.log(`   Complex Operations - Avg: ${complexStats.avg.toFixed(2)}ms, P95: ${complexStats.p95.toFixed(2)}ms`);
    }
    
    console.log(`   Errors: ${this.results.redis.errors}`);

    // Performance Recommendations
    console.log('\n💡 Performance Recommendations:');
    
    if (this.results.postgres.simple_queries.length > 0) {
      const avgSimple = this.results.postgres.simple_queries.reduce((a, b) => a + b, 0) / this.results.postgres.simple_queries.length;
      if (avgSimple > 100) {
        console.log('   ⚠️  Simple PostgreSQL queries are slow - consider adding indexes');
      }
    }
    
    if (this.results.postgres.complex_queries.length > 0) {
      const avgComplex = this.results.postgres.complex_queries.reduce((a, b) => a + b, 0) / this.results.postgres.complex_queries.length;
      if (avgComplex > 1000) {
        console.log('   ⚠️  Complex PostgreSQL queries are slow - consider query optimization');
      }
    }
    
    if (this.results.redis.get_operations.length > 0) {
      const avgGet = this.results.redis.get_operations.reduce((a, b) => a + b, 0) / this.results.redis.get_operations.length;
      if (avgGet > 10) {
        console.log('   ⚠️  Redis GET operations are slow - check network latency');
      }
    }
    
    if (this.results.postgres.errors > 0 || this.results.redis.errors > 0) {
      console.log('   ❌ Database errors detected - check logs and connectivity');
    }

    // Save detailed results
    const detailedResults = {
      timestamp: new Date().toISOString(),
      postgres: this.results.postgres,
      redis: this.results.redis,
      summary: {
        postgres_avg_simple_query: this.results.postgres.simple_queries.length > 0 ? 
          this.results.postgres.simple_queries.reduce((a, b) => a + b, 0) / this.results.postgres.simple_queries.length : 0,
        redis_avg_get: this.results.redis.get_operations.length > 0 ?
          this.results.redis.get_operations.reduce((a, b) => a + b, 0) / this.results.redis.get_operations.length : 0,
        total_errors: this.results.postgres.errors + this.results.redis.errors
      }
    };

    require('fs').writeFileSync('database-performance-results.json', JSON.stringify(detailedResults, null, 2));
    console.log('\n📄 Detailed results saved to database-performance-results.json');
  }

  calculateStats(values) {
    const sorted = values.slice().sort((a, b) => a - b);
    const avg = values.reduce((a, b) => a + b, 0) / values.length;
    const p95Index = Math.floor(sorted.length * 0.95);
    const p95 = sorted[p95Index] || sorted[sorted.length - 1];
    const max = Math.max(...values);
    const min = Math.min(...values);
    
    return { avg, p95, max, min };
  }
}

// Run the tests
const test = new DatabasePerformanceTest();
test.runTests().catch(console.error);