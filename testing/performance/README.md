# Performance Testing Suite

This directory contains comprehensive performance testing tools for the E-commerce Analytics SaaS platform. The testing suite is designed to validate system performance under various load conditions and identify bottlenecks before production deployment.

## Overview

The performance testing framework includes:

- **Load Testing**: Normal expected traffic patterns
- **Stress Testing**: High traffic to find breaking points  
- **Spike Testing**: Sudden traffic spikes simulation
- **Endurance Testing**: Long-duration stability testing
- **Service-Specific Testing**: Targeted testing for each microservice
- **Database Performance Testing**: Direct database and cache testing

## Test Types

### 1. Load Testing (`tests/load-test.js`)
- **Purpose**: Validate performance under expected traffic
- **Duration**: 16 minutes
- **Peak Users**: 20 concurrent users
- **Targets**: 95% of requests < 500ms, < 10% error rate
- **Coverage**: All services (Analytics, Dashboard, Billing, Integration)

### 2. Stress Testing (`tests/stress-test.js`)
- **Purpose**: Find system breaking points
- **Duration**: 32 minutes  
- **Peak Users**: 300 concurrent users
- **Targets**: 95% of requests < 1000ms, < 20% error rate
- **Focus**: Heavy workloads and resource exhaustion

### 3. Spike Testing (`tests/spike-test.js`)
- **Purpose**: Test resilience during traffic spikes
- **Duration**: 8 minutes
- **Peak Users**: 1000 concurrent users (extreme spike)
- **Targets**: Critical endpoints remain functional
- **Focus**: Health checks, real-time data, webhook processing

### 4. Endurance Testing (`tests/endurance-test.js`)
- **Purpose**: Long-term stability and memory leak detection
- **Duration**: 2 hours 10 minutes
- **Users**: 50 concurrent users (sustained)
- **Targets**: No performance degradation over time
- **Focus**: Memory leaks, connection pools, resource cleanup

### 5. Service-Specific Tests

#### Analytics Performance (`tests/analytics-performance.js`)
- Real-time metrics (< 100ms)
- Simple queries (< 300ms)
- Complex queries (< 5000ms)
- Data exports (< 30s)
- Custom metrics and predictions

#### Billing Performance (`tests/billing-performance.js`)
- Webhook processing (< 150ms) - revenue critical
- Usage tracking (< 50ms) - called frequently
- Subscription validation (< 100ms) - API gating
- Payment operations
- Dunning management

#### Database Performance (`tests/database-performance.js`)
- PostgreSQL query performance
- Redis cache performance
- Concurrent operations
- Insert/update operations
- Connection pooling

## Getting Started

### Prerequisites

1. **Install Dependencies**:
   ```bash
   cd testing/performance
   npm install
   ```

2. **Set Environment Variables**:
   ```bash
   export ANALYTICS_URL=http://localhost:3001
   export DASHBOARD_URL=http://localhost:3000
   export BILLING_URL=http://localhost:3003
   export INTEGRATION_URL=http://localhost:3002
   export TEST_API_KEY=your-test-api-key
   export TEST_TENANT_ID=test-tenant-uuid
   ```

3. **Database Configuration** (for database tests):
   ```bash
   export POSTGRES_HOST=localhost
   export POSTGRES_PORT=5432
   export POSTGRES_DB=ecommerce_analytics
   export POSTGRES_USER=postgres
   export POSTGRES_PASSWORD=password
   export REDIS_HOST=localhost
   export REDIS_PORT=6379
   ```

### Running Tests

#### Individual Test Types

```bash
# Load testing
npm run test:load

# Stress testing  
npm run test:stress

# Spike testing
npm run test:spike

# Endurance testing (2+ hours)
npm run test:endurance

# Service-specific tests
npm run test:analytics
npm run test:billing

# Database performance
npm run test:database
```

#### Comprehensive Testing

```bash
# Run all API tests (excludes endurance)
npm run test:all

# Run all tests including endurance
npm test
```

#### Performance Profiling

```bash
# CPU profiling
npm run profile

# Flame graph generation
npm run flame

# Memory profiling
npm run bubbleprof
```

## Test Results

### Output Files

Each test generates detailed results:

- `load-test-results.json` - Load test metrics
- `stress-test-results.json` - Stress test analysis
- `spike-test-results.json` - Spike test summary
- `endurance-test-results.json` - Endurance test report
- `analytics-performance-results.json` - Analytics service metrics
- `billing-performance-results.json` - Billing service metrics
- `database-performance-results.json` - Database performance data

### Key Metrics Tracked

1. **Response Times**:
   - Average response time
   - 95th percentile (P95)
   - Maximum response time
   - Response time trends

2. **Error Rates**:
   - HTTP error rate
   - Custom error rate
   - Error distribution by endpoint

3. **Throughput**:
   - Requests per second
   - Operations per second
   - Data transfer rates

4. **Resource Utilization**:
   - Memory usage patterns
   - Connection pool status
   - Cache hit rates

5. **Business Metrics**:
   - Webhook processing success
   - Payment operation success
   - Data sync completion rates

## Performance Targets

### Response Time Targets

| Service | Endpoint Type | Target |
|---------|---------------|---------|
| Analytics | Real-time metrics | < 100ms |
| Analytics | Simple queries | < 300ms |
| Analytics | Complex queries | < 5000ms |
| Dashboard | Widget loading | < 500ms |
| Dashboard | Main dashboard | < 600ms |
| Billing | Usage tracking | < 50ms |
| Billing | Webhook processing | < 150ms |
| Billing | Subscription validation | < 100ms |
| Integration | Webhook ingestion | < 100ms |
| Integration | Data sync | < 1000ms |

### Availability Targets

- **Load Testing**: < 1% error rate
- **Stress Testing**: < 20% error rate
- **Spike Testing**: Critical endpoints remain functional
- **Endurance Testing**: < 5% error rate over 2+ hours

## Monitoring and Alerting

### Critical Path Monitoring

The tests monitor critical business paths:

1. **Revenue Path**: Webhook → Processing → Analytics
2. **User Experience**: Dashboard → Widgets → Real-time data
3. **Billing Path**: Usage tracking → Validation → Billing
4. **Data Path**: Integration → Processing → Analytics

### Performance Regression Detection

- Automated comparison with baseline metrics
- Response time trend analysis
- Error rate threshold monitoring
- Memory leak detection in endurance tests

## Troubleshooting

### Common Issues

1. **Connection Timeouts**:
   - Check service availability
   - Verify network connectivity
   - Review connection pool settings

2. **High Error Rates**:
   - Check service logs
   - Verify API key validity
   - Review rate limiting settings

3. **Slow Database Queries**:
   - Run `EXPLAIN ANALYZE` on slow queries
   - Check index usage
   - Review connection pool status

4. **Memory Issues**:
   - Monitor heap usage during tests
   - Check for connection leaks
   - Review garbage collection patterns

### Performance Optimization

1. **Database Optimization**:
   - Add missing indexes
   - Optimize complex queries
   - Implement query caching
   - Use connection pooling

2. **Caching Strategy**:
   - Implement Redis caching for frequent queries
   - Use CDN for static assets
   - Cache computed metrics

3. **Service Optimization**:
   - Implement response compression
   - Use async processing for heavy operations
   - Optimize serialization/deserialization

## Continuous Integration

### Automated Testing

The performance tests can be integrated into CI/CD pipelines:

```bash
# Quick performance check (10 minutes)
npm run test:quick

# Full performance validation (30 minutes)
npm run test:comprehensive
```

### Performance Budgets

Set performance budgets in CI:

- P95 response time degradation < 10%
- Error rate increase < 2%
- Throughput decrease < 5%

### Reporting

Generate performance reports for stakeholders:

```bash
# Generate consolidated report
npm run report

# Generate HTML dashboard
npm run report:html
```

## Best Practices

1. **Test Environment**:
   - Use production-like data volumes
   - Match production infrastructure
   - Include realistic network latency

2. **Test Data**:
   - Use representative test data
   - Include edge cases
   - Clean up test data after runs

3. **Baseline Establishment**:
   - Establish performance baselines
   - Regular baseline updates
   - Track performance trends

4. **Load Simulation**:
   - Model realistic user behavior
   - Include think time between requests
   - Vary load patterns

## Support

For questions or issues with performance testing:

1. Check service logs in `/logs` directory
2. Review performance metrics in monitoring dashboard
3. Consult the troubleshooting guide above
4. Contact the platform team for assistance

---

**Note**: Performance testing should be conducted in a dedicated testing environment to avoid impacting production systems. Always coordinate with the operations team before running comprehensive performance tests.