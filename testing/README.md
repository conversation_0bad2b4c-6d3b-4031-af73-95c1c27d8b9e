# E-commerce Analytics SaaS - Testing Framework

## Overview

This directory contains a comprehensive testing framework for the e-commerce analytics platform, providing unit tests, integration tests, end-to-end tests, and performance testing capabilities.

## Test Architecture

```mermaid
graph TB
    subgraph "Test Layers"
        UNIT[Unit Tests]
        INTEGRATION[Integration Tests]
        E2E[End-to-End Tests]
        PERFORMANCE[Performance Tests]
    end
    
    subgraph "Test Infrastructure"
        JEST[Jest Framework]
        SETUP[Test Setup]
        FIXTURES[Test Fixtures]
        MOCKS[Mock Services]
    end
    
    subgraph "Services Under Test"
        DASHBOARD[Dashboard API]
        ANALYTICS[Analytics Service]
        INTEGRATION_SVC[Integration Service]
        ERROR_TRACKING[Error Tracking]
        ADMIN[Admin Service]
    end
    
    subgraph "Test Database"
        TEST_PG[(Test PostgreSQL)]
        TEST_REDIS[(Test Redis)]
    end
    
    UNIT --> JEST
    INTEGRATION --> JEST
    E2E --> JEST
    PERFORMANCE --> JEST
    
    JEST --> SETUP
    JEST --> FIXTURES
    JEST --> MOCKS
    
    INTEGRATION --> DASHBOARD
    INTEGRATION --> <PERSON><PERSON><PERSON><PERSON>CS
    INTEGRATION --> INTEGRATION_SVC
    INTEGRATION --> ERROR_TRACKING
    INTEGRATION --> ADMIN
    
    E2E --> DASHBOARD
    E2E --> ANALYTICS
    E2E --> INTEGRATION_SVC
    
    SETUP --> TEST_PG
    SETUP --> TEST_REDIS
```

## Test Types

### 1. Unit Tests
- **Location**: `services/*/src/**/*.test.js`
- **Purpose**: Test individual functions and modules in isolation
- **Coverage**: 80%+ required for all services
- **Execution Time**: < 30 seconds total

### 2. Integration Tests
- **Location**: `testing/integration/**/*.test.js`
- **Purpose**: Test API endpoints and service interactions
- **Coverage**: All major API endpoints and workflows
- **Execution Time**: < 2 minutes total

### 3. End-to-End Tests
- **Location**: `testing/e2e/**/*.test.js`
- **Purpose**: Test complete user journeys and workflows
- **Coverage**: Critical user paths and business scenarios
- **Execution Time**: < 5 minutes total

### 4. Performance Tests
- **Location**: `testing/performance/**/*.test.js`
- **Purpose**: Validate system performance under load
- **Coverage**: API response times, throughput, and resource usage
- **Execution Time**: < 10 minutes total

## Quick Start

### Prerequisites

- Node.js 18+
- Docker and Docker Compose
- PostgreSQL 14+
- Redis 6+

### Installation

```bash
# Install testing dependencies
cd testing
npm install

# Or install from root
npm run test:install
```

### Running Tests

```bash
# Run all tests
./scripts/run-tests.sh

# Run specific test types
./scripts/run-tests.sh --type unit
./scripts/run-tests.sh --type integration
./scripts/run-tests.sh --type e2e
./scripts/run-tests.sh --type performance

# Run with coverage
./scripts/run-tests.sh --coverage

# Run in watch mode
./scripts/run-tests.sh --watch --type unit

# Run in CI mode
./scripts/run-tests.sh --ci --coverage
```

### Direct Jest Commands

```bash
cd testing

# Run all tests
npm test

# Run specific test suites
npm run test:unit
npm run test:integration
npm run test:e2e
npm run test:performance

# Run with coverage
npm run test:coverage

# Run in watch mode
npm run test:watch
```

## Test Configuration

### Environment Variables

Create `.env.test` file in project root:

```env
NODE_ENV=test
LOG_LEVEL=error

# Test Database
DB_HOST=localhost
DB_PORT=5432
DB_NAME=ecommerce_analytics_test
DB_USER=postgres
DB_PASSWORD=password

# Test Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=15

# Test Authentication
JWT_SECRET=test_jwt_secret_key_for_testing_only
JWT_EXPIRES_IN=1h

# Test Rate Limiting
RATE_LIMIT_MAX=1000
RATE_LIMIT_WINDOW_MS=60000

# Test CORS
CORS_ORIGINS=http://localhost:3000,http://localhost:5173
```

### Jest Configuration

The Jest configuration is split by service and test type:

- **Unit Tests**: Fast, isolated tests for each service
- **Integration Tests**: API endpoint testing with real database
- **E2E Tests**: Complete user journey testing
- **Performance Tests**: Load and stress testing

## Writing Tests

### Unit Test Example

```javascript
// services/analytics/src/services/__tests__/cohortService.test.js
const cohortService = require('../cohortService');

describe('CohortService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('calculateRetention', () => {
    test('should calculate weekly retention correctly', async () => {
      const mockData = [
        { userId: '1', firstSeen: '2024-01-01', week: 0 },
        { userId: '1', week: 1 },
        { userId: '2', firstSeen: '2024-01-01', week: 0 }
      ];

      const result = await cohortService.calculateRetention(mockData, 'week');

      expect(result).toHaveProperty('cohorts');
      expect(result.cohorts).toHaveLength(1);
      expect(result.cohorts[0].retention[1]).toBe(0.5); // 50% retention
    });
  });
});
```

### Integration Test Example

```javascript
// testing/integration/dashboard-api.test.js
const request = require('supertest');
const app = require('../../services/dashboard/src/app');

describe('Dashboard API Integration', () => {
  let authToken;

  beforeAll(async () => {
    // Setup test user and get auth token
    const response = await request(app)
      .post('/api/auth/login')
      .send({ email: '<EMAIL>', password: 'test123' });
    
    authToken = response.body.token;
  });

  test('GET /api/links should return user links', async () => {
    const response = await request(app)
      .get('/api/links')
      .set('Authorization', `Bearer ${authToken}`)
      .expect(200);

    expect(response.body).toHaveProperty('links');
    expect(response.body).toHaveProperty('pagination');
    expect(response.body.pagination).toHaveValidPagination();
  });
});
```

### E2E Test Example

```javascript
// testing/e2e/user-onboarding.test.js
describe('User Onboarding Flow', () => {
  test('complete user registration and first link creation', async () => {
    // 1. Register new user
    const registerResponse = await request(dashboardApp)
      .post('/api/auth/register')
      .send({
        email: global.testUtils.randomEmail(),
        password: 'SecurePass123!',
        firstName: 'John',
        lastName: 'Doe',
        tenantName: 'Test Company'
      })
      .expect(201);

    const { token } = registerResponse.body;

    // 2. Create first link
    const linkResponse = await request(dashboardApp)
      .post('/api/links')
      .set('Authorization', `Bearer ${token}`)
      .send({
        originalUrl: 'https://example.com/product',
        title: 'My First Link'
      })
      .expect(201);

    expect(linkResponse.body).toHaveProperty('shortCode');
    expect(linkResponse.body.shortCode).toMatch(/^[a-zA-Z0-9]{6,8}$/);

    // 3. Verify analytics tracking
    const analyticsResponse = await request(analyticsApp)
      .get(`/api/analytics/links/${linkResponse.body.id}`)
      .expect(200);

    expect(analyticsResponse.body).toHaveProperty('overview');
  });
});
```

### Performance Test Example

```javascript
// testing/performance/api-load.test.js
const autocannon = require('autocannon');

describe('API Performance Tests', () => {
  test('link redirect endpoint load test', async () => {
    const result = await autocannon({
      url: 'http://localhost:3000/api/r/test123',
      connections: 100,
      duration: 30
    });

    expect(result.errors).toBe(0);
    expect(result.latency.average).toBeLessThan(100); // < 100ms
    expect(result.requests.average).toBeGreaterThan(1000); // > 1000 req/s
  });
});
```

## Test Utilities

### Global Test Utilities

Available in all tests via `global.testUtils`:

```javascript
// Random data generators
const email = global.testUtils.randomEmail();
const string = global.testUtils.randomString(10);
const url = global.testUtils.randomUrl();

// Mock data creators
const user = global.testUtils.mockUser({ role: 'admin' });
const link = global.testUtils.mockLink({ title: 'Test Link' });
const order = global.testUtils.mockOrder({ total: 99.99 });

// Date utilities
const pastDate = global.testUtils.dateInPast(7); // 7 days ago
const futureDate = global.testUtils.dateInFuture(30); // 30 days from now

// Validation helpers
expect(email).toBeValidEmail();
expect(url).toBeValidUrl();
expect(response.body.pagination).toHaveValidPagination();

// HTTP assertion helpers
global.testUtils.expectHttpStatus(response, 200);
global.testUtils.expectValidationError(response, 'email');
global.testUtils.expectNotFound(response);
```

### Custom Jest Matchers

```javascript
// Custom matchers for common assertions
expect('<EMAIL>').toBeValidEmail();
expect('https://example.com').toBeValidUrl();
expect('123e4567-e89b-12d3-a456-************').toBeValidUUID();
expect(new Date()).toBeWithinTimeRange(startDate, endDate);
expect(paginationObject).toHaveValidPagination();
```

## Database Testing

### Test Database Setup

The test framework automatically:
1. Creates isolated test database
2. Runs all migrations
3. Seeds test data
4. Cleans up after tests

### Test Data Management

```javascript
// Use transactions for test isolation
beforeEach(async () => {
  await db.query('BEGIN');
});

afterEach(async () => {
  await db.query('ROLLBACK');
});

// Or use test-specific data cleanup
afterEach(async () => {
  await cleanupTestData();
});
```

## Mocking and Fixtures

### External Service Mocks

```javascript
// Mock Shopify API
jest.mock('../../src/platforms/shopify/client', () => ({
  getOrders: jest.fn().mockResolvedValue([]),
  createWebhook: jest.fn().mockResolvedValue({ id: 'webhook_123' })
}));

// Mock Redis
jest.mock('redis', () => ({
  createClient: jest.fn(() => ({
    connect: jest.fn(),
    get: jest.fn(),
    set: jest.fn(),
    del: jest.fn()
  }))
}));
```

### Test Fixtures

```javascript
// Load test fixtures
const userFixtures = require('../fixtures/users.json');
const linkFixtures = require('../fixtures/links.json');

// Use in tests
beforeEach(async () => {
  await loadFixtures([userFixtures, linkFixtures]);
});
```

## Coverage Requirements

### Minimum Coverage Thresholds

- **Overall**: 80%
- **Functions**: 80%
- **Branches**: 80%
- **Lines**: 80%
- **Statements**: 80%

### Coverage Exclusions

- Configuration files
- Database migration files
- Test files themselves
- Third-party integrations

## Continuous Integration

### GitHub Actions Integration

```yaml
# .github/workflows/test.yml
name: Test Suite
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_PASSWORD: password
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      redis:
        image: redis:6
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run tests
        run: ./scripts/run-tests.sh --ci --coverage
      
      - name: Upload coverage
        uses: codecov/codecov-action@v3
        with:
          file: ./testing/coverage/lcov.info
```

### Local CI Simulation

```bash
# Run tests exactly as CI would
./scripts/run-tests.sh --ci --coverage --type all

# Check if ready for CI
npm run validate
```

## Debugging Tests

### Debug Mode

```bash
# Run tests with debugger
npm run test:debug

# Run specific test with debugger
npm run test:debug -- --testNamePattern="should create link"
```

### Verbose Output

```bash
# Enable verbose logging
./scripts/run-tests.sh --verbose --type unit

# Or set environment variable
export TEST_VERBOSE=true
npm run test:unit
```

### Test Isolation

```bash
# Run tests serially for easier debugging
./scripts/run-tests.sh --type integration --no-parallel

# Run single test file
npx jest testing/integration/api.test.js
```

## Performance Monitoring

### Test Performance Metrics

The framework tracks:
- Test execution time
- Database query performance
- Memory usage during tests
- API response times

### Performance Budgets

- Unit tests: < 30 seconds total
- Integration tests: < 2 minutes total
- E2E tests: < 5 minutes total
- Performance tests: < 10 minutes total

## Troubleshooting

### Common Issues

1. **Database Connection Errors**
   ```bash
   # Ensure PostgreSQL is running
   docker-compose up -d postgres
   
   # Check connection
   psql -h localhost -U postgres -d ecommerce_analytics_test
   ```

2. **Redis Connection Errors**
   ```bash
   # Ensure Redis is running
   docker-compose up -d redis
   
   # Check connection
   redis-cli -h localhost ping
   ```

3. **Port Conflicts**
   ```bash
   # Check what's using test ports
   lsof -i :3010-3020
   
   # Kill conflicting processes
   pkill -f "node.*3010"
   ```

4. **Memory Issues**
   ```bash
   # Run tests with more memory
   NODE_OPTIONS="--max-old-space-size=4096" npm run test
   
   # Run tests serially to reduce memory usage
   npm run test:serial
   ```

### Debug Commands

```bash
# Check test database
docker-compose exec postgres psql -U postgres -d ecommerce_analytics_test -c "\dt"

# Check test Redis
docker-compose exec redis redis-cli -n 15 keys "*"

# View test logs
tail -f testing/logs/test.log

# Clean and restart
./scripts/run-tests.sh --type smoke --no-cleanup
```

## Contributing

### Adding New Tests

1. **Unit Tests**: Add to appropriate service `__tests__` directory
2. **Integration Tests**: Add to `testing/integration/`
3. **E2E Tests**: Add to `testing/e2e/`
4. **Performance Tests**: Add to `testing/performance/`

### Test Naming Conventions

- Test files: `*.test.js`
- Test descriptions: Use `should` for behavior
- Test groups: Use `describe` for logical grouping

### Code Quality

```bash
# Lint test files
npm run lint

# Fix linting issues
npm run lint:fix

# Validate test structure
npm run validate
```

## Resources

- [Jest Documentation](https://jestjs.io/docs/getting-started)
- [Supertest Documentation](https://github.com/visionmedia/supertest)
- [Autocannon Documentation](https://github.com/mcollina/autocannon)
- [Testing Best Practices](https://github.com/goldbergyoni/javascript-testing-best-practices)