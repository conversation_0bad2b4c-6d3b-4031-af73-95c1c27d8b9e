const request = require('supertest');
const { v4: uuidv4 } = require('uuid');

describe('API Integration Tests', () => {
  let apps = {};
  let authTokens = {};
  let testUser;
  let testTenant;
  
  beforeAll(async () => {
    // Import all service apps
    const dashboardApp = require('../../services/dashboard/src/index');
    const analyticsApp = require('../../services/analytics/src/app');
    const integrationApp = require('../../services/integration/src/index');
    
    apps = {
      dashboard: dashboardApp,
      analytics: analyticsApp,
      integration: integrationApp
    };
    
    // Create test tenant and user
    testTenant = {
      id: uuidv4(),
      name: 'Test Tenant',
      domain: 'test.example.com',
      settings: {}
    };
    
    testUser = {
      id: uuidv4(),
      email: '<EMAIL>',
      password: 'test_password_123',
      firstName: 'Test',
      lastName: 'User',
      role: 'admin',
      tenantId: testTenant.id
    };
    
    // Create test data
    await createTestData();
    
    // Get authentication tokens
    await authenticateTestUser();
  });
  
  afterAll(async () => {
    // Clean up test data
    await cleanupTestData();
  });
  
  describe('Authentication Flow', () => {
    test('should register a new user', async () => {
      const newUser = {
        email: '<EMAIL>',
        password: 'new_password_123',
        firstName: 'New',
        lastName: 'User',
        tenantName: 'New Tenant'
      };
      
      const response = await request(apps.dashboard)
        .post('/api/auth/register')
        .send(newUser)
        .expect(201);
      
      expect(response.body).toHaveProperty('user');
      expect(response.body).toHaveProperty('token');
      expect(response.body.user.email).toBe(newUser.email);
      expect(response.body.user).not.toHaveProperty('password');
    });
    
    test('should login with valid credentials', async () => {
      const response = await request(apps.dashboard)
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: testUser.password
        })
        .expect(200);
      
      expect(response.body).toHaveProperty('user');
      expect(response.body).toHaveProperty('token');
      expect(response.body.user.email).toBe(testUser.email);
      
      authTokens.dashboard = response.body.token;
    });
    
    test('should reject login with invalid credentials', async () => {
      await request(apps.dashboard)
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: 'wrong_password'
        })
        .expect(401);
    });
    
    test('should refresh authentication token', async () => {
      const response = await request(apps.dashboard)
        .post('/api/auth/refresh')
        .set('Authorization', `Bearer ${authTokens.dashboard}`)
        .expect(200);
      
      expect(response.body).toHaveProperty('token');
      expect(response.body.token).not.toBe(authTokens.dashboard);
    });
  });
  
  describe('Link Management', () => {
    let testLink;
    
    test('should create a new link', async () => {
      const linkData = {
        originalUrl: 'https://example.com/product/123',
        title: 'Test Product',
        description: 'A test product link',
        tags: ['test', 'product'],
        campaignId: uuidv4()
      };
      
      const response = await request(apps.dashboard)
        .post('/api/links')
        .set('Authorization', `Bearer ${authTokens.dashboard}`)
        .send(linkData)
        .expect(201);
      
      expect(response.body).toHaveProperty('id');
      expect(response.body).toHaveProperty('shortCode');
      expect(response.body.originalUrl).toBe(linkData.originalUrl);
      expect(response.body.title).toBe(linkData.title);
      expect(response.body.shortCode).toMatch(/^[a-zA-Z0-9]{6,8}$/);
      
      testLink = response.body;
    });
    
    test('should get link details', async () => {
      const response = await request(apps.dashboard)
        .get(`/api/links/${testLink.id}`)
        .set('Authorization', `Bearer ${authTokens.dashboard}`)
        .expect(200);
      
      expect(response.body.id).toBe(testLink.id);
      expect(response.body.originalUrl).toBe(testLink.originalUrl);
      expect(response.body).toHaveProperty('analytics');
    });
    
    test('should list user links with pagination', async () => {
      const response = await request(apps.dashboard)
        .get('/api/links?page=1&limit=10')
        .set('Authorization', `Bearer ${authTokens.dashboard}`)
        .expect(200);
      
      expect(response.body).toHaveProperty('links');
      expect(response.body).toHaveProperty('pagination');
      expect(response.body.pagination).toHaveValidPagination();
      expect(Array.isArray(response.body.links)).toBe(true);
    });
    
    test('should update link details', async () => {
      const updateData = {
        title: 'Updated Test Product',
        description: 'Updated description',
        tags: ['test', 'product', 'updated']
      };
      
      const response = await request(apps.dashboard)
        .put(`/api/links/${testLink.id}`)
        .set('Authorization', `Bearer ${authTokens.dashboard}`)
        .send(updateData)
        .expect(200);
      
      expect(response.body.title).toBe(updateData.title);
      expect(response.body.description).toBe(updateData.description);
      expect(response.body.tags).toEqual(updateData.tags);
    });
    
    test('should delete a link', async () => {
      await request(apps.dashboard)
        .delete(`/api/links/${testLink.id}`)
        .set('Authorization', `Bearer ${authTokens.dashboard}`)
        .expect(200);
      
      // Verify link is deleted
      await request(apps.dashboard)
        .get(`/api/links/${testLink.id}`)
        .set('Authorization', `Bearer ${authTokens.dashboard}`)
        .expect(404);
    });
  });
  
  describe('Analytics API', () => {
    let analyticsTestLink;
    
    beforeAll(async () => {
      // Create a link for analytics testing
      const response = await request(apps.dashboard)
        .post('/api/links')
        .set('Authorization', `Bearer ${authTokens.dashboard}`)
        .send({
          originalUrl: 'https://example.com/analytics-test',
          title: 'Analytics Test Link'
        });
      
      analyticsTestLink = response.body;
      
      // Generate some test clicks
      await generateTestClicks(analyticsTestLink.id);
    });
    
    test('should get link analytics', async () => {
      const response = await request(apps.analytics)
        .get(`/api/analytics/links/${analyticsTestLink.id}`)
        .query({
          timeRange: '7d',
          groupBy: 'day'
        })
        .expect(200);
      
      expect(response.body).toHaveProperty('overview');
      expect(response.body).toHaveProperty('timeSeries');
      expect(response.body).toHaveProperty('geographic');
      expect(response.body).toHaveProperty('devices');
      expect(response.body.overview).toHaveProperty('totalClicks');
      expect(response.body.overview).toHaveProperty('uniqueClicks');
      expect(Array.isArray(response.body.timeSeries)).toBe(true);
    });
    
    test('should get cohort analysis', async () => {
      const response = await request(apps.analytics)
        .get('/api/analytics/cohorts')
        .query({
          type: 'user',
          timeRange: '30d',
          groupBy: 'week'
        })
        .expect(200);
      
      expect(response.body).toHaveProperty('cohorts');
      expect(response.body).toHaveProperty('summary');
      expect(Array.isArray(response.body.cohorts)).toBe(true);
    });
    
    test('should get funnel analysis', async () => {
      const response = await request(apps.analytics)
        .get('/api/analytics/funnels')
        .query({
          events: JSON.stringify(['click', 'view', 'purchase']),
          timeRange: '7d'
        })
        .expect(200);
      
      expect(response.body).toHaveProperty('steps');
      expect(response.body).toHaveProperty('conversion');
      expect(Array.isArray(response.body.steps)).toBe(true);
    });
    
    test('should get attribution analysis', async () => {
      const response = await request(apps.analytics)
        .get('/api/analytics/attribution')
        .query({
          model: 'last_click',
          timeRange: '30d'
        })
        .expect(200);
      
      expect(response.body).toHaveProperty('attribution');
      expect(response.body).toHaveProperty('touchpoints');
      expect(response.body).toHaveProperty('conversions');
    });
  });
  
  describe('Integration API', () => {
    test('should get available integrations', async () => {
      const response = await request(apps.integration)
        .get('/api/integrations')
        .expect(200);
      
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
      
      const shopifyIntegration = response.body.find(i => i.platform === 'shopify');
      expect(shopifyIntegration).toBeDefined();
      expect(shopifyIntegration).toHaveProperty('name');
      expect(shopifyIntegration).toHaveProperty('description');
      expect(shopifyIntegration).toHaveProperty('authType');
    });
    
    test('should create Shopify integration', async () => {
      const integrationData = {
        platform: 'shopify',
        name: 'Test Shopify Store',
        settings: {
          shopDomain: 'test-store.myshopify.com',
          accessToken: 'test_access_token_123'
        }
      };
      
      const response = await request(apps.integration)
        .post('/api/integrations')
        .send(integrationData)
        .expect(201);
      
      expect(response.body).toHaveProperty('id');
      expect(response.body.platform).toBe('shopify');
      expect(response.body.name).toBe(integrationData.name);
      expect(response.body.status).toBe('pending');
    });
    
    test('should handle webhook endpoint', async () => {
      const webhookData = {
        id: 12345,
        email: '<EMAIL>',
        total_price: '99.99',
        currency: 'USD',
        line_items: [
          {
            id: 54321,
            title: 'Test Product',
            quantity: 1,
            price: '99.99'
          }
        ],
        created_at: new Date().toISOString()
      };
      
      const response = await request(apps.integration)
        .post('/api/webhooks/shopify/orders/create')
        .set('X-Shopify-Topic', 'orders/create')
        .set('X-Shopify-Shop-Domain', 'test-store.myshopify.com')
        .send(webhookData)
        .expect(200);
      
      expect(response.body).toHaveProperty('success');
      expect(response.body.success).toBe(true);
    });
  });
  
  describe('Error Handling', () => {
    test('should handle 404 errors gracefully', async () => {
      await request(apps.dashboard)
        .get('/api/nonexistent-endpoint')
        .expect(404);
    });
    
    test('should handle validation errors', async () => {
      const response = await request(apps.dashboard)
        .post('/api/links')
        .set('Authorization', `Bearer ${authTokens.dashboard}`)
        .send({
          originalUrl: 'invalid-url',
          title: ''
        })
        .expect(400);
      
      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toContain('validation');
    });
    
    test('should handle unauthorized access', async () => {
      await request(apps.dashboard)
        .get('/api/links')
        .expect(401);
    });
    
    test('should handle malformed JSON', async () => {
      await request(apps.dashboard)
        .post('/api/auth/login')
        .set('Content-Type', 'application/json')
        .send('{"invalid": json}')
        .expect(400);
    });
  });
  
  describe('Rate Limiting', () => {
    test('should enforce rate limits', async () => {
      const requests = Array(15).fill().map(() =>
        request(apps.dashboard)
          .get('/api/auth/me')
          .set('Authorization', `Bearer ${authTokens.dashboard}`)
      );
      
      const responses = await Promise.all(requests);
      const rateLimited = responses.some(response => response.status === 429);
      
      // In test environment, rate limiting might be more permissive
      // This test verifies the rate limiting middleware is active
      expect(rateLimited || responses.every(r => r.status === 200)).toBe(true);
    });
  });
  
  describe('CORS Handling', () => {
    test('should handle CORS preflight requests', async () => {
      const response = await request(apps.dashboard)
        .options('/api/auth/login')
        .set('Origin', 'http://localhost:3000')
        .set('Access-Control-Request-Method', 'POST')
        .set('Access-Control-Request-Headers', 'Content-Type,Authorization')
        .expect(200);
      
      expect(response.headers['access-control-allow-origin']).toBeDefined();
      expect(response.headers['access-control-allow-methods']).toBeDefined();
      expect(response.headers['access-control-allow-headers']).toBeDefined();
    });
  });
  
  // Helper functions
  async function createTestData() {
    // This would create test data in the database
    // Implementation depends on your database setup
    console.log('Creating test data...');
  }
  
  async function cleanupTestData() {
    // This would clean up test data from the database
    console.log('Cleaning up test data...');
  }
  
  async function authenticateTestUser() {
    // This would authenticate the test user and get tokens
    console.log('Authenticating test user...');
  }
  
  async function generateTestClicks(linkId) {
    // This would generate test click data for analytics
    console.log(`Generating test clicks for link ${linkId}...`);
  }
});