{"name": "ecommerce-analytics-testing", "version": "1.0.0", "description": "Comprehensive testing suite for e-commerce analytics platform", "main": "jest.config.js", "scripts": {"test": "jest --config jest.config.js", "test:unit": "jest --config jest.config.js --selectProjects \"Dashboard API\" \"Analytics Service\" \"Integration Service\" \"Error Tracking Service\" \"Admin Service\"", "test:integration": "jest --config jest.config.js --selectProjects \"Integration Tests\"", "test:e2e": "jest --config jest.config.js --selectProjects \"E2E Tests\"", "test:performance": "jest --config jest.config.js --selectProjects \"Performance Tests\"", "test:watch": "jest --config jest.config.js --watch", "test:coverage": "jest --config jest.config.js --coverage", "test:ci": "jest --config jest.config.js --ci --coverage --watchAll=false", "test:debug": "node --inspect-brk node_modules/.bin/jest --config jest.config.js --runInBand", "test:smoke": "jest --config jest.config.js --testNamePattern=\"smoke\"", "test:security": "jest --config jest.config.js --testNamePattern=\"security\"", "test:parallel": "jest --config jest.config.js --maxWorkers=4", "test:serial": "jest --config jest.config.js --runInBand", "setup": "node setup/global.setup.js", "cleanup": "node setup/global.teardown.js", "lint": "eslint **/*.js", "lint:fix": "eslint **/*.js --fix", "report:open": "open reports/jest-report.html", "report:coverage": "open coverage/lcov-report/index.html", "validate": "npm run lint && npm run test:smoke"}, "keywords": ["testing", "jest", "e2e", "integration", "performance", "api-testing"], "author": "E-commerce Analytics Team", "license": "MIT", "dependencies": {"supertest": "^6.3.3", "autocannon": "^7.12.0", "puppeteer": "^21.3.8", "playwright": "^1.40.0", "axios": "^1.5.1", "ws": "^8.14.2", "node-fetch": "^3.3.2", "form-data": "^4.0.0"}, "devDependencies": {"jest": "^29.7.0", "jest-html-reporters": "^3.1.5", "jest-junit": "^16.0.0", "babel-jest": "^29.7.0", "@babel/core": "^7.23.2", "@babel/preset-env": "^7.23.2", "eslint": "^8.52.0", "eslint-plugin-jest": "^27.6.0", "dotenv": "^16.3.1", "pg": "^8.11.3", "redis": "^4.6.10", "uuid": "^9.0.1", "faker": "^6.6.6", "lodash": "^4.17.21", "moment": "^2.29.4", "csv-parse": "^5.5.2", "xml2js": "^0.6.2"}, "jest": {"projects": ["<rootDir>/jest.config.js"]}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/your-org/ecommerce-analytics-saas.git"}, "bugs": {"url": "https://github.com/your-org/ecommerce-analytics-saas/issues"}, "homepage": "https://github.com/your-org/ecommerce-analytics-saas#readme"}