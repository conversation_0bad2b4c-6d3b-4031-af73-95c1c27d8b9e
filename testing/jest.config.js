/** @type {import('jest').Config} */
module.exports = {
  // Use different projects for different service testing
  projects: [
    // Dashboard API Service Tests
    {
      displayName: 'Dashboard API',
      testMatch: ['<rootDir>/services/dashboard/**/*.test.js'],
      testEnvironment: 'node',
      setupFilesAfterEnv: ['<rootDir>/testing/setup/dashboard.setup.js'],
      collectCoverageFrom: [
        'services/dashboard/src/**/*.js',
        '!services/dashboard/src/index.js',
        '!services/dashboard/src/database/**/*.js'
      ],
      coverageDirectory: '<rootDir>/testing/coverage/dashboard',
      coverageReporters: ['text', 'lcov', 'html', 'json-summary'],
      testTimeout: 30000,
      globalSetup: '<rootDir>/testing/setup/global.setup.js',
      globalTeardown: '<rootDir>/testing/setup/global.teardown.js'
    },

    // Analytics Service Tests
    {
      displayName: 'Analytics Service',
      testMatch: ['<rootDir>/services/analytics/**/*.test.js'],
      testEnvironment: 'node',
      setupFilesAfterEnv: ['<rootDir>/testing/setup/analytics.setup.js'],
      collectCoverageFrom: [
        'services/analytics/src/**/*.js',
        '!services/analytics/src/index.js',
        '!services/analytics/src/database/**/*.js'
      ],
      coverageDirectory: '<rootDir>/testing/coverage/analytics',
      coverageReporters: ['text', 'lcov', 'html', 'json-summary'],
      testTimeout: 30000
    },

    // Integration Service Tests
    {
      displayName: 'Integration Service',
      testMatch: ['<rootDir>/services/integration/**/*.test.js'],
      testEnvironment: 'node',
      setupFilesAfterEnv: ['<rootDir>/testing/setup/integration.setup.js'],
      collectCoverageFrom: [
        'services/integration/src/**/*.js',
        '!services/integration/src/index.js',
        '!services/integration/src/database/**/*.js'
      ],
      coverageDirectory: '<rootDir>/testing/coverage/integration',
      coverageReporters: ['text', 'lcov', 'html', 'json-summary'],
      testTimeout: 30000
    },

    // Error Tracking Service Tests
    {
      displayName: 'Error Tracking Service',
      testMatch: ['<rootDir>/services/error-tracking/**/*.test.js'],
      testEnvironment: 'node',
      setupFilesAfterEnv: ['<rootDir>/testing/setup/error-tracking.setup.js'],
      collectCoverageFrom: [
        'services/error-tracking/src/**/*.js',
        '!services/error-tracking/src/app.js',
        '!services/error-tracking/src/config/**/*.js'
      ],
      coverageDirectory: '<rootDir>/testing/coverage/error-tracking',
      coverageReporters: ['text', 'lcov', 'html', 'json-summary'],
      testTimeout: 30000
    },

    // Admin Service Tests
    {
      displayName: 'Admin Service',
      testMatch: ['<rootDir>/services/admin/**/*.test.js'],
      testEnvironment: 'node',
      setupFilesAfterEnv: ['<rootDir>/testing/setup/admin.setup.js'],
      collectCoverageFrom: [
        'services/admin/src/**/*.js',
        '!services/admin/src/app.js',
        '!services/admin/src/config/**/*.js'
      ],
      coverageDirectory: '<rootDir>/testing/coverage/admin',
      coverageReporters: ['text', 'lcov', 'html', 'json-summary'],
      testTimeout: 30000
    },

    // Integration Tests
    {
      displayName: 'Integration Tests',
      testMatch: ['<rootDir>/testing/integration/**/*.test.js'],
      testEnvironment: 'node',
      setupFilesAfterEnv: ['<rootDir>/testing/setup/integration-tests.setup.js'],
      testTimeout: 60000,
      globalSetup: '<rootDir>/testing/setup/integration.global.setup.js',
      globalTeardown: '<rootDir>/testing/setup/integration.global.teardown.js'
    },

    // End-to-End Tests
    {
      displayName: 'E2E Tests',
      testMatch: ['<rootDir>/testing/e2e/**/*.test.js'],
      testEnvironment: 'node',
      setupFilesAfterEnv: ['<rootDir>/testing/setup/e2e.setup.js'],
      testTimeout: 120000,
      globalSetup: '<rootDir>/testing/setup/e2e.global.setup.js',
      globalTeardown: '<rootDir>/testing/setup/e2e.global.teardown.js'
    },

    // Performance Tests
    {
      displayName: 'Performance Tests',
      testMatch: ['<rootDir>/testing/performance/**/*.test.js'],
      testEnvironment: 'node',
      setupFilesAfterEnv: ['<rootDir>/testing/setup/performance.setup.js'],
      testTimeout: 300000, // 5 minutes for performance tests
      globalSetup: '<rootDir>/testing/setup/performance.global.setup.js',
      globalTeardown: '<rootDir>/testing/setup/performance.global.teardown.js'
    }
  ],

  // Global Jest configuration
  roots: ['<rootDir>/services', '<rootDir>/testing'],
  moduleFileExtensions: ['js', 'json', 'ts'],
  transform: {
    '^.+\\.(js|ts)$': 'babel-jest'
  },
  transformIgnorePatterns: [
    '/node_modules/(?!(node-fetch|fetch-blob|data-uri-to-buffer|formdata-polyfill)/)'
  ],

  // Global coverage settings
  collectCoverage: true,
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  },

  // Test result processors
  reporters: [
    'default',
    ['jest-html-reporters', {
      publicPath: './testing/reports',
      filename: 'jest-report.html',
      expand: true,
      hideIcon: false,
      pageTitle: 'E-commerce Analytics Test Report'
    }],
    ['jest-junit', {
      outputDirectory: './testing/reports',
      outputName: 'junit.xml',
      ancestorSeparator: ' › ',
      uniqueOutputName: 'false',
      suiteNameTemplate: '{filepath}',
      classNameTemplate: '{classname}',
      titleTemplate: '{title}'
    }]
  ],

  // Mock configuration
  clearMocks: true,
  resetMocks: true,
  restoreMocks: true,

  // Error handling
  errorOnDeprecated: true,
  verbose: true,

  // Test environment
  testEnvironment: 'node',
  testEnvironmentOptions: {
    NODE_ENV: 'test'
  },

  // Module path mapping
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@shared/(.*)$': '<rootDir>/shared/$1',
    '^@testing/(.*)$': '<rootDir>/testing/$1'
  },

  // Setup files
  setupFiles: ['<rootDir>/testing/setup/jest.setup.js'],

  // Watch mode configuration
  watchPathIgnorePatterns: [
    '/node_modules/',
    '/coverage/',
    '/testing/reports/',
    '/testing/coverage/',
    '/.git/'
  ],

  // Cache configuration
  cacheDirectory: '<rootDir>/testing/.jest-cache',

  // Notification configuration
  notify: false,
  notifyMode: 'failure-change',

  // Bail configuration for CI
  bail: process.env.CI ? 1 : 0,

  // Force exit for CI
  forceExit: process.env.CI ? true : false,

  // Run tests in band for CI to avoid resource issues
  runInBand: process.env.CI ? true : false,

  // Max workers configuration
  maxWorkers: process.env.CI ? 2 : '50%'
};