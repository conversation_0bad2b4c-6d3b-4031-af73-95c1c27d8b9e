const request = require('supertest');
const { v4: uuidv4 } = require('uuid');

describe('E2E User Journey Tests', () => {
  let apps = {};
  let testUser;
  let authToken;
  let testTenant;
  let createdResources = {
    links: [],
    campaigns: [],
    integrations: []
  };
  
  beforeAll(async () => {
    // Start all services
    apps.dashboard = require('../../services/dashboard/src/index');
    apps.analytics = require('../../services/analytics/src/app');
    apps.integration = require('../../services/integration/src/index');
    
    // Wait for services to be ready
    await global.testUtils.timeout(2000);
  });
  
  afterAll(async () => {
    // Cleanup all created resources
    await cleanupAllResources();
  });
  
  describe('Complete User Onboarding Journey', () => {
    test('1. User Registration and Account Setup', async () => {
      const userData = {
        email: global.testUtils.randomEmail(),
        password: 'SecurePassword123!',
        firstName: 'John',
        lastName: 'Doe',
        tenantName: 'Acme E-commerce'
      };
      
      // Register new user
      const registerResponse = await request(apps.dashboard)
        .post('/api/auth/register')
        .send(userData)
        .expect(201);
      
      expect(registerResponse.body).toHaveProperty('user');
      expect(registerResponse.body).toHaveProperty('token');
      expect(registerResponse.body).toHaveProperty('tenant');
      expect(registerResponse.body.user.email).toBe(userData.email);
      expect(registerResponse.body.tenant.name).toBe(userData.tenantName);
      
      testUser = registerResponse.body.user;
      testTenant = registerResponse.body.tenant;
      authToken = registerResponse.body.token;
      
      // Verify user can access protected routes
      const profileResponse = await request(apps.dashboard)
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);
      
      expect(profileResponse.body.id).toBe(testUser.id);
    });
    
    test('2. Complete Profile Setup', async () => {
      const profileData = {
        company: 'Acme E-commerce Inc.',
        website: 'https://acme-ecommerce.com',
        industry: 'retail',
        teamSize: '10-50',
        timezone: 'America/New_York',
        preferences: {
          emailNotifications: true,
          weeklyReports: true,
          realTimeAlerts: false
        }
      };
      
      const response = await request(apps.dashboard)
        .put('/api/users/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .send(profileData)
        .expect(200);
      
      expect(response.body.company).toBe(profileData.company);
      expect(response.body.website).toBe(profileData.website);
      expect(response.body.preferences).toEqual(profileData.preferences);
    });
    
    test('3. Create First Campaign', async () => {
      const campaignData = {
        name: 'Summer Sale 2024',
        description: 'Promotional campaign for summer products',
        startDate: new Date(),
        endDate: global.testUtils.dateInFuture(30),
        goals: {
          clicks: 10000,
          conversions: 500,
          revenue: 25000
        },
        utmSource: 'email',
        utmMedium: 'newsletter',
        utmCampaign: 'summer_sale_2024'
      };
      
      const response = await request(apps.dashboard)
        .post('/api/campaigns')
        .set('Authorization', `Bearer ${authToken}`)
        .send(campaignData)
        .expect(201);
      
      expect(response.body).toHaveProperty('id');
      expect(response.body.name).toBe(campaignData.name);
      expect(response.body.status).toBe('active');
      
      createdResources.campaigns.push(response.body.id);
    });
  });
  
  describe('Link Management Workflow', () => {
    let campaignId;
    
    beforeAll(async () => {
      // Get the campaign created in previous test
      const campaignsResponse = await request(apps.dashboard)
        .get('/api/campaigns')
        .set('Authorization', `Bearer ${authToken}`);
      
      campaignId = campaignsResponse.body.campaigns[0].id;
    });
    
    test('4. Create Multiple Product Links', async () => {
      const products = [
        {
          name: 'Wireless Headphones',
          url: 'https://acme-ecommerce.com/products/wireless-headphones',
          price: 199.99
        },
        {
          name: 'Smart Watch',
          url: 'https://acme-ecommerce.com/products/smart-watch',
          price: 299.99
        },
        {
          name: 'Bluetooth Speaker',
          url: 'https://acme-ecommerce.com/products/bluetooth-speaker',
          price: 89.99
        }
      ];
      
      for (const product of products) {
        const linkData = {
          originalUrl: product.url,
          title: product.name,
          description: `Premium ${product.name} - Special Summer Price $${product.price}`,
          campaignId: campaignId,
          tags: ['summer-sale', 'electronics', product.name.toLowerCase().replace(/\s+/g, '-')],
          metadata: {
            price: product.price,
            category: 'electronics'
          }
        };
        
        const response = await request(apps.dashboard)
          .post('/api/links')
          .set('Authorization', `Bearer ${authToken}`)
          .send(linkData)
          .expect(201);
        
        expect(response.body).toHaveProperty('shortCode');
        expect(response.body.title).toBe(product.name);
        expect(response.body.campaignId).toBe(campaignId);
        
        createdResources.links.push(response.body.id);
      }
    });
    
    test('5. Bulk Update Link Settings', async () => {
      const updateData = {
        linkIds: createdResources.links,
        updates: {
          tags: ['summer-sale', 'electronics', 'featured'],
          expiresAt: global.testUtils.dateInFuture(30),
          trackingEnabled: true
        }
      };
      
      const response = await request(apps.dashboard)
        .patch('/api/links/bulk')
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData)
        .expect(200);
      
      expect(response.body.updated).toBe(createdResources.links.length);
      expect(response.body.failed).toBe(0);
    });
    
    test('6. Generate QR Codes for Links', async () => {
      const linkId = createdResources.links[0];
      
      const response = await request(apps.dashboard)
        .post(`/api/links/${linkId}/qr-code`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          size: 300,
          format: 'png',
          errorCorrection: 'M'
        })
        .expect(200);
      
      expect(response.body).toHaveProperty('qrCodeUrl');
      expect(response.body.qrCodeUrl).toBeValidUrl();
    });
  });
  
  describe('E-commerce Integration Setup', () => {
    test('7. Connect Shopify Store', async () => {
      const shopifyData = {
        platform: 'shopify',
        name: 'Acme Shopify Store',
        settings: {
          shopDomain: 'acme-test.myshopify.com',
          accessToken: 'test_access_token_' + global.testUtils.randomString(32),
          apiVersion: '2023-07'
        },
        webhookEndpoints: [
          'orders/create',
          'orders/updated',
          'customers/create'
        ]
      };
      
      const response = await request(apps.integration)
        .post('/api/integrations')
        .send(shopifyData)
        .expect(201);
      
      expect(response.body).toHaveProperty('id');
      expect(response.body.platform).toBe('shopify');
      expect(response.body.status).toBe('pending');
      
      createdResources.integrations.push(response.body.id);
      
      // Test connection
      const testResponse = await request(apps.integration)
        .post(`/api/integrations/${response.body.id}/test`)
        .expect(200);
      
      expect(testResponse.body.connected).toBe(true);
    });
    
    test('8. Configure Automatic Link Generation', async () => {
      const integrationId = createdResources.integrations[0];
      
      const autoLinkConfig = {
        enabled: true,
        rules: [
          {
            trigger: 'product_created',
            template: {
              title: '{{product.title}}',
              description: '{{product.description}}',
              tags: ['auto-generated', 'product'],
              campaignId: campaignId
            }
          },
          {
            trigger: 'collection_created',
            template: {
              title: '{{collection.title}} Collection',
              description: 'Shop the {{collection.title}} collection',
              tags: ['auto-generated', 'collection'],
              campaignId: campaignId
            }
          }
        ]
      };
      
      const response = await request(apps.integration)
        .put(`/api/integrations/${integrationId}/auto-links`)
        .send(autoLinkConfig)
        .expect(200);
      
      expect(response.body.enabled).toBe(true);
      expect(response.body.rules).toHaveLength(2);
    });
    
    test('9. Simulate Order Attribution', async () => {
      const linkId = createdResources.links[0];
      
      // Simulate click on link
      const clickResponse = await request(apps.dashboard)
        .post(`/api/links/${linkId}/click`)
        .send({
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          ipAddress: '*************',
          referer: 'https://google.com',
          utm: {
            source: 'email',
            medium: 'newsletter',
            campaign: 'summer_sale_2024'
          }
        })
        .expect(200);
      
      const clickId = clickResponse.body.clickId;
      
      // Simulate order creation via webhook
      const orderData = {
        id: 12345,
        email: '<EMAIL>',
        total_price: '199.99',
        currency: 'USD',
        customer: {
          id: 67890,
          email: '<EMAIL>',
          first_name: 'Jane',
          last_name: 'Smith'
        },
        line_items: [
          {
            id: 54321,
            title: 'Wireless Headphones',
            quantity: 1,
            price: '199.99',
            product_id: 98765
          }
        ],
        created_at: new Date().toISOString(),
        referring_site: `https://acme.link/${linkResponse.body.shortCode}`
      };
      
      const webhookResponse = await request(apps.integration)
        .post('/api/webhooks/shopify/orders/create')
        .set('X-Shopify-Topic', 'orders/create')
        .set('X-Shopify-Shop-Domain', 'acme-test.myshopify.com')
        .send(orderData)
        .expect(200);
      
      expect(webhookResponse.body.success).toBe(true);
      
      // Verify attribution was created
      await global.testUtils.timeout(1000); // Wait for processing
      
      const attributionResponse = await request(apps.analytics)
        .get(`/api/analytics/attribution/click/${clickId}`)
        .expect(200);
      
      expect(attributionResponse.body).toHaveProperty('order');
      expect(attributionResponse.body.order.total).toBe(199.99);
    });
  });
  
  describe('Analytics and Reporting', () => {
    test('10. View Campaign Performance', async () => {
      const campaignId = createdResources.campaigns[0];
      
      const response = await request(apps.analytics)
        .get(`/api/analytics/campaigns/${campaignId}`)
        .query({
          timeRange: '7d',
          includeAttribution: true
        })
        .expect(200);
      
      expect(response.body).toHaveProperty('overview');
      expect(response.body).toHaveProperty('performance');
      expect(response.body).toHaveProperty('attribution');
      expect(response.body.overview).toHaveProperty('totalClicks');
      expect(response.body.overview).toHaveProperty('totalConversions');
      expect(response.body.overview).toHaveProperty('revenue');
    });
    
    test('11. Generate Custom Report', async () => {
      const reportConfig = {
        name: 'Summer Sale Performance Report',
        type: 'campaign_performance',
        timeRange: {
          start: global.testUtils.dateInPast(7),
          end: new Date()
        },
        metrics: [
          'clicks',
          'conversions',
          'revenue',
          'click_through_rate',
          'conversion_rate'
        ],
        dimensions: [
          'campaign',
          'utm_source',
          'device_type',
          'geographic_region'
        ],
        filters: {
          campaignId: createdResources.campaigns
        },
        format: 'json'
      };
      
      const response = await request(apps.analytics)
        .post('/api/analytics/reports')
        .send(reportConfig)
        .expect(201);
      
      expect(response.body).toHaveProperty('reportId');
      expect(response.body.status).toBe('processing');
      
      // Poll for report completion
      let reportCompleted = false;
      let attempts = 0;
      const maxAttempts = 10;
      
      while (!reportCompleted && attempts < maxAttempts) {
        await global.testUtils.timeout(1000);
        
        const statusResponse = await request(apps.analytics)
          .get(`/api/analytics/reports/${response.body.reportId}`)
          .expect(200);
        
        if (statusResponse.body.status === 'completed') {
          reportCompleted = true;
          expect(statusResponse.body).toHaveProperty('data');
          expect(statusResponse.body.data).toHaveProperty('metrics');
          expect(statusResponse.body.data).toHaveProperty('dimensions');
        }
        
        attempts++;
      }
      
      expect(reportCompleted).toBe(true);
    });
    
    test('12. Set Up Real-time Alerts', async () => {
      const alertConfig = {
        name: 'High Traffic Alert',
        description: 'Alert when campaign traffic exceeds threshold',
        conditions: [
          {
            metric: 'clicks_per_hour',
            operator: 'greater_than',
            threshold: 100,
            timeWindow: '1h'
          }
        ],
        actions: [
          {
            type: 'email',
            recipients: [testUser.email],
            template: 'high_traffic_alert'
          },
          {
            type: 'webhook',
            url: 'https://hooks.slack.com/services/test/webhook'
          }
        ],
        enabled: true
      };
      
      const response = await request(apps.analytics)
        .post('/api/analytics/alerts')
        .send(alertConfig)
        .expect(201);
      
      expect(response.body).toHaveProperty('id');
      expect(response.body.name).toBe(alertConfig.name);
      expect(response.body.enabled).toBe(true);
    });
  });
  
  describe('Advanced Features', () => {
    test('13. A/B Testing Setup', async () => {
      const linkId = createdResources.links[0];
      
      const abTestConfig = {
        name: 'Landing Page Test',
        description: 'Test different landing page designs',
        variants: [
          {
            name: 'Original',
            url: 'https://acme-ecommerce.com/products/wireless-headphones',
            weight: 50
          },
          {
            name: 'New Design',
            url: 'https://acme-ecommerce.com/products/wireless-headphones?variant=new',
            weight: 50
          }
        ],
        conversionGoal: 'purchase',
        duration: 14, // days
        minSampleSize: 1000
      };
      
      const response = await request(apps.dashboard)
        .post(`/api/links/${linkId}/ab-test`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(abTestConfig)
        .expect(201);
      
      expect(response.body).toHaveProperty('testId');
      expect(response.body.status).toBe('active');
      expect(response.body.variants).toHaveLength(2);
    });
    
    test('14. Custom Link Domains', async () => {
      const domainConfig = {
        domain: 'go.acme-ecommerce.com',
        sslEnabled: true,
        defaultRedirect: 'https://acme-ecommerce.com',
        analyticsEnabled: true
      };
      
      const response = await request(apps.dashboard)
        .post('/api/domains')
        .set('Authorization', `Bearer ${authToken}`)
        .send(domainConfig)
        .expect(201);
      
      expect(response.body).toHaveProperty('id');
      expect(response.body.domain).toBe(domainConfig.domain);
      expect(response.body.status).toBe('pending_verification');
    });
    
    test('15. Export Data', async () => {
      const exportConfig = {
        type: 'campaign_data',
        format: 'csv',
        dateRange: {
          start: global.testUtils.dateInPast(30),
          end: new Date()
        },
        includeFields: [
          'link_id',
          'short_code',
          'original_url',
          'title',
          'clicks',
          'conversions',
          'revenue'
        ]
      };
      
      const response = await request(apps.dashboard)
        .post('/api/exports')
        .set('Authorization', `Bearer ${authToken}`)
        .send(exportConfig)
        .expect(202);
      
      expect(response.body).toHaveProperty('exportId');
      expect(response.body.status).toBe('processing');
    });
  });
  
  describe('Account Management', () => {
    test('16. Team Member Invitation', async () => {
      const inviteData = {
        email: '<EMAIL>',
        role: 'editor',
        permissions: [
          'links.create',
          'links.edit',
          'analytics.view',
          'campaigns.view'
        ]
      };
      
      const response = await request(apps.dashboard)
        .post('/api/team/invite')
        .set('Authorization', `Bearer ${authToken}`)
        .send(inviteData)
        .expect(201);
      
      expect(response.body).toHaveProperty('inviteId');
      expect(response.body.email).toBe(inviteData.email);
      expect(response.body.status).toBe('pending');
    });
    
    test('17. Billing Information', async () => {
      const billingData = {
        plan: 'professional',
        billingCycle: 'monthly',
        paymentMethod: {
          type: 'card',
          last4: '4242',
          brand: 'visa'
        }
      };
      
      const response = await request(apps.dashboard)
        .put('/api/billing')
        .set('Authorization', `Bearer ${authToken}`)
        .send(billingData)
        .expect(200);
      
      expect(response.body.plan).toBe(billingData.plan);
      expect(response.body.billingCycle).toBe(billingData.billingCycle);
    });
    
    test('18. Account Cleanup (Soft Delete)', async () => {
      // Test account deactivation (not permanent deletion)
      const response = await request(apps.dashboard)
        .post('/api/account/deactivate')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          reason: 'testing',
          feedback: 'This is a test account'
        })
        .expect(200);
      
      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('deactivated');
    });
  });
  
  // Helper function to cleanup all created resources
  async function cleanupAllResources() {
    console.log('🧹 Cleaning up E2E test resources...');
    
    try {
      // Delete links
      for (const linkId of createdResources.links) {
        try {
          await request(apps.dashboard)
            .delete(`/api/links/${linkId}`)
            .set('Authorization', `Bearer ${authToken}`);
        } catch (error) {
          console.warn(`Failed to delete link ${linkId}:`, error.message);
        }
      }
      
      // Delete campaigns
      for (const campaignId of createdResources.campaigns) {
        try {
          await request(apps.dashboard)
            .delete(`/api/campaigns/${campaignId}`)
            .set('Authorization', `Bearer ${authToken}`);
        } catch (error) {
          console.warn(`Failed to delete campaign ${campaignId}:`, error.message);
        }
      }
      
      // Delete integrations
      for (const integrationId of createdResources.integrations) {
        try {
          await request(apps.integration)
            .delete(`/api/integrations/${integrationId}`);
        } catch (error) {
          console.warn(`Failed to delete integration ${integrationId}:`, error.message);
        }
      }
      
      console.log('✅ E2E test resources cleaned up');
    } catch (error) {
      console.error('❌ Error during E2E cleanup:', error);
    }
  }
});