// Global Jest setup file
require('dotenv').config({ path: '.env.test' });

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.LOG_LEVEL = 'error'; // Reduce log noise in tests
process.env.DB_NAME = 'ecommerce_analytics_test';
process.env.REDIS_DB = '15'; // Use different Redis DB for tests

// Global test timeout
jest.setTimeout(30000);

// Global console error capture
const originalError = console.error;
const originalWarn = console.warn;

// Capture console errors and warnings during tests
global.capturedErrors = [];
global.capturedWarnings = [];

console.error = (...args) => {
  global.capturedErrors.push(args);
  if (process.env.TEST_VERBOSE === 'true') {
    originalError.apply(console, args);
  }
};

console.warn = (...args) => {
  global.capturedWarnings.push(args);
  if (process.env.TEST_VERBOSE === 'true') {
    originalWarn.apply(console, args);
  }
};

// Global test utilities
global.testUtils = {
  // Async timeout utility
  timeout: (ms) => new Promise(resolve => setTimeout(resolve, ms)),
  
  // Random test data generators
  randomString: (length = 10) => {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  },
  
  randomEmail: () => {
    return `test.${global.testUtils.randomString(8)}@example.com`;
  },
  
  randomUrl: () => {
    return `https://example.com/${global.testUtils.randomString(8)}`;
  },
  
  randomPort: () => {
    return Math.floor(Math.random() * (9999 - 3000 + 1)) + 3000;
  },
  
  // Date utilities
  dateInPast: (days = 1) => {
    const date = new Date();
    date.setDate(date.getDate() - days);
    return date;
  },
  
  dateInFuture: (days = 1) => {
    const date = new Date();
    date.setDate(date.getDate() + days);
    return date;
  },
  
  // Mock data generators
  mockUser: (overrides = {}) => ({
    id: global.testUtils.randomString(8),
    email: global.testUtils.randomEmail(),
    password: 'test_password_123',
    firstName: 'Test',
    lastName: 'User',
    role: 'user',
    tenantId: global.testUtils.randomString(8),
    createdAt: new Date(),
    updatedAt: new Date(),
    ...overrides
  }),
  
  mockLink: (overrides = {}) => ({
    id: global.testUtils.randomString(8),
    shortCode: global.testUtils.randomString(6),
    originalUrl: global.testUtils.randomUrl(),
    userId: global.testUtils.randomString(8),
    tenantId: global.testUtils.randomString(8),
    campaignId: global.testUtils.randomString(8),
    title: 'Test Link',
    description: 'Test description',
    tags: ['test'],
    isActive: true,
    expiresAt: null,
    createdAt: new Date(),
    updatedAt: new Date(),
    ...overrides
  }),
  
  mockClick: (overrides = {}) => ({
    id: global.testUtils.randomString(8),
    linkId: global.testUtils.randomString(8),
    ipAddress: '127.0.0.1',
    userAgent: 'Mozilla/5.0 (Test Browser)',
    referer: 'https://example.com',
    country: 'US',
    region: 'CA',
    city: 'San Francisco',
    device: 'desktop',
    browser: 'chrome',
    os: 'windows',
    timestamp: new Date(),
    ...overrides
  }),
  
  mockOrder: (overrides = {}) => ({
    id: global.testUtils.randomString(8),
    externalId: global.testUtils.randomString(10),
    platform: 'shopify',
    tenantId: global.testUtils.randomString(8),
    customerId: global.testUtils.randomString(8),
    customerEmail: global.testUtils.randomEmail(),
    total: 99.99,
    currency: 'USD',
    status: 'completed',
    items: [],
    createdAt: new Date(),
    updatedAt: new Date(),
    ...overrides
  }),
  
  // Validation utilities
  isValidEmail: (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },
  
  isValidUrl: (url) => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  },
  
  isValidUUID: (uuid) => {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
  },
  
  // HTTP utilities
  expectHttpStatus: (response, expectedStatus) => {
    expect(response.status).toBe(expectedStatus);
  },
  
  expectValidationError: (response, field) => {
    expect(response.status).toBe(400);
    expect(response.body.error).toContain('validation');
    if (field) {
      expect(response.body.details).toContain(field);
    }
  },
  
  expectNotFound: (response) => {
    expect(response.status).toBe(404);
    expect(response.body.error).toContain('not found');
  },
  
  expectUnauthorized: (response) => {
    expect(response.status).toBe(401);
    expect(response.body.error).toContain('unauthorized');
  },
  
  expectForbidden: (response) => {
    expect(response.status).toBe(403);
    expect(response.body.error).toContain('forbidden');
  }
};

// Global test matchers
expect.extend({
  toBeValidEmail(received) {
    const pass = global.testUtils.isValidEmail(received);
    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid email`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid email`,
        pass: false,
      };
    }
  },
  
  toBeValidUrl(received) {
    const pass = global.testUtils.isValidUrl(received);
    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid URL`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid URL`,
        pass: false,
      };
    }
  },
  
  toBeValidUUID(received) {
    const pass = global.testUtils.isValidUUID(received);
    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid UUID`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid UUID`,
        pass: false,
      };
    }
  },
  
  toBeWithinTimeRange(received, start, end) {
    const receivedTime = new Date(received).getTime();
    const startTime = new Date(start).getTime();
    const endTime = new Date(end).getTime();
    const pass = receivedTime >= startTime && receivedTime <= endTime;
    
    if (pass) {
      return {
        message: () => `expected ${received} not to be within time range ${start} - ${end}`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be within time range ${start} - ${end}`,
        pass: false,
      };
    }
  },
  
  toHaveValidPagination(received) {
    const hasPage = typeof received.page === 'number' && received.page > 0;
    const hasLimit = typeof received.limit === 'number' && received.limit > 0;
    const hasTotal = typeof received.total === 'number' && received.total >= 0;
    const hasPages = typeof received.pages === 'number' && received.pages >= 0;
    
    const pass = hasPage && hasLimit && hasTotal && hasPages;
    
    if (pass) {
      return {
        message: () => `expected pagination object not to be valid`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected pagination object to have valid page, limit, total, and pages properties`,
        pass: false,
      };
    }
  }
});

// Global error handling for unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  // Don't exit the process in tests, just log the error
});

// Global error handling for uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  // Don't exit the process in tests, just log the error
});

// Clean up console overrides after each test
afterEach(() => {
  global.capturedErrors = [];
  global.capturedWarnings = [];
});

// Restore original console methods after all tests
afterAll(() => {
  console.error = originalError;
  console.warn = originalWarn;
});