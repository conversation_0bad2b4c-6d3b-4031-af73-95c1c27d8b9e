const { Pool } = require('pg');
const Redis = require('redis');
const fs = require('fs');
const path = require('path');

module.exports = async () => {
  console.log('🧹 Cleaning up global test environment...');
  
  try {
    // Clean up test database
    await cleanupTestDatabase();
    
    // Clean up test Redis
    await cleanupTestRedis();
    
    // Clean up test files
    await cleanupTestFiles();
    
    console.log('✅ Global test environment cleanup completed');
    
  } catch (error) {
    console.error('❌ Failed to cleanup global test environment:', error);
    // Don't throw error during cleanup to avoid masking test failures
  }
};

async function cleanupTestDatabase() {
  if (process.env.KEEP_TEST_DB === 'true') {
    console.log('🔒 Keeping test database (KEEP_TEST_DB=true)');
    return;
  }

  const adminPool = new Pool({
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    database: 'postgres',
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'password'
  });

  try {
    console.log('🗑️ Cleaning up test database...');
    
    // Terminate any active connections to the test database
    await adminPool.query(`
      SELECT pg_terminate_backend(pid)
      FROM pg_stat_activity
      WHERE datname = $1 AND pid <> pg_backend_pid()
    `, [process.env.DB_NAME]);
    
    // Drop test database
    await adminPool.query(`DROP DATABASE IF EXISTS ${process.env.DB_NAME}`);
    
    console.log('✅ Test database cleaned up');
  } catch (error) {
    console.error('❌ Error cleaning up test database:', error);
  } finally {
    await adminPool.end();
  }
}

async function cleanupTestRedis() {
  if (process.env.KEEP_TEST_REDIS === 'true') {
    console.log('🔒 Keeping test Redis data (KEEP_TEST_REDIS=true)');
    return;
  }

  const redis = Redis.createClient({
    host: process.env.REDIS_HOST || 'localhost',
    port: process.env.REDIS_PORT || 6379,
    password: process.env.REDIS_PASSWORD || undefined,
    db: parseInt(process.env.REDIS_DB)
  });

  try {
    console.log('🗑️ Cleaning up test Redis...');
    
    await redis.connect();
    await redis.flushDb();
    
    console.log('✅ Test Redis cleaned up');
  } catch (error) {
    console.error('❌ Error cleaning up test Redis:', error);
  } finally {
    try {
      await redis.disconnect();
    } catch (disconnectError) {
      // Ignore disconnect errors during cleanup
    }
  }
}

async function cleanupTestFiles() {
  if (process.env.KEEP_TEST_FILES === 'true') {
    console.log('🔒 Keeping test files (KEEP_TEST_FILES=true)');
    return;
  }

  console.log('🗑️ Cleaning up test files...');
  
  const cleanupDirs = [
    'testing/temp',
    'testing/logs'
  ];

  for (const dir of cleanupDirs) {
    const fullPath = path.join(process.cwd(), dir);
    if (fs.existsSync(fullPath)) {
      try {
        // Remove all files in the directory but keep the directory
        const files = fs.readdirSync(fullPath);
        for (const file of files) {
          const filePath = path.join(fullPath, file);
          const stat = fs.statSync(filePath);
          
          if (stat.isDirectory()) {
            fs.rmSync(filePath, { recursive: true, force: true });
          } else {
            fs.unlinkSync(filePath);
          }
        }
        console.log(`  Cleaned: ${dir}`);
      } catch (error) {
        console.error(`❌ Error cleaning ${dir}:`, error.message);
      }
    }
  }

  // Clean up coverage reports if not in CI
  if (!process.env.CI) {
    const coverageDir = path.join(process.cwd(), 'testing/coverage');
    if (fs.existsSync(coverageDir)) {
      try {
        fs.rmSync(coverageDir, { recursive: true, force: true });
        fs.mkdirSync(coverageDir, { recursive: true });
        console.log('  Cleaned: testing/coverage');
      } catch (error) {
        console.error('❌ Error cleaning coverage:', error.message);
      }
    }
  }

  console.log('✅ Test files cleaned up');
}

// Handle process signals for graceful cleanup
process.on('SIGTERM', async () => {
  console.log('🔄 Received SIGTERM, cleaning up...');
  await module.exports();
  process.exit(0);
});

process.on('SIGINT', async () => {
  console.log('🔄 Received SIGINT, cleaning up...');
  await module.exports();
  process.exit(0);
});