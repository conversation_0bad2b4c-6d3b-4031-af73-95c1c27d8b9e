const { execSync } = require('child_process');
const { Pool } = require('pg');
const Redis = require('redis');
const path = require('path');
const fs = require('fs');

module.exports = async () => {
  console.log('🧪 Setting up global test environment...');
  
  try {
    // Set test environment variables
    process.env.NODE_ENV = 'test';
    process.env.DB_NAME = 'ecommerce_analytics_test';
    process.env.REDIS_DB = '15';
    process.env.LOG_LEVEL = 'error';
    
    // Create test database if it doesn't exist
    await setupTestDatabase();
    
    // Run database migrations for test database
    await runTestMigrations();
    
    // Setup test Redis database
    await setupTestRedis();
    
    // Create test data directories
    await createTestDirectories();
    
    console.log('✅ Global test environment setup completed');
    
  } catch (error) {
    console.error('❌ Failed to setup global test environment:', error);
    throw error;
  }
};

async function setupTestDatabase() {
  const adminPool = new Pool({
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    database: 'postgres',
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'password'
  });

  try {
    // Check if test database exists
    const checkDbQuery = `
      SELECT 1 FROM pg_database WHERE datname = $1
    `;
    const result = await adminPool.query(checkDbQuery, [process.env.DB_NAME]);
    
    if (result.rows.length === 0) {
      // Create test database
      console.log('📊 Creating test database...');
      await adminPool.query(`CREATE DATABASE ${process.env.DB_NAME}`);
      console.log('✅ Test database created');
    } else {
      console.log('📊 Test database already exists');
    }
  } catch (error) {
    console.error('❌ Error setting up test database:', error);
    throw error;
  } finally {
    await adminPool.end();
  }
}

async function runTestMigrations() {
  console.log('🔄 Running test database migrations...');
  
  const testPool = new Pool({
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    database: process.env.DB_NAME,
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'password'
  });

  try {
    // Drop all existing tables (clean slate for tests)
    await testPool.query(`
      DROP SCHEMA public CASCADE;
      CREATE SCHEMA public;
      GRANT ALL ON SCHEMA public TO ${process.env.DB_USER || 'postgres'};
      GRANT ALL ON SCHEMA public TO public;
    `);

    // Run all migration files
    const migrationDirs = [
      'database/migrations',
      'services/analytics/migrations',
      'services/error-tracking/migrations'
    ];

    for (const migrationDir of migrationDirs) {
      const fullPath = path.join(process.cwd(), migrationDir);
      if (fs.existsSync(fullPath)) {
        const files = fs.readdirSync(fullPath)
          .filter(file => file.endsWith('.sql'))
          .sort();

        for (const file of files) {
          console.log(`  Running migration: ${file}`);
          const migrationSQL = fs.readFileSync(path.join(fullPath, file), 'utf8');
          await testPool.query(migrationSQL);
        }
      }
    }

    console.log('✅ Test database migrations completed');
  } catch (error) {
    console.error('❌ Error running test migrations:', error);
    throw error;
  } finally {
    await testPool.end();
  }
}

async function setupTestRedis() {
  console.log('📦 Setting up test Redis database...');
  
  const redis = Redis.createClient({
    host: process.env.REDIS_HOST || 'localhost',
    port: process.env.REDIS_PORT || 6379,
    password: process.env.REDIS_PASSWORD || undefined,
    db: parseInt(process.env.REDIS_DB)
  });

  try {
    await redis.connect();
    
    // Clear test Redis database
    await redis.flushDb();
    
    console.log('✅ Test Redis database setup completed');
  } catch (error) {
    console.error('❌ Error setting up test Redis:', error);
    throw error;
  } finally {
    await redis.disconnect();
  }
}

async function createTestDirectories() {
  console.log('📁 Creating test directories...');
  
  const testDirs = [
    'testing/temp',
    'testing/fixtures',
    'testing/reports',
    'testing/coverage',
    'testing/logs'
  ];

  for (const dir of testDirs) {
    const fullPath = path.join(process.cwd(), dir);
    if (!fs.existsSync(fullPath)) {
      fs.mkdirSync(fullPath, { recursive: true });
      console.log(`  Created: ${dir}`);
    }
  }

  // Create test environment file if it doesn't exist
  const testEnvPath = path.join(process.cwd(), '.env.test');
  if (!fs.existsSync(testEnvPath)) {
    const testEnvContent = `
# Test Environment Configuration
NODE_ENV=test
LOG_LEVEL=error

# Test Database
DB_HOST=localhost
DB_PORT=5432
DB_NAME=ecommerce_analytics_test
DB_USER=postgres
DB_PASSWORD=password

# Test Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=15
REDIS_PASSWORD=

# Test JWT
JWT_SECRET=test_jwt_secret_key_for_testing_only
JWT_EXPIRES_IN=1h

# Test Rate Limiting (more permissive for tests)
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX=1000

# Test CORS
CORS_ORIGINS=http://localhost:3000,http://localhost:5173

# Test Ports (use different ports to avoid conflicts)
DASHBOARD_PORT=3010
ANALYTICS_PORT=3012
INTEGRATION_PORT=3011
LINK_TRACKING_PORT=8081
ERROR_TRACKING_PORT=3015
ADMIN_PORT=3016

# Test External APIs (use mocks)
SHOPIFY_API_URL=http://localhost:3020/mock/shopify
WOOCOMMERCE_API_URL=http://localhost:3020/mock/woocommerce
AMAZON_API_URL=http://localhost:3020/mock/amazon
`;
    fs.writeFileSync(testEnvPath, testEnvContent.trim());
    console.log('  Created: .env.test');
  }

  console.log('✅ Test directories created');
}