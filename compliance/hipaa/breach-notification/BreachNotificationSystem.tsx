import React, { useState } from 'react';

// Mock data for breaches. In a real application, this would come from an API.
const initialBreaches = [
  { id: 1, date: '2025-06-28', description: 'Laptop stolen from employee\'s car', individualsAffected: 1, reportedToHHS: 'Yes' },
  { id: 2, date: '2025-06-20', description: 'Email sent to wrong recipient', individualsAffected: 1, reportedToHHS: 'No' },
];

const BreachNotificationSystem = () => {
  const [breaches, setBreaches] = useState(initialBreaches);

  return (
    <div style={{ fontFamily: 'Arial, sans-serif', padding: '20px' }}>
      <h1 style={{ borderBottom: '2px solid #eee', paddingBottom: '10px' }}>Breach Notification System</h1>

      <div style={{ marginTop: '20px' }}>
        <table style={{ width: '100%', borderCollapse: 'collapse' }}>
          <thead>
            <tr style={{ borderBottom: '1px solid #ddd' }}>
              <th style={{ padding: '10px', textAlign: 'left' }}>ID</th>
              <th style={{ padding: '10px', textAlign: 'left' }}>Date</th>
              <th style={{ padding: '10px', textAlign: 'left' }}>Description</th>
              <th style={{ padding: '10px', textAlign: 'left' }}>Individuals Affected</th>
              <th style={{ padding: '10px', textAlign: 'left' }}>Reported to HHS</th>
            </tr>
          </thead>
          <tbody>
            {breaches.map(breach => (
              <tr key={breach.id} style={{ borderBottom: '1px solid #eee' }}>
                <td style={{ padding: '10px' }}>{breach.id}</td>
                <td style={{ padding: '10px' }}>{breach.date}</td>
                <td style={{ padding: '10px' }}>{breach.description}</td>
                <td style={{ padding: '10px' }}>{breach.individualsAffected}</td>
                <td style={{ padding: '10px' }}>{breach.reportedToHHS}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default BreachNotificationSystem;
