
import React from 'react';

const HIPAATraining = () => {
  return (
    <div style={{ fontFamily: 'Arial, sans-serif', padding: '20px' }}>
      <h1 style={{ borderBottom: '2px solid #eee', paddingBottom: '10px' }}>HIPAA Training Module</h1>

      <div style={{ marginTop: '20px' }}>
        <h2>Introduction to HIPAA</h2>
        <p>The Health Insurance Portability and Accountability Act (HIPAA) is a federal law that protects sensitive patient health information from being disclosed without the patient's consent or knowledge.</p>

        <h2>Key Concepts</h2>
        <ul>
          <li><strong>Protected Health Information (PHI):</strong> Any information that can be used to identify a patient and relates to their past, present, or future physical or mental health or condition.</li>
          <li><strong>Privacy Rule:</strong> Governs the use and disclosure of PHI.</li>
          <li><strong>Security Rule:</strong> Sets standards for protecting electronic PHI (ePHI).</li>
          <li><strong>Breach Notification Rule:</strong> Requires notification to individuals and the government in the event of a breach of unsecured PHI.</li>
        </ul>

        <h2>Your Responsibilities</h2>
        <ul>
          <li>Complete HIPAA training annually.</li>
          <li>Only access PHI when necessary for your job duties.</li>
          <li>Keep PHI confidential and secure.</li>
          <li>Report any suspected HIPAA violations immediately.</li>
        </ul>
      </div>
    </div>
  );
};

export default HIPAATraining;
