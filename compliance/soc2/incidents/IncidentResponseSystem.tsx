
import React, { useState } from 'react';

// Mock data for incidents. In a real application, this would come from an API.
const initialIncidents = [
  { id: 1, title: 'Unauthorized access attempt', status: 'Open', severity: 'High', assignee: 'Alice' },
  { id: 2, title: 'Malware detected on server', status: 'In Progress', severity: 'Critical', assignee: 'Bob' },
  { id: 3, title: 'Phishing email reported', status: 'Closed', severity: 'Medium', assignee: 'Charlie' },
];

const IncidentResponseSystem = () => {
  const [incidents, setIncidents] = useState(initialIncidents);

  return (
    <div style={{ fontFamily: 'Arial, sans-serif', padding: '20px' }}>
      <h1 style={{ borderBottom: '2px solid #eee', paddingBottom: '10px' }}>Incident Response System</h1>

      <div style={{ marginTop: '20px' }}>
        <table style={{ width: '100%', borderCollapse: 'collapse' }}>
          <thead>
            <tr style={{ borderBottom: '1px solid #ddd' }}>
              <th style={{ padding: '10px', textAlign: 'left' }}>ID</th>
              <th style={{ padding: '10px', textAlign: 'left' }}>Title</th>
              <th style={{ padding: '10px', textAlign: 'left' }}>Status</th>
              <th style={{ padding: '10px', textAlign: 'left' }}>Severity</th>
              <th style={{ padding: '10px', textAlign: 'left' }}>Assignee</th>
            </tr>
          </thead>
          <tbody>
            {incidents.map(incident => (
              <tr key={incident.id} style={{ borderBottom: '1px solid #eee' }}>
                <td style={{ padding: '10px' }}>{incident.id}</td>
                <td style={{ padding: '10px' }}>{incident.title}</td>
                <td style={{ padding: '10px' }}>{incident.status}</td>
                <td style={{ padding: '10px' }}>{incident.severity}</td>
                <td style={{ padding: '10px' }}>{incident.assignee}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default IncidentResponseSystem;
