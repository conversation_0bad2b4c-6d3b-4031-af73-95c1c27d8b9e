
import React from 'react';

// Mock data for the dashboard. In a real application, this would come from an API.
const complianceData = {
  overallStatus: 'Compliant',
  controlStatus: {
    compliant: 120,
    nonCompliant: 5,
    inProgress: 10,
  },
  evidenceCollection: {
    collected: 135,
    pending: 10,
  },
  issues: [
    { id: 1, description: 'Firewall rules not reviewed in Q2', status: 'Remediation in Progress' },
    { id: 2, description: 'Access control policy needs updating', status: 'Open' },
  ],
};

const ComplianceDashboard = () => {
  return (
    <div style={{ fontFamily: 'Arial, sans-serif', padding: '20px' }}>
      <h1 style={{ borderBottom: '2px solid #eee', paddingBottom: '10px' }}>SOC 2 Compliance Dashboard</h1>

      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '20px', marginTop: '20px' }}>
        <div style={{ border: '1px solid #ddd', padding: '20px', borderRadius: '5px' }}>
          <h2>Overall Compliance Status</h2>
          <p style={{ fontSize: '2em', color: complianceData.overallStatus === 'Compliant' ? 'green' : 'red' }}>
            {complianceData.overallStatus}
          </p>
        </div>

        <div style={{ border: '1px solid #ddd', padding: '20px', borderRadius: '5px' }}>
          <h2>Control Status</h2>
          <p>Compliant: {complianceData.controlStatus.compliant}</p>
          <p>Non-Compliant: {complianceData.controlStatus.nonCompliant}</p>
          <p>In Progress: {complianceData.controlStatus.inProgress}</p>
        </div>

        <div style={{ border: '1px solid #ddd', padding: '20px', borderRadius: '5px' }}>
          <h2>Evidence Collection</h2>
          <p>Collected: {complianceData.evidenceCollection.collected}</p>
          <p>Pending: {complianceData.evidenceCollection.pending}</p>
        </div>
      </div>

      <div style={{ marginTop: '40px' }}>
        <h2>Issues and Remediation</h2>
        <table style={{ width: '100%', borderCollapse: 'collapse' }}>
          <thead>
            <tr style={{ borderBottom: '1px solid #ddd' }}>
              <th style={{ padding: '10px', textAlign: 'left' }}>ID</th>
              <th style={{ padding: '10px', textAlign: 'left' }}>Description</th>
              <th style={{ padding: '10px', textAlign: 'left' }}>Status</th>
            </tr>
          </thead>
          <tbody>
            {complianceData.issues.map(issue => (
              <tr key={issue.id} style={{ borderBottom: '1px solid #eee' }}>
                <td style={{ padding: '10px' }}>{issue.id}</td>
                <td style={{ padding: '10px' }}>{issue.description}</td>
                <td style={{ padding: '10px' }}>{issue.status}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default ComplianceDashboard;
