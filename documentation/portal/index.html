<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>E-commerce Analytics API Documentation</title>
    <meta name="description" content="Comprehensive API documentation for the E-commerce Analytics platform with interactive examples and code samples.">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Styles -->
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary-color: #64748b;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --background: #ffffff;
            --surface: #f8fafc;
            --border: #e2e8f0;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --text-muted: #94a3b8;
            --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            background-color: var(--background);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        .header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            padding: 2rem 0;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }

        .header-content {
            position: relative;
            z-index: 1;
            text-align: center;
        }

        .header h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .header p {
            font-size: 1.25rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }

        .version-badge {
            display: inline-block;
            background: rgba(255, 255, 255, 0.2);
            padding: 0.5rem 1rem;
            border-radius: 2rem;
            font-size: 0.875rem;
            margin-top: 1rem;
        }

        /* Navigation */
        .nav {
            background: var(--surface);
            border-bottom: 1px solid var(--border);
            position: sticky;
            top: 0;
            z-index: 100;
            backdrop-filter: blur(10px);
        }

        .nav-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem 0;
        }

        .nav-links {
            display: flex;
            gap: 2rem;
            list-style: none;
        }

        .nav-links a {
            text-decoration: none;
            color: var(--text-secondary);
            font-weight: 500;
            transition: color 0.2s;
        }

        .nav-links a:hover,
        .nav-links a.active {
            color: var(--primary-color);
        }

        .nav-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 0.375rem;
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.2s;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-dark);
        }

        .btn-outline {
            background: transparent;
            color: var(--primary-color);
            border: 1px solid var(--primary-color);
        }

        .btn-outline:hover {
            background: var(--primary-color);
            color: white;
        }

        /* Main Content */
        .main {
            padding: 3rem 0;
        }

        .section {
            margin-bottom: 4rem;
        }

        .section-title {
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--text-primary);
        }

        .section-subtitle {
            color: var(--text-secondary);
            margin-bottom: 2rem;
        }

        /* Feature Grid */
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .feature-card {
            background: var(--surface);
            border: 1px solid var(--border);
            border-radius: 0.75rem;
            padding: 2rem;
            transition: all 0.2s;
        }

        .feature-card:hover {
            box-shadow: var(--shadow-lg);
            transform: translateY(-2px);
        }

        .feature-icon {
            width: 3rem;
            height: 3rem;
            background: var(--primary-color);
            border-radius: 0.75rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }

        .feature-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .feature-description {
            color: var(--text-secondary);
            line-height: 1.6;
        }

        /* Quick Start */
        .quick-start {
            background: var(--surface);
            border-radius: 0.75rem;
            padding: 2rem;
            margin-bottom: 3rem;
        }

        .code-block {
            background: #1e293b;
            color: #e2e8f0;
            border-radius: 0.5rem;
            padding: 1rem;
            font-family: 'Fira Code', monospace;
            font-size: 0.875rem;
            overflow-x: auto;
            margin: 1rem 0;
        }

        .code-block pre {
            margin: 0;
        }

        /* API Endpoints */
        .endpoint-list {
            display: grid;
            gap: 1rem;
        }

        .endpoint-item {
            background: var(--surface);
            border: 1px solid var(--border);
            border-radius: 0.5rem;
            overflow: hidden;
            transition: all 0.2s;
        }

        .endpoint-item:hover {
            box-shadow: var(--shadow);
        }

        .endpoint-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            cursor: pointer;
        }

        .endpoint-method {
            padding: 0.25rem 0.75rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .method-get { background: #dbeafe; color: #1e40af; }
        .method-post { background: #dcfce7; color: #15803d; }
        .method-put { background: #fef3c7; color: #92400e; }
        .method-delete { background: #fee2e2; color: #dc2626; }

        .endpoint-path {
            font-family: 'Fira Code', monospace;
            font-weight: 500;
            flex-grow: 1;
        }

        .endpoint-description {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        /* Documentation Links */
        .doc-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
        }

        .doc-link {
            display: block;
            background: var(--surface);
            border: 1px solid var(--border);
            border-radius: 0.5rem;
            padding: 1.5rem;
            text-decoration: none;
            color: inherit;
            transition: all 0.2s;
        }

        .doc-link:hover {
            box-shadow: var(--shadow);
            transform: translateY(-1px);
        }

        .doc-link-icon {
            font-size: 2rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        .doc-link-title {
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .doc-link-description {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        /* Footer */
        .footer {
            background: var(--text-primary);
            color: white;
            padding: 3rem 0 2rem;
            margin-top: 4rem;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .footer-section h3 {
            margin-bottom: 1rem;
        }

        .footer-section ul {
            list-style: none;
        }

        .footer-section a {
            color: #cbd5e1;
            text-decoration: none;
            line-height: 2;
            transition: color 0.2s;
        }

        .footer-section a:hover {
            color: white;
        }

        .footer-bottom {
            border-top: 1px solid #374151;
            padding-top: 2rem;
            text-align: center;
            color: #9ca3af;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }

            .nav-content {
                flex-direction: column;
                gap: 1rem;
            }

            .nav-links {
                gap: 1rem;
            }

            .container {
                padding: 0 15px;
            }
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }

        /* Loading spinner */
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content fade-in-up">
                <h1>E-commerce Analytics API</h1>
                <p>Comprehensive API documentation for enterprise-grade e-commerce analytics platform with real-time insights, advanced security, and powerful integrations.</p>
                <span class="version-badge">v1.0.0</span>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="nav">
        <div class="container">
            <div class="nav-content">
                <ul class="nav-links">
                    <li><a href="#overview" class="active">Overview</a></li>
                    <li><a href="#quickstart">Quick Start</a></li>
                    <li><a href="#endpoints">API Reference</a></li>
                    <li><a href="#examples">Examples</a></li>
                    <li><a href="#sdks">SDKs</a></li>
                </ul>
                <div class="nav-actions">
                    <a href="/api-docs" class="btn btn-outline">
                        <i class="fas fa-book"></i>
                        Interactive Docs
                    </a>
                    <a href="https://github.com/company/ecommerce-analytics-api" class="btn btn-primary">
                        <i class="fab fa-github"></i>
                        GitHub
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <!-- Overview Section -->
            <section id="overview" class="section fade-in-up">
                <h2 class="section-title">Platform Overview</h2>
                <p class="section-subtitle">
                    Our e-commerce analytics API provides enterprise-grade data processing, real-time insights, 
                    and comprehensive security features for modern e-commerce platforms.
                </p>

                <div class="feature-grid">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h3 class="feature-title">Real-time Analytics</h3>
                        <p class="feature-description">
                            Process and analyze e-commerce data in real-time with sub-second latency. 
                            Track user behavior, sales metrics, and conversion funnels instantly.
                        </p>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h3 class="feature-title">Enterprise Security</h3>
                        <p class="feature-description">
                            Advanced security features including OAuth2, API keys, MFA, RBAC, 
                            comprehensive audit logging, and compliance with SOX, GDPR, PCI DSS.
                        </p>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-plug"></i>
                        </div>
                        <h3 class="feature-title">Easy Integration</h3>
                        <p class="feature-description">
                            RESTful API with comprehensive SDKs, webhooks, and pre-built integrations 
                            for popular e-commerce platforms like Shopify, WooCommerce, and Magento.
                        </p>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-tachometer-alt"></i>
                        </div>
                        <h3 class="feature-title">High Performance</h3>
                        <p class="feature-description">
                            Built for scale with intelligent caching, load balancing, and optimized 
                            database queries. Handle millions of events per day with consistent performance.
                        </p>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <h3 class="feature-title">Flexible Dashboards</h3>
                        <p class="feature-description">
                            Create custom dashboards and reports with our powerful visualization engine. 
                            Drag-and-drop interface with real-time data updates.
                        </p>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <h3 class="feature-title">Team Collaboration</h3>
                        <p class="feature-description">
                            Role-based access control, team workspaces, sharing capabilities, 
                            and audit trails for enterprise collaboration and governance.
                        </p>
                    </div>
                </div>
            </section>

            <!-- Quick Start Section -->
            <section id="quickstart" class="section fade-in-up">
                <h2 class="section-title">Quick Start Guide</h2>
                <p class="section-subtitle">
                    Get started with the E-commerce Analytics API in minutes. Follow these simple steps to make your first API call.
                </p>

                <div class="quick-start">
                    <h3>1. Get Your API Key</h3>
                    <p>Sign up for an account and generate your API key from the dashboard:</p>
                    <div class="code-block">
                        <pre>curl -X POST https://api.ecommerce-analytics.com/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SecurePassword123!",
    "firstName": "John",
    "lastName": "Doe",
    "company": "Your Company"
  }'</pre>
                    </div>

                    <h3>2. Authenticate Your Requests</h3>
                    <p>Use your API key in the request headers:</p>
                    <div class="code-block">
                        <pre>curl -X GET https://api.ecommerce-analytics.com/api/analytics/events \
  -H "X-API-Key: ek_your_api_key_here" \
  -H "Content-Type: application/json"</pre>
                    </div>

                    <h3>3. Send Your First Event</h3>
                    <p>Track user interactions by sending analytics events:</p>
                    <div class="code-block">
                        <pre>curl -X POST https://api.ecommerce-analytics.com/api/analytics/events \
  -H "X-API-Key: ek_your_api_key_here" \
  -H "Content-Type: application/json" \
  -d '{
    "eventType": "page_view",
    "userId": "user_12345",
    "sessionId": "session_67890",
    "properties": {
      "page": "/product/widget-123",
      "category": "electronics",
      "price": 99.99
    }
  }'</pre>
                    </div>

                    <h3>4. Retrieve Analytics Data</h3>
                    <p>Query your analytics data and generate insights:</p>
                    <div class="code-block">
                        <pre>curl -X GET "https://api.ecommerce-analytics.com/api/analytics/metrics?timeRange=7d&metrics=page_views,unique_visitors,conversion_rate" \
  -H "X-API-Key: ek_your_api_key_here"</pre>
                    </div>
                </div>
            </section>

            <!-- API Endpoints Section -->
            <section id="endpoints" class="section fade-in-up">
                <h2 class="section-title">API Reference</h2>
                <p class="section-subtitle">
                    Explore our comprehensive API endpoints organized by functionality.
                </p>

                <div class="endpoint-list">
                    <div class="endpoint-item">
                        <div class="endpoint-header">
                            <span class="endpoint-method method-post">POST</span>
                            <span class="endpoint-path">/api/auth/login</span>
                            <span class="endpoint-description">Authenticate user and get access token</span>
                        </div>
                    </div>

                    <div class="endpoint-item">
                        <div class="endpoint-header">
                            <span class="endpoint-method method-get">GET</span>
                            <span class="endpoint-path">/api/analytics/events</span>
                            <span class="endpoint-description">Retrieve analytics events with filtering</span>
                        </div>
                    </div>

                    <div class="endpoint-item">
                        <div class="endpoint-header">
                            <span class="endpoint-method method-post">POST</span>
                            <span class="endpoint-path">/api/analytics/events</span>
                            <span class="endpoint-description">Create new analytics event</span>
                        </div>
                    </div>

                    <div class="endpoint-item">
                        <div class="endpoint-header">
                            <span class="endpoint-method method-get">GET</span>
                            <span class="endpoint-path">/api/analytics/metrics</span>
                            <span class="endpoint-description">Get calculated metrics and KPIs</span>
                        </div>
                    </div>

                    <div class="endpoint-item">
                        <div class="endpoint-header">
                            <span class="endpoint-method method-post">POST</span>
                            <span class="endpoint-path">/api/analytics/query</span>
                            <span class="endpoint-description">Execute custom analytics query</span>
                        </div>
                    </div>

                    <div class="endpoint-item">
                        <div class="endpoint-header">
                            <span class="endpoint-method method-get">GET</span>
                            <span class="endpoint-path">/api/dashboards</span>
                            <span class="endpoint-description">List user dashboards</span>
                        </div>
                    </div>

                    <div class="endpoint-item">
                        <div class="endpoint-header">
                            <span class="endpoint-method method-post">POST</span>
                            <span class="endpoint-path">/api/api-keys</span>
                            <span class="endpoint-description">Create new API key</span>
                        </div>
                    </div>

                    <div class="endpoint-item">
                        <div class="endpoint-header">
                            <span class="endpoint-method method-get">GET</span>
                            <span class="endpoint-path">/api/integrations</span>
                            <span class="endpoint-description">List available integrations</span>
                        </div>
                    </div>
                </div>

                <div style="text-align: center; margin-top: 2rem;">
                    <a href="/api-docs" class="btn btn-primary">
                        <i class="fas fa-external-link-alt"></i>
                        View Full API Reference
                    </a>
                </div>
            </section>

            <!-- Documentation Links -->
            <section id="examples" class="section fade-in-up">
                <h2 class="section-title">Documentation & Resources</h2>
                <p class="section-subtitle">
                    Comprehensive guides, tutorials, and resources to help you integrate successfully.
                </p>

                <div class="doc-links">
                    <a href="/api-docs" class="doc-link">
                        <div class="doc-link-icon">
                            <i class="fas fa-book-open"></i>
                        </div>
                        <h3 class="doc-link-title">Interactive API Docs</h3>
                        <p class="doc-link-description">
                            Explore and test API endpoints with our interactive Swagger documentation.
                        </p>
                    </a>

                    <a href="/guides/getting-started" class="doc-link">
                        <div class="doc-link-icon">
                            <i class="fas fa-rocket"></i>
                        </div>
                        <h3 class="doc-link-title">Getting Started Guide</h3>
                        <p class="doc-link-description">
                            Step-by-step tutorial to get up and running with the API in 15 minutes.
                        </p>
                    </a>

                    <a href="/guides/authentication" class="doc-link">
                        <div class="doc-link-icon">
                            <i class="fas fa-key"></i>
                        </div>
                        <h3 class="doc-link-title">Authentication Guide</h3>
                        <p class="doc-link-description">
                            Learn about API keys, OAuth2, JWT tokens, and security best practices.
                        </p>
                    </a>

                    <a href="/sdks" class="doc-link">
                        <div class="doc-link-icon">
                            <i class="fas fa-code"></i>
                        </div>
                        <h3 class="doc-link-title">SDKs & Libraries</h3>
                        <p class="doc-link-description">
                            Official SDKs for JavaScript, Python, PHP, Ruby, Java, and .NET.
                        </p>
                    </a>

                    <a href="/examples" class="doc-link">
                        <div class="doc-link-icon">
                            <i class="fas fa-file-code"></i>
                        </div>
                        <h3 class="doc-link-title">Code Examples</h3>
                        <p class="doc-link-description">
                            Real-world examples and use cases with complete code samples.
                        </p>
                    </a>

                    <a href="/webhooks" class="doc-link">
                        <div class="doc-link-icon">
                            <i class="fas fa-webhook"></i>
                        </div>
                        <h3 class="doc-link-title">Webhooks</h3>
                        <p class="doc-link-description">
                            Real-time notifications and event-driven integrations with webhooks.
                        </p>
                    </a>

                    <a href="/integrations" class="doc-link">
                        <div class="doc-link-icon">
                            <i class="fas fa-puzzle-piece"></i>
                        </div>
                        <h3 class="doc-link-title">Platform Integrations</h3>
                        <p class="doc-link-description">
                            Pre-built integrations for Shopify, WooCommerce, Magento, and more.
                        </p>
                    </a>

                    <a href="/support" class="doc-link">
                        <div class="doc-link-icon">
                            <i class="fas fa-life-ring"></i>
                        </div>
                        <h3 class="doc-link-title">Support & Community</h3>
                        <p class="doc-link-description">
                            Get help from our support team and connect with the developer community.
                        </p>
                    </a>
                </div>
            </section>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>API Resources</h3>
                    <ul>
                        <li><a href="/api-docs">API Reference</a></li>
                        <li><a href="/guides">Developer Guides</a></li>
                        <li><a href="/sdks">SDKs & Libraries</a></li>
                        <li><a href="/examples">Code Examples</a></li>
                        <li><a href="/changelog">Changelog</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h3>Platform</h3>
                    <ul>
                        <li><a href="/dashboard">Dashboard</a></li>
                        <li><a href="/integrations">Integrations</a></li>
                        <li><a href="/webhooks">Webhooks</a></li>
                        <li><a href="/analytics">Analytics</a></li>
                        <li><a href="/security">Security</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h3>Support</h3>
                    <ul>
                        <li><a href="/support">Help Center</a></li>
                        <li><a href="/community">Community</a></li>
                        <li><a href="/status">System Status</a></li>
                        <li><a href="/contact">Contact Us</a></li>
                        <li><a href="/enterprise">Enterprise</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h3>Company</h3>
                    <ul>
                        <li><a href="/about">About</a></li>
                        <li><a href="/blog">Blog</a></li>
                        <li><a href="/careers">Careers</a></li>
                        <li><a href="/privacy">Privacy Policy</a></li>
                        <li><a href="/terms">Terms of Service</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2023 E-commerce Analytics. All rights reserved. Built with ❤️ for developers.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Update active navigation link based on scroll position
        window.addEventListener('scroll', () => {
            const sections = document.querySelectorAll('section[id]');
            const navLinks = document.querySelectorAll('.nav-links a');
            
            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop - 100;
                const sectionHeight = section.clientHeight;
                if (scrollY >= sectionTop && scrollY < sectionTop + sectionHeight) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === `#${current}`) {
                    link.classList.add('active');
                }
            });
        });

        // Add fade-in animation to elements as they come into view
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -100px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('fade-in-up');
                }
            });
        }, observerOptions);

        document.querySelectorAll('.section').forEach(section => {
            observer.observe(section);
        });

        // API status check (optional)
        async function checkAPIStatus() {
            try {
                const response = await fetch('/api/health');
                const status = response.ok ? 'online' : 'offline';
                console.log(`API Status: ${status}`);
            } catch (error) {
                console.log('API Status: offline');
            }
        }

        // Check API status on page load
        checkAPIStatus();
    </script>
</body>
</html>