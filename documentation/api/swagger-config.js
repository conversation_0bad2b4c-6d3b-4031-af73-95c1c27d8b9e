/**
 * Swagger/OpenAPI Configuration for E-commerce Analytics API
 * Comprehensive API documentation with interactive examples
 */

const swaggerJsDoc = require('swagger-jsdoc');
const swaggerUi = require('swagger-ui-express');

const swaggerOptions = {
  definition: {
    openapi: '3.0.3',
    info: {
      title: 'E-commerce Analytics API',
      version: '1.0.0',
      description: `
        Comprehensive e-commerce analytics platform providing real-time insights,
        advanced security features, and enterprise-grade data processing capabilities.
        
        ## Features
        - Real-time analytics and reporting
        - Advanced authentication and authorization
        - OAuth2 and API key support
        - Role-based access control (RBAC)
        - Comprehensive audit logging
        - Enterprise security measures
        
        ## Authentication
        This API supports multiple authentication methods:
        1. **JWT Bearer Tokens** - For user authentication
        2. **API Keys** - For programmatic access
        3. **OAuth2** - For third-party integrations
        
        ## Rate Limiting
        API requests are rate-limited based on authentication method:
        - Authenticated users: 1000 requests per 15 minutes
        - API keys: Based on key configuration
        - OAuth2: Based on client configuration
        
        ## Error Handling
        The API uses conventional HTTP response codes and returns detailed error information
        in a consistent format.
      `,
      termsOfService: 'https://api.ecommerce-analytics.com/terms',
      contact: {
        name: 'API Support',
        url: 'https://ecommerce-analytics.com/support',
        email: '<EMAIL>'
      },
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT'
      }
    },
    servers: [
      {
        url: 'https://api.ecommerce-analytics.com',
        description: 'Production server'
      },
      {
        url: 'https://staging-api.ecommerce-analytics.com',
        description: 'Staging server'
      },
      {
        url: 'http://localhost:3000',
        description: 'Development server'
      }
    ],
    components: {
      securitySchemes: {
        BearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          description: 'JWT token obtained from login endpoint'
        },
        ApiKeyAuth: {
          type: 'apiKey',
          in: 'header',
          name: 'X-API-Key',
          description: 'API key for programmatic access'
        },
        OAuth2: {
          type: 'oauth2',
          flows: {
            authorizationCode: {
              authorizationUrl: '/oauth/authorize',
              tokenUrl: '/oauth/token',
              scopes: {
                'read': 'Read access to resources',
                'write': 'Write access to resources',
                'analytics': 'Access to analytics data',
                'integrations': 'Manage integrations',
                'admin': 'Administrative access'
              }
            }
          }
        }
      },
      schemas: {
        Error: {
          type: 'object',
          required: ['error', 'message'],
          properties: {
            error: {
              type: 'string',
              description: 'Error code',
              example: 'validation_failed'
            },
            message: {
              type: 'string',
              description: 'Human-readable error message',
              example: 'The provided data is invalid'
            },
            details: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  field: {
                    type: 'string',
                    description: 'Field name that caused the error'
                  },
                  message: {
                    type: 'string',
                    description: 'Field-specific error message'
                  }
                }
              },
              description: 'Detailed validation errors'
            },
            code: {
              type: 'integer',
              description: 'HTTP status code',
              example: 400
            }
          }
        },
        User: {
          type: 'object',
          required: ['id', 'email', 'firstName', 'lastName'],
          properties: {
            id: {
              type: 'string',
              format: 'uuid',
              description: 'Unique user identifier',
              example: '550e8400-e29b-41d4-a716-************'
            },
            email: {
              type: 'string',
              format: 'email',
              description: 'User email address',
              example: '<EMAIL>'
            },
            firstName: {
              type: 'string',
              description: 'User first name',
              example: 'John'
            },
            lastName: {
              type: 'string',
              description: 'User last name',
              example: 'Doe'
            },
            role: {
              type: 'string',
              enum: ['admin', 'manager', 'analyst', 'user', 'viewer'],
              description: 'User role',
              example: 'user'
            },
            isActive: {
              type: 'boolean',
              description: 'Whether the user account is active',
              example: true
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
              description: 'Account creation timestamp',
              example: '2023-01-01T00:00:00Z'
            },
            lastLoginAt: {
              type: 'string',
              format: 'date-time',
              description: 'Last login timestamp',
              example: '2023-12-01T12:00:00Z'
            }
          }
        },
        AnalyticsEvent: {
          type: 'object',
          required: ['eventType', 'timestamp'],
          properties: {
            id: {
              type: 'string',
              format: 'uuid',
              description: 'Unique event identifier',
              example: '550e8400-e29b-41d4-a716-************'
            },
            eventType: {
              type: 'string',
              description: 'Type of analytics event',
              example: 'page_view'
            },
            timestamp: {
              type: 'string',
              format: 'date-time',
              description: 'Event timestamp',
              example: '2023-12-01T12:00:00Z'
            },
            userId: {
              type: 'string',
              format: 'uuid',
              description: 'User who triggered the event',
              example: '550e8400-e29b-41d4-a716-************'
            },
            sessionId: {
              type: 'string',
              description: 'Session identifier',
              example: 'sess_1234567890'
            },
            properties: {
              type: 'object',
              description: 'Event-specific properties',
              additionalProperties: true,
              example: {
                page: '/dashboard',
                referrer: '/login',
                duration: 5000
              }
            },
            metadata: {
              type: 'object',
              description: 'Additional metadata',
              properties: {
                userAgent: {
                  type: 'string',
                  description: 'User agent string'
                },
                ipAddress: {
                  type: 'string',
                  description: 'Client IP address'
                },
                country: {
                  type: 'string',
                  description: 'User country'
                }
              }
            }
          }
        },
        Dashboard: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              format: 'uuid',
              description: 'Dashboard identifier',
              example: '550e8400-e29b-41d4-a716-************'
            },
            name: {
              type: 'string',
              description: 'Dashboard name',
              example: 'Sales Overview'
            },
            description: {
              type: 'string',
              description: 'Dashboard description',
              example: 'Real-time sales performance metrics'
            },
            widgets: {
              type: 'array',
              items: {
                $ref: '#/components/schemas/Widget'
              },
              description: 'Dashboard widgets'
            },
            isPublic: {
              type: 'boolean',
              description: 'Whether dashboard is publicly accessible',
              example: false
            },
            ownerId: {
              type: 'string',
              format: 'uuid',
              description: 'Dashboard owner',
              example: '550e8400-e29b-41d4-a716-************'
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
              description: 'Creation timestamp',
              example: '2023-01-01T00:00:00Z'
            },
            updatedAt: {
              type: 'string',
              format: 'date-time',
              description: 'Last update timestamp',
              example: '2023-12-01T12:00:00Z'
            }
          }
        },
        Widget: {
          type: 'object',
          required: ['type', 'title'],
          properties: {
            id: {
              type: 'string',
              format: 'uuid',
              description: 'Widget identifier',
              example: '550e8400-e29b-41d4-a716-************'
            },
            type: {
              type: 'string',
              enum: ['chart', 'table', 'metric', 'map'],
              description: 'Widget type',
              example: 'chart'
            },
            title: {
              type: 'string',
              description: 'Widget title',
              example: 'Revenue Trend'
            },
            configuration: {
              type: 'object',
              description: 'Widget-specific configuration',
              additionalProperties: true,
              example: {
                chartType: 'line',
                metric: 'revenue',
                timeRange: '30d'
              }
            },
            position: {
              type: 'object',
              properties: {
                x: {
                  type: 'integer',
                  description: 'X coordinate'
                },
                y: {
                  type: 'integer',
                  description: 'Y coordinate'
                },
                width: {
                  type: 'integer',
                  description: 'Widget width'
                },
                height: {
                  type: 'integer',
                  description: 'Widget height'
                }
              }
            }
          }
        },
        ApiKey: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              format: 'uuid',
              description: 'API key identifier',
              example: '550e8400-e29b-41d4-a716-************'
            },
            keyId: {
              type: 'string',
              description: 'Public key identifier',
              example: 'ek_1234567890abcdef'
            },
            name: {
              type: 'string',
              description: 'API key name',
              example: 'Production Integration'
            },
            description: {
              type: 'string',
              description: 'API key description',
              example: 'Key for production data integration'
            },
            scopes: {
              type: 'array',
              items: {
                type: 'string',
                enum: ['read', 'write', 'analytics', 'integrations', 'admin']
              },
              description: 'API key permissions',
              example: ['read', 'analytics']
            },
            expiresAt: {
              type: 'string',
              format: 'date-time',
              description: 'Expiration timestamp',
              example: '2024-12-31T23:59:59Z'
            },
            lastUsedAt: {
              type: 'string',
              format: 'date-time',
              description: 'Last usage timestamp',
              example: '2023-12-01T12:00:00Z'
            },
            usageCount: {
              type: 'integer',
              description: 'Total usage count',
              example: 1500
            },
            rateLimitOverride: {
              type: 'integer',
              description: 'Custom rate limit (requests per minute)',
              example: 1000
            },
            ipWhitelist: {
              type: 'array',
              items: {
                type: 'string',
                format: 'ipv4'
              },
              description: 'Allowed IP addresses',
              example: ['*************', '*********']
            },
            revoked: {
              type: 'boolean',
              description: 'Whether the key is revoked',
              example: false
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
              description: 'Creation timestamp',
              example: '2023-01-01T00:00:00Z'
            }
          }
        },
        Role: {
          type: 'object',
          required: ['name', 'permissions'],
          properties: {
            id: {
              type: 'string',
              format: 'uuid',
              description: 'Role identifier',
              example: '550e8400-e29b-41d4-a716-************'
            },
            name: {
              type: 'string',
              description: 'Role name',
              example: 'Data Analyst'
            },
            description: {
              type: 'string',
              description: 'Role description',
              example: 'Access to analytics data and reporting features'
            },
            permissions: {
              type: 'array',
              items: {
                type: 'string'
              },
              description: 'Role permissions',
              example: ['analytics.read', 'dashboard.read', 'dashboard.create']
            },
            parentRoleId: {
              type: 'string',
              format: 'uuid',
              description: 'Parent role for inheritance',
              example: '550e8400-e29b-41d4-a716-************'
            },
            active: {
              type: 'boolean',
              description: 'Whether the role is active',
              example: true
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
              description: 'Creation timestamp',
              example: '2023-01-01T00:00:00Z'
            }
          }
        },
        AuditLog: {
          type: 'object',
          properties: {
            auditId: {
              type: 'string',
              description: 'Unique audit identifier',
              example: 'audit_1701432000_a1b2c3d4'
            },
            timestamp: {
              type: 'string',
              format: 'date-time',
              description: 'Event timestamp',
              example: '2023-12-01T12:00:00Z'
            },
            eventType: {
              type: 'string',
              description: 'Type of audited event',
              example: 'user_login'
            },
            category: {
              type: 'string',
              enum: ['authentication', 'authorization', 'data_access', 'data_modification', 'system_admin', 'security'],
              description: 'Event category',
              example: 'authentication'
            },
            severity: {
              type: 'string',
              enum: ['info', 'low', 'medium', 'high', 'critical'],
              description: 'Event severity',
              example: 'info'
            },
            userId: {
              type: 'string',
              format: 'uuid',
              description: 'User who performed the action',
              example: '550e8400-e29b-41d4-a716-************'
            },
            ipAddress: {
              type: 'string',
              description: 'Client IP address',
              example: '*************'
            },
            action: {
              type: 'string',
              description: 'Action performed',
              example: 'POST /api/auth/login'
            },
            resource: {
              type: 'string',
              description: 'Resource accessed',
              example: '/api/auth/login'
            },
            riskScore: {
              type: 'integer',
              minimum: 0,
              maximum: 10,
              description: 'Calculated risk score',
              example: 3
            },
            complianceTags: {
              type: 'array',
              items: {
                type: 'string'
              },
              description: 'Compliance framework tags',
              example: ['SOX', 'GDPR']
            }
          }
        },
        Integration: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              format: 'uuid',
              description: 'Integration identifier',
              example: '550e8400-e29b-41d4-a716-************'
            },
            name: {
              type: 'string',
              description: 'Integration name',
              example: 'Shopify Store'
            },
            type: {
              type: 'string',
              enum: ['shopify', 'woocommerce', 'magento', 'custom'],
              description: 'Integration type',
              example: 'shopify'
            },
            status: {
              type: 'string',
              enum: ['active', 'inactive', 'error', 'configuring'],
              description: 'Integration status',
              example: 'active'
            },
            configuration: {
              type: 'object',
              description: 'Integration-specific configuration',
              additionalProperties: true,
              example: {
                shopUrl: 'mystore.myshopify.com',
                apiVersion: '2023-10'
              }
            },
            lastSyncAt: {
              type: 'string',
              format: 'date-time',
              description: 'Last synchronization timestamp',
              example: '2023-12-01T12:00:00Z'
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
              description: 'Creation timestamp',
              example: '2023-01-01T00:00:00Z'
            }
          }
        },
        PaginationInfo: {
          type: 'object',
          properties: {
            page: {
              type: 'integer',
              minimum: 1,
              description: 'Current page number',
              example: 1
            },
            limit: {
              type: 'integer',
              minimum: 1,
              maximum: 100,
              description: 'Items per page',
              example: 20
            },
            total: {
              type: 'integer',
              description: 'Total number of items',
              example: 150
            },
            pages: {
              type: 'integer',
              description: 'Total number of pages',
              example: 8
            },
            hasNext: {
              type: 'boolean',
              description: 'Whether there are more pages',
              example: true
            },
            hasPrev: {
              type: 'boolean',
              description: 'Whether there are previous pages',
              example: false
            }
          }
        }
      },
      parameters: {
        PageParam: {
          name: 'page',
          in: 'query',
          description: 'Page number for pagination',
          required: false,
          schema: {
            type: 'integer',
            minimum: 1,
            default: 1
          }
        },
        LimitParam: {
          name: 'limit',
          in: 'query',
          description: 'Number of items per page',
          required: false,
          schema: {
            type: 'integer',
            minimum: 1,
            maximum: 100,
            default: 20
          }
        },
        SortParam: {
          name: 'sort',
          in: 'query',
          description: 'Sort field and direction (e.g., "createdAt:desc")',
          required: false,
          schema: {
            type: 'string',
            example: 'createdAt:desc'
          }
        },
        FilterParam: {
          name: 'filter',
          in: 'query',
          description: 'Filter criteria in JSON format',
          required: false,
          schema: {
            type: 'string',
            example: '{"status":"active","type":"user"}'
          }
        }
      },
      responses: {
        BadRequest: {
          description: 'Bad Request - Invalid input data',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/Error'
              },
              example: {
                error: 'validation_failed',
                message: 'Invalid input data provided',
                details: [
                  {
                    field: 'email',
                    message: 'Email format is invalid'
                  }
                ],
                code: 400
              }
            }
          }
        },
        Unauthorized: {
          description: 'Unauthorized - Authentication required',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/Error'
              },
              example: {
                error: 'unauthorized',
                message: 'Authentication required',
                code: 401
              }
            }
          }
        },
        Forbidden: {
          description: 'Forbidden - Insufficient permissions',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/Error'
              },
              example: {
                error: 'forbidden',
                message: 'Insufficient permissions for this operation',
                code: 403
              }
            }
          }
        },
        NotFound: {
          description: 'Not Found - Resource does not exist',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/Error'
              },
              example: {
                error: 'not_found',
                message: 'The requested resource was not found',
                code: 404
              }
            }
          }
        },
        RateLimitExceeded: {
          description: 'Too Many Requests - Rate limit exceeded',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/Error'
              },
              example: {
                error: 'rate_limit_exceeded',
                message: 'Too many requests, please try again later',
                code: 429
              }
            }
          }
        },
        InternalServerError: {
          description: 'Internal Server Error',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/Error'
              },
              example: {
                error: 'server_error',
                message: 'An internal server error occurred',
                code: 500
              }
            }
          }
        }
      }
    },
    security: [
      {
        BearerAuth: []
      }
    ],
    tags: [
      {
        name: 'Authentication',
        description: 'User authentication and session management'
      },
      {
        name: 'Users',
        description: 'User management operations'
      },
      {
        name: 'Analytics',
        description: 'Analytics data and events'
      },
      {
        name: 'Dashboards',
        description: 'Dashboard creation and management'
      },
      {
        name: 'API Keys',
        description: 'API key management'
      },
      {
        name: 'OAuth2',
        description: 'OAuth2 authentication and authorization'
      },
      {
        name: 'Roles',
        description: 'Role-based access control'
      },
      {
        name: 'Audit',
        description: 'Audit logs and compliance'
      },
      {
        name: 'Integrations',
        description: 'Third-party integrations'
      },
      {
        name: 'System',
        description: 'System information and health checks'
      }
    ]
  },
  apis: [
    './src/routes/**/*.js',
    './documentation/api/examples/**/*.yaml'
  ]
};

const swaggerSpec = swaggerJsDoc(swaggerOptions);

// Custom CSS for Swagger UI
const customCss = `
  .swagger-ui .topbar { display: none; }
  .swagger-ui .info .title { color: #2c3e50; }
  .swagger-ui .info .description { color: #34495e; }
  .swagger-ui .scheme-container { background: #f8f9fa; padding: 10px; border-radius: 5px; }
  .swagger-ui .opblock .opblock-summary-method { font-weight: bold; }
  .swagger-ui .opblock.opblock-post .opblock-summary-method { background: #27ae60; }
  .swagger-ui .opblock.opblock-get .opblock-summary-method { background: #3498db; }
  .swagger-ui .opblock.opblock-put .opblock-summary-method { background: #f39c12; }
  .swagger-ui .opblock.opblock-delete .opblock-summary-method { background: #e74c3c; }
`;

const swaggerUiOptions = {
  customCss,
  customSiteTitle: 'E-commerce Analytics API Documentation',
  customfavIcon: '/favicon.ico',
  swaggerOptions: {
    persistAuthorization: true,
    displayRequestDuration: true,
    defaultModelsExpandDepth: 1,
    defaultModelExpandDepth: 1,
    docExpansion: 'list',
    filter: true,
    showExtensions: true,
    showCommonExtensions: true,
    tryItOutEnabled: true
  }
};

module.exports = {
  swaggerSpec,
  swaggerUi,
  swaggerUiOptions
};