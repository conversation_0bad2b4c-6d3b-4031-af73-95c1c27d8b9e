# Authentication API Examples
# Interactive examples for the authentication endpoints

paths:
  /api/auth/register:
    post:
      tags:
        - Authentication
      summary: Register a new user account
      description: |
        Create a new user account with email verification.
        
        **Requirements:**
        - Valid email address
        - Strong password (min 8 chars, uppercase, lowercase, number, special char)
        - Unique email address
        
        **Process:**
        1. User submits registration data
        2. System validates input and uniqueness
        3. Password is hashed using bcrypt
        4. Verification email is sent
        5. User account is created with `emailVerified: false`
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - password
                - firstName
                - lastName
              properties:
                email:
                  type: string
                  format: email
                  description: User email address
                  example: "<EMAIL>"
                password:
                  type: string
                  minLength: 8
                  description: Strong password
                  example: "SecurePass123!"
                firstName:
                  type: string
                  minLength: 1
                  maxLength: 50
                  description: User first name
                  example: "John"
                lastName:
                  type: string
                  minLength: 1
                  maxLength: 50
                  description: User last name
                  example: "Doe"
                company:
                  type: string
                  maxLength: 100
                  description: Company name (optional)
                  example: "Acme Corp"
                phone:
                  type: string
                  description: Phone number (optional)
                  example: "******-123-4567"
            examples:
              standard_user:
                summary: Standard user registration
                value:
                  email: "<EMAIL>"
                  password: "UserPass123!"
                  firstName: "Jane"
                  lastName: "Smith"
                  company: "Tech Solutions Inc"
              admin_user:
                summary: Admin user registration
                value:
                  email: "<EMAIL>"
                  password: "AdminSecure456!"
                  firstName: "Admin"
                  lastName: "User"
                  company: "Company Name"
                  phone: "******-987-6543"
      responses:
        201:
          description: User registered successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "User registered successfully. Please check your email for verification."
                  data:
                    type: object
                    properties:
                      userId:
                        type: string
                        format: uuid
                        example: "550e8400-e29b-41d4-a716-************"
                      email:
                        type: string
                        example: "<EMAIL>"
                      emailVerified:
                        type: boolean
                        example: false
        400:
          $ref: '#/components/responses/BadRequest'
        409:
          description: Email already exists
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              example:
                error: "email_exists"
                message: "An account with this email already exists"

  /api/auth/login:
    post:
      tags:
        - Authentication
      summary: Authenticate user and get access token
      description: |
        Authenticate a user with email and password.
        
        **Features:**
        - JWT token generation
        - Session management
        - MFA support (if enabled)
        - Brute force protection
        - Device fingerprinting
        - Geographical anomaly detection
        
        **Security:**
        - Rate limited (5 attempts per 15 minutes)
        - Account lockout after failed attempts
        - Audit logging for all attempts
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - password
              properties:
                email:
                  type: string
                  format: email
                  description: User email address
                  example: "<EMAIL>"
                password:
                  type: string
                  description: User password
                  example: "UserPass123!"
                rememberMe:
                  type: boolean
                  description: Extend session duration
                  default: false
                  example: false
                mfaCode:
                  type: string
                  description: MFA code (if MFA is enabled)
                  example: "123456"
            examples:
              standard_login:
                summary: Standard login
                value:
                  email: "<EMAIL>"
                  password: "UserPass123!"
                  rememberMe: false
              login_with_mfa:
                summary: Login with MFA
                value:
                  email: "<EMAIL>"
                  password: "UserPass123!"
                  mfaCode: "123456"
              remember_me_login:
                summary: Login with remember me
                value:
                  email: "<EMAIL>"
                  password: "UserPass123!"
                  rememberMe: true
      responses:
        200:
          description: Login successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  token:
                    type: string
                    description: JWT access token
                    example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                  refreshToken:
                    type: string
                    description: Refresh token for token renewal
                    example: "rt_1234567890abcdef..."
                  expiresIn:
                    type: integer
                    description: Token expiration time in seconds
                    example: 3600
                  user:
                    $ref: '#/components/schemas/User'
        400:
          $ref: '#/components/responses/BadRequest'
        401:
          description: Invalid credentials
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                invalid_credentials:
                  summary: Invalid email or password
                  value:
                    error: "invalid_credentials"
                    message: "Invalid email or password"
                mfa_required:
                  summary: MFA code required
                  value:
                    error: "mfa_required"
                    message: "Multi-factor authentication code required"
                account_locked:
                  summary: Account locked
                  value:
                    error: "account_locked"
                    message: "Account temporarily locked due to failed login attempts"
        429:
          $ref: '#/components/responses/RateLimitExceeded'

  /api/auth/logout:
    post:
      tags:
        - Authentication
      summary: Logout user and invalidate session
      description: |
        Logout the current user and invalidate their session.
        
        **Actions:**
        - Invalidate JWT token
        - Clear refresh token
        - Log logout event
        - Clear session data
      security:
        - BearerAuth: []
      requestBody:
        required: false
        content:
          application/json:
            schema:
              type: object
              properties:
                allDevices:
                  type: boolean
                  description: Logout from all devices
                  default: false
                  example: false
      responses:
        200:
          description: Logout successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Logged out successfully"
        401:
          $ref: '#/components/responses/Unauthorized'

  /api/auth/refresh:
    post:
      tags:
        - Authentication
      summary: Refresh access token
      description: |
        Refresh an expired access token using a valid refresh token.
        
        **Features:**
        - Token rotation for security
        - Refresh token validation
        - Session continuation
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - refreshToken
              properties:
                refreshToken:
                  type: string
                  description: Valid refresh token
                  example: "rt_1234567890abcdef..."
      responses:
        200:
          description: Token refreshed successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  token:
                    type: string
                    description: New JWT access token
                    example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                  refreshToken:
                    type: string
                    description: New refresh token
                    example: "rt_abcdef1234567890..."
                  expiresIn:
                    type: integer
                    description: Token expiration time in seconds
                    example: 3600
        400:
          $ref: '#/components/responses/BadRequest'
        401:
          description: Invalid refresh token
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              example:
                error: "invalid_refresh_token"
                message: "Refresh token is invalid or expired"

  /api/auth/password-reset:
    post:
      tags:
        - Authentication
      summary: Request password reset
      description: |
        Request a password reset email for the given email address.
        
        **Security Features:**
        - Rate limited (5 requests per hour)
        - Secure token generation
        - Email verification
        - Token expiration (1 hour)
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
              properties:
                email:
                  type: string
                  format: email
                  description: Email address for password reset
                  example: "<EMAIL>"
      responses:
        200:
          description: Password reset email sent
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "If an account with this email exists, a password reset link has been sent"
        400:
          $ref: '#/components/responses/BadRequest'
        429:
          description: Too many password reset requests
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              example:
                error: "rate_limit_exceeded"
                message: "Too many password reset requests. Please try again later."

  /api/auth/password-reset/confirm:
    post:
      tags:
        - Authentication
      summary: Confirm password reset
      description: |
        Confirm password reset with token and set new password.
        
        **Validation:**
        - Token validity and expiration
        - Password strength requirements
        - One-time token usage
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - token
                - newPassword
              properties:
                token:
                  type: string
                  description: Password reset token from email
                  example: "reset_abc123def456..."
                newPassword:
                  type: string
                  minLength: 8
                  description: New strong password
                  example: "NewSecurePass123!"
      responses:
        200:
          description: Password reset successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Password reset successfully. You can now login with your new password."
        400:
          description: Invalid or expired token
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                invalid_token:
                  summary: Invalid reset token
                  value:
                    error: "invalid_token"
                    message: "Password reset token is invalid or expired"
                weak_password:
                  summary: Weak password
                  value:
                    error: "weak_password"
                    message: "Password does not meet security requirements"

  /api/auth/verify-email:
    post:
      tags:
        - Authentication
      summary: Verify email address
      description: |
        Verify user email address using verification token.
        
        **Process:**
        - Token validation
        - Email verification flag update
        - Welcome actions trigger
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - token
              properties:
                token:
                  type: string
                  description: Email verification token
                  example: "verify_xyz789abc123..."
      responses:
        200:
          description: Email verified successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Email verified successfully"
        400:
          description: Invalid verification token
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              example:
                error: "invalid_verification_token"
                message: "Email verification token is invalid or expired"

  /api/auth/profile:
    get:
      tags:
        - Authentication
      summary: Get current user profile
      description: |
        Get the profile information of the currently authenticated user.
        
        **Includes:**
        - Basic user information
        - Role and permissions
        - Account status
        - Security settings
      security:
        - BearerAuth: []
      responses:
        200:
          description: User profile retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    allOf:
                      - $ref: '#/components/schemas/User'
                      - type: object
                        properties:
                          permissions:
                            type: array
                            items:
                              type: string
                            example: ["analytics.read", "dashboard.create"]
                          mfaEnabled:
                            type: boolean
                            example: false
                          emailVerified:
                            type: boolean
                            example: true
                          sessionCount:
                            type: integer
                            example: 2
        401:
          $ref: '#/components/responses/Unauthorized'

    put:
      tags:
        - Authentication
      summary: Update user profile
      description: |
        Update the profile information of the currently authenticated user.
        
        **Updatable Fields:**
        - First name and last name
        - Company information
        - Phone number
        - Profile preferences
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                firstName:
                  type: string
                  maxLength: 50
                  example: "John"
                lastName:
                  type: string
                  maxLength: 50
                  example: "Doe"
                company:
                  type: string
                  maxLength: 100
                  example: "Updated Company"
                phone:
                  type: string
                  example: "******-999-8888"
                preferences:
                  type: object
                  properties:
                    timezone:
                      type: string
                      example: "America/New_York"
                    language:
                      type: string
                      example: "en"
                    notifications:
                      type: object
                      properties:
                        email:
                          type: boolean
                          example: true
                        push:
                          type: boolean
                          example: false
      responses:
        200:
          description: Profile updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Profile updated successfully"
                  data:
                    $ref: '#/components/schemas/User'
        400:
          $ref: '#/components/responses/BadRequest'
        401:
          $ref: '#/components/responses/Unauthorized'