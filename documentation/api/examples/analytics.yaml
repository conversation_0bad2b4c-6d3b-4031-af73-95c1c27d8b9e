# Analytics API Examples
# Interactive examples for analytics and reporting endpoints

paths:
  /api/analytics/events:
    get:
      tags:
        - Analytics
      summary: Get analytics events
      description: |
        Retrieve analytics events with filtering, sorting, and pagination.
        
        **Features:**
        - Real-time event data
        - Advanced filtering options
        - Custom date ranges
        - Aggregation support
        - Export capabilities
        
        **Use Cases:**
        - Event monitoring
        - User behavior analysis
        - Performance tracking
        - Custom reporting
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
      parameters:
        - $ref: '#/components/parameters/PageParam'
        - $ref: '#/components/parameters/LimitParam'
        - name: startDate
          in: query
          description: Start date for event filtering (ISO 8601)
          schema:
            type: string
            format: date-time
            example: "2023-12-01T00:00:00Z"
        - name: endDate
          in: query
          description: End date for event filtering (ISO 8601)
          schema:
            type: string
            format: date-time
            example: "2023-12-31T23:59:59Z"
        - name: eventType
          in: query
          description: Filter by event type
          schema:
            type: string
            example: "page_view"
        - name: userId
          in: query
          description: Filter by user ID
          schema:
            type: string
            format: uuid
            example: "550e8400-e29b-41d4-a716-************"
        - name: sessionId
          in: query
          description: Filter by session ID
          schema:
            type: string
            example: "sess_1234567890"
        - name: aggregation
          in: query
          description: Aggregation level (hour, day, week, month)
          schema:
            type: string
            enum: [hour, day, week, month]
            default: day
            example: "day"
        - name: metrics
          in: query
          description: Comma-separated list of metrics to include
          schema:
            type: string
            example: "count,unique_users,avg_duration"
      responses:
        200:
          description: Analytics events retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/AnalyticsEvent'
                  pagination:
                    $ref: '#/components/schemas/PaginationInfo'
                  aggregation:
                    type: object
                    properties:
                      total_events:
                        type: integer
                        example: 15420
                      unique_users:
                        type: integer
                        example: 1250
                      unique_sessions:
                        type: integer
                        example: 2100
                      time_range:
                        type: object
                        properties:
                          start:
                            type: string
                            format: date-time
                          end:
                            type: string
                            format: date-time
              examples:
                page_views:
                  summary: Page view events
                  value:
                    success: true
                    data:
                      - id: "550e8400-e29b-41d4-a716-446655440001"
                        eventType: "page_view"
                        timestamp: "2023-12-01T12:00:00Z"
                        userId: "550e8400-e29b-41d4-a716-************"
                        sessionId: "sess_1234567890"
                        properties:
                          page: "/dashboard"
                          referrer: "/login"
                          duration: 5000
                        metadata:
                          userAgent: "Mozilla/5.0..."
                          ipAddress: "*************"
                          country: "US"
                user_registrations:
                  summary: User registration events
                  value:
                    success: true
                    data:
                      - id: "550e8400-e29b-41d4-a716-446655440002"
                        eventType: "user_registration"
                        timestamp: "2023-12-01T10:30:00Z"
                        userId: "550e8400-e29b-41d4-a716-446655440003"
                        properties:
                          source: "organic"
                          plan: "free"
                        metadata:
                          ipAddress: "**********"
                          country: "CA"
        400:
          $ref: '#/components/responses/BadRequest'
        401:
          $ref: '#/components/responses/Unauthorized'
        403:
          $ref: '#/components/responses/Forbidden'

    post:
      tags:
        - Analytics
      summary: Create analytics event
      description: |
        Create a new analytics event for tracking user interactions and system activities.
        
        **Event Types:**
        - Page views
        - User actions
        - System events
        - Custom events
        
        **Automatic Fields:**
        - Timestamp (if not provided)
        - IP address
        - User agent
        - Session tracking
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - eventType
              properties:
                eventType:
                  type: string
                  description: Type of analytics event
                  example: "button_click"
                timestamp:
                  type: string
                  format: date-time
                  description: Event timestamp (auto-generated if not provided)
                  example: "2023-12-01T12:00:00Z"
                userId:
                  type: string
                  format: uuid
                  description: User who triggered the event
                  example: "550e8400-e29b-41d4-a716-************"
                sessionId:
                  type: string
                  description: Session identifier
                  example: "sess_1234567890"
                properties:
                  type: object
                  description: Event-specific properties
                  additionalProperties: true
                  example:
                    button_id: "signup_cta"
                    page: "/pricing"
                    location: "header"
                metadata:
                  type: object
                  description: Additional metadata
                  properties:
                    campaign:
                      type: string
                      description: Marketing campaign
                    source:
                      type: string
                      description: Traffic source
                    medium:
                      type: string
                      description: Traffic medium
            examples:
              page_view:
                summary: Page view event
                value:
                  eventType: "page_view"
                  userId: "550e8400-e29b-41d4-a716-************"
                  sessionId: "sess_1234567890"
                  properties:
                    page: "/dashboard"
                    title: "Analytics Dashboard"
                    load_time: 1200
                  metadata:
                    campaign: "email_newsletter"
                    source: "email"
              button_click:
                summary: Button click event
                value:
                  eventType: "button_click"
                  userId: "550e8400-e29b-41d4-a716-************"
                  properties:
                    button_id: "export_data"
                    button_text: "Export CSV"
                    page: "/analytics"
              custom_event:
                summary: Custom business event
                value:
                  eventType: "purchase_completed"
                  userId: "550e8400-e29b-41d4-a716-************"
                  properties:
                    order_id: "order_12345"
                    amount: 99.99
                    currency: "USD"
                    items: 3
      responses:
        201:
          description: Analytics event created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Analytics event created successfully"
                  data:
                    $ref: '#/components/schemas/AnalyticsEvent'
        400:
          $ref: '#/components/responses/BadRequest'
        401:
          $ref: '#/components/responses/Unauthorized'
        403:
          $ref: '#/components/responses/Forbidden'

  /api/analytics/metrics:
    get:
      tags:
        - Analytics
      summary: Get analytics metrics
      description: |
        Retrieve calculated analytics metrics and KPIs.
        
        **Available Metrics:**
        - Page views and unique visitors
        - User engagement metrics
        - Conversion rates
        - Revenue metrics
        - Custom KPIs
        
        **Time Ranges:**
        - Real-time (last hour)
        - Daily, weekly, monthly
        - Custom date ranges
        - Year-over-year comparisons
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
      parameters:
        - name: timeRange
          in: query
          description: Predefined time range
          schema:
            type: string
            enum: [1h, 24h, 7d, 30d, 90d, 1y]
            default: 7d
            example: "7d"
        - name: startDate
          in: query
          description: Custom start date (overrides timeRange)
          schema:
            type: string
            format: date-time
            example: "2023-12-01T00:00:00Z"
        - name: endDate
          in: query
          description: Custom end date (overrides timeRange)
          schema:
            type: string
            format: date-time
            example: "2023-12-31T23:59:59Z"
        - name: metrics
          in: query
          description: Specific metrics to calculate
          schema:
            type: array
            items:
              type: string
              enum: [
                page_views, unique_visitors, sessions, bounce_rate,
                avg_session_duration, conversion_rate, revenue,
                new_users, returning_users, top_pages, top_referrers
              ]
            example: ["page_views", "unique_visitors", "sessions"]
        - name: breakdown
          in: query
          description: Breakdown dimension
          schema:
            type: string
            enum: [hour, day, week, month, country, device, browser, page]
            example: "day"
        - name: filters
          in: query
          description: Filter criteria in JSON format
          schema:
            type: string
            example: '{"country":"US","device":"desktop"}'
      responses:
        200:
          description: Analytics metrics retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      summary:
                        type: object
                        properties:
                          total_page_views:
                            type: integer
                            example: 15420
                          unique_visitors:
                            type: integer
                            example: 3250
                          total_sessions:
                            type: integer
                            example: 4100
                          bounce_rate:
                            type: number
                            format: float
                            example: 0.32
                          avg_session_duration:
                            type: integer
                            description: Duration in seconds
                            example: 185
                          conversion_rate:
                            type: number
                            format: float
                            example: 0.045
                      breakdown:
                        type: array
                        items:
                          type: object
                          properties:
                            date:
                              type: string
                              format: date
                            page_views:
                              type: integer
                            unique_visitors:
                              type: integer
                            sessions:
                              type: integer
                      top_pages:
                        type: array
                        items:
                          type: object
                          properties:
                            page:
                              type: string
                            views:
                              type: integer
                            unique_visitors:
                              type: integer
                      top_referrers:
                        type: array
                        items:
                          type: object
                          properties:
                            referrer:
                              type: string
                            visits:
                              type: integer
                            conversion_rate:
                              type: number
                  time_range:
                    type: object
                    properties:
                      start:
                        type: string
                        format: date-time
                      end:
                        type: string
                        format: date-time
              examples:
                weekly_summary:
                  summary: Weekly metrics summary
                  value:
                    success: true
                    data:
                      summary:
                        total_page_views: 15420
                        unique_visitors: 3250
                        total_sessions: 4100
                        bounce_rate: 0.32
                        avg_session_duration: 185
                        conversion_rate: 0.045
                      breakdown:
                        - date: "2023-12-01"
                          page_views: 2200
                          unique_visitors: 465
                          sessions: 580
                        - date: "2023-12-02"
                          page_views: 2350
                          unique_visitors: 490
                          sessions: 615
                      top_pages:
                        - page: "/dashboard"
                          views: 4500
                          unique_visitors: 1200
                        - page: "/analytics"
                          views: 3200
                          unique_visitors: 850
        400:
          $ref: '#/components/responses/BadRequest'
        401:
          $ref: '#/components/responses/Unauthorized'
        403:
          $ref: '#/components/responses/Forbidden'

  /api/analytics/query:
    post:
      tags:
        - Analytics
      summary: Execute custom analytics query
      description: |
        Execute a custom analytics query with advanced filtering and aggregation.
        
        **Query Capabilities:**
        - Custom SQL-like syntax
        - Multiple aggregation functions
        - Complex filtering conditions
        - Joins across event types
        - Time-series analysis
        
        **Security:**
        - Query validation and sanitization
        - Resource limits and timeouts
        - Permission-based data access
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - query
              properties:
                query:
                  type: object
                  description: Query specification
                  properties:
                    select:
                      type: array
                      items:
                        type: string
                      description: Fields to select
                      example: ["eventType", "COUNT(*) as total", "COUNT(DISTINCT userId) as unique_users"]
                    from:
                      type: string
                      description: Data source (analytics_events)
                      example: "analytics_events"
                    where:
                      type: array
                      items:
                        type: object
                        properties:
                          field:
                            type: string
                          operator:
                            type: string
                            enum: ["=", "!=", ">", "<", ">=", "<=", "IN", "NOT IN", "LIKE", "NOT LIKE"]
                          value:
                            type: string
                      description: Filter conditions
                      example:
                        - field: "timestamp"
                          operator: ">="
                          value: "2023-12-01T00:00:00Z"
                        - field: "eventType"
                          operator: "IN"
                          value: ["page_view", "button_click"]
                    groupBy:
                      type: array
                      items:
                        type: string
                      description: Group by fields
                      example: ["eventType", "DATE(timestamp)"]
                    orderBy:
                      type: array
                      items:
                        type: object
                        properties:
                          field:
                            type: string
                          direction:
                            type: string
                            enum: ["ASC", "DESC"]
                      description: Sort order
                      example:
                        - field: "total"
                          direction: "DESC"
                    limit:
                      type: integer
                      minimum: 1
                      maximum: 10000
                      description: Result limit
                      example: 100
                cache:
                  type: boolean
                  description: Whether to cache query results
                  default: true
                  example: true
                cacheTTL:
                  type: integer
                  description: Cache TTL in seconds
                  default: 300
                  example: 600
            examples:
              page_views_by_day:
                summary: Page views by day
                value:
                  query:
                    select: ["DATE(timestamp) as date", "COUNT(*) as page_views", "COUNT(DISTINCT userId) as unique_users"]
                    from: "analytics_events"
                    where:
                      - field: "eventType"
                        operator: "="
                        value: "page_view"
                      - field: "timestamp"
                        operator: ">="
                        value: "2023-12-01T00:00:00Z"
                    groupBy: ["DATE(timestamp)"]
                    orderBy:
                      - field: "date"
                        direction: "DESC"
                    limit: 30
              top_users_by_activity:
                summary: Most active users
                value:
                  query:
                    select: ["userId", "COUNT(*) as total_events", "COUNT(DISTINCT eventType) as event_types"]
                    from: "analytics_events"
                    where:
                      - field: "timestamp"
                        operator: ">="
                        value: "2023-12-01T00:00:00Z"
                      - field: "userId"
                        operator: "!="
                        value: "null"
                    groupBy: ["userId"]
                    orderBy:
                      - field: "total_events"
                        direction: "DESC"
                    limit: 50
      responses:
        200:
          description: Query executed successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      results:
                        type: array
                        items:
                          type: object
                          additionalProperties: true
                        description: Query results
                      metadata:
                        type: object
                        properties:
                          rowCount:
                            type: integer
                            description: Number of rows returned
                          executionTime:
                            type: number
                            description: Query execution time in milliseconds
                          cached:
                            type: boolean
                            description: Whether result was served from cache
                          query:
                            type: string
                            description: Executed SQL query (for debugging)
        400:
          description: Invalid query
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                invalid_syntax:
                  summary: Invalid query syntax
                  value:
                    error: "invalid_query_syntax"
                    message: "Query syntax is invalid"
                unauthorized_field:
                  summary: Unauthorized field access
                  value:
                    error: "unauthorized_field"
                    message: "Access to field 'sensitive_data' is not permitted"
        401:
          $ref: '#/components/responses/Unauthorized'
        403:
          $ref: '#/components/responses/Forbidden'
        408:
          description: Query timeout
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              example:
                error: "query_timeout"
                message: "Query execution timed out after 30 seconds"

  /api/analytics/export:
    post:
      tags:
        - Analytics
      summary: Export analytics data
      description: |
        Export analytics data in various formats for external analysis.
        
        **Supported Formats:**
        - CSV (Comma-separated values)
        - JSON (JavaScript Object Notation)
        - Excel (XLSX format)
        - PDF (Formatted reports)
        
        **Features:**
        - Large dataset support
        - Asynchronous processing
        - Email delivery for large exports
        - Custom formatting options
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - format
                - timeRange
              properties:
                format:
                  type: string
                  enum: [csv, json, xlsx, pdf]
                  description: Export format
                  example: "csv"
                timeRange:
                  type: object
                  properties:
                    start:
                      type: string
                      format: date-time
                      description: Start date
                      example: "2023-12-01T00:00:00Z"
                    end:
                      type: string
                      format: date-time
                      description: End date
                      example: "2023-12-31T23:59:59Z"
                filters:
                  type: object
                  description: Data filters
                  properties:
                    eventTypes:
                      type: array
                      items:
                        type: string
                      example: ["page_view", "button_click"]
                    userIds:
                      type: array
                      items:
                        type: string
                        format: uuid
                    countries:
                      type: array
                      items:
                        type: string
                      example: ["US", "CA", "GB"]
                options:
                  type: object
                  description: Export options
                  properties:
                    includeMetadata:
                      type: boolean
                      default: true
                      description: Include event metadata
                    compression:
                      type: boolean
                      default: false
                      description: Compress export file
                    emailDelivery:
                      type: boolean
                      default: false
                      description: Email large exports
                    customFields:
                      type: array
                      items:
                        type: string
                      description: Additional fields to include
            examples:
              csv_export:
                summary: CSV export for last month
                value:
                  format: "csv"
                  timeRange:
                    start: "2023-11-01T00:00:00Z"
                    end: "2023-11-30T23:59:59Z"
                  filters:
                    eventTypes: ["page_view", "user_registration"]
                  options:
                    includeMetadata: true
                    compression: true
              excel_report:
                summary: Excel report with charts
                value:
                  format: "xlsx"
                  timeRange:
                    start: "2023-12-01T00:00:00Z"
                    end: "2023-12-31T23:59:59Z"
                  options:
                    includeMetadata: false
                    emailDelivery: true
      responses:
        200:
          description: Export initiated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Export initiated successfully"
                  data:
                    type: object
                    properties:
                      exportId:
                        type: string
                        description: Unique export identifier
                        example: "export_abc123def456"
                      estimatedRows:
                        type: integer
                        description: Estimated number of rows
                        example: 50000
                      estimatedSize:
                        type: string
                        description: Estimated file size
                        example: "12.5 MB"
                      downloadUrl:
                        type: string
                        description: Download URL (for small exports)
                        example: "/api/analytics/export/download/export_abc123def456"
                      statusUrl:
                        type: string
                        description: Status check URL
                        example: "/api/analytics/export/status/export_abc123def456"
        400:
          $ref: '#/components/responses/BadRequest'
        401:
          $ref: '#/components/responses/Unauthorized'
        403:
          $ref: '#/components/responses/Forbidden'

  /api/analytics/real-time:
    get:
      tags:
        - Analytics
      summary: Get real-time analytics data
      description: |
        Get real-time analytics data for live monitoring and dashboards.
        
        **Real-time Metrics:**
        - Active users (current sessions)
        - Live page views per minute
        - Real-time conversions
        - System performance metrics
        - Geographic distribution
        
        **Update Frequency:**
        - Data updated every 30 seconds
        - WebSocket support for live updates
        - Caching for performance
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
      parameters:
        - name: metrics
          in: query
          description: Real-time metrics to include
          schema:
            type: array
            items:
              type: string
              enum: [
                active_users, page_views_per_minute, conversions_per_hour,
                top_pages_now, geographic_distribution, device_breakdown
              ]
            example: ["active_users", "page_views_per_minute", "top_pages_now"]
        - name: interval
          in: query
          description: Data interval in minutes
          schema:
            type: integer
            enum: [1, 5, 15, 30, 60]
            default: 5
            example: 5
      responses:
        200:
          description: Real-time analytics data retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      timestamp:
                        type: string
                        format: date-time
                        description: Data timestamp
                        example: "2023-12-01T12:00:00Z"
                      active_users:
                        type: integer
                        description: Currently active users
                        example: 245
                      page_views_per_minute:
                        type: array
                        items:
                          type: object
                          properties:
                            minute:
                              type: string
                              format: date-time
                            count:
                              type: integer
                        description: Page views in recent minutes
                      conversions_per_hour:
                        type: integer
                        description: Conversions in current hour
                        example: 12
                      top_pages_now:
                        type: array
                        items:
                          type: object
                          properties:
                            page:
                              type: string
                            active_users:
                              type: integer
                            views_last_5min:
                              type: integer
                        description: Most active pages right now
                      geographic_distribution:
                        type: array
                        items:
                          type: object
                          properties:
                            country:
                              type: string
                            active_users:
                              type: integer
                            percentage:
                              type: number
                        description: Active users by country
                  next_update:
                    type: string
                    format: date-time
                    description: Next data update time
                    example: "2023-12-01T12:00:30Z"
        401:
          $ref: '#/components/responses/Unauthorized'
        403:
          $ref: '#/components/responses/Forbidden'