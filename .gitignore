# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Go dependencies
vendor/

# Build outputs
dist/
build/
tmp/
*.out

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Docker
*.tar

# Kubernetes secrets
secrets/

# AWS
.aws/

# Terraform
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl

# Go specific
*.test
*.prof
__debug_bin

# Air (Go hot reload)
tmp/

# Database
*.sqlite
*.db

# Cache
.cache/

# Temporary files
*.tmp
*.temp

# Documentation build
docs/_build/

# Python (if used for scripts)
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
.venv/
pip-log.txt
pip-delete-this-directory.txt

# Local development
docker-compose.override.yml
.env.override

# Monitoring data
prometheus_data/
grafana_data/