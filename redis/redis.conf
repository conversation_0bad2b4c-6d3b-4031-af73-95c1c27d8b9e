# Redis configuration for e-commerce analytics platform
# Production-ready settings with performance optimizations

# =============================================================================
# NETWORK AND SECURITY
# =============================================================================

# Bind to all interfaces (Docker networking)
bind 0.0.0.0

# Default port
port 6379

# Disable protected mode when password is set
protected-mode yes

# Require password authentication
requirepass ${REDIS_PASSWORD}

# Timeout for idle clients (0 = disable)
timeout 300

# TCP keepalive
tcp-keepalive 300

# TCP backlog
tcp-backlog 511

# =============================================================================
# PERSISTENCE
# =============================================================================

# Save snapshots
save 900 1      # Save if at least 1 change in 900 seconds
save 300 10     # Save if at least 10 changes in 300 seconds  
save 60 10000   # Save if at least 10000 changes in 60 seconds

# Compression
rdbcompression yes
rdbchecksum yes

# AOF persistence
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec

# AOF rewrite
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# =============================================================================
# MEMORY MANAGEMENT
# =============================================================================

# Maximum memory policy
maxmemory-policy allkeys-lru

# Memory sampling
maxmemory-samples 5

# =============================================================================
# LOGGING
# =============================================================================

# Log level
loglevel notice

# Log file (empty string = stdout)
logfile ""

# Syslog
syslog-enabled no

# =============================================================================
# DATABASE SETTINGS
# =============================================================================

# Number of databases
databases 16

# =============================================================================
# REPLICATION
# =============================================================================

# Replica settings
replica-serve-stale-data yes
replica-read-only yes
repl-diskless-sync no
repl-diskless-sync-delay 5

# =============================================================================
# SECURITY
# =============================================================================

# Rename dangerous commands
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command EVAL ""
rename-command DEBUG ""
rename-command CONFIG "CONFIG_b840fc02d524045429941cc15f59e41cb7be6c52"

# =============================================================================
# CLIENT LIMITS
# =============================================================================

# Max number of connected clients
maxclients 10000

# =============================================================================
# SLOW LOG
# =============================================================================

# Slow log settings
slowlog-log-slower-than 10000
slowlog-max-len 128

# =============================================================================
# LATENCY MONITORING
# =============================================================================

# Latency threshold in milliseconds
latency-monitor-threshold 100

# =============================================================================
# ADVANCED CONFIG
# =============================================================================

# Hash table settings
hash-max-ziplist-entries 512
hash-max-ziplist-value 64

# List settings
list-max-ziplist-size -2
list-compress-depth 0

# Set settings
set-max-intset-entries 512

# Sorted set settings
zset-max-ziplist-entries 128
zset-max-ziplist-value 64

# HyperLogLog settings
hll-sparse-max-bytes 3000

# Streams settings
stream-node-max-bytes 4096
stream-node-max-entries 100

# Active rehashing
activerehashing yes

# Client output buffer limits
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60

# Client query buffer limit
client-query-buffer-limit 1gb

# Protocol buffer limit
proto-max-bulk-len 512mb

# Frequency of background tasks
hz 10

# Dynamic HZ
dynamic-hz yes

# AOF use RDB preamble
aof-use-rdb-preamble yes

# JEMalloc background thread
jemalloc-bg-thread yes

# =============================================================================
# MODULES (Optional)
# =============================================================================

# Load modules here if needed
# loadmodule /path/to/module.so