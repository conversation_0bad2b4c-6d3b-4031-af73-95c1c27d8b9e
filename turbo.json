{"$schema": "https://turbo.build/schema.json", "ui": "tui", "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": [".next/**", "!.next/cache/**", "dist/**"]}, "dev": {"cache": false, "persistent": true}, "start": {"cache": false, "persistent": true}, "type-check": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env*"]}, "test": {"inputs": ["$TURBO_DEFAULT$", ".env*"]}, "clean": {"cache": false}}}