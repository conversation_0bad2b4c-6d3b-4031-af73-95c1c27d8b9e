"""
Intelligent Traffic Routing System
Advanced global traffic routing with machine learning optimization, real-time adaptation, and multi-provider failover
"""

import asyncio
import json
import logging
import time
import hashlib
import random
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any, Set
from enum import Enum
import aiohttp
import aiodns
import geoip2.database
import redis.asyncio as redis
from prometheus_client import Counter, Histogram, Gauge, start_http_server
import numpy as np
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
import joblib
import asyncpg
from fastapi import FastAPI, Request, Response, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Prometheus metrics
route_requests = Counter('traffic_router_requests_total', 'Total routing requests', ['region', 'provider', 'status'])
route_latency = Histogram('traffic_router_latency_seconds', 'Request latency', ['region', 'provider'])
active_connections = Gauge('traffic_router_active_connections', 'Active connections', ['region', 'provider'])
health_score = Gauge('traffic_router_health_score', 'Provider health score', ['region', 'provider'])

class RoutingStrategy(Enum):
    ROUND_ROBIN = "round_robin"
    WEIGHTED_ROUND_ROBIN = "weighted_round_robin"
    LEAST_CONNECTIONS = "least_connections"
    LATENCY_BASED = "latency_based"
    GEOLOCATION = "geolocation"
    MACHINE_LEARNING = "machine_learning"
    ADAPTIVE = "adaptive"

class ProviderStatus(Enum):
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    MAINTENANCE = "maintenance"

@dataclass
class EndpointConfig:
    id: str
    url: str
    region: str
    provider: str
    weight: int = 100
    max_connections: int = 1000
    health_check_url: str = ""
    priority: int = 1
    capabilities: List[str] = None
    ssl_enabled: bool = True
    
    def __post_init__(self):
        if self.capabilities is None:
            self.capabilities = []
        if not self.health_check_url:
            self.health_check_url = f"{self.url}/health"

@dataclass
class RoutingRequest:
    client_ip: str
    user_agent: str
    request_path: str
    request_method: str
    headers: Dict[str, str]
    timestamp: float
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    capabilities_required: List[str] = None
    
    def __post_init__(self):
        if self.capabilities_required is None:
            self.capabilities_required = []

@dataclass
class RoutingResult:
    endpoint: EndpointConfig
    reason: str
    latency_ms: float
    health_score: float
    confidence: float
    fallback_used: bool = False
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}

@dataclass
class HealthMetrics:
    status: ProviderStatus
    response_time_ms: float
    success_rate: float
    error_rate: float
    cpu_usage: float
    memory_usage: float
    active_connections: int
    last_check: float
    consecutive_failures: int = 0

class GeoLocationService:
    def __init__(self, geoip_db_path: str = "GeoLite2-City.mmdb"):
        self.db_path = geoip_db_path
        self.reader = None
        self.setup_geoip()
    
    def setup_geoip(self):
        try:
            self.reader = geoip2.database.Reader(self.db_path)
            logger.info("GeoIP database loaded successfully")
        except Exception as e:
            logger.warning(f"Failed to load GeoIP database: {e}")
            self.reader = None
    
    async def get_location(self, ip_address: str) -> Dict[str, Any]:
        if not self.reader:
            return {"country": "US", "region": "us-east-1", "city": "Unknown", "latitude": 0.0, "longitude": 0.0}
        
        try:
            response = self.reader.city(ip_address)
            return {
                "country": response.country.iso_code or "US",
                "region": self.map_to_region(response.country.iso_code, response.subdivisions.most_specific.iso_code),
                "city": response.city.name or "Unknown",
                "latitude": float(response.location.latitude or 0.0),
                "longitude": float(response.location.longitude or 0.0)
            }
        except Exception as e:
            logger.warning(f"GeoIP lookup failed for {ip_address}: {e}")
            return {"country": "US", "region": "us-east-1", "city": "Unknown", "latitude": 0.0, "longitude": 0.0}
    
    def map_to_region(self, country_code: str, subdivision_code: str) -> str:
        region_mapping = {
            "US": {"default": "us-east-1", "CA": "us-west-2", "WA": "us-west-2", "OR": "us-west-2"},
            "CA": {"default": "us-east-1"},
            "GB": {"default": "eu-west-1"},
            "DE": {"default": "eu-west-1"},
            "FR": {"default": "eu-west-1"},
            "JP": {"default": "ap-southeast-1"},
            "SG": {"default": "ap-southeast-1"},
            "AU": {"default": "ap-southeast-1"}
        }
        
        if country_code in region_mapping:
            country_regions = region_mapping[country_code]
            return country_regions.get(subdivision_code, country_regions["default"])
        
        return "us-east-1"  # Default fallback

class HealthChecker:
    def __init__(self, redis_client: redis.Redis):
        self.redis_client = redis_client
        self.health_metrics: Dict[str, HealthMetrics] = {}
        self.check_interval = 30  # seconds
        self.timeout = 10  # seconds
        
    async def start_health_checks(self, endpoints: List[EndpointConfig]):
        """Start continuous health checking for all endpoints"""
        while True:
            tasks = [self.check_endpoint_health(endpoint) for endpoint in endpoints]
            await asyncio.gather(*tasks, return_exceptions=True)
            await asyncio.sleep(self.check_interval)
    
    async def check_endpoint_health(self, endpoint: EndpointConfig) -> HealthMetrics:
        """Check health of a single endpoint"""
        start_time = time.time()
        
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                async with session.get(endpoint.health_check_url) as response:
                    response_time = (time.time() - start_time) * 1000
                    
                    if response.status == 200:
                        # Try to parse health response
                        try:
                            health_data = await response.json()
                            cpu_usage = health_data.get('cpu_usage', 0.0)
                            memory_usage = health_data.get('memory_usage', 0.0)
                            active_connections = health_data.get('active_connections', 0)
                        except:
                            cpu_usage = memory_usage = 0.0
                            active_connections = 0
                        
                        metrics = HealthMetrics(
                            status=ProviderStatus.HEALTHY,
                            response_time_ms=response_time,
                            success_rate=1.0,
                            error_rate=0.0,
                            cpu_usage=cpu_usage,
                            memory_usage=memory_usage,
                            active_connections=active_connections,
                            last_check=time.time(),
                            consecutive_failures=0
                        )
                    else:
                        metrics = await self.create_failure_metrics(endpoint, response_time)
                        
        except Exception as e:
            logger.warning(f"Health check failed for {endpoint.id}: {e}")
            metrics = await self.create_failure_metrics(endpoint, (time.time() - start_time) * 1000)
        
        # Store metrics
        self.health_metrics[endpoint.id] = metrics
        await self.store_health_metrics(endpoint.id, metrics)
        
        # Update Prometheus metrics
        health_score.labels(region=endpoint.region, provider=endpoint.provider).set(
            self.calculate_health_score(metrics)
        )
        
        return metrics
    
    async def create_failure_metrics(self, endpoint: EndpointConfig, response_time: float) -> HealthMetrics:
        """Create metrics for failed health check"""
        previous_metrics = self.health_metrics.get(endpoint.id)
        consecutive_failures = (previous_metrics.consecutive_failures + 1) if previous_metrics else 1
        
        status = ProviderStatus.UNHEALTHY
        if consecutive_failures <= 3:
            status = ProviderStatus.DEGRADED
        
        return HealthMetrics(
            status=status,
            response_time_ms=response_time,
            success_rate=0.0,
            error_rate=1.0,
            cpu_usage=100.0,  # Assume high load on failure
            memory_usage=100.0,
            active_connections=0,
            last_check=time.time(),
            consecutive_failures=consecutive_failures
        )
    
    def calculate_health_score(self, metrics: HealthMetrics) -> float:
        """Calculate overall health score (0.0 to 1.0)"""
        if metrics.status == ProviderStatus.UNHEALTHY:
            return 0.0
        
        # Base score from response time (lower is better)
        response_score = max(0.0, 1.0 - (metrics.response_time_ms / 5000.0))  # 5s max
        
        # Resource usage score
        resource_score = 1.0 - ((metrics.cpu_usage + metrics.memory_usage) / 200.0)
        
        # Success rate score
        success_score = metrics.success_rate
        
        # Weighted combination
        total_score = (response_score * 0.4 + resource_score * 0.3 + success_score * 0.3)
        
        return max(0.0, min(1.0, total_score))
    
    async def store_health_metrics(self, endpoint_id: str, metrics: HealthMetrics):
        """Store health metrics in Redis"""
        try:
            key = f"health:{endpoint_id}"
            data = asdict(metrics)
            data['status'] = metrics.status.value
            await self.redis_client.hset(key, mapping=data)
            await self.redis_client.expire(key, 300)  # 5 minutes TTL
        except Exception as e:
            logger.warning(f"Failed to store health metrics for {endpoint_id}: {e}")
    
    async def get_health_metrics(self, endpoint_id: str) -> Optional[HealthMetrics]:
        """Get cached health metrics from Redis"""
        try:
            data = await self.redis_client.hgetall(f"health:{endpoint_id}")
            if data:
                return HealthMetrics(
                    status=ProviderStatus(data['status']),
                    response_time_ms=float(data['response_time_ms']),
                    success_rate=float(data['success_rate']),
                    error_rate=float(data['error_rate']),
                    cpu_usage=float(data['cpu_usage']),
                    memory_usage=float(data['memory_usage']),
                    active_connections=int(data['active_connections']),
                    last_check=float(data['last_check']),
                    consecutive_failures=int(data.get('consecutive_failures', 0))
                )
        except Exception as e:
            logger.warning(f"Failed to get health metrics for {endpoint_id}: {e}")
        
        return self.health_metrics.get(endpoint_id)

class MachineLearningRouter:
    def __init__(self, redis_client: redis.Redis):
        self.redis_client = redis_client
        self.model = RandomForestRegressor(n_estimators=100, random_state=42)
        self.scaler = StandardScaler()
        self.is_trained = False
        self.feature_names = [
            'hour_of_day', 'day_of_week', 'user_geolocation_lat', 'user_geolocation_lon',
            'endpoint_cpu_usage', 'endpoint_memory_usage', 'endpoint_response_time',
            'endpoint_active_connections', 'historical_success_rate', 'distance_km'
        ]
    
    async def train_model(self):
        """Train the ML model with historical data"""
        try:
            # Fetch training data from database/Redis
            training_data = await self.fetch_training_data()
            
            if len(training_data) < 100:  # Need minimum data for training
                logger.warning("Insufficient training data for ML model")
                return
            
            X, y = self.prepare_training_data(training_data)
            
            # Scale features
            X_scaled = self.scaler.fit_transform(X)
            
            # Train model
            self.model.fit(X_scaled, y)
            self.is_trained = True
            
            # Save model
            await self.save_model()
            
            logger.info(f"ML model trained with {len(training_data)} samples")
            
        except Exception as e:
            logger.error(f"Failed to train ML model: {e}")
    
    async def predict_performance(self, request: RoutingRequest, endpoint: EndpointConfig, 
                                health_metrics: HealthMetrics, user_location: Dict[str, Any]) -> float:
        """Predict endpoint performance for the given request"""
        if not self.is_trained:
            return 0.5  # Default neutral score
        
        try:
            features = self.extract_features(request, endpoint, health_metrics, user_location)
            features_scaled = self.scaler.transform([features])
            
            # Predict performance score (0.0 to 1.0)
            prediction = self.model.predict(features_scaled)[0]
            return max(0.0, min(1.0, prediction))
            
        except Exception as e:
            logger.warning(f"ML prediction failed: {e}")
            return 0.5
    
    def extract_features(self, request: RoutingRequest, endpoint: EndpointConfig, 
                        health_metrics: HealthMetrics, user_location: Dict[str, Any]) -> List[float]:
        """Extract features for ML prediction"""
        dt = datetime.fromtimestamp(request.timestamp)
        
        # Calculate distance between user and endpoint
        distance = self.calculate_distance(
            user_location['latitude'], user_location['longitude'],
            self.get_endpoint_coordinates(endpoint.region)
        )
        
        return [
            dt.hour,  # hour_of_day
            dt.weekday(),  # day_of_week
            user_location['latitude'],  # user_geolocation_lat
            user_location['longitude'],  # user_geolocation_lon
            health_metrics.cpu_usage,  # endpoint_cpu_usage
            health_metrics.memory_usage,  # endpoint_memory_usage
            health_metrics.response_time_ms,  # endpoint_response_time
            health_metrics.active_connections,  # endpoint_active_connections
            health_metrics.success_rate,  # historical_success_rate
            distance  # distance_km
        ]
    
    def calculate_distance(self, lat1: float, lon1: float, endpoint_coords: Tuple[float, float]) -> float:
        """Calculate distance between user and endpoint in kilometers"""
        lat2, lon2 = endpoint_coords
        
        # Haversine formula
        R = 6371  # Earth's radius in kilometers
        
        dlat = np.radians(lat2 - lat1)
        dlon = np.radians(lon2 - lon1)
        
        a = (np.sin(dlat/2) * np.sin(dlat/2) + 
             np.cos(np.radians(lat1)) * np.cos(np.radians(lat2)) * 
             np.sin(dlon/2) * np.sin(dlon/2))
        
        c = 2 * np.arctan2(np.sqrt(a), np.sqrt(1-a))
        distance = R * c
        
        return distance
    
    def get_endpoint_coordinates(self, region: str) -> Tuple[float, float]:
        """Get approximate coordinates for endpoint region"""
        region_coords = {
            'us-east-1': (39.0458, -76.6413),  # Virginia
            'us-west-2': (45.5152, -122.6784),  # Oregon
            'eu-west-1': (53.3498, -6.2603),  # Ireland
            'ap-southeast-1': (1.3521, 103.8198),  # Singapore
        }
        return region_coords.get(region, (39.0458, -76.6413))
    
    async def fetch_training_data(self) -> List[Dict[str, Any]]:
        """Fetch historical routing data for training"""
        # This would fetch from a time-series database
        # For now, return empty list
        return []
    
    def prepare_training_data(self, raw_data: List[Dict[str, Any]]) -> Tuple[np.ndarray, np.ndarray]:
        """Prepare training data for ML model"""
        X = []
        y = []
        
        for record in raw_data:
            features = record['features']  # Should match feature_names
            performance = record['performance_score']  # Target variable (0.0 to 1.0)
            
            X.append(features)
            y.append(performance)
        
        return np.array(X), np.array(y)
    
    async def save_model(self):
        """Save trained model to Redis"""
        try:
            model_data = joblib.dumps(self.model)
            scaler_data = joblib.dumps(self.scaler)
            
            await self.redis_client.set("ml_model", model_data)
            await self.redis_client.set("ml_scaler", scaler_data)
            await self.redis_client.set("ml_model_trained", "true")
            
        except Exception as e:
            logger.error(f"Failed to save ML model: {e}")
    
    async def load_model(self):
        """Load trained model from Redis"""
        try:
            model_data = await self.redis_client.get("ml_model")
            scaler_data = await self.redis_client.get("ml_scaler")
            is_trained = await self.redis_client.get("ml_model_trained")
            
            if model_data and scaler_data and is_trained:
                self.model = joblib.loads(model_data)
                self.scaler = joblib.loads(scaler_data)
                self.is_trained = True
                logger.info("ML model loaded from Redis")
            
        except Exception as e:
            logger.warning(f"Failed to load ML model: {e}")

class IntelligentTrafficRouter:
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.endpoints: List[EndpointConfig] = []
        self.default_strategy = RoutingStrategy(config.get('default_strategy', 'adaptive'))
        
        # Initialize components
        self.redis_client = None
        self.geolocation = GeoLocationService(config.get('geoip_db_path'))
        self.health_checker = None
        self.ml_router = None
        
        # Connection tracking
        self.active_connections: Dict[str, int] = {}
        self.round_robin_counters: Dict[str, int] = {}
        
        # Setup endpoints
        self.setup_endpoints()
    
    async def initialize(self):
        """Initialize async components"""
        # Connect to Redis
        redis_url = self.config.get('redis_url', 'redis://localhost:6379')
        self.redis_client = redis.from_url(redis_url)
        
        # Initialize health checker
        self.health_checker = HealthChecker(self.redis_client)
        
        # Initialize ML router
        self.ml_router = MachineLearningRouter(self.redis_client)
        await self.ml_router.load_model()
        
        # Start background tasks
        asyncio.create_task(self.health_checker.start_health_checks(self.endpoints))
        asyncio.create_task(self.periodic_ml_training())
        
        logger.info("Intelligent Traffic Router initialized")
    
    def setup_endpoints(self):
        """Setup endpoint configurations"""
        endpoint_configs = self.config.get('endpoints', [])
        
        for config in endpoint_configs:
            endpoint = EndpointConfig(**config)
            self.endpoints.append(endpoint)
            self.active_connections[endpoint.id] = 0
            self.round_robin_counters[endpoint.id] = 0
        
        logger.info(f"Configured {len(self.endpoints)} endpoints")
    
    async def route_request(self, request: RoutingRequest) -> RoutingResult:
        """Route request to optimal endpoint"""
        start_time = time.time()
        
        try:
            # Get user location
            user_location = await self.geolocation.get_location(request.client_ip)
            
            # Filter available endpoints
            available_endpoints = await self.filter_available_endpoints(request)
            
            if not available_endpoints:
                raise HTTPException(status_code=503, detail="No healthy endpoints available")
            
            # Select optimal endpoint based on strategy
            result = await self.select_optimal_endpoint(request, available_endpoints, user_location)
            
            # Track connection
            self.active_connections[result.endpoint.id] += 1
            
            # Update metrics
            latency = (time.time() - start_time) * 1000
            route_requests.labels(
                region=result.endpoint.region,
                provider=result.endpoint.provider,
                status='success'
            ).inc()
            
            route_latency.labels(
                region=result.endpoint.region,
                provider=result.endpoint.provider
            ).observe(latency / 1000)
            
            # Log routing decision
            await self.log_routing_decision(request, result, user_location)
            
            return result
            
        except Exception as e:
            route_requests.labels(region='unknown', provider='unknown', status='error').inc()
            logger.error(f"Routing failed: {e}")
            raise
    
    async def filter_available_endpoints(self, request: RoutingRequest) -> List[EndpointConfig]:
        """Filter endpoints based on health and capabilities"""
        available = []
        
        for endpoint in self.endpoints:
            # Check health
            health_metrics = await self.health_checker.get_health_metrics(endpoint.id)
            if not health_metrics or health_metrics.status == ProviderStatus.UNHEALTHY:
                continue
            
            # Check connection limits
            if self.active_connections[endpoint.id] >= endpoint.max_connections:
                continue
            
            # Check required capabilities
            if request.capabilities_required:
                if not all(cap in endpoint.capabilities for cap in request.capabilities_required):
                    continue
            
            available.append(endpoint)
        
        return available
    
    async def select_optimal_endpoint(self, request: RoutingRequest, 
                                   available_endpoints: List[EndpointConfig],
                                   user_location: Dict[str, Any]) -> RoutingResult:
        """Select optimal endpoint using specified strategy"""
        
        if self.default_strategy == RoutingStrategy.ROUND_ROBIN:
            return await self.round_robin_selection(available_endpoints)
        
        elif self.default_strategy == RoutingStrategy.WEIGHTED_ROUND_ROBIN:
            return await self.weighted_round_robin_selection(available_endpoints)
        
        elif self.default_strategy == RoutingStrategy.LEAST_CONNECTIONS:
            return await self.least_connections_selection(available_endpoints)
        
        elif self.default_strategy == RoutingStrategy.LATENCY_BASED:
            return await self.latency_based_selection(available_endpoints)
        
        elif self.default_strategy == RoutingStrategy.GEOLOCATION:
            return await self.geolocation_selection(available_endpoints, user_location)
        
        elif self.default_strategy == RoutingStrategy.MACHINE_LEARNING:
            return await self.ml_selection(request, available_endpoints, user_location)
        
        elif self.default_strategy == RoutingStrategy.ADAPTIVE:
            return await self.adaptive_selection(request, available_endpoints, user_location)
        
        else:
            # Fallback to round robin
            return await self.round_robin_selection(available_endpoints)
    
    async def round_robin_selection(self, endpoints: List[EndpointConfig]) -> RoutingResult:
        """Simple round-robin selection"""
        if not endpoints:
            raise ValueError("No available endpoints")
        
        # Find endpoint with lowest counter
        min_counter = min(self.round_robin_counters[ep.id] for ep in endpoints)
        candidates = [ep for ep in endpoints if self.round_robin_counters[ep.id] == min_counter]
        
        selected = random.choice(candidates)
        self.round_robin_counters[selected.id] += 1
        
        return RoutingResult(
            endpoint=selected,
            reason="round_robin",
            latency_ms=0.0,
            health_score=1.0,
            confidence=1.0
        )
    
    async def weighted_round_robin_selection(self, endpoints: List[EndpointConfig]) -> RoutingResult:
        """Weighted round-robin selection based on endpoint weights"""
        if not endpoints:
            raise ValueError("No available endpoints")
        
        # Calculate selection probabilities based on weights
        total_weight = sum(ep.weight for ep in endpoints)
        probabilities = [ep.weight / total_weight for ep in endpoints]
        
        # Select endpoint based on weights
        selected = np.random.choice(endpoints, p=probabilities)
        
        return RoutingResult(
            endpoint=selected,
            reason="weighted_round_robin",
            latency_ms=0.0,
            health_score=1.0,
            confidence=1.0
        )
    
    async def least_connections_selection(self, endpoints: List[EndpointConfig]) -> RoutingResult:
        """Select endpoint with least active connections"""
        if not endpoints:
            raise ValueError("No available endpoints")
        
        min_connections = min(self.active_connections[ep.id] for ep in endpoints)
        candidates = [ep for ep in endpoints if self.active_connections[ep.id] == min_connections]
        
        selected = random.choice(candidates)
        
        return RoutingResult(
            endpoint=selected,
            reason="least_connections",
            latency_ms=0.0,
            health_score=1.0,
            confidence=1.0,
            metadata={"active_connections": self.active_connections[selected.id]}
        )
    
    async def latency_based_selection(self, endpoints: List[EndpointConfig]) -> RoutingResult:
        """Select endpoint with lowest latency"""
        if not endpoints:
            raise ValueError("No available endpoints")
        
        best_endpoint = None
        best_latency = float('inf')
        
        for endpoint in endpoints:
            health_metrics = await self.health_checker.get_health_metrics(endpoint.id)
            if health_metrics and health_metrics.response_time_ms < best_latency:
                best_latency = health_metrics.response_time_ms
                best_endpoint = endpoint
        
        if not best_endpoint:
            best_endpoint = random.choice(endpoints)
            best_latency = 0.0
        
        return RoutingResult(
            endpoint=best_endpoint,
            reason="latency_based",
            latency_ms=best_latency,
            health_score=1.0,
            confidence=0.8
        )
    
    async def geolocation_selection(self, endpoints: List[EndpointConfig], 
                                  user_location: Dict[str, Any]) -> RoutingResult:
        """Select endpoint based on geographic proximity"""
        if not endpoints:
            raise ValueError("No available endpoints")
        
        user_lat = user_location['latitude']
        user_lon = user_location['longitude']
        
        best_endpoint = None
        min_distance = float('inf')
        
        for endpoint in endpoints:
            endpoint_coords = self.ml_router.get_endpoint_coordinates(endpoint.region)
            distance = self.ml_router.calculate_distance(user_lat, user_lon, endpoint_coords)
            
            if distance < min_distance:
                min_distance = distance
                best_endpoint = endpoint
        
        if not best_endpoint:
            best_endpoint = random.choice(endpoints)
            min_distance = 0.0
        
        return RoutingResult(
            endpoint=best_endpoint,
            reason="geolocation",
            latency_ms=0.0,
            health_score=1.0,
            confidence=0.9,
            metadata={"distance_km": min_distance}
        )
    
    async def ml_selection(self, request: RoutingRequest, endpoints: List[EndpointConfig],
                          user_location: Dict[str, Any]) -> RoutingResult:
        """Select endpoint using machine learning predictions"""
        if not endpoints:
            raise ValueError("No available endpoints")
        
        if not self.ml_router.is_trained:
            # Fallback to geolocation if ML model not ready
            return await self.geolocation_selection(endpoints, user_location)
        
        best_endpoint = None
        best_score = -1.0
        
        for endpoint in endpoints:
            health_metrics = await self.health_checker.get_health_metrics(endpoint.id)
            if not health_metrics:
                continue
            
            predicted_score = await self.ml_router.predict_performance(
                request, endpoint, health_metrics, user_location
            )
            
            if predicted_score > best_score:
                best_score = predicted_score
                best_endpoint = endpoint
        
        if not best_endpoint:
            best_endpoint = random.choice(endpoints)
            best_score = 0.5
        
        return RoutingResult(
            endpoint=best_endpoint,
            reason="machine_learning",
            latency_ms=0.0,
            health_score=best_score,
            confidence=best_score
        )
    
    async def adaptive_selection(self, request: RoutingRequest, endpoints: List[EndpointConfig],
                               user_location: Dict[str, Any]) -> RoutingResult:
        """Adaptive selection combining multiple strategies"""
        if not endpoints:
            raise ValueError("No available endpoints")
        
        # Combine multiple scoring factors
        endpoint_scores = {}
        
        for endpoint in endpoints:
            health_metrics = await self.health_checker.get_health_metrics(endpoint.id)
            if not health_metrics:
                continue
            
            # Health score (40% weight)
            health_score = self.health_checker.calculate_health_score(health_metrics)
            
            # Geographic score (30% weight)
            endpoint_coords = self.ml_router.get_endpoint_coordinates(endpoint.region)
            distance = self.ml_router.calculate_distance(
                user_location['latitude'], user_location['longitude'], endpoint_coords
            )
            geo_score = max(0.0, 1.0 - (distance / 10000))  # Normalize by 10,000km
            
            # Load score (20% weight)
            load_score = max(0.0, 1.0 - (self.active_connections[endpoint.id] / endpoint.max_connections))
            
            # ML prediction score (10% weight)
            ml_score = 0.5  # Default
            if self.ml_router.is_trained:
                ml_score = await self.ml_router.predict_performance(
                    request, endpoint, health_metrics, user_location
                )
            
            # Combined score
            total_score = (health_score * 0.4 + geo_score * 0.3 + 
                          load_score * 0.2 + ml_score * 0.1)
            
            endpoint_scores[endpoint.id] = {
                'endpoint': endpoint,
                'score': total_score,
                'health_score': health_score,
                'geo_score': geo_score,
                'load_score': load_score,
                'ml_score': ml_score
            }
        
        if not endpoint_scores:
            selected = random.choice(endpoints)
            return RoutingResult(
                endpoint=selected,
                reason="adaptive_fallback",
                latency_ms=0.0,
                health_score=0.5,
                confidence=0.5
            )
        
        # Select endpoint with highest score
        best_result = max(endpoint_scores.values(), key=lambda x: x['score'])
        
        return RoutingResult(
            endpoint=best_result['endpoint'],
            reason="adaptive",
            latency_ms=0.0,
            health_score=best_result['health_score'],
            confidence=best_result['score'],
            metadata={
                'health_score': best_result['health_score'],
                'geo_score': best_result['geo_score'],
                'load_score': best_result['load_score'],
                'ml_score': best_result['ml_score']
            }
        )
    
    async def release_connection(self, endpoint_id: str):
        """Release connection from endpoint"""
        if endpoint_id in self.active_connections:
            self.active_connections[endpoint_id] = max(0, self.active_connections[endpoint_id] - 1)
    
    async def log_routing_decision(self, request: RoutingRequest, result: RoutingResult, 
                                 user_location: Dict[str, Any]):
        """Log routing decision for analytics and ML training"""
        try:
            log_entry = {
                'timestamp': request.timestamp,
                'client_ip': request.client_ip,
                'user_location': user_location,
                'selected_endpoint': result.endpoint.id,
                'region': result.endpoint.region,
                'provider': result.endpoint.provider,
                'reason': result.reason,
                'confidence': result.confidence,
                'health_score': result.health_score,
                'metadata': result.metadata or {}
            }
            
            # Store in Redis for analytics
            key = f"routing_log:{int(request.timestamp)}"
            await self.redis_client.lpush("routing_decisions", json.dumps(log_entry))
            await self.redis_client.ltrim("routing_decisions", 0, 10000)  # Keep last 10k decisions
            
        except Exception as e:
            logger.warning(f"Failed to log routing decision: {e}")
    
    async def periodic_ml_training(self):
        """Periodically retrain ML model with new data"""
        while True:
            try:
                await asyncio.sleep(3600)  # Train every hour
                await self.ml_router.train_model()
            except Exception as e:
                logger.error(f"ML training failed: {e}")

# FastAPI application
app = FastAPI(title="Intelligent Traffic Router", version="1.0.0")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global router instance
router_instance: Optional[IntelligentTrafficRouter] = None

class RouteRequest(BaseModel):
    client_ip: str
    user_agent: str
    request_path: str
    request_method: str = "GET"
    headers: Dict[str, str] = {}
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    capabilities_required: List[str] = []

@app.on_event("startup")
async def startup_event():
    global router_instance
    
    # Load configuration
    config = {
        'default_strategy': 'adaptive',
        'redis_url': 'redis://localhost:6379',
        'geoip_db_path': 'GeoLite2-City.mmdb',
        'endpoints': [
            {
                'id': 'us-east-1-primary',
                'url': 'https://us-east-1.analytics-platform.com',
                'region': 'us-east-1',
                'provider': 'aws',
                'weight': 100,
                'max_connections': 1000,
                'capabilities': ['analytics', 'real-time', 'ml']
            },
            {
                'id': 'us-west-2-primary',
                'url': 'https://us-west-2.analytics-platform.com',
                'region': 'us-west-2',
                'provider': 'aws',
                'weight': 100,
                'max_connections': 1000,
                'capabilities': ['analytics', 'real-time', 'ml']
            },
            {
                'id': 'eu-west-1-primary',
                'url': 'https://eu-west-1.analytics-platform.com',
                'region': 'eu-west-1',
                'provider': 'aws',
                'weight': 100,
                'max_connections': 1000,
                'capabilities': ['analytics', 'real-time', 'ml']
            },
            {
                'id': 'ap-southeast-1-primary',
                'url': 'https://ap-southeast-1.analytics-platform.com',
                'region': 'ap-southeast-1',
                'provider': 'aws',
                'weight': 100,
                'max_connections': 1000,
                'capabilities': ['analytics', 'real-time', 'ml']
            }
        ]
    }
    
    # Initialize router
    router_instance = IntelligentTrafficRouter(config)
    await router_instance.initialize()
    
    # Start Prometheus metrics server
    start_http_server(8001)
    
    logger.info("Traffic Router started successfully")

@app.post("/route")
async def route_request(request: RouteRequest, background_tasks: BackgroundTasks):
    """Route request to optimal endpoint"""
    if not router_instance:
        raise HTTPException(status_code=503, detail="Router not initialized")
    
    routing_request = RoutingRequest(
        client_ip=request.client_ip,
        user_agent=request.user_agent,
        request_path=request.request_path,
        request_method=request.request_method,
        headers=request.headers,
        timestamp=time.time(),
        user_id=request.user_id,
        session_id=request.session_id,
        capabilities_required=request.capabilities_required
    )
    
    try:
        result = await router_instance.route_request(routing_request)
        
        # Schedule connection release (simulated)
        background_tasks.add_task(
            router_instance.release_connection,
            result.endpoint.id
        )
        
        return {
            'endpoint_id': result.endpoint.id,
            'url': result.endpoint.url,
            'region': result.endpoint.region,
            'provider': result.endpoint.provider,
            'reason': result.reason,
            'confidence': result.confidence,
            'health_score': result.health_score,
            'metadata': result.metadata
        }
        
    except Exception as e:
        logger.error(f"Routing request failed: {e}")
        raise HTTPException(status_code=503, detail=str(e))

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        'status': 'healthy',
        'timestamp': time.time(),
        'endpoints_configured': len(router_instance.endpoints) if router_instance else 0
    }

@app.get("/metrics")
async def get_metrics():
    """Get routing metrics"""
    if not router_instance:
        raise HTTPException(status_code=503, detail="Router not initialized")
    
    return {
        'active_connections': router_instance.active_connections,
        'round_robin_counters': router_instance.round_robin_counters,
        'strategy': router_instance.default_strategy.value,
        'endpoints': [
            {
                'id': ep.id,
                'region': ep.region,
                'provider': ep.provider,
                'active_connections': router_instance.active_connections[ep.id]
            }
            for ep in router_instance.endpoints
        ]
    }

@app.get("/endpoints/{endpoint_id}/health")
async def get_endpoint_health(endpoint_id: str):
    """Get health metrics for specific endpoint"""
    if not router_instance or not router_instance.health_checker:
        raise HTTPException(status_code=503, detail="Router not initialized")
    
    health_metrics = await router_instance.health_checker.get_health_metrics(endpoint_id)
    
    if not health_metrics:
        raise HTTPException(status_code=404, detail="Endpoint not found")
    
    return {
        'endpoint_id': endpoint_id,
        'status': health_metrics.status.value,
        'response_time_ms': health_metrics.response_time_ms,
        'success_rate': health_metrics.success_rate,
        'error_rate': health_metrics.error_rate,
        'cpu_usage': health_metrics.cpu_usage,
        'memory_usage': health_metrics.memory_usage,
        'active_connections': health_metrics.active_connections,
        'last_check': health_metrics.last_check,
        'consecutive_failures': health_metrics.consecutive_failures,
        'health_score': router_instance.health_checker.calculate_health_score(health_metrics)
    }

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)