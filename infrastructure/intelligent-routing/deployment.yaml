apiVersion: v1
kind: Namespace
metadata:
  name: intelligent-routing
  labels:
    name: intelligent-routing

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: traffic-router-config
  namespace: intelligent-routing
data:
  config.yaml: |
    default_strategy: adaptive
    redis_url: redis://redis-service:6379
    geoip_db_path: /data/GeoLite2-City.mmdb
    endpoints:
      - id: us-east-1-primary
        url: https://us-east-1.analytics-platform.com
        region: us-east-1
        provider: aws
        weight: 100
        max_connections: 1000
        capabilities: ["analytics", "real-time", "ml"]
      - id: us-east-1-secondary
        url: https://us-east-1-b.analytics-platform.com
        region: us-east-1
        provider: aws
        weight: 80
        max_connections: 800
        capabilities: ["analytics", "real-time"]
      - id: us-west-2-primary
        url: https://us-west-2.analytics-platform.com
        region: us-west-2
        provider: aws
        weight: 100
        max_connections: 1000
        capabilities: ["analytics", "real-time", "ml"]
      - id: us-west-2-gcp
        url: https://us-west-2.gcp.analytics-platform.com
        region: us-west-2
        provider: gcp
        weight: 90
        max_connections: 900
        capabilities: ["analytics", "real-time", "ml"]
      - id: eu-west-1-primary
        url: https://eu-west-1.analytics-platform.com
        region: eu-west-1
        provider: aws
        weight: 100
        max_connections: 1000
        capabilities: ["analytics", "real-time", "ml"]
      - id: eu-west-1-azure
        url: https://eu-west-1.azure.analytics-platform.com
        region: eu-west-1
        provider: azure
        weight: 85
        max_connections: 850
        capabilities: ["analytics", "real-time"]
      - id: ap-southeast-1-primary
        url: https://ap-southeast-1.analytics-platform.com
        region: ap-southeast-1
        provider: aws
        weight: 100
        max_connections: 1000
        capabilities: ["analytics", "real-time", "ml"]
      - id: ap-southeast-1-gcp
        url: https://ap-southeast-1.gcp.analytics-platform.com
        region: ap-southeast-1
        provider: gcp
        weight: 95
        max_connections: 950
        capabilities: ["analytics", "real-time", "ml"]

---
apiVersion: v1
kind: Secret
metadata:
  name: traffic-router-secrets
  namespace: intelligent-routing
type: Opaque
data:
  redis-password: cGFzc3dvcmQxMjM=  # password123 base64 encoded
  geoip-license-key: eW91cl9nZW9pcF9saWNlbnNlX2tleQ==  # your_geoip_license_key base64 encoded

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: geoip-data-pvc
  namespace: intelligent-routing
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 1Gi
  storageClassName: fast-ssd

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: intelligent-routing
  labels:
    app: redis
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        ports:
        - containerPort: 6379
        env:
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: traffic-router-secrets
              key: redis-password
        command:
        - redis-server
        - --requirepass
        - $(REDIS_PASSWORD)
        - --maxmemory
        - 512mb
        - --maxmemory-policy
        - allkeys-lru
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          tcpSocket:
            port: 6379
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - redis-cli
            - --no-auth-warning
            - -a
            - $(REDIS_PASSWORD)
            - ping
          initialDelaySeconds: 5
          periodSeconds: 5
        volumeMounts:
        - name: redis-data
          mountPath: /data
      volumes:
      - name: redis-data
        emptyDir: {}

---
apiVersion: v1
kind: Service
metadata:
  name: redis-service
  namespace: intelligent-routing
spec:
  selector:
    app: redis
  ports:
  - port: 6379
    targetPort: 6379
  type: ClusterIP

---
apiVersion: batch/v1
kind: Job
metadata:
  name: geoip-downloader
  namespace: intelligent-routing
spec:
  template:
    spec:
      restartPolicy: OnFailure
      containers:
      - name: geoip-downloader
        image: curlimages/curl:latest
        env:
        - name: GEOIP_LICENSE_KEY
          valueFrom:
            secretKeyRef:
              name: traffic-router-secrets
              key: geoip-license-key
        command:
        - /bin/sh
        - -c
        - |
          echo "Downloading GeoLite2 database..."
          curl -o /data/GeoLite2-City.tar.gz \
            "https://download.maxmind.com/app/geoip_download?edition_id=GeoLite2-City&license_key=${GEOIP_LICENSE_KEY}&suffix=tar.gz"
          
          cd /data
          tar -xzf GeoLite2-City.tar.gz
          mv GeoLite2-City_*/GeoLite2-City.mmdb .
          rm -rf GeoLite2-City_* GeoLite2-City.tar.gz
          
          echo "GeoLite2 database downloaded successfully"
          ls -la /data/
        volumeMounts:
        - name: geoip-data
          mountPath: /data
      volumes:
      - name: geoip-data
        persistentVolumeClaim:
          claimName: geoip-data-pvc

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: traffic-router
  namespace: intelligent-routing
  labels:
    app: traffic-router
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  selector:
    matchLabels:
      app: traffic-router
  template:
    metadata:
      labels:
        app: traffic-router
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8001"
        prometheus.io/path: "/metrics"
    spec:
      initContainers:
      - name: wait-for-redis
        image: busybox:1.35
        command:
        - sh
        - -c
        - |
          echo "Waiting for Redis to be ready..."
          until nc -z redis-service 6379; do
            echo "Redis not ready, waiting..."
            sleep 2
          done
          echo "Redis is ready!"
      - name: wait-for-geoip
        image: busybox:1.35
        command:
        - sh
        - -c
        - |
          echo "Waiting for GeoIP database..."
          while [ ! -f /data/GeoLite2-City.mmdb ]; do
            echo "GeoIP database not found, waiting..."
            sleep 5
          done
          echo "GeoIP database is ready!"
        volumeMounts:
        - name: geoip-data
          mountPath: /data
      containers:
      - name: traffic-router
        image: analytics-platform/traffic-router:latest
        ports:
        - containerPort: 8000
          name: http
        - containerPort: 8001
          name: metrics
        env:
        - name: REDIS_URL
          value: "redis://:$(REDIS_PASSWORD)@redis-service:6379"
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: traffic-router-secrets
              key: redis-password
        - name: GEOIP_DB_PATH
          value: "/data/GeoLite2-City.mmdb"
        - name: LOG_LEVEL
          value: "INFO"
        - name: PROMETHEUS_PORT
          value: "8001"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 2
        volumeMounts:
        - name: config
          mountPath: /app/config
        - name: geoip-data
          mountPath: /data
        - name: tmp
          mountPath: /tmp
      volumes:
      - name: config
        configMap:
          name: traffic-router-config
      - name: geoip-data
        persistentVolumeClaim:
          claimName: geoip-data-pvc
      - name: tmp
        emptyDir: {}
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - traffic-router
              topologyKey: kubernetes.io/hostname

---
apiVersion: v1
kind: Service
metadata:
  name: traffic-router-service
  namespace: intelligent-routing
  labels:
    app: traffic-router
spec:
  selector:
    app: traffic-router
  ports:
  - name: http
    port: 80
    targetPort: 8000
    protocol: TCP
  - name: metrics
    port: 8001
    targetPort: 8001
    protocol: TCP
  type: ClusterIP

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: traffic-router-ingress
  namespace: intelligent-routing
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /$1
    nginx.ingress.kubernetes.io/configuration-snippet: |
      more_set_headers "X-Router-Version: 1.0.0";
      more_set_headers "X-Content-Type-Options: nosniff";
      more_set_headers "X-Frame-Options: DENY";
    cert-manager.io/cluster-issuer: letsencrypt-prod
spec:
  tls:
  - hosts:
    - router.analytics-platform.com
    secretName: traffic-router-tls
  rules:
  - host: router.analytics-platform.com
    http:
      paths:
      - path: /(.*)
        pathType: Prefix
        backend:
          service:
            name: traffic-router-service
            port:
              number: 80

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: traffic-router-hpa
  namespace: intelligent-routing
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: traffic-router
  minReplicas: 3
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 15
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60

---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: traffic-router-pdb
  namespace: intelligent-routing
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: traffic-router

---
apiVersion: v1
kind: Service
metadata:
  name: traffic-router-metrics
  namespace: intelligent-routing
  labels:
    app: traffic-router
    monitoring: prometheus
spec:
  selector:
    app: traffic-router
  ports:
  - name: metrics
    port: 8001
    targetPort: 8001
  type: ClusterIP

---
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: traffic-router-monitor
  namespace: intelligent-routing
  labels:
    app: traffic-router
    release: prometheus
spec:
  selector:
    matchLabels:
      app: traffic-router
      monitoring: prometheus
  endpoints:
  - port: metrics
    interval: 30s
    path: /metrics
    scrapeTimeout: 10s

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-dashboard-traffic-router
  namespace: intelligent-routing
  labels:
    grafana_dashboard: "1"
data:
  traffic-router-dashboard.json: |
    {
      "dashboard": {
        "id": null,
        "title": "Intelligent Traffic Router",
        "tags": ["routing", "traffic", "analytics"],
        "style": "dark",
        "timezone": "browser",
        "panels": [
          {
            "id": 1,
            "title": "Request Rate",
            "type": "graph",
            "targets": [
              {
                "expr": "rate(traffic_router_requests_total[5m])",
                "legendFormat": "{{region}} - {{provider}}"
              }
            ],
            "yAxes": [
              {
                "label": "Requests/sec"
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}
          },
          {
            "id": 2,
            "title": "Response Latency",
            "type": "graph",
            "targets": [
              {
                "expr": "histogram_quantile(0.95, traffic_router_latency_seconds_bucket)",
                "legendFormat": "95th percentile"
              },
              {
                "expr": "histogram_quantile(0.50, traffic_router_latency_seconds_bucket)",
                "legendFormat": "50th percentile"
              }
            ],
            "yAxes": [
              {
                "label": "Latency (seconds)"
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}
          },
          {
            "id": 3,
            "title": "Active Connections",
            "type": "graph",
            "targets": [
              {
                "expr": "traffic_router_active_connections",
                "legendFormat": "{{region}} - {{provider}}"
              }
            ],
            "yAxes": [
              {
                "label": "Connections"
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}
          },
          {
            "id": 4,
            "title": "Health Scores",
            "type": "graph",
            "targets": [
              {
                "expr": "traffic_router_health_score",
                "legendFormat": "{{region}} - {{provider}}"
              }
            ],
            "yAxes": [
              {
                "label": "Health Score",
                "min": 0,
                "max": 1
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}
          }
        ],
        "time": {
          "from": "now-1h",
          "to": "now"
        },
        "refresh": "30s"
      }
    }

---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: geoip-updater
  namespace: intelligent-routing
spec:
  schedule: "0 2 * * 0"  # Weekly at 2 AM on Sunday
  jobTemplate:
    spec:
      template:
        spec:
          restartPolicy: OnFailure
          containers:
          - name: geoip-updater
            image: curlimages/curl:latest
            env:
            - name: GEOIP_LICENSE_KEY
              valueFrom:
                secretKeyRef:
                  name: traffic-router-secrets
                  key: geoip-license-key
            command:
            - /bin/sh
            - -c
            - |
              echo "Updating GeoLite2 database..."
              
              # Download new database
              curl -o /tmp/GeoLite2-City.tar.gz \
                "https://download.maxmind.com/app/geoip_download?edition_id=GeoLite2-City&license_key=${GEOIP_LICENSE_KEY}&suffix=tar.gz"
              
              cd /tmp
              tar -xzf GeoLite2-City.tar.gz
              
              # Backup current database
              cp /data/GeoLite2-City.mmdb /data/GeoLite2-City.mmdb.backup
              
              # Install new database
              mv GeoLite2-City_*/GeoLite2-City.mmdb /data/
              
              # Cleanup
              rm -rf GeoLite2-City_* GeoLite2-City.tar.gz
              
              echo "GeoLite2 database updated successfully"
              ls -la /data/
            volumeMounts:
            - name: geoip-data
              mountPath: /data
          volumes:
          - name: geoip-data
            persistentVolumeClaim:
              claimName: geoip-data-pvc

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: traffic-router-nginx-config
  namespace: intelligent-routing
data:
  nginx.conf: |
    upstream traffic_router_backend {
        least_conn;
        server traffic-router-service:80 max_fails=3 fail_timeout=30s;
    }
    
    map $http_x_forwarded_for $client_real_ip {
        default $remote_addr;
        ~^(?P<firstaddr>[0-9\.]+),?.*$ $firstaddr;
    }
    
    server {
        listen 80;
        server_name router.analytics-platform.com;
        
        # Security headers
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Strict-Transport-Security "max-age=********; includeSubDomains";
        
        # Rate limiting
        limit_req_zone $client_real_ip zone=router_rate_limit:10m rate=100r/s;
        limit_req zone=router_rate_limit burst=200 nodelay;
        
        # Connection limiting
        limit_conn_zone $client_real_ip zone=router_conn_limit:10m;
        limit_conn router_conn_limit 20;
        
        location / {
            proxy_pass http://traffic_router_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $client_real_ip;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Timeouts
            proxy_connect_timeout 5s;
            proxy_send_timeout 10s;
            proxy_read_timeout 10s;
            
            # Buffering
            proxy_buffering on;
            proxy_buffer_size 4k;
            proxy_buffers 8 4k;
            
            # Health check
            proxy_next_upstream error timeout invalid_header http_500 http_502 http_503;
            proxy_next_upstream_tries 3;
            proxy_next_upstream_timeout 10s;
        }
        
        location /health {
            access_log off;
            proxy_pass http://traffic_router_backend/health;
            proxy_set_header Host $host;
        }
        
        location /metrics {
            access_log off;
            allow 10.0.0.0/8;
            allow **********/12;
            allow ***********/16;
            deny all;
            
            proxy_pass http://traffic_router_backend:8001/metrics;
            proxy_set_header Host $host;
        }
    }

---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: traffic-router-network-policy
  namespace: intelligent-routing
spec:
  podSelector:
    matchLabels:
      app: traffic-router
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    ports:
    - protocol: TCP
      port: 8000
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    ports:
    - protocol: TCP
      port: 8001
  egress:
  - to:
    - podSelector:
        matchLabels:
          app: redis
    ports:
    - protocol: TCP
      port: 6379
  - to: []
    ports:
    - protocol: TCP
      port: 53
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 80