#!/usr/bin/env python3
"""
Webhook Event Consumer and Handler
Consumes events from Kafka and triggers webhook deliveries
"""

import asyncio
import logging
import json
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any
import os

# Kafka consumer
from aiokafka import AIOKafkaConsumer
import aiokafka.errors

# HTTP client
import httpx

# Event processing
from webhook_delivery_system import (
    WebhookManager, WebhookConfig, WebhookEvent, WebhookEventType
)

# Configuration
KAFKA_BOOTSTRAP_SERVERS = os.getenv('KAFKA_BOOTSTRAP_SERVERS', 'kafka:9092')
KAFKA_CONSUMER_GROUP = os.getenv('KAFKA_CONSUMER_GROUP', 'webhook-consumer-group')
WEBHOOK_TOPICS = [
    'user-interactions',
    'transactions', 
    'product-events',
    'customer-events',
    'ml-predictions',
    'recommendations',
    'fraud-alerts',
    'system-metrics',
    'analytics-queries',
    'notifications'
]

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class WebhookEventMapper:
    """Maps Kafka events to webhook events"""
    
    # Mapping from Kafka topics to webhook event types
    TOPIC_TO_EVENT_TYPE = {
        'user-interactions': WebhookEventType.USER_UPDATED,
        'transactions': WebhookEventType.ORDER_PLACED,
        'product-events': WebhookEventType.PRODUCT_UPDATED,
        'customer-events': WebhookEventType.USER_UPDATED,
        'ml-predictions': WebhookEventType.ML_PREDICTION_COMPLETE,
        'recommendations': WebhookEventType.RECOMMENDATION_UPDATED,
        'fraud-alerts': WebhookEventType.FRAUD_DETECTED,
        'analytics-queries': WebhookEventType.ANALYTICS_REPORT_READY,
        'notifications': WebhookEventType.SYSTEM_HEALTH_ALERT
    }
    
    @staticmethod
    def map_kafka_event_to_webhook(topic: str, kafka_message: Dict[str, Any]) -> Optional[WebhookEvent]:
        """Convert Kafka message to webhook event"""
        try:
            # Determine event type from topic and message content
            event_type = WebhookEventMapper.TOPIC_TO_EVENT_TYPE.get(topic)
            
            if not event_type:
                logger.warning(f"No webhook event type mapping for topic: {topic}")
                return None
            
            # Handle specific event type mappings based on message content
            if topic == 'transactions':
                if kafka_message.get('eventType') == 'order.created':
                    event_type = WebhookEventType.ORDER_PLACED
                elif kafka_message.get('eventType') == 'order.updated':
                    event_type = WebhookEventType.ORDER_UPDATED
                elif kafka_message.get('eventType') == 'order.cancelled':
                    event_type = WebhookEventType.ORDER_CANCELLED
                elif kafka_message.get('eventType') == 'order.completed':
                    event_type = WebhookEventType.ORDER_COMPLETED
                elif kafka_message.get('eventType') == 'payment.completed':
                    event_type = WebhookEventType.PAYMENT_PROCESSED
                elif kafka_message.get('eventType') == 'payment.failed':
                    event_type = WebhookEventType.PAYMENT_FAILED
            
            elif topic == 'customer-events':
                if kafka_message.get('eventType') == 'customer.registered':
                    event_type = WebhookEventType.USER_CREATED
                elif kafka_message.get('eventType') == 'customer.updated':
                    event_type = WebhookEventType.USER_UPDATED
                elif kafka_message.get('eventType') == 'customer.deleted':
                    event_type = WebhookEventType.USER_DELETED
            
            elif topic == 'product-events':
                if kafka_message.get('eventType') == 'product.created':
                    event_type = WebhookEventType.PRODUCT_CREATED
                elif kafka_message.get('eventType') == 'product.updated':
                    event_type = WebhookEventType.PRODUCT_UPDATED
            
            # Create webhook event
            webhook_event = WebhookEvent(
                event_type=event_type,
                payload=kafka_message.get('data', kafka_message),
                metadata={
                    'source_topic': topic,
                    'kafka_partition': kafka_message.get('partition'),
                    'kafka_offset': kafka_message.get('offset'),
                    'original_timestamp': kafka_message.get('timestamp'),
                    'message_id': kafka_message.get('eventId') or kafka_message.get('id')
                }
            )
            
            return webhook_event
            
        except Exception as e:
            logger.error(f"Error mapping Kafka event to webhook: {str(e)}")
            return None

class WebhookEventConsumer:
    """Kafka consumer for webhook events"""
    
    def __init__(self, webhook_manager: WebhookManager):
        self.webhook_manager = webhook_manager
        self.consumer = None
        self.running = False
        
    async def initialize(self):
        """Initialize Kafka consumer"""
        try:
            self.consumer = AIOKafkaConsumer(
                *WEBHOOK_TOPICS,
                bootstrap_servers=KAFKA_BOOTSTRAP_SERVERS,
                group_id=KAFKA_CONSUMER_GROUP,
                auto_offset_reset='latest',
                enable_auto_commit=True,
                auto_commit_interval_ms=1000,
                value_deserializer=lambda x: json.loads(x.decode('utf-8'))
            )
            
            await self.consumer.start()
            logger.info(f"Webhook event consumer initialized for topics: {WEBHOOK_TOPICS}")
            
        except Exception as e:
            logger.error(f"Error initializing webhook consumer: {str(e)}")
            raise
    
    async def start_consuming(self):
        """Start consuming events from Kafka"""
        self.running = True
        logger.info("Starting webhook event consumption")
        
        try:
            async for message in self.consumer:
                if not self.running:
                    break
                
                try:
                    await self._process_message(message)
                except Exception as e:
                    logger.error(f"Error processing message: {str(e)}")
                    
        except Exception as e:
            logger.error(f"Error in consumption loop: {str(e)}")
        finally:
            await self.consumer.stop()
    
    async def _process_message(self, message):
        """Process a single Kafka message"""
        try:
            topic = message.topic
            kafka_data = message.value
            
            # Add Kafka metadata to the message
            kafka_data.update({
                'partition': message.partition,
                'offset': message.offset,
                'timestamp': message.timestamp
            })
            
            logger.debug(f"Processing message from topic {topic}: {kafka_data}")
            
            # Map to webhook event
            webhook_event = WebhookEventMapper.map_kafka_event_to_webhook(topic, kafka_data)
            
            if webhook_event:
                # Trigger webhook delivery
                delivery_ids = await self.webhook_manager.trigger_webhook(webhook_event)
                
                if delivery_ids:
                    logger.info(f"Triggered {len(delivery_ids)} webhook deliveries for event {webhook_event.id}")
                else:
                    logger.debug(f"No webhook endpoints configured for event type: {webhook_event.event_type.value}")
            else:
                logger.warning(f"Could not map Kafka message to webhook event: {topic}")
                
        except Exception as e:
            logger.error(f"Error processing Kafka message: {str(e)}")
    
    async def stop_consuming(self):
        """Stop consuming events"""
        self.running = False
        if self.consumer:
            await self.consumer.stop()
        logger.info("Stopped webhook event consumption")

class WebhookIntegrationService:
    """Service for managing webhook integrations with third-party systems"""
    
    def __init__(self, webhook_manager: WebhookManager):
        self.webhook_manager = webhook_manager
        self.http_client = httpx.AsyncClient(timeout=30.0)
        
    async def setup_default_integrations(self):
        """Set up default webhook integrations"""
        try:
            # Example integrations - these would be configured based on customer needs
            
            # Slack notifications for fraud alerts
            slack_endpoint = WebhookEndpoint(
                name="Slack Fraud Alerts",
                url="https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK",
                secret_key="slack-webhook-secret",
                event_types=[WebhookEventType.FRAUD_DETECTED],
                headers={
                    "Content-Type": "application/json"
                }
            )
            
            # Customer CRM system for user events
            crm_endpoint = WebhookEndpoint(
                name="CRM User Sync",
                url="https://api.crm-system.com/webhooks/users",
                secret_key="crm-webhook-secret",
                event_types=[
                    WebhookEventType.USER_CREATED,
                    WebhookEventType.USER_UPDATED,
                    WebhookEventType.USER_DELETED
                ],
                headers={
                    "Authorization": "Bearer YOUR_CRM_API_TOKEN",
                    "Content-Type": "application/json"
                }
            )
            
            # Analytics dashboard for ML predictions
            analytics_endpoint = WebhookEndpoint(
                name="Analytics Dashboard",
                url="https://dashboard.analytics-platform.com/api/webhooks/ml",
                secret_key="analytics-webhook-secret",
                event_types=[
                    WebhookEventType.ML_PREDICTION_COMPLETE,
                    WebhookEventType.ANALYTICS_REPORT_READY
                ]
            )
            
            # E-commerce platform for order events
            ecommerce_endpoint = WebhookEndpoint(
                name="E-commerce Platform",
                url="https://api.ecommerce-platform.com/webhooks/orders",
                secret_key="ecommerce-webhook-secret",
                event_types=[
                    WebhookEventType.ORDER_PLACED,
                    WebhookEventType.ORDER_UPDATED,
                    WebhookEventType.ORDER_CANCELLED,
                    WebhookEventType.ORDER_COMPLETED
                ],
                timeout=60,
                max_retries=3
            )
            
            # Create all endpoints
            endpoints = [slack_endpoint, crm_endpoint, analytics_endpoint, ecommerce_endpoint]
            
            for endpoint in endpoints:
                success = await self.webhook_manager.create_endpoint(endpoint)
                if success:
                    logger.info(f"Created default integration: {endpoint.name}")
                else:
                    logger.error(f"Failed to create integration: {endpoint.name}")
                    
        except Exception as e:
            logger.error(f"Error setting up default integrations: {str(e)}")
    
    async def test_webhook_endpoint(self, endpoint_id: str) -> Dict[str, Any]:
        """Test a webhook endpoint with a sample payload"""
        try:
            endpoint = await self.webhook_manager.database.get_endpoint(endpoint_id)
            if not endpoint:
                return {"success": False, "error": "Endpoint not found"}
            
            # Create test event
            test_event = WebhookEvent(
                event_type=WebhookEventType.SYSTEM_HEALTH_ALERT,
                payload={
                    "test": True,
                    "message": "This is a test webhook delivery",
                    "timestamp": datetime.now(timezone.utc).isoformat()
                },
                metadata={
                    "test_delivery": True,
                    "endpoint_id": endpoint_id
                }
            )
            
            # Trigger webhook
            delivery_ids = await self.webhook_manager.trigger_webhook(test_event)
            
            if delivery_ids:
                return {
                    "success": True,
                    "test_event_id": test_event.id,
                    "delivery_ids": delivery_ids
                }
            else:
                return {
                    "success": False,
                    "error": "No deliveries created"
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    async def close(self):
        """Close integration service resources"""
        await self.http_client.aclose()

async def main():
    """Main function to run the webhook consumer service"""
    
    # Initialize webhook manager
    config = WebhookConfig()
    webhook_manager = WebhookManager(config)
    await webhook_manager.initialize()
    await webhook_manager.start_processing()
    
    # Initialize integration service
    integration_service = WebhookIntegrationService(webhook_manager)
    
    # Set up default integrations (uncomment for initial setup)
    # await integration_service.setup_default_integrations()
    
    # Initialize and start consumer
    consumer = WebhookEventConsumer(webhook_manager)
    await consumer.initialize()
    
    try:
        logger.info("Starting webhook event consumer service...")
        await consumer.start_consuming()
        
    except KeyboardInterrupt:
        logger.info("Received shutdown signal")
    except Exception as e:
        logger.error(f"Error in main loop: {str(e)}")
    finally:
        # Cleanup
        await consumer.stop_consuming()
        await integration_service.close()
        await webhook_manager.close()
        logger.info("Webhook consumer service stopped")

if __name__ == "__main__":
    asyncio.run(main())