#!/usr/bin/env python3
"""
Reliable Webhook Delivery System for E-commerce Analytics Platform
High-reliability webhook delivery with retry mechanisms, dead letter queues, and monitoring
"""

import asyncio
import logging
import json
import uuid
import hashlib
import hmac
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any, Union, Callable
from dataclasses import dataclass, field
from enum import Enum
import os
import base64

# HTTP client and server
import httpx
from fastapi import FastAPI, HTTPException, BackgroundTasks, Depends, Header
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
import uvicorn

# Database and storage
import asyncpg
import aioredis

# Message queues
from celery import Celery
import aiokafka
from aiokafka import AIOKafkaProducer, AIOKafkaConsumer

# Validation and serialization
from pydantic import BaseModel, validator, Field
import orjson

# Cryptography for webhook signatures
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.hmac import HMAC

# Monitoring and observability
import time
from collections import defaultdict
import prometheus_client
from prometheus_client import Counter, Histogram, Gauge

# Configuration
@dataclass
class WebhookConfig:
    # System settings
    service_host: str = "0.0.0.0"
    service_port: int = 8080
    worker_concurrency: int = 10
    
    # Database configuration
    db_host: str = os.getenv('DB_HOST', 'postgres-webhooks')
    db_name: str = os.getenv('DB_NAME', 'webhook_system')
    db_user: str = os.getenv('DB_USER', 'webhook_user')
    db_password: str = os.getenv('DB_PASSWORD', 'webhook_password')
    
    # Redis for queuing and caching
    redis_url: str = os.getenv('REDIS_URL', 'redis://redis-cluster:6379/6')
    
    # Kafka for event streaming
    kafka_bootstrap_servers: str = os.getenv('KAFKA_BOOTSTRAP_SERVERS', 'kafka:9092')
    kafka_topic_webhooks: str = 'webhook-events'
    kafka_topic_dlq: str = 'webhook-dead-letter-queue'
    
    # Retry configuration
    max_retry_attempts: int = 5
    initial_retry_delay: int = 30  # seconds
    max_retry_delay: int = 3600  # 1 hour
    retry_exponential_base: float = 2.0
    
    # Delivery settings
    delivery_timeout: int = 30  # seconds
    max_payload_size: int = 1024 * 1024  # 1MB
    signature_header: str = "X-Webhook-Signature"
    timestamp_header: str = "X-Webhook-Timestamp"
    event_type_header: str = "X-Webhook-Event-Type"
    
    # Security
    default_secret_key: str = os.getenv('WEBHOOK_SECRET_KEY', 'default-webhook-secret-change-in-production')
    signature_tolerance: int = 300  # 5 minutes tolerance for timestamp validation
    
    # Performance
    batch_size: int = 100
    processing_interval: int = 5  # seconds
    
    # Monitoring
    enable_metrics: bool = True
    slow_delivery_threshold: int = 5000  # 5 seconds

# Webhook models
class WebhookEventType(Enum):
    USER_CREATED = "user.created"
    USER_UPDATED = "user.updated"
    USER_DELETED = "user.deleted"
    ORDER_PLACED = "order.placed"
    ORDER_UPDATED = "order.updated"
    ORDER_CANCELLED = "order.cancelled"
    ORDER_COMPLETED = "order.completed"
    PAYMENT_PROCESSED = "payment.processed"
    PAYMENT_FAILED = "payment.failed"
    PRODUCT_CREATED = "product.created"
    PRODUCT_UPDATED = "product.updated"
    FRAUD_DETECTED = "fraud.detected"
    ANALYTICS_REPORT_READY = "analytics.report_ready"
    ML_PREDICTION_COMPLETE = "ml.prediction_complete"
    RECOMMENDATION_UPDATED = "recommendation.updated"
    SYSTEM_HEALTH_ALERT = "system.health_alert"

class WebhookStatus(Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    DELIVERED = "delivered"
    FAILED = "failed"
    EXPIRED = "expired"

class WebhookEndpointStatus(Enum):
    ACTIVE = "active"
    PAUSED = "paused"
    DISABLED = "disabled"

# Pydantic models
class WebhookEndpoint(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    url: str
    secret_key: str
    event_types: List[WebhookEventType]
    status: WebhookEndpointStatus = WebhookEndpointStatus.ACTIVE
    headers: Optional[Dict[str, str]] = None
    timeout: int = 30
    max_retries: int = 5
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            WebhookEventType: lambda v: v.value,
            WebhookEndpointStatus: lambda v: v.value
        }

class WebhookEvent(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    event_type: WebhookEventType
    payload: Dict[str, Any]
    metadata: Optional[Dict[str, Any]] = None
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            WebhookEventType: lambda v: v.value
        }

class WebhookDelivery(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    endpoint_id: str
    event_id: str
    status: WebhookStatus = WebhookStatus.PENDING
    attempt_count: int = 0
    next_retry_at: Optional[datetime] = None
    response_status_code: Optional[int] = None
    response_body: Optional[str] = None
    error_message: Optional[str] = None
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    delivered_at: Optional[datetime] = None
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            WebhookStatus: lambda v: v.value
        }

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Metrics
if prometheus_client.REGISTRY._names_to_collectors:
    # Clear existing metrics to avoid conflicts
    prometheus_client.REGISTRY._names_to_collectors.clear()

webhook_deliveries_total = Counter(
    'webhook_deliveries_total',
    'Total number of webhook deliveries',
    ['status', 'event_type']
)

webhook_delivery_duration = Histogram(
    'webhook_delivery_duration_seconds',
    'Time spent delivering webhooks',
    ['endpoint_id', 'event_type']
)

webhook_retry_count = Counter(
    'webhook_retries_total',
    'Total number of webhook retry attempts',
    ['endpoint_id', 'attempt']
)

active_webhooks = Gauge(
    'active_webhooks_count',
    'Number of active webhook endpoints'
)

pending_deliveries = Gauge(
    'pending_webhook_deliveries',
    'Number of pending webhook deliveries'
)

class WebhookSigner:
    """Handles webhook signature generation and verification"""
    
    @staticmethod
    def generate_signature(payload: bytes, secret: str, timestamp: str) -> str:
        """Generate HMAC-SHA256 signature for webhook payload"""
        # Create the signed payload string
        signed_payload = f"{timestamp}.{payload.decode('utf-8')}"
        
        # Generate HMAC signature
        mac = hmac.new(
            secret.encode('utf-8'),
            signed_payload.encode('utf-8'),
            hashlib.sha256
        )
        
        return f"sha256={mac.hexdigest()}"
    
    @staticmethod
    def verify_signature(payload: bytes, signature: str, secret: str, timestamp: str) -> bool:
        """Verify webhook signature"""
        try:
            expected_signature = WebhookSigner.generate_signature(payload, secret, timestamp)
            return hmac.compare_digest(signature, expected_signature)
        except Exception as e:
            logger.error(f"Error verifying webhook signature: {str(e)}")
            return False

class WebhookDatabase:
    """Database operations for webhook system"""
    
    def __init__(self, config: WebhookConfig):
        self.config = config
        self.db_pool = None
    
    async def initialize(self):
        """Initialize database connection and create tables"""
        try:
            self.db_pool = await asyncpg.create_pool(
                host=self.config.db_host,
                database=self.config.db_name,
                user=self.config.db_user,
                password=self.config.db_password,
                min_size=5,
                max_size=20
            )
            
            await self._create_tables()
            logger.info("Webhook database initialized")
            
        except Exception as e:
            logger.error(f"Error initializing webhook database: {str(e)}")
            raise
    
    async def _create_tables(self):
        """Create webhook system tables"""
        async with self.db_pool.acquire() as conn:
            # Webhook endpoints table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS webhook_endpoints (
                    id VARCHAR(255) PRIMARY KEY,
                    name VARCHAR(255) NOT NULL,
                    url TEXT NOT NULL,
                    secret_key VARCHAR(255) NOT NULL,
                    event_types TEXT[] NOT NULL,
                    status VARCHAR(50) NOT NULL DEFAULT 'active',
                    headers JSONB,
                    timeout INTEGER DEFAULT 30,
                    max_retries INTEGER DEFAULT 5,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                )
            """)
            
            # Webhook events table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS webhook_events (
                    id VARCHAR(255) PRIMARY KEY,
                    event_type VARCHAR(100) NOT NULL,
                    payload JSONB NOT NULL,
                    metadata JSONB,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                )
            """)
            
            # Webhook deliveries table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS webhook_deliveries (
                    id VARCHAR(255) PRIMARY KEY,
                    endpoint_id VARCHAR(255) NOT NULL REFERENCES webhook_endpoints(id),
                    event_id VARCHAR(255) NOT NULL REFERENCES webhook_events(id),
                    status VARCHAR(50) NOT NULL DEFAULT 'pending',
                    attempt_count INTEGER DEFAULT 0,
                    next_retry_at TIMESTAMP WITH TIME ZONE,
                    response_status_code INTEGER,
                    response_body TEXT,
                    error_message TEXT,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    delivered_at TIMESTAMP WITH TIME ZONE
                )
            """)
            
            # Create indexes
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_deliveries_status ON webhook_deliveries(status)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_deliveries_next_retry ON webhook_deliveries(next_retry_at)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_deliveries_endpoint ON webhook_deliveries(endpoint_id)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_events_type ON webhook_events(event_type)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_endpoints_status ON webhook_endpoints(status)")
    
    async def save_endpoint(self, endpoint: WebhookEndpoint) -> bool:
        """Save webhook endpoint"""
        try:
            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO webhook_endpoints 
                    (id, name, url, secret_key, event_types, status, headers, timeout, max_retries, created_at, updated_at)
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
                    ON CONFLICT (id) DO UPDATE SET
                    name = EXCLUDED.name,
                    url = EXCLUDED.url,
                    secret_key = EXCLUDED.secret_key,
                    event_types = EXCLUDED.event_types,
                    status = EXCLUDED.status,
                    headers = EXCLUDED.headers,
                    timeout = EXCLUDED.timeout,
                    max_retries = EXCLUDED.max_retries,
                    updated_at = EXCLUDED.updated_at
                """, 
                    endpoint.id, endpoint.name, endpoint.url, endpoint.secret_key,
                    [et.value for et in endpoint.event_types], endpoint.status.value,
                    json.dumps(endpoint.headers) if endpoint.headers else None,
                    endpoint.timeout, endpoint.max_retries,
                    endpoint.created_at, endpoint.updated_at
                )
                return True
        except Exception as e:
            logger.error(f"Error saving webhook endpoint: {str(e)}")
            return False
    
    async def get_endpoint(self, endpoint_id: str) -> Optional[WebhookEndpoint]:
        """Get webhook endpoint by ID"""
        try:
            async with self.db_pool.acquire() as conn:
                row = await conn.fetchrow(
                    "SELECT * FROM webhook_endpoints WHERE id = $1", endpoint_id
                )
                if row:
                    return WebhookEndpoint(
                        id=row['id'],
                        name=row['name'],
                        url=row['url'],
                        secret_key=row['secret_key'],
                        event_types=[WebhookEventType(et) for et in row['event_types']],
                        status=WebhookEndpointStatus(row['status']),
                        headers=json.loads(row['headers']) if row['headers'] else None,
                        timeout=row['timeout'],
                        max_retries=row['max_retries'],
                        created_at=row['created_at'],
                        updated_at=row['updated_at']
                    )
                return None
        except Exception as e:
            logger.error(f"Error getting webhook endpoint: {str(e)}")
            return None
    
    async def save_event(self, event: WebhookEvent) -> bool:
        """Save webhook event"""
        try:
            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO webhook_events (id, event_type, payload, metadata, created_at)
                    VALUES ($1, $2, $3, $4, $5)
                """, 
                    event.id, event.event_type.value, 
                    json.dumps(event.payload), 
                    json.dumps(event.metadata) if event.metadata else None,
                    event.created_at
                )
                return True
        except Exception as e:
            logger.error(f"Error saving webhook event: {str(e)}")
            return False
    
    async def save_delivery(self, delivery: WebhookDelivery) -> bool:
        """Save webhook delivery"""
        try:
            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO webhook_deliveries 
                    (id, endpoint_id, event_id, status, attempt_count, next_retry_at, 
                     response_status_code, response_body, error_message, created_at, updated_at, delivered_at)
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
                    ON CONFLICT (id) DO UPDATE SET
                    status = EXCLUDED.status,
                    attempt_count = EXCLUDED.attempt_count,
                    next_retry_at = EXCLUDED.next_retry_at,
                    response_status_code = EXCLUDED.response_status_code,
                    response_body = EXCLUDED.response_body,
                    error_message = EXCLUDED.error_message,
                    updated_at = EXCLUDED.updated_at,
                    delivered_at = EXCLUDED.delivered_at
                """, 
                    delivery.id, delivery.endpoint_id, delivery.event_id, delivery.status.value,
                    delivery.attempt_count, delivery.next_retry_at, delivery.response_status_code,
                    delivery.response_body, delivery.error_message, delivery.created_at,
                    delivery.updated_at, delivery.delivered_at
                )
                return True
        except Exception as e:
            logger.error(f"Error saving webhook delivery: {str(e)}")
            return False
    
    async def get_pending_deliveries(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Get pending webhook deliveries ready for retry"""
        try:
            async with self.db_pool.acquire() as conn:
                rows = await conn.fetch("""
                    SELECT d.*, e.event_type, e.payload, e.metadata, 
                           ep.url, ep.secret_key, ep.headers, ep.timeout
                    FROM webhook_deliveries d
                    JOIN webhook_events e ON d.event_id = e.id
                    JOIN webhook_endpoints ep ON d.endpoint_id = ep.id
                    WHERE d.status IN ('pending', 'failed') 
                    AND (d.next_retry_at IS NULL OR d.next_retry_at <= NOW())
                    AND d.attempt_count < ep.max_retries
                    AND ep.status = 'active'
                    ORDER BY d.created_at
                    LIMIT $1
                """, limit)
                
                return [dict(row) for row in rows]
        except Exception as e:
            logger.error(f"Error getting pending deliveries: {str(e)}")
            return []
    
    async def close(self):
        """Close database connection"""
        if self.db_pool:
            await self.db_pool.close()

class WebhookDeliveryEngine:
    """Core webhook delivery engine with retry logic"""
    
    def __init__(self, config: WebhookConfig, database: WebhookDatabase):
        self.config = config
        self.database = database
        self.http_client = httpx.AsyncClient(timeout=config.delivery_timeout)
        self.redis = None
        self.kafka_producer = None
    
    async def initialize(self):
        """Initialize delivery engine"""
        try:
            # Initialize Redis
            self.redis = aioredis.from_url(self.config.redis_url)
            
            # Initialize Kafka producer
            self.kafka_producer = AIOKafkaProducer(
                bootstrap_servers=self.config.kafka_bootstrap_servers,
                value_serializer=lambda v: orjson.dumps(v)
            )
            await self.kafka_producer.start()
            
            logger.info("Webhook delivery engine initialized")
            
        except Exception as e:
            logger.error(f"Error initializing delivery engine: {str(e)}")
            raise
    
    async def deliver_webhook(self, delivery_data: Dict[str, Any]) -> bool:
        """Deliver a single webhook"""
        delivery_id = delivery_data['id']
        endpoint_url = delivery_data['url']
        secret_key = delivery_data['secret_key']
        event_type = delivery_data['event_type']
        payload = delivery_data['payload']
        
        start_time = time.time()
        
        try:
            # Prepare webhook payload
            timestamp = str(int(time.time()))
            headers = {
                'Content-Type': 'application/json',
                self.config.timestamp_header: timestamp,
                self.config.event_type_header: event_type,
                'User-Agent': 'Analytics-Platform-Webhook/1.0'
            }
            
            # Add custom headers
            if delivery_data.get('headers'):
                custom_headers = json.loads(delivery_data['headers'])
                headers.update(custom_headers)
            
            # Prepare payload
            webhook_payload = {
                'id': delivery_data['event_id'],
                'type': event_type,
                'data': payload,
                'timestamp': timestamp
            }
            
            payload_bytes = orjson.dumps(webhook_payload)
            
            # Generate signature
            signature = WebhookSigner.generate_signature(payload_bytes, secret_key, timestamp)
            headers[self.config.signature_header] = signature
            
            # Make HTTP request
            response = await self.http_client.post(
                endpoint_url,
                content=payload_bytes,
                headers=headers,
                timeout=delivery_data.get('timeout', self.config.delivery_timeout)
            )
            
            delivery_time = (time.time() - start_time) * 1000
            
            # Update delivery record
            success = 200 <= response.status_code < 300
            delivery_status = WebhookStatus.DELIVERED if success else WebhookStatus.FAILED
            
            delivery = WebhookDelivery(
                id=delivery_id,
                endpoint_id=delivery_data['endpoint_id'],
                event_id=delivery_data['event_id'],
                status=delivery_status,
                attempt_count=delivery_data['attempt_count'] + 1,
                response_status_code=response.status_code,
                response_body=response.text[:1000],  # Limit response body size
                delivered_at=datetime.now(timezone.utc) if success else None,
                updated_at=datetime.now(timezone.utc)
            )
            
            if not success:
                # Calculate next retry time
                delivery.next_retry_at = self._calculate_next_retry(delivery.attempt_count)
                delivery.error_message = f"HTTP {response.status_code}: {response.text[:500]}"
            
            await self.database.save_delivery(delivery)
            
            # Update metrics
            webhook_deliveries_total.labels(
                status='success' if success else 'failed',
                event_type=event_type
            ).inc()
            
            webhook_delivery_duration.labels(
                endpoint_id=delivery_data['endpoint_id'],
                event_type=event_type
            ).observe(delivery_time / 1000)
            
            if not success:
                webhook_retry_count.labels(
                    endpoint_id=delivery_data['endpoint_id'],
                    attempt=str(delivery.attempt_count)
                ).inc()
            
            logger.info(f"Webhook delivery {'successful' if success else 'failed'}: {delivery_id} -> {endpoint_url} (HTTP {response.status_code}) in {delivery_time:.2f}ms")
            
            return success
            
        except Exception as e:
            delivery_time = (time.time() - start_time) * 1000
            error_message = str(e)
            
            # Update delivery record with error
            delivery = WebhookDelivery(
                id=delivery_id,
                endpoint_id=delivery_data['endpoint_id'],
                event_id=delivery_data['event_id'],
                status=WebhookStatus.FAILED,
                attempt_count=delivery_data['attempt_count'] + 1,
                next_retry_at=self._calculate_next_retry(delivery_data['attempt_count'] + 1),
                error_message=error_message,
                updated_at=datetime.now(timezone.utc)
            )
            
            await self.database.save_delivery(delivery)
            
            # Update metrics
            webhook_deliveries_total.labels(
                status='error',
                event_type=event_type
            ).inc()
            
            webhook_retry_count.labels(
                endpoint_id=delivery_data['endpoint_id'],
                attempt=str(delivery.attempt_count)
            ).inc()
            
            logger.error(f"Webhook delivery error: {delivery_id} -> {endpoint_url}: {error_message}")
            
            return False
    
    def _calculate_next_retry(self, attempt_count: int) -> datetime:
        """Calculate next retry time using exponential backoff"""
        delay = min(
            self.config.initial_retry_delay * (self.config.retry_exponential_base ** (attempt_count - 1)),
            self.config.max_retry_delay
        )
        return datetime.now(timezone.utc) + timedelta(seconds=delay)
    
    async def process_pending_deliveries(self):
        """Process pending webhook deliveries"""
        try:
            pending = await self.database.get_pending_deliveries(self.config.batch_size)
            
            if pending:
                logger.info(f"Processing {len(pending)} pending webhook deliveries")
                
                # Process deliveries concurrently
                semaphore = asyncio.Semaphore(self.config.worker_concurrency)
                
                async def deliver_with_semaphore(delivery_data):
                    async with semaphore:
                        return await self.deliver_webhook(delivery_data)
                
                tasks = [deliver_with_semaphore(delivery) for delivery in pending]
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                successful = sum(1 for r in results if r is True)
                logger.info(f"Delivered {successful}/{len(pending)} webhooks successfully")
            
            # Update metrics
            pending_deliveries.set(len(pending))
            
        except Exception as e:
            logger.error(f"Error processing pending deliveries: {str(e)}")
    
    async def publish_to_dlq(self, delivery_data: Dict[str, Any]):
        """Publish failed delivery to dead letter queue"""
        try:
            dlq_message = {
                'delivery_id': delivery_data['id'],
                'endpoint_id': delivery_data['endpoint_id'],
                'event_id': delivery_data['event_id'],
                'url': delivery_data['url'],
                'event_type': delivery_data['event_type'],
                'payload': delivery_data['payload'],
                'attempt_count': delivery_data['attempt_count'],
                'failed_at': datetime.now(timezone.utc).isoformat(),
                'error_message': delivery_data.get('error_message')
            }
            
            await self.kafka_producer.send(
                self.config.kafka_topic_dlq,
                value=dlq_message
            )
            
            logger.warning(f"Webhook delivery sent to DLQ: {delivery_data['id']}")
            
        except Exception as e:
            logger.error(f"Error publishing to DLQ: {str(e)}")
    
    async def close(self):
        """Close delivery engine resources"""
        try:
            await self.http_client.aclose()
            if self.redis:
                await self.redis.close()
            if self.kafka_producer:
                await self.kafka_producer.stop()
        except Exception as e:
            logger.error(f"Error closing delivery engine: {str(e)}")

class WebhookManager:
    """High-level webhook management interface"""
    
    def __init__(self, config: WebhookConfig):
        self.config = config
        self.database = WebhookDatabase(config)
        self.delivery_engine = WebhookDeliveryEngine(config, self.database)
        self.processing_task = None
        self.running = False
    
    async def initialize(self):
        """Initialize webhook manager"""
        await self.database.initialize()
        await self.delivery_engine.initialize()
        logger.info("Webhook manager initialized")
    
    async def create_endpoint(self, endpoint: WebhookEndpoint) -> bool:
        """Create a new webhook endpoint"""
        success = await self.database.save_endpoint(endpoint)
        if success:
            active_webhooks.inc()
            logger.info(f"Created webhook endpoint: {endpoint.name} ({endpoint.id})")
        return success
    
    async def trigger_webhook(self, event: WebhookEvent) -> List[str]:
        """Trigger webhook delivery for an event"""
        try:
            # Save event
            await self.database.save_event(event)
            
            # Find matching endpoints
            async with self.database.db_pool.acquire() as conn:
                endpoints = await conn.fetch("""
                    SELECT * FROM webhook_endpoints 
                    WHERE status = 'active' AND $1 = ANY(event_types)
                """, event.event_type.value)
            
            delivery_ids = []
            
            # Create deliveries for matching endpoints
            for endpoint_row in endpoints:
                delivery = WebhookDelivery(
                    endpoint_id=endpoint_row['id'],
                    event_id=event.id,
                    status=WebhookStatus.PENDING
                )
                
                success = await self.database.save_delivery(delivery)
                if success:
                    delivery_ids.append(delivery.id)
            
            logger.info(f"Created {len(delivery_ids)} webhook deliveries for event {event.id}")
            return delivery_ids
            
        except Exception as e:
            logger.error(f"Error triggering webhook: {str(e)}")
            return []
    
    async def start_processing(self):
        """Start background processing of webhook deliveries"""
        self.running = True
        self.processing_task = asyncio.create_task(self._process_loop())
        logger.info("Started webhook processing")
    
    async def stop_processing(self):
        """Stop background processing"""
        self.running = False
        if self.processing_task:
            self.processing_task.cancel()
            try:
                await self.processing_task
            except asyncio.CancelledError:
                pass
        logger.info("Stopped webhook processing")
    
    async def _process_loop(self):
        """Main processing loop"""
        while self.running:
            try:
                await self.delivery_engine.process_pending_deliveries()
                await asyncio.sleep(self.config.processing_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in webhook processing loop: {str(e)}")
                await asyncio.sleep(self.config.processing_interval)
    
    async def close(self):
        """Close webhook manager"""
        await self.stop_processing()
        await self.delivery_engine.close()
        await self.database.close()

# FastAPI application for webhook management
app = FastAPI(title="Webhook Delivery System API")

# Global webhook manager instance
webhook_manager = None

@app.on_event("startup")
async def startup_event():
    global webhook_manager
    config = WebhookConfig()
    webhook_manager = WebhookManager(config)
    await webhook_manager.initialize()
    await webhook_manager.start_processing()

@app.on_event("shutdown")
async def shutdown_event():
    global webhook_manager
    if webhook_manager:
        await webhook_manager.close()

@app.post("/endpoints")
async def create_endpoint(endpoint: WebhookEndpoint):
    """Create a new webhook endpoint"""
    success = await webhook_manager.create_endpoint(endpoint)
    if not success:
        raise HTTPException(status_code=500, detail="Failed to create endpoint")
    return {"id": endpoint.id, "status": "created"}

@app.post("/events")
async def trigger_event(event: WebhookEvent):
    """Trigger a webhook event"""
    delivery_ids = await webhook_manager.trigger_webhook(event)
    return {"event_id": event.id, "deliveries": delivery_ids}

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "processing": webhook_manager.running if webhook_manager else False
    }

@app.get("/metrics")
async def get_metrics():
    """Prometheus metrics endpoint"""
    from prometheus_client import generate_latest, CONTENT_TYPE_LATEST
    return Response(generate_latest(), media_type=CONTENT_TYPE_LATEST)

async def main():
    """Run the webhook delivery system"""
    config = WebhookConfig()
    
    # Run FastAPI server
    import uvicorn
    await uvicorn.run(
        app,
        host=config.service_host,
        port=config.service_port,
        log_level="info"
    )

if __name__ == "__main__":
    asyncio.run(main())