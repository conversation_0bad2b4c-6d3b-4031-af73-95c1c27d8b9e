# Docker Compose for Webhook Delivery System
# Production-ready webhook infrastructure with monitoring and reliability

version: '3.8'

services:
  # Webhook Delivery System API
  webhook-api:
    build:
      context: .
      dockerfile: Dockerfile.webhook-api
    ports:
      - "8080:8080"
    environment:
      - SERVICE_HOST=0.0.0.0
      - SERVICE_PORT=8080
      - DB_HOST=postgres-webhooks
      - DB_NAME=webhook_system
      - DB_USER=webhook_user
      - DB_PASSWORD=${WEBHOOK_DB_PASSWORD:-webhook_password}
      - REDIS_URL=redis://redis-webhooks:6379/0
      - KAFKA_BOOTSTRAP_SERVERS=kafka:9092
      - WEBHOOK_SECRET_KEY=${WEBHOOK_SECRET_KEY:-default-webhook-secret-change-in-production}
      - MAX_RETRY_ATTEMPTS=5
      - WORKER_CONCURRENCY=10
      - DELIVERY_TIMEOUT=30
    depends_on:
      - postgres-webhooks
      - redis-webhooks
      - kafka
    volumes:
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'
    networks:
      - webhook-network
      - analytics-network

  # Webhook Event Consumer
  webhook-consumer:
    build:
      context: .
      dockerfile: Dockerfile.webhook-consumer
    environment:
      - KAFKA_BOOTSTRAP_SERVERS=kafka:9092
      - KAFKA_CONSUMER_GROUP=webhook-consumer-group
      - DB_HOST=postgres-webhooks
      - DB_NAME=webhook_system
      - DB_USER=webhook_user
      - DB_PASSWORD=${WEBHOOK_DB_PASSWORD:-webhook_password}
      - REDIS_URL=redis://redis-webhooks:6379/0
      - LOG_LEVEL=INFO
    depends_on:
      - postgres-webhooks
      - redis-webhooks
      - kafka
      - webhook-api
    volumes:
      - ./logs:/app/logs
    deploy:
      replicas: 3
      resources:
        limits:
          memory: 512M
          cpus: '0.25'
        reservations:
          memory: 256M
          cpus: '0.1'
    networks:
      - webhook-network
      - analytics-network

  # PostgreSQL Database for Webhooks
  postgres-webhooks:
    image: postgres:15
    environment:
      - POSTGRES_DB=webhook_system
      - POSTGRES_USER=webhook_user
      - POSTGRES_PASSWORD=${WEBHOOK_DB_PASSWORD:-webhook_password}
      - POSTGRES_INITDB_ARGS=--auth-host=scram-sha-256
    volumes:
      - postgres-webhook-data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5433:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U webhook_user -d webhook_system"]
      interval: 30s
      timeout: 10s
      retries: 5
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
    networks:
      - webhook-network

  # Redis for Webhook Queuing and Caching
  redis-webhooks:
    image: redis:7-alpine
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    ports:
      - "6380:6379"
    volumes:
      - redis-webhook-data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.2'
    networks:
      - webhook-network

  # Webhook Delivery Worker (Celery)
  webhook-worker:
    build:
      context: .
      dockerfile: Dockerfile.webhook-worker
    environment:
      - CELERY_BROKER_URL=redis://redis-webhooks:6379/1
      - CELERY_RESULT_BACKEND=redis://redis-webhooks:6379/2
      - DB_HOST=postgres-webhooks
      - DB_NAME=webhook_system
      - DB_USER=webhook_user
      - DB_PASSWORD=${WEBHOOK_DB_PASSWORD:-webhook_password}
      - WEBHOOK_SECRET_KEY=${WEBHOOK_SECRET_KEY:-default-webhook-secret-change-in-production}
      - WORKER_CONCURRENCY=4
      - LOG_LEVEL=INFO
    depends_on:
      - postgres-webhooks
      - redis-webhooks
    volumes:
      - ./logs:/app/logs
    deploy:
      replicas: 4
      resources:
        limits:
          memory: 512M
          cpus: '0.25'
        reservations:
          memory: 256M
          cpus: '0.1'
    networks:
      - webhook-network

  # Webhook Monitor and Dashboard
  webhook-monitor:
    build:
      context: .
      dockerfile: Dockerfile.webhook-monitor
    ports:
      - "8081:8081"
    environment:
      - MONITOR_HOST=0.0.0.0
      - MONITOR_PORT=8081
      - DB_HOST=postgres-webhooks
      - DB_NAME=webhook_system
      - DB_USER=webhook_user
      - DB_PASSWORD=${WEBHOOK_DB_PASSWORD:-webhook_password}
      - REDIS_URL=redis://redis-webhooks:6379/3
    depends_on:
      - postgres-webhooks
      - redis-webhooks
    volumes:
      - ./dashboard:/app/dashboard
    networks:
      - webhook-network

  # Dead Letter Queue Processor
  dlq-processor:
    build:
      context: .
      dockerfile: Dockerfile.dlq-processor
    environment:
      - KAFKA_BOOTSTRAP_SERVERS=kafka:9092
      - KAFKA_CONSUMER_GROUP=webhook-dlq-processor
      - DB_HOST=postgres-webhooks
      - DB_NAME=webhook_system
      - DB_USER=webhook_user
      - DB_PASSWORD=${WEBHOOK_DB_PASSWORD:-webhook_password}
      - PROCESSING_INTERVAL=300  # 5 minutes
      - MAX_DLQ_AGE_DAYS=7
    depends_on:
      - kafka
      - postgres-webhooks
    volumes:
      - ./logs:/app/logs
    networks:
      - webhook-network
      - analytics-network

# External Infrastructure (from main analytics platform)
  kafka:
    image: confluentinc/cp-kafka:latest
    ports:
      - "9092:9092"
    environment:
      - KAFKA_BROKER_ID=1
      - KAFKA_ZOOKEEPER_CONNECT=zookeeper:2181
      - KAFKA_ADVERTISED_LISTENERS=PLAINTEXT://kafka:9092
      - KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR=1
      - KAFKA_AUTO_CREATE_TOPICS_ENABLE=true
    depends_on:
      - zookeeper
    volumes:
      - kafka-webhook-data:/var/lib/kafka/data
    networks:
      - webhook-network
      - analytics-network

  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    environment:
      - ZOOKEEPER_CLIENT_PORT=2181
      - ZOOKEEPER_TICK_TIME=2000
    volumes:
      - zookeeper-webhook-data:/var/lib/zookeeper/data
    networks:
      - webhook-network

  # NGINX Load Balancer
  nginx-webhook:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - webhook-api
      - webhook-monitor
    networks:
      - webhook-network
    deploy:
      resources:
        limits:
          memory: 128M
          cpus: '0.1'

  # Prometheus for Webhook Metrics
  prometheus-webhook:
    image: prom/prometheus:latest
    ports:
      - "9091:9090"
    volumes:
      - ./monitoring/prometheus-webhook.yml:/etc/prometheus/prometheus.yml
      - prometheus-webhook-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    networks:
      - webhook-network

  # Grafana for Webhook Monitoring
  grafana-webhook:
    image: grafana/grafana:latest
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=webhook123
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
    volumes:
      - grafana-webhook-data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus-webhook
    networks:
      - webhook-network

  # Webhook Testing Service
  webhook-tester:
    build:
      context: .
      dockerfile: Dockerfile.webhook-tester
    ports:
      - "8082:8082"
    environment:
      - TESTER_HOST=0.0.0.0
      - TESTER_PORT=8082
      - WEBHOOK_API_URL=http://webhook-api:8080
    depends_on:
      - webhook-api
    volumes:
      - ./test-data:/app/test-data
    networks:
      - webhook-network

volumes:
  postgres-webhook-data:
  redis-webhook-data:
  kafka-webhook-data:
  zookeeper-webhook-data:
  prometheus-webhook-data:
  grafana-webhook-data:

networks:
  webhook-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
  analytics-network:
    external: true