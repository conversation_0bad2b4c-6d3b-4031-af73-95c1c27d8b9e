# Webhook Delivery System Requirements

# FastAPI and ASGI
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# HTTP client
httpx==0.25.2
aiohttp==3.9.1

# Database and storage
asyncpg==0.29.0
sqlalchemy[asyncio]==2.0.23
psycopg2-binary==2.9.9
alembic==1.12.1

# Redis and caching
redis==5.0.1
aioredis==2.0.1

# Message queues
aiokafka==0.8.11
celery[redis]==5.3.4
kombu==5.3.4

# Authentication and security
pyjwt[crypto]==2.8.0
passlib[bcrypt]==1.7.4
cryptography==41.0.8
bcrypt==4.1.2

# Data validation and serialization
pydantic==2.5.0
pydantic-settings==2.1.0
marshmallow==3.20.1
orjson==3.9.10

# Monitoring and observability
prometheus-client==0.19.0
structlog==23.2.0
opentelemetry-api==1.21.0
opentelemetry-sdk==1.21.0

# Configuration
python-dotenv==1.0.0
pyyaml==6.0.1
click==8.1.7
typer==0.9.0

# Async utilities
asyncio-mqtt==0.16.1
aiofiles==23.2.1

# Utilities
python-dateutil==2.8.2
pytz==2023.3
uuid==1.30

# Testing (development)
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-mock==3.12.0
httpx-oauth==0.10.2
fakeredis==2.20.1

# Code quality
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# Performance
uvloop==0.19.0

# Webhook signature validation
hmac

# HTTP utilities
requests==2.31.0
urllib3==2.1.0

# JSON processing
jsonschema==4.20.0

# Retry mechanisms
tenacity==8.2.3
backoff==2.2.1

# Health checks
psutil==5.9.6