# CDN Infrastructure for Global Analytics Collection
# Multi-CDN deployment with intelligent routing and edge caching

apiVersion: v1
kind: Namespace
metadata:
  name: cdn-analytics
  labels:
    name: cdn-analytics

---
# ConfigMap for CDN Configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: cdn-config
  namespace: cdn-analytics
data:
  cdn.yaml: |
    cdn:
      providers:
        - name: "cloudflare"
          enabled: true
          api_key: "${CLOUDFLARE_API_KEY}"
          zone_id: "${CLOUDFLARE_ZONE_ID}"
          
        - name: "fastly"
          enabled: true
          api_key: "${FASTLY_API_KEY}"
          service_id: "${FASTLY_SERVICE_ID}"
          
        - name: "aws-cloudfront"
          enabled: true
          distribution_id: "${CLOUDFRONT_DISTRIBUTION_ID}"
          
      edge_locations:
        us-east-1:
          provider: "cloudflare"
          origin: "us-east-1.analytics-platform.com"
          cname: "us-east-1.edge.analytics-platform.com"
          
        us-west-2:
          provider: "aws-cloudfront"
          origin: "us-west-2.analytics-platform.com"
          cname: "us-west-2.edge.analytics-platform.com"
          
        eu-west-1:
          provider: "fastly"
          origin: "eu-west-1.analytics-platform.com"
          cname: "eu-west-1.edge.analytics-platform.com"
          
        ap-southeast-1:
          provider: "cloudflare"
          origin: "ap-southeast-1.analytics-platform.com"
          cname: "ap-southeast-1.edge.analytics-platform.com"
      
      caching:
        default_ttl: 3600
        max_ttl: 86400
        browser_ttl: 300
        edge_ttl: 7200
        
      compression:
        enabled: true
        algorithms: ["gzip", "brotli"]
        min_size: 1024
        
      security:
        waf_enabled: true
        ddos_protection: true
        bot_management: true
        rate_limiting: true

---
# CDN Manager Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cdn-manager
  namespace: cdn-analytics
  labels:
    app: cdn-manager
spec:
  replicas: 2
  selector:
    matchLabels:
      app: cdn-manager
  template:
    metadata:
      labels:
        app: cdn-manager
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9090"
    spec:
      containers:
      - name: cdn-manager
        image: analytics-platform/cdn-manager:latest
        ports:
        - containerPort: 8080
          name: http
        - containerPort: 9090
          name: metrics
        env:
        - name: CLOUDFLARE_API_KEY
          valueFrom:
            secretKeyRef:
              name: cdn-secrets
              key: cloudflare-api-key
        - name: CLOUDFLARE_ZONE_ID
          valueFrom:
            secretKeyRef:
              name: cdn-secrets
              key: cloudflare-zone-id
        - name: FASTLY_API_KEY
          valueFrom:
            secretKeyRef:
              name: cdn-secrets
              key: fastly-api-key
        - name: FASTLY_SERVICE_ID
          valueFrom:
            secretKeyRef:
              name: cdn-secrets
              key: fastly-service-id
        - name: CLOUDFRONT_DISTRIBUTION_ID
          valueFrom:
            secretKeyRef:
              name: cdn-secrets
              key: cloudfront-distribution-id
        
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "250m"
        
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
        
        volumeMounts:
        - name: config
          mountPath: /app/config
          readOnly: true
      
      volumes:
      - name: config
        configMap:
          name: cdn-config

---
# CDN Manager Service
apiVersion: v1
kind: Service
metadata:
  name: cdn-manager
  namespace: cdn-analytics
  labels:
    app: cdn-manager
spec:
  type: ClusterIP
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
    name: http
  - port: 9090
    targetPort: 9090
    protocol: TCP
    name: metrics
  selector:
    app: cdn-manager

---
# Analytics Asset Server
apiVersion: apps/v1
kind: Deployment
metadata:
  name: analytics-assets
  namespace: cdn-analytics
  labels:
    app: analytics-assets
spec:
  replicas: 3
  selector:
    matchLabels:
      app: analytics-assets
  template:
    metadata:
      labels:
        app: analytics-assets
    spec:
      containers:
      - name: nginx
        image: nginx:alpine
        ports:
        - containerPort: 80
        volumeMounts:
        - name: assets
          mountPath: /usr/share/nginx/html
        - name: nginx-config
          mountPath: /etc/nginx/conf.d
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "128Mi"
            cpu: "100m"
      
      volumes:
      - name: assets
        configMap:
          name: analytics-assets
      - name: nginx-config
        configMap:
          name: nginx-assets-config

---
# Analytics Assets ConfigMap
apiVersion: v1
kind: ConfigMap
metadata:
  name: analytics-assets
  namespace: cdn-analytics
data:
  analytics.min.js: |
    // Minified CDN Analytics Collector would go here
    // This would be the compiled/minified version of cdn-analytics-collector.js
    !function(e,t,n,a,r){e[a]=e[a]||function(){(e[a].q=e[a].q||[]).push(arguments)}}(window,document,"script","analytics");
    
  pixel.gif: |
    // Base64 encoded 1x1 transparent pixel for tracking
    data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7

---
# NGINX Configuration for Assets
apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-assets-config
  namespace: cdn-analytics
data:
  default.conf: |
    server {
        listen 80;
        server_name _;
        
        # Security headers
        add_header X-Frame-Options DENY always;
        add_header X-Content-Type-Options nosniff always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;
        
        # CORS headers for analytics
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, POST, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Content-Type, Authorization" always;
        add_header Access-Control-Max-Age "3600" always;
        
        # Caching headers
        location ~* \.(js|css)$ {
            expires 1d;
            add_header Cache-Control "public, immutable";
            etag on;
        }
        
        location ~* \.(gif|png|jpg|jpeg|svg)$ {
            expires 7d;
            add_header Cache-Control "public, immutable";
            etag on;
        }
        
        # Analytics endpoint
        location /analytics.js {
            alias /usr/share/nginx/html/analytics.min.js;
            add_header Content-Type "application/javascript";
        }
        
        # Tracking pixel
        location /pixel.gif {
            alias /usr/share/nginx/html/pixel.gif;
            add_header Content-Type "image/gif";
            access_log off;
        }
        
        # Health check
        location /health {
            return 200 'OK';
            add_header Content-Type text/plain;
        }
        
        # Handle preflight requests
        if ($request_method = 'OPTIONS') {
            return 204;
        }
    }

---
# Analytics Assets Service
apiVersion: v1
kind: Service
metadata:
  name: analytics-assets
  namespace: cdn-analytics
  labels:
    app: analytics-assets
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 80
    protocol: TCP
    name: http
  selector:
    app: analytics-assets

---
# CDN Secrets
apiVersion: v1
kind: Secret
metadata:
  name: cdn-secrets
  namespace: cdn-analytics
type: Opaque
data:
  cloudflare-api-key: ${CLOUDFLARE_API_KEY_B64}
  cloudflare-zone-id: ${CLOUDFLARE_ZONE_ID_B64}
  fastly-api-key: ${FASTLY_API_KEY_B64}
  fastly-service-id: ${FASTLY_SERVICE_ID_B64}
  cloudfront-distribution-id: ${CLOUDFRONT_DISTRIBUTION_ID_B64}

---
# CDN Performance Monitor
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cdn-performance-monitor
  namespace: cdn-analytics
  labels:
    app: cdn-performance-monitor
spec:
  replicas: 1
  selector:
    matchLabels:
      app: cdn-performance-monitor
  template:
    metadata:
      labels:
        app: cdn-performance-monitor
    spec:
      containers:
      - name: monitor
        image: analytics-platform/cdn-performance-monitor:latest
        env:
        - name: MONITORING_INTERVAL
          value: "60"
        - name: ENDPOINTS_TO_MONITOR
          value: "us-east-1.edge.analytics-platform.com,us-west-2.edge.analytics-platform.com,eu-west-1.edge.analytics-platform.com,ap-southeast-1.edge.analytics-platform.com"
        - name: PROMETHEUS_URL
          value: "http://prometheus:9090"
        
        resources:
          requests:
            memory: "128Mi"
            cpu: "50m"
          limits:
            memory: "256Mi"
            cpu: "100m"
        
        volumeMounts:
        - name: monitoring-config
          mountPath: /app/config
      
      volumes:
      - name: monitoring-config
        configMap:
          name: cdn-monitoring-config

---
# CDN Monitoring Configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: cdn-monitoring-config
  namespace: cdn-analytics
data:
  monitoring.yaml: |
    monitoring:
      checks:
        - name: "response_time"
          threshold: 2000  # 2 seconds
          critical_threshold: 5000  # 5 seconds
          
        - name: "availability"
          threshold: 99.9  # 99.9% uptime
          critical_threshold: 99.0  # 99% uptime
          
        - name: "cache_hit_rate"
          threshold: 80  # 80% cache hit rate
          critical_threshold: 60  # 60% cache hit rate
          
        - name: "bandwidth_usage"
          threshold: 1000000000  # 1GB per hour
          critical_threshold: 5000000000  # 5GB per hour
      
      alerts:
        - name: "high_response_time"
          condition: "response_time > threshold"
          severity: "warning"
          
        - name: "critical_response_time"
          condition: "response_time > critical_threshold"
          severity: "critical"
          
        - name: "low_availability"
          condition: "availability < threshold"
          severity: "critical"
          
        - name: "low_cache_hit_rate"
          condition: "cache_hit_rate < threshold"
          severity: "warning"

---
# Ingress for CDN Assets
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: cdn-assets-ingress
  namespace: cdn-analytics
  annotations:
    kubernetes.io/ingress.class: "nginx"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, POST, OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-headers: "Content-Type, Authorization"
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/configuration-snippet: |
      add_header Cache-Control "public, max-age=3600" always;
      add_header X-CDN-Cache "kubernetes" always;
spec:
  tls:
  - hosts:
    - cdn.analytics-platform.com
    secretName: cdn-tls-secret
  rules:
  - host: cdn.analytics-platform.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: analytics-assets
            port:
              number: 80

---
# Horizontal Pod Autoscaler for Assets
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: analytics-assets-hpa
  namespace: cdn-analytics
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: analytics-assets
  minReplicas: 3
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80

---
# Network Policy for CDN
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: cdn-network-policy
  namespace: cdn-analytics
spec:
  podSelector:
    matchLabels:
      app: analytics-assets
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    ports:
    - protocol: TCP
      port: 80
  egress:
  - to: []
    ports:
    - protocol: TCP
      port: 53   # DNS
    - protocol: UDP
      port: 53   # DNS
    - protocol: TCP
      port: 443  # HTTPS

---
# ServiceMonitor for Prometheus
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: cdn-metrics
  namespace: cdn-analytics
  labels:
    app: cdn-manager
spec:
  selector:
    matchLabels:
      app: cdn-manager
  endpoints:
  - port: metrics
    interval: 30s
    path: /metrics

---
# PrometheusRule for CDN Alerting
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: cdn-alerts
  namespace: cdn-analytics
spec:
  groups:
  - name: cdn-analytics
    rules:
    - alert: CDNHighResponseTime
      expr: cdn_response_time_seconds > 2.0
      for: 2m
      labels:
        severity: warning
      annotations:
        summary: "CDN response time is high"
        description: "CDN endpoint {{ $labels.endpoint }} response time is {{ $value }}s"
    
    - alert: CDNLowCacheHitRate
      expr: cdn_cache_hit_rate < 0.8
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: "CDN cache hit rate is low"
        description: "CDN cache hit rate is {{ $value }} (below 80%)"
    
    - alert: CDNHighErrorRate
      expr: rate(cdn_errors_total[5m]) > 0.05
      for: 2m
      labels:
        severity: critical
      annotations:
        summary: "CDN error rate is high"
        description: "CDN error rate is {{ $value }} errors per second"