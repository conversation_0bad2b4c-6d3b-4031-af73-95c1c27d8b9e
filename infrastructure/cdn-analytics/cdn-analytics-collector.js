/**
 * CDN-Based Analytics Collection System
 * Lightweight analytics collection with edge processing and intelligent batching
 */

class CDNAnalyticsCollector {
    constructor(config = {}) {
        this.config = {
            // CDN endpoints by region
            endpoints: {
                'us-east-1': 'https://us-east-1.edge.analytics-platform.com',
                'us-west-2': 'https://us-west-2.edge.analytics-platform.com',
                'eu-west-1': 'https://eu-west-1.edge.analytics-platform.com',
                'ap-southeast-1': 'https://ap-southeast-1.edge.analytics-platform.com',
                'default': 'https://api.analytics-platform.com'
            },
            
            // Collection settings
            apiKey: config.apiKey || '',
            userId: config.userId || null,
            sessionId: config.sessionId || this.generateSessionId(),
            
            // Batching and performance
            batchSize: config.batchSize || 10,
            flushInterval: config.flushInterval || 5000, // 5 seconds
            maxRetries: config.maxRetries || 3,
            retryDelay: config.retryDelay || 1000,
            
            // Privacy and compliance
            respectDNT: config.respectDNT !== false,
            anonymizeIPs: config.anonymizeIPs !== false,
            enableConsent: config.enableConsent || false,
            
            // Feature flags
            enableRealTime: config.enableRealTime !== false,
            enableOfflineQueue: config.enableOfflineQueue !== false,
            enableCompression: config.enableCompression !== false,
            enableEdgeProcessing: config.enableEdgeProcessing !== false,
            
            // Advanced features
            enableHeatmaps: config.enableHeatmaps || false,
            enableScrollTracking: config.enableScrollTracking || false,
            enableFormAnalytics: config.enableFormAnalytics || false,
            enablePerformanceMonitoring: config.enablePerformanceMonitoring !== false,
            
            ...config
        };
        
        // Internal state
        this.eventQueue = [];
        this.isOnline = navigator.onLine;
        this.currentEndpoint = null;
        this.flushTimer = null;
        this.sessionStartTime = Date.now();
        this.pageLoadTime = performance.now();
        this.lastActivityTime = Date.now();
        
        // Event tracking state
        this.pageViews = new Map();
        this.scrollDepth = 0;
        this.maxScrollDepth = 0;
        this.clickCount = 0;
        this.formInteractions = new Map();
        
        // Performance monitoring
        this.performanceMetrics = {
            navigationTiming: null,
            resourceTimings: [],
            vitals: {}
        };
        
        // Initialize
        this.initialize();
    }
    
    /**
     * Initialize the analytics collector
     */
    initialize() {
        // Check for Do Not Track
        if (this.config.respectDNT && (
            navigator.doNotTrack === '1' ||
            window.doNotTrack === '1' ||
            navigator.msDoNotTrack === '1'
        )) {
            console.log('Analytics disabled due to Do Not Track preference');
            return;
        }
        
        // Check consent if enabled
        if (this.config.enableConsent && !this.hasConsent()) {
            console.log('Analytics disabled due to missing consent');
            return;
        }
        
        // Detect optimal endpoint
        this.detectOptimalEndpoint();
        
        // Set up event listeners
        this.setupEventListeners();
        
        // Start flush timer
        this.startFlushTimer();
        
        // Track page load
        this.trackPageView();
        
        // Set up performance monitoring
        if (this.config.enablePerformanceMonitoring) {
            this.setupPerformanceMonitoring();
        }
        
        console.log('CDN Analytics Collector initialized');
    }
    
    /**
     * Detect the optimal CDN endpoint based on user location
     */
    async detectOptimalEndpoint() {
        try {
            // Try to detect user's region using various methods
            const region = await this.detectUserRegion();
            
            this.currentEndpoint = this.config.endpoints[region] || this.config.endpoints.default;
            
            // Test endpoint latency and fallback if needed
            const isHealthy = await this.testEndpointHealth(this.currentEndpoint);
            if (!isHealthy) {
                this.currentEndpoint = this.config.endpoints.default;
            }
            
            console.log(`Using analytics endpoint: ${this.currentEndpoint}`);
            
        } catch (error) {
            console.warn('Failed to detect optimal endpoint, using default:', error);
            this.currentEndpoint = this.config.endpoints.default;
        }
    }
    
    /**
     * Detect user's region for optimal endpoint selection
     */
    async detectUserRegion() {
        try {
            // Method 1: Use Cloudflare's CF-IPCountry header (if available)
            if (typeof window !== 'undefined' && window.CF) {
                const country = window.CF.country;
                return this.mapCountryToRegion(country);
            }
            
            // Method 2: Use timezone to estimate region
            const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
            return this.mapTimezoneToRegion(timezone);
            
        } catch (error) {
            console.warn('Failed to detect region:', error);
            return 'default';
        }
    }
    
    /**
     * Map country code to CDN region
     */
    mapCountryToRegion(country) {
        const regionMap = {
            'US': 'us-east-1',
            'CA': 'us-east-1',
            'GB': 'eu-west-1',
            'DE': 'eu-west-1',
            'FR': 'eu-west-1',
            'IT': 'eu-west-1',
            'ES': 'eu-west-1',
            'SG': 'ap-southeast-1',
            'JP': 'ap-southeast-1',
            'AU': 'ap-southeast-1',
            'IN': 'ap-southeast-1'
        };
        
        return regionMap[country] || 'default';
    }
    
    /**
     * Map timezone to CDN region
     */
    mapTimezoneToRegion(timezone) {
        if (timezone.includes('America/')) {
            return timezone.includes('Los_Angeles') || timezone.includes('Vancouver') 
                ? 'us-west-2' : 'us-east-1';
        } else if (timezone.includes('Europe/')) {
            return 'eu-west-1';
        } else if (timezone.includes('Asia/') || timezone.includes('Australia/')) {
            return 'ap-southeast-1';
        }
        
        return 'default';
    }
    
    /**
     * Test endpoint health and latency
     */
    async testEndpointHealth(endpoint) {
        try {
            const startTime = performance.now();
            
            const response = await fetch(`${endpoint}/health`, {
                method: 'GET',
                mode: 'cors',
                cache: 'no-cache'
            });
            
            const latency = performance.now() - startTime;
            
            if (response.ok && latency < 2000) { // 2 second threshold
                return true;
            }
            
            return false;
            
        } catch (error) {
            console.warn(`Endpoint health check failed for ${endpoint}:`, error);
            return false;
        }
    }
    
    /**
     * Set up event listeners for automatic tracking
     */
    setupEventListeners() {
        // Page visibility change
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.track('page_hidden', {
                    time_on_page: Date.now() - this.sessionStartTime
                });
                this.flush(); // Flush immediately when page becomes hidden
            } else {
                this.track('page_visible');
            }
        });
        
        // Before unload - flush remaining events
        window.addEventListener('beforeunload', () => {
            this.track('page_unload', {
                time_on_page: Date.now() - this.sessionStartTime,
                scroll_depth: this.maxScrollDepth
            });
            this.flush(true); // Synchronous flush
        });
        
        // Online/offline status
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.track('connection_restored');
            this.processOfflineQueue();
        });
        
        window.addEventListener('offline', () => {
            this.isOnline = false;
            this.track('connection_lost');
        });
        
        // Click tracking
        document.addEventListener('click', (event) => {
            this.handleClick(event);
        });
        
        // Scroll tracking
        if (this.config.enableScrollTracking) {
            this.setupScrollTracking();
        }
        
        // Form analytics
        if (this.config.enableFormAnalytics) {
            this.setupFormAnalytics();
        }
        
        // Heatmap tracking
        if (this.config.enableHeatmaps) {
            this.setupHeatmapTracking();
        }
        
        // Error tracking
        window.addEventListener('error', (event) => {
            this.trackError(event.error, 'javascript_error');
        });
        
        window.addEventListener('unhandledrejection', (event) => {
            this.trackError(event.reason, 'unhandled_promise_rejection');
        });
    }
    
    /**
     * Set up scroll tracking
     */
    setupScrollTracking() {
        let scrollTimer = null;
        
        window.addEventListener('scroll', () => {
            clearTimeout(scrollTimer);
            
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const windowHeight = window.innerHeight;
            const documentHeight = document.documentElement.scrollHeight;
            
            this.scrollDepth = Math.round((scrollTop + windowHeight) / documentHeight * 100);
            this.maxScrollDepth = Math.max(this.maxScrollDepth, this.scrollDepth);
            
            scrollTimer = setTimeout(() => {
                this.track('scroll', {
                    scroll_depth: this.scrollDepth,
                    max_scroll_depth: this.maxScrollDepth
                });
            }, 250);
        });
    }
    
    /**
     * Set up form analytics
     */
    setupFormAnalytics() {
        document.addEventListener('focus', (event) => {
            if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA' || event.target.tagName === 'SELECT') {
                const formId = event.target.form ? event.target.form.id || 'unknown' : 'no-form';
                const fieldName = event.target.name || event.target.id || 'unknown';
                
                this.track('form_field_focus', {
                    form_id: formId,
                    field_name: fieldName,
                    field_type: event.target.type || event.target.tagName.toLowerCase()
                });
            }
        });
        
        document.addEventListener('submit', (event) => {
            const formId = event.target.id || 'unknown';
            const formAction = event.target.action || window.location.href;
            
            this.track('form_submit', {
                form_id: formId,
                form_action: formAction,
                field_count: event.target.elements.length
            });
        });
    }
    
    /**
     * Set up heatmap tracking
     */
    setupHeatmapTracking() {
        document.addEventListener('click', (event) => {
            const rect = document.documentElement.getBoundingClientRect();
            const x = Math.round((event.clientX / window.innerWidth) * 100);
            const y = Math.round((event.clientY / window.innerHeight) * 100);
            
            this.track('heatmap_click', {
                x_percent: x,
                y_percent: y,
                element_tag: event.target.tagName.toLowerCase(),
                element_class: event.target.className,
                element_id: event.target.id,
                viewport_width: window.innerWidth,
                viewport_height: window.innerHeight
            });
        });
    }
    
    /**
     * Set up performance monitoring
     */
    setupPerformanceMonitoring() {
        // Navigation timing
        window.addEventListener('load', () => {
            setTimeout(() => {
                const navigation = performance.getEntriesByType('navigation')[0];
                if (navigation) {
                    this.performanceMetrics.navigationTiming = {
                        dns_lookup: navigation.domainLookupEnd - navigation.domainLookupStart,
                        tcp_connect: navigation.connectEnd - navigation.connectStart,
                        ssl_negotiate: navigation.connectEnd - navigation.secureConnectionStart,
                        ttfb: navigation.responseStart - navigation.requestStart,
                        dom_load: navigation.domContentLoadedEventEnd - navigation.navigationStart,
                        page_load: navigation.loadEventEnd - navigation.navigationStart
                    };
                    
                    this.track('performance_navigation', this.performanceMetrics.navigationTiming);
                }
                
                // Resource timing
                const resources = performance.getEntriesByType('resource');
                this.trackResourcePerformance(resources);
                
            }, 1000);
        });
        
        // Core Web Vitals
        this.measureWebVitals();
    }
    
    /**
     * Measure Core Web Vitals
     */
    measureWebVitals() {
        // Largest Contentful Paint (LCP)
        if ('PerformanceObserver' in window) {
            try {
                const lcpObserver = new PerformanceObserver((list) => {
                    const entries = list.getEntries();
                    const lastEntry = entries[entries.length - 1];
                    
                    this.performanceMetrics.vitals.lcp = lastEntry.startTime;
                    this.track('web_vital_lcp', { value: lastEntry.startTime });
                });
                lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
                
                // First Input Delay (FID)
                const fidObserver = new PerformanceObserver((list) => {
                    const entries = list.getEntries();
                    entries.forEach((entry) => {
                        this.performanceMetrics.vitals.fid = entry.processingStart - entry.startTime;
                        this.track('web_vital_fid', { value: entry.processingStart - entry.startTime });
                    });
                });
                fidObserver.observe({ entryTypes: ['first-input'] });
                
                // Cumulative Layout Shift (CLS)
                let clsValue = 0;
                const clsObserver = new PerformanceObserver((list) => {
                    const entries = list.getEntries();
                    entries.forEach((entry) => {
                        if (!entry.hadRecentInput) {
                            clsValue += entry.value;
                        }
                    });
                    
                    this.performanceMetrics.vitals.cls = clsValue;
                });
                clsObserver.observe({ entryTypes: ['layout-shift'] });
                
                // Report CLS on page unload
                window.addEventListener('beforeunload', () => {
                    this.track('web_vital_cls', { value: clsValue });
                });
                
            } catch (error) {
                console.warn('Failed to set up Web Vitals monitoring:', error);
            }
        }
    }
    
    /**
     * Track resource performance
     */
    trackResourcePerformance(resources) {
        const resourceMetrics = resources.map(resource => ({
            name: resource.name,
            type: this.getResourceType(resource.name),
            duration: resource.duration,
            size: resource.transferSize || 0,
            cached: resource.transferSize === 0 && resource.decodedBodySize > 0
        }));
        
        this.track('performance_resources', {
            resource_count: resourceMetrics.length,
            total_size: resourceMetrics.reduce((sum, r) => sum + r.size, 0),
            avg_duration: resourceMetrics.reduce((sum, r) => sum + r.duration, 0) / resourceMetrics.length,
            cached_percentage: resourceMetrics.filter(r => r.cached).length / resourceMetrics.length * 100
        });
    }
    
    /**
     * Get resource type from URL
     */
    getResourceType(url) {
        if (url.match(/\.(css)$/)) return 'css';
        if (url.match(/\.(js)$/)) return 'javascript';
        if (url.match(/\.(png|jpg|jpeg|gif|svg|webp)$/)) return 'image';
        if (url.match(/\.(woff|woff2|ttf|eot)$/)) return 'font';
        return 'other';
    }
    
    /**
     * Handle click events
     */
    handleClick(event) {
        this.clickCount++;
        this.lastActivityTime = Date.now();
        
        const element = event.target;
        const clickData = {
            element_tag: element.tagName.toLowerCase(),
            element_class: element.className,
            element_id: element.id,
            element_text: element.textContent?.substring(0, 100) || '',
            click_count: this.clickCount,
            x: event.clientX,
            y: event.clientY
        };
        
        // Track specific click types
        if (element.tagName === 'A') {
            clickData.link_url = element.href;
            clickData.link_target = element.target;
            this.track('link_click', clickData);
        } else if (element.tagName === 'BUTTON' || element.type === 'submit') {
            this.track('button_click', clickData);
        } else {
            this.track('element_click', clickData);
        }
    }
    
    /**
     * Track page view
     */
    trackPageView() {
        const pageData = {
            url: window.location.href,
            path: window.location.pathname,
            title: document.title,
            referrer: document.referrer,
            user_agent: navigator.userAgent,
            viewport_width: window.innerWidth,
            viewport_height: window.innerHeight,
            screen_width: screen.width,
            screen_height: screen.height,
            color_depth: screen.colorDepth,
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
            language: navigator.language
        };
        
        // Add UTM parameters if present
        const urlParams = new URLSearchParams(window.location.search);
        const utmParams = {};
        for (const [key, value] of urlParams.entries()) {
            if (key.startsWith('utm_')) {
                utmParams[key] = value;
            }
        }
        
        if (Object.keys(utmParams).length > 0) {
            pageData.utm_parameters = utmParams;
        }
        
        this.track('page_view', pageData);
    }
    
    /**
     * Track custom event
     */
    track(eventName, properties = {}) {
        const event = {
            event: eventName,
            properties: {
                ...properties,
                session_id: this.config.sessionId,
                user_id: this.config.userId,
                timestamp: new Date().toISOString(),
                url: window.location.href,
                user_agent: navigator.userAgent,
                edge_processed: this.config.enableEdgeProcessing
            }
        };
        
        // Add to queue
        this.eventQueue.push(event);
        
        // Auto-flush if queue is full
        if (this.eventQueue.length >= this.config.batchSize) {
            this.flush();
        }
        
        // Real-time events (high priority)
        if (this.config.enableRealTime && this.isHighPriorityEvent(eventName)) {
            this.sendRealTimeEvent(event);
        }
    }
    
    /**
     * Track error
     */
    trackError(error, type = 'error') {
        this.track('error', {
            error_type: type,
            error_message: error.message || String(error),
            error_stack: error.stack || '',
            error_filename: error.filename || '',
            error_line: error.lineno || 0,
            error_column: error.colno || 0
        });
    }
    
    /**
     * Check if event should be sent in real-time
     */
    isHighPriorityEvent(eventName) {
        const highPriorityEvents = [
            'error',
            'form_submit',
            'purchase',
            'sign_up',
            'login'
        ];
        
        return highPriorityEvents.includes(eventName);
    }
    
    /**
     * Send real-time event
     */
    async sendRealTimeEvent(event) {
        try {
            await this.sendEvents([event], true);
        } catch (error) {
            console.warn('Failed to send real-time event:', error);
        }
    }
    
    /**
     * Flush event queue
     */
    async flush(synchronous = false) {
        if (this.eventQueue.length === 0) return;
        
        const events = [...this.eventQueue];
        this.eventQueue = [];
        
        if (synchronous) {
            // Use sendBeacon for synchronous sending (e.g., on page unload)
            this.sendEventsSync(events);
        } else {
            await this.sendEvents(events);
        }
    }
    
    /**
     * Send events asynchronously
     */
    async sendEvents(events, isRealTime = false) {
        if (!this.currentEndpoint) {
            await this.detectOptimalEndpoint();
        }
        
        const payload = {
            api_key: this.config.apiKey,
            events: events,
            batch_id: this.generateBatchId(),
            is_real_time: isRealTime,
            edge_region: this.getCurrentRegion()
        };
        
        try {
            const response = await fetch(`${this.currentEndpoint}/analytics/events`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Analytics-Version': '2.0',
                    'X-Edge-Region': this.getCurrentRegion()
                },
                body: JSON.stringify(payload),
                keepalive: true
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            console.debug(`Sent ${events.length} events to analytics`);
            
        } catch (error) {
            console.warn('Failed to send events:', error);
            
            // Add back to queue for retry (if offline queue enabled)
            if (this.config.enableOfflineQueue) {
                this.addToOfflineQueue(events);
            }
            
            throw error;
        }
    }
    
    /**
     * Send events synchronously using sendBeacon
     */
    sendEventsSync(events) {
        if (!this.currentEndpoint) return;
        
        const payload = {
            api_key: this.config.apiKey,
            events: events,
            batch_id: this.generateBatchId(),
            edge_region: this.getCurrentRegion()
        };
        
        const blob = new Blob([JSON.stringify(payload)], {
            type: 'application/json'
        });
        
        navigator.sendBeacon(`${this.currentEndpoint}/analytics/events`, blob);
    }
    
    /**
     * Add events to offline queue
     */
    addToOfflineQueue(events) {
        try {
            const offlineQueue = JSON.parse(localStorage.getItem('analytics_offline_queue') || '[]');
            offlineQueue.push(...events);
            
            // Limit offline queue size
            if (offlineQueue.length > 1000) {
                offlineQueue.splice(0, offlineQueue.length - 1000);
            }
            
            localStorage.setItem('analytics_offline_queue', JSON.stringify(offlineQueue));
        } catch (error) {
            console.warn('Failed to save to offline queue:', error);
        }
    }
    
    /**
     * Process offline queue when back online
     */
    async processOfflineQueue() {
        try {
            const offlineQueue = JSON.parse(localStorage.getItem('analytics_offline_queue') || '[]');
            
            if (offlineQueue.length > 0) {
                console.log(`Processing ${offlineQueue.length} offline events`);
                
                // Process in batches
                for (let i = 0; i < offlineQueue.length; i += this.config.batchSize) {
                    const batch = offlineQueue.slice(i, i + this.config.batchSize);
                    try {
                        await this.sendEvents(batch);
                    } catch (error) {
                        console.warn('Failed to process offline batch:', error);
                        break; // Stop processing if we're still having issues
                    }
                }
                
                // Clear offline queue
                localStorage.removeItem('analytics_offline_queue');
            }
        } catch (error) {
            console.warn('Failed to process offline queue:', error);
        }
    }
    
    /**
     * Start flush timer
     */
    startFlushTimer() {
        this.flushTimer = setInterval(() => {
            this.flush();
        }, this.config.flushInterval);
    }
    
    /**
     * Stop flush timer
     */
    stopFlushTimer() {
        if (this.flushTimer) {
            clearInterval(this.flushTimer);
            this.flushTimer = null;
        }
    }
    
    /**
     * Generate session ID
     */
    generateSessionId() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    /**
     * Generate batch ID
     */
    generateBatchId() {
        return 'batch_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    /**
     * Get current region
     */
    getCurrentRegion() {
        if (!this.currentEndpoint) return 'unknown';
        
        for (const [region, endpoint] of Object.entries(this.config.endpoints)) {
            if (endpoint === this.currentEndpoint) {
                return region;
            }
        }
        
        return 'default';
    }
    
    /**
     * Check if user has given consent
     */
    hasConsent() {
        return localStorage.getItem('analytics_consent') === 'true';
    }
    
    /**
     * Set user consent
     */
    setConsent(hasConsent) {
        localStorage.setItem('analytics_consent', hasConsent ? 'true' : 'false');
        
        if (hasConsent && !this.flushTimer) {
            this.initialize();
        } else if (!hasConsent) {
            this.stopFlushTimer();
            this.eventQueue = [];
        }
    }
    
    /**
     * Identify user
     */
    identify(userId, traits = {}) {
        this.config.userId = userId;
        
        this.track('identify', {
            user_id: userId,
            traits: traits
        });
    }
    
    /**
     * Reset session
     */
    reset() {
        this.config.sessionId = this.generateSessionId();
        this.config.userId = null;
        this.sessionStartTime = Date.now();
        this.clickCount = 0;
        this.scrollDepth = 0;
        this.maxScrollDepth = 0;
    }
    
    /**
     * Cleanup and stop tracking
     */
    destroy() {
        this.stopFlushTimer();
        this.flush(true);
        
        // Remove event listeners
        // (In a real implementation, you'd store references to remove them)
        
        console.log('CDN Analytics Collector destroyed');
    }
}

// Auto-initialize if running in browser and config is available
if (typeof window !== 'undefined' && window.analyticsConfig) {
    window.analytics = new CDNAnalyticsCollector(window.analyticsConfig);
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CDNAnalyticsCollector;
} else if (typeof window !== 'undefined') {
    window.CDNAnalyticsCollector = CDNAnalyticsCollector;
}

/**
 * Lightweight analytics snippet for easy integration
 */
const AnalyticsSnippet = `
(function(w,d,s,o,f,js,fjs){
    w['AnalyticsObject']=o;w[o]=w[o]||function(){
    (w[o].q=w[o].q||[]).push(arguments)};w[o].l=1*new Date();
    js=d.createElement(s),fjs=d.getElementsByTagName(s)[0];
    js.async=1;js.src=f;fjs.parentNode.insertBefore(js,fjs);
})(window,document,'script','analytics','https://cdn.analytics-platform.com/v2/analytics.min.js');

analytics('init', {
    apiKey: 'YOUR_API_KEY',
    enableEdgeProcessing: true,
    enableRealTime: true,
    respectDNT: true
});

analytics('page');
`;

if (typeof window !== 'undefined') {
    window.AnalyticsSnippet = AnalyticsSnippet;
}