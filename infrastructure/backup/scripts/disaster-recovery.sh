#!/bin/bash

# Disaster Recovery Script for E-commerce Analytics SaaS Platform
# This script orchestrates the disaster recovery process for various failure scenarios

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_FILE="${CONFIG_FILE:-$SCRIPT_DIR/../config/dr.conf}"
LOG_FILE="${LOG_FILE:-/var/log/disaster-recovery/dr-$(date +%Y%m%d_%H%M%S).log}"

# Default configuration
DR_REGION="${DR_REGION:-us-west-2}"
PRIMARY_REGION="${PRIMARY_REGION:-us-east-1}"
S3_BUCKET="${S3_BUCKET:-ecommerce-analytics-backups}"
DR_CLUSTER_NAME="${DR_CLUSTER_NAME:-ecommerce-analytics-dr}"
DR_DB_IDENTIFIER="${DR_DB_IDENTIFIER:-analytics-db-dr}"
DR_REDIS_CLUSTER="${DR_REDIS_CLUSTER:-analytics-redis-dr}"

# DNS and networking
ROUTE53_HOSTED_ZONE="${ROUTE53_HOSTED_ZONE:-Z123456789}"
PRIMARY_DOMAIN="${PRIMARY_DOMAIN:-api.company.com}"
DR_DOMAIN="${DR_DOMAIN:-dr-api.company.com}"

# Notification settings
SNS_TOPIC="${SNS_TOPIC:-arn:aws:sns:us-west-2:123456789012:dr-notifications}"
SLACK_WEBHOOK="${SLACK_WEBHOOK:-}"
PAGERDUTY_KEY="${PAGERDUTY_KEY:-}"

# Recovery targets
RTO_MINUTES="${RTO_MINUTES:-60}"
RPO_MINUTES="${RPO_MINUTES:-15}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Load configuration
if [[ -f "$CONFIG_FILE" ]]; then
    source "$CONFIG_FILE"
fi

# Create log directory
mkdir -p "$(dirname "$LOG_FILE")"

# Logging function
log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message" | tee -a "$LOG_FILE"
    
    # Also log to console with colors
    case $level in
        "ERROR")
            echo -e "${RED}[$timestamp] [$level] $message${NC}" >&2
            ;;
        "WARN")
            echo -e "${YELLOW}[$timestamp] [$level] $message${NC}" >&2
            ;;
        "INFO")
            echo -e "${GREEN}[$timestamp] [$level] $message${NC}"
            ;;
        "DEBUG")
            echo -e "${BLUE}[$timestamp] [$level] $message${NC}"
            ;;
    esac
}

# Error handling
error_exit() {
    log "ERROR" "$1"
    send_notification "DISASTER_RECOVERY_FAILED" "DR failed: $1"
    exit 1
}

# Send notifications
send_notification() {
    local event_type="$1"
    local message="$2"
    
    # SNS notification
    if [[ -n "$SNS_TOPIC" ]]; then
        aws sns publish \
            --topic-arn "$SNS_TOPIC" \
            --subject "DR Event: $event_type" \
            --message "$message" \
            --region "$DR_REGION" &
    fi
    
    # Slack notification
    if [[ -n "$SLACK_WEBHOOK" ]]; then
        local color="danger"
        [[ "$event_type" == *"SUCCESS"* ]] && color="good"
        [[ "$event_type" == *"WARNING"* ]] && color="warning"
        
        curl -X POST "$SLACK_WEBHOOK" \
            -H 'Content-type: application/json' \
            --data "{
                \"attachments\": [{
                    \"color\": \"$color\",
                    \"title\": \"Disaster Recovery: $event_type\",
                    \"text\": \"$message\",
                    \"fields\": [{
                        \"title\": \"Time\",
                        \"value\": \"$(date)\",
                        \"short\": true
                    }, {
                        \"title\": \"Region\",
                        \"value\": \"$DR_REGION\",
                        \"short\": true
                    }]
                }]
            }" &
    fi
    
    # PagerDuty integration
    if [[ -n "$PAGERDUTY_KEY" ]]; then
        local severity="critical"
        [[ "$event_type" == *"SUCCESS"* ]] && severity="info"
        
        curl -X POST 'https://events.pagerduty.com/v2/enqueue' \
            -H 'Content-Type: application/json' \
            --data "{
                \"routing_key\": \"$PAGERDUTY_KEY\",
                \"event_action\": \"trigger\",
                \"payload\": {
                    \"summary\": \"DR Event: $event_type\",
                    \"source\": \"disaster-recovery-script\",
                    \"severity\": \"$severity\",
                    \"custom_details\": {
                        \"message\": \"$message\",
                        \"region\": \"$DR_REGION\",
                        \"timestamp\": \"$(date -u +%Y-%m-%dT%H:%M:%SZ)\"
                    }
                }
            }" &
    fi
    
    wait # Wait for notifications
}

# Validate DR environment
validate_dr_environment() {
    log "INFO" "Validating DR environment..."
    
    # Check AWS CLI and credentials
    if ! aws sts get-caller-identity --region "$DR_REGION" &> /dev/null; then
        error_exit "AWS credentials not configured for DR region: $DR_REGION"
    fi
    
    # Check kubectl access to DR cluster
    if ! kubectl config get-contexts | grep -q "$DR_CLUSTER_NAME"; then
        log "WARN" "DR cluster context not found, setting up..."
        aws eks update-kubeconfig --region "$DR_REGION" --name "$DR_CLUSTER_NAME" || \
            error_exit "Cannot configure kubectl for DR cluster"
    fi
    
    # Check S3 bucket access
    if ! aws s3 ls "s3://$S3_BUCKET" --region "$DR_REGION" &> /dev/null; then
        error_exit "Cannot access backup bucket in DR region"
    fi
    
    log "INFO" "DR environment validation completed"
}

# Assess disaster scope
assess_disaster() {
    local disaster_type="$1"
    
    log "INFO" "Assessing disaster scope: $disaster_type"
    
    case "$disaster_type" in
        "database_failure")
            assess_database_failure
            ;;
        "region_failure")
            assess_region_failure
            ;;
        "application_failure")
            assess_application_failure
            ;;
        "security_incident")
            assess_security_incident
            ;;
        *)
            log "WARN" "Unknown disaster type, performing general assessment"
            assess_general_failure
            ;;
    esac
}

# Database failure assessment
assess_database_failure() {
    log "INFO" "Assessing database failure..."
    
    # Check primary database connectivity
    if pg_isready -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" 2>/dev/null; then
        log "INFO" "Primary database is accessible - may be a partial failure"
        export PARTIAL_FAILURE=true
    else
        log "WARN" "Primary database is not accessible - complete database failure"
        export COMPLETE_DB_FAILURE=true
    fi
    
    # Check Redis connectivity
    if redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" ping 2>/dev/null | grep -q PONG; then
        log "INFO" "Redis is accessible"
    else
        log "WARN" "Redis is not accessible"
        export REDIS_FAILURE=true
    fi
    
    # Find latest valid backup
    export LATEST_BACKUP=$(find_latest_backup "postgresql")
    if [[ -z "$LATEST_BACKUP" ]]; then
        error_exit "No valid database backup found"
    fi
    
    local backup_age=$(get_backup_age "$LATEST_BACKUP")
    log "INFO" "Latest backup is $backup_age minutes old"
    
    if [[ $backup_age -gt $RPO_MINUTES ]]; then
        log "WARN" "Latest backup exceeds RPO target of $RPO_MINUTES minutes"
    fi
}

# Find latest backup
find_latest_backup() {
    local backup_type="$1"
    
    aws s3api list-objects-v2 \
        --bucket "$S3_BUCKET" \
        --prefix "$backup_type/" \
        --query 'sort_by(Contents, &LastModified)[-1].Key' \
        --output text \
        --region "$DR_REGION"
}

# Get backup age in minutes
get_backup_age() {
    local backup_key="$1"
    
    local backup_time=$(aws s3api head-object \
        --bucket "$S3_BUCKET" \
        --key "$backup_key" \
        --query 'LastModified' \
        --output text \
        --region "$DR_REGION")
    
    local backup_epoch=$(date -d "$backup_time" +%s)
    local current_epoch=$(date +%s)
    local age_seconds=$((current_epoch - backup_epoch))
    
    echo $((age_seconds / 60))
}

# Region failure assessment
assess_region_failure() {
    log "INFO" "Assessing region failure..."
    
    # Check primary region services
    local primary_services_down=0
    
    # Check if we can access primary region at all
    if ! aws sts get-caller-identity --region "$PRIMARY_REGION" &> /dev/null; then
        log "WARN" "Cannot access primary region AWS services"
        primary_services_down=$((primary_services_down + 1))
    fi
    
    # Check primary region EKS
    if ! aws eks describe-cluster --name "ecommerce-analytics" --region "$PRIMARY_REGION" &> /dev/null; then
        log "WARN" "Primary region EKS cluster not accessible"
        primary_services_down=$((primary_services_down + 1))
    fi
    
    # Check primary region RDS
    if ! aws rds describe-db-instances --region "$PRIMARY_REGION" &> /dev/null; then
        log "WARN" "Primary region RDS not accessible"
        primary_services_down=$((primary_services_down + 1))
    fi
    
    if [[ $primary_services_down -ge 2 ]]; then
        log "ERROR" "Multiple primary region services down - confirming region failure"
        export REGION_FAILURE=true
    else
        log "INFO" "Primary region appears to be accessible - may be service-specific failure"
    fi
}

# Restore database from backup
restore_database() {
    local backup_key="$1"
    local target_db_identifier="$2"
    
    log "INFO" "Starting database restoration from backup: $backup_key"
    
    local start_time=$(date +%s)
    
    # Download backup from S3
    local backup_file="/tmp/$(basename "$backup_key")"
    log "INFO" "Downloading backup file..."
    
    aws s3 cp "s3://$S3_BUCKET/$backup_key" "$backup_file" \
        --region "$DR_REGION" || error_exit "Failed to download backup"
    
    # Create or restore RDS instance
    log "INFO" "Creating/restoring RDS instance: $target_db_identifier"
    
    # Check if DR database already exists
    if aws rds describe-db-instances \
        --db-instance-identifier "$target_db_identifier" \
        --region "$DR_REGION" &> /dev/null; then
        
        log "INFO" "DR database exists, stopping it for restoration..."
        aws rds stop-db-instance \
            --db-instance-identifier "$target_db_identifier" \
            --region "$DR_REGION" || log "WARN" "Could not stop DR database"
        
        # Wait for stop
        aws rds wait db-instance-stopped \
            --db-instance-identifier "$target_db_identifier" \
            --region "$DR_REGION"
    else
        log "INFO" "Creating new DR database instance..."
        aws rds create-db-instance \
            --db-instance-identifier "$target_db_identifier" \
            --db-instance-class "db.r5.xlarge" \
            --engine "postgres" \
            --master-username "postgres" \
            --master-user-password "$(generate_random_password)" \
            --allocated-storage 100 \
            --storage-type "gp2" \
            --vpc-security-group-ids "$(get_dr_security_group)" \
            --db-subnet-group-name "$(get_dr_subnet_group)" \
            --backup-retention-period 7 \
            --multi-az \
            --region "$DR_REGION" || error_exit "Failed to create DR database"
    fi
    
    # Wait for database to be available
    log "INFO" "Waiting for database to be available..."
    aws rds wait db-instance-available \
        --db-instance-identifier "$target_db_identifier" \
        --region "$DR_REGION" || error_exit "Database failed to become available"
    
    # Get database endpoint
    local db_endpoint=$(aws rds describe-db-instances \
        --db-instance-identifier "$target_db_identifier" \
        --query 'DBInstances[0].Endpoint.Address' \
        --output text \
        --region "$DR_REGION")
    
    log "INFO" "Database endpoint: $db_endpoint"
    
    # Restore backup to database
    log "INFO" "Restoring backup to database..."
    
    # Set password for restoration
    export PGPASSWORD="$(get_db_password "$target_db_identifier")"
    
    pg_restore \
        --host="$db_endpoint" \
        --port=5432 \
        --username="postgres" \
        --dbname="postgres" \
        --clean \
        --if-exists \
        --verbose \
        "$backup_file" || error_exit "Database restoration failed"
    
    # Verify restoration
    local record_count=$(psql \
        -h "$db_endpoint" \
        -p 5432 \
        -U postgres \
        -d ecommerce_analytics \
        -t \
        -c "SELECT COUNT(*) FROM analytics_events;")
    
    log "INFO" "Database restoration completed. Records: $record_count"
    
    # Cleanup
    rm -f "$backup_file"
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    log "INFO" "Database restoration took $duration seconds"
    
    # Update configuration with new endpoint
    update_application_config "DB_HOST" "$db_endpoint"
    
    echo "$db_endpoint"
}

# Restore Redis from backup
restore_redis() {
    local backup_key="$1"
    local target_redis_cluster="$2"
    
    log "INFO" "Starting Redis restoration from backup: $backup_key"
    
    # Download backup
    local backup_file="/tmp/$(basename "$backup_key")"
    aws s3 cp "s3://$S3_BUCKET/$backup_key" "$backup_file" \
        --region "$DR_REGION" || error_exit "Failed to download Redis backup"
    
    # Decompress if needed
    if [[ "$backup_file" == *.gz ]]; then
        gunzip "$backup_file"
        backup_file="${backup_file%.gz}"
    fi
    
    # Get Redis endpoint
    local redis_endpoint=$(aws elasticache describe-cache-clusters \
        --cache-cluster-id "$target_redis_cluster" \
        --show-cache-node-info \
        --query 'CacheClusters[0].CacheNodes[0].Endpoint.Address' \
        --output text \
        --region "$DR_REGION")
    
    # Flush existing data
    redis-cli -h "$redis_endpoint" FLUSHALL
    
    # Restore data
    log "INFO" "Restoring Redis data..."
    while IFS= read -r line; do
        echo "$line" | redis-cli -h "$redis_endpoint" --pipe
    done < "$backup_file"
    
    # Verify restoration
    local key_count=$(redis-cli -h "$redis_endpoint" DBSIZE)
    log "INFO" "Redis restoration completed. Keys: $key_count"
    
    # Cleanup
    rm -f "$backup_file"
    
    # Update configuration
    update_application_config "REDIS_HOST" "$redis_endpoint"
    
    echo "$redis_endpoint"
}

# Deploy applications to DR site
deploy_applications() {
    log "INFO" "Deploying applications to DR site..."
    
    # Switch to DR cluster context
    kubectl config use-context "$DR_CLUSTER_NAME" || \
        error_exit "Failed to switch to DR cluster context"
    
    # Create namespace if not exists
    kubectl create namespace production --dry-run=client -o yaml | kubectl apply -f -
    
    # Deploy applications using Helm or kubectl
    local apps=("analytics" "dashboard" "billing" "integration")
    
    for app in "${apps[@]}"; do
        log "INFO" "Deploying $app to DR site..."
        
        # Update image tags to use latest stable versions
        local image_tag=$(get_latest_stable_image_tag "$app")
        
        # Deploy using Helm
        helm upgrade --install "$app-dr" "./charts/$app" \
            --namespace production \
            --set environment=disaster-recovery \
            --set image.tag="$image_tag" \
            --set replicaCount=2 \
            --set resources.requests.cpu=200m \
            --set resources.requests.memory=256Mi \
            --wait \
            --timeout=10m || log "WARN" "Failed to deploy $app"
    done
    
    # Wait for all deployments to be ready
    log "INFO" "Waiting for applications to be ready..."
    kubectl wait --for=condition=available \
        --timeout=600s \
        deployment --all \
        -n production || log "WARN" "Some deployments may not be ready"
    
    log "INFO" "Application deployment completed"
}

# Update DNS for failover
update_dns_failover() {
    local dr_load_balancer="$1"
    
    log "INFO" "Updating DNS for failover to DR site..."
    
    # Get current DNS record
    local current_record=$(aws route53 list-resource-record-sets \
        --hosted-zone-id "$ROUTE53_HOSTED_ZONE" \
        --query "ResourceRecordSets[?Name=='$PRIMARY_DOMAIN.']" \
        --output json)
    
    # Create change batch for DNS update
    cat > /tmp/dns-change.json << EOF
{
    "Comment": "Disaster Recovery DNS Failover - $(date)",
    "Changes": [
        {
            "Action": "UPSERT",
            "ResourceRecordSet": {
                "Name": "$PRIMARY_DOMAIN",
                "Type": "CNAME",
                "TTL": 60,
                "ResourceRecords": [
                    {
                        "Value": "$dr_load_balancer"
                    }
                ]
            }
        }
    ]
}
EOF
    
    # Apply DNS change
    local change_id=$(aws route53 change-resource-record-sets \
        --hosted-zone-id "$ROUTE53_HOSTED_ZONE" \
        --change-batch file:///tmp/dns-change.json \
        --query 'ChangeInfo.Id' \
        --output text)
    
    log "INFO" "DNS change submitted: $change_id"
    
    # Wait for DNS change to propagate
    aws route53 wait resource-record-sets-changed --id "$change_id"
    
    log "INFO" "DNS failover completed - traffic now routing to DR site"
    
    # Cleanup
    rm -f /tmp/dns-change.json
}

# Validate recovery
validate_recovery() {
    log "INFO" "Validating disaster recovery..."
    
    local validation_start=$(date +%s)
    local errors=0
    
    # Test database connectivity
    log "INFO" "Testing database connectivity..."
    if ! pg_isready -h "$DR_DB_ENDPOINT" -p 5432; then
        log "ERROR" "Database connectivity test failed"
        errors=$((errors + 1))
    fi
    
    # Test Redis connectivity
    log "INFO" "Testing Redis connectivity..."
    if ! redis-cli -h "$DR_REDIS_ENDPOINT" ping | grep -q PONG; then
        log "ERROR" "Redis connectivity test failed"
        errors=$((errors + 1))
    fi
    
    # Test application endpoints
    log "INFO" "Testing application endpoints..."
    local endpoints=(
        "https://$PRIMARY_DOMAIN/health"
        "https://$PRIMARY_DOMAIN/api/analytics/health"
        "https://$PRIMARY_DOMAIN/api/billing/health"
    )
    
    for endpoint in "${endpoints[@]}"; do
        local http_code=$(curl -s -o /dev/null -w "%{http_code}" "$endpoint" || echo "000")
        if [[ "$http_code" != "200" ]]; then
            log "ERROR" "Endpoint test failed: $endpoint (HTTP $http_code)"
            errors=$((errors + 1))
        else
            log "INFO" "Endpoint test passed: $endpoint"
        fi
    done
    
    # Test critical business functions
    log "INFO" "Testing critical business functions..."
    
    # Test API authentication
    local auth_test=$(curl -s -H "Authorization: Bearer test-token" \
        "https://$PRIMARY_DOMAIN/api/auth/validate" \
        -w "%{http_code}" -o /dev/null || echo "000")
    
    if [[ "$auth_test" != "200" ]]; then
        log "ERROR" "API authentication test failed"
        errors=$((errors + 1))
    fi
    
    # Calculate recovery time
    local validation_end=$(date +%s)
    local recovery_time=$((validation_end - DR_START_TIME))
    
    log "INFO" "Recovery validation completed in $((validation_end - validation_start)) seconds"
    log "INFO" "Total recovery time: $recovery_time seconds ($((recovery_time / 60)) minutes)"
    
    # Check against RTO
    if [[ $((recovery_time / 60)) -gt $RTO_MINUTES ]]; then
        log "WARN" "Recovery time ($((recovery_time / 60)) min) exceeded RTO target ($RTO_MINUTES min)"
    else
        log "INFO" "Recovery time within RTO target"
    fi
    
    if [[ $errors -eq 0 ]]; then
        log "INFO" "All validation tests passed"
        return 0
    else
        log "ERROR" "Validation failed with $errors errors"
        return 1
    fi
}

# Utility functions
generate_random_password() {
    openssl rand -base64 32 | tr -d "=+/" | cut -c1-25
}

get_dr_security_group() {
    aws ec2 describe-security-groups \
        --filters "Name=group-name,Values=dr-database-sg" \
        --query 'SecurityGroups[0].GroupId' \
        --output text \
        --region "$DR_REGION"
}

get_dr_subnet_group() {
    aws rds describe-db-subnet-groups \
        --query 'DBSubnetGroups[?contains(DBSubnetGroupName, `dr`)].DBSubnetGroupName | [0]' \
        --output text \
        --region "$DR_REGION"
}

get_db_password() {
    local db_identifier="$1"
    # In production, retrieve from AWS Secrets Manager
    aws secretsmanager get-secret-value \
        --secret-id "rds-db-credentials/$db_identifier" \
        --query 'SecretString' \
        --output text \
        --region "$DR_REGION" | jq -r '.password'
}

get_latest_stable_image_tag() {
    local app="$1"
    # Get latest stable tag from ECR
    aws ecr describe-images \
        --repository-name "$app" \
        --query 'sort_by(imageDetails, &imagePushedAt)[-1].imageTags[0]' \
        --output text \
        --region "$DR_REGION"
}

update_application_config() {
    local key="$1"
    local value="$2"
    
    # Update ConfigMap in Kubernetes
    kubectl patch configmap app-config \
        -n production \
        -p "{\"data\":{\"$key\":\"$value\"}}"
    
    # Restart deployments to pick up new config
    kubectl rollout restart deployment -n production
}

get_load_balancer_dns() {
    kubectl get service ingress-nginx-controller \
        -n ingress-nginx \
        -o jsonpath='{.status.loadBalancer.ingress[0].hostname}'
}

# Main disaster recovery orchestration
main() {
    export DR_START_TIME=$(date +%s)
    local disaster_type="${1:-general}"
    
    log "INFO" "=== DISASTER RECOVERY INITIATED ==="
    log "INFO" "Disaster Type: $disaster_type"
    log "INFO" "DR Region: $DR_REGION"
    log "INFO" "Target RTO: $RTO_MINUTES minutes"
    log "INFO" "Target RPO: $RPO_MINUTES minutes"
    
    # Send initial notification
    send_notification "DISASTER_RECOVERY_STARTED" "Disaster recovery initiated for $disaster_type"
    
    # Validate DR environment
    validate_dr_environment
    
    # Assess disaster scope
    assess_disaster "$disaster_type"
    
    # Execute recovery based on disaster type
    case "$disaster_type" in
        "database_failure")
            log "INFO" "Executing database failure recovery..."
            if [[ -n "${LATEST_BACKUP:-}" ]]; then
                export DR_DB_ENDPOINT=$(restore_database "$LATEST_BACKUP" "$DR_DB_IDENTIFIER")
            fi
            if [[ -n "${REDIS_FAILURE:-}" ]]; then
                local redis_backup=$(find_latest_backup "redis")
                export DR_REDIS_ENDPOINT=$(restore_redis "$redis_backup" "$DR_REDIS_CLUSTER")
            fi
            ;;
            
        "region_failure")
            log "INFO" "Executing region failure recovery..."
            # Full DR site activation
            export DR_DB_ENDPOINT=$(restore_database "$(find_latest_backup 'postgresql')" "$DR_DB_IDENTIFIER")
            export DR_REDIS_ENDPOINT=$(restore_redis "$(find_latest_backup 'redis')" "$DR_REDIS_CLUSTER")
            deploy_applications
            
            # Get DR load balancer
            local dr_lb=$(get_load_balancer_dns)
            update_dns_failover "$dr_lb"
            ;;
            
        "application_failure")
            log "INFO" "Executing application failure recovery..."
            deploy_applications
            ;;
            
        *)
            log "INFO" "Executing general disaster recovery..."
            # Conservative approach - full recovery
            export DR_DB_ENDPOINT=$(restore_database "$(find_latest_backup 'postgresql')" "$DR_DB_IDENTIFIER")
            export DR_REDIS_ENDPOINT=$(restore_redis "$(find_latest_backup 'redis')" "$DR_REDIS_CLUSTER")
            deploy_applications
            ;;
    esac
    
    # Validate recovery
    if validate_recovery; then
        local total_time=$(( $(date +%s) - DR_START_TIME ))
        log "INFO" "=== DISASTER RECOVERY COMPLETED SUCCESSFULLY ==="
        log "INFO" "Total recovery time: $((total_time / 60)) minutes"
        
        send_notification "DISASTER_RECOVERY_SUCCESS" \
            "Disaster recovery completed successfully in $((total_time / 60)) minutes"
    else
        log "ERROR" "=== DISASTER RECOVERY VALIDATION FAILED ==="
        send_notification "DISASTER_RECOVERY_VALIDATION_FAILED" \
            "Disaster recovery completed but validation failed"
        exit 1
    fi
}

# Help function
show_help() {
    cat << EOF
Usage: $0 [DISASTER_TYPE] [OPTIONS]

Disaster Recovery Script for E-commerce Analytics SaaS Platform

DISASTER TYPES:
    database_failure    Database server failure or corruption
    region_failure      Complete AWS region failure
    application_failure Application deployment issues
    security_incident   Security breach requiring system rebuild
    general            General disaster (default - full recovery)

OPTIONS:
    -c, --config FILE   Configuration file path
    -l, --log FILE      Log file path
    -h, --help          Show this help message
    --dry-run           Show what would be done without executing
    --validate-only     Only run validation checks

ENVIRONMENT VARIABLES:
    DR_REGION              Disaster recovery AWS region
    PRIMARY_REGION         Primary AWS region
    S3_BUCKET             Backup storage bucket
    DR_CLUSTER_NAME       DR Kubernetes cluster name
    RTO_MINUTES           Recovery Time Objective in minutes
    RPO_MINUTES           Recovery Point Objective in minutes

EXAMPLES:
    $0 database_failure    # Recover from database failure
    $0 region_failure      # Full region failover
    $0 --validate-only     # Run validation checks only
    $0 --dry-run general   # Show what would be done

EOF
}

# Parse command line arguments
DISASTER_TYPE="general"
while [[ $# -gt 0 ]]; do
    case $1 in
        database_failure|region_failure|application_failure|security_incident|general)
            DISASTER_TYPE="$1"
            shift
            ;;
        -c|--config)
            CONFIG_FILE="$2"
            shift 2
            ;;
        -l|--log)
            LOG_FILE="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        --dry-run)
            DRY_RUN=1
            shift
            ;;
        --validate-only)
            VALIDATE_ONLY=1
            shift
            ;;
        *)
            echo "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Dry run mode
if [[ "${DRY_RUN:-0}" == "1" ]]; then
    echo "DRY RUN MODE - No actual recovery will be performed"
    echo "Would execute disaster recovery for: $DISASTER_TYPE"
    echo "DR Region: $DR_REGION"
    echo "Primary Region: $PRIMARY_REGION"
    exit 0
fi

# Validate only mode
if [[ "${VALIDATE_ONLY:-0}" == "1" ]]; then
    validate_dr_environment
    echo "Validation completed successfully"
    exit 0
fi

# Execute main disaster recovery
main "$DISASTER_TYPE"