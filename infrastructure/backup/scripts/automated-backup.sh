#!/bin/bash

# Automated Backup Script for E-commerce Analytics SaaS Platform
# This script performs comprehensive backups of all critical system components

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_FILE="${CONFIG_FILE:-$SCRIPT_DIR/../config/backup.conf}"
LOG_FILE="${LOG_FILE:-/var/log/backup/backup-$(date +%Y%m%d_%H%M%S).log}"
LOCK_FILE="/var/run/backup.lock"

# Default configuration (can be overridden by config file)
BACKUP_TYPE="${BACKUP_TYPE:-incremental}"
S3_BUCKET="${S3_BUCKET:-ecommerce-analytics-backups}"
AWS_REGION="${AWS_REGION:-us-east-1}"
ENCRYPTION_KEY_ID="${ENCRYPTION_KEY_ID:-alias/backup-encryption-key}"
RETENTION_DAYS="${RETENTION_DAYS:-30}"
PARALLEL_JOBS="${PARALLEL_JOBS:-4}"

# Database configuration
DB_HOST="${DB_HOST:-analytics-db.cluster-xyz.us-east-1.rds.amazonaws.com}"
DB_PORT="${DB_PORT:-5432}"
DB_NAME="${DB_NAME:-ecommerce_analytics}"
DB_USER="${DB_USER:-backup_user}"
REDIS_HOST="${REDIS_HOST:-analytics-redis.abc123.cache.amazonaws.com}"
REDIS_PORT="${REDIS_PORT:-6379}"

# Notification configuration
SNS_TOPIC="${SNS_TOPIC:-arn:aws:sns:us-east-1:123456789012:backup-notifications}"
SLACK_WEBHOOK="${SLACK_WEBHOOK:-}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Load configuration file if it exists
if [[ -f "$CONFIG_FILE" ]]; then
    source "$CONFIG_FILE"
fi

# Create log directory
mkdir -p "$(dirname "$LOG_FILE")"

# Logging function
log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message" | tee -a "$LOG_FILE"
}

# Error handling
error_exit() {
    log "ERROR" "$1"
    cleanup
    send_notification "FAILURE" "Backup failed: $1"
    exit 1
}

# Cleanup function
cleanup() {
    log "INFO" "Performing cleanup..."
    
    # Remove lock file
    if [[ -f "$LOCK_FILE" ]]; then
        rm -f "$LOCK_FILE"
    fi
    
    # Clean up temporary files
    if [[ -n "${TEMP_DIR:-}" && -d "$TEMP_DIR" ]]; then
        rm -rf "$TEMP_DIR"
    fi
    
    # Stop any background jobs
    jobs -p | xargs -r kill 2>/dev/null || true
}

# Signal handlers
trap 'error_exit "Script interrupted by user"' INT TERM
trap 'cleanup' EXIT

# Check if another backup is running
check_lock() {
    if [[ -f "$LOCK_FILE" ]]; then
        local pid=$(cat "$LOCK_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            error_exit "Another backup process is already running (PID: $pid)"
        else
            log "WARN" "Stale lock file found, removing..."
            rm -f "$LOCK_FILE"
        fi
    fi
    
    # Create lock file
    echo $$ > "$LOCK_FILE"
}

# Dependency checks
check_dependencies() {
    log "INFO" "Checking dependencies..."
    
    local deps=("aws" "pg_dump" "redis-cli" "gzip" "parallel")
    local missing_deps=()
    
    for dep in "${deps[@]}"; do
        if ! command -v "$dep" &> /dev/null; then
            missing_deps+=("$dep")
        fi
    done
    
    if [[ ${#missing_deps[@]} -ne 0 ]]; then
        error_exit "Missing dependencies: ${missing_deps[*]}"
    fi
    
    # Check AWS credentials
    if ! aws sts get-caller-identity &> /dev/null; then
        error_exit "AWS credentials not configured or invalid"
    fi
    
    # Check S3 bucket access
    if ! aws s3 ls "s3://$S3_BUCKET" &> /dev/null; then
        error_exit "Cannot access S3 bucket: $S3_BUCKET"
    fi
    
    log "INFO" "All dependencies satisfied"
}

# Send notifications
send_notification() {
    local status="$1"
    local message="$2"
    
    # SNS notification
    if [[ -n "$SNS_TOPIC" ]]; then
        aws sns publish \
            --topic-arn "$SNS_TOPIC" \
            --subject "Backup $status - $(hostname)" \
            --message "$message" \
            --region "$AWS_REGION" &
    fi
    
    # Slack notification
    if [[ -n "$SLACK_WEBHOOK" ]]; then
        local color="good"
        [[ "$status" == "FAILURE" ]] && color="danger"
        [[ "$status" == "WARNING" ]] && color="warning"
        
        curl -X POST "$SLACK_WEBHOOK" \
            -H 'Content-type: application/json' \
            --data "{
                \"attachments\": [{
                    \"color\": \"$color\",
                    \"title\": \"Backup $status\",
                    \"text\": \"$message\",
                    \"fields\": [{
                        \"title\": \"Host\",
                        \"value\": \"$(hostname)\",
                        \"short\": true
                    }, {
                        \"title\": \"Time\",
                        \"value\": \"$(date)\",
                        \"short\": true
                    }]
                }]
            }" &
    fi
    
    wait # Wait for notifications to complete
}

# PostgreSQL backup
backup_postgresql() {
    log "INFO" "Starting PostgreSQL backup..."
    
    local backup_file="postgresql_${BACKUP_TYPE}_$(date +%Y%m%d_%H%M%S).sql"
    local backup_path="/tmp/$backup_file"
    local s3_key="postgresql/$(date +%Y/%m/%d)/$backup_file"
    
    # Set PGPASSWORD from environment or prompt
    if [[ -z "${PGPASSWORD:-}" ]]; then
        read -s -p "Enter PostgreSQL password: " PGPASSWORD
        export PGPASSWORD
        echo
    fi
    
    local start_time=$(date +%s)
    
    case "$BACKUP_TYPE" in
        "full")
            log "INFO" "Performing full PostgreSQL backup..."
            pg_dump -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
                --verbose \
                --format=custom \
                --compress=9 \
                --no-privileges \
                --no-owner \
                --file="$backup_path" || error_exit "PostgreSQL backup failed"
            ;;
        "incremental")
            log "INFO" "Performing incremental PostgreSQL backup..."
            # For incremental, we'll use WAL-E or similar for transaction logs
            # This is a simplified version - in production, use proper WAL archiving
            pg_dump -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
                --verbose \
                --format=custom \
                --compress=9 \
                --no-privileges \
                --no-owner \
                --file="$backup_path" || error_exit "PostgreSQL incremental backup failed"
            ;;
        *)
            error_exit "Unknown backup type: $BACKUP_TYPE"
            ;;
    esac
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    local size=$(stat -c%s "$backup_path")
    local size_mb=$((size / 1024 / 1024))
    
    log "INFO" "PostgreSQL backup completed in ${duration}s, size: ${size_mb}MB"
    
    # Upload to S3 with encryption
    log "INFO" "Uploading PostgreSQL backup to S3..."
    aws s3 cp "$backup_path" "s3://$S3_BUCKET/$s3_key" \
        --sse aws:kms \
        --sse-kms-key-id "$ENCRYPTION_KEY_ID" \
        --storage-class STANDARD \
        --metadata "backup-type=$BACKUP_TYPE,source=postgresql,size=$size" || error_exit "S3 upload failed"
    
    # Verify upload
    if ! aws s3 ls "s3://$S3_BUCKET/$s3_key" &> /dev/null; then
        error_exit "Backup verification failed - file not found in S3"
    fi
    
    # Cleanup local file
    rm -f "$backup_path"
    
    log "INFO" "PostgreSQL backup completed successfully"
    echo "$s3_key" # Return S3 key for manifest
}

# Redis backup
backup_redis() {
    log "INFO" "Starting Redis backup..."
    
    local backup_file="redis_$(date +%Y%m%d_%H%M%S).rdb"
    local backup_path="/tmp/$backup_file"
    local s3_key="redis/$(date +%Y/%m/%d)/$backup_file"
    
    local start_time=$(date +%s)
    
    # Trigger Redis background save
    if ! redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" BGSAVE; then
        error_exit "Redis BGSAVE command failed"
    fi
    
    # Wait for background save to complete
    log "INFO" "Waiting for Redis background save to complete..."
    while true; do
        local last_save=$(redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" LASTSAVE)
        sleep 5
        local current_save=$(redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" LASTSAVE)
        if [[ "$current_save" != "$last_save" ]]; then
            break
        fi
        log "INFO" "Still waiting for Redis save to complete..."
    done
    
    # For managed Redis (ElastiCache), we can't directly access the RDB file
    # Instead, we'll create a logical backup using DUMP commands
    log "INFO" "Creating Redis logical backup..."
    
    # Get all keys and create backup script
    redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" --scan | while read -r key; do
        redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" DUMP "$key" | \
        sed "s/^/RESTORE \"$key\" 0 /"
    done > "$backup_path"
    
    # Compress the backup
    gzip "$backup_path"
    backup_path="${backup_path}.gz"
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    local size=$(stat -c%s "$backup_path")
    local size_mb=$((size / 1024 / 1024))
    
    log "INFO" "Redis backup completed in ${duration}s, size: ${size_mb}MB"
    
    # Upload to S3
    log "INFO" "Uploading Redis backup to S3..."
    aws s3 cp "$backup_path" "s3://$S3_BUCKET/$s3_key" \
        --sse aws:kms \
        --sse-kms-key-id "$ENCRYPTION_KEY_ID" \
        --storage-class STANDARD \
        --metadata "backup-type=$BACKUP_TYPE,source=redis,size=$size" || error_exit "Redis S3 upload failed"
    
    # Cleanup
    rm -f "$backup_path"
    
    log "INFO" "Redis backup completed successfully"
    echo "$s3_key" # Return S3 key for manifest
}

# Configuration backup
backup_configurations() {
    log "INFO" "Starting configuration backup..."
    
    local config_backup="configurations_$(date +%Y%m%d_%H%M%S).tar.gz"
    local backup_path="/tmp/$config_backup"
    local s3_key="configurations/$(date +%Y/%m/%d)/$config_backup"
    
    # Create temporary directory for configurations
    local temp_config_dir="/tmp/config_backup_$$"
    mkdir -p "$temp_config_dir"
    
    # Backup Kubernetes configurations
    if command -v kubectl &> /dev/null; then
        log "INFO" "Backing up Kubernetes configurations..."
        local k8s_dir="$temp_config_dir/kubernetes"
        mkdir -p "$k8s_dir"
        
        # Backup all resources
        kubectl get all --all-namespaces -o yaml > "$k8s_dir/all-resources.yaml" 2>/dev/null || true
        kubectl get configmaps --all-namespaces -o yaml > "$k8s_dir/configmaps.yaml" 2>/dev/null || true
        kubectl get secrets --all-namespaces -o yaml > "$k8s_dir/secrets.yaml" 2>/dev/null || true
        kubectl get ingress --all-namespaces -o yaml > "$k8s_dir/ingress.yaml" 2>/dev/null || true
        kubectl get pv,pvc --all-namespaces -o yaml > "$k8s_dir/volumes.yaml" 2>/dev/null || true
    fi
    
    # Backup Terraform state (if accessible)
    if [[ -f "./terraform.tfstate" ]]; then
        log "INFO" "Backing up Terraform state..."
        cp ./terraform.tfstate "$temp_config_dir/terraform.tfstate"
    fi
    
    # Backup application configurations
    local app_configs=(
        "/etc/analytics"
        "/etc/dashboard" 
        "/etc/billing"
        "/etc/integration"
    )
    
    for config_dir in "${app_configs[@]}"; do
        if [[ -d "$config_dir" ]]; then
            log "INFO" "Backing up $config_dir..."
            cp -r "$config_dir" "$temp_config_dir/" 2>/dev/null || true
        fi
    done
    
    # Create compressed archive
    tar -czf "$backup_path" -C "/tmp" "config_backup_$$" || error_exit "Configuration backup creation failed"
    
    # Cleanup temp directory
    rm -rf "$temp_config_dir"
    
    local size=$(stat -c%s "$backup_path")
    local size_mb=$((size / 1024 / 1024))
    log "INFO" "Configuration backup created, size: ${size_mb}MB"
    
    # Upload to S3
    log "INFO" "Uploading configuration backup to S3..."
    aws s3 cp "$backup_path" "s3://$S3_BUCKET/$s3_key" \
        --sse aws:kms \
        --sse-kms-key-id "$ENCRYPTION_KEY_ID" \
        --storage-class STANDARD \
        --metadata "backup-type=$BACKUP_TYPE,source=configurations,size=$size" || error_exit "Configuration S3 upload failed"
    
    # Cleanup
    rm -f "$backup_path"
    
    log "INFO" "Configuration backup completed successfully"
    echo "$s3_key" # Return S3 key for manifest
}

# Application data backup
backup_application_data() {
    log "INFO" "Starting application data backup..."
    
    local data_backup="application_data_$(date +%Y%m%d_%H%M%S).tar.gz"
    local backup_path="/tmp/$data_backup"
    local s3_key="application-data/$(date +%Y/%m/%d)/$data_backup"
    
    # Create temporary directory
    local temp_data_dir="/tmp/app_data_backup_$$"
    mkdir -p "$temp_data_dir"
    
    # Backup application logs (recent)
    log "INFO" "Backing up recent application logs..."
    local log_dirs=(
        "/var/log/analytics"
        "/var/log/dashboard"
        "/var/log/billing"
        "/var/log/integration"
    )
    
    for log_dir in "${log_dirs[@]}"; do
        if [[ -d "$log_dir" ]]; then
            # Only backup logs from last 7 days
            find "$log_dir" -type f -mtime -7 -exec cp {} "$temp_data_dir/" \; 2>/dev/null || true
        fi
    done
    
    # Backup user-uploaded files (if any)
    local upload_dirs=(
        "/var/uploads"
        "/opt/analytics/uploads"
    )
    
    for upload_dir in "${upload_dirs[@]}"; do
        if [[ -d "$upload_dir" ]]; then
            log "INFO" "Backing up uploads from $upload_dir..."
            cp -r "$upload_dir" "$temp_data_dir/" 2>/dev/null || true
        fi
    done
    
    # Only create backup if we have data
    if [[ $(find "$temp_data_dir" -type f | wc -l) -gt 0 ]]; then
        # Create compressed archive
        tar -czf "$backup_path" -C "/tmp" "app_data_backup_$$" || error_exit "Application data backup creation failed"
        
        local size=$(stat -c%s "$backup_path")
        local size_mb=$((size / 1024 / 1024))
        log "INFO" "Application data backup created, size: ${size_mb}MB"
        
        # Upload to S3
        log "INFO" "Uploading application data backup to S3..."
        aws s3 cp "$backup_path" "s3://$S3_BUCKET/$s3_key" \
            --sse aws:kms \
            --sse-kms-key-id "$ENCRYPTION_KEY_ID" \
            --storage-class STANDARD \
            --metadata "backup-type=$BACKUP_TYPE,source=application-data,size=$size" || error_exit "Application data S3 upload failed"
        
        # Cleanup
        rm -f "$backup_path"
        
        log "INFO" "Application data backup completed successfully"
        echo "$s3_key" # Return S3 key for manifest
    else
        log "INFO" "No application data found to backup"
    fi
    
    # Cleanup temp directory
    rm -rf "$temp_data_dir"
}

# Create backup manifest
create_backup_manifest() {
    local backup_files=("$@")
    
    log "INFO" "Creating backup manifest..."
    
    local manifest_file="backup_manifest_$(date +%Y%m%d_%H%M%S).json"
    local manifest_path="/tmp/$manifest_file"
    local s3_key="manifests/$(date +%Y/%m/%d)/$manifest_file"
    
    # Create manifest JSON
    cat > "$manifest_path" << EOF
{
    "backup_id": "$(uuidgen)",
    "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
    "backup_type": "$BACKUP_TYPE",
    "hostname": "$(hostname)",
    "script_version": "1.0",
    "files": [
$(printf '        {"path": "%s", "type": "%s"}' "${backup_files[@]}" | sed 's/$/,/' | sed '$s/,$//')
    ],
    "environment": {
        "aws_region": "$AWS_REGION",
        "s3_bucket": "$S3_BUCKET",
        "encryption_key": "$ENCRYPTION_KEY_ID"
    },
    "statistics": {
        "total_files": ${#backup_files[@]},
        "total_duration_seconds": $(($(date +%s) - backup_start_time))
    }
}
EOF
    
    # Upload manifest
    aws s3 cp "$manifest_path" "s3://$S3_BUCKET/$s3_key" \
        --sse aws:kms \
        --sse-kms-key-id "$ENCRYPTION_KEY_ID" \
        --storage-class STANDARD || error_exit "Manifest upload failed"
    
    # Cleanup
    rm -f "$manifest_path"
    
    log "INFO" "Backup manifest created: $s3_key"
}

# Cleanup old backups
cleanup_old_backups() {
    log "INFO" "Cleaning up old backups (older than $RETENTION_DAYS days)..."
    
    local cutoff_date=$(date -d "$RETENTION_DAYS days ago" +%Y-%m-%d)
    
    # List and delete old backups
    local prefixes=("postgresql/" "redis/" "configurations/" "application-data/" "manifests/")
    
    for prefix in "${prefixes[@]}"; do
        log "INFO" "Cleaning up old backups in $prefix..."
        
        aws s3api list-objects-v2 \
            --bucket "$S3_BUCKET" \
            --prefix "$prefix" \
            --query "Contents[?LastModified<='$cutoff_date'].Key" \
            --output text | \
        while read -r key; do
            if [[ -n "$key" && "$key" != "None" ]]; then
                log "INFO" "Deleting old backup: $key"
                aws s3 rm "s3://$S3_BUCKET/$key" || log "WARN" "Failed to delete $key"
            fi
        done
    done
    
    log "INFO" "Old backup cleanup completed"
}

# Verify backup integrity
verify_backup_integrity() {
    local s3_key="$1"
    
    log "INFO" "Verifying backup integrity for $s3_key..."
    
    # Check if file exists and get metadata
    local metadata=$(aws s3api head-object \
        --bucket "$S3_BUCKET" \
        --key "$s3_key" \
        --query 'Metadata' \
        --output json 2>/dev/null) || return 1
    
    # Verify file size is greater than 0
    local content_length=$(aws s3api head-object \
        --bucket "$S3_BUCKET" \
        --key "$s3_key" \
        --query 'ContentLength' \
        --output text 2>/dev/null) || return 1
    
    if [[ "$content_length" -le 0 ]]; then
        log "ERROR" "Backup file $s3_key has zero size"
        return 1
    fi
    
    # Verify encryption
    local encryption=$(aws s3api head-object \
        --bucket "$S3_BUCKET" \
        --key "$s3_key" \
        --query 'ServerSideEncryption' \
        --output text 2>/dev/null) || return 1
    
    if [[ "$encryption" != "aws:kms" ]]; then
        log "WARN" "Backup file $s3_key is not encrypted with KMS"
    fi
    
    log "INFO" "Backup integrity verification passed for $s3_key"
    return 0
}

# Main backup execution
main() {
    local backup_start_time=$(date +%s)
    
    log "INFO" "Starting backup process (Type: $BACKUP_TYPE)"
    log "INFO" "Backup destination: s3://$S3_BUCKET"
    log "INFO" "Encryption key: $ENCRYPTION_KEY_ID"
    
    # Pre-flight checks
    check_lock
    check_dependencies
    
    # Array to store backup file paths
    local backup_files=()
    
    # Execute backups in parallel where possible
    log "INFO" "Executing backup jobs..."
    
    # Database backups (can run in parallel)
    (
        postgresql_backup=$(backup_postgresql)
        echo "postgresql:$postgresql_backup" > "/tmp/backup_results_$$_pg"
    ) &
    
    (
        redis_backup=$(backup_redis)
        echo "redis:$redis_backup" > "/tmp/backup_results_$$_redis"
    ) &
    
    # Configuration backup (can run independently)
    (
        config_backup=$(backup_configurations)
        echo "config:$config_backup" > "/tmp/backup_results_$$_config"
    ) &
    
    # Application data backup (can run independently)
    (
        app_data_backup=$(backup_application_data)
        if [[ -n "$app_data_backup" ]]; then
            echo "appdata:$app_data_backup" > "/tmp/backup_results_$$_appdata"
        fi
    ) &
    
    # Wait for all backup jobs to complete
    log "INFO" "Waiting for backup jobs to complete..."
    wait
    
    # Collect results
    for result_file in /tmp/backup_results_$$_*; do
        if [[ -f "$result_file" ]]; then
            local backup_info=$(cat "$result_file")
            local backup_file=$(echo "$backup_info" | cut -d: -f2-)
            if [[ -n "$backup_file" ]]; then
                backup_files+=("$backup_file")
                
                # Verify each backup
                if ! verify_backup_integrity "$backup_file"; then
                    log "WARN" "Backup integrity verification failed for $backup_file"
                fi
            fi
            rm -f "$result_file"
        fi
    done
    
    # Create backup manifest
    if [[ ${#backup_files[@]} -gt 0 ]]; then
        create_backup_manifest "${backup_files[@]}"
    else
        error_exit "No backups were created successfully"
    fi
    
    # Cleanup old backups
    cleanup_old_backups
    
    # Calculate total time
    local backup_end_time=$(date +%s)
    local total_duration=$((backup_end_time - backup_start_time))
    
    log "INFO" "Backup process completed successfully"
    log "INFO" "Total duration: ${total_duration} seconds"
    log "INFO" "Files backed up: ${#backup_files[@]}"
    
    # Send success notification
    send_notification "SUCCESS" "Backup completed successfully. Duration: ${total_duration}s, Files: ${#backup_files[@]}"
    
    return 0
}

# Help function
show_help() {
    cat << EOF
Usage: $0 [OPTIONS]

Automated backup script for E-commerce Analytics SaaS Platform

OPTIONS:
    -t, --type TYPE         Backup type (full|incremental) [default: incremental]
    -c, --config FILE       Configuration file path
    -l, --log FILE          Log file path
    -h, --help              Show this help message
    -v, --verbose           Verbose output
    --dry-run               Show what would be done without executing

ENVIRONMENT VARIABLES:
    BACKUP_TYPE             Backup type (full|incremental)
    S3_BUCKET              S3 bucket for backups
    AWS_REGION             AWS region
    ENCRYPTION_KEY_ID       KMS key ID for encryption
    DB_HOST                Database hostname
    REDIS_HOST             Redis hostname
    SNS_TOPIC              SNS topic for notifications
    SLACK_WEBHOOK          Slack webhook URL

EXAMPLES:
    $0                      # Run incremental backup with default settings
    $0 -t full              # Run full backup
    $0 -c /etc/backup.conf  # Use custom configuration file
    $0 --dry-run            # Show what would be done

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -t|--type)
            BACKUP_TYPE="$2"
            shift 2
            ;;
        -c|--config)
            CONFIG_FILE="$2"
            shift 2
            ;;
        -l|--log)
            LOG_FILE="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        -v|--verbose)
            set -x
            shift
            ;;
        --dry-run)
            DRY_RUN=1
            shift
            ;;
        *)
            echo "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Validate backup type
if [[ "$BACKUP_TYPE" != "full" && "$BACKUP_TYPE" != "incremental" ]]; then
    error_exit "Invalid backup type: $BACKUP_TYPE. Must be 'full' or 'incremental'"
fi

# Dry run mode
if [[ "${DRY_RUN:-0}" == "1" ]]; then
    echo "DRY RUN MODE - No actual backups will be performed"
    echo "Configuration:"
    echo "  Backup Type: $BACKUP_TYPE"
    echo "  S3 Bucket: $S3_BUCKET"
    echo "  AWS Region: $AWS_REGION"
    echo "  Database Host: $DB_HOST"
    echo "  Redis Host: $REDIS_HOST"
    echo "  Log File: $LOG_FILE"
    exit 0
fi

# Run main function
main "$@"