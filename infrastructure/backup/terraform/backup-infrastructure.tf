# Backup and Disaster Recovery Infrastructure
# Terraform configuration for backup storage, DR site, and automation

terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

# Variables
variable "environment" {
  description = "Environment name"
  type        = string
  default     = "production"
}

variable "project_name" {
  description = "Project name"
  type        = string
  default     = "ecommerce-analytics"
}

variable "primary_region" {
  description = "Primary AWS region"
  type        = string
  default     = "us-east-1"
}

variable "dr_region" {
  description = "Disaster recovery AWS region"
  type        = string
  default     = "us-west-2"
}

variable "backup_retention_days" {
  description = "Backup retention period in days"
  type        = number
  default     = 30
}

variable "backup_transition_days" {
  description = "Days before transitioning to cheaper storage"
  type        = number
  default     = 30
}

variable "archive_transition_days" {
  description = "Days before transitioning to archive storage"
  type        = number
  default     = 90
}

variable "backup_schedule" {
  description = "Backup schedule expression"
  type        = string
  default     = "cron(0 2 * * ? *)" # Daily at 2 AM UTC
}

variable "kms_key_rotation" {
  description = "Enable automatic KMS key rotation"
  type        = bool
  default     = true
}

# Local values
locals {
  common_tags = {
    Environment = var.environment
    Project     = var.project_name
    Component   = "backup"
    ManagedBy   = "terraform"
  }
  
  backup_bucket_name = "${var.project_name}-backups-${random_id.bucket_suffix.hex}"
  dr_bucket_name     = "${var.project_name}-dr-backups-${random_id.bucket_suffix.hex}"
}

# Random ID for unique bucket names
resource "random_id" "bucket_suffix" {
  byte_length = 4
}

# Data sources
data "aws_caller_identity" "current" {}
data "aws_region" "current" {}

# KMS Key for backup encryption
resource "aws_kms_key" "backup_encryption" {
  description             = "KMS key for backup encryption"
  deletion_window_in_days = 7
  enable_key_rotation     = var.kms_key_rotation
  
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "Enable IAM User Permissions"
        Effect = "Allow"
        Principal = {
          AWS = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
        }
        Action   = "kms:*"
        Resource = "*"
      },
      {
        Sid    = "Allow backup services"
        Effect = "Allow"
        Principal = {
          Service = [
            "s3.amazonaws.com",
            "rds.amazonaws.com",
            "lambda.amazonaws.com"
          ]
        }
        Action = [
          "kms:Decrypt",
          "kms:GenerateDataKey",
          "kms:DescribeKey"
        ]
        Resource = "*"
      }
    ]
  })
  
  tags = merge(local.common_tags, {
    Name = "${var.project_name}-backup-encryption-key"
  })
}

# KMS Key Alias
resource "aws_kms_alias" "backup_encryption" {
  name          = "alias/${var.project_name}-backup-encryption"
  target_key_id = aws_kms_key.backup_encryption.key_id
}

# Primary backup S3 bucket
resource "aws_s3_bucket" "backup_primary" {
  bucket = local.backup_bucket_name
  
  tags = merge(local.common_tags, {
    Name        = "${var.project_name}-primary-backups"
    Purpose     = "primary-backup-storage"
    Region      = var.primary_region
  })
}

# S3 bucket versioning
resource "aws_s3_bucket_versioning" "backup_primary" {
  bucket = aws_s3_bucket.backup_primary.id
  versioning_configuration {
    status = "Enabled"
  }
}

# S3 bucket encryption
resource "aws_s3_bucket_server_side_encryption_configuration" "backup_primary" {
  bucket = aws_s3_bucket.backup_primary.id
  
  rule {
    apply_server_side_encryption_by_default {
      kms_master_key_id = aws_kms_key.backup_encryption.arn
      sse_algorithm     = "aws:kms"
    }
    bucket_key_enabled = true
  }
}

# S3 bucket public access block
resource "aws_s3_bucket_public_access_block" "backup_primary" {
  bucket = aws_s3_bucket.backup_primary.id
  
  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# S3 bucket lifecycle configuration
resource "aws_s3_bucket_lifecycle_configuration" "backup_primary" {
  bucket = aws_s3_bucket.backup_primary.id
  
  rule {
    id     = "backup_lifecycle"
    status = "Enabled"
    
    # Database backups
    filter {
      prefix = "postgresql/"
    }
    
    transition {
      days          = var.backup_transition_days
      storage_class = "STANDARD_IA"
    }
    
    transition {
      days          = var.archive_transition_days
      storage_class = "GLACIER"
    }
    
    transition {
      days          = 365
      storage_class = "DEEP_ARCHIVE"
    }
    
    expiration {
      days = 2555 # 7 years for compliance
    }
    
    noncurrent_version_expiration {
      noncurrent_days = 90
    }
  }
  
  rule {
    id     = "log_lifecycle"
    status = "Enabled"
    
    filter {
      prefix = "logs/"
    }
    
    transition {
      days          = 7
      storage_class = "STANDARD_IA"
    }
    
    transition {
      days          = 30
      storage_class = "GLACIER"
    }
    
    expiration {
      days = 365 # 1 year for logs
    }
  }
  
  rule {
    id     = "config_lifecycle"
    status = "Enabled"
    
    filter {
      prefix = "configurations/"
    }
    
    transition {
      days          = 90
      storage_class = "STANDARD_IA"
    }
    
    # Keep configurations indefinitely
  }
}

# DR region S3 bucket
resource "aws_s3_bucket" "backup_dr" {
  provider = aws.dr
  bucket   = local.dr_bucket_name
  
  tags = merge(local.common_tags, {
    Name        = "${var.project_name}-dr-backups"
    Purpose     = "disaster-recovery-backup-storage"
    Region      = var.dr_region
  })
}

# DR bucket versioning
resource "aws_s3_bucket_versioning" "backup_dr" {
  provider = aws.dr
  bucket   = aws_s3_bucket.backup_dr.id
  versioning_configuration {
    status = "Enabled"
  }
}

# DR bucket encryption
resource "aws_s3_bucket_server_side_encryption_configuration" "backup_dr" {
  provider = aws.dr
  bucket   = aws_s3_bucket.backup_dr.id
  
  rule {
    apply_server_side_encryption_by_default {
      kms_master_key_id = aws_kms_key.backup_encryption_dr.arn
      sse_algorithm     = "aws:kms"
    }
    bucket_key_enabled = true
  }
}

# DR region KMS key
resource "aws_kms_key" "backup_encryption_dr" {
  provider                = aws.dr
  description             = "KMS key for DR backup encryption"
  deletion_window_in_days = 7
  enable_key_rotation     = var.kms_key_rotation
  
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "Enable IAM User Permissions"
        Effect = "Allow"
        Principal = {
          AWS = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
        }
        Action   = "kms:*"
        Resource = "*"
      }
    ]
  })
  
  tags = merge(local.common_tags, {
    Name = "${var.project_name}-dr-backup-encryption-key"
  })
}

# DR KMS Key Alias
resource "aws_kms_alias" "backup_encryption_dr" {
  provider      = aws.dr
  name          = "alias/${var.project_name}-dr-backup-encryption"
  target_key_id = aws_kms_key.backup_encryption_dr.key_id
}

# Cross-region replication configuration
resource "aws_s3_bucket_replication_configuration" "backup_replication" {
  role   = aws_iam_role.replication.arn
  bucket = aws_s3_bucket.backup_primary.id
  
  rule {
    id     = "backup_replication"
    status = "Enabled"
    
    priority = 1
    
    filter {
      prefix = "postgresql/"
    }
    
    destination {
      bucket        = aws_s3_bucket.backup_dr.arn
      storage_class = "STANDARD_IA"
      
      encryption_configuration {
        replica_kms_key_id = aws_kms_key.backup_encryption_dr.arn
      }
    }
  }
  
  rule {
    id     = "config_replication"
    status = "Enabled"
    
    priority = 2
    
    filter {
      prefix = "configurations/"
    }
    
    destination {
      bucket        = aws_s3_bucket.backup_dr.arn
      storage_class = "STANDARD"
      
      encryption_configuration {
        replica_kms_key_id = aws_kms_key.backup_encryption_dr.arn
      }
    }
  }
  
  depends_on = [aws_s3_bucket_versioning.backup_primary]
}

# IAM role for S3 replication
resource "aws_iam_role" "replication" {
  name = "${var.project_name}-backup-replication-role"
  
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "s3.amazonaws.com"
        }
      }
    ]
  })
  
  tags = local.common_tags
}

# IAM policy for S3 replication
resource "aws_iam_role_policy" "replication" {
  name = "${var.project_name}-backup-replication-policy"
  role = aws_iam_role.replication.id
  
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "s3:GetObjectVersionForReplication",
          "s3:GetObjectVersionAcl",
          "s3:GetObjectVersionTagging"
        ]
        Resource = "${aws_s3_bucket.backup_primary.arn}/*"
      },
      {
        Effect = "Allow"
        Action = [
          "s3:ListBucket"
        ]
        Resource = aws_s3_bucket.backup_primary.arn
      },
      {
        Effect = "Allow"
        Action = [
          "s3:ReplicateObject",
          "s3:ReplicateDelete",
          "s3:ReplicateTags"
        ]
        Resource = "${aws_s3_bucket.backup_dr.arn}/*"
      },
      {
        Effect = "Allow"
        Action = [
          "kms:Decrypt",
          "kms:GenerateDataKey"
        ]
        Resource = [
          aws_kms_key.backup_encryption.arn,
          aws_kms_key.backup_encryption_dr.arn
        ]
      }
    ]
  })
}

# Lambda function for automated backups
resource "aws_lambda_function" "backup_orchestrator" {
  filename         = "backup_orchestrator.zip"
  function_name    = "${var.project_name}-backup-orchestrator"
  role            = aws_iam_role.lambda_backup.arn
  handler         = "lambda_function.lambda_handler"
  runtime         = "python3.9"
  timeout         = 900
  
  environment {
    variables = {
      S3_BUCKET          = aws_s3_bucket.backup_primary.bucket
      KMS_KEY_ID         = aws_kms_key.backup_encryption.key_id
      SNS_TOPIC_ARN      = aws_sns_topic.backup_notifications.arn
      DR_REGION          = var.dr_region
    }
  }
  
  tags = merge(local.common_tags, {
    Name = "${var.project_name}-backup-orchestrator"
  })
}

# Lambda function code (inline for demonstration)
data "archive_file" "backup_orchestrator" {
  type        = "zip"
  output_path = "backup_orchestrator.zip"
  
  source {
    content = templatefile("${path.module}/lambda/backup_orchestrator.py", {
      s3_bucket = aws_s3_bucket.backup_primary.bucket
      kms_key_id = aws_kms_key.backup_encryption.key_id
    })
    filename = "lambda_function.py"
  }
}

# IAM role for Lambda
resource "aws_iam_role" "lambda_backup" {
  name = "${var.project_name}-lambda-backup-role"
  
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })
  
  tags = local.common_tags
}

# IAM policy for Lambda backup function
resource "aws_iam_role_policy" "lambda_backup" {
  name = "${var.project_name}-lambda-backup-policy"
  role = aws_iam_role.lambda_backup.id
  
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ]
        Resource = "arn:aws:logs:*:*:*"
      },
      {
        Effect = "Allow"
        Action = [
          "s3:PutObject",
          "s3:GetObject",
          "s3:ListBucket"
        ]
        Resource = [
          aws_s3_bucket.backup_primary.arn,
          "${aws_s3_bucket.backup_primary.arn}/*"
        ]
      },
      {
        Effect = "Allow"
        Action = [
          "rds:CreateDBSnapshot",
          "rds:DescribeDBInstances",
          "rds:DescribeDBSnapshots"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = [
          "kms:Decrypt",
          "kms:GenerateDataKey",
          "kms:DescribeKey"
        ]
        Resource = aws_kms_key.backup_encryption.arn
      },
      {
        Effect = "Allow"
        Action = [
          "sns:Publish"
        ]
        Resource = aws_sns_topic.backup_notifications.arn
      }
    ]
  })
}

# EventBridge rule for scheduled backups
resource "aws_cloudwatch_event_rule" "backup_schedule" {
  name                = "${var.project_name}-backup-schedule"
  description         = "Trigger backup orchestrator on schedule"
  schedule_expression = var.backup_schedule
  
  tags = local.common_tags
}

# EventBridge target
resource "aws_cloudwatch_event_target" "backup_lambda" {
  rule      = aws_cloudwatch_event_rule.backup_schedule.name
  target_id = "BackupOrchestrator"
  arn       = aws_lambda_function.backup_orchestrator.arn
}

# Lambda permission for EventBridge
resource "aws_lambda_permission" "allow_eventbridge" {
  statement_id  = "AllowExecutionFromEventBridge"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.backup_orchestrator.function_name
  principal     = "events.amazonaws.com"
  source_arn    = aws_cloudwatch_event_rule.backup_schedule.arn
}

# SNS topic for backup notifications
resource "aws_sns_topic" "backup_notifications" {
  name = "${var.project_name}-backup-notifications"
  
  tags = local.common_tags
}

# SNS topic policy
resource "aws_sns_topic_policy" "backup_notifications" {
  arn = aws_sns_topic.backup_notifications.arn
  
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          AWS = "*"
        }
        Action = [
          "SNS:GetTopicAttributes",
          "SNS:SetTopicAttributes",
          "SNS:AddPermission",
          "SNS:RemovePermission",
          "SNS:DeleteTopic",
          "SNS:Subscribe",
          "SNS:ListSubscriptionsByTopic",
          "SNS:Publish"
        ]
        Resource = aws_sns_topic.backup_notifications.arn
        Condition = {
          StringEquals = {
            "AWS:SourceAccount" = data.aws_caller_identity.current.account_id
          }
        }
      }
    ]
  })
}

# CloudWatch alarms for backup monitoring
resource "aws_cloudwatch_metric_alarm" "backup_failure" {
  alarm_name          = "${var.project_name}-backup-failure"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "1"
  metric_name         = "Errors"
  namespace           = "AWS/Lambda"
  period              = "300"
  statistic           = "Sum"
  threshold           = "0"
  alarm_description   = "This metric monitors backup lambda failures"
  alarm_actions       = [aws_sns_topic.backup_notifications.arn]
  
  dimensions = {
    FunctionName = aws_lambda_function.backup_orchestrator.function_name
  }
  
  tags = local.common_tags
}

# CloudWatch dashboard for backup monitoring
resource "aws_cloudwatch_dashboard" "backup_monitoring" {
  dashboard_name = "${var.project_name}-backup-monitoring"
  
  dashboard_body = jsonencode({
    widgets = [
      {
        type   = "metric"
        x      = 0
        y      = 0
        width  = 12
        height = 6
        
        properties = {
          metrics = [
            ["AWS/Lambda", "Duration", "FunctionName", aws_lambda_function.backup_orchestrator.function_name],
            ["AWS/Lambda", "Invocations", "FunctionName", aws_lambda_function.backup_orchestrator.function_name],
            ["AWS/Lambda", "Errors", "FunctionName", aws_lambda_function.backup_orchestrator.function_name]
          ]
          period = 300
          stat   = "Average"
          region = data.aws_region.current.name
          title  = "Backup Lambda Metrics"
        }
      },
      {
        type   = "metric"
        x      = 0
        y      = 6
        width  = 12
        height = 6
        
        properties = {
          metrics = [
            ["AWS/S3", "BucketSizeBytes", "BucketName", aws_s3_bucket.backup_primary.bucket, "StorageType", "StandardStorage"],
            ["AWS/S3", "NumberOfObjects", "BucketName", aws_s3_bucket.backup_primary.bucket, "StorageType", "AllStorageTypes"]
          ]
          period = 86400
          stat   = "Average"
          region = data.aws_region.current.name
          title  = "Backup Storage Metrics"
        }
      }
    ]
  })
}

# Backup IAM user for external scripts
resource "aws_iam_user" "backup_user" {
  name = "${var.project_name}-backup-user"
  
  tags = local.common_tags
}

# IAM policy for backup user
resource "aws_iam_user_policy" "backup_user" {
  name = "${var.project_name}-backup-user-policy"
  user = aws_iam_user.backup_user.name
  
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "s3:PutObject",
          "s3:GetObject",
          "s3:ListBucket",
          "s3:DeleteObject"
        ]
        Resource = [
          aws_s3_bucket.backup_primary.arn,
          "${aws_s3_bucket.backup_primary.arn}/*"
        ]
      },
      {
        Effect = "Allow"
        Action = [
          "kms:Decrypt",
          "kms:GenerateDataKey",
          "kms:DescribeKey"
        ]
        Resource = aws_kms_key.backup_encryption.arn
      },
      {
        Effect = "Allow"
        Action = [
          "sns:Publish"
        ]
        Resource = aws_sns_topic.backup_notifications.arn
      }
    ]
  })
}

# Access keys for backup user
resource "aws_iam_access_key" "backup_user" {
  user = aws_iam_user.backup_user.name
}

# Store access keys in AWS Secrets Manager
resource "aws_secretsmanager_secret" "backup_credentials" {
  name = "${var.project_name}-backup-credentials"
  description = "Backup user access credentials"
  
  tags = local.common_tags
}

resource "aws_secretsmanager_secret_version" "backup_credentials" {
  secret_id = aws_secretsmanager_secret.backup_credentials.id
  secret_string = jsonencode({
    access_key_id     = aws_iam_access_key.backup_user.id
    secret_access_key = aws_iam_access_key.backup_user.secret
    s3_bucket         = aws_s3_bucket.backup_primary.bucket
    kms_key_id        = aws_kms_key.backup_encryption.key_id
    sns_topic_arn     = aws_sns_topic.backup_notifications.arn
  })
}

# DR provider configuration
provider "aws" {
  alias  = "dr"
  region = var.dr_region
}

# Outputs
output "backup_bucket_name" {
  description = "Name of the primary backup S3 bucket"
  value       = aws_s3_bucket.backup_primary.bucket
}

output "dr_bucket_name" {
  description = "Name of the DR backup S3 bucket"
  value       = aws_s3_bucket.backup_dr.bucket
}

output "backup_kms_key_id" {
  description = "KMS key ID for backup encryption"
  value       = aws_kms_key.backup_encryption.key_id
}

output "backup_kms_key_arn" {
  description = "KMS key ARN for backup encryption"
  value       = aws_kms_key.backup_encryption.arn
}

output "dr_kms_key_id" {
  description = "DR KMS key ID for backup encryption"
  value       = aws_kms_key.backup_encryption_dr.key_id
}

output "sns_topic_arn" {
  description = "SNS topic ARN for backup notifications"
  value       = aws_sns_topic.backup_notifications.arn
}

output "lambda_function_name" {
  description = "Name of the backup orchestrator Lambda function"
  value       = aws_lambda_function.backup_orchestrator.function_name
}

output "backup_user_access_key_id" {
  description = "Access key ID for backup user"
  value       = aws_iam_access_key.backup_user.id
  sensitive   = true
}

output "backup_credentials_secret_arn" {
  description = "ARN of the secret containing backup credentials"
  value       = aws_secretsmanager_secret.backup_credentials.arn
}