# Backup Configuration File
# E-commerce Analytics SaaS Platform
# 
# This file contains configuration settings for automated backup scripts
# Source this file in backup scripts to override defaults

# ============================
# General Configuration
# ============================

# Backup type: full, incremental
BACKUP_TYPE="incremental"

# Environment identifier
ENVIRONMENT="production"

# Project name
PROJECT_NAME="ecommerce-analytics"

# ============================
# AWS Configuration
# ============================

# Primary AWS region
AWS_REGION="us-east-1"

# Disaster recovery region
DR_REGION="us-west-2"

# S3 bucket for backups (will be created by Terraform)
S3_BUCKET="ecommerce-analytics-backups-${RANDOM_SUFFIX}"

# S3 bucket for DR backups
DR_S3_BUCKET="ecommerce-analytics-dr-backups-${RANDOM_SUFFIX}"

# KMS key for encryption (will be created by Terraform)
ENCRYPTION_KEY_ID="alias/ecommerce-analytics-backup-encryption"

# DR KMS key
DR_ENCRYPTION_KEY_ID="alias/ecommerce-analytics-dr-backup-encryption"

# ============================
# Database Configuration
# ============================

# PostgreSQL primary database
DB_HOST="analytics-db.cluster-xyz.us-east-1.rds.amazonaws.com"
DB_PORT="5432"
DB_NAME="ecommerce_analytics"
DB_USER="backup_user"

# PostgreSQL DR database
DR_DB_HOST="analytics-db-dr.cluster-abc.us-west-2.rds.amazonaws.com"
DR_DB_IDENTIFIER="analytics-db-dr"

# Redis primary cluster
REDIS_HOST="analytics-redis.abc123.cache.amazonaws.com"
REDIS_PORT="6379"

# Redis DR cluster
DR_REDIS_HOST="analytics-redis-dr.def456.cache.amazonaws.com"
DR_REDIS_CLUSTER="analytics-redis-dr"

# ============================
# Kubernetes Configuration
# ============================

# Primary EKS cluster
EKS_CLUSTER_NAME="ecommerce-analytics"
KUBE_CONTEXT="arn:aws:eks:us-east-1:123456789012:cluster/ecommerce-analytics"

# DR EKS cluster
DR_EKS_CLUSTER_NAME="ecommerce-analytics-dr"
DR_KUBE_CONTEXT="arn:aws:eks:us-west-2:123456789012:cluster/ecommerce-analytics-dr"

# ============================
# Backup Retention
# ============================

# Local backup retention (days)
LOCAL_RETENTION_DAYS="7"

# S3 backup retention (days)
RETENTION_DAYS="30"

# Long-term archive retention (days)
ARCHIVE_RETENTION_DAYS="2555"  # 7 years

# Log retention (days)
LOG_RETENTION_DAYS="365"

# Configuration backup retention (indefinite)
CONFIG_RETENTION_DAYS="0"  # 0 = indefinite

# ============================
# Performance Configuration
# ============================

# Number of parallel backup jobs
PARALLEL_JOBS="4"

# Backup compression level (1-9)
COMPRESSION_LEVEL="9"

# Network timeout for operations (seconds)
NETWORK_TIMEOUT="300"

# Large file threshold for multipart upload (MB)
MULTIPART_THRESHOLD="100"

# Backup verification enabled
VERIFY_BACKUPS="true"

# ============================
# Notification Configuration
# ============================

# SNS topic for backup notifications
SNS_TOPIC="arn:aws:sns:us-east-1:123456789012:backup-notifications"

# Slack webhook URL (optional)
SLACK_WEBHOOK=""

# PagerDuty integration key (optional)
PAGERDUTY_KEY=""

# Email notifications (optional)
NOTIFICATION_EMAIL=""

# ============================
# Monitoring and Alerting
# ============================

# CloudWatch log group
LOG_GROUP="/aws/backup/ecommerce-analytics"

# CloudWatch log stream
LOG_STREAM="backup-operations"

# Backup failure alert threshold
FAILURE_THRESHOLD="1"

# Backup duration alert threshold (minutes)
DURATION_THRESHOLD="60"

# Storage usage alert threshold (GB)
STORAGE_THRESHOLD="1000"

# ============================
# Security Configuration
# ============================

# Enable backup encryption
ENCRYPT_BACKUPS="true"

# Enable access logging
ACCESS_LOGGING="true"

# Enable integrity verification
INTEGRITY_CHECK="true"

# Backup file permissions
BACKUP_FILE_PERMISSIONS="600"

# Temp directory permissions
TEMP_DIR_PERMISSIONS="700"

# ============================
# Application-Specific Paths
# ============================

# Application configuration directories
APP_CONFIG_DIRS=(
    "/etc/analytics"
    "/etc/dashboard"
    "/etc/billing"
    "/etc/integration"
)

# Application log directories
APP_LOG_DIRS=(
    "/var/log/analytics"
    "/var/log/dashboard"
    "/var/log/billing"
    "/var/log/integration"
)

# Application data directories
APP_DATA_DIRS=(
    "/var/lib/analytics"
    "/var/lib/dashboard"
    "/opt/uploads"
)

# Kubernetes namespace for applications
APP_NAMESPACE="production"

# ============================
# Disaster Recovery Configuration
# ============================

# Recovery Time Objective (minutes)
RTO_MINUTES="60"

# Recovery Point Objective (minutes)
RPO_MINUTES="15"

# DNS configuration for failover
ROUTE53_HOSTED_ZONE="Z123456789"
PRIMARY_DOMAIN="api.company.com"
DR_DOMAIN="dr-api.company.com"

# Health check endpoints
HEALTH_CHECK_ENDPOINTS=(
    "https://api.company.com/health"
    "https://api.company.com/api/analytics/health"
    "https://api.company.com/api/billing/health"
    "https://api.company.com/api/integration/health"
)

# ============================
# Backup Schedule Configuration
# ============================

# Full backup schedule (cron format)
FULL_BACKUP_SCHEDULE="0 2 * * 0"  # Sunday 2 AM

# Incremental backup schedule (cron format) 
INCREMENTAL_BACKUP_SCHEDULE="0 2 * * 1-6"  # Monday-Saturday 2 AM

# Transaction log backup interval (minutes)
WAL_BACKUP_INTERVAL="15"

# Configuration backup trigger
CONFIG_BACKUP_TRIGGER="change"  # on change or daily

# ============================
# Test Configuration
# ============================

# Enable test mode (uses test resources)
TEST_MODE="false"

# Test database identifier
TEST_DB_IDENTIFIER="analytics-db-test"

# Test S3 bucket prefix
TEST_S3_PREFIX="test-backups"

# Test retention (shorter for test data)
TEST_RETENTION_DAYS="7"

# ============================
# Compliance Configuration
# ============================

# Enable GDPR compliance features
GDPR_COMPLIANCE="true"

# Enable SOX compliance features
SOX_COMPLIANCE="true"

# Enable PCI DSS compliance features
PCI_COMPLIANCE="true"

# Audit logging enabled
AUDIT_LOGGING="true"

# Data residency requirements
DATA_RESIDENCY="US"

# ============================
# Cost Optimization
# ============================

# Enable intelligent tiering
INTELLIGENT_TIERING="true"

# Compress old backups
COMPRESS_OLD_BACKUPS="true"

# Deduplicate backups
ENABLE_DEDUPLICATION="true"

# Storage class for new backups
DEFAULT_STORAGE_CLASS="STANDARD"

# Storage class for archived backups
ARCHIVE_STORAGE_CLASS="GLACIER"

# ============================
# Advanced Configuration
# ============================

# Backup chunk size for large files (MB)
BACKUP_CHUNK_SIZE="64"

# Maximum concurrent uploads
MAX_CONCURRENT_UPLOADS="4"

# Retry attempts for failed operations
RETRY_ATTEMPTS="3"

# Retry delay (seconds)
RETRY_DELAY="30"

# Backup timeout (seconds)
BACKUP_TIMEOUT="3600"

# Enable debug logging
DEBUG_LOGGING="false"

# Temporary directory for backup operations
TEMP_DIR="/tmp/backup"

# Lock file location
LOCK_FILE="/var/run/backup.lock"

# PID file location
PID_FILE="/var/run/backup.pid"

# ============================
# Service-Specific Configuration
# ============================

# Analytics service backup includes
ANALYTICS_BACKUP_INCLUDES=(
    "user_events"
    "analytics_data"
    "processed_metrics"
    "custom_dimensions"
)

# Billing service backup includes
BILLING_BACKUP_INCLUDES=(
    "subscriptions"
    "invoices"
    "payments"
    "usage_records"
)

# Integration service backup includes
INTEGRATION_BACKUP_INCLUDES=(
    "integration_configs"
    "sync_history"
    "webhook_logs"
    "transformation_rules"
)

# Dashboard service backup includes
DASHBOARD_BACKUP_INCLUDES=(
    "dashboards"
    "widgets"
    "user_preferences"
    "saved_queries"
)

# ============================
# Environment Overrides
# ============================

# Load environment-specific overrides
if [[ -f "${CONFIG_DIR}/backup-${ENVIRONMENT}.conf" ]]; then
    source "${CONFIG_DIR}/backup-${ENVIRONMENT}.conf"
fi

# Load local overrides (not in version control)
if [[ -f "${CONFIG_DIR}/backup-local.conf" ]]; then
    source "${CONFIG_DIR}/backup-local.conf"
fi

# ============================
# Validation
# ============================

# Validate required variables are set
required_vars=(
    "AWS_REGION"
    "S3_BUCKET"
    "DB_HOST"
    "REDIS_HOST"
    "ENCRYPTION_KEY_ID"
)

for var in "${required_vars[@]}"; do
    if [[ -z "${!var}" ]]; then
        echo "ERROR: Required variable $var is not set" >&2
        exit 1
    fi
done

# Export all configuration variables
set -a
# Variables are now exported
set +a