apiVersion: v1
kind: Secret
metadata:
  name: app-secrets
  namespace: production
type: Opaque
data:
  # Database credentials (base64 encoded)
  DB_PASSWORD: <base64-encoded-db-password>
  
  # Redis credentials
  REDIS_PASSWORD: <base64-encoded-redis-password>
  
  # JWT secrets
  JWT_SECRET: <base64-encoded-jwt-secret>
  JWT_REFRESH_SECRET: <base64-encoded-jwt-refresh-secret>
  
  # Encryption keys
  ENCRYPTION_KEY: <base64-encoded-encryption-key>
  API_ENCRYPTION_KEY: <base64-encoded-api-encryption-key>
  
  # External service credentials
  STRIPE_SECRET_KEY: <base64-encoded-stripe-secret>
  STRIPE_WEBHOOK_SECRET: <base64-encoded-stripe-webhook-secret>
  
  # Email service
  SENDGRID_API_KEY: <base64-encoded-sendgrid-key>
  
  # Slack integration
  SLACK_WEBHOOK_URL: <base64-encoded-slack-webhook>
  SLACK_BOT_TOKEN: <base64-encoded-slack-bot-token>
  
  # OAuth secrets
  SHOPIFY_CLIENT_SECRET: <base64-encoded-shopify-secret>
  WOOCOMMERCE_CLIENT_SECRET: <base64-encoded-woocommerce-secret>
  
  # Monitoring
  DATADOG_API_KEY: <base64-encoded-datadog-key>
  NEW_RELIC_LICENSE_KEY: <base64-encoded-newrelic-key>
  
  # Session secrets
  SESSION_SECRET: <base64-encoded-session-secret>
  COOKIE_SECRET: <base64-encoded-cookie-secret>
---
apiVersion: v1
kind: Secret
metadata:
  name: tls-secret
  namespace: production
type: kubernetes.io/tls
data:
  tls.crt: <base64-encoded-certificate>
  tls.key: <base64-encoded-private-key>
---
apiVersion: v1
kind: Secret
metadata:
  name: docker-registry-secret
  namespace: production
type: kubernetes.io/dockerconfigjson
data:
  .dockerconfigjson: <base64-encoded-docker-config>
---
apiVersion: v1
kind: Secret
metadata:
  name: backup-credentials
  namespace: production
type: Opaque
data:
  AWS_ACCESS_KEY_ID: <base64-encoded-backup-access-key>
  AWS_SECRET_ACCESS_KEY: <base64-encoded-backup-secret-key>
  S3_BACKUP_BUCKET: <base64-encoded-backup-bucket-name>
---
# External Secrets Operator configuration for AWS Secrets Manager
apiVersion: external-secrets.io/v1beta1
kind: SecretStore
metadata:
  name: aws-secrets-manager
  namespace: production
spec:
  provider:
    aws:
      service: SecretsManager
      region: us-east-1
      auth:
        jwt:
          serviceAccountRef:
            name: external-secrets-sa
---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: app-secrets-external
  namespace: production
spec:
  refreshInterval: 15s
  secretStoreRef:
    name: aws-secrets-manager
    kind: SecretStore
  target:
    name: app-secrets-managed
    creationPolicy: Owner
  data:
  - secretKey: DB_PASSWORD
    remoteRef:
      key: ecommerce-analytics/database
      property: password
  - secretKey: REDIS_PASSWORD
    remoteRef:
      key: ecommerce-analytics/redis
      property: auth_token
  - secretKey: JWT_SECRET
    remoteRef:
      key: ecommerce-analytics/auth
      property: jwt_secret
  - secretKey: ENCRYPTION_KEY
    remoteRef:
      key: ecommerce-analytics/encryption
      property: main_key
  - secretKey: STRIPE_SECRET_KEY
    remoteRef:
      key: ecommerce-analytics/stripe
      property: secret_key
  - secretKey: SENDGRID_API_KEY
    remoteRef:
      key: ecommerce-analytics/sendgrid
      property: api_key