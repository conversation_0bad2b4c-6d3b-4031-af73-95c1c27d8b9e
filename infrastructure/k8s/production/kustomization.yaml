apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

metadata:
  name: ecommerce-analytics-production

namespace: production

resources:
  - namespace.yaml
  - configmap.yaml
  - secrets.yaml
  - services/
  - ingress.yaml
  - monitoring/
  - autoscaling/

images:
  - name: dashboard-service
    newTag: latest
  - name: analytics-service
    newTag: latest
  - name: integration-service
    newTag: latest
  - name: error-tracking-service
    newTag: latest
  - name: admin-service
    newTag: latest

replicas:
  - name: dashboard-deployment
    count: 3
  - name: analytics-deployment
    count: 5
  - name: integration-deployment
    count: 3
  - name: error-tracking-deployment
    count: 2
  - name: admin-deployment
    count: 2

patchesStrategicMerge:
  - patches/production-resources.yaml
  - patches/production-env.yaml
  - patches/security-context.yaml

commonLabels:
  app.kubernetes.io/environment: production
  app.kubernetes.io/managed-by: kustomize

commonAnnotations:
  deployment.kubernetes.io/revision: "1"