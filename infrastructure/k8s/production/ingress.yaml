apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: api-ingress
  namespace: production
  annotations:
    # AWS Load Balancer Controller
    kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS": 443}]'
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:us-east-1:ACCOUNT:certificate/CERT-ID
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    
    # Security
    alb.ingress.kubernetes.io/security-groups: sg-api-ingress
    alb.ingress.kubernetes.io/wafv2-acl-arn: arn:aws:wafv2:us-east-1:ACCOUNT:regional/webacl/api-protection/WEB-ACL-ID
    
    # Health checks
    alb.ingress.kubernetes.io/healthcheck-path: /health
    alb.ingress.kubernetes.io/healthcheck-interval-seconds: '30'
    alb.ingress.kubernetes.io/healthcheck-timeout-seconds: '5'
    alb.ingress.kubernetes.io/healthy-threshold-count: '2'
    alb.ingress.kubernetes.io/unhealthy-threshold-count: '3'
    
    # Load balancing
    alb.ingress.kubernetes.io/load-balancer-attributes: |
      routing.http2.enabled=true,
      idle_timeout.timeout_seconds=60,
      deletion_protection.enabled=true
    
    # Logging
    alb.ingress.kubernetes.io/load-balancer-attributes: |
      access_logs.s3.enabled=true,
      access_logs.s3.bucket=ecommerce-analytics-production-alb-logs,
      access_logs.s3.prefix=api-ingress
    
    # Rate limiting and security headers
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
    nginx.ingress.kubernetes.io/configuration-snippet: |
      add_header X-Frame-Options DENY;
      add_header X-Content-Type-Options nosniff;
      add_header X-XSS-Protection "1; mode=block";
      add_header Strict-Transport-Security "max-age=********; includeSubDomains";
      add_header Referrer-Policy "strict-origin-when-cross-origin";
      add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self'; connect-src 'self' wss: https:; frame-ancestors 'none';";
spec:
  rules:
  - host: api.yourdomain.com
    http:
      paths:
      - path: /api/dashboard
        pathType: Prefix
        backend:
          service:
            name: dashboard-service
            port:
              number: 3001
      - path: /api/analytics
        pathType: Prefix
        backend:
          service:
            name: analytics-service
            port:
              number: 3002
      - path: /api/integration
        pathType: Prefix
        backend:
          service:
            name: integration-service
            port:
              number: 3003
      - path: /api/error-tracking
        pathType: Prefix
        backend:
          service:
            name: error-tracking-service
            port:
              number: 3004
      - path: /api/admin
        pathType: Prefix
        backend:
          service:
            name: admin-service
            port:
              number: 3005
      - path: /ws
        pathType: Prefix
        backend:
          service:
            name: analytics-service
            port:
              number: 3002
      - path: /health
        pathType: Prefix
        backend:
          service:
            name: nginx-service
            port:
              number: 80
  tls:
  - hosts:
    - api.yourdomain.com
    secretName: tls-secret
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: frontend-ingress
  namespace: production
  annotations:
    kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS": 443}]'
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:us-east-1:ACCOUNT:certificate/CERT-ID
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    
    # Frontend specific configurations
    alb.ingress.kubernetes.io/actions.frontend-redirect: |
      {
        "type": "redirect",
        "redirectConfig": {
          "protocol": "HTTPS",
          "port": "443",
          "statusCode": "HTTP_301"
        }
      }
    
    # CloudFront integration
    alb.ingress.kubernetes.io/load-balancer-attributes: |
      routing.http.xff_client_port.enabled=true
spec:
  rules:
  - host: app.yourdomain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: frontend-service
            port:
              number: 80
  tls:
  - hosts:
    - app.yourdomain.com
    secretName: frontend-tls-secret
---
# Network Policy for additional security
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: api-network-policy
  namespace: production
spec:
  podSelector:
    matchLabels:
      tier: api
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    - podSelector:
        matchLabels:
          app: nginx-ingress
    ports:
    - protocol: TCP
      port: 3001
    - protocol: TCP
      port: 3002
    - protocol: TCP
      port: 3003
    - protocol: TCP
      port: 3004
    - protocol: TCP
      port: 3005
  egress:
  - to: []
    ports:
    - protocol: TCP
      port: 5432  # PostgreSQL
    - protocol: TCP
      port: 6379  # Redis
    - protocol: TCP
      port: 443   # HTTPS
    - protocol: TCP
      port: 53    # DNS
    - protocol: UDP
      port: 53    # DNS