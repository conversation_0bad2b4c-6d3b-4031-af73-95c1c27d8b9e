apiVersion: v1
kind: ConfigMap
metadata:
  name: app-config
  namespace: production
data:
  # Environment Configuration
  NODE_ENV: "production"
  LOG_LEVEL: "info"
  
  # Service Ports
  DASHBOARD_PORT: "3001"
  ANALYTICS_PORT: "3002"
  INTEGRATION_PORT: "3003"
  ERROR_TRACKING_PORT: "3004"
  ADMIN_PORT: "3005"
  
  # Service URLs
  DASHBOARD_URL: "http://dashboard-service:3001"
  ANALYTICS_URL: "http://analytics-service:3002"
  INTEGRATION_URL: "http://integration-service:3003"
  ERROR_TRACKING_URL: "http://error-tracking-service:3004"
  ADMIN_URL: "http://admin-service:3005"
  
  # Database Configuration
  DB_HOST: "ecommerce-analytics-production-database.cluster-xyz.us-east-1.rds.amazonaws.com"
  DB_PORT: "5432"
  DB_NAME: "ecommerce_analytics"
  DB_USER: "postgres"
  DB_SSL: "require"
  DB_POOL_MIN: "5"
  DB_POOL_MAX: "20"
  
  # Redis Configuration
  REDIS_HOST: "ecommerce-analytics-production-redis.xyz.cache.amazonaws.com"
  REDIS_PORT: "6379"
  REDIS_DB: "0"
  REDIS_CLUSTER_MODE: "true"
  
  # CORS Configuration
  CORS_ORIGINS: "https://app.yourdomain.com,https://admin.yourdomain.com"
  CORS_CREDENTIALS: "true"
  
  # Rate Limiting
  RATE_LIMIT_WINDOW_MS: "900000"
  RATE_LIMIT_MAX_REQUESTS: "1000"
  
  # Analytics Configuration
  ANALYTICS_BATCH_SIZE: "1000"
  ANALYTICS_FLUSH_INTERVAL: "30000"
  DATA_RETENTION_DAYS: "365"
  
  # Integration Configuration
  WEBHOOK_TIMEOUT: "30000"
  WEBHOOK_RETRY_ATTEMPTS: "3"
  SYNC_BATCH_SIZE: "500"
  
  # Monitoring Configuration
  METRICS_PORT: "9090"
  HEALTH_CHECK_INTERVAL: "30000"
  
  # Feature Flags
  ENABLE_REAL_TIME_ANALYTICS: "true"
  ENABLE_ADVANCED_ATTRIBUTION: "true"
  ENABLE_CUSTOM_REPORTS: "true"
  ENABLE_WEBHOOK_RETRIES: "true"
  
  # Performance Configuration
  MAX_QUERY_TIMEOUT: "30000"
  CACHE_TTL: "300"
  SESSION_TIMEOUT: "3600"
  
  # Security Configuration
  BCRYPT_ROUNDS: "12"
  JWT_EXPIRES_IN: "1h"
  REFRESH_TOKEN_EXPIRES_IN: "7d"
  
  # Notification Configuration
  EMAIL_FROM: "<EMAIL>"
  SUPPORT_EMAIL: "<EMAIL>"
  
  # External Services
  STRIPE_WEBHOOK_SECRET: "whsec_production_key"
  SENDGRID_FROM_EMAIL: "<EMAIL>"
  
  # Timezone
  TZ: "UTC"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-config
  namespace: production
data:
  default.conf: |
    upstream backend {
        server dashboard-service:3001;
        server analytics-service:3002;
        server integration-service:3003;
        server error-tracking-service:3004;
        server admin-service:3005;
    }
    
    server {
        listen 80;
        server_name api.yourdomain.com;
        
        # Security headers
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
        
        # Rate limiting
        limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
        limit_req zone=api burst=20 nodelay;
        
        # API routes
        location /api/dashboard {
            proxy_pass http://dashboard-service:3001;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        location /api/analytics {
            proxy_pass http://analytics-service:3002;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        location /api/integration {
            proxy_pass http://integration-service:3003;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        location /api/error-tracking {
            proxy_pass http://error-tracking-service:3004;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        location /api/admin {
            proxy_pass http://admin-service:3005;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # WebSocket support for analytics
        location /ws {
            proxy_pass http://analytics-service:3002;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # Health check
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
    }