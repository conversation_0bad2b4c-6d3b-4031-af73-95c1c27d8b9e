# Edge Computing Node Deployment for Global Analytics Platform
# Multi-region edge infrastructure with intelligent routing and local processing

apiVersion: v1
kind: Namespace
metadata:
  name: edge-computing
  labels:
    name: edge-computing

---
# ConfigMap for Edge Node Configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: edge-node-config
  namespace: edge-computing
data:
  edge-config.yaml: |
    edge:
      region: "${EDGE_REGION}"
      node_id: "${EDGE_NODE_ID}"
      datacenter: "${EDGE_DATACENTER}"
      
    processing:
      max_concurrent_requests: 1000
      request_timeout: 30s
      batch_size: 100
      processing_interval: 5s
      
    storage:
      local_cache_size: "10Gi"
      retention_hours: 24
      compression_enabled: true
      encryption_enabled: true
      
    networking:
      cdn_origin: "https://api.analytics-platform.com"
      edge_port: 8080
      health_check_port: 8081
      metrics_port: 9090
      
    analytics:
      enable_local_processing: true
      enable_real_time_aggregation: true
      enable_edge_ml_inference: true
      cache_hit_rate_target: 0.85
      
    security:
      jwt_validation: true
      rate_limiting_enabled: true
      ddos_protection: true
      geo_blocking_enabled: false

---
# Edge Node Deployment Template
apiVersion: apps/v1
kind: Deployment
metadata:
  name: edge-node
  namespace: edge-computing
  labels:
    app: edge-node
    tier: edge
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 1
  selector:
    matchLabels:
      app: edge-node
  template:
    metadata:
      labels:
        app: edge-node
        tier: edge
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9090"
        prometheus.io/path: "/metrics"
    spec:
      nodeSelector:
        edge-zone: "true"
      tolerations:
      - key: edge-node
        operator: Equal
        value: "true"
        effect: NoSchedule
      containers:
      - name: edge-processor
        image: analytics-platform/edge-processor:latest
        ports:
        - containerPort: 8080
          name: http
        - containerPort: 8081
          name: health
        - containerPort: 9090
          name: metrics
        env:
        - name: EDGE_REGION
          valueFrom:
            fieldRef:
              fieldPath: metadata.labels['topology.kubernetes.io/region']
        - name: EDGE_NODE_ID
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: EDGE_DATACENTER
          valueFrom:
            fieldRef:
              fieldPath: metadata.labels['topology.kubernetes.io/zone']
        - name: REDIS_URL
          value: "redis://redis-edge:6379/0"
        - name: KAFKA_BROKERS
          value: "kafka-edge:9092"
        - name: CENTRAL_API_URL
          value: "https://api.analytics-platform.com"
        - name: LOG_LEVEL
          value: "INFO"
        
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        
        livenessProbe:
          httpGet:
            path: /health
            port: 8081
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        
        readinessProbe:
          httpGet:
            path: /ready
            port: 8081
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 2
        
        volumeMounts:
        - name: config
          mountPath: /app/config
          readOnly: true
        - name: cache-storage
          mountPath: /app/cache
        - name: logs
          mountPath: /app/logs
      
      # Redis sidecar for local caching
      - name: redis-cache
        image: redis:7-alpine
        ports:
        - containerPort: 6379
        command:
        - redis-server
        - --maxmemory
        - 1gb
        - --maxmemory-policy
        - allkeys-lru
        - --appendonly
        - "yes"
        resources:
          requests:
            memory: "512Mi"
            cpu: "100m"
          limits:
            memory: "1Gi"
            cpu: "200m"
        volumeMounts:
        - name: redis-data
          mountPath: /data
      
      volumes:
      - name: config
        configMap:
          name: edge-node-config
      - name: cache-storage
        emptyDir:
          sizeLimit: 10Gi
      - name: redis-data
        emptyDir:
          sizeLimit: 2Gi
      - name: logs
        emptyDir: {}

---
# Edge Node Service
apiVersion: v1
kind: Service
metadata:
  name: edge-node
  namespace: edge-computing
  labels:
    app: edge-node
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "9090"
spec:
  type: ClusterIP
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
    name: http
  - port: 8081
    targetPort: 8081
    protocol: TCP
    name: health
  - port: 9090
    targetPort: 9090
    protocol: TCP
    name: metrics
  selector:
    app: edge-node

---
# Edge Load Balancer Service
apiVersion: v1
kind: Service
metadata:
  name: edge-lb
  namespace: edge-computing
  labels:
    app: edge-node
    type: load-balancer
spec:
  type: LoadBalancer
  ports:
  - port: 80
    targetPort: 8080
    protocol: TCP
    name: http
  - port: 443
    targetPort: 8080
    protocol: TCP
    name: https
  selector:
    app: edge-node
  loadBalancerSourceRanges:
  - 0.0.0.0/0

---
# Horizontal Pod Autoscaler for Edge Nodes
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: edge-node-hpa
  namespace: edge-computing
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: edge-node
  minReplicas: 3
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  - type: Pods
    pods:
      metric:
        name: http_requests_per_second
      target:
        type: AverageValue
        averageValue: "500"
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 60
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60

---
# Pod Disruption Budget
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: edge-node-pdb
  namespace: edge-computing
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: edge-node

---
# Edge Ingress Controller
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: edge-ingress
  namespace: edge-computing
  annotations:
    kubernetes.io/ingress.class: "nginx"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/rate-limit: "1000"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/upstream-hash-by: "$request_uri"
    nginx.ingress.kubernetes.io/server-snippet: |
      location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
      }
spec:
  tls:
  - hosts:
    - edge.analytics-platform.com
    - "*.edge.analytics-platform.com"
    secretName: edge-tls-secret
  rules:
  - host: us-east-1.edge.analytics-platform.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: edge-node
            port:
              number: 8080
  - host: us-west-2.edge.analytics-platform.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: edge-node
            port:
              number: 8080
  - host: eu-west-1.edge.analytics-platform.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: edge-node
            port:
              number: 8080
  - host: ap-southeast-1.edge.analytics-platform.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: edge-node
            port:
              number: 8080

---
# Network Policy for Edge Security
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: edge-network-policy
  namespace: edge-computing
spec:
  podSelector:
    matchLabels:
      app: edge-node
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    ports:
    - protocol: TCP
      port: 8080
  - from: []
    ports:
    - protocol: TCP
      port: 8081  # Health checks
    - protocol: TCP
      port: 9090  # Metrics
  egress:
  - to: []
    ports:
    - protocol: TCP
      port: 53   # DNS
    - protocol: UDP
      port: 53   # DNS
    - protocol: TCP
      port: 443  # HTTPS to central API
    - protocol: TCP
      port: 6379 # Redis
    - protocol: TCP
      port: 9092 # Kafka

---
# ServiceMonitor for Prometheus
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: edge-node-metrics
  namespace: edge-computing
  labels:
    app: edge-node
spec:
  selector:
    matchLabels:
      app: edge-node
  endpoints:
  - port: metrics
    interval: 30s
    path: /metrics

---
# PrometheusRule for Edge Alerting
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: edge-node-alerts
  namespace: edge-computing
spec:
  groups:
  - name: edge-node
    rules:
    - alert: EdgeNodeDown
      expr: up{job="edge-node"} == 0
      for: 1m
      labels:
        severity: critical
      annotations:
        summary: "Edge node is down"
        description: "Edge node {{ $labels.instance }} has been down for more than 1 minute"
    
    - alert: EdgeNodeHighCPU
      expr: rate(cpu_usage_total[5m]) > 0.8
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: "Edge node high CPU usage"
        description: "Edge node {{ $labels.instance }} CPU usage is above 80%"
    
    - alert: EdgeNodeHighMemory
      expr: memory_usage_ratio > 0.9
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: "Edge node high memory usage"
        description: "Edge node {{ $labels.instance }} memory usage is above 90%"
    
    - alert: EdgeCacheHitRateLow
      expr: cache_hit_rate < 0.7
      for: 10m
      labels:
        severity: warning
      annotations:
        summary: "Edge cache hit rate is low"
        description: "Edge node {{ $labels.instance }} cache hit rate is {{ $value }} (below 70%)"
    
    - alert: EdgeLatencyHigh
      expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 1.0
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: "Edge node high latency"
        description: "Edge node {{ $labels.instance }} 95th percentile latency is {{ $value }}s"

---
# Edge Node DaemonSet for Node-Level Operations
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: edge-node-agent
  namespace: edge-computing
  labels:
    app: edge-node-agent
spec:
  selector:
    matchLabels:
      app: edge-node-agent
  template:
    metadata:
      labels:
        app: edge-node-agent
    spec:
      hostNetwork: true
      hostPID: true
      containers:
      - name: node-agent
        image: analytics-platform/edge-node-agent:latest
        securityContext:
          privileged: true
        env:
        - name: NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        - name: NODE_IP
          valueFrom:
            fieldRef:
              fieldPath: status.hostIP
        volumeMounts:
        - name: proc
          mountPath: /host/proc
          readOnly: true
        - name: sys
          mountPath: /host/sys
          readOnly: true
        - name: dev
          mountPath: /host/dev
          readOnly: true
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "128Mi"
            cpu: "100m"
      volumes:
      - name: proc
        hostPath:
          path: /proc
      - name: sys
        hostPath:
          path: /sys
      - name: dev
        hostPath:
          path: /dev
      tolerations:
      - operator: Exists
        effect: NoSchedule

---
# Edge Computing Custom Resource Definition
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: edgenodes.edge.analytics-platform.com
spec:
  group: edge.analytics-platform.com
  versions:
  - name: v1
    served: true
    storage: true
    schema:
      openAPIV3Schema:
        type: object
        properties:
          spec:
            type: object
            properties:
              region:
                type: string
              datacenter:
                type: string
              capacity:
                type: object
                properties:
                  cpu:
                    type: string
                  memory:
                    type: string
                  storage:
                    type: string
              features:
                type: array
                items:
                  type: string
          status:
            type: object
            properties:
              conditions:
                type: array
                items:
                  type: object
                  properties:
                    type:
                      type: string
                    status:
                      type: string
                    lastTransitionTime:
                      type: string
  scope: Namespaced
  names:
    plural: edgenodes
    singular: edgenode
    kind: EdgeNode

---
# Sample Edge Node Custom Resource
apiVersion: edge.analytics-platform.com/v1
kind: EdgeNode
metadata:
  name: us-east-1a
  namespace: edge-computing
spec:
  region: us-east-1
  datacenter: us-east-1a
  capacity:
    cpu: "16"
    memory: "32Gi"
    storage: "1Ti"
  features:
  - "real-time-analytics"
  - "ml-inference"
  - "cdn-caching"
  - "geo-routing"