#!/usr/bin/env python3
"""
Edge Computing Processor for Global Analytics Platform
High-performance edge node with local processing, caching, and intelligent routing
"""

import asyncio
import logging
import json
import time
import uuid
import hashlib
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any, Set, Union
from dataclasses import dataclass, field
from enum import Enum
import os
import geoip2.database
import geoip2.errors

# FastAPI and HTTP
from fastapi import FastAPI, Request, Response, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
import uvicorn
import httpx

# Caching and storage
import aioredis
import aiocache
from aiocache import cached, Cache
from aiocache.serializers import PickleSerializer

# Message queues
from aiokafka import AIOKafkaProducer, AIOKafkaConsumer

# Database
import asyncpg
import sqlite3
import aiosqlite

# Data processing
import numpy as np
import pandas as pd
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import IsolationForest

# Monitoring and metrics
import psutil
import prometheus_client
from prometheus_client import Counter, Histogram, Gauge, start_http_server

# Compression and serialization
import gzip
import lz4.frame
import orjson

# Configuration
@dataclass
class EdgeProcessorConfig:
    # Node identification
    region: str = os.getenv('EDGE_REGION', 'us-east-1')
    node_id: str = os.getenv('EDGE_NODE_ID', f'edge-{uuid.uuid4().hex[:8]}')
    datacenter: str = os.getenv('EDGE_DATACENTER', 'us-east-1a')
    
    # Service settings
    host: str = "0.0.0.0"
    port: int = 8080
    health_port: int = 8081
    metrics_port: int = 9090
    
    # Central API
    central_api_url: str = os.getenv('CENTRAL_API_URL', 'https://api.analytics-platform.com')
    api_timeout: int = 30
    
    # Redis configuration
    redis_url: str = os.getenv('REDIS_URL', 'redis://localhost:6379/0')
    cache_ttl: int = 3600  # 1 hour
    
    # Kafka configuration
    kafka_brokers: str = os.getenv('KAFKA_BROKERS', 'localhost:9092')
    kafka_topic_prefix: str = 'edge'
    
    # Processing settings
    max_concurrent_requests: int = 1000
    request_timeout: int = 30
    batch_size: int = 100
    processing_interval: int = 5
    
    # Storage settings
    local_cache_size: str = "10Gi"
    retention_hours: int = 24
    compression_enabled: bool = True
    encryption_enabled: bool = True
    
    # Analytics settings
    enable_local_processing: bool = True
    enable_real_time_aggregation: bool = True
    enable_edge_ml_inference: bool = True
    cache_hit_rate_target: float = 0.85
    
    # Security
    jwt_validation: bool = True
    rate_limiting_enabled: bool = True
    ddos_protection: bool = True
    
    # Performance
    max_memory_usage: float = 0.8  # 80% of available memory
    max_cpu_usage: float = 0.8     # 80% of available CPU

# Edge processing modes
class ProcessingMode(Enum):
    CACHE_ONLY = "cache_only"
    PROCESS_AND_CACHE = "process_and_cache"
    FORWARD_TO_CENTRAL = "forward_to_central"
    HYBRID = "hybrid"

class DataType(Enum):
    EVENT = "event"
    QUERY = "query"
    BATCH = "batch"
    STREAM = "stream"

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Metrics
request_count = Counter('edge_requests_total', 'Total requests processed', ['method', 'endpoint', 'status'])
request_duration = Histogram('edge_request_duration_seconds', 'Request duration', ['endpoint'])
cache_operations = Counter('edge_cache_operations_total', 'Cache operations', ['operation', 'result'])
cache_hit_rate = Gauge('edge_cache_hit_rate', 'Cache hit rate')
processing_queue_size = Gauge('edge_processing_queue_size', 'Processing queue size')
active_connections = Gauge('edge_active_connections', 'Active connections')
memory_usage = Gauge('edge_memory_usage_bytes', 'Memory usage in bytes')
cpu_usage = Gauge('edge_cpu_usage_percent', 'CPU usage percentage')
network_bytes_sent = Counter('edge_network_bytes_sent_total', 'Network bytes sent')
network_bytes_received = Counter('edge_network_bytes_received_total', 'Network bytes received')

class GeolocationService:
    """Geolocation service for intelligent routing"""
    
    def __init__(self):
        self.db_path = "/app/data/GeoLite2-City.mmdb"
        self.reader = None
        
    async def initialize(self):
        """Initialize geolocation database"""
        try:
            if os.path.exists(self.db_path):
                self.reader = geoip2.database.Reader(self.db_path)
                logger.info("Geolocation database initialized")
            else:
                logger.warning("Geolocation database not found, using fallback")
        except Exception as e:
            logger.error(f"Error initializing geolocation: {str(e)}")
    
    def get_location(self, ip_address: str) -> Dict[str, Any]:
        """Get location information for IP address"""
        try:
            if self.reader:
                response = self.reader.city(ip_address)
                return {
                    "country": response.country.name,
                    "country_code": response.country.iso_code,
                    "city": response.city.name,
                    "latitude": float(response.location.latitude) if response.location.latitude else None,
                    "longitude": float(response.location.longitude) if response.location.longitude else None,
                    "timezone": str(response.location.time_zone) if response.location.time_zone else None
                }
        except (geoip2.errors.AddressNotFoundError, Exception) as e:
            logger.debug(f"Geolocation lookup failed for {ip_address}: {str(e)}")
        
        return {
            "country": "Unknown",
            "country_code": "XX",
            "city": "Unknown",
            "latitude": None,
            "longitude": None,
            "timezone": None
        }
    
    def close(self):
        """Close geolocation database"""
        if self.reader:
            self.reader.close()

class EdgeCache:
    """Advanced caching system with intelligent eviction"""
    
    def __init__(self, config: EdgeProcessorConfig):
        self.config = config
        self.redis = None
        self.local_cache = {}
        self.cache_stats = {
            "hits": 0,
            "misses": 0,
            "sets": 0,
            "deletes": 0
        }
        
    async def initialize(self):
        """Initialize cache systems"""
        try:
            self.redis = aioredis.from_url(self.config.redis_url)
            await self.redis.ping()
            logger.info("Redis cache initialized")
        except Exception as e:
            logger.error(f"Error initializing Redis cache: {str(e)}")
    
    async def get(self, key: str, use_local: bool = True) -> Optional[Any]:
        """Get value from cache with fallback logic"""
        try:
            # Try local cache first
            if use_local and key in self.local_cache:
                item = self.local_cache[key]
                if not self._is_expired(item):
                    self.cache_stats["hits"] += 1
                    cache_operations.labels(operation="get", result="hit").inc()
                    return item["value"]
                else:
                    del self.local_cache[key]
            
            # Try Redis cache
            if self.redis:
                cached_data = await self.redis.get(key)
                if cached_data:
                    value = orjson.loads(cached_data)
                    
                    # Store in local cache for faster access
                    if use_local:
                        self._set_local(key, value)
                    
                    self.cache_stats["hits"] += 1
                    cache_operations.labels(operation="get", result="hit").inc()
                    return value
            
            self.cache_stats["misses"] += 1
            cache_operations.labels(operation="get", result="miss").inc()
            return None
            
        except Exception as e:
            logger.error(f"Cache get error: {str(e)}")
            return None
    
    async def set(self, key: str, value: Any, ttl: int = None, use_local: bool = True) -> bool:
        """Set value in cache"""
        try:
            ttl = ttl or self.config.cache_ttl
            
            # Set in Redis
            if self.redis:
                serialized = orjson.dumps(value)
                await self.redis.setex(key, ttl, serialized)
            
            # Set in local cache
            if use_local:
                self._set_local(key, value, ttl)
            
            self.cache_stats["sets"] += 1
            cache_operations.labels(operation="set", result="success").inc()
            return True
            
        except Exception as e:
            logger.error(f"Cache set error: {str(e)}")
            cache_operations.labels(operation="set", result="error").inc()
            return False
    
    async def delete(self, key: str) -> bool:
        """Delete value from cache"""
        try:
            # Delete from Redis
            if self.redis:
                await self.redis.delete(key)
            
            # Delete from local cache
            if key in self.local_cache:
                del self.local_cache[key]
            
            self.cache_stats["deletes"] += 1
            cache_operations.labels(operation="delete", result="success").inc()
            return True
            
        except Exception as e:
            logger.error(f"Cache delete error: {str(e)}")
            cache_operations.labels(operation="delete", result="error").inc()
            return False
    
    def _set_local(self, key: str, value: Any, ttl: int = None):
        """Set value in local cache"""
        ttl = ttl or self.config.cache_ttl
        self.local_cache[key] = {
            "value": value,
            "expires_at": time.time() + ttl
        }
        
        # Evict old entries if cache is too large
        self._evict_if_needed()
    
    def _is_expired(self, item: Dict[str, Any]) -> bool:
        """Check if cache item is expired"""
        return time.time() > item["expires_at"]
    
    def _evict_if_needed(self):
        """Evict old entries from local cache"""
        max_size = 10000  # Maximum number of items in local cache
        
        if len(self.local_cache) > max_size:
            # Remove 10% of oldest entries
            items_to_remove = sorted(
                self.local_cache.items(),
                key=lambda x: x[1]["expires_at"]
            )[:int(max_size * 0.1)]
            
            for key, _ in items_to_remove:
                del self.local_cache[key]
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        total_requests = self.cache_stats["hits"] + self.cache_stats["misses"]
        hit_rate = self.cache_stats["hits"] / total_requests if total_requests > 0 else 0
        
        cache_hit_rate.set(hit_rate)
        
        return {
            **self.cache_stats,
            "hit_rate": hit_rate,
            "local_cache_size": len(self.local_cache)
        }
    
    async def close(self):
        """Close cache connections"""
        if self.redis:
            await self.redis.close()

class LocalDataProcessor:
    """Local data processing capabilities at the edge"""
    
    def __init__(self, config: EdgeProcessorConfig):
        self.config = config
        self.anomaly_detector = None
        self.scaler = StandardScaler()
        
    async def initialize(self):
        """Initialize local processing capabilities"""
        try:
            # Initialize anomaly detection model
            self.anomaly_detector = IsolationForest(
                contamination=0.1,
                random_state=42,
                n_estimators=100
            )
            
            logger.info("Local data processor initialized")
        except Exception as e:
            logger.error(f"Error initializing local processor: {str(e)}")
    
    async def process_analytics_event(self, event: Dict[str, Any]) -> Dict[str, Any]:
        """Process analytics event locally"""
        try:
            # Add edge processing metadata
            processed_event = {
                **event,
                "edge_processed": True,
                "edge_node_id": self.config.node_id,
                "edge_region": self.config.region,
                "processed_at": datetime.now(timezone.utc).isoformat()
            }
            
            # Perform local enrichment
            if self.config.enable_local_processing:
                processed_event = await self._enrich_event(processed_event)
            
            # Real-time aggregation
            if self.config.enable_real_time_aggregation:
                await self._update_aggregations(processed_event)
            
            # ML inference
            if self.config.enable_edge_ml_inference:
                processed_event = await self._ml_inference(processed_event)
            
            return processed_event
            
        except Exception as e:
            logger.error(f"Error processing event: {str(e)}")
            return event
    
    async def _enrich_event(self, event: Dict[str, Any]) -> Dict[str, Any]:
        """Enrich event with additional data"""
        try:
            # Add timestamp if missing
            if "timestamp" not in event:
                event["timestamp"] = datetime.now(timezone.utc).isoformat()
            
            # Add session information
            if "session_id" not in event and "user_id" in event:
                event["session_id"] = f"session_{event['user_id']}_{int(time.time() // 3600)}"
            
            # Add device fingerprinting
            if "user_agent" in event:
                event["device_info"] = self._parse_user_agent(event["user_agent"])
            
            return event
            
        except Exception as e:
            logger.error(f"Error enriching event: {str(e)}")
            return event
    
    async def _update_aggregations(self, event: Dict[str, Any]):
        """Update real-time aggregations"""
        try:
            # Update counters and metrics based on event type
            event_type = event.get("type", "unknown")
            
            # Example aggregations
            aggregation_key = f"agg:{event_type}:{datetime.now().strftime('%Y%m%d%H')}"
            
            # This would be stored in cache for real-time dashboards
            # Implementation would depend on specific analytics requirements
            
        except Exception as e:
            logger.error(f"Error updating aggregations: {str(e)}")
    
    async def _ml_inference(self, event: Dict[str, Any]) -> Dict[str, Any]:
        """Perform ML inference on event"""
        try:
            if not self.anomaly_detector:
                return event
            
            # Extract numerical features for anomaly detection
            features = self._extract_features(event)
            
            if features:
                # Reshape for single prediction
                features_array = np.array(features).reshape(1, -1)
                
                # Normalize features
                features_normalized = self.scaler.fit_transform(features_array)
                
                # Predict anomaly
                is_anomaly = self.anomaly_detector.fit_predict(features_normalized)[0] == -1
                anomaly_score = self.anomaly_detector.score_samples(features_normalized)[0]
                
                event["anomaly_detection"] = {
                    "is_anomaly": bool(is_anomaly),
                    "anomaly_score": float(anomaly_score),
                    "processed_by": "edge_ml"
                }
            
            return event
            
        except Exception as e:
            logger.error(f"Error in ML inference: {str(e)}")
            return event
    
    def _extract_features(self, event: Dict[str, Any]) -> List[float]:
        """Extract numerical features from event"""
        features = []
        
        try:
            # Extract common numerical features
            if "value" in event and isinstance(event["value"], (int, float)):
                features.append(float(event["value"]))
            
            if "duration" in event and isinstance(event["duration"], (int, float)):
                features.append(float(event["duration"]))
            
            if "count" in event and isinstance(event["count"], (int, float)):
                features.append(float(event["count"]))
            
            # Add timestamp features
            if "timestamp" in event:
                try:
                    ts = datetime.fromisoformat(event["timestamp"].replace('Z', '+00:00'))
                    features.extend([
                        float(ts.hour),
                        float(ts.weekday()),
                        float(ts.day)
                    ])
                except:
                    pass
            
            return features if len(features) >= 3 else None
            
        except Exception as e:
            logger.error(f"Error extracting features: {str(e)}")
            return None
    
    def _parse_user_agent(self, user_agent: str) -> Dict[str, Any]:
        """Parse user agent string"""
        # Simplified user agent parsing
        device_info = {
            "browser": "Unknown",
            "os": "Unknown",
            "device_type": "Unknown"
        }
        
        try:
            ua_lower = user_agent.lower()
            
            # Browser detection
            if "chrome" in ua_lower:
                device_info["browser"] = "Chrome"
            elif "firefox" in ua_lower:
                device_info["browser"] = "Firefox"
            elif "safari" in ua_lower:
                device_info["browser"] = "Safari"
            elif "edge" in ua_lower:
                device_info["browser"] = "Edge"
            
            # OS detection
            if "windows" in ua_lower:
                device_info["os"] = "Windows"
            elif "mac" in ua_lower:
                device_info["os"] = "macOS"
            elif "linux" in ua_lower:
                device_info["os"] = "Linux"
            elif "android" in ua_lower:
                device_info["os"] = "Android"
            elif "ios" in ua_lower:
                device_info["os"] = "iOS"
            
            # Device type detection
            if "mobile" in ua_lower or "android" in ua_lower or "iphone" in ua_lower:
                device_info["device_type"] = "Mobile"
            elif "tablet" in ua_lower or "ipad" in ua_lower:
                device_info["device_type"] = "Tablet"
            else:
                device_info["device_type"] = "Desktop"
            
        except Exception as e:
            logger.error(f"Error parsing user agent: {str(e)}")
        
        return device_info

class EdgeProcessor:
    """Main edge processor service"""
    
    def __init__(self, config: EdgeProcessorConfig):
        self.config = config
        self.app = FastAPI(title="Edge Analytics Processor")
        self.cache = EdgeCache(config)
        self.geolocation = GeolocationService()
        self.local_processor = LocalDataProcessor(config)
        self.kafka_producer = None
        self.http_client = None
        self.processing_queue = asyncio.Queue(maxsize=config.max_concurrent_requests)
        
        # Add middleware
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        self.app.add_middleware(GZipMiddleware, minimum_size=1000)
        
        self._setup_routes()
        self._start_background_tasks()
    
    async def initialize(self):
        """Initialize edge processor"""
        try:
            await self.cache.initialize()
            await self.geolocation.initialize()
            await self.local_processor.initialize()
            
            # Initialize HTTP client
            self.http_client = httpx.AsyncClient(
                timeout=self.config.api_timeout,
                limits=httpx.Limits(max_connections=100, max_keepalive_connections=20)
            )
            
            # Initialize Kafka producer
            self.kafka_producer = AIOKafkaProducer(
                bootstrap_servers=self.config.kafka_brokers,
                value_serializer=lambda v: orjson.dumps(v)
            )
            await self.kafka_producer.start()
            
            logger.info(f"Edge processor initialized for region {self.config.region}")
            
        except Exception as e:
            logger.error(f"Error initializing edge processor: {str(e)}")
            raise
    
    def _setup_routes(self):
        """Setup API routes"""
        
        @self.app.middleware("http")
        async def track_requests(request: Request, call_next):
            start_time = time.time()
            active_connections.inc()
            
            # Track request metrics
            client_ip = request.client.host
            user_agent = request.headers.get("user-agent", "")
            
            # Process request
            response = await call_next(request)
            
            # Record metrics
            duration = time.time() - start_time
            request_count.labels(
                method=request.method,
                endpoint=request.url.path,
                status=response.status_code
            ).inc()
            request_duration.labels(endpoint=request.url.path).observe(duration)
            active_connections.dec()
            
            # Add edge headers
            response.headers["X-Edge-Node"] = self.config.node_id
            response.headers["X-Edge-Region"] = self.config.region
            response.headers["X-Cache-Status"] = getattr(response, "cache_status", "miss")
            
            return response
        
        @self.app.post("/analytics/events")
        async def process_analytics_events(request: Request, background_tasks: BackgroundTasks):
            """Process analytics events"""
            try:
                # Get request data
                events = await request.json()
                if not isinstance(events, list):
                    events = [events]
                
                # Get client location
                client_ip = request.client.host
                location = self.geolocation.get_location(client_ip)
                
                processed_events = []
                
                for event in events:
                    # Add client information
                    event["client_ip"] = client_ip
                    event["location"] = location
                    
                    # Process event locally
                    processed_event = await self.local_processor.process_analytics_event(event)
                    processed_events.append(processed_event)
                    
                    # Queue for background processing
                    await self.processing_queue.put({
                        "type": "event",
                        "data": processed_event
                    })
                
                return {
                    "status": "success",
                    "processed_count": len(processed_events),
                    "edge_node": self.config.node_id
                }
                
            except Exception as e:
                logger.error(f"Error processing analytics events: {str(e)}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/analytics/query")
        async def handle_analytics_query(request: Request):
            """Handle analytics queries with caching"""
            try:
                # Build cache key from query parameters
                query_params = dict(request.query_params)
                cache_key = f"query:{hashlib.md5(orjson.dumps(query_params, sort_keys=True)).hexdigest()}"
                
                # Try cache first
                cached_result = await self.cache.get(cache_key)
                if cached_result:
                    response = Response(
                        content=orjson.dumps(cached_result),
                        media_type="application/json"
                    )
                    response.cache_status = "hit"
                    return response
                
                # Forward to central API
                central_url = f"{self.config.central_api_url}/analytics/query"
                
                async with self.http_client.get(central_url, params=query_params) as response:
                    if response.status_code == 200:
                        result = response.json()
                        
                        # Cache the result
                        await self.cache.set(cache_key, result, ttl=self.config.cache_ttl)
                        
                        api_response = Response(
                            content=orjson.dumps(result),
                            media_type="application/json"
                        )
                        api_response.cache_status = "miss"
                        return api_response
                    else:
                        raise HTTPException(status_code=response.status_code, detail="Central API error")
                
            except Exception as e:
                logger.error(f"Error handling analytics query: {str(e)}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/health")
        async def health_check():
            """Health check endpoint"""
            return {
                "status": "healthy",
                "node_id": self.config.node_id,
                "region": self.config.region,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "cache_stats": self.cache.get_stats(),
                "queue_size": self.processing_queue.qsize(),
                "memory_usage": psutil.virtual_memory().percent,
                "cpu_usage": psutil.cpu_percent()
            }
        
        @self.app.get("/ready")
        async def readiness_check():
            """Readiness check endpoint"""
            try:
                # Check if all services are ready
                if self.cache.redis:
                    await self.cache.redis.ping()
                
                return {"status": "ready"}
                
            except Exception as e:
                raise HTTPException(status_code=503, detail=f"Not ready: {str(e)}")
        
        @self.app.get("/metrics")
        async def get_metrics():
            """Prometheus metrics endpoint"""
            # Update system metrics
            memory_usage.set(psutil.virtual_memory().used)
            cpu_usage.set(psutil.cpu_percent())
            processing_queue_size.set(self.processing_queue.qsize())
            
            from prometheus_client import generate_latest, CONTENT_TYPE_LATEST
            return Response(generate_latest(), media_type=CONTENT_TYPE_LATEST)
    
    def _start_background_tasks(self):
        """Start background processing tasks"""
        
        @self.app.on_event("startup")
        async def startup_event():
            # Start metrics server
            start_http_server(self.config.metrics_port)
            
            # Start background processing
            asyncio.create_task(self._process_queue())
            asyncio.create_task(self._sync_with_central())
            asyncio.create_task(self._cleanup_cache())
            
            await self.initialize()
        
        @self.app.on_event("shutdown")
        async def shutdown_event():
            await self.close()
    
    async def _process_queue(self):
        """Process queued items in background"""
        while True:
            try:
                # Process items in batches
                batch = []
                for _ in range(self.config.batch_size):
                    try:
                        item = await asyncio.wait_for(
                            self.processing_queue.get(),
                            timeout=1.0
                        )
                        batch.append(item)
                    except asyncio.TimeoutError:
                        break
                
                if batch:
                    await self._process_batch(batch)
                
                await asyncio.sleep(self.config.processing_interval)
                
            except Exception as e:
                logger.error(f"Error in background processing: {str(e)}")
                await asyncio.sleep(5)
    
    async def _process_batch(self, batch: List[Dict[str, Any]]):
        """Process a batch of items"""
        try:
            # Send to Kafka
            if self.kafka_producer:
                for item in batch:
                    topic = f"{self.config.kafka_topic_prefix}.{item['type']}"
                    await self.kafka_producer.send(topic, value=item['data'])
            
            # Update network metrics
            network_bytes_sent.inc(len(orjson.dumps(batch)))
            
        except Exception as e:
            logger.error(f"Error processing batch: {str(e)}")
    
    async def _sync_with_central(self):
        """Sync with central API periodically"""
        while True:
            try:
                # Send node status to central API
                status = {
                    "node_id": self.config.node_id,
                    "region": self.config.region,
                    "datacenter": self.config.datacenter,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "metrics": {
                        "cache_hit_rate": self.cache.get_stats()["hit_rate"],
                        "queue_size": self.processing_queue.qsize(),
                        "memory_usage": psutil.virtual_memory().percent,
                        "cpu_usage": psutil.cpu_percent(),
                        "active_connections": active_connections._value._value
                    }
                }
                
                if self.http_client:
                    try:
                        await self.http_client.post(
                            f"{self.config.central_api_url}/edge/nodes/{self.config.node_id}/status",
                            json=status,
                            timeout=10
                        )
                    except Exception as e:
                        logger.debug(f"Failed to sync with central API: {str(e)}")
                
                await asyncio.sleep(60)  # Sync every minute
                
            except Exception as e:
                logger.error(f"Error in sync with central: {str(e)}")
                await asyncio.sleep(60)
    
    async def _cleanup_cache(self):
        """Cleanup expired cache entries"""
        while True:
            try:
                # Clean up local cache
                current_time = time.time()
                expired_keys = [
                    key for key, item in self.cache.local_cache.items()
                    if current_time > item["expires_at"]
                ]
                
                for key in expired_keys:
                    del self.cache.local_cache[key]
                
                if expired_keys:
                    logger.debug(f"Cleaned up {len(expired_keys)} expired cache entries")
                
                await asyncio.sleep(300)  # Cleanup every 5 minutes
                
            except Exception as e:
                logger.error(f"Error in cache cleanup: {str(e)}")
                await asyncio.sleep(300)
    
    async def close(self):
        """Close edge processor"""
        try:
            if self.kafka_producer:
                await self.kafka_producer.stop()
            if self.http_client:
                await self.http_client.aclose()
            await self.cache.close()
            self.geolocation.close()
            
            logger.info("Edge processor closed")
            
        except Exception as e:
            logger.error(f"Error closing edge processor: {str(e)}")

async def main():
    """Run the edge processor"""
    config = EdgeProcessorConfig()
    processor = EdgeProcessor(config)
    
    try:
        # Run FastAPI server
        await uvicorn.run(
            processor.app,
            host=config.host,
            port=config.port,
            log_level="info",
            access_log=False  # We handle our own request logging
        )
        
    except Exception as e:
        logger.error(f"Error running edge processor: {str(e)}")
        raise
    finally:
        await processor.close()

if __name__ == "__main__":
    asyncio.run(main())