#!/usr/bin/env python3
"""
Kafka Topics Setup and Management for E-commerce Analytics
Creates and manages all required Kafka topics with proper partitioning and replication
"""

import logging
import os
import json
import time
from typing import Dict, List, Optional
from dataclasses import dataclass
from kafka.admin import KafkaAdminClient, ConfigResource, ConfigResourceType
from kafka.admin.config_resource import ConfigResource
from kafka.admin.new_topic import NewTopic
from kafka.errors import TopicAlreadyExistsError, KafkaError
from kafka import KafkaProducer, KafkaConsumer
import asyncio

# Configuration
@dataclass
class KafkaConfig:
    bootstrap_servers: str = os.getenv('KAFKA_BOOTSTRAP_SERVERS', 'kafka:9092')
    replication_factor: int = 3
    min_in_sync_replicas: int = 2
    default_partitions: int = 6
    retention_ms: int = 604800000  # 7 days in milliseconds
    segment_ms: int = 86400000     # 1 day in milliseconds
    compression_type: str = 'lz4'
    
    # Topic-specific configurations
    topics_config: Dict[str, Dict] = None
    
    def __post_init__(self):
        if self.topics_config is None:
            self.topics_config = {
                # User interaction events
                'user-interactions': {
                    'partitions': 12,
                    'replication_factor': 3,
                    'config': {
                        'retention.ms': str(self.retention_ms * 4),  # Keep longer for ML training
                        'compression.type': self.compression_type,
                        'segment.ms': str(self.segment_ms),
                        'min.insync.replicas': str(self.min_in_sync_replicas)
                    }
                },
                
                # E-commerce transactions
                'transactions': {
                    'partitions': 12,
                    'replication_factor': 3,
                    'config': {
                        'retention.ms': str(self.retention_ms * 12),  # Keep transactions longer
                        'compression.type': self.compression_type,
                        'segment.ms': str(self.segment_ms),
                        'min.insync.replicas': str(self.min_in_sync_replicas),
                        'cleanup.policy': 'compact,delete'
                    }
                },
                
                # Product catalog events
                'product-events': {
                    'partitions': 6,
                    'replication_factor': 3,
                    'config': {
                        'retention.ms': str(self.retention_ms * 8),
                        'compression.type': self.compression_type,
                        'segment.ms': str(self.segment_ms),
                        'min.insync.replicas': str(self.min_in_sync_replicas)
                    }
                },
                
                # Customer data changes
                'customer-events': {
                    'partitions': 8,
                    'replication_factor': 3,
                    'config': {
                        'retention.ms': str(self.retention_ms * 16),  # Keep customer data longer
                        'compression.type': self.compression_type,
                        'segment.ms': str(self.segment_ms),
                        'min.insync.replicas': str(self.min_in_sync_replicas),
                        'cleanup.policy': 'compact'  # Keep latest customer state
                    }
                },
                
                # ML model events and predictions
                'ml-predictions': {
                    'partitions': 8,
                    'replication_factor': 3,
                    'config': {
                        'retention.ms': str(self.retention_ms * 2),
                        'compression.type': self.compression_type,
                        'segment.ms': str(self.segment_ms),
                        'min.insync.replicas': str(self.min_in_sync_replicas)
                    }
                },
                
                # Recommendation events
                'recommendations': {
                    'partitions': 6,
                    'replication_factor': 3,
                    'config': {
                        'retention.ms': str(self.retention_ms),
                        'compression.type': self.compression_type,
                        'segment.ms': str(self.segment_ms),
                        'min.insync.replicas': str(self.min_in_sync_replicas)
                    }
                },
                
                # Fraud detection alerts
                'fraud-alerts': {
                    'partitions': 4,
                    'replication_factor': 3,
                    'config': {
                        'retention.ms': str(self.retention_ms * 24),  # Keep fraud alerts longer
                        'compression.type': self.compression_type,
                        'segment.ms': str(self.segment_ms),
                        'min.insync.replicas': str(self.min_in_sync_replicas)
                    }
                },
                
                # System monitoring and metrics
                'system-metrics': {
                    'partitions': 4,
                    'replication_factor': 3,
                    'config': {
                        'retention.ms': str(self.retention_ms * 2),
                        'compression.type': self.compression_type,
                        'segment.ms': str(self.segment_ms // 2),  # Smaller segments for metrics
                        'min.insync.replicas': str(self.min_in_sync_replicas)
                    }
                },
                
                # Analytics queries and responses
                'analytics-queries': {
                    'partitions': 6,
                    'replication_factor': 3,
                    'config': {
                        'retention.ms': str(self.retention_ms),
                        'compression.type': self.compression_type,
                        'segment.ms': str(self.segment_ms),
                        'min.insync.replicas': str(self.min_in_sync_replicas)
                    }
                },
                
                # Real-time notifications
                'notifications': {
                    'partitions': 4,
                    'replication_factor': 3,
                    'config': {
                        'retention.ms': str(self.retention_ms // 2),  # Shorter retention for notifications
                        'compression.type': self.compression_type,
                        'segment.ms': str(self.segment_ms // 4),
                        'min.insync.replicas': str(self.min_in_sync_replicas)
                    }
                },
                
                # Event sourcing - order events
                'order-events': {
                    'partitions': 8,
                    'replication_factor': 3,
                    'config': {
                        'retention.ms': str(self.retention_ms * 52),  # Keep order events for 1 year
                        'compression.type': self.compression_type,
                        'segment.ms': str(self.segment_ms),
                        'min.insync.replicas': str(self.min_in_sync_replicas),
                        'cleanup.policy': 'compact'
                    }
                },
                
                # Event sourcing - payment events
                'payment-events': {
                    'partitions': 6,
                    'replication_factor': 3,
                    'config': {
                        'retention.ms': str(self.retention_ms * 52),  # Keep payment events for 1 year
                        'compression.type': self.compression_type,
                        'segment.ms': str(self.segment_ms),
                        'min.insync.replicas': str(self.min_in_sync_replicas),
                        'cleanup.policy': 'compact'
                    }
                },
                
                # Inventory events
                'inventory-events': {
                    'partitions': 4,
                    'replication_factor': 3,
                    'config': {
                        'retention.ms': str(self.retention_ms * 8),
                        'compression.type': self.compression_type,
                        'segment.ms': str(self.segment_ms),
                        'min.insync.replicas': str(self.min_in_sync_replicas),
                        'cleanup.policy': 'compact'
                    }
                },
                
                # Search and discovery events
                'search-events': {
                    'partitions': 6,
                    'replication_factor': 3,
                    'config': {
                        'retention.ms': str(self.retention_ms * 2),
                        'compression.type': self.compression_type,
                        'segment.ms': str(self.segment_ms),
                        'min.insync.replicas': str(self.min_in_sync_replicas)
                    }
                },
                
                # A/B testing events
                'ab-testing-events': {
                    'partitions': 4,
                    'replication_factor': 3,
                    'config': {
                        'retention.ms': str(self.retention_ms * 4),
                        'compression.type': self.compression_type,
                        'segment.ms': str(self.segment_ms),
                        'min.insync.replicas': str(self.min_in_sync_replicas)
                    }
                },
                
                # Dead letter queue for failed events
                'dead-letter-queue': {
                    'partitions': 2,
                    'replication_factor': 3,
                    'config': {
                        'retention.ms': str(self.retention_ms * 4),
                        'compression.type': self.compression_type,
                        'segment.ms': str(self.segment_ms),
                        'min.insync.replicas': str(self.min_in_sync_replicas)
                    }
                }
            }

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class KafkaTopicManager:
    """Manages Kafka topics creation, configuration, and monitoring"""
    
    def __init__(self, config: KafkaConfig):
        self.config = config
        self.admin_client = None
        self.producer = None
        
    def initialize(self):
        """Initialize Kafka admin client and producer"""
        try:
            self.admin_client = KafkaAdminClient(
                bootstrap_servers=self.config.bootstrap_servers,
                client_id='kafka-topic-manager'
            )
            
            self.producer = KafkaProducer(
                bootstrap_servers=self.config.bootstrap_servers,
                value_serializer=lambda v: json.dumps(v).encode('utf-8'),
                key_serializer=lambda k: str(k).encode('utf-8') if k else None,
                compression_type=self.config.compression_type,
                acks='all',
                retries=3,
                max_in_flight_requests_per_connection=1
            )
            
            logger.info("Kafka topic manager initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing Kafka topic manager: {str(e)}")
            raise
    
    def create_all_topics(self):
        """Create all required topics with their configurations"""
        try:
            topics_to_create = []
            
            for topic_name, topic_config in self.config.topics_config.items():
                new_topic = NewTopic(
                    name=topic_name,
                    num_partitions=topic_config['partitions'],
                    replication_factor=topic_config['replication_factor'],
                    topic_configs=topic_config['config']
                )
                topics_to_create.append(new_topic)
            
            # Create topics
            future_map = self.admin_client.create_topics(topics_to_create, validate_only=False)
            
            # Wait for completion and handle results
            for topic_name, future in future_map.items():
                try:
                    future.result()  # Block until topic is created
                    logger.info(f"Successfully created topic: {topic_name}")
                except TopicAlreadyExistsError:
                    logger.info(f"Topic already exists: {topic_name}")
                except Exception as e:
                    logger.error(f"Failed to create topic {topic_name}: {str(e)}")
            
            logger.info("Topic creation process completed")
            
        except Exception as e:
            logger.error(f"Error creating topics: {str(e)}")
            raise
    
    def list_topics(self) -> Dict[str, any]:
        """List all topics and their metadata"""
        try:
            metadata = self.admin_client.list_topics(timeout_s=30)
            topics_info = {}
            
            for topic in metadata.topics:
                topic_metadata = metadata.topics[topic]
                topics_info[topic] = {
                    'partitions': len(topic_metadata.partitions),
                    'partition_details': {}
                }
                
                for partition_id, partition_metadata in topic_metadata.partitions.items():
                    topics_info[topic]['partition_details'][partition_id] = {
                        'leader': partition_metadata.leader,
                        'replicas': partition_metadata.replicas,
                        'isr': partition_metadata.isr
                    }
            
            return topics_info
            
        except Exception as e:
            logger.error(f"Error listing topics: {str(e)}")
            return {}
    
    def get_topic_configuration(self, topic_name: str) -> Dict[str, str]:
        """Get configuration for a specific topic"""
        try:
            resource = ConfigResource(ConfigResourceType.TOPIC, topic_name)
            config_map = self.admin_client.describe_configs([resource])
            
            topic_config = {}
            for config_resource, config_response in config_map.items():
                for config_entry in config_response.configs.values():
                    topic_config[config_entry.name] = config_entry.value
            
            return topic_config
            
        except Exception as e:
            logger.error(f"Error getting topic configuration for {topic_name}: {str(e)}")
            return {}
    
    def update_topic_configuration(self, topic_name: str, configs: Dict[str, str]):
        """Update configuration for a specific topic"""
        try:
            resource = ConfigResource(ConfigResourceType.TOPIC, topic_name)
            config_map = {resource: configs}
            
            future_map = self.admin_client.alter_configs(config_map)
            
            for resource, future in future_map.items():
                try:
                    future.result()
                    logger.info(f"Successfully updated configuration for topic: {topic_name}")
                except Exception as e:
                    logger.error(f"Failed to update configuration for topic {topic_name}: {str(e)}")
                    
        except Exception as e:
            logger.error(f"Error updating topic configuration: {str(e)}")
    
    def delete_topic(self, topic_name: str):
        """Delete a specific topic (use with caution!)"""
        try:
            future_map = self.admin_client.delete_topics([topic_name])
            
            for topic, future in future_map.items():
                try:
                    future.result()
                    logger.info(f"Successfully deleted topic: {topic}")
                except Exception as e:
                    logger.error(f"Failed to delete topic {topic}: {str(e)}")
                    
        except Exception as e:
            logger.error(f"Error deleting topic: {str(e)}")
    
    def send_test_message(self, topic_name: str, test_data: Dict):
        """Send a test message to verify topic functionality"""
        try:
            future = self.producer.send(topic_name, value=test_data)
            record_metadata = future.get(timeout=10)
            
            logger.info(f"Test message sent to {topic_name}: partition={record_metadata.partition}, offset={record_metadata.offset}")
            return True
            
        except Exception as e:
            logger.error(f"Error sending test message to {topic_name}: {str(e)}")
            return False
    
    def validate_topics_health(self) -> Dict[str, bool]:
        """Validate that all topics are healthy and accessible"""
        try:
            health_status = {}
            topics_info = self.list_topics()
            
            for topic_name in self.config.topics_config.keys():
                if topic_name in topics_info:
                    # Check if topic has the expected number of partitions
                    expected_partitions = self.config.topics_config[topic_name]['partitions']
                    actual_partitions = topics_info[topic_name]['partitions']
                    
                    if actual_partitions == expected_partitions:
                        # Send test message to verify topic is writable
                        test_data = {
                            'event_type': 'health_check',
                            'timestamp': time.time(),
                            'source': 'kafka-topic-manager'
                        }
                        
                        health_status[topic_name] = self.send_test_message(topic_name, test_data)
                    else:
                        logger.warning(f"Topic {topic_name} has {actual_partitions} partitions, expected {expected_partitions}")
                        health_status[topic_name] = False
                else:
                    logger.error(f"Topic {topic_name} does not exist")
                    health_status[topic_name] = False
            
            return health_status
            
        except Exception as e:
            logger.error(f"Error validating topics health: {str(e)}")
            return {}
    
    def close(self):
        """Close Kafka connections"""
        try:
            if self.producer:
                self.producer.flush()
                self.producer.close()
            
            if self.admin_client:
                self.admin_client.close()
            
            logger.info("Kafka connections closed")
            
        except Exception as e:
            logger.error(f"Error closing Kafka connections: {str(e)}")

class EventSchemaManager:
    """Manages event schemas for Kafka topics"""
    
    def __init__(self, schema_registry_url: str = 'http://schema-registry:8081'):
        self.schema_registry_url = schema_registry_url
        self.schemas = self._define_event_schemas()
    
    def _define_event_schemas(self) -> Dict[str, Dict]:
        """Define Avro schemas for all event types"""
        return {
            'user-interaction-schema': {
                "type": "record",
                "name": "UserInteraction",
                "namespace": "com.analytics.events",
                "fields": [
                    {"name": "user_id", "type": "string"},
                    {"name": "session_id", "type": "string"},
                    {"name": "event_type", "type": "string"},
                    {"name": "timestamp", "type": "long"},
                    {"name": "page_url", "type": ["null", "string"], "default": None},
                    {"name": "user_agent", "type": ["null", "string"], "default": None},
                    {"name": "ip_address", "type": ["null", "string"], "default": None},
                    {"name": "properties", "type": ["null", {"type": "map", "values": "string"}], "default": None}
                ]
            },
            
            'transaction-schema': {
                "type": "record",
                "name": "Transaction",
                "namespace": "com.analytics.events",
                "fields": [
                    {"name": "transaction_id", "type": "string"},
                    {"name": "customer_id", "type": "string"},
                    {"name": "order_id", "type": "string"},
                    {"name": "product_id", "type": "string"},
                    {"name": "quantity", "type": "int"},
                    {"name": "unit_price", "type": "double"},
                    {"name": "total_amount", "type": "double"},
                    {"name": "currency", "type": "string"},
                    {"name": "payment_method", "type": "string"},
                    {"name": "timestamp", "type": "long"},
                    {"name": "metadata", "type": ["null", {"type": "map", "values": "string"}], "default": None}
                ]
            },
            
            'product-event-schema': {
                "type": "record",
                "name": "ProductEvent",
                "namespace": "com.analytics.events",
                "fields": [
                    {"name": "product_id", "type": "string"},
                    {"name": "event_type", "type": "string"},
                    {"name": "timestamp", "type": "long"},
                    {"name": "name", "type": ["null", "string"], "default": None},
                    {"name": "category", "type": ["null", "string"], "default": None},
                    {"name": "price", "type": ["null", "double"], "default": None},
                    {"name": "inventory_count", "type": ["null", "int"], "default": None},
                    {"name": "changes", "type": ["null", {"type": "map", "values": "string"}], "default": None}
                ]
            },
            
            'ml-prediction-schema': {
                "type": "record",
                "name": "MLPrediction",
                "namespace": "com.analytics.events",
                "fields": [
                    {"name": "prediction_id", "type": "string"},
                    {"name": "model_name", "type": "string"},
                    {"name": "model_version", "type": "string"},
                    {"name": "input_data", "type": {"type": "map", "values": "string"}},
                    {"name": "prediction", "type": "string"},
                    {"name": "confidence", "type": "double"},
                    {"name": "timestamp", "type": "long"},
                    {"name": "customer_id", "type": ["null", "string"], "default": None}
                ]
            }
        }
    
    def register_schemas(self):
        """Register all schemas with Schema Registry"""
        try:
            import requests
            
            for schema_name, schema_definition in self.schemas.items():
                subject = f"{schema_name}-value"
                
                payload = {
                    "schema": json.dumps(schema_definition)
                }
                
                response = requests.post(
                    f"{self.schema_registry_url}/subjects/{subject}/versions",
                    headers={'Content-Type': 'application/vnd.schemaregistry.v1+json'},
                    json=payload
                )
                
                if response.status_code in [200, 409]:  # 409 means already exists
                    logger.info(f"Schema registered successfully: {schema_name}")
                else:
                    logger.error(f"Failed to register schema {schema_name}: {response.text}")
                    
        except Exception as e:
            logger.error(f"Error registering schemas: {str(e)}")

def main():
    """Main function to set up Kafka topics and schemas"""
    config = KafkaConfig()
    
    # Initialize topic manager
    topic_manager = KafkaTopicManager(config)
    topic_manager.initialize()
    
    try:
        # Create all topics
        logger.info("Creating Kafka topics...")
        topic_manager.create_all_topics()
        
        # Wait a moment for topics to be fully created
        time.sleep(5)
        
        # List created topics
        logger.info("Listing created topics...")
        topics_info = topic_manager.list_topics()
        for topic_name, info in topics_info.items():
            if topic_name in config.topics_config:
                logger.info(f"Topic: {topic_name}, Partitions: {info['partitions']}")
        
        # Validate topics health
        logger.info("Validating topics health...")
        health_status = topic_manager.validate_topics_health()
        
        healthy_topics = sum(1 for status in health_status.values() if status)
        total_topics = len(health_status)
        
        logger.info(f"Topics health check: {healthy_topics}/{total_topics} topics are healthy")
        
        # Register event schemas
        logger.info("Registering event schemas...")
        schema_manager = EventSchemaManager()
        schema_manager.register_schemas()
        
        logger.info("Kafka topics setup completed successfully!")
        
        # Print summary
        print("\n" + "="*60)
        print("KAFKA TOPICS SETUP SUMMARY")
        print("="*60)
        print(f"Total topics created: {len(config.topics_config)}")
        print(f"Healthy topics: {healthy_topics}")
        print(f"Bootstrap servers: {config.bootstrap_servers}")
        print(f"Default replication factor: {config.replication_factor}")
        print(f"Default retention: {config.retention_ms // (1000 * 60 * 60 * 24)} days")
        print("="*60)
        
    except Exception as e:
        logger.error(f"Error in main setup process: {str(e)}")
        raise
    finally:
        topic_manager.close()

if __name__ == "__main__":
    main()