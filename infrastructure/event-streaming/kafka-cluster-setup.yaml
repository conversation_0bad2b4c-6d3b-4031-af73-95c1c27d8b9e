# Apache Kafka Cluster Setup for Event-Driven Architecture
# High-availability, production-ready Kafka deployment

apiVersion: v1
kind: Namespace
metadata:
  name: kafka-system
  labels:
    name: kafka-system

---
# Zookeeper StatefulSet for Kafka coordination
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: zookeeper
  namespace: kafka-system
spec:
  serviceName: zookeeper-headless
  replicas: 3
  selector:
    matchLabels:
      app: zookeeper
  template:
    metadata:
      labels:
        app: zookeeper
    spec:
      containers:
      - name: zookeeper
        image: confluentinc/cp-zookeeper:7.4.0
        ports:
        - containerPort: 2181
          name: client
        - containerPort: 2888
          name: peer
        - containerPort: 3888
          name: leader-election
        env:
        - name: ZOOKEEPER_CLIENT_PORT
          value: "2181"
        - name: ZOOKEEPER_TICK_TIME
          value: "2000"
        - name: ZOOKEEPER_INIT_LIMIT
          value: "5"
        - name: ZOOKEEPER_SYNC_LIMIT
          value: "2"
        - name: ZOOKEEPER_SERVERS
          value: "zookeeper-0.zookeeper-headless.kafka-system.svc.cluster.local:2888:3888;zookeeper-1.zookeeper-headless.kafka-system.svc.cluster.local:2888:3888;zookeeper-2.zookeeper-headless.kafka-system.svc.cluster.local:2888:3888"
        - name: ZOOKEEPER_SERVER_ID
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        volumeMounts:
        - name: zookeeper-data
          mountPath: /var/lib/zookeeper/data
        - name: zookeeper-logs
          mountPath: /var/lib/zookeeper/log
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          exec:
            command:
            - /bin/bash
            - -c
            - "echo ruok | nc localhost 2181 | grep imok"
          initialDelaySeconds: 30
          timeoutSeconds: 5
        readinessProbe:
          exec:
            command:
            - /bin/bash
            - -c
            - "echo ruok | nc localhost 2181 | grep imok"
          initialDelaySeconds: 10
          timeoutSeconds: 5
  volumeClaimTemplates:
  - metadata:
      name: zookeeper-data
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 10Gi
  - metadata:
      name: zookeeper-logs
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 5Gi

---
# Zookeeper Headless Service
apiVersion: v1
kind: Service
metadata:
  name: zookeeper-headless
  namespace: kafka-system
spec:
  clusterIP: None
  ports:
  - port: 2181
    name: client
  - port: 2888
    name: peer
  - port: 3888
    name: leader-election
  selector:
    app: zookeeper

---
# Zookeeper Service
apiVersion: v1
kind: Service
metadata:
  name: zookeeper
  namespace: kafka-system
spec:
  ports:
  - port: 2181
    name: client
  selector:
    app: zookeeper

---
# Kafka StatefulSet
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: kafka
  namespace: kafka-system
spec:
  serviceName: kafka-headless
  replicas: 3
  selector:
    matchLabels:
      app: kafka
  template:
    metadata:
      labels:
        app: kafka
    spec:
      containers:
      - name: kafka
        image: confluentinc/cp-kafka:7.4.0
        ports:
        - containerPort: 9092
          name: kafka-internal
        - containerPort: 9093
          name: kafka-external
        env:
        - name: KAFKA_BROKER_ID
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: KAFKA_ZOOKEEPER_CONNECT
          value: "zookeeper:2181"
        - name: KAFKA_LISTENERS
          value: "INTERNAL://0.0.0.0:9092,EXTERNAL://0.0.0.0:9093"
        - name: KAFKA_ADVERTISED_LISTENERS
          value: "INTERNAL://$(HOSTNAME).kafka-headless.kafka-system.svc.cluster.local:9092,EXTERNAL://$(HOSTNAME).kafka-external.kafka-system.svc.cluster.local:9093"
        - name: KAFKA_LISTENER_SECURITY_PROTOCOL_MAP
          value: "INTERNAL:PLAINTEXT,EXTERNAL:PLAINTEXT"
        - name: KAFKA_INTER_BROKER_LISTENER_NAME
          value: "INTERNAL"
        - name: KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR
          value: "3"
        - name: KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR
          value: "3"
        - name: KAFKA_TRANSACTION_STATE_LOG_MIN_ISR
          value: "2"
        - name: KAFKA_DEFAULT_REPLICATION_FACTOR
          value: "3"
        - name: KAFKA_MIN_IN_SYNC_REPLICAS
          value: "2"
        - name: KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS
          value: "3000"
        - name: KAFKA_LOG_RETENTION_HOURS
          value: "168"
        - name: KAFKA_LOG_RETENTION_BYTES
          value: "1073741824"
        - name: KAFKA_LOG_SEGMENT_BYTES
          value: "1073741824"
        - name: KAFKA_AUTO_CREATE_TOPICS_ENABLE
          value: "false"
        - name: KAFKA_NUM_PARTITIONS
          value: "3"
        - name: KAFKA_COMPRESSION_TYPE
          value: "lz4"
        - name: KAFKA_JMX_PORT
          value: "9999"
        - name: KAFKA_JMX_HOSTNAME
          value: "localhost"
        - name: KAFKA_HEAP_OPTS
          value: "-Xmx2G -Xms2G"
        volumeMounts:
        - name: kafka-data
          mountPath: /var/lib/kafka/data
        resources:
          requests:
            memory: "3Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
        livenessProbe:
          tcpSocket:
            port: 9092
          initialDelaySeconds: 30
          timeoutSeconds: 5
        readinessProbe:
          tcpSocket:
            port: 9092
          initialDelaySeconds: 10
          timeoutSeconds: 5
  volumeClaimTemplates:
  - metadata:
      name: kafka-data
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 50Gi

---
# Kafka Headless Service
apiVersion: v1
kind: Service
metadata:
  name: kafka-headless
  namespace: kafka-system
spec:
  clusterIP: None
  ports:
  - port: 9092
    name: kafka-internal
  selector:
    app: kafka

---
# Kafka Internal Service
apiVersion: v1
kind: Service
metadata:
  name: kafka
  namespace: kafka-system
spec:
  ports:
  - port: 9092
    name: kafka-internal
    targetPort: 9092
  selector:
    app: kafka

---
# Kafka External Service (LoadBalancer)
apiVersion: v1
kind: Service
metadata:
  name: kafka-external
  namespace: kafka-system
spec:
  type: LoadBalancer
  ports:
  - port: 9093
    name: kafka-external
    targetPort: 9093
  selector:
    app: kafka

---
# Kafka Connect Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: kafka-connect
  namespace: kafka-system
spec:
  replicas: 2
  selector:
    matchLabels:
      app: kafka-connect
  template:
    metadata:
      labels:
        app: kafka-connect
    spec:
      containers:
      - name: kafka-connect
        image: confluentinc/cp-kafka-connect:7.4.0
        ports:
        - containerPort: 8083
        env:
        - name: CONNECT_BOOTSTRAP_SERVERS
          value: "kafka:9092"
        - name: CONNECT_REST_ADVERTISED_HOST_NAME
          value: "kafka-connect"
        - name: CONNECT_REST_PORT
          value: "8083"
        - name: CONNECT_GROUP_ID
          value: "analytics-connect-cluster"
        - name: CONNECT_CONFIG_STORAGE_TOPIC
          value: "analytics-connect-configs"
        - name: CONNECT_OFFSET_STORAGE_TOPIC
          value: "analytics-connect-offsets"
        - name: CONNECT_STATUS_STORAGE_TOPIC
          value: "analytics-connect-status"
        - name: CONNECT_CONFIG_STORAGE_REPLICATION_FACTOR
          value: "3"
        - name: CONNECT_OFFSET_STORAGE_REPLICATION_FACTOR
          value: "3"
        - name: CONNECT_STATUS_STORAGE_REPLICATION_FACTOR
          value: "3"
        - name: CONNECT_KEY_CONVERTER
          value: "org.apache.kafka.connect.json.JsonConverter"
        - name: CONNECT_VALUE_CONVERTER
          value: "org.apache.kafka.connect.json.JsonConverter"
        - name: CONNECT_KEY_CONVERTER_SCHEMAS_ENABLE
          value: "false"
        - name: CONNECT_VALUE_CONVERTER_SCHEMAS_ENABLE
          value: "false"
        - name: CONNECT_INTERNAL_KEY_CONVERTER
          value: "org.apache.kafka.connect.json.JsonConverter"
        - name: CONNECT_INTERNAL_VALUE_CONVERTER
          value: "org.apache.kafka.connect.json.JsonConverter"
        - name: CONNECT_INTERNAL_KEY_CONVERTER_SCHEMAS_ENABLE
          value: "false"
        - name: CONNECT_INTERNAL_VALUE_CONVERTER_SCHEMAS_ENABLE
          value: "false"
        - name: CONNECT_PLUGIN_PATH
          value: "/usr/share/java,/usr/share/confluent-hub-components"
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /
            port: 8083
          initialDelaySeconds: 60
          timeoutSeconds: 5
        readinessProbe:
          httpGet:
            path: /
            port: 8083
          initialDelaySeconds: 30
          timeoutSeconds: 5

---
# Kafka Connect Service
apiVersion: v1
kind: Service
metadata:
  name: kafka-connect
  namespace: kafka-system
spec:
  ports:
  - port: 8083
    name: rest-api
  selector:
    app: kafka-connect

---
# Schema Registry Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: schema-registry
  namespace: kafka-system
spec:
  replicas: 2
  selector:
    matchLabels:
      app: schema-registry
  template:
    metadata:
      labels:
        app: schema-registry
    spec:
      containers:
      - name: schema-registry
        image: confluentinc/cp-schema-registry:7.4.0
        ports:
        - containerPort: 8081
        env:
        - name: SCHEMA_REGISTRY_HOST_NAME
          value: "schema-registry"
        - name: SCHEMA_REGISTRY_KAFKASTORE_BOOTSTRAP_SERVERS
          value: "kafka:9092"
        - name: SCHEMA_REGISTRY_LISTENERS
          value: "http://0.0.0.0:8081"
        - name: SCHEMA_REGISTRY_KAFKASTORE_TOPIC
          value: "_schemas"
        - name: SCHEMA_REGISTRY_KAFKASTORE_TOPIC_REPLICATION_FACTOR
          value: "3"
        - name: SCHEMA_REGISTRY_DEBUG
          value: "false"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /
            port: 8081
          initialDelaySeconds: 30
          timeoutSeconds: 5
        readinessProbe:
          httpGet:
            path: /
            port: 8081
          initialDelaySeconds: 10
          timeoutSeconds: 5

---
# Schema Registry Service
apiVersion: v1
kind: Service
metadata:
  name: schema-registry
  namespace: kafka-system
spec:
  ports:
  - port: 8081
    name: schema-registry
  selector:
    app: schema-registry

---
# Kafka UI Deployment (for monitoring and management)
apiVersion: apps/v1
kind: Deployment
metadata:
  name: kafka-ui
  namespace: kafka-system
spec:
  replicas: 1
  selector:
    matchLabels:
      app: kafka-ui
  template:
    metadata:
      labels:
        app: kafka-ui
    spec:
      containers:
      - name: kafka-ui
        image: provectuslabs/kafka-ui:latest
        ports:
        - containerPort: 8080
        env:
        - name: KAFKA_CLUSTERS_0_NAME
          value: "analytics-cluster"
        - name: KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS
          value: "kafka:9092"
        - name: KAFKA_CLUSTERS_0_ZOOKEEPER
          value: "zookeeper:2181"
        - name: KAFKA_CLUSTERS_0_SCHEMAREGISTRY
          value: "http://schema-registry:8081"
        - name: KAFKA_CLUSTERS_0_KAFKACONNECT_0_NAME
          value: "analytics-connect"
        - name: KAFKA_CLUSTERS_0_KAFKACONNECT_0_ADDRESS
          value: "http://kafka-connect:8083"
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "250m"

---
# Kafka UI Service
apiVersion: v1
kind: Service
metadata:
  name: kafka-ui
  namespace: kafka-system
spec:
  type: LoadBalancer
  ports:
  - port: 8080
    name: kafka-ui
  selector:
    app: kafka-ui

---
# RBAC for Kafka operations
apiVersion: v1
kind: ServiceAccount
metadata:
  name: kafka-operator
  namespace: kafka-system

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: kafka-operator
rules:
- apiGroups: [""]
  resources: ["pods", "services", "endpoints", "persistentvolumeclaims", "events", "configmaps", "secrets"]
  verbs: ["*"]
- apiGroups: ["apps"]
  resources: ["deployments", "daemonsets", "replicasets", "statefulsets"]
  verbs: ["*"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: kafka-operator
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: kafka-operator
subjects:
- kind: ServiceAccount
  name: kafka-operator
  namespace: kafka-system