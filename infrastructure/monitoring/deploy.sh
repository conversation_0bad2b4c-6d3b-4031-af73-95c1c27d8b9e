#!/bin/bash

# E-commerce Analytics SaaS - Monitoring Stack Deployment Script
# Deploys Prometheus, Grafana, and related monitoring components

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Configuration
NAMESPACE="monitoring"
ENVIRONMENT="${ENVIRONMENT:-production}"
HELM_TIMEOUT="600s"

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check if kubectl is installed and configured
    if ! command -v kubectl >/dev/null 2>&1; then
        error "kubectl is not installed"
        exit 1
    fi
    
    # Check if helm is installed
    if ! command -v helm >/dev/null 2>&1; then
        error "helm is not installed"
        exit 1
    fi
    
    # Check cluster connection
    if ! kubectl cluster-info >/dev/null 2>&1; then
        error "Cannot connect to Kubernetes cluster"
        exit 1
    fi
    
    info "Prerequisites check passed"
}

# Create namespace
create_namespace() {
    log "Creating monitoring namespace..."
    
    kubectl create namespace $NAMESPACE --dry-run=client -o yaml | kubectl apply -f -
    
    # Add labels to namespace
    kubectl label namespace $NAMESPACE name=$NAMESPACE --overwrite
    kubectl label namespace $NAMESPACE monitoring=enabled --overwrite
    
    info "Namespace $NAMESPACE created/updated"
}

# Add Helm repositories
add_helm_repos() {
    log "Adding Helm repositories..."
    
    helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
    helm repo add grafana https://grafana.github.io/helm-charts
    helm repo add stable https://charts.helm.sh/stable
    
    helm repo update
    
    info "Helm repositories added and updated"
}

# Create secrets for monitoring
create_secrets() {
    log "Creating monitoring secrets..."
    
    # Grafana admin secret
    kubectl create secret generic grafana-admin \
        --from-literal=admin-user=admin \
        --from-literal=admin-password="$(openssl rand -base64 32)" \
        --namespace=$NAMESPACE \
        --dry-run=client -o yaml | kubectl apply -f -
    
    # OAuth secrets
    kubectl create secret generic grafana-oauth \
        --from-literal=github-client-secret="${GITHUB_CLIENT_SECRET:-dummy}" \
        --namespace=$NAMESPACE \
        --dry-run=client -o yaml | kubectl apply -f -
    
    # SMTP secrets
    kubectl create secret generic grafana-smtp \
        --from-literal=password="${SENDGRID_API_KEY:-dummy}" \
        --namespace=$NAMESPACE \
        --dry-run=client -o yaml | kubectl apply -f -
    
    info "Monitoring secrets created"
}

# Deploy Prometheus
deploy_prometheus() {
    log "Deploying Prometheus..."
    
    helm upgrade --install prometheus prometheus-community/kube-prometheus-stack \
        --namespace $NAMESPACE \
        --values ./prometheus/values.yaml \
        --timeout $HELM_TIMEOUT \
        --wait
    
    info "Prometheus deployed successfully"
}

# Deploy Grafana
deploy_grafana() {
    log "Deploying Grafana..."
    
    helm upgrade --install grafana grafana/grafana \
        --namespace $NAMESPACE \
        --values ./grafana/values.yaml \
        --timeout $HELM_TIMEOUT \
        --wait
    
    info "Grafana deployed successfully"
}

# Configure ServiceMonitors for applications
configure_service_monitors() {
    log "Configuring ServiceMonitors for applications..."
    
    cat <<EOF | kubectl apply -f -
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: ecommerce-analytics-services
  namespace: $NAMESPACE
  labels:
    app: ecommerce-analytics
    release: prometheus
spec:
  selector:
    matchLabels:
      monitoring: "true"
  endpoints:
  - port: metrics
    interval: 30s
    path: /metrics
  namespaceSelector:
    matchNames:
    - production
    - staging
EOF
    
    info "ServiceMonitors configured"
}

# Create custom dashboards
create_custom_dashboards() {
    log "Creating custom Grafana dashboards..."
    
    # Application Overview Dashboard
    cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: ConfigMap
metadata:
  name: dashboard-app-overview
  namespace: $NAMESPACE
  labels:
    grafana_dashboard: "1"
data:
  app-overview.json: |
    {
      "dashboard": {
        "id": null,
        "title": "E-commerce Analytics - Application Overview",
        "tags": ["ecommerce", "analytics", "overview"],
        "style": "dark",
        "timezone": "UTC",
        "panels": [
          {
            "id": 1,
            "title": "Request Rate",
            "type": "graph",
            "targets": [
              {
                "expr": "sum(rate(http_requests_total[5m])) by (service)",
                "legendFormat": "{{service}}"
              }
            ],
            "gridPos": {"h": 9, "w": 12, "x": 0, "y": 0}
          },
          {
            "id": 2,
            "title": "Error Rate",
            "type": "graph",
            "targets": [
              {
                "expr": "sum(rate(http_requests_total{status=~\"5..\"}[5m])) by (service)",
                "legendFormat": "{{service}} errors"
              }
            ],
            "gridPos": {"h": 9, "w": 12, "x": 12, "y": 0}
          },
          {
            "id": 3,
            "title": "Response Time (95th percentile)",
            "type": "graph",
            "targets": [
              {
                "expr": "histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket[5m])) by (le, service))",
                "legendFormat": "{{service}}"
              }
            ],
            "gridPos": {"h": 9, "w": 24, "x": 0, "y": 9}
          }
        ],
        "time": {"from": "now-1h", "to": "now"},
        "refresh": "30s"
      }
    }
EOF
    
    # Database Performance Dashboard
    cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: ConfigMap
metadata:
  name: dashboard-database
  namespace: $NAMESPACE
  labels:
    grafana_dashboard: "1"
data:
  database.json: |
    {
      "dashboard": {
        "id": null,
        "title": "Database Performance",
        "tags": ["database", "postgresql", "performance"],
        "style": "dark",
        "timezone": "UTC",
        "panels": [
          {
            "id": 1,
            "title": "Database Connections",
            "type": "graph",
            "targets": [
              {
                "expr": "pg_stat_database_numbackends",
                "legendFormat": "Active connections"
              }
            ],
            "gridPos": {"h": 9, "w": 12, "x": 0, "y": 0}
          },
          {
            "id": 2,
            "title": "Query Duration",
            "type": "graph",
            "targets": [
              {
                "expr": "pg_stat_statements_mean_time_ms",
                "legendFormat": "Mean query time"
              }
            ],
            "gridPos": {"h": 9, "w": 12, "x": 12, "y": 0}
          }
        ],
        "time": {"from": "now-1h", "to": "now"},
        "refresh": "30s"
      }
    }
EOF
    
    info "Custom dashboards created"
}

# Configure alerting rules
configure_alerting() {
    log "Configuring custom alerting rules..."
    
    cat <<EOF | kubectl apply -f -
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: ecommerce-analytics-alerts
  namespace: $NAMESPACE
  labels:
    app: ecommerce-analytics
    release: prometheus
spec:
  groups:
  - name: ecommerce-analytics.rules
    rules:
    - alert: HighErrorRate
      expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.05
      for: 5m
      labels:
        severity: critical
        service: "{{ \$labels.service }}"
      annotations:
        summary: "High error rate detected"
        description: "Error rate is {{ \$value | humanizePercentage }} for service {{ \$labels.service }}"
    
    - alert: DatabaseConnectionFailure
      expr: pg_up == 0
      for: 1m
      labels:
        severity: critical
      annotations:
        summary: "Database is down"
        description: "PostgreSQL database is not responding"
    
    - alert: RedisConnectionFailure
      expr: redis_up == 0
      for: 1m
      labels:
        severity: critical
      annotations:
        summary: "Redis is down"
        description: "Redis cache is not responding"
    
    - alert: HighMemoryUsage
      expr: container_memory_usage_bytes / container_spec_memory_limit_bytes > 0.9
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: "High memory usage"
        description: "Container {{ \$labels.container }} in pod {{ \$labels.pod }} is using {{ \$value | humanizePercentage }} of memory"
    
    - alert: PodCrashLooping
      expr: rate(kube_pod_container_status_restarts_total[5m]) > 0
      for: 5m
      labels:
        severity: critical
      annotations:
        summary: "Pod is crash looping"
        description: "Pod {{ \$labels.pod }} in namespace {{ \$labels.namespace }} is restarting frequently"
EOF
    
    info "Alerting rules configured"
}

# Verify deployment
verify_deployment() {
    log "Verifying monitoring deployment..."
    
    # Wait for pods to be ready
    kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=prometheus -n $NAMESPACE --timeout=300s
    kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=grafana -n $NAMESPACE --timeout=300s
    
    # Check service endpoints
    info "Prometheus services:"
    kubectl get svc -n $NAMESPACE -l app.kubernetes.io/name=prometheus
    
    info "Grafana services:"
    kubectl get svc -n $NAMESPACE -l app.kubernetes.io/name=grafana
    
    # Get Grafana admin password
    GRAFANA_PASSWORD=$(kubectl get secret grafana-admin -n $NAMESPACE -o jsonpath="{.data.admin-password}" | base64 -d)
    info "Grafana admin password: $GRAFANA_PASSWORD"
    
    # Port forward instructions
    info "To access Grafana locally:"
    info "kubectl port-forward -n $NAMESPACE svc/grafana 3000:80"
    info "Then visit http://localhost:3000"
    
    info "To access Prometheus locally:"
    info "kubectl port-forward -n $NAMESPACE svc/prometheus-server 9090:80"
    info "Then visit http://localhost:9090"
    
    log "Monitoring deployment verification completed"
}

# Cleanup function
cleanup() {
    if [[ "$1" == "true" ]]; then
        warn "Cleaning up monitoring stack..."
        helm uninstall prometheus -n $NAMESPACE || true
        helm uninstall grafana -n $NAMESPACE || true
        kubectl delete namespace $NAMESPACE || true
        info "Cleanup completed"
    fi
}

# Main execution
main() {
    log "Starting monitoring stack deployment for $ENVIRONMENT environment"
    
    # Parse command line arguments
    CLEANUP=false
    while [[ $# -gt 0 ]]; do
        case $1 in
            --cleanup)
                CLEANUP=true
                shift
                ;;
            --environment)
                ENVIRONMENT="$2"
                shift 2
                ;;
            *)
                error "Unknown option: $1"
                exit 1
                ;;
        esac
    done
    
    if [[ "$CLEANUP" == "true" ]]; then
        cleanup true
        return
    fi
    
    # Execute deployment steps
    check_prerequisites
    create_namespace
    add_helm_repos
    create_secrets
    deploy_prometheus
    deploy_grafana
    configure_service_monitors
    create_custom_dashboards
    configure_alerting
    verify_deployment
    
    log "Monitoring stack deployment completed successfully! 🎉"
}

# Handle script interruption
trap 'error "Script interrupted"; exit 1' INT TERM

# Execute main function
main "$@"