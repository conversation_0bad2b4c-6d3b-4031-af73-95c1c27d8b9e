# Grafana Helm Chart Values for Production

# Image configuration
image:
  repository: grafana/grafana
  tag: "10.2.0"
  pullPolicy: IfNotPresent

# Resource allocation
resources:
  requests:
    cpu: 100m
    memory: 256Mi
  limits:
    cpu: 500m
    memory: 1Gi

# Security context
securityContext:
  runAsUser: 472
  runAsGroup: 472
  fsGroup: 472

# Service configuration
service:
  type: ClusterIP
  port: 3000

# Ingress configuration
ingress:
  enabled: true
  hosts:
    - grafana.yourdomain.com
  tls:
    - secretName: grafana-tls
      hosts:
        - grafana.yourdomain.com
  annotations:
    kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:us-east-1:ACCOUNT:certificate/CERT-ID

# Persistence
persistence:
  enabled: true
  storageClassName: gp3
  accessModes:
    - ReadWriteOnce
  size: 20Gi

# Admin configuration
adminUser: admin
adminPassword: <secure-admin-password>

# Grafana configuration
grafana.ini:
  server:
    root_url: https://grafana.yourdomain.com
    serve_from_sub_path: false
  
  security:
    admin_user: admin
    admin_password: <secure-admin-password>
    secret_key: <grafana-secret-key>
    disable_gravatar: true
    cookie_secure: true
    cookie_samesite: strict
    strict_transport_security: true
    x_content_type_options: true
    x_xss_protection: true
  
  auth:
    disable_login_form: false
    disable_signout_menu: false
    signout_redirect_url: ""
    oauth_auto_login: false
  
  auth.github:
    enabled: true
    allow_sign_up: true
    client_id: <github-oauth-client-id>
    client_secret: <github-oauth-client-secret>
    scopes: user:email,read:org
    auth_url: https://github.com/login/oauth/authorize
    token_url: https://github.com/login/oauth/access_token
    api_url: https://api.github.com/user
    allowed_organizations: your-organization
    team_ids: ""
    allowed_domains: yourdomain.com
    role_attribute_path: ""
    role_attribute_strict: false
  
  smtp:
    enabled: true
    host: smtp.sendgrid.net:587
    user: apikey
    password: <sendgrid-api-key>
    from_address: <EMAIL>
    from_name: Grafana
    ehlo_identity: yourdomain.com
    startTLS_policy: MandatoryStartTLS
  
  log:
    mode: console
    level: info
  
  alerting:
    enabled: true
    execute_alerts: true
    error_or_timeout: alerting
    nodata_or_nullvalues: no_data
    concurrent_render_limit: 5
  
  unified_alerting:
    enabled: true
    disabled_orgs: ""
    min_interval: 10s
    max_attempts: 3
    
  feature_toggles:
    enable: "ngalert"

# Data sources
datasources:
  datasources.yaml:
    apiVersion: 1
    datasources:
    - name: Prometheus
      type: prometheus
      url: http://prometheus-server:9090
      access: proxy
      isDefault: true
      editable: true
      jsonData:
        httpMethod: POST
        timeInterval: 30s
    
    - name: CloudWatch
      type: cloudwatch
      access: proxy
      jsonData:
        authType: default
        defaultRegion: us-east-1
      editable: true
    
    - name: PostgreSQL
      type: postgres
      url: ecommerce-analytics-production-database.cluster-xyz.us-east-1.rds.amazonaws.com:5432
      database: ecommerce_analytics
      user: grafana_readonly
      jsonData:
        sslmode: require
        maxOpenConns: 5
        maxIdleConns: 2
        connMaxLifetime: 14400
      secureJsonData:
        password: <readonly-db-password>
      editable: true

# Dashboard providers
dashboardProviders:
  dashboardproviders.yaml:
    apiVersion: 1
    providers:
    - name: 'default'
      orgId: 1
      folder: ''
      type: file
      disableDeletion: false
      editable: true
      options:
        path: /var/lib/grafana/dashboards/default
    
    - name: 'analytics'
      orgId: 1
      folder: 'Analytics'
      type: file
      disableDeletion: false
      editable: true
      options:
        path: /var/lib/grafana/dashboards/analytics
    
    - name: 'infrastructure'
      orgId: 1
      folder: 'Infrastructure'
      type: file
      disableDeletion: false
      editable: true
      options:
        path: /var/lib/grafana/dashboards/infrastructure

# Dashboards
dashboards:
  default:
    # Kubernetes cluster overview
    kubernetes-cluster:
      gnetId: 7249
      revision: 1
      datasource: Prometheus
    
    # Node exporter dashboard
    node-exporter:
      gnetId: 1860
      revision: 27
      datasource: Prometheus
    
    # Postgres overview
    postgres:
      gnetId: 9628
      revision: 7
      datasource: Prometheus
  
  analytics:
    # Custom analytics dashboard
    analytics-overview:
      url: https://raw.githubusercontent.com/your-org/grafana-dashboards/main/analytics-overview.json
      datasource: Prometheus
    
    # Real-time metrics
    real-time-metrics:
      url: https://raw.githubusercontent.com/your-org/grafana-dashboards/main/real-time-metrics.json
      datasource: Prometheus
  
  infrastructure:
    # Application performance
    application-performance:
      url: https://raw.githubusercontent.com/your-org/grafana-dashboards/main/application-performance.json
      datasource: Prometheus
    
    # Database performance
    database-performance:
      url: https://raw.githubusercontent.com/your-org/grafana-dashboards/main/database-performance.json
      datasource: PostgreSQL

# Plugins
plugins:
  - grafana-piechart-panel
  - grafana-worldmap-panel
  - grafana-clock-panel
  - grafana-simple-json-datasource
  - grafana-polystat-panel
  - vonage-status-panel
  - jdbranham-diagram-panel

# Notification channels
notifiers:
  notifiers.yaml:
    notifiers:
    - name: email-ops
      type: email
      uid: email-ops
      org_id: 1
      is_default: true
      settings:
        addresses: <EMAIL>
        subject: "Grafana Alert: {{ .Title }}"
        body: |
          {{ if gt (len .Alerts.Firing) 0 }}
          **Firing Alerts:**
          {{ range .Alerts.Firing }}
          - {{ .Annotations.summary }}
          {{ end }}
          {{ end }}
          
          {{ if gt (len .Alerts.Resolved) 0 }}
          **Resolved Alerts:**
          {{ range .Alerts.Resolved }}
          - {{ .Annotations.summary }}
          {{ end }}
          {{ end }}
    
    - name: slack-critical
      type: slack
      uid: slack-critical
      org_id: 1
      settings:
        url: <slack-webhook-url>
        channel: "#critical-alerts"
        username: "Grafana"
        title: "Critical Alert"
        text: |
          {{ if gt (len .Alerts.Firing) 0 }}
          **🚨 Firing Alerts:**
          {{ range .Alerts.Firing }}
          • {{ .Annotations.summary }}
          {{ end }}
          {{ end }}

# Environment variables
env:
  GF_SECURITY_ADMIN_USER:
    valueFrom:
      secretKeyRef:
        name: grafana-admin
        key: admin-user
  GF_SECURITY_ADMIN_PASSWORD:
    valueFrom:
      secretKeyRef:
        name: grafana-admin
        key: admin-password
  GF_AUTH_GITHUB_CLIENT_SECRET:
    valueFrom:
      secretKeyRef:
        name: grafana-oauth
        key: github-client-secret
  GF_SMTP_PASSWORD:
    valueFrom:
      secretKeyRef:
        name: grafana-smtp
        key: password

# Sidecar configuration for dynamic dashboard loading
sidecar:
  dashboards:
    enabled: true
    label: grafana_dashboard
    folder: /var/lib/grafana/dashboards
    provider:
      foldersFromFilesStructure: true
  
  datasources:
    enabled: true
    label: grafana_datasource