#!/usr/bin/env python3
"""
Real-Time Transaction Monitoring System for E-commerce Analytics
Advanced streaming analytics with complex event processing and real-time alerting
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import os
import sys
import json
import asyncio
import aiohttp
import concurrent.futures
from typing import Dict, List, Tuple, Optional, Any, Union
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
import threading
import time

# Streaming and real-time processing
from kafka import KafkaConsumer, KafkaProducer
import redis
import websocket
from confluent_kafka import Producer, Consumer, KafkaException
from confluent_kafka.admin import AdminClient, NewTopic

# Time series and streaming analytics
import pytz
from collections import Counter
import heapq
import bisect

# Complex event processing
import py_cep
from py_cep.cep import CEP
from py_cep.pattern import Pattern
from py_cep.events import Event

# Real-time ML inference
import joblib
import pickle
from sklearn.preprocessing import StandardScaler
import onnxruntime as ort

# Database connections
import asyncpg
from sqlalchemy import create_engine
from sqlalchemy.pool import QueuePool

# Monitoring and metrics
import prometheus_client
from prometheus_client import CollectorRegistry, Gauge, Counter, Histogram, push_to_gateway
import statsd

# Visualization and dashboards
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import dash
from dash import dcc, html, Input, Output

# MLflow integration
import mlflow
from mlflow.tracking import MlflowClient

# Cloud services
import boto3
from google.cloud import pubsub_v1

# Configuration
@dataclass
class MonitoringConfig:
    # Kafka configuration
    kafka_bootstrap_servers: str = os.getenv('KAFKA_BOOTSTRAP_SERVERS', 'kafka-cluster:9092')
    kafka_consumer_group: str = 'transaction-monitor'
    kafka_topics: List[str] = None
    kafka_auto_offset_reset: str = 'latest'
    kafka_enable_auto_commit: bool = True
    
    # Redis configuration
    redis_host: str = os.getenv('REDIS_HOST', 'redis-cluster')
    redis_port: int = int(os.getenv('REDIS_PORT', '6379'))
    redis_db: int = int(os.getenv('REDIS_DB', '2'))
    redis_password: str = os.getenv('REDIS_PASSWORD', '')
    
    # Database configuration
    db_host: str = os.getenv('DB_HOST', 'analytics-db.cluster-xyz.us-east-1.rds.amazonaws.com')
    db_name: str = os.getenv('DB_NAME', 'ecommerce_analytics')
    db_user: str = os.getenv('DB_USER', 'analytics_user')
    db_password: str = os.getenv('DB_PASSWORD', 'analytics_password')
    
    # Monitoring windows
    window_sizes_seconds: List[int] = None
    sliding_window_size: int = 300  # 5 minutes
    batch_window_size: int = 60     # 1 minute
    
    # Alert thresholds
    transaction_volume_threshold: int = 1000  # per minute
    fraud_score_threshold: float = 0.8
    error_rate_threshold: float = 0.05
    response_time_threshold_ms: float = 2000
    
    # Performance parameters
    max_concurrent_processors: int = 10
    buffer_size: int = 10000
    processing_timeout_seconds: int = 30
    
    # Real-time ML inference
    model_endpoint_url: str = os.getenv('MODEL_ENDPOINT', 'http://ml-serving:8080/predict')
    inference_batch_size: int = 100
    inference_timeout_seconds: int = 5
    
    # Metrics and monitoring
    prometheus_gateway: str = os.getenv('PROMETHEUS_GATEWAY', 'prometheus-pushgateway:9091')
    statsd_host: str = os.getenv('STATSD_HOST', 'statsd')
    statsd_port: int = int(os.getenv('STATSD_PORT', '8125'))
    
    # Dashboard configuration
    dashboard_port: int = int(os.getenv('DASHBOARD_PORT', '8050'))
    dashboard_host: str = '0.0.0.0'
    
    # WebSocket configuration for real-time updates
    websocket_port: int = int(os.getenv('WEBSOCKET_PORT', '8765'))
    max_websocket_connections: int = 100
    
    def __post_init__(self):
        if self.kafka_topics is None:
            self.kafka_topics = ['transactions', 'user-interactions', 'system-events']
        if self.window_sizes_seconds is None:
            self.window_sizes_seconds = [60, 300, 900, 3600]  # 1m, 5m, 15m, 1h

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TimeWindowManager:
    """Manage sliding time windows for real-time analytics"""
    
    def __init__(self, window_sizes: List[int]):
        self.window_sizes = window_sizes
        self.windows = {size: deque() for size in window_sizes}
        self.lock = threading.Lock()
    
    def add_event(self, event: Dict):
        """Add event to all time windows"""
        with self.lock:
            timestamp = event.get('timestamp', datetime.now())
            if isinstance(timestamp, str):
                timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            
            event_with_ts = {**event, 'timestamp': timestamp}
            
            for window_size in self.window_sizes:
                self.windows[window_size].append(event_with_ts)
                self._cleanup_window(window_size)
    
    def _cleanup_window(self, window_size: int):
        """Remove old events from window"""
        cutoff_time = datetime.now() - timedelta(seconds=window_size)
        window = self.windows[window_size]
        
        while window and window[0]['timestamp'] < cutoff_time:
            window.popleft()
    
    def get_window_data(self, window_size: int) -> List[Dict]:
        """Get current window data"""
        with self.lock:
            self._cleanup_window(window_size)
            return list(self.windows[window_size])
    
    def get_window_stats(self, window_size: int) -> Dict:
        """Get aggregated statistics for window"""
        data = self.get_window_data(window_size)
        
        if not data:
            return {
                'count': 0,
                'total_amount': 0,
                'avg_amount': 0,
                'min_amount': 0,
                'max_amount': 0,
                'unique_customers': 0,
                'unique_merchants': 0
            }
        
        amounts = [event.get('amount', 0) for event in data if 'amount' in event]
        customers = {event.get('customer_id') for event in data if 'customer_id' in event}
        merchants = {event.get('merchant_id') for event in data if 'merchant_id' in event}
        
        return {
            'count': len(data),
            'total_amount': sum(amounts),
            'avg_amount': np.mean(amounts) if amounts else 0,
            'min_amount': min(amounts) if amounts else 0,
            'max_amount': max(amounts) if amounts else 0,
            'unique_customers': len(customers),
            'unique_merchants': len(merchants)
        }

class ComplexEventProcessor:
    """Complex Event Processing for detecting patterns in transaction streams"""
    
    def __init__(self, config: MonitoringConfig):
        self.config = config
        self.patterns = {}
        self.event_buffer = deque(maxlen=10000)
        self.pattern_matches = defaultdict(list)
        
    def register_pattern(self, pattern_name: str, pattern_definition: Dict):
        """Register a new pattern for detection"""
        self.patterns[pattern_name] = pattern_definition
        logger.info(f"Registered pattern: {pattern_name}")
    
    def process_event(self, event: Dict) -> List[Dict]:
        """Process incoming event and detect patterns"""
        self.event_buffer.append(event)
        detected_patterns = []
        
        # Check all registered patterns
        for pattern_name, pattern_def in self.patterns.items():
            matches = self._check_pattern(pattern_name, pattern_def, event)
            if matches:
                detected_patterns.extend(matches)
        
        return detected_patterns
    
    def _check_pattern(self, pattern_name: str, pattern_def: Dict, new_event: Dict) -> List[Dict]:
        """Check if a specific pattern is detected"""
        try:
            pattern_type = pattern_def.get('type')
            
            if pattern_type == 'sequence':
                return self._check_sequence_pattern(pattern_name, pattern_def, new_event)
            elif pattern_type == 'frequency':
                return self._check_frequency_pattern(pattern_name, pattern_def, new_event)
            elif pattern_type == 'anomaly':
                return self._check_anomaly_pattern(pattern_name, pattern_def, new_event)
            elif pattern_type == 'correlation':
                return self._check_correlation_pattern(pattern_name, pattern_def, new_event)
            
            return []
            
        except Exception as e:
            logger.error(f"Error checking pattern {pattern_name}: {str(e)}")
            return []
    
    def _check_sequence_pattern(self, pattern_name: str, pattern_def: Dict, new_event: Dict) -> List[Dict]:
        """Check for sequence patterns (e.g., failed login followed by successful transaction)"""
        sequence = pattern_def.get('sequence', [])
        time_window = pattern_def.get('time_window_seconds', 300)
        
        if not sequence:
            return []
        
        # Check if new event matches the last step in sequence
        last_step = sequence[-1]
        if not self._event_matches_condition(new_event, last_step):
            return []
        
        # Look for the rest of the sequence in recent events
        cutoff_time = datetime.now() - timedelta(seconds=time_window)
        recent_events = [e for e in self.event_buffer if e.get('timestamp', datetime.now()) > cutoff_time]
        
        # Find sequence match
        for i in range(len(recent_events) - len(sequence) + 1):
            sequence_events = recent_events[i:i+len(sequence)]
            
            if all(self._event_matches_condition(event, condition) 
                   for event, condition in zip(sequence_events, sequence)):
                return [{
                    'pattern_name': pattern_name,
                    'pattern_type': 'sequence',
                    'matched_events': sequence_events,
                    'timestamp': datetime.now(),
                    'confidence': pattern_def.get('confidence', 1.0)
                }]
        
        return []
    
    def _check_frequency_pattern(self, pattern_name: str, pattern_def: Dict, new_event: Dict) -> List[Dict]:
        """Check for frequency-based patterns (e.g., too many transactions from same user)"""
        condition = pattern_def.get('condition', {})
        threshold = pattern_def.get('threshold', 10)
        time_window = pattern_def.get('time_window_seconds', 300)
        group_by = pattern_def.get('group_by', 'customer_id')
        
        if not self._event_matches_condition(new_event, condition):
            return []
        
        # Count matching events in time window
        cutoff_time = datetime.now() - timedelta(seconds=time_window)
        group_value = new_event.get(group_by)
        
        if not group_value:
            return []
        
        matching_events = [
            e for e in self.event_buffer 
            if (e.get('timestamp', datetime.now()) > cutoff_time and
                e.get(group_by) == group_value and
                self._event_matches_condition(e, condition))
        ]
        
        if len(matching_events) >= threshold:
            return [{
                'pattern_name': pattern_name,
                'pattern_type': 'frequency',
                'group_by': group_by,
                'group_value': group_value,
                'event_count': len(matching_events),
                'threshold': threshold,
                'timestamp': datetime.now(),
                'confidence': min(len(matching_events) / threshold, 2.0) / 2.0
            }]
        
        return []
    
    def _check_anomaly_pattern(self, pattern_name: str, pattern_def: Dict, new_event: Dict) -> List[Dict]:
        """Check for anomaly patterns (e.g., transaction amount significantly higher than usual)"""
        field = pattern_def.get('field', 'amount')
        anomaly_type = pattern_def.get('anomaly_type', 'statistical')
        sensitivity = pattern_def.get('sensitivity', 3.0)  # Z-score threshold
        
        if field not in new_event:
            return []
        
        current_value = new_event[field]
        
        # Get historical values for comparison
        historical_values = [
            e[field] for e in self.event_buffer 
            if field in e and isinstance(e[field], (int, float))
        ]
        
        if len(historical_values) < 10:  # Not enough historical data
            return []
        
        if anomaly_type == 'statistical':
            mean_val = np.mean(historical_values)
            std_val = np.std(historical_values)
            
            if std_val == 0:
                return []
            
            z_score = abs(current_value - mean_val) / std_val
            
            if z_score > sensitivity:
                return [{
                    'pattern_name': pattern_name,
                    'pattern_type': 'anomaly',
                    'field': field,
                    'current_value': current_value,
                    'expected_range': (mean_val - sensitivity * std_val, mean_val + sensitivity * std_val),
                    'z_score': z_score,
                    'timestamp': datetime.now(),
                    'confidence': min(z_score / sensitivity, 2.0) / 2.0
                }]
        
        return []
    
    def _check_correlation_pattern(self, pattern_name: str, pattern_def: Dict, new_event: Dict) -> List[Dict]:
        """Check for correlation patterns between different event types"""
        primary_condition = pattern_def.get('primary_condition', {})
        secondary_condition = pattern_def.get('secondary_condition', {})
        time_window = pattern_def.get('time_window_seconds', 300)
        correlation_threshold = pattern_def.get('correlation_threshold', 0.8)
        
        if not self._event_matches_condition(new_event, primary_condition):
            return []
        
        # Look for correlated events
        cutoff_time = datetime.now() - timedelta(seconds=time_window)
        primary_events = [
            e for e in self.event_buffer 
            if (e.get('timestamp', datetime.now()) > cutoff_time and
                self._event_matches_condition(e, primary_condition))
        ]
        
        secondary_events = [
            e for e in self.event_buffer 
            if (e.get('timestamp', datetime.now()) > cutoff_time and
                self._event_matches_condition(e, secondary_condition))
        ]
        
        if len(primary_events) < 5 or len(secondary_events) < 5:
            return []
        
        # Simple correlation check based on timing
        correlation_count = 0
        for primary_event in primary_events:
            primary_time = primary_event.get('timestamp', datetime.now())
            
            # Look for secondary events within a small time window
            for secondary_event in secondary_events:
                secondary_time = secondary_event.get('timestamp', datetime.now())
                time_diff = abs((primary_time - secondary_time).total_seconds())
                
                if time_diff <= 60:  # Within 1 minute
                    correlation_count += 1
                    break
        
        correlation_ratio = correlation_count / len(primary_events)
        
        if correlation_ratio >= correlation_threshold:
            return [{
                'pattern_name': pattern_name,
                'pattern_type': 'correlation',
                'primary_events_count': len(primary_events),
                'secondary_events_count': len(secondary_events),
                'correlation_ratio': correlation_ratio,
                'timestamp': datetime.now(),
                'confidence': correlation_ratio
            }]
        
        return []
    
    def _event_matches_condition(self, event: Dict, condition: Dict) -> bool:
        """Check if an event matches a given condition"""
        for field, expected_value in condition.items():
            if field not in event:
                return False
            
            actual_value = event[field]
            
            if isinstance(expected_value, dict):
                # Handle range conditions
                if 'min' in expected_value and actual_value < expected_value['min']:
                    return False
                if 'max' in expected_value and actual_value > expected_value['max']:
                    return False
                if 'equals' in expected_value and actual_value != expected_value['equals']:
                    return False
                if 'contains' in expected_value and expected_value['contains'] not in str(actual_value):
                    return False
            else:
                # Direct comparison
                if actual_value != expected_value:
                    return False
        
        return True

class RealTimeMLInference:
    """Real-time machine learning inference for transaction scoring"""
    
    def __init__(self, config: MonitoringConfig):
        self.config = config
        self.models = {}
        self.scalers = {}
        self.session = None
        self.feature_cache = {}
        
    async def load_models(self):
        """Load ML models for real-time inference"""
        try:
            # Load fraud detection model
            model_path = "/models/fraud_detection_model.pkl"
            if os.path.exists(model_path):
                self.models['fraud_detection'] = joblib.load(model_path)
                
            # Load scaler
            scaler_path = "/models/fraud_detection_scaler.pkl"
            if os.path.exists(scaler_path):
                self.scalers['fraud_detection'] = joblib.load(scaler_path)
            
            # Initialize ONNX runtime session if available
            onnx_model_path = "/models/fraud_detection_model.onnx"
            if os.path.exists(onnx_model_path):
                self.session = ort.InferenceSession(onnx_model_path)
            
            logger.info("ML models loaded successfully for real-time inference")
            
        except Exception as e:
            logger.error(f"Error loading ML models: {str(e)}")
    
    async def predict_fraud_score(self, transaction: Dict) -> float:
        """Predict fraud score for a transaction in real-time"""
        try:
            if 'fraud_detection' not in self.models:
                return 0.0
            
            # Extract features
            features = self._extract_features(transaction)
            
            if not features:
                return 0.0
            
            # Scale features
            if 'fraud_detection' in self.scalers:
                features_scaled = self.scalers['fraud_detection'].transform([features])
            else:
                features_scaled = [features]
            
            # Make prediction
            if self.session:
                # Use ONNX runtime for faster inference
                input_name = self.session.get_inputs()[0].name
                result = self.session.run(None, {input_name: features_scaled})
                fraud_score = result[0][0]
            else:
                # Use scikit-learn model
                fraud_score = self.models['fraud_detection'].predict_proba(features_scaled)[0][1]
            
            return float(fraud_score)
            
        except Exception as e:
            logger.error(f"Error predicting fraud score: {str(e)}")
            return 0.0
    
    def _extract_features(self, transaction: Dict) -> List[float]:
        """Extract numerical features from transaction for ML model"""
        try:
            features = []
            
            # Basic transaction features
            features.append(transaction.get('amount', 0))
            features.append(transaction.get('transaction_hour', 0))
            features.append(transaction.get('is_weekend', 0))
            features.append(transaction.get('is_night_time', 0))
            
            # Customer historical features (cached)
            customer_id = transaction.get('customer_id')
            if customer_id:
                customer_features = self._get_customer_features(customer_id)
                features.extend(customer_features)
            else:
                features.extend([0] * 5)  # Default customer features
            
            # Merchant features
            merchant_id = transaction.get('merchant_id')
            if merchant_id:
                merchant_features = self._get_merchant_features(merchant_id)
                features.extend(merchant_features)
            else:
                features.extend([0] * 3)  # Default merchant features
            
            return features
            
        except Exception as e:
            logger.error(f"Error extracting features: {str(e)}")
            return []
    
    def _get_customer_features(self, customer_id: str) -> List[float]:
        """Get cached customer features or compute defaults"""
        cache_key = f"customer_features:{customer_id}"
        
        if cache_key in self.feature_cache:
            return self.feature_cache[cache_key]
        
        # Default customer features (in production, these would be computed from database)
        features = [
            100.0,  # average_transaction_amount
            5.0,    # transactions_per_month
            30.0,   # days_since_last_transaction
            0.02,   # historical_fraud_rate
            365.0   # days_since_account_creation
        ]
        
        # Cache for future use
        self.feature_cache[cache_key] = features
        return features
    
    def _get_merchant_features(self, merchant_id: str) -> List[float]:
        """Get cached merchant features or compute defaults"""
        cache_key = f"merchant_features:{merchant_id}"
        
        if cache_key in self.feature_cache:
            return self.feature_cache[cache_key]
        
        # Default merchant features
        features = [
            1000.0,  # daily_transaction_volume
            0.01,    # merchant_fraud_rate
            4.2      # merchant_rating
        ]
        
        # Cache for future use
        self.feature_cache[cache_key] = features
        return features

class MetricsCollector:
    """Collect and publish real-time metrics"""
    
    def __init__(self, config: MonitoringConfig):
        self.config = config
        self.prometheus_registry = CollectorRegistry()
        self.statsd_client = None
        
        # Prometheus metrics
        self.transaction_counter = Counter(
            'transactions_total',
            'Total number of transactions processed',
            ['status', 'payment_method'],
            registry=self.prometheus_registry
        )
        
        self.transaction_amount_histogram = Histogram(
            'transaction_amount',
            'Transaction amounts',
            buckets=[10, 50, 100, 500, 1000, 5000, 10000],
            registry=self.prometheus_registry
        )
        
        self.fraud_score_gauge = Gauge(
            'fraud_score',
            'Current fraud score',
            ['customer_id'],
            registry=self.prometheus_registry
        )
        
        self.processing_time_histogram = Histogram(
            'processing_time_seconds',
            'Transaction processing time',
            registry=self.prometheus_registry
        )
        
        # Initialize StatsD client
        try:
            import statsd
            self.statsd_client = statsd.StatsClient(
                host=self.config.statsd_host,
                port=self.config.statsd_port
            )
        except Exception as e:
            logger.warning(f"Could not initialize StatsD client: {str(e)}")
    
    def record_transaction(self, transaction: Dict, processing_time: float, fraud_score: float):
        """Record transaction metrics"""
        try:
            status = 'success' if transaction.get('status') == 'completed' else 'failed'
            payment_method = transaction.get('payment_method', 'unknown')
            amount = transaction.get('amount', 0)
            customer_id = transaction.get('customer_id', 'unknown')
            
            # Prometheus metrics
            self.transaction_counter.labels(status=status, payment_method=payment_method).inc()
            self.transaction_amount_histogram.observe(amount)
            self.fraud_score_gauge.labels(customer_id=customer_id).set(fraud_score)
            self.processing_time_histogram.observe(processing_time)
            
            # StatsD metrics
            if self.statsd_client:
                self.statsd_client.incr(f'transactions.{status}')
                self.statsd_client.histogram('transaction.amount', amount)
                self.statsd_client.gauge('fraud.score', fraud_score)
                self.statsd_client.timing('processing.time', processing_time * 1000)  # Convert to ms
            
        except Exception as e:
            logger.error(f"Error recording transaction metrics: {str(e)}")
    
    def push_metrics(self):
        """Push metrics to Prometheus gateway"""
        try:
            push_to_gateway(
                self.config.prometheus_gateway,
                job='real-time-transaction-monitor',
                registry=self.prometheus_registry
            )
        except Exception as e:
            logger.error(f"Error pushing metrics to Prometheus: {str(e)}")

class RealTimeTransactionMonitor:
    """Main real-time transaction monitoring system"""
    
    def __init__(self, config: MonitoringConfig):
        self.config = config
        self.window_manager = TimeWindowManager(config.window_sizes_seconds)
        self.event_processor = ComplexEventProcessor(config)
        self.ml_inference = RealTimeMLInference(config)
        self.metrics_collector = MetricsCollector(config)
        
        # Initialize connections
        self.kafka_consumer = None
        self.kafka_producer = None
        self.redis_client = None
        self.db_pool = None
        
        # Control flags
        self.running = False
        self.threads = []
        
        # WebSocket connections for real-time updates
        self.websocket_connections = set()
        
        # MLflow setup
        mlflow.set_tracking_uri(os.getenv('MLFLOW_TRACKING_URI', 'http://mlflow-server.mlflow:5000'))
        mlflow.set_experiment('real-time-monitoring')
    
    async def initialize(self):
        """Initialize all components"""
        try:
            await self._initialize_kafka()
            await self._initialize_redis()
            await self._initialize_database()
            await self.ml_inference.load_models()
            self._register_default_patterns()
            
            logger.info("Real-time transaction monitor initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing monitor: {str(e)}")
            raise
    
    async def _initialize_kafka(self):
        """Initialize Kafka consumer and producer"""
        try:
            # Kafka consumer
            self.kafka_consumer = KafkaConsumer(
                *self.config.kafka_topics,
                bootstrap_servers=self.config.kafka_bootstrap_servers,
                group_id=self.config.kafka_consumer_group,
                auto_offset_reset=self.config.kafka_auto_offset_reset,
                enable_auto_commit=self.config.kafka_enable_auto_commit,
                value_deserializer=lambda x: json.loads(x.decode('utf-8'))
            )
            
            # Kafka producer for alerts
            self.kafka_producer = KafkaProducer(
                bootstrap_servers=self.config.kafka_bootstrap_servers,
                value_serializer=lambda v: json.dumps(v, default=str).encode('utf-8')
            )
            
            logger.info("Kafka initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing Kafka: {str(e)}")
            raise
    
    async def _initialize_redis(self):
        """Initialize Redis connection"""
        try:
            self.redis_client = redis.Redis(
                host=self.config.redis_host,
                port=self.config.redis_port,
                db=self.config.redis_db,
                password=self.config.redis_password if self.config.redis_password else None,
                decode_responses=True
            )
            
            await self.redis_client.ping()
            logger.info("Redis initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing Redis: {str(e)}")
            raise
    
    async def _initialize_database(self):
        """Initialize database connection pool"""
        try:
            self.db_pool = await asyncpg.create_pool(
                host=self.config.db_host,
                database=self.config.db_name,
                user=self.config.db_user,
                password=self.config.db_password,
                min_size=5,
                max_size=20
            )
            
            logger.info("Database connection pool initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing database: {str(e)}")
            raise
    
    def _register_default_patterns(self):
        """Register default patterns for complex event processing"""
        # Pattern 1: High-frequency transactions from same customer
        self.event_processor.register_pattern('high_frequency_customer', {
            'type': 'frequency',
            'condition': {'event_type': 'transaction'},
            'group_by': 'customer_id',
            'threshold': 20,
            'time_window_seconds': 300,
            'confidence': 0.8
        })
        
        # Pattern 2: Large transaction after failed login attempts
        self.event_processor.register_pattern('fraud_sequence', {
            'type': 'sequence',
            'sequence': [
                {'event_type': 'failed_login'},
                {'event_type': 'transaction', 'amount': {'min': 1000}}
            ],
            'time_window_seconds': 600,
            'confidence': 0.9
        })
        
        # Pattern 3: Transaction amount anomaly
        self.event_processor.register_pattern('amount_anomaly', {
            'type': 'anomaly',
            'field': 'amount',
            'anomaly_type': 'statistical',
            'sensitivity': 3.0,
            'confidence': 0.7
        })
        
        # Pattern 4: Correlation between failed payments and successful transactions
        self.event_processor.register_pattern('payment_correlation', {
            'type': 'correlation',
            'primary_condition': {'event_type': 'failed_payment'},
            'secondary_condition': {'event_type': 'transaction', 'status': 'completed'},
            'time_window_seconds': 300,
            'correlation_threshold': 0.7,
            'confidence': 0.8
        })
        
        logger.info("Default patterns registered successfully")
    
    async def start_monitoring(self):
        """Start the real-time monitoring system"""
        try:
            self.running = True
            
            # Start main processing thread
            processing_thread = threading.Thread(target=self._process_events)
            processing_thread.start()
            self.threads.append(processing_thread)
            
            # Start metrics collection thread
            metrics_thread = threading.Thread(target=self._collect_metrics)
            metrics_thread.start()
            self.threads.append(metrics_thread)
            
            # Start dashboard
            dashboard_thread = threading.Thread(target=self._start_dashboard)
            dashboard_thread.start()
            self.threads.append(dashboard_thread)
            
            logger.info("Real-time monitoring started successfully")
            
            # Keep main thread alive
            while self.running:
                await asyncio.sleep(1)
                
        except KeyboardInterrupt:
            logger.info("Shutdown signal received")
            await self.stop_monitoring()
        except Exception as e:
            logger.error(f"Error in monitoring: {str(e)}")
            await self.stop_monitoring()
    
    def _process_events(self):
        """Main event processing loop"""
        logger.info("Starting event processing loop")
        
        try:
            for message in self.kafka_consumer:
                if not self.running:
                    break
                
                try:
                    event = message.value
                    start_time = time.time()
                    
                    # Add to time windows
                    self.window_manager.add_event(event)
                    
                    # Process with CEP
                    pattern_matches = self.event_processor.process_event(event)
                    
                    # Handle pattern matches
                    for match in pattern_matches:
                        asyncio.create_task(self._handle_pattern_match(match))
                    
                    # ML inference for fraud detection
                    if event.get('event_type') == 'transaction':
                        fraud_score = asyncio.run(self.ml_inference.predict_fraud_score(event))
                        event['fraud_score'] = fraud_score
                        
                        # Check fraud threshold
                        if fraud_score > self.config.fraud_score_threshold:
                            asyncio.create_task(self._handle_fraud_alert(event, fraud_score))
                    
                    # Record metrics
                    processing_time = time.time() - start_time
                    if event.get('event_type') == 'transaction':
                        self.metrics_collector.record_transaction(
                            event, processing_time, event.get('fraud_score', 0)
                        )
                    
                    # Cache event data
                    if self.redis_client:
                        self._cache_event_data(event)
                    
                    # Broadcast to WebSocket connections
                    asyncio.create_task(self._broadcast_to_websockets(event))
                    
                except Exception as e:
                    logger.error(f"Error processing event: {str(e)}")
                    continue
                    
        except Exception as e:
            logger.error(f"Error in event processing loop: {str(e)}")
        finally:
            logger.info("Event processing loop stopped")
    
    async def _handle_pattern_match(self, match: Dict):
        """Handle detected pattern matches"""
        try:
            alert = {
                'alert_type': 'pattern_match',
                'pattern_name': match['pattern_name'],
                'pattern_type': match['pattern_type'],
                'confidence': match['confidence'],
                'timestamp': datetime.now().isoformat(),
                'details': match
            }
            
            # Send alert via Kafka
            if self.kafka_producer:
                self.kafka_producer.send('pattern-alerts', alert)
            
            # Store in database
            await self._store_alert(alert)
            
            logger.info(f"Pattern match detected: {match['pattern_name']} (confidence: {match['confidence']:.2f})")
            
        except Exception as e:
            logger.error(f"Error handling pattern match: {str(e)}")
    
    async def _handle_fraud_alert(self, transaction: Dict, fraud_score: float):
        """Handle fraud alerts"""
        try:
            alert = {
                'alert_type': 'fraud_detection',
                'transaction_id': transaction.get('transaction_id'),
                'customer_id': transaction.get('customer_id'),
                'fraud_score': fraud_score,
                'transaction_amount': transaction.get('amount'),
                'timestamp': datetime.now().isoformat(),
                'severity': 'HIGH' if fraud_score > 0.9 else 'MEDIUM'
            }
            
            # Send alert via Kafka
            if self.kafka_producer:
                self.kafka_producer.send('fraud-alerts', alert)
            
            # Store in database
            await self._store_alert(alert)
            
            logger.warning(f"Fraud alert: Transaction {transaction.get('transaction_id')} - Score: {fraud_score:.3f}")
            
        except Exception as e:
            logger.error(f"Error handling fraud alert: {str(e)}")
    
    async def _store_alert(self, alert: Dict):
        """Store alert in database"""
        try:
            if not self.db_pool:
                return
            
            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO real_time_alerts 
                    (alert_type, alert_data, created_at)
                    VALUES ($1, $2, $3)
                """, alert['alert_type'], json.dumps(alert), datetime.now())
                
        except Exception as e:
            logger.error(f"Error storing alert: {str(e)}")
    
    def _cache_event_data(self, event: Dict):
        """Cache event data in Redis for fast access"""
        try:
            if not self.redis_client:
                return
            
            # Cache recent transactions by customer
            if event.get('event_type') == 'transaction' and event.get('customer_id'):
                customer_key = f"customer_transactions:{event['customer_id']}"
                self.redis_client.lpush(customer_key, json.dumps(event, default=str))
                self.redis_client.ltrim(customer_key, 0, 99)  # Keep last 100 transactions
                self.redis_client.expire(customer_key, 86400)  # 24 hour expiry
            
            # Cache window statistics
            for window_size in self.config.window_sizes_seconds:
                stats = self.window_manager.get_window_stats(window_size)
                stats_key = f"window_stats:{window_size}"
                self.redis_client.setex(stats_key, 300, json.dumps(stats))  # 5 minute expiry
            
        except Exception as e:
            logger.error(f"Error caching event data: {str(e)}")
    
    async def _broadcast_to_websockets(self, event: Dict):
        """Broadcast event data to WebSocket connections"""
        try:
            if not self.websocket_connections:
                return
            
            message = {
                'type': 'event_update',
                'data': event,
                'timestamp': datetime.now().isoformat()
            }
            
            # Send to all connected WebSocket clients
            disconnected = set()
            for ws in self.websocket_connections:
                try:
                    await ws.send(json.dumps(message, default=str))
                except Exception:
                    disconnected.add(ws)
            
            # Remove disconnected clients
            self.websocket_connections -= disconnected
            
        except Exception as e:
            logger.error(f"Error broadcasting to WebSockets: {str(e)}")
    
    def _collect_metrics(self):
        """Collect and push metrics periodically"""
        logger.info("Starting metrics collection thread")
        
        try:
            while self.running:
                try:
                    # Push metrics to Prometheus
                    self.metrics_collector.push_metrics()
                    
                    # Sleep for 30 seconds
                    time.sleep(30)
                    
                except Exception as e:
                    logger.error(f"Error collecting metrics: {str(e)}")
                    time.sleep(30)
                    
        except Exception as e:
            logger.error(f"Error in metrics collection thread: {str(e)}")
        finally:
            logger.info("Metrics collection thread stopped")
    
    def _start_dashboard(self):
        """Start real-time dashboard"""
        try:
            app = dash.Dash(__name__)
            
            app.layout = html.Div([
                html.H1("Real-Time Transaction Monitor"),
                
                dcc.Graph(id='transaction-volume'),
                dcc.Graph(id='fraud-scores'),
                dcc.Graph(id='pattern-alerts'),
                
                dcc.Interval(
                    id='interval-component',
                    interval=5000,  # Update every 5 seconds
                    n_intervals=0
                )
            ])
            
            @app.callback(
                [Output('transaction-volume', 'figure'),
                 Output('fraud-scores', 'figure'),
                 Output('pattern-alerts', 'figure')],
                [Input('interval-component', 'n_intervals')]
            )
            def update_dashboard(n):
                return self._generate_dashboard_figures()
            
            app.run_server(
                host=self.config.dashboard_host,
                port=self.config.dashboard_port,
                debug=False
            )
            
        except Exception as e:
            logger.error(f"Error starting dashboard: {str(e)}")
    
    def _generate_dashboard_figures(self):
        """Generate figures for dashboard"""
        try:
            # Transaction volume figure
            volume_data = []
            for window_size in [60, 300, 900]:
                stats = self.window_manager.get_window_stats(window_size)
                volume_data.append({
                    'window': f"{window_size // 60}min",
                    'count': stats['count'],
                    'total_amount': stats['total_amount']
                })
            
            volume_fig = go.Figure()
            volume_fig.add_trace(go.Bar(
                x=[d['window'] for d in volume_data],
                y=[d['count'] for d in volume_data],
                name='Transaction Count'
            ))
            volume_fig.update_layout(title='Transaction Volume by Time Window')
            
            # Fraud scores figure (placeholder)
            fraud_fig = go.Figure()
            fraud_fig.add_trace(go.Scatter(
                x=list(range(10)),
                y=np.random.rand(10),
                mode='lines+markers',
                name='Fraud Score'
            ))
            fraud_fig.update_layout(title='Recent Fraud Scores')
            
            # Pattern alerts figure (placeholder)
            alerts_fig = go.Figure()
            alerts_fig.add_trace(go.Bar(
                x=['Frequency', 'Sequence', 'Anomaly', 'Correlation'],
                y=[5, 3, 7, 2],
                name='Pattern Alerts'
            ))
            alerts_fig.update_layout(title='Pattern Alerts by Type')
            
            return volume_fig, fraud_fig, alerts_fig
            
        except Exception as e:
            logger.error(f"Error generating dashboard figures: {str(e)}")
            return {}, {}, {}
    
    async def stop_monitoring(self):
        """Stop the monitoring system"""
        try:
            logger.info("Stopping real-time monitoring system...")
            self.running = False
            
            # Close Kafka connections
            if self.kafka_consumer:
                self.kafka_consumer.close()
            if self.kafka_producer:
                self.kafka_producer.close()
            
            # Close Redis connection
            if self.redis_client:
                self.redis_client.close()
            
            # Close database pool
            if self.db_pool:
                await self.db_pool.close()
            
            # Wait for threads to finish
            for thread in self.threads:
                thread.join(timeout=5)
            
            logger.info("Real-time monitoring system stopped")
            
        except Exception as e:
            logger.error(f"Error stopping monitoring system: {str(e)}")

async def main():
    """Main execution function"""
    config = MonitoringConfig()
    
    # Initialize monitoring system
    monitor = RealTimeTransactionMonitor(config)
    await monitor.initialize()
    
    # Start monitoring
    await monitor.start_monitoring()

if __name__ == "__main__":
    asyncio.run(main())