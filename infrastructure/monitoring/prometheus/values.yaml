# Prometheus Helm Chart Values for Production

# Global configuration
global:
  scrape_interval: 30s
  evaluation_interval: 30s
  external_labels:
    cluster: ecommerce-analytics-production
    environment: production

# Prometheus server configuration
server:
  enabled: true
  
  # Resource allocation
  resources:
    requests:
      cpu: 500m
      memory: 2Gi
    limits:
      cpu: 2
      memory: 8Gi
  
  # Persistence
  persistentVolume:
    enabled: true
    accessModes:
      - ReadWriteOnce
    size: 100Gi
    storageClass: gp3
  
  # Retention
  retention: "30d"
  retentionSize: "80GB"
  
  # Security context
  securityContext:
    runAsUser: 65534
    runAsGroup: 65534
    fsGroup: 65534
  
  # Service configuration
  service:
    type: ClusterIP
    port: 9090
  
  # Ingress
  ingress:
    enabled: true
    hosts:
      - prometheus.yourdomain.com
    tls:
      - secretName: prometheus-tls
        hosts:
          - prometheus.yourdomain.com

# AlertManager configuration
alertmanager:
  enabled: true
  
  # Resource allocation
  resources:
    requests:
      cpu: 100m
      memory: 256Mi
    limits:
      cpu: 500m
      memory: 1Gi
  
  # Persistence
  persistentVolume:
    enabled: true
    size: 10Gi
    storageClass: gp3
  
  # Configuration
  config:
    global:
      smtp_smarthost: 'smtp.sendgrid.net:587'
      smtp_from: '<EMAIL>'
      smtp_auth_username: 'apikey'
      smtp_auth_password: '<sendgrid-api-key>'
    
    route:
      group_by: ['alertname', 'cluster', 'service']
      group_wait: 10s
      group_interval: 10s
      repeat_interval: 1h
      receiver: 'default'
      routes:
        - match:
            severity: critical
          receiver: 'critical-alerts'
        - match:
            severity: warning
          receiver: 'warning-alerts'
    
    receivers:
      - name: 'default'
        email_configs:
          - to: '<EMAIL>'
            subject: 'Alert: {{ .GroupLabels.alertname }}'
            body: |
              {{ range .Alerts }}
              Alert: {{ .Annotations.summary }}
              Description: {{ .Annotations.description }}
              {{ end }}
      
      - name: 'critical-alerts'
        email_configs:
          - to: '<EMAIL>,<EMAIL>'
            subject: 'CRITICAL: {{ .GroupLabels.alertname }}'
        slack_configs:
          - api_url: '<slack-webhook-url>'
            channel: '#critical-alerts'
            title: 'Critical Alert'
            text: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
      
      - name: 'warning-alerts'
        email_configs:
          - to: '<EMAIL>'
            subject: 'WARNING: {{ .GroupLabels.alertname }}'
        slack_configs:
          - api_url: '<slack-webhook-url>'
            channel: '#alerts'
            title: 'Warning Alert'
            text: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'

# Node Exporter
nodeExporter:
  enabled: true
  
  # Resource allocation
  resources:
    requests:
      cpu: 100m
      memory: 128Mi
    limits:
      cpu: 200m
      memory: 256Mi
  
  # Security
  securityContext:
    runAsUser: 65534

# kube-state-metrics
kubeStateMetrics:
  enabled: true
  
  # Resource allocation
  resources:
    requests:
      cpu: 100m
      memory: 128Mi
    limits:
      cpu: 200m
      memory: 256Mi

# Pushgateway
pushgateway:
  enabled: true
  
  # Resource allocation
  resources:
    requests:
      cpu: 50m
      memory: 64Mi
    limits:
      cpu: 100m
      memory: 128Mi
  
  # Persistence
  persistentVolume:
    enabled: true
    size: 5Gi

# Service monitors for application services
extraScrapeConfigs: |
  - job_name: 'kubernetes-pods'
    kubernetes_sd_configs:
      - role: pod
    relabel_configs:
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
        action: keep
        regex: true
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
        action: replace
        target_label: __metrics_path__
        regex: (.+)
      - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
        action: replace
        regex: ([^:]+)(?::\d+)?;(\d+)
        replacement: $1:$2
        target_label: __address__
      - action: labelmap
        regex: __meta_kubernetes_pod_label_(.+)
      - source_labels: [__meta_kubernetes_namespace]
        action: replace
        target_label: kubernetes_namespace
      - source_labels: [__meta_kubernetes_pod_name]
        action: replace
        target_label: kubernetes_pod_name

  - job_name: 'analytics-service'
    static_configs:
      - targets: ['analytics-service:9090']
    scrape_interval: 15s
    metrics_path: /metrics

  - job_name: 'dashboard-service'
    static_configs:
      - targets: ['dashboard-service:9090']
    scrape_interval: 15s
    metrics_path: /metrics

  - job_name: 'integration-service'
    static_configs:
      - targets: ['integration-service:9090']
    scrape_interval: 15s
    metrics_path: /metrics

  - job_name: 'error-tracking-service'
    static_configs:
      - targets: ['error-tracking-service:9090']
    scrape_interval: 15s
    metrics_path: /metrics

  - job_name: 'admin-service'
    static_configs:
      - targets: ['admin-service:9090']
    scrape_interval: 15s
    metrics_path: /metrics

# Custom rules
serverFiles:
  alerting_rules.yml:
    groups:
    - name: ecommerce-analytics.rules
      rules:
      # High error rate alert
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.05
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value | humanizePercentage }} for {{ $labels.service }}"

      # High response time alert
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 0.5
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "High response time detected"
          description: "95th percentile response time is {{ $value }}s for {{ $labels.service }}"

      # Database connection issues
      - alert: DatabaseConnectionFailure
        expr: db_connections_failed_total > 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Database connection failures detected"
          description: "{{ $value }} database connections have failed"

      # Redis connection issues
      - alert: RedisConnectionFailure
        expr: redis_connections_failed_total > 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Redis connection failures detected"
          description: "{{ $value }} Redis connections have failed"

      # High memory usage
      - alert: HighMemoryUsage
        expr: container_memory_usage_bytes / container_spec_memory_limit_bytes > 0.9
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage detected"
          description: "Memory usage is {{ $value | humanizePercentage }} for {{ $labels.pod }}"

      # High CPU usage
      - alert: HighCPUUsage
        expr: rate(container_cpu_usage_seconds_total[5m]) / container_spec_cpu_quota * 100 > 80
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage detected"
          description: "CPU usage is {{ $value }}% for {{ $labels.pod }}"

      # Pod crash looping
      - alert: PodCrashLooping
        expr: rate(kube_pod_container_status_restarts_total[5m]) > 0
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "Pod is crash looping"
          description: "Pod {{ $labels.pod }} in namespace {{ $labels.namespace }} is crash looping"

      # Service down
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Service is down"
          description: "{{ $labels.job }} service is down"

      # Disk space running low
      - alert: DiskSpaceLow
        expr: (node_filesystem_avail_bytes / node_filesystem_size_bytes) < 0.1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Disk space running low"
          description: "Disk space is {{ $value | humanizePercentage }} available on {{ $labels.instance }}"