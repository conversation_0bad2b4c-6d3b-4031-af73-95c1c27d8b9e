#!/bin/bash

# Kubeflow Setup Script for E-commerce Analytics ML Platform
# Sets up Kubeflow Pipelines, Central Dashboard, and ML-specific infrastructure

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_FILE="${LOG_FILE:-/var/log/kubeflow/setup-$(date +%Y%m%d_%H%M%S).log}"
KUBEFLOW_VERSION="${KUBEFLOW_VERSION:-1.8.0}"
NAMESPACE="${NAMESPACE:-kubeflow}"
EKS_CLUSTER_NAME="${EKS_CLUSTER_NAME:-ecommerce-analytics}"
AWS_REGION="${AWS_REGION:-us-east-1}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Create log directory
mkdir -p "$(dirname "$LOG_FILE")"

# Logging function
log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message" | tee -a "$LOG_FILE"
    
    case $level in
        "ERROR")
            echo -e "${RED}[$timestamp] [$level] $message${NC}" >&2
            ;;
        "WARN")
            echo -e "${YELLOW}[$timestamp] [$level] $message${NC}"
            ;;
        "INFO")
            echo -e "${GREEN}[$timestamp] [$level] $message${NC}"
            ;;
        "DEBUG")
            echo -e "${BLUE}[$timestamp] [$level] $message${NC}"
            ;;
    esac
}

# Error handling
error_exit() {
    log "ERROR" "$1"
    exit 1
}

# Check prerequisites
check_prerequisites() {
    log "INFO" "Checking prerequisites..."
    
    # Check kubectl
    if ! command -v kubectl &> /dev/null; then
        error_exit "kubectl is not installed"
    fi
    
    # Check AWS CLI
    if ! command -v aws &> /dev/null; then
        error_exit "AWS CLI is not installed"
    fi
    
    # Check cluster access
    if ! kubectl cluster-info &> /dev/null; then
        error_exit "Cannot access Kubernetes cluster"
    fi
    
    # Check kustomize
    if ! command -v kustomize &> /dev/null; then
        log "INFO" "Installing kustomize..."
        curl -s "https://raw.githubusercontent.com/kubernetes-sigs/kustomize/master/hack/install_kustomize.sh" | bash
        sudo mv kustomize /usr/local/bin/
    fi
    
    log "INFO" "Prerequisites check completed"
}

# Create GPU node group for ML workloads
create_gpu_node_group() {
    log "INFO" "Creating GPU node group for ML workloads..."
    
    # Check if GPU node group already exists
    if aws eks describe-nodegroup \
        --cluster-name "$EKS_CLUSTER_NAME" \
        --nodegroup-name "ml-gpu-nodes" \
        --region "$AWS_REGION" &> /dev/null; then
        log "INFO" "GPU node group already exists"
        return 0
    fi
    
    # Create GPU node group
    aws eks create-nodegroup \
        --cluster-name "$EKS_CLUSTER_NAME" \
        --nodegroup-name "ml-gpu-nodes" \
        --instance-types "g4dn.xlarge" "g4dn.2xlarge" \
        --ami-type "AL2_x86_64_GPU" \
        --node-role "arn:aws:iam::$(aws sts get-caller-identity --query Account --output text):role/NodeInstanceRole" \
        --subnets $(aws eks describe-cluster --name "$EKS_CLUSTER_NAME" --query 'cluster.resourcesVpcConfig.subnetIds' --output text --region "$AWS_REGION") \
        --capacity-type "SPOT" \
        --scaling-config minSize=0,maxSize=10,desiredSize=1 \
        --disk-size 100 \
        --region "$AWS_REGION" \
        --tags Environment=production,Component=ml-platform,ManagedBy=kubeflow-setup
    
    log "INFO" "GPU node group creation initiated"
}

# Install NVIDIA device plugin for GPU support
install_nvidia_plugin() {
    log "INFO" "Installing NVIDIA device plugin..."
    
    kubectl apply -f https://raw.githubusercontent.com/NVIDIA/k8s-device-plugin/v0.14.1/nvidia-device-plugin.yml
    
    log "INFO" "NVIDIA device plugin installed"
}

# Install Kubeflow Pipelines
install_kubeflow_pipelines() {
    log "INFO" "Installing Kubeflow Pipelines..."
    
    # Create kubeflow namespace
    kubectl create namespace "$NAMESPACE" --dry-run=client -o yaml | kubectl apply -f -
    
    # Install cluster-scoped resources
    kubectl apply -k "github.com/kubeflow/pipelines/manifests/kustomize/cluster-scoped-resources?ref=$KUBEFLOW_VERSION"
    
    # Wait for CRDs to be established
    kubectl wait --for condition=established --timeout=60s crd/applications.app.k8s.io
    
    # Install environment-specific resources
    kubectl apply -k "github.com/kubeflow/pipelines/manifests/kustomize/env/platform-agnostic-pns?ref=$KUBEFLOW_VERSION"
    
    # Wait for deployment to be ready
    kubectl wait --for=condition=available --timeout=600s deployment/ml-pipeline-ui -n kubeflow
    kubectl wait --for=condition=available --timeout=600s deployment/ml-pipeline -n kubeflow
    
    log "INFO" "Kubeflow Pipelines installation completed"
}

# Install Kubeflow Central Dashboard
install_kubeflow_dashboard() {
    log "INFO" "Installing Kubeflow Central Dashboard..."
    
    # Install Central Dashboard
    kubectl apply -k "github.com/kubeflow/kubeflow/components/centraldashboard/manifests/overlays/kserve?ref=v1.7.0"
    
    # Wait for deployment
    kubectl wait --for=condition=available --timeout=300s deployment/centraldashboard -n kubeflow
    
    log "INFO" "Kubeflow Central Dashboard installed"
}

# Install Kubeflow Notebooks
install_kubeflow_notebooks() {
    log "INFO" "Installing Kubeflow Notebooks..."
    
    # Install Notebook Controller
    kubectl apply -k "github.com/kubeflow/kubeflow/components/notebook-controller/config/overlays/kubeflow?ref=v1.7.0"
    
    # Install Jupyter Web App
    kubectl apply -k "github.com/kubeflow/kubeflow/components/jupyter-web-app/manifests/overlays/istio?ref=v1.7.0"
    
    log "INFO" "Kubeflow Notebooks installed"
}

# Install Istio for service mesh (required for Kubeflow)
install_istio() {
    log "INFO" "Installing Istio service mesh..."
    
    # Download and install istioctl
    if ! command -v istioctl &> /dev/null; then
        curl -L https://istio.io/downloadIstio | sh -
        sudo mv istio-*/bin/istioctl /usr/local/bin/
    fi
    
    # Install Istio
    istioctl install --set values.defaultRevision=default -y
    
    # Label namespace for injection
    kubectl label namespace kubeflow istio-injection=enabled --overwrite
    
    log "INFO" "Istio installation completed"
}

# Set up ingress for Kubeflow
setup_kubeflow_ingress() {
    log "INFO" "Setting up Kubeflow ingress..."
    
    # Create Kubeflow Gateway
    cat <<EOF | kubectl apply -f -
apiVersion: networking.istio.io/v1alpha3
kind: Gateway
metadata:
  name: kubeflow-gateway
  namespace: kubeflow
spec:
  selector:
    istio: ingressgateway
  servers:
  - port:
      number: 80
      name: http
      protocol: HTTP
    hosts:
    - kubeflow.${PRIMARY_DOMAIN:-company.com}
    tls:
      httpsRedirect: true
  - port:
      number: 443
      name: https
      protocol: HTTPS
    hosts:
    - kubeflow.${PRIMARY_DOMAIN:-company.com}
    tls:
      mode: SIMPLE
      credentialName: kubeflow-tls
EOF

    # Create VirtualService
    cat <<EOF | kubectl apply -f -
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: kubeflow-vs
  namespace: kubeflow
spec:
  hosts:
  - kubeflow.${PRIMARY_DOMAIN:-company.com}
  gateways:
  - kubeflow-gateway
  http:
  - match:
    - uri:
        prefix: /
    route:
    - destination:
        host: centraldashboard.kubeflow.svc.cluster.local
        port:
          number: 80
EOF

    log "INFO" "Kubeflow ingress setup completed"
}

# Create ML-specific storage classes
create_ml_storage() {
    log "INFO" "Creating ML-specific storage classes..."
    
    # High-performance storage for ML workloads
    cat <<EOF | kubectl apply -f -
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: ml-high-performance
  annotations:
    storageclass.kubernetes.io/is-default-class: "false"
provisioner: ebs.csi.aws.com
parameters:
  type: gp3
  iops: "3000"
  throughput: "125"
  encrypted: "true"
volumeBindingMode: WaitForFirstConsumer
allowVolumeExpansion: true
---
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: ml-dataset-storage
  annotations:
    storageclass.kubernetes.io/is-default-class: "false"
provisioner: efs.csi.aws.com
parameters:
  provisioningMode: efs-ap
  fileSystemId: ${EFS_FILE_SYSTEM_ID:-fs-0123456789abcdef0}
  directoryPerms: "0755"
volumeBindingMode: Immediate
allowVolumeExpansion: true
EOF

    log "INFO" "ML storage classes created"
}

# Create service accounts with proper RBAC
create_service_accounts() {
    log "INFO" "Creating ML service accounts..."
    
    # ML Pipeline service account
    cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: ServiceAccount
metadata:
  name: ml-pipeline-runner
  namespace: kubeflow
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::$(aws sts get-caller-identity --query Account --output text):role/KubeflowPipelineRole
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: ml-pipeline-runner
rules:
- apiGroups: [""]
  resources: ["pods", "services", "endpoints", "persistentvolumeclaims", "events", "configmaps", "secrets"]
  verbs: ["*"]
- apiGroups: ["apps"]
  resources: ["deployments", "daemonsets", "replicasets", "statefulsets"]
  verbs: ["*"]
- apiGroups: ["argoproj.io"]
  resources: ["workflows", "workflowtemplates"]
  verbs: ["*"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: ml-pipeline-runner
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: ml-pipeline-runner
subjects:
- kind: ServiceAccount
  name: ml-pipeline-runner
  namespace: kubeflow
EOF

    log "INFO" "Service accounts created"
}

# Verify installation
verify_installation() {
    log "INFO" "Verifying Kubeflow installation..."
    
    # Check all pods are running
    local failed_pods=0
    while IFS= read -r pod; do
        if [[ -n "$pod" ]]; then
            log "ERROR" "Pod not ready: $pod"
            failed_pods=$((failed_pods + 1))
        fi
    done < <(kubectl get pods -n kubeflow --field-selector=status.phase!=Running --no-headers -o custom-columns=NAME:.metadata.name 2>/dev/null)
    
    if [[ $failed_pods -gt 0 ]]; then
        log "WARN" "$failed_pods pods are not in Running state"
    else
        log "INFO" "All Kubeflow pods are running"
    fi
    
    # Check services
    local services=(
        "ml-pipeline-ui"
        "ml-pipeline"
        "centraldashboard"
    )
    
    for service in "${services[@]}"; do
        if kubectl get service "$service" -n kubeflow &> /dev/null; then
            log "INFO" "Service $service is available"
        else
            log "ERROR" "Service $service is not available"
        fi
    done
    
    # Get access URL
    local kubeflow_url="https://kubeflow.${PRIMARY_DOMAIN:-company.com}"
    log "INFO" "Kubeflow Dashboard URL: $kubeflow_url"
    
    log "INFO" "Installation verification completed"
}

# Create sample ML pipeline
create_sample_pipeline() {
    log "INFO" "Creating sample ML pipeline..."
    
    cat <<EOF > /tmp/sample-ml-pipeline.yaml
apiVersion: argoproj.io/v1alpha1
kind: Workflow
metadata:
  generateName: sample-ml-pipeline-
  namespace: kubeflow
spec:
  entrypoint: ml-pipeline
  serviceAccountName: ml-pipeline-runner
  volumes:
  - name: workspace
    emptyDir: {}
  templates:
  - name: ml-pipeline
    dag:
      tasks:
      - name: data-preparation
        template: data-prep
      - name: model-training
        template: train-model
        dependencies: [data-preparation]
      - name: model-evaluation
        template: evaluate-model
        dependencies: [model-training]
  
  - name: data-prep
    container:
      image: python:3.9-slim
      command: [python]
      args: ["-c", "print('Data preparation completed')"]
      volumeMounts:
      - name: workspace
        mountPath: /workspace
  
  - name: train-model
    container:
      image: python:3.9-slim
      command: [python]
      args: ["-c", "print('Model training completed')"]
      volumeMounts:
      - name: workspace
        mountPath: /workspace
  
  - name: evaluate-model
    container:
      image: python:3.9-slim
      command: [python]
      args: ["-c", "print('Model evaluation completed')"]
      volumeMounts:
      - name: workspace
        mountPath: /workspace
EOF

    kubectl apply -f /tmp/sample-ml-pipeline.yaml
    rm -f /tmp/sample-ml-pipeline.yaml
    
    log "INFO" "Sample ML pipeline created"
}

# Main installation function
main() {
    log "INFO" "=== Starting Kubeflow Setup ==="
    log "INFO" "Kubeflow Version: $KUBEFLOW_VERSION"
    log "INFO" "Target Namespace: $NAMESPACE"
    log "INFO" "EKS Cluster: $EKS_CLUSTER_NAME"
    
    # Run installation steps
    check_prerequisites
    create_gpu_node_group
    install_nvidia_plugin
    install_istio
    install_kubeflow_pipelines
    install_kubeflow_dashboard
    install_kubeflow_notebooks
    setup_kubeflow_ingress
    create_ml_storage
    create_service_accounts
    create_sample_pipeline
    
    # Verify installation
    verify_installation
    
    log "INFO" "=== Kubeflow Setup Completed Successfully ==="
    log "INFO" "Access the Kubeflow Dashboard at: https://kubeflow.${PRIMARY_DOMAIN:-company.com}"
    log "INFO" "Use 'kubectl port-forward -n istio-system svc/istio-ingressgateway 8080:80' for local access"
}

# Help function
show_help() {
    cat << EOF
Usage: $0 [OPTIONS]

Kubeflow Setup Script for E-commerce Analytics ML Platform

OPTIONS:
    -v, --version VERSION   Kubeflow version to install (default: $KUBEFLOW_VERSION)
    -n, --namespace NS      Target namespace (default: $NAMESPACE)
    -c, --cluster NAME      EKS cluster name (default: $EKS_CLUSTER_NAME)
    -r, --region REGION     AWS region (default: $AWS_REGION)
    -l, --log FILE          Log file path
    -h, --help              Show this help message

EXAMPLES:
    $0                      # Install with defaults
    $0 -v 1.7.0            # Install specific version
    $0 -n ml-platform      # Install to custom namespace

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -v|--version)
            KUBEFLOW_VERSION="$2"
            shift 2
            ;;
        -n|--namespace)
            NAMESPACE="$2"
            shift 2
            ;;
        -c|--cluster)
            EKS_CLUSTER_NAME="$2"
            shift 2
            ;;
        -r|--region)
            AWS_REGION="$2"
            shift 2
            ;;
        -l|--log)
            LOG_FILE="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Execute main function
main "$@"