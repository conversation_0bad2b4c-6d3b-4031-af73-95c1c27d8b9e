#!/usr/bin/env python3
"""
ML Model Monitoring and Drift Detection System
Monitors model performance, data drift, and concept drift in production
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import os
import sys
from typing import Dict, List, Tuple, Optional, Any
import json
import boto3
import redis
from sqlalchemy import create_engine
import mlflow
import mlflow.tracking
from mlflow.tracking import MlflowClient
from scipy import stats
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import joblib
import matplotlib.pyplot as plt
import seaborn as sns
from dataclasses import dataclass
import warnings
warnings.filterwarnings('ignore')

# Configuration
@dataclass
class MonitoringConfig:
    # MLflow configuration
    mlflow_tracking_uri: str = os.getenv('MLFLOW_TRACKING_URI', 'http://mlflow-server.mlflow:5000')
    
    # Database connections
    db_host: str = os.getenv('DB_HOST', 'analytics-db.cluster-xyz.us-east-1.rds.amazonaws.com')
    db_name: str = os.getenv('DB_NAME', 'ecommerce_analytics')
    db_user: str = os.getenv('DB_USER', 'analytics_user')
    db_password: str = os.getenv('DB_PASSWORD', 'analytics_password')
    
    # Redis connection
    redis_host: str = os.getenv('REDIS_HOST', 'analytics-redis.abc123.cache.amazonaws.com')
    redis_port: int = int(os.getenv('REDIS_PORT', '6379'))
    
    # S3 configuration
    s3_bucket: str = os.getenv('S3_BUCKET', 'ecommerce-analytics-ml-monitoring')
    
    # Monitoring parameters
    drift_threshold: float = float(os.getenv('DRIFT_THRESHOLD', '0.05'))
    performance_threshold: float = float(os.getenv('PERFORMANCE_THRESHOLD', '0.1'))
    monitoring_window_days: int = int(os.getenv('MONITORING_WINDOW_DAYS', '7'))
    
    # Alert configuration
    slack_webhook: str = os.getenv('SLACK_WEBHOOK', '')
    sns_topic_arn: str = os.getenv('SNS_TOPIC_ARN', '')

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ModelMonitor:
    def __init__(self, config: MonitoringConfig):
        self.config = config
        self.db_engine = self._create_db_engine()
        self.redis_client = self._create_redis_client()
        self.s3_client = boto3.client('s3')
        self.sns_client = boto3.client('sns')
        
        # Initialize MLflow
        mlflow.set_tracking_uri(config.mlflow_tracking_uri)
        self.mlflow_client = MlflowClient()
        
    def _create_db_engine(self):
        """Create database engine for PostgreSQL"""
        connection_string = f"postgresql://{self.config.db_user}:{self.config.db_password}@{self.config.db_host}/{self.config.db_name}"
        return create_engine(connection_string)
    
    def _create_redis_client(self):
        """Create Redis client"""
        return redis.Redis(
            host=self.config.redis_host,
            port=self.config.redis_port,
            decode_responses=True
        )
    
    def get_production_models(self) -> List[Dict]:
        """Get list of models currently in production"""
        logger.info("Fetching production models...")
        
        production_models = []
        
        # Get all registered models
        registered_models = self.mlflow_client.search_registered_models()
        
        for model in registered_models:
            # Get production versions
            production_versions = self.mlflow_client.get_latest_versions(
                model.name, 
                stages=["Production"]
            )
            
            for version in production_versions:
                model_info = {
                    'name': model.name,
                    'version': version.version,
                    'run_id': version.run_id,
                    'stage': version.current_stage,
                    'creation_timestamp': version.creation_timestamp,
                    'last_updated_timestamp': version.last_updated_timestamp
                }
                production_models.append(model_info)
        
        logger.info(f"Found {len(production_models)} production models")
        return production_models
    
    def get_recent_predictions(self, model_name: str, days: int = 7) -> pd.DataFrame:
        """Get recent predictions from the database"""
        logger.info(f"Fetching recent predictions for {model_name}...")
        
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        query = """
            SELECT 
                prediction_id,
                model_name,
                model_version,
                prediction_timestamp,
                features,
                prediction,
                prediction_probability,
                actual_outcome,
                user_id,
                prediction_context
            FROM ml_predictions 
            WHERE model_name = %s 
                AND prediction_timestamp >= %s 
                AND prediction_timestamp <= %s
            ORDER BY prediction_timestamp DESC
        """
        
        predictions = pd.read_sql(
            query,
            self.db_engine,
            params=[model_name, start_date, end_date]
        )
        
        logger.info(f"Retrieved {len(predictions)} predictions for {model_name}")
        return predictions
    
    def get_training_data_stats(self, model_name: str, run_id: str) -> Dict:
        """Get training data statistics from MLflow"""
        logger.info(f"Fetching training data stats for {model_name}...")
        
        try:
            # Get run information
            run = self.mlflow_client.get_run(run_id)
            
            # Extract training data statistics from metrics and tags
            training_stats = {
                'training_samples': run.data.metrics.get('training_samples', 0),
                'features_count': run.data.metrics.get('features_count', 0),
                'target_mean': run.data.metrics.get('target_mean', 0),
                'target_std': run.data.metrics.get('target_std', 0),
                'feature_means': {},
                'feature_stds': {}
            }
            
            # Extract feature statistics from metrics
            for metric_name, metric_value in run.data.metrics.items():
                if metric_name.startswith('feature_mean_'):
                    feature_name = metric_name.replace('feature_mean_', '')
                    training_stats['feature_means'][feature_name] = metric_value
                elif metric_name.startswith('feature_std_'):
                    feature_name = metric_name.replace('feature_std_', '')
                    training_stats['feature_stds'][feature_name] = metric_value
            
            return training_stats
            
        except Exception as e:
            logger.error(f"Error fetching training stats: {str(e)}")
            return {}
    
    def detect_data_drift(self, predictions_df: pd.DataFrame, training_stats: Dict) -> Dict:
        """Detect data drift using statistical tests"""
        logger.info("Detecting data drift...")
        
        drift_results = {
            'has_drift': False,
            'drift_features': [],
            'drift_scores': {},
            'test_results': {}
        }
        
        if predictions_df.empty or not training_stats:
            return drift_results
        
        try:
            # Parse features from JSON
            features_list = []
            for _, row in predictions_df.iterrows():
                if pd.notna(row['features']):
                    try:
                        features = json.loads(row['features'])
                        features_list.append(features)
                    except (json.JSONDecodeError, TypeError):
                        continue
            
            if not features_list:
                return drift_results
            
            current_features_df = pd.DataFrame(features_list)
            
            # Perform drift detection for each feature
            for feature_name in current_features_df.columns:
                if feature_name in training_stats.get('feature_means', {}):
                    
                    current_values = current_features_df[feature_name].dropna()
                    
                    if len(current_values) < 30:  # Skip if too few samples
                        continue
                    
                    # Get training statistics
                    training_mean = training_stats['feature_means'][feature_name]
                    training_std = training_stats['feature_stds'].get(feature_name, 1.0)
                    
                    # Kolmogorov-Smirnov test for distribution drift
                    training_distribution = np.random.normal(training_mean, training_std, len(current_values))
                    ks_statistic, ks_p_value = stats.ks_2samp(training_distribution, current_values)
                    
                    # Population Stability Index (PSI)
                    psi_score = self._calculate_psi(training_distribution, current_values)
                    
                    # Store results
                    drift_results['drift_scores'][feature_name] = {
                        'ks_statistic': ks_statistic,
                        'ks_p_value': ks_p_value,
                        'psi_score': psi_score,
                        'current_mean': current_values.mean(),
                        'training_mean': training_mean,
                        'mean_shift': abs(current_values.mean() - training_mean) / training_std
                    }
                    
                    # Check for significant drift
                    if ks_p_value < self.config.drift_threshold or psi_score > 0.2:
                        drift_results['has_drift'] = True
                        drift_results['drift_features'].append(feature_name)
                        
                        drift_results['test_results'][feature_name] = {
                            'drift_detected': True,
                            'ks_p_value': ks_p_value,
                            'psi_score': psi_score
                        }
            
            logger.info(f"Drift detection completed. Drift detected in {len(drift_results['drift_features'])} features")
            
        except Exception as e:
            logger.error(f"Error in drift detection: {str(e)}")
        
        return drift_results
    
    def _calculate_psi(self, expected: np.array, actual: np.array, buckets: int = 10) -> float:
        """Calculate Population Stability Index (PSI)"""
        try:
            # Create bins based on expected distribution
            bin_edges = np.histogram_bin_edges(expected, bins=buckets)
            
            # Calculate distributions
            expected_counts, _ = np.histogram(expected, bins=bin_edges)
            actual_counts, _ = np.histogram(actual, bins=bin_edges)
            
            # Add small constant to avoid division by zero
            expected_percents = (expected_counts + 1e-6) / (len(expected) + buckets * 1e-6)
            actual_percents = (actual_counts + 1e-6) / (len(actual) + buckets * 1e-6)
            
            # Calculate PSI
            psi = np.sum((actual_percents - expected_percents) * np.log(actual_percents / expected_percents))
            
            return float(psi)
            
        except Exception as e:
            logger.error(f"Error calculating PSI: {str(e)}")
            return 0.0
    
    def monitor_model_performance(self, predictions_df: pd.DataFrame, model_name: str) -> Dict:
        """Monitor model performance metrics"""
        logger.info(f"Monitoring performance for {model_name}...")
        
        performance_results = {
            'has_performance_degradation': False,
            'metrics': {},
            'comparison': {}
        }
        
        if predictions_df.empty:
            return performance_results
        
        try:
            # Filter predictions with actual outcomes
            labeled_predictions = predictions_df[predictions_df['actual_outcome'].notna()]
            
            if len(labeled_predictions) < 10:  # Too few labeled samples
                logger.warning(f"Insufficient labeled data for {model_name}: {len(labeled_predictions)} samples")
                return performance_results
            
            predictions = labeled_predictions['prediction'].values
            actuals = labeled_predictions['actual_outcome'].values
            
            # Determine if it's classification or regression
            is_classification = self._is_classification_model(predictions, actuals)
            
            if is_classification:
                metrics = self._calculate_classification_metrics(predictions, actuals, labeled_predictions)
            else:
                metrics = self._calculate_regression_metrics(predictions, actuals)
            
            performance_results['metrics'] = metrics
            
            # Compare with baseline metrics from training
            baseline_metrics = self._get_baseline_metrics(model_name)
            if baseline_metrics:
                comparison = self._compare_metrics(metrics, baseline_metrics)
                performance_results['comparison'] = comparison
                
                # Check for significant degradation
                if comparison.get('significant_degradation', False):
                    performance_results['has_performance_degradation'] = True
            
            logger.info(f"Performance monitoring completed for {model_name}")
            
        except Exception as e:
            logger.error(f"Error in performance monitoring: {str(e)}")
        
        return performance_results
    
    def _is_classification_model(self, predictions: np.array, actuals: np.array) -> bool:
        """Determine if this is a classification or regression model"""
        # Check if predictions and actuals are binary/categorical
        unique_predictions = len(np.unique(predictions))
        unique_actuals = len(np.unique(actuals))
        
        # If both have few unique values, likely classification
        return unique_predictions <= 10 and unique_actuals <= 10
    
    def _calculate_classification_metrics(self, predictions: np.array, actuals: np.array, df: pd.DataFrame) -> Dict:
        """Calculate classification metrics"""
        metrics = {}
        
        try:
            # Convert to binary if needed
            if len(np.unique(actuals)) == 2:
                # Binary classification
                metrics['accuracy'] = accuracy_score(actuals, predictions)
                metrics['precision'] = precision_score(actuals, predictions, average='binary', zero_division=0)
                metrics['recall'] = recall_score(actuals, predictions, average='binary', zero_division=0)
                metrics['f1_score'] = f1_score(actuals, predictions, average='binary', zero_division=0)
                
                # AUC-ROC if prediction probabilities are available
                if 'prediction_probability' in df.columns:
                    proba = df['prediction_probability'].dropna().values
                    if len(proba) == len(actuals):
                        metrics['auc_roc'] = roc_auc_score(actuals, proba)
            else:
                # Multi-class classification
                metrics['accuracy'] = accuracy_score(actuals, predictions)
                metrics['precision'] = precision_score(actuals, predictions, average='weighted', zero_division=0)
                metrics['recall'] = recall_score(actuals, predictions, average='weighted', zero_division=0)
                metrics['f1_score'] = f1_score(actuals, predictions, average='weighted', zero_division=0)
            
        except Exception as e:
            logger.error(f"Error calculating classification metrics: {str(e)}")
        
        return metrics
    
    def _calculate_regression_metrics(self, predictions: np.array, actuals: np.array) -> Dict:
        """Calculate regression metrics"""
        metrics = {}
        
        try:
            metrics['mse'] = mean_squared_error(actuals, predictions)
            metrics['rmse'] = np.sqrt(metrics['mse'])
            metrics['mae'] = mean_absolute_error(actuals, predictions)
            metrics['r2_score'] = r2_score(actuals, predictions)
            
            # Mean Absolute Percentage Error
            non_zero_actuals = actuals[actuals != 0]
            non_zero_predictions = predictions[actuals != 0]
            if len(non_zero_actuals) > 0:
                metrics['mape'] = np.mean(np.abs((non_zero_actuals - non_zero_predictions) / non_zero_actuals)) * 100
            
        except Exception as e:
            logger.error(f"Error calculating regression metrics: {str(e)}")
        
        return metrics
    
    def _get_baseline_metrics(self, model_name: str) -> Dict:
        """Get baseline metrics from training/validation"""
        try:
            # Get the latest production model
            production_versions = self.mlflow_client.get_latest_versions(model_name, stages=["Production"])
            
            if not production_versions:
                return {}
            
            run_id = production_versions[0].run_id
            run = self.mlflow_client.get_run(run_id)
            
            # Extract validation metrics
            baseline_metrics = {}
            for metric_name, metric_value in run.data.metrics.items():
                if metric_name.startswith('val_') or metric_name.startswith('test_'):
                    clean_name = metric_name.replace('val_', '').replace('test_', '')
                    baseline_metrics[clean_name] = metric_value
            
            return baseline_metrics
            
        except Exception as e:
            logger.error(f"Error getting baseline metrics: {str(e)}")
            return {}
    
    def _compare_metrics(self, current_metrics: Dict, baseline_metrics: Dict) -> Dict:
        """Compare current metrics with baseline"""
        comparison = {
            'significant_degradation': False,
            'metric_changes': {}
        }
        
        for metric_name, current_value in current_metrics.items():
            if metric_name in baseline_metrics:
                baseline_value = baseline_metrics[metric_name]
                
                # Calculate percentage change
                if baseline_value != 0:
                    pct_change = ((current_value - baseline_value) / baseline_value) * 100
                else:
                    pct_change = 0
                
                comparison['metric_changes'][metric_name] = {
                    'current': current_value,
                    'baseline': baseline_value,
                    'change_pct': pct_change,
                    'degraded': self._is_metric_degraded(metric_name, pct_change)
                }
                
                # Check for significant degradation
                if comparison['metric_changes'][metric_name]['degraded']:
                    if abs(pct_change) > self.config.performance_threshold * 100:
                        comparison['significant_degradation'] = True
        
        return comparison
    
    def _is_metric_degraded(self, metric_name: str, pct_change: float) -> bool:
        """Determine if a metric has degraded based on its type"""
        # Metrics where higher is better
        higher_is_better = ['accuracy', 'precision', 'recall', 'f1_score', 'auc_roc', 'r2_score']
        
        # Metrics where lower is better
        lower_is_better = ['mse', 'rmse', 'mae', 'mape', 'loss']
        
        if metric_name in higher_is_better:
            return pct_change < 0  # Degraded if decreased
        elif metric_name in lower_is_better:
            return pct_change > 0  # Degraded if increased
        else:
            return False  # Unknown metric, assume no degradation
    
    def detect_concept_drift(self, predictions_df: pd.DataFrame) -> Dict:
        """Detect concept drift by analyzing prediction patterns over time"""
        logger.info("Detecting concept drift...")
        
        concept_drift_results = {
            'has_concept_drift': False,
            'trend_analysis': {},
            'pattern_changes': {}
        }
        
        if len(predictions_df) < 100:  # Need sufficient data
            return concept_drift_results
        
        try:
            # Sort by timestamp
            predictions_df = predictions_df.sort_values('prediction_timestamp')
            
            # Analyze prediction distribution over time
            predictions_df['prediction_date'] = pd.to_datetime(predictions_df['prediction_timestamp']).dt.date
            daily_stats = predictions_df.groupby('prediction_date')['prediction'].agg(['mean', 'std', 'count'])
            
            if len(daily_stats) < 3:  # Need at least 3 days
                return concept_drift_results
            
            # Check for trends in prediction means
            trend_slope, trend_p_value = stats.linregress(range(len(daily_stats)), daily_stats['mean'])[:2]
            
            concept_drift_results['trend_analysis'] = {
                'slope': trend_slope,
                'p_value': trend_p_value,
                'has_significant_trend': trend_p_value < 0.05
            }
            
            # Check for changes in prediction variance
            early_period = daily_stats[:len(daily_stats)//2]['std'].mean()
            late_period = daily_stats[len(daily_stats)//2:]['std'].mean()
            
            variance_change = abs(late_period - early_period) / early_period if early_period > 0 else 0
            
            concept_drift_results['pattern_changes'] = {
                'variance_change': variance_change,
                'variance_change_significant': variance_change > 0.5
            }
            
            # Overall concept drift detection
            if (concept_drift_results['trend_analysis']['has_significant_trend'] or 
                concept_drift_results['pattern_changes']['variance_change_significant']):
                concept_drift_results['has_concept_drift'] = True
            
            logger.info("Concept drift detection completed")
            
        except Exception as e:
            logger.error(f"Error in concept drift detection: {str(e)}")
        
        return concept_drift_results
    
    def generate_monitoring_report(self, model_name: str) -> Dict:
        """Generate comprehensive monitoring report for a model"""
        logger.info(f"Generating monitoring report for {model_name}...")
        
        # Get recent predictions
        predictions_df = self.get_recent_predictions(model_name, self.config.monitoring_window_days)
        
        # Get training data stats
        production_models = [m for m in self.get_production_models() if m['name'] == model_name]
        training_stats = {}
        if production_models:
            training_stats = self.get_training_data_stats(model_name, production_models[0]['run_id'])
        
        # Run all monitoring checks
        drift_results = self.detect_data_drift(predictions_df, training_stats)
        performance_results = self.monitor_model_performance(predictions_df, model_name)
        concept_drift_results = self.detect_concept_drift(predictions_df)
        
        report = {
            'model_name': model_name,
            'report_timestamp': datetime.now().isoformat(),
            'monitoring_period_days': self.config.monitoring_window_days,
            'total_predictions': len(predictions_df),
            'labeled_predictions': len(predictions_df[predictions_df['actual_outcome'].notna()]),
            'data_drift': drift_results,
            'performance': performance_results,
            'concept_drift': concept_drift_results,
            'overall_health': self._assess_overall_health(drift_results, performance_results, concept_drift_results)
        }
        
        return report
    
    def _assess_overall_health(self, drift_results: Dict, performance_results: Dict, concept_drift_results: Dict) -> Dict:
        """Assess overall model health"""
        issues = []
        severity = 'healthy'
        
        if drift_results.get('has_drift', False):
            issues.append(f"Data drift detected in {len(drift_results.get('drift_features', []))} features")
            severity = 'warning'
        
        if performance_results.get('has_performance_degradation', False):
            issues.append("Performance degradation detected")
            severity = 'critical'
        
        if concept_drift_results.get('has_concept_drift', False):
            issues.append("Concept drift detected")
            if severity != 'critical':
                severity = 'warning'
        
        return {
            'status': severity,
            'issues': issues,
            'requires_attention': severity in ['warning', 'critical']
        }
    
    def send_alert(self, report: Dict):
        """Send alert based on monitoring report"""
        if not report['overall_health']['requires_attention']:
            return
        
        alert_message = self._create_alert_message(report)
        
        # Send Slack notification
        if self.config.slack_webhook:
            self._send_slack_alert(alert_message, report['overall_health']['status'])
        
        # Send SNS notification
        if self.config.sns_topic_arn:
            self._send_sns_alert(alert_message, report)
        
        logger.info(f"Alert sent for model {report['model_name']}")
    
    def _create_alert_message(self, report: Dict) -> str:
        """Create alert message"""
        model_name = report['model_name']
        status = report['overall_health']['status']
        issues = report['overall_health']['issues']
        
        message = f"🚨 Model Alert: {model_name}\n"
        message += f"Status: {status.upper()}\n"
        message += f"Issues detected:\n"
        
        for issue in issues:
            message += f"• {issue}\n"
        
        message += f"\nMonitoring period: {report['monitoring_period_days']} days\n"
        message += f"Total predictions: {report['total_predictions']}\n"
        message += f"Report timestamp: {report['report_timestamp']}\n"
        
        return message
    
    def _send_slack_alert(self, message: str, severity: str):
        """Send Slack alert"""
        try:
            import requests
            
            color_map = {
                'warning': '#ff9500',
                'critical': '#ff0000'
            }
            
            payload = {
                "attachments": [{
                    "color": color_map.get(severity, '#ff9500'),
                    "title": "ML Model Monitoring Alert",
                    "text": message,
                    "footer": "ML Monitoring System",
                    "ts": int(datetime.now().timestamp())
                }]
            }
            
            response = requests.post(self.config.slack_webhook, json=payload)
            response.raise_for_status()
            
        except Exception as e:
            logger.error(f"Error sending Slack alert: {str(e)}")
    
    def _send_sns_alert(self, message: str, report: Dict):
        """Send SNS alert"""
        try:
            subject = f"ML Model Alert: {report['model_name']} - {report['overall_health']['status'].upper()}"
            
            self.sns_client.publish(
                TopicArn=self.config.sns_topic_arn,
                Subject=subject,
                Message=message
            )
            
        except Exception as e:
            logger.error(f"Error sending SNS alert: {str(e)}")
    
    def save_report(self, report: Dict):
        """Save monitoring report to S3"""
        try:
            report_key = f"monitoring-reports/{report['model_name']}/{datetime.now().strftime('%Y/%m/%d')}/report_{int(datetime.now().timestamp())}.json"
            
            self.s3_client.put_object(
                Bucket=self.config.s3_bucket,
                Key=report_key,
                Body=json.dumps(report, indent=2),
                ContentType='application/json'
            )
            
            logger.info(f"Report saved to s3://{self.config.s3_bucket}/{report_key}")
            
        except Exception as e:
            logger.error(f"Error saving report: {str(e)}")
    
    def run_monitoring_for_all_models(self):
        """Run monitoring for all production models"""
        logger.info("Starting monitoring for all production models...")
        
        production_models = self.get_production_models()
        
        for model_info in production_models:
            model_name = model_info['name']
            
            try:
                logger.info(f"Monitoring model: {model_name}")
                
                # Generate monitoring report
                report = self.generate_monitoring_report(model_name)
                
                # Save report
                self.save_report(report)
                
                # Send alerts if needed
                self.send_alert(report)
                
                # Log to MLflow
                with mlflow.start_run(run_name=f"monitoring_{model_name}_{int(datetime.now().timestamp())}"):
                    mlflow.log_metrics({
                        f"{model_name}_total_predictions": report['total_predictions'],
                        f"{model_name}_drift_features": len(report['data_drift'].get('drift_features', [])),
                        f"{model_name}_has_performance_degradation": int(report['performance'].get('has_performance_degradation', False)),
                        f"{model_name}_has_concept_drift": int(report['concept_drift'].get('has_concept_drift', False))
                    })
                    
                    mlflow.log_dict(report, f"monitoring_report_{model_name}.json")
                
                logger.info(f"Monitoring completed for {model_name}")
                
            except Exception as e:
                logger.error(f"Error monitoring model {model_name}: {str(e)}")
                continue
        
        logger.info("Monitoring completed for all models")

def main():
    """Main function to run model monitoring"""
    config = MonitoringConfig()
    monitor = ModelMonitor(config)
    
    try:
        # Run monitoring for all production models
        monitor.run_monitoring_for_all_models()
        
        logger.info("Model monitoring completed successfully")
        
    except Exception as e:
        logger.error(f"Model monitoring failed: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()