apiVersion: v1
kind: Namespace
metadata:
  name: feast
  labels:
    app.kubernetes.io/name: feast
    app.kubernetes.io/component: feature-store
---
apiVersion: v1
kind: Secret
metadata:
  name: feast-secrets
  namespace: feast
type: Opaque
data:
  # Base64 encoded values - replace with actual values
  postgres-password: ZmVhc3QtcGFzc3dvcmQ=  # feast-password
  redis-password: cmVkaXMtcGFzc3dvcmQ=  # redis-password
  s3-access-key: YWNjZXNzLWtleQ==  # access-key
  s3-secret-key: c2VjcmV0LWtleQ==  # secret-key
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: feast-config
  namespace: feast
data:
  feature_store.yaml: |
    project: ecommerce_analytics
    registry: s3://ecommerce-analytics-feast/registry.db
    provider: aws
    offline_store:
      type: snowflake.offline
      config_path: "/opt/feast/snowflake_config.yaml"
    online_store:
      type: redis
      connection_string: "redis://:$(REDIS_PASSWORD)@redis:6379"
    entity_key_serialization_version: 2
    
  snowflake_config.yaml: |
    offline_store:
      type: snowflake.offline
      account: ecommerce-analytics
      user: feast_user
      password: feast_password
      role: FEAST_ROLE
      warehouse: FEAST_WH
      database: ECOMMERCE_ANALYTICS
      schema: FEAST
    
  features.py: |
    from datetime import timedelta
    from feast import Entity, FeatureService, FeatureView, Field, PushSource, RequestSource
    from feast.data_source import BigQuerySource
    from feast.types import Float32, Float64, Int64, String, UnixTimestamp
    
    # Define entities
    user = Entity(
        name="user_id",
        description="User identifier",
    )
    
    product = Entity(
        name="product_id", 
        description="Product identifier",
    )
    
    # User features from analytics events
    user_analytics_source = BigQuerySource(
        name="user_analytics_source",
        table="ecommerce_analytics.user_analytics_features",
        timestamp_field="event_timestamp",
    )
    
    user_features = FeatureView(
        name="user_features",
        entities=[user],
        ttl=timedelta(days=7),
        schema=[
            Field(name="page_views_7d", dtype=Int64),
            Field(name="sessions_7d", dtype=Int64),
            Field(name="total_purchase_amount_7d", dtype=Float64),
            Field(name="avg_session_duration_7d", dtype=Float64),
            Field(name="bounce_rate_7d", dtype=Float64),
            Field(name="cart_abandonment_rate_7d", dtype=Float64),
            Field(name="last_purchase_days_ago", dtype=Int64),
            Field(name="favorite_category", dtype=String),
            Field(name="device_type", dtype=String),
            Field(name="traffic_source", dtype=String),
        ],
        source=user_analytics_source,
    )
    
    # Product features
    product_analytics_source = BigQuerySource(
        name="product_analytics_source",
        table="ecommerce_analytics.product_analytics_features",
        timestamp_field="event_timestamp",
    )
    
    product_features = FeatureView(
        name="product_features",
        entities=[product],
        ttl=timedelta(days=1),
        schema=[
            Field(name="views_7d", dtype=Int64),
            Field(name="purchases_7d", dtype=Int64),
            Field(name="cart_adds_7d", dtype=Int64),
            Field(name="avg_rating", dtype=Float32),
            Field(name="price", dtype=Float64),
            Field(name="discount_percentage", dtype=Float32),
            Field(name="inventory_level", dtype=Int64),
            Field(name="category", dtype=String),
            Field(name="brand", dtype=String),
            Field(name="conversion_rate_7d", dtype=Float32),
        ],
        source=product_analytics_source,
    )
    
    # Real-time user activity features
    user_activity_push_source = PushSource(
        name="user_activity_push_source",
        batch_source=user_analytics_source,
    )
    
    user_realtime_features = FeatureView(
        name="user_realtime_features",
        entities=[user],
        ttl=timedelta(minutes=30),
        schema=[
            Field(name="current_session_page_views", dtype=Int64),
            Field(name="current_session_duration", dtype=Int64),
            Field(name="items_in_cart", dtype=Int64),
            Field(name="last_page_viewed", dtype=String),
        ],
        source=user_activity_push_source,
    )
    
    # Feature services for ML models
    churn_prediction_fs = FeatureService(
        name="churn_prediction_v1",
        features=[
            user_features[["page_views_7d", "sessions_7d", "total_purchase_amount_7d", 
                          "avg_session_duration_7d", "bounce_rate_7d", "last_purchase_days_ago"]],
            user_realtime_features[["current_session_page_views", "current_session_duration"]],
        ],
    )
    
    recommendation_fs = FeatureService(
        name="product_recommendation_v1",
        features=[
            user_features[["favorite_category", "total_purchase_amount_7d", "device_type"]],
            product_features[["views_7d", "avg_rating", "price", "category", "conversion_rate_7d"]],
        ],
    )
    
    price_optimization_fs = FeatureService(
        name="price_optimization_v1", 
        features=[
            product_features[["views_7d", "purchases_7d", "avg_rating", "price", 
                            "discount_percentage", "inventory_level", "conversion_rate_7d"]],
        ],
    )
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres
  namespace: feast
  labels:
    app: postgres
    component: registry-database
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
      - name: postgres
        image: postgres:14-alpine
        ports:
        - containerPort: 5432
        env:
        - name: POSTGRES_DB
          value: feast
        - name: POSTGRES_USER
          value: feast
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: feast-secrets
              key: postgres-password
        - name: PGDATA
          value: /var/lib/postgresql/data/pgdata
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - feast
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - feast
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: postgres-storage
        persistentVolumeClaim:
          claimName: postgres-pvc
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: feast
  labels:
    app: redis
    component: online-store
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        ports:
        - containerPort: 6379
        command:
        - redis-server
        - --requirepass
        - $(REDIS_PASSWORD)
        - --maxmemory
        - 2gb
        - --maxmemory-policy
        - allkeys-lru
        env:
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: feast-secrets
              key: redis-password
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          exec:
            command:
            - redis-cli
            - -a
            - $(REDIS_PASSWORD)
            - ping
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - redis-cli
            - -a
            - $(REDIS_PASSWORD)
            - ping
          initialDelaySeconds: 5
          periodSeconds: 5
        volumeMounts:
        - name: redis-storage
          mountPath: /data
      volumes:
      - name: redis-storage
        persistentVolumeClaim:
          claimName: redis-pvc
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: feast-serving
  namespace: feast
  labels:
    app: feast-serving
    component: feature-server
spec:
  replicas: 3
  selector:
    matchLabels:
      app: feast-serving
  template:
    metadata:
      labels:
        app: feast-serving
    spec:
      serviceAccountName: feast-service-account
      containers:
      - name: feast-serving
        image: feastdev/feature-server:0.34.0
        ports:
        - containerPort: 6566
          name: http
        - containerPort: 6567
          name: grpc
        command:
        - feast
        - serve
        env:
        - name: FEAST_USAGE
          value: "false"
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: feast-secrets
              key: redis-password
        - name: AWS_ACCESS_KEY_ID
          valueFrom:
            secretKeyRef:
              name: feast-secrets
              key: s3-access-key
        - name: AWS_SECRET_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: feast-secrets
              key: s3-secret-key
        - name: AWS_DEFAULT_REGION
          value: us-east-1
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 6566
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /health
            port: 6566
          initialDelaySeconds: 30
          periodSeconds: 10
        volumeMounts:
        - name: feast-config
          mountPath: /feature_repo/feature_store.yaml
          subPath: feature_store.yaml
          readOnly: true
        - name: feast-config
          mountPath: /feature_repo/features.py
          subPath: features.py
          readOnly: true
      volumes:
      - name: feast-config
        configMap:
          name: feast-config
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgres-pvc
  namespace: feast
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: ml-high-performance
  resources:
    requests:
      storage: 10Gi
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: redis-pvc
  namespace: feast
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: ml-high-performance
  resources:
    requests:
      storage: 20Gi
---
apiVersion: v1
kind: Service
metadata:
  name: postgres
  namespace: feast
  labels:
    app: postgres
spec:
  type: ClusterIP
  ports:
  - port: 5432
    targetPort: 5432
    protocol: TCP
  selector:
    app: postgres
---
apiVersion: v1
kind: Service
metadata:
  name: redis
  namespace: feast
  labels:
    app: redis
spec:
  type: ClusterIP
  ports:
  - port: 6379
    targetPort: 6379
    protocol: TCP
  selector:
    app: redis
---
apiVersion: v1
kind: Service
metadata:
  name: feast-serving
  namespace: feast
  labels:
    app: feast-serving
spec:
  type: ClusterIP
  ports:
  - port: 6566
    targetPort: 6566
    protocol: TCP
    name: http
  - port: 6567
    targetPort: 6567
    protocol: TCP
    name: grpc
  selector:
    app: feast-serving
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: feast-service-account
  namespace: feast
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::ACCOUNT_ID:role/FeastServiceRole
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: feast-ingress
  namespace: feast
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/auth-type: basic
    nginx.ingress.kubernetes.io/auth-secret: feast-basic-auth
    nginx.ingress.kubernetes.io/auth-realm: 'Feast Authentication Required'
spec:
  tls:
  - hosts:
    - feast.company.com
    secretName: feast-tls
  rules:
  - host: feast.company.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: feast-serving
            port:
              number: 6566
---
apiVersion: v1
kind: Secret
metadata:
  name: feast-basic-auth
  namespace: feast
type: Opaque
data:
  auth: ZmVhc3Q6JGFwcjEkZU5OMFU3SDMkYmFkbHc5THBtc1l2YWRWbTRZaW9KMQ==  # feast:feast123
---
apiVersion: batch/v1
kind: Job
metadata:
  name: feast-repo-init
  namespace: feast
  labels:
    app: feast-init
spec:
  template:
    spec:
      restartPolicy: OnFailure
      serviceAccountName: feast-service-account
      containers:
      - name: feast-init
        image: feastdev/feast:0.34.0
        command: ["/bin/bash"]
        args:
        - -c
        - |
          set -e
          cd /feature_repo
          echo "Initializing Feast repository..."
          feast init --minimal
          cp /config/feature_store.yaml .
          cp /config/features.py .
          echo "Applying feature definitions..."
          feast apply
          echo "Materializing initial features..."
          feast materialize-incremental $(date -d "7 days ago" +%Y-%m-%d) $(date +%Y-%m-%d)
          echo "Feast repository initialization completed"
        env:
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: feast-secrets
              key: redis-password
        - name: AWS_ACCESS_KEY_ID
          valueFrom:
            secretKeyRef:
              name: feast-secrets
              key: s3-access-key
        - name: AWS_SECRET_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: feast-secrets
              key: s3-secret-key
        - name: AWS_DEFAULT_REGION
          value: us-east-1
        volumeMounts:
        - name: feast-config
          mountPath: /config
        - name: feast-repo
          mountPath: /feature_repo
      volumes:
      - name: feast-config
        configMap:
          name: feast-config
      - name: feast-repo
        emptyDir: {}
---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: feast-materialization
  namespace: feast
spec:
  schedule: "0 */6 * * *"  # Every 6 hours
  jobTemplate:
    spec:
      template:
        spec:
          restartPolicy: OnFailure
          serviceAccountName: feast-service-account
          containers:
          - name: feast-materialize
            image: feastdev/feast:0.34.0
            command: ["/bin/bash"]
            args:
            - -c
            - |
              set -e
              cd /feature_repo
              cp /config/feature_store.yaml .
              cp /config/features.py .
              echo "Running incremental materialization..."
              feast materialize-incremental $(date -d "1 day ago" +%Y-%m-%d) $(date +%Y-%m-%d)
              echo "Materialization completed"
            env:
            - name: REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: feast-secrets
                  key: redis-password
            - name: AWS_ACCESS_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: feast-secrets
                  key: s3-access-key
            - name: AWS_SECRET_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: feast-secrets
                  key: s3-secret-key
            - name: AWS_DEFAULT_REGION
              value: us-east-1
            volumeMounts:
            - name: feast-config
              mountPath: /config
            - name: feast-repo
              mountPath: /feature_repo
          volumes:
          - name: feast-config
            configMap:
              name: feast-config
          - name: feast-repo
            emptyDir: {}
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: feast-serving-pdb
  namespace: feast
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: feast-serving
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: feast-client-examples
  namespace: feast
data:
  feature_retrieval.py: |
    #!/usr/bin/env python3
    """
    Example script showing how to retrieve features from Feast for ML inference
    """
    
    import pandas as pd
    from feast import FeatureStore
    from datetime import datetime, timedelta
    
    # Initialize Feast client
    store = FeatureStore(repo_path="/feature_repo")
    
    def get_user_features_for_churn_prediction(user_ids):
        """Retrieve user features for churn prediction model"""
        
        # Create entity dataframe
        entity_df = pd.DataFrame({
            "user_id": user_ids,
            "event_timestamp": [datetime.now()] * len(user_ids)
        })
        
        # Get features for churn prediction
        features = store.get_online_features(
            features=[
                "user_features:page_views_7d",
                "user_features:sessions_7d", 
                "user_features:total_purchase_amount_7d",
                "user_features:avg_session_duration_7d",
                "user_features:bounce_rate_7d",
                "user_features:last_purchase_days_ago",
                "user_realtime_features:current_session_page_views",
                "user_realtime_features:current_session_duration"
            ],
            entity_rows=[
                {"user_id": user_id} for user_id in user_ids
            ]
        ).to_df()
        
        return features
    
    def get_recommendation_features(user_id, product_ids):
        """Retrieve features for product recommendation model"""
        
        # Get user context features
        user_features = store.get_online_features(
            features=[
                "user_features:favorite_category",
                "user_features:total_purchase_amount_7d",
                "user_features:device_type"
            ],
            entity_rows=[{"user_id": user_id}]
        ).to_df()
        
        # Get product features
        product_features = store.get_online_features(
            features=[
                "product_features:views_7d",
                "product_features:avg_rating",
                "product_features:price",
                "product_features:category",
                "product_features:conversion_rate_7d"
            ],
            entity_rows=[
                {"product_id": product_id} for product_id in product_ids
            ]
        ).to_df()
        
        return user_features, product_features
    
    def push_realtime_features(user_id, features_dict):
        """Push real-time user activity features"""
        
        feature_df = pd.DataFrame({
            "user_id": [user_id],
            "current_session_page_views": [features_dict.get("page_views", 0)],
            "current_session_duration": [features_dict.get("session_duration", 0)],
            "items_in_cart": [features_dict.get("cart_items", 0)],
            "last_page_viewed": [features_dict.get("last_page", "")],
            "event_timestamp": [datetime.now()]
        })
        
        store.push("user_activity_push_source", feature_df)
        
    # Example usage
    if __name__ == "__main__":
        # Example: Get features for churn prediction
        user_ids = ["user_123", "user_456", "user_789"]
        churn_features = get_user_features_for_churn_prediction(user_ids)
        print("Churn prediction features:")
        print(churn_features)
        
        # Example: Get features for product recommendation
        user_id = "user_123"
        product_ids = ["prod_001", "prod_002", "prod_003"]
        user_ctx, product_ctx = get_recommendation_features(user_id, product_ids)
        print("\nRecommendation features:")
        print("User context:", user_ctx)
        print("Product context:", product_ctx)
        
        # Example: Push real-time features
        realtime_data = {
            "page_views": 5,
            "session_duration": 120,
            "cart_items": 2,
            "last_page": "/product/123"
        }
        push_realtime_features(user_id, realtime_data)
        print("\nReal-time features pushed successfully")
  
  monitoring.py: |
    #!/usr/bin/env python3
    """
    Feast feature store monitoring and validation
    """
    
    import pandas as pd
    from feast import FeatureStore
    from datetime import datetime, timedelta
    import logging
    
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    class FeastMonitor:
        def __init__(self, repo_path="/feature_repo"):
            self.store = FeatureStore(repo_path=repo_path)
            
        def check_feature_freshness(self):
            """Check if features are fresh and within expected time windows"""
            
            feature_views = self.store.list_feature_views()
            issues = []
            
            for fv in feature_views:
                try:
                    # Get latest materialization timestamp
                    last_materialized = self.store.get_historical_features(
                        entity_df=pd.DataFrame({
                            "user_id": ["test_user"],
                            "event_timestamp": [datetime.now()]
                        }),
                        features=[f"{fv.name}:*"]
                    ).to_df()
                    
                    if last_materialized.empty:
                        issues.append(f"No data found for feature view {fv.name}")
                        continue
                        
                    # Check freshness based on TTL
                    expected_freshness = datetime.now() - fv.ttl
                    # Add validation logic here
                    
                except Exception as e:
                    issues.append(f"Error checking {fv.name}: {str(e)}")
                    
            return issues
            
        def validate_feature_quality(self):
            """Validate feature data quality"""
            
            quality_issues = []
            
            # Check for missing values, outliers, data drift
            # This is a simplified example
            try:
                # Sample some recent features
                entity_df = pd.DataFrame({
                    "user_id": [f"user_{i}" for i in range(100)],
                    "event_timestamp": [datetime.now()] * 100
                })
                
                features = self.store.get_historical_features(
                    entity_df=entity_df,
                    features=[
                        "user_features:page_views_7d",
                        "user_features:total_purchase_amount_7d",
                        "product_features:avg_rating"
                    ]
                ).to_df()
                
                # Check for null values
                null_counts = features.isnull().sum()
                for col, null_count in null_counts.items():
                    if null_count > len(features) * 0.1:  # >10% nulls
                        quality_issues.append(f"High null rate in {col}: {null_count}/{len(features)}")
                
                # Check for outliers (simplified)
                numeric_cols = features.select_dtypes(include=['number']).columns
                for col in numeric_cols:
                    if col in features.columns:
                        q99 = features[col].quantile(0.99)
                        q1 = features[col].quantile(0.01)
                        outliers = ((features[col] > q99) | (features[col] < q1)).sum()
                        if outliers > len(features) * 0.05:  # >5% outliers
                            quality_issues.append(f"High outlier rate in {col}: {outliers}/{len(features)}")
                            
            except Exception as e:
                quality_issues.append(f"Error in quality validation: {str(e)}")
                
            return quality_issues
            
        def generate_monitoring_report(self):
            """Generate comprehensive monitoring report"""
            
            report = {
                "timestamp": datetime.now().isoformat(),
                "freshness_issues": self.check_feature_freshness(),
                "quality_issues": self.validate_feature_quality(),
                "feature_views": len(self.store.list_feature_views()),
                "feature_services": len(self.store.list_feature_services())
            }
            
            return report
    
    if __name__ == "__main__":
        monitor = FeastMonitor()
        report = monitor.generate_monitoring_report()
        
        logger.info("Feast Monitoring Report:")
        logger.info(f"Feature Views: {report['feature_views']}")
        logger.info(f"Feature Services: {report['feature_services']}")
        
        if report['freshness_issues']:
            logger.warning(f"Freshness Issues: {report['freshness_issues']}")
        else:
            logger.info("All features are fresh")
            
        if report['quality_issues']:
            logger.warning(f"Quality Issues: {report['quality_issues']}")
        else:
            logger.info("Feature quality validation passed")