#!/usr/bin/env python3
"""
Automated ML Model Training Pipeline for E-commerce Analytics
Orchestrates automated training, validation, and deployment of ML models
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import os
import sys
import subprocess
from typing import Dict, List, Tuple, Optional, Any, Union
import yaml
import json
from dataclasses import dataclass, asdict
from pathlib import Path
import schedule
import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed

# ML and data processing
import joblib
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score

# MLflow integration
import mlflow
import mlflow.sklearn
from mlflow.tracking import MlflowClient
from mlflow.entities import ViewType

# Kubernetes and containerization
from kubernetes import client, config as k8s_config
from kubernetes.client.rest import ApiException

# Cloud services
import boto3
from sqlalchemy import create_engine

# Monitoring and alerting
import requests
from prometheus_client import Collector<PERSON>eg<PERSON><PERSON>, Gauge, Counter, push_to_gateway

# Configuration
@dataclass
class PipelineConfig:
    # Training schedule
    churn_model_schedule: str = "daily"  # daily, weekly, monthly
    sales_forecasting_schedule: str = "weekly"
    revenue_analysis_schedule: str = "weekly"
    clv_model_schedule: str = "monthly"
    
    # Data freshness requirements
    min_data_freshness_hours: int = 6
    min_training_samples: int = 1000
    
    # Model performance thresholds
    churn_model_min_auc: float = 0.75
    sales_forecast_max_mape: float = 15.0
    clv_model_min_r2: float = 0.60
    
    # Infrastructure
    kubernetes_namespace: str = "ml-training"
    training_image: str = "ecommerce-analytics/ml-training:latest"
    gpu_enabled: bool = True
    max_parallel_jobs: int = 3
    
    # Storage and artifacts
    s3_bucket: str = os.getenv('S3_BUCKET', 'ecommerce-analytics-models')
    model_registry_prefix: str = 'production-models/'
    
    # MLflow configuration
    mlflow_tracking_uri: str = os.getenv('MLFLOW_TRACKING_URI', 'http://mlflow-server.mlflow:5000')
    
    # Database connections
    db_host: str = os.getenv('DB_HOST', 'analytics-db.cluster-xyz.us-east-1.rds.amazonaws.com')
    db_name: str = os.getenv('DB_NAME', 'ecommerce_analytics')
    db_user: str = os.getenv('DB_USER', 'analytics_user')
    db_password: str = os.getenv('DB_PASSWORD', 'analytics_password')
    
    # Monitoring and alerting
    prometheus_gateway: str = os.getenv('PROMETHEUS_GATEWAY', 'prometheus-pushgateway:9091')
    slack_webhook: str = os.getenv('SLACK_WEBHOOK', '')
    
    # Feature store
    feast_repo_path: str = os.getenv('FEAST_REPO_PATH', '/feature_repo')

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class AutomatedTrainingPipeline:
    def __init__(self, config: PipelineConfig):
        self.config = config
        self.db_engine = self._create_db_engine()
        self.s3_client = boto3.client('s3')
        self.k8s_batch_v1 = None
        self.k8s_core_v1 = None
        self._setup_kubernetes()
        
        # Initialize MLflow
        mlflow.set_tracking_uri(config.mlflow_tracking_uri)
        self.mlflow_client = MlflowClient()
        
        # Monitoring
        self.prometheus_registry = CollectorRegistry()
        self.setup_prometheus_metrics()
        
        # Job tracking
        self.active_jobs = {}
        self.job_history = []
        
        # Model configurations
        self.model_configs = self._load_model_configurations()
        
    def _create_db_engine(self):
        """Create database engine for PostgreSQL"""
        connection_string = f"postgresql://{self.config.db_user}:{self.config.db_password}@{self.config.db_host}/{self.config.db_name}"
        return create_engine(connection_string)
    
    def _setup_kubernetes(self):
        """Setup Kubernetes client"""
        try:
            # Try in-cluster config first
            k8s_config.load_incluster_config()
        except k8s_config.ConfigException:
            # Fall back to local config
            k8s_config.load_kube_config()
        
        self.k8s_batch_v1 = client.BatchV1Api()
        self.k8s_core_v1 = client.CoreV1Api()
    
    def setup_prometheus_metrics(self):
        """Setup Prometheus metrics for monitoring"""
        self.training_jobs_total = Counter(
            'ml_training_jobs_total',
            'Total number of ML training jobs',
            ['model_type', 'status'],
            registry=self.prometheus_registry
        )
        
        self.training_duration = Gauge(
            'ml_training_duration_seconds',
            'Duration of ML training jobs in seconds',
            ['model_type'],
            registry=self.prometheus_registry
        )
        
        self.model_performance = Gauge(
            'ml_model_performance',
            'Model performance metrics',
            ['model_type', 'metric_name'],
            registry=self.prometheus_registry
        )
        
        self.data_freshness = Gauge(
            'ml_training_data_freshness_hours',
            'Hours since last data update',
            ['data_source'],
            registry=self.prometheus_registry
        )
    
    def _load_model_configurations(self) -> Dict:
        """Load model training configurations"""
        return {
            'churn_prediction': {
                'script_path': '/models/churn-prediction/churn_prediction_model.py',
                'min_samples': 1000,
                'performance_metric': 'auc',
                'performance_threshold': self.config.churn_model_min_auc,
                'schedule': self.config.churn_model_schedule,
                'dependencies': ['user_events', 'purchases', 'user_profiles'],
                'compute_resources': {
                    'cpu': '2000m',
                    'memory': '4Gi',
                    'gpu': False
                }
            },
            'sales_forecasting': {
                'script_path': '/models/sales-forecasting/sales_forecasting_model.py',
                'min_samples': 365,  # At least 1 year of daily data
                'performance_metric': 'mape',
                'performance_threshold': self.config.sales_forecast_max_mape,
                'schedule': self.config.sales_forecasting_schedule,
                'dependencies': ['purchases', 'user_events'],
                'compute_resources': {
                    'cpu': '3000m',
                    'memory': '6Gi',
                    'gpu': False
                }
            },
            'revenue_analysis': {
                'script_path': '/models/revenue-prediction/revenue_trend_analysis.py',
                'min_samples': 730,  # 2 years of data
                'performance_metric': 'completion',
                'performance_threshold': 1.0,
                'schedule': self.config.revenue_analysis_schedule,
                'dependencies': ['purchases', 'user_events', 'purchase_items'],
                'compute_resources': {
                    'cpu': '2000m',
                    'memory': '4Gi',
                    'gpu': False
                }
            },
            'clv_prediction': {
                'script_path': '/models/clv-prediction/customer_lifetime_value_model.py',
                'min_samples': 500,
                'performance_metric': 'r2',
                'performance_threshold': self.config.clv_model_min_r2,
                'schedule': self.config.clv_model_schedule,
                'dependencies': ['purchases', 'user_events', 'user_profiles', 'purchase_items'],
                'compute_resources': {
                    'cpu': '4000m',
                    'memory': '8Gi',
                    'gpu': self.config.gpu_enabled
                }
            }
        }
    
    def check_data_freshness(self, dependencies: List[str]) -> Dict[str, float]:
        """Check data freshness for required dependencies"""
        logger.info(f"Checking data freshness for dependencies: {dependencies}")
        
        freshness_results = {}
        
        for table in dependencies:
            try:
                # Query to get the latest timestamp for each table
                query = f"""
                    SELECT MAX(
                        CASE 
                            WHEN '{table}' = 'user_events' THEN event_timestamp
                            WHEN '{table}' = 'purchases' THEN purchase_timestamp
                            WHEN '{table}' = 'user_profiles' THEN updated_at
                            WHEN '{table}' = 'purchase_items' THEN created_at
                            ELSE updated_at
                        END
                    ) as latest_timestamp
                    FROM {table}
                """
                
                result = pd.read_sql(query, self.db_engine)
                latest_timestamp = pd.to_datetime(result['latest_timestamp'].iloc[0])
                
                # Calculate hours since last update
                hours_since_update = (datetime.now() - latest_timestamp).total_seconds() / 3600
                freshness_results[table] = hours_since_update
                
                # Update Prometheus metric
                self.data_freshness.labels(data_source=table).set(hours_since_update)
                
            except Exception as e:
                logger.error(f"Error checking freshness for {table}: {str(e)}")
                freshness_results[table] = 999  # Indicate stale data
        
        return freshness_results
    
    def check_data_quality(self, model_type: str) -> Dict[str, Any]:
        """Check data quality for model training"""
        logger.info(f"Checking data quality for {model_type}")
        
        quality_results = {
            'sufficient_data': False,
            'data_issues': [],
            'sample_count': 0,
            'quality_score': 0.0
        }
        
        try:
            if model_type == 'churn_prediction':
                # Check user activity and purchase data
                query = """
                    SELECT COUNT(DISTINCT user_id) as user_count,
                           COUNT(*) as total_events,
                           COUNT(DISTINCT DATE(event_timestamp)) as active_days
                    FROM user_events 
                    WHERE event_timestamp >= NOW() - INTERVAL '90 days'
                """
                result = pd.read_sql(query, self.db_engine)
                
                user_count = result['user_count'].iloc[0]
                quality_results['sample_count'] = user_count
                
                if user_count < self.model_configs[model_type]['min_samples']:
                    quality_results['data_issues'].append(f"Insufficient users: {user_count}")
                else:
                    quality_results['sufficient_data'] = True
                
            elif model_type == 'sales_forecasting':
                # Check daily sales data availability
                query = """
                    SELECT COUNT(DISTINCT DATE(purchase_timestamp)) as days_with_sales,
                           SUM(total_amount) as total_revenue
                    FROM purchases 
                    WHERE purchase_timestamp >= NOW() - INTERVAL '2 years'
                        AND total_amount > 0
                """
                result = pd.read_sql(query, self.db_engine)
                
                days_with_sales = result['days_with_sales'].iloc[0]
                quality_results['sample_count'] = days_with_sales
                
                if days_with_sales < self.model_configs[model_type]['min_samples']:
                    quality_results['data_issues'].append(f"Insufficient daily data: {days_with_sales}")
                else:
                    quality_results['sufficient_data'] = True
                
            elif model_type == 'clv_prediction':
                # Check customer transaction data
                query = """
                    SELECT COUNT(DISTINCT user_id) as customer_count,
                           AVG(order_count) as avg_orders_per_customer
                    FROM (
                        SELECT user_id, COUNT(*) as order_count
                        FROM purchases 
                        WHERE purchase_timestamp >= NOW() - INTERVAL '2 years'
                            AND total_amount > 0
                        GROUP BY user_id
                        HAVING COUNT(*) >= 2
                    ) customer_orders
                """
                result = pd.read_sql(query, self.db_engine)
                
                customer_count = result['customer_count'].iloc[0]
                quality_results['sample_count'] = customer_count
                
                if customer_count < self.model_configs[model_type]['min_samples']:
                    quality_results['data_issues'].append(f"Insufficient repeat customers: {customer_count}")
                else:
                    quality_results['sufficient_data'] = True
            
            # Calculate overall quality score
            if quality_results['sufficient_data'] and not quality_results['data_issues']:
                quality_results['quality_score'] = 1.0
            elif quality_results['sufficient_data']:
                quality_results['quality_score'] = 0.8
            else:
                quality_results['quality_score'] = 0.2
                
        except Exception as e:
            logger.error(f"Error checking data quality for {model_type}: {str(e)}")
            quality_results['data_issues'].append(f"Quality check failed: {str(e)}")
        
        return quality_results
    
    def should_trigger_training(self, model_type: str) -> Dict[str, Any]:
        """Determine if model training should be triggered"""
        logger.info(f"Evaluating training trigger for {model_type}")
        
        model_config = self.model_configs[model_type]
        
        trigger_decision = {
            'should_train': False,
            'reasons': [],
            'blocking_issues': [],
            'data_freshness': {},
            'data_quality': {}
        }
        
        # Check data freshness
        freshness_results = self.check_data_freshness(model_config['dependencies'])
        trigger_decision['data_freshness'] = freshness_results
        
        # Check if data is fresh enough
        max_freshness = max(freshness_results.values())
        if max_freshness > self.config.min_data_freshness_hours:
            trigger_decision['blocking_issues'].append(
                f"Data too stale: {max_freshness:.1f} hours (max: {self.config.min_data_freshness_hours})"
            )
        
        # Check data quality
        quality_results = self.check_data_quality(model_type)
        trigger_decision['data_quality'] = quality_results
        
        if not quality_results['sufficient_data']:
            trigger_decision['blocking_issues'].append("Insufficient training data")
        
        # Check schedule-based triggers
        last_training = self._get_last_training_time(model_type)
        schedule_interval = self._get_schedule_interval(model_config['schedule'])
        
        if last_training is None:
            trigger_decision['reasons'].append("No previous training found")
            trigger_decision['should_train'] = True
        elif datetime.now() - last_training > schedule_interval:
            trigger_decision['reasons'].append(f"Scheduled training due ({model_config['schedule']})")
            trigger_decision['should_train'] = True
        
        # Check performance-based triggers
        current_performance = self._get_current_model_performance(model_type)
        if current_performance is not None:
            threshold = model_config['performance_threshold']
            metric = model_config['performance_metric']
            
            if metric in ['auc', 'r2'] and current_performance < threshold:
                trigger_decision['reasons'].append(f"Performance degraded: {current_performance:.3f} < {threshold}")
                trigger_decision['should_train'] = True
            elif metric == 'mape' and current_performance > threshold:
                trigger_decision['reasons'].append(f"Performance degraded: {current_performance:.1f}% > {threshold}%")
                trigger_decision['should_train'] = True
        
        # Override decision if there are blocking issues
        if trigger_decision['blocking_issues']:
            trigger_decision['should_train'] = False
        
        return trigger_decision
    
    def _get_last_training_time(self, model_type: str) -> Optional[datetime]:
        """Get the last training time for a model"""
        try:
            experiment = mlflow.get_experiment_by_name(f"{model_type}")
            if experiment is None:
                return None
            
            runs = mlflow.search_runs(
                experiment_ids=[experiment.experiment_id],
                filter_string="status = 'FINISHED'",
                order_by=["start_time DESC"],
                max_results=1
            )
            
            if len(runs) > 0:
                return pd.to_datetime(runs.iloc[0]['start_time'])
            
        except Exception as e:
            logger.warning(f"Could not get last training time for {model_type}: {str(e)}")
        
        return None
    
    def _get_schedule_interval(self, schedule: str) -> timedelta:
        """Convert schedule string to timedelta"""
        schedule_map = {
            'daily': timedelta(days=1),
            'weekly': timedelta(weeks=1),
            'monthly': timedelta(days=30),
            'quarterly': timedelta(days=90)
        }
        return schedule_map.get(schedule, timedelta(days=1))
    
    def _get_current_model_performance(self, model_type: str) -> Optional[float]:
        """Get current model performance from production"""
        try:
            # Get the latest production model
            model_name = f"{model_type.replace('_', '-')}-model"
            latest_version = self.mlflow_client.get_latest_versions(model_name, stages=["Production"])
            
            if latest_version:
                run_id = latest_version[0].run_id
                run = self.mlflow_client.get_run(run_id)
                
                # Get the relevant performance metric
                metric_key = self.model_configs[model_type]['performance_metric']
                metric_mappings = {
                    'auc': ['val_auc', 'test_auc', 'auc'],
                    'mape': ['val_mape', 'test_mape', 'mape'],
                    'r2': ['val_r2', 'test_r2', 'r2_score']
                }
                
                for possible_key in metric_mappings.get(metric_key, [metric_key]):
                    if possible_key in run.data.metrics:
                        return run.data.metrics[possible_key]
            
        except Exception as e:
            logger.warning(f"Could not get current performance for {model_type}: {str(e)}")
        
        return None
    
    def create_training_job(self, model_type: str, job_config: Dict) -> str:
        """Create Kubernetes training job"""
        logger.info(f"Creating training job for {model_type}")
        
        model_config = self.model_configs[model_type]
        job_name = f"ml-training-{model_type.replace('_', '-')}-{int(time.time())}"
        
        # Create job specification
        job_spec = {
            'apiVersion': 'batch/v1',
            'kind': 'Job',
            'metadata': {
                'name': job_name,
                'namespace': self.config.kubernetes_namespace,
                'labels': {
                    'app': 'ml-training',
                    'model-type': model_type,
                    'created-by': 'automated-pipeline'
                }
            },
            'spec': {
                'backoffLimit': 2,
                'ttlSecondsAfterFinished': 3600,  # Clean up after 1 hour
                'template': {
                    'metadata': {
                        'labels': {
                            'app': 'ml-training',
                            'model-type': model_type
                        }
                    },
                    'spec': {
                        'restartPolicy': 'Never',
                        'serviceAccountName': 'ml-training-service-account',
                        'containers': [{
                            'name': 'ml-training',
                            'image': self.config.training_image,
                            'command': ['python'],
                            'args': [model_config['script_path']],
                            'env': [
                                {'name': 'MLFLOW_TRACKING_URI', 'value': self.config.mlflow_tracking_uri},
                                {'name': 'DB_HOST', 'value': self.config.db_host},
                                {'name': 'DB_NAME', 'value': self.config.db_name},
                                {'name': 'DB_USER', 'value': self.config.db_user},
                                {'name': 'DB_PASSWORD', 'value': self.config.db_password},
                                {'name': 'S3_BUCKET', 'value': self.config.s3_bucket},
                                {'name': 'FEAST_REPO_PATH', 'value': self.config.feast_repo_path},
                                {'name': 'MODEL_TYPE', 'value': model_type}
                            ],
                            'resources': {
                                'requests': {
                                    'cpu': model_config['compute_resources']['cpu'],
                                    'memory': model_config['compute_resources']['memory']
                                },
                                'limits': {
                                    'cpu': model_config['compute_resources']['cpu'],
                                    'memory': model_config['compute_resources']['memory']
                                }
                            },
                            'volumeMounts': [
                                {
                                    'name': 'model-code',
                                    'mountPath': '/models',
                                    'readOnly': True
                                },
                                {
                                    'name': 'feast-repo',
                                    'mountPath': '/feature_repo',
                                    'readOnly': True
                                }
                            ]
                        }],
                        'volumes': [
                            {
                                'name': 'model-code',
                                'configMap': {
                                    'name': 'ml-model-code'
                                }
                            },
                            {
                                'name': 'feast-repo',
                                'configMap': {
                                    'name': 'feast-feature-repo'
                                }
                            }
                        ]
                    }
                }
            }
        }
        
        # Add GPU resources if enabled
        if model_config['compute_resources'].get('gpu', False):
            job_spec['spec']['template']['spec']['containers'][0]['resources']['limits']['nvidia.com/gpu'] = '1'
        
        try:
            # Create the job
            self.k8s_batch_v1.create_namespaced_job(
                namespace=self.config.kubernetes_namespace,
                body=job_spec
            )
            
            # Track the job
            self.active_jobs[job_name] = {
                'model_type': model_type,
                'start_time': datetime.now(),
                'status': 'running',
                'job_config': job_config
            }
            
            # Update Prometheus metrics
            self.training_jobs_total.labels(model_type=model_type, status='started').inc()
            
            logger.info(f"Training job {job_name} created successfully")
            return job_name
            
        except ApiException as e:
            logger.error(f"Failed to create training job: {str(e)}")
            self.training_jobs_total.labels(model_type=model_type, status='failed').inc()
            raise
    
    def monitor_training_jobs(self):
        """Monitor active training jobs"""
        if not self.active_jobs:
            return
        
        logger.info(f"Monitoring {len(self.active_jobs)} active training jobs")
        
        completed_jobs = []
        
        for job_name, job_info in self.active_jobs.items():
            try:
                # Get job status
                job = self.k8s_batch_v1.read_namespaced_job(
                    name=job_name,
                    namespace=self.config.kubernetes_namespace
                )
                
                model_type = job_info['model_type']
                
                # Check if job completed
                if job.status.conditions:
                    for condition in job.status.conditions:
                        if condition.type == 'Complete' and condition.status == 'True':
                            # Job completed successfully
                            duration = (datetime.now() - job_info['start_time']).total_seconds()
                            
                            self.training_duration.labels(model_type=model_type).set(duration)
                            self.training_jobs_total.labels(model_type=model_type, status='completed').inc()
                            
                            # Post-process completed job
                            self._handle_completed_job(job_name, job_info, 'success')
                            completed_jobs.append(job_name)
                            
                        elif condition.type == 'Failed' and condition.status == 'True':
                            # Job failed
                            self.training_jobs_total.labels(model_type=model_type, status='failed').inc()
                            
                            # Handle failed job
                            self._handle_completed_job(job_name, job_info, 'failed')
                            completed_jobs.append(job_name)
                
            except ApiException as e:
                logger.error(f"Error monitoring job {job_name}: {str(e)}")
        
        # Remove completed jobs from active tracking
        for job_name in completed_jobs:
            del self.active_jobs[job_name]
    
    def _handle_completed_job(self, job_name: str, job_info: Dict, status: str):
        """Handle completed training job"""
        model_type = job_info['model_type']
        duration = (datetime.now() - job_info['start_time']).total_seconds()
        
        logger.info(f"Training job {job_name} completed with status: {status}")
        
        # Add to job history
        job_result = {
            'job_name': job_name,
            'model_type': model_type,
            'start_time': job_info['start_time'],
            'end_time': datetime.now(),
            'duration_seconds': duration,
            'status': status
        }
        
        self.job_history.append(job_result)
        
        if status == 'success':
            # Validate and promote model if successful
            self._validate_and_promote_model(model_type, job_name)
            
            # Send success notification
            self._send_notification(
                f"✅ ML Training Success",
                f"Model {model_type} training completed successfully in {duration/60:.1f} minutes"
            )
        else:
            # Send failure notification
            self._send_notification(
                f"❌ ML Training Failed",
                f"Model {model_type} training failed after {duration/60:.1f} minutes. Job: {job_name}"
            )
        
        # Clean up job if needed
        self._cleanup_completed_job(job_name)
    
    def _validate_and_promote_model(self, model_type: str, job_name: str):
        """Validate trained model and promote to production if meets criteria"""
        logger.info(f"Validating and promoting model {model_type}")
        
        try:
            # Get the latest run for this model type
            experiment = mlflow.get_experiment_by_name(model_type)
            if experiment is None:
                logger.error(f"No experiment found for {model_type}")
                return
            
            # Get the most recent successful run
            runs = mlflow.search_runs(
                experiment_ids=[experiment.experiment_id],
                filter_string="status = 'FINISHED'",
                order_by=["start_time DESC"],
                max_results=1
            )
            
            if len(runs) == 0:
                logger.error(f"No completed runs found for {model_type}")
                return
            
            latest_run = runs.iloc[0]
            run_id = latest_run['run_id']
            
            # Validate model performance
            model_config = self.model_configs[model_type]
            metric_key = model_config['performance_metric']
            threshold = model_config['performance_threshold']
            
            # Find the relevant metric
            performance_value = None
            metric_mappings = {
                'auc': ['val_auc', 'test_auc', 'auc'],
                'mape': ['val_mape', 'test_mape', 'mape'],
                'r2': ['val_r2', 'test_r2', 'r2_score'],
                'completion': ['pipeline_success']
            }
            
            for possible_key in metric_mappings.get(metric_key, [metric_key]):
                metric_col = f'metrics.{possible_key}'
                if metric_col in latest_run and pd.notna(latest_run[metric_col]):
                    performance_value = latest_run[metric_col]
                    break
            
            if performance_value is None:
                logger.warning(f"Could not find performance metric for {model_type}")
                return
            
            # Check if model meets performance criteria
            model_passes = False
            if metric_key in ['auc', 'r2', 'completion']:
                model_passes = performance_value >= threshold
            elif metric_key == 'mape':
                model_passes = performance_value <= threshold
            
            # Update Prometheus metrics
            self.model_performance.labels(
                model_type=model_type,
                metric_name=metric_key
            ).set(performance_value)
            
            if model_passes:
                # Register and promote model
                model_name = f"{model_type.replace('_', '-')}-model"
                
                # Register model if not already registered
                try:
                    self.mlflow_client.get_registered_model(model_name)
                except:
                    self.mlflow_client.create_registered_model(model_name)
                
                # Create new model version
                model_version = self.mlflow_client.create_model_version(
                    name=model_name,
                    source=f"runs:/{run_id}/{model_type}_model",
                    run_id=run_id
                )
                
                # Transition to production
                self.mlflow_client.transition_model_version_stage(
                    name=model_name,
                    version=model_version.version,
                    stage="Production"
                )
                
                logger.info(f"Model {model_type} v{model_version.version} promoted to production")
                
                # Send success notification
                self._send_notification(
                    f"🚀 Model Promoted",
                    f"Model {model_type} v{model_version.version} promoted to production. "
                    f"Performance: {metric_key}={performance_value:.3f}"
                )
                
            else:
                logger.warning(f"Model {model_type} did not meet performance criteria: "
                             f"{metric_key}={performance_value:.3f} vs threshold={threshold}")
                
                # Send warning notification
                self._send_notification(
                    f"⚠️ Model Performance Warning",
                    f"Model {model_type} training completed but did not meet performance criteria. "
                    f"{metric_key}={performance_value:.3f} (threshold: {threshold})"
                )
                
        except Exception as e:
            logger.error(f"Error validating model {model_type}: {str(e)}")
    
    def _cleanup_completed_job(self, job_name: str):
        """Clean up completed Kubernetes job"""
        try:
            # Delete the job (pods will be cleaned up by TTL)
            self.k8s_batch_v1.delete_namespaced_job(
                name=job_name,
                namespace=self.config.kubernetes_namespace
            )
            logger.info(f"Cleaned up job {job_name}")
        except ApiException as e:
            logger.warning(f"Could not clean up job {job_name}: {str(e)}")
    
    def _send_notification(self, title: str, message: str):
        """Send notification via Slack webhook"""
        if not self.config.slack_webhook:
            return
        
        try:
            payload = {
                "text": f"{title}\n{message}",
                "username": "ML Training Pipeline",
                "icon_emoji": ":robot_face:"
            }
            
            response = requests.post(self.config.slack_webhook, json=payload)
            response.raise_for_status()
            
        except Exception as e:
            logger.error(f"Failed to send notification: {str(e)}")
    
    def run_training_evaluation(self):
        """Evaluate all models and trigger training if needed"""
        logger.info("Running training evaluation for all models")
        
        # Push metrics to Prometheus
        try:
            push_to_gateway(
                self.config.prometheus_gateway,
                job='ml_training_pipeline',
                registry=self.prometheus_registry
            )
        except Exception as e:
            logger.warning(f"Could not push metrics to Prometheus: {str(e)}")
        
        training_decisions = {}
        
        # Evaluate each model type
        for model_type in self.model_configs.keys():
            logger.info(f"Evaluating training trigger for {model_type}")
            
            decision = self.should_trigger_training(model_type)
            training_decisions[model_type] = decision
            
            if decision['should_train']:
                logger.info(f"Training triggered for {model_type}. Reasons: {decision['reasons']}")
                
                # Check if we have capacity for more jobs
                if len(self.active_jobs) >= self.config.max_parallel_jobs:
                    logger.warning(f"Maximum parallel jobs ({self.config.max_parallel_jobs}) reached. "
                                 f"Skipping {model_type} training.")
                    continue
                
                try:
                    job_name = self.create_training_job(model_type, decision)
                    logger.info(f"Started training job {job_name} for {model_type}")
                    
                except Exception as e:
                    logger.error(f"Failed to start training job for {model_type}: {str(e)}")
                    
            else:
                logger.info(f"Training not triggered for {model_type}. "
                          f"Blocking issues: {decision['blocking_issues']}")
        
        return training_decisions
    
    def setup_scheduler(self):
        """Setup automated training scheduler"""
        logger.info("Setting up automated training scheduler")
        
        # Schedule training evaluation every hour
        schedule.every().hour.do(self.run_training_evaluation)
        
        # Schedule job monitoring every 5 minutes
        schedule.every(5).minutes.do(self.monitor_training_jobs)
        
        # Schedule daily health check
        schedule.every().day.at("06:00").do(self.run_health_check)
        
        # Schedule weekly model performance report
        schedule.every().week.do(self.generate_performance_report)
    
    def run_health_check(self):
        """Run daily health check of the training pipeline"""
        logger.info("Running training pipeline health check")
        
        health_status = {
            'timestamp': datetime.now().isoformat(),
            'active_jobs': len(self.active_jobs),
            'recent_jobs': len([j for j in self.job_history if 
                              (datetime.now() - j['end_time']).days <= 7]),
            'services_status': {}
        }
        
        # Check MLflow connectivity
        try:
            self.mlflow_client.list_experiments()
            health_status['services_status']['mlflow'] = 'healthy'
        except Exception as e:
            health_status['services_status']['mlflow'] = f'unhealthy: {str(e)}'
        
        # Check database connectivity
        try:
            pd.read_sql("SELECT 1", self.db_engine)
            health_status['services_status']['database'] = 'healthy'
        except Exception as e:
            health_status['services_status']['database'] = f'unhealthy: {str(e)}'
        
        # Check Kubernetes connectivity
        try:
            self.k8s_core_v1.list_namespace()
            health_status['services_status']['kubernetes'] = 'healthy'
        except Exception as e:
            health_status['services_status']['kubernetes'] = f'unhealthy: {str(e)}'
        
        # Send health report
        unhealthy_services = [k for k, v in health_status['services_status'].items() 
                            if not v.startswith('healthy')]
        
        if unhealthy_services:
            self._send_notification(
                "🏥 Training Pipeline Health Alert",
                f"Unhealthy services detected: {', '.join(unhealthy_services)}\n"
                f"Full status: {json.dumps(health_status, indent=2)}"
            )
        
        return health_status
    
    def generate_performance_report(self):
        """Generate weekly model performance report"""
        logger.info("Generating weekly performance report")
        
        report = {
            'period': 'last_7_days',
            'timestamp': datetime.now().isoformat(),
            'model_performance': {},
            'training_summary': {
                'total_jobs': len([j for j in self.job_history if 
                                 (datetime.now() - j['end_time']).days <= 7]),
                'successful_jobs': len([j for j in self.job_history if 
                                      j['status'] == 'success' and 
                                      (datetime.now() - j['end_time']).days <= 7]),
                'failed_jobs': len([j for j in self.job_history if 
                                  j['status'] == 'failed' and 
                                  (datetime.now() - j['end_time']).days <= 7])
            }
        }
        
        # Get performance for each model
        for model_type in self.model_configs.keys():
            performance = self._get_current_model_performance(model_type)
            if performance is not None:
                report['model_performance'][model_type] = {
                    'current_performance': performance,
                    'metric': self.model_configs[model_type]['performance_metric'],
                    'threshold': self.model_configs[model_type]['performance_threshold']
                }
        
        # Send report
        report_text = f"""📊 Weekly ML Performance Report
        
Training Summary:
- Total jobs: {report['training_summary']['total_jobs']}
- Successful: {report['training_summary']['successful_jobs']}
- Failed: {report['training_summary']['failed_jobs']}

Model Performance:"""
        
        for model_type, perf in report['model_performance'].items():
            status = "✅" if perf['current_performance'] >= perf['threshold'] else "⚠️"
            report_text += f"\n- {model_type}: {status} {perf['current_performance']:.3f} ({perf['metric']})"
        
        self._send_notification("📊 Weekly ML Performance Report", report_text)
        
        return report
    
    def run_pipeline(self):
        """Run the automated training pipeline"""
        logger.info("Starting automated ML training pipeline")
        
        # Setup scheduler
        self.setup_scheduler()
        
        # Initial evaluation
        self.run_training_evaluation()
        
        # Run scheduler
        logger.info("Pipeline scheduler started. Monitoring for training triggers...")
        
        try:
            while True:
                schedule.run_pending()
                time.sleep(60)  # Check every minute
                
        except KeyboardInterrupt:
            logger.info("Pipeline stopped by user")
        except Exception as e:
            logger.error(f"Pipeline error: {str(e)}")
            self._send_notification(
                "💥 Pipeline Error",
                f"Automated training pipeline encountered an error: {str(e)}"
            )
            raise

def main():
    """Main function to run the automated training pipeline"""
    config = PipelineConfig()
    pipeline = AutomatedTrainingPipeline(config)
    
    try:
        pipeline.run_pipeline()
    except Exception as e:
        logger.error(f"Pipeline failed: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()