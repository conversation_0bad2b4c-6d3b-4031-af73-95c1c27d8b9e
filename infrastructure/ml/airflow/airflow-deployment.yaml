apiVersion: v1
kind: Namespace
metadata:
  name: airflow
  labels:
    app.kubernetes.io/name: airflow
    app.kubernetes.io/component: ml-workflows
---
apiVersion: v1
kind: Secret
metadata:
  name: airflow-secrets
  namespace: airflow
type: Opaque
data:
  # Base64 encoded values - replace with actual values
  postgres-password: YWlyZmxvdy1wYXNzd29yZA==  # airflow-password
  redis-password: cmVkaXMtcGFzc3dvcmQ=  # redis-password
  fernet-key: VGhpc0lzQUZlcm5ldEtleUZvckFpcmZsb3cxMjM0NTY3ODk=  # 32-byte base64 encoded key
  webserver-secret-key: V2Vic2VydmVyU2VjcmV0S2V5Rm9yQWlyZmxvdw==  # WebserverSecretKeyForAirflow
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: airflow-config
  namespace: airflow
data:
  airflow.cfg: |
    [core]
    dags_folder = /opt/airflow/dags
    hostname_callable = airflow.utils.net.getfqdn
    default_timezone = utc
    executor = KubernetesExecutor
    sql_alchemy_conn = *******************************************************/airflow
    load_examples = False
    donot_pickle = True
    dagbag_import_timeout = 30
    parallelism = 32
    dag_concurrency = 16
    max_active_runs_per_dag = 16
    max_active_tasks_per_dag = 16
    
    [kubernetes]
    namespace = airflow
    airflow_configmap = airflow-config
    worker_container_repository = apache/airflow
    worker_container_tag = 2.7.3-python3.9
    worker_service_account_name = airflow-worker
    image_pull_policy = IfNotPresent
    delete_worker_pods = True
    delete_worker_pods_on_failure = False
    worker_pods_creation_batch_size = 1
    
    [kubernetes_executor]
    namespace = airflow
    multi_namespace_mode = False
    pod_template_file = /opt/airflow/pod_template.yaml
    worker_container_repository = apache/airflow
    worker_container_tag = 2.7.3-python3.9
    worker_service_account_name = airflow-worker
    
    [scheduler]
    job_heartbeat_sec = 5
    scheduler_heartbeat_sec = 5
    run_duration = -1
    min_file_process_interval = 0
    dag_dir_list_interval = 300
    print_stats_interval = 30
    child_process_log_directory = /opt/airflow/logs/scheduler
    scheduler_zombie_task_threshold = 300
    catchup_by_default = False
    max_dagruns_to_create_per_loop = 10
    max_dagruns_per_loop_to_schedule = 20
    
    [webserver]
    base_url = https://airflow.company.com
    web_server_port = 8080
    web_server_host = 0.0.0.0
    secret_key = $(WEBSERVER_SECRET_KEY)
    workers = 4
    worker_refresh_batch_size = 1
    worker_refresh_interval = 6000
    reload_on_plugin_change = False
    expose_config = True
    authenticate = True
    auth_backend = airflow.contrib.auth.backends.password_auth
    
    [celery]
    broker_url = redis://:$(REDIS_PASSWORD)@redis:6379/1
    result_backend = db+*******************************************************/airflow
    flower_host = 0.0.0.0
    flower_port = 5555
    
    [api]
    auth_backend = airflow.api.auth.backend.basic_auth
    enable_experimental_api = True
    
    [logging]
    logging_level = INFO
    fab_logging_level = WARN
    log_format = [%%(asctime)s] {%%(filename)s:%%(lineno)d} %%(levelname)s - %%(message)s
    simple_log_format = %%(asctime)s %%(levelname)s - %%(message)s
    
    [metrics]
    statsd_on = True
    statsd_host = statsd-exporter
    statsd_port = 9125
    statsd_prefix = airflow
    
    [email]
    email_backend = airflow.providers.sendgrid.utils.emailer.send_email
    
  pod_template.yaml: |
    apiVersion: v1
    kind: Pod
    metadata:
      name: airflow-worker-template
      namespace: airflow
    spec:
      serviceAccountName: airflow-worker
      restartPolicy: Never
      containers:
      - name: base
        image: apache/airflow:2.7.3-python3.9
        command: []
        args: []
        env:
        - name: AIRFLOW__CORE__EXECUTOR
          value: KubernetesExecutor
        - name: AIRFLOW__CORE__SQL_ALCHEMY_CONN
          value: *******************************************************/airflow
        - name: AIRFLOW__CELERY__BROKER_URL
          value: redis://:$(REDIS_PASSWORD)@redis:6379/1
        - name: AIRFLOW__CELERY__RESULT_BACKEND
          value: db+*******************************************************/airflow
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: airflow-secrets
              key: postgres-password
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: airflow-secrets
              key: redis-password
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        volumeMounts:
        - name: airflow-dags
          mountPath: /opt/airflow/dags
          readOnly: True
        - name: airflow-logs
          mountPath: /opt/airflow/logs
      volumes:
      - name: airflow-dags
        persistentVolumeClaim:
          claimName: airflow-dags-pvc
      - name: airflow-logs
        persistentVolumeClaim:
          claimName: airflow-logs-pvc
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres
  namespace: airflow
  labels:
    app: postgres
    component: database
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
      - name: postgres
        image: postgres:14-alpine
        ports:
        - containerPort: 5432
        env:
        - name: POSTGRES_DB
          value: airflow
        - name: POSTGRES_USER
          value: airflow
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: airflow-secrets
              key: postgres-password
        - name: PGDATA
          value: /var/lib/postgresql/data/pgdata
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        livenessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - airflow
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - airflow
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: postgres-storage
        persistentVolumeClaim:
          claimName: postgres-pvc
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: airflow
  labels:
    app: redis
    component: message-broker
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        ports:
        - containerPort: 6379
        command:
        - redis-server
        - --requirepass
        - $(REDIS_PASSWORD)
        env:
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: airflow-secrets
              key: redis-password
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          exec:
            command:
            - redis-cli
            - -a
            - $(REDIS_PASSWORD)
            - ping
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - redis-cli
            - -a
            - $(REDIS_PASSWORD)
            - ping
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: airflow-webserver
  namespace: airflow
  labels:
    app: airflow-webserver
    component: webserver
spec:
  replicas: 2
  selector:
    matchLabels:
      app: airflow-webserver
  template:
    metadata:
      labels:
        app: airflow-webserver
    spec:
      serviceAccountName: airflow-webserver
      containers:
      - name: airflow-webserver
        image: apache/airflow:2.7.3-python3.9
        ports:
        - containerPort: 8080
        command:
        - airflow
        - webserver
        env:
        - name: AIRFLOW__CORE__EXECUTOR
          value: KubernetesExecutor
        - name: AIRFLOW__CORE__SQL_ALCHEMY_CONN
          value: *******************************************************/airflow
        - name: AIRFLOW__CELERY__BROKER_URL
          value: redis://:$(REDIS_PASSWORD)@redis:6379/1
        - name: AIRFLOW__CELERY__RESULT_BACKEND
          value: db+*******************************************************/airflow
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: airflow-secrets
              key: postgres-password
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: airflow-secrets
              key: redis-password
        - name: WEBSERVER_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: airflow-secrets
              key: webserver-secret-key
        - name: AIRFLOW__CORE__FERNET_KEY
          valueFrom:
            secretKeyRef:
              name: airflow-secrets
              key: fernet-key
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 120
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 10
        volumeMounts:
        - name: airflow-config
          mountPath: /opt/airflow/airflow.cfg
          subPath: airflow.cfg
          readOnly: true
        - name: airflow-config
          mountPath: /opt/airflow/pod_template.yaml
          subPath: pod_template.yaml
          readOnly: true
        - name: airflow-dags
          mountPath: /opt/airflow/dags
          readOnly: true
        - name: airflow-logs
          mountPath: /opt/airflow/logs
      volumes:
      - name: airflow-config
        configMap:
          name: airflow-config
      - name: airflow-dags
        persistentVolumeClaim:
          claimName: airflow-dags-pvc
      - name: airflow-logs
        persistentVolumeClaim:
          claimName: airflow-logs-pvc
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: airflow-scheduler
  namespace: airflow
  labels:
    app: airflow-scheduler
    component: scheduler
spec:
  replicas: 1
  selector:
    matchLabels:
      app: airflow-scheduler
  template:
    metadata:
      labels:
        app: airflow-scheduler
    spec:
      serviceAccountName: airflow-scheduler
      containers:
      - name: airflow-scheduler
        image: apache/airflow:2.7.3-python3.9
        command:
        - airflow
        - scheduler
        env:
        - name: AIRFLOW__CORE__EXECUTOR
          value: KubernetesExecutor
        - name: AIRFLOW__CORE__SQL_ALCHEMY_CONN
          value: *******************************************************/airflow
        - name: AIRFLOW__CELERY__BROKER_URL
          value: redis://:$(REDIS_PASSWORD)@redis:6379/1
        - name: AIRFLOW__CELERY__RESULT_BACKEND
          value: db+*******************************************************/airflow
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: airflow-secrets
              key: postgres-password
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: airflow-secrets
              key: redis-password
        - name: AIRFLOW__CORE__FERNET_KEY
          valueFrom:
            secretKeyRef:
              name: airflow-secrets
              key: fernet-key
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          exec:
            command:
            - python
            - -c
            - "import airflow.jobs.scheduler_job; print('Scheduler is running')"
          initialDelaySeconds: 120
          periodSeconds: 60
        volumeMounts:
        - name: airflow-config
          mountPath: /opt/airflow/airflow.cfg
          subPath: airflow.cfg
          readOnly: true
        - name: airflow-config
          mountPath: /opt/airflow/pod_template.yaml
          subPath: pod_template.yaml
          readOnly: true
        - name: airflow-dags
          mountPath: /opt/airflow/dags
          readOnly: true
        - name: airflow-logs
          mountPath: /opt/airflow/logs
      volumes:
      - name: airflow-config
        configMap:
          name: airflow-config
      - name: airflow-dags
        persistentVolumeClaim:
          claimName: airflow-dags-pvc
      - name: airflow-logs
        persistentVolumeClaim:
          claimName: airflow-logs-pvc
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgres-pvc
  namespace: airflow
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: ml-high-performance
  resources:
    requests:
      storage: 20Gi
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: airflow-dags-pvc
  namespace: airflow
spec:
  accessModes:
    - ReadWriteMany
  storageClassName: ml-dataset-storage
  resources:
    requests:
      storage: 10Gi
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: airflow-logs-pvc
  namespace: airflow
spec:
  accessModes:
    - ReadWriteMany
  storageClassName: ml-dataset-storage
  resources:
    requests:
      storage: 50Gi
---
apiVersion: v1
kind: Service
metadata:
  name: postgres
  namespace: airflow
  labels:
    app: postgres
spec:
  type: ClusterIP
  ports:
  - port: 5432
    targetPort: 5432
    protocol: TCP
  selector:
    app: postgres
---
apiVersion: v1
kind: Service
metadata:
  name: redis
  namespace: airflow
  labels:
    app: redis
spec:
  type: ClusterIP
  ports:
  - port: 6379
    targetPort: 6379
    protocol: TCP
  selector:
    app: redis
---
apiVersion: v1
kind: Service
metadata:
  name: airflow-webserver
  namespace: airflow
  labels:
    app: airflow-webserver
spec:
  type: ClusterIP
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
    name: http
  selector:
    app: airflow-webserver
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: airflow-webserver
  namespace: airflow
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::ACCOUNT_ID:role/AirflowWebserverRole
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: airflow-scheduler
  namespace: airflow
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::ACCOUNT_ID:role/AirflowSchedulerRole
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: airflow-worker
  namespace: airflow
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::ACCOUNT_ID:role/AirflowWorkerRole
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: airflow-scheduler
rules:
- apiGroups: [""]
  resources: ["pods", "services", "endpoints", "persistentvolumeclaims", "events", "configmaps", "secrets"]
  verbs: ["*"]
- apiGroups: ["apps"]
  resources: ["deployments", "daemonsets", "replicasets", "statefulsets"]
  verbs: ["*"]
- apiGroups: ["extensions"]
  resources: ["deployments", "daemonsets", "replicasets"]
  verbs: ["*"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: airflow-scheduler
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: airflow-scheduler
subjects:
- kind: ServiceAccount
  name: airflow-scheduler
  namespace: airflow
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: airflow-worker
  namespace: airflow
rules:
- apiGroups: [""]
  resources: ["pods", "persistentvolumeclaims", "configmaps", "secrets"]
  verbs: ["get", "list", "create", "update", "delete"]
- apiGroups: [""]
  resources: ["pods/log"]
  verbs: ["get", "list"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: airflow-worker
  namespace: airflow
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: airflow-worker
subjects:
- kind: ServiceAccount
  name: airflow-worker
  namespace: airflow
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: airflow-ingress
  namespace: airflow
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/auth-type: basic
    nginx.ingress.kubernetes.io/auth-secret: airflow-basic-auth
    nginx.ingress.kubernetes.io/auth-realm: 'Airflow Authentication Required'
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
spec:
  tls:
  - hosts:
    - airflow.company.com
    secretName: airflow-tls
  rules:
  - host: airflow.company.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: airflow-webserver
            port:
              number: 8080
---
apiVersion: v1
kind: Secret
metadata:
  name: airflow-basic-auth
  namespace: airflow
type: Opaque
data:
  auth: YWlyZmxvdzokYXByMSRlTk4wVTdIMyRiYWRsdzlMcG1zWXZhZFZtNFlpb0ox  # airflow:airflow123
---
apiVersion: batch/v1
kind: Job
metadata:
  name: airflow-db-init
  namespace: airflow
spec:
  template:
    spec:
      restartPolicy: OnFailure
      serviceAccountName: airflow-scheduler
      containers:
      - name: db-init
        image: apache/airflow:2.7.3-python3.9
        command:
        - bash
        - -c
        - |
          set -e
          echo "Initializing Airflow database..."
          airflow db init
          echo "Creating admin user..."
          airflow users create \
            --username admin \
            --firstname Admin \
            --lastname User \
            --role Admin \
            --email <EMAIL> \
            --password admin123
          echo "Database initialization completed"
        env:
        - name: AIRFLOW__CORE__EXECUTOR
          value: KubernetesExecutor
        - name: AIRFLOW__CORE__SQL_ALCHEMY_CONN
          value: *******************************************************/airflow
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: airflow-secrets
              key: postgres-password
        - name: AIRFLOW__CORE__FERNET_KEY
          valueFrom:
            secretKeyRef:
              name: airflow-secrets
              key: fernet-key
        volumeMounts:
        - name: airflow-config
          mountPath: /opt/airflow/airflow.cfg
          subPath: airflow.cfg
          readOnly: true
      volumes:
      - name: airflow-config
        configMap:
          name: airflow-config
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: airflow-webserver-pdb
  namespace: airflow
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: airflow-webserver
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: airflow-sample-dags
  namespace: airflow
data:
  ml_training_pipeline.py: |
    from datetime import datetime, timedelta
    from airflow import DAG
    from airflow.providers.kubernetes.operators.kubernetes_pod import KubernetesPodOperator
    from airflow.operators.python_operator import PythonOperator
    import mlflow
    
    default_args = {
        'owner': 'ml-team',
        'depends_on_past': False,
        'start_date': datetime(2024, 1, 1),
        'email_on_failure': True,
        'email_on_retry': False,
        'retries': 1,
        'retry_delay': timedelta(minutes=5),
    }
    
    dag = DAG(
        'ml_training_pipeline',
        default_args=default_args,
        description='ML model training and deployment pipeline',
        schedule_interval=timedelta(days=1),
        catchup=False,
        tags=['ml', 'training', 'production'],
    )
    
    # Data preparation task
    data_prep = KubernetesPodOperator(
        task_id='data_preparation',
        name='data-preparation',
        namespace='airflow',
        image='ecommerce-analytics/ml-data-prep:latest',
        image_pull_policy='Always',
        cmds=['/bin/bash'],
        arguments=['-c', 'python /app/data_preparation.py'],
        env_vars={
            'MLFLOW_TRACKING_URI': 'http://mlflow-server.mlflow:5000',
            'DATA_SOURCE': 's3://ecommerce-analytics-data/raw/',
            'OUTPUT_PATH': 's3://ecommerce-analytics-data/processed/'
        },
        resources={
            'request_memory': '2Gi',
            'request_cpu': '1000m',
            'limit_memory': '4Gi',
            'limit_cpu': '2000m'
        },
        dag=dag,
    )
    
    # Model training task
    model_training = KubernetesPodOperator(
        task_id='model_training',
        name='model-training',
        namespace='airflow',
        image='ecommerce-analytics/ml-training:latest',
        image_pull_policy='Always',
        cmds=['/bin/bash'],
        arguments=['-c', 'python /app/train_model.py'],
        env_vars={
            'MLFLOW_TRACKING_URI': 'http://mlflow-server.mlflow:5000',
            'EXPERIMENT_NAME': 'customer-churn-prediction',
            'DATA_PATH': 's3://ecommerce-analytics-data/processed/',
        },
        resources={
            'request_memory': '4Gi',
            'request_cpu': '2000m',
            'limit_memory': '8Gi',
            'limit_cpu': '4000m'
        },
        dag=dag,
    )
    
    # Model evaluation task
    model_evaluation = KubernetesPodOperator(
        task_id='model_evaluation',
        name='model-evaluation',
        namespace='airflow',
        image='ecommerce-analytics/ml-evaluation:latest',
        image_pull_policy='Always',
        cmds=['/bin/bash'],
        arguments=['-c', 'python /app/evaluate_model.py'],
        env_vars={
            'MLFLOW_TRACKING_URI': 'http://mlflow-server.mlflow:5000',
            'MODEL_NAME': 'customer-churn-predictor',
            'VALIDATION_DATA': 's3://ecommerce-analytics-data/validation/',
        },
        resources={
            'request_memory': '2Gi',
            'request_cpu': '1000m',
            'limit_memory': '4Gi',
            'limit_cpu': '2000m'
        },
        dag=dag,
    )
    
    # Model deployment task
    def deploy_model(**context):
        """Deploy model to production if evaluation metrics are satisfactory"""
        import mlflow
        from mlflow.tracking import MlflowClient
        
        client = MlflowClient()
        model_name = "customer-churn-predictor"
        
        # Get latest model version
        latest_version = client.get_latest_versions(model_name, stages=["Staging"])
        
        if latest_version:
            version = latest_version[0].version
            # Add model validation logic here
            # If validation passes, promote to production
            client.transition_model_version_stage(
                name=model_name,
                version=version,
                stage="Production"
            )
            print(f"Model {model_name} v{version} deployed to production")
        else:
            raise Exception("No model found in staging")
    
    model_deployment = PythonOperator(
        task_id='model_deployment',
        python_callable=deploy_model,
        dag=dag,
    )
    
    # Define task dependencies
    data_prep >> model_training >> model_evaluation >> model_deployment
    
  feature_engineering_pipeline.py: |
    from datetime import datetime, timedelta
    from airflow import DAG
    from airflow.providers.kubernetes.operators.kubernetes_pod import KubernetesPodOperator
    from airflow.operators.python_operator import PythonOperator
    
    default_args = {
        'owner': 'data-team',
        'depends_on_past': False,
        'start_date': datetime(2024, 1, 1),
        'email_on_failure': True,
        'email_on_retry': False,
        'retries': 2,
        'retry_delay': timedelta(minutes=5),
    }
    
    dag = DAG(
        'feature_engineering_pipeline',
        default_args=default_args,
        description='Feature engineering and feature store update pipeline',
        schedule_interval=timedelta(hours=6),
        catchup=False,
        tags=['features', 'engineering', 'feast'],
    )
    
    # Extract features from analytics events
    extract_user_features = KubernetesPodOperator(
        task_id='extract_user_features',
        name='extract-user-features',
        namespace='airflow',
        image='ecommerce-analytics/feature-engineering:latest',
        image_pull_policy='Always',
        cmds=['/bin/bash'],
        arguments=['-c', 'python /app/extract_user_features.py'],
        env_vars={
            'DB_HOST': 'analytics-db.cluster-xyz.us-east-1.rds.amazonaws.com',
            'FEAST_REPO': '/feast/feature_repo',
            'FEATURE_TYPE': 'user_features'
        },
        dag=dag,
    )
    
    # Extract product features
    extract_product_features = KubernetesPodOperator(
        task_id='extract_product_features',
        name='extract-product-features',
        namespace='airflow',
        image='ecommerce-analytics/feature-engineering:latest',
        image_pull_policy='Always',
        cmds=['/bin/bash'],
        arguments=['-c', 'python /app/extract_product_features.py'],
        env_vars={
            'DB_HOST': 'analytics-db.cluster-xyz.us-east-1.rds.amazonaws.com',
            'FEAST_REPO': '/feast/feature_repo',
            'FEATURE_TYPE': 'product_features'
        },
        dag=dag,
    )
    
    # Update feature store
    update_feature_store = KubernetesPodOperator(
        task_id='update_feature_store',
        name='update-feature-store',
        namespace='airflow',
        image='ecommerce-analytics/feast-client:latest',
        image_pull_policy='Always',
        cmds=['/bin/bash'],
        arguments=['-c', 'feast apply && feast materialize-incremental $(date -d "1 day ago" +%Y-%m-%d)'],
        env_vars={
            'FEAST_REPO': '/feast/feature_repo',
        },
        dag=dag,
    )
    
    # Validate feature freshness
    def validate_feature_freshness(**context):
        """Validate that features are fresh and within expected ranges"""
        # Add feature validation logic here
        print("Feature freshness validation completed")
        return True
    
    validate_features = PythonOperator(
        task_id='validate_features',
        python_callable=validate_feature_freshness,
        dag=dag,
    )
    
    # Task dependencies
    [extract_user_features, extract_product_features] >> update_feature_store >> validate_features