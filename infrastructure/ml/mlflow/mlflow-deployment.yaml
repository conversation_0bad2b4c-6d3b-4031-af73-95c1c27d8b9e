apiVersion: v1
kind: Namespace
metadata:
  name: mlflow
  labels:
    app.kubernetes.io/name: mlflow
    app.kubernetes.io/component: ml-platform
---
apiVersion: v1
kind: Secret
metadata:
  name: mlflow-secrets
  namespace: mlflow
type: Opaque
data:
  # Base64 encoded values - replace with actual values
  postgres-password: bWxmbG93LXBhc3N3b3Jk  # mlflow-password
  s3-access-key: YWNjZXNzLWtleQ==  # access-key
  s3-secret-key: c2VjcmV0LWtleQ==  # secret-key
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: mlflow-config
  namespace: mlflow
data:
  mlflow.conf: |
    # MLflow Configuration
    backend_store_uri: ******************************************************/mlflow
    default_artifact_root: s3://ecommerce-analytics-mlflow-artifacts
    host: 0.0.0.0
    port: "5000"
    workers: "4"
    static_prefix: /mlflow
  init.sql: |
    CREATE DATABASE IF NOT EXISTS mlflow;
    CREATE USER IF NOT EXISTS 'mlflow'@'%' IDENTIFIED BY '$(POSTGRES_PASSWORD)';
    GRANT ALL PRIVILEGES ON mlflow.* TO 'mlflow'@'%';
    FLUSH PRIVILEGES;
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres
  namespace: mlflow
  labels:
    app: postgres
    component: database
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
      - name: postgres
        image: postgres:14-alpine
        ports:
        - containerPort: 5432
        env:
        - name: POSTGRES_DB
          value: mlflow
        - name: POSTGRES_USER
          value: mlflow
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: mlflow-secrets
              key: postgres-password
        - name: PGDATA
          value: /var/lib/postgresql/data/pgdata
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - mlflow
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - mlflow
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: postgres-storage
        persistentVolumeClaim:
          claimName: postgres-pvc
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgres-pvc
  namespace: mlflow
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: ml-high-performance
  resources:
    requests:
      storage: 20Gi
---
apiVersion: v1
kind: Service
metadata:
  name: postgres
  namespace: mlflow
  labels:
    app: postgres
spec:
  type: ClusterIP
  ports:
  - port: 5432
    targetPort: 5432
    protocol: TCP
  selector:
    app: postgres
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mlflow-server
  namespace: mlflow
  labels:
    app: mlflow-server
    component: tracking-server
spec:
  replicas: 2
  selector:
    matchLabels:
      app: mlflow-server
  template:
    metadata:
      labels:
        app: mlflow-server
    spec:
      serviceAccountName: mlflow-service-account
      containers:
      - name: mlflow-server
        image: mlflow/mlflow:2.8.1
        ports:
        - containerPort: 5000
          name: http
        command:
        - mlflow
        - server
        args:
        - --backend-store-uri
        - ******************************************************/mlflow
        - --default-artifact-root
        - s3://ecommerce-analytics-mlflow-artifacts
        - --host
        - 0.0.0.0
        - --port
        - "5000"
        - --workers
        - "4"
        - --static-prefix
        - /mlflow
        env:
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: mlflow-secrets
              key: postgres-password
        - name: AWS_ACCESS_KEY_ID
          valueFrom:
            secretKeyRef:
              name: mlflow-secrets
              key: s3-access-key
        - name: AWS_SECRET_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: mlflow-secrets
              key: s3-secret-key
        - name: AWS_DEFAULT_REGION
          value: us-east-1
        - name: MLFLOW_S3_ENDPOINT_URL
          value: https://s3.amazonaws.com
        - name: MLFLOW_S3_IGNORE_TLS
          value: "false"
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 5000
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /health
            port: 5000
          initialDelaySeconds: 30
          periodSeconds: 10
        volumeMounts:
        - name: mlflow-config
          mountPath: /opt/mlflow/mlflow.conf
          subPath: mlflow.conf
          readOnly: true
      volumes:
      - name: mlflow-config
        configMap:
          name: mlflow-config
---
apiVersion: v1
kind: Service
metadata:
  name: mlflow-server
  namespace: mlflow
  labels:
    app: mlflow-server
spec:
  type: ClusterIP
  ports:
  - port: 5000
    targetPort: 5000
    protocol: TCP
    name: http
  selector:
    app: mlflow-server
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: mlflow-service-account
  namespace: mlflow
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::ACCOUNT_ID:role/MLflowServiceRole
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: mlflow-ingress
  namespace: mlflow
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/rewrite-target: /$2
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/auth-type: basic
    nginx.ingress.kubernetes.io/auth-secret: mlflow-basic-auth
    nginx.ingress.kubernetes.io/auth-realm: 'MLflow Authentication Required'
spec:
  tls:
  - hosts:
    - mlflow.company.com
    secretName: mlflow-tls
  rules:
  - host: mlflow.company.com
    http:
      paths:
      - path: /mlflow(/|$)(.*)
        pathType: Prefix
        backend:
          service:
            name: mlflow-server
            port:
              number: 5000
---
apiVersion: v1
kind: Secret
metadata:
  name: mlflow-basic-auth
  namespace: mlflow
type: Opaque
data:
  auth: bWxmbG93OiRhcHIxJGVOTjBVN0gzJGJhZGx3OUxwbXNZdmFkVm00WWlvSjE=  # mlflow:mlflow123
---
apiVersion: batch/v1
kind: Job
metadata:
  name: mlflow-db-migration
  namespace: mlflow
spec:
  template:
    spec:
      restartPolicy: OnFailure
      containers:
      - name: db-migration
        image: mlflow/mlflow:2.8.1
        command:
        - mlflow
        - db
        - upgrade
        - ******************************************************/mlflow
        env:
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: mlflow-secrets
              key: postgres-password
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: mlflow-model-registry-config
  namespace: mlflow
data:
  registry.py: |
    import mlflow
    from mlflow.tracking import MlflowClient
    
    # MLflow Model Registry Configuration
    client = MlflowClient()
    
    # Create registered models for common use cases
    models = [
        {
            "name": "customer-churn-predictor",
            "description": "Predicts customer churn probability based on usage patterns"
        },
        {
            "name": "sales-forecasting-model",
            "description": "Forecasts sales revenue and volume for next 30 days"
        },
        {
            "name": "product-recommendation-engine",
            "description": "Recommends products based on customer behavior and preferences"
        },
        {
            "name": "price-optimization-model",
            "description": "Optimizes product pricing based on market conditions"
        },
        {
            "name": "anomaly-detection-model",
            "description": "Detects unusual patterns in customer behavior and transactions"
        }
    ]
    
    for model in models:
        try:
            client.create_registered_model(
                name=model["name"],
                description=model["description"]
            )
            print(f"Created registered model: {model['name']}")
        except Exception as e:
            print(f"Model {model['name']} already exists or error: {e}")
---
apiVersion: batch/v1
kind: Job
metadata:
  name: mlflow-model-registry-init
  namespace: mlflow
  labels:
    app: mlflow-init
spec:
  template:
    spec:
      restartPolicy: OnFailure
      serviceAccountName: mlflow-service-account
      containers:
      - name: model-registry-init
        image: mlflow/mlflow:2.8.1
        command: ["/bin/bash"]
        args:
        - -c
        - |
          set -e
          echo "Waiting for MLflow server to be ready..."
          until curl -f http://mlflow-server:5000/health; do
            echo "MLflow server not ready, waiting..."
            sleep 10
          done
          echo "MLflow server is ready, initializing model registry..."
          export MLFLOW_TRACKING_URI=http://mlflow-server:5000
          python /opt/config/registry.py
        env:
        - name: AWS_ACCESS_KEY_ID
          valueFrom:
            secretKeyRef:
              name: mlflow-secrets
              key: s3-access-key
        - name: AWS_SECRET_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: mlflow-secrets
              key: s3-secret-key
        - name: AWS_DEFAULT_REGION
          value: us-east-1
        volumeMounts:
        - name: registry-config
          mountPath: /opt/config
      volumes:
      - name: registry-config
        configMap:
          name: mlflow-model-registry-config
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: mlflow-server-pdb
  namespace: mlflow
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: mlflow-server
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: mlflow-monitoring
  namespace: mlflow
data:
  prometheus-config.yml: |
    global:
      scrape_interval: 15s
      evaluation_interval: 15s
    
    scrape_configs:
    - job_name: 'mlflow-server'
      static_configs:
      - targets: ['mlflow-server:5000']
      metrics_path: /metrics
      scrape_interval: 30s
    
    - job_name: 'postgres'
      static_configs:
      - targets: ['postgres:5432']
      scrape_interval: 60s
  
  grafana-dashboard.json: |
    {
      "dashboard": {
        "id": null,
        "title": "MLflow Monitoring Dashboard",
        "tags": ["mlflow", "ml-platform"],
        "timezone": "browser",
        "panels": [
          {
            "id": 1,
            "title": "MLflow Server Requests",
            "type": "graph",
            "targets": [
              {
                "expr": "rate(mlflow_requests_total[5m])",
                "legendFormat": "Requests/sec"
              }
            ]
          },
          {
            "id": 2,
            "title": "Model Registry Operations",
            "type": "graph",
            "targets": [
              {
                "expr": "mlflow_model_registry_operations_total",
                "legendFormat": "Registry Operations"
              }
            ]
          },
          {
            "id": 3,
            "title": "Database Connections",
            "type": "singlestat",
            "targets": [
              {
                "expr": "postgres_connections_active",
                "legendFormat": "Active Connections"
              }
            ]
          }
        ],
        "time": {
          "from": "now-1h",
          "to": "now"
        },
        "refresh": "5s"
      }
    }