#!/usr/bin/env python3
"""
A/B Testing Framework for ML Model Deployment
Advanced framework for gradual model rollout with statistical significance testing
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import os
import sys
import json
import hashlib
from typing import Dict, List, Tuple, Optional, Any, Union
from dataclasses import dataclass, asdict
from enum import Enum
import asyncio
import aiohttp

# Statistical libraries
from scipy import stats
from statsmodels.stats.proportion import proportions_ztest
from statsmodels.stats.contingency_tables import mcnemar
import bayesian_testing as bt

# ML and model serving
import mlflow
from mlflow.tracking import MlflowClient
import joblib
import pickle

# Infrastructure
from kubernetes import client, config as k8s_config
import redis
import boto3
from sqlalchemy import create_engine, text

# Monitoring
import prometheus_client
from prometheus_client import CollectorRegistry, Gauge, Counter, Histogram

# Visualization
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.express as px

# Configuration
class ExperimentStatus(Enum):
    DRAFT = "draft"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    STOPPED = "stopped"

class SignificanceTest(Enum):
    FREQUENTIST = "frequentist"
    BAYESIAN = "bayesian"
    SEQUENTIAL = "sequential"

@dataclass
class ABTestConfig:
    # Experiment configuration
    experiment_name: str
    model_type: str
    control_model_version: str
    treatment_model_version: str
    
    # Traffic allocation
    traffic_allocation: Dict[str, float]  # {"control": 0.8, "treatment": 0.2}
    ramp_up_schedule: Optional[Dict[str, float]] = None  # Day-by-day ramp up
    
    # Success metrics
    primary_metric: str
    secondary_metrics: List[str]
    minimum_effect_size: float
    significance_level: float = 0.05
    statistical_power: float = 0.8
    
    # Business constraints
    max_runtime_days: int = 30
    min_sample_size_per_variant: int = 1000
    early_stopping_enabled: bool = True
    guardrail_metrics: Dict[str, Dict] = None  # {"error_rate": {"threshold": 0.05, "direction": "lower"}}
    
    # Infrastructure
    kubernetes_namespace: str = "ml-serving"
    redis_host: str = "redis-cluster"
    redis_port: int = 6379
    
    # MLflow configuration
    mlflow_tracking_uri: str = os.getenv('MLFLOW_TRACKING_URI', 'http://mlflow-server.mlflow:5000')
    
    # Database connections
    db_host: str = os.getenv('DB_HOST', 'analytics-db.cluster-xyz.us-east-1.rds.amazonaws.com')
    db_name: str = os.getenv('DB_NAME', 'ecommerce_analytics')
    db_user: str = os.getenv('DB_USER', 'analytics_user')
    db_password: str = os.getenv('DB_PASSWORD', 'analytics_password')
    
    # Monitoring
    prometheus_gateway: str = os.getenv('PROMETHEUS_GATEWAY', 'prometheus-pushgateway:9091')
    slack_webhook: str = os.getenv('SLACK_WEBHOOK', '')
    
    def __post_init__(self):
        if self.guardrail_metrics is None:
            self.guardrail_metrics = {
                "error_rate": {"threshold": 0.05, "direction": "lower"},
                "response_time_p95": {"threshold": 1000, "direction": "lower"},
                "throughput": {"threshold": 0.9, "direction": "higher"}
            }

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ABTestFramework:
    def __init__(self, config: ABTestConfig):
        self.config = config
        self.db_engine = self._create_db_engine()
        self.redis_client = self._create_redis_client()
        self.s3_client = boto3.client('s3')
        
        # Setup Kubernetes
        self._setup_kubernetes()
        
        # Initialize MLflow
        mlflow.set_tracking_uri(config.mlflow_tracking_uri)
        self.mlflow_client = MlflowClient()
        
        # Monitoring
        self.prometheus_registry = CollectorRegistry()
        self._setup_prometheus_metrics()
        
        # Experiment state
        self.experiment_state = {
            'status': ExperimentStatus.DRAFT,
            'start_time': None,
            'end_time': None,
            'current_allocation': self.config.traffic_allocation.copy(),
            'total_samples': {'control': 0, 'treatment': 0},
            'results': {},
            'decisions': []
        }
        
        # Statistical testing
        self.statistical_tests = {
            SignificanceTest.FREQUENTIST: self._frequentist_test,
            SignificanceTest.BAYESIAN: self._bayesian_test,
            SignificanceTest.SEQUENTIAL: self._sequential_test
        }
        
    def _create_db_engine(self):
        """Create database engine for PostgreSQL"""
        connection_string = f"postgresql://{self.config.db_user}:{self.config.db_password}@{self.config.db_host}/{self.config.db_name}"
        return create_engine(connection_string)
    
    def _create_redis_client(self):
        """Create Redis client for experiment assignment"""
        return redis.Redis(
            host=self.config.redis_host,
            port=self.config.redis_port,
            decode_responses=True,
            socket_connect_timeout=5,
            socket_timeout=5
        )
    
    def _setup_kubernetes(self):
        """Setup Kubernetes client"""
        try:
            k8s_config.load_incluster_config()
        except k8s_config.ConfigException:
            k8s_config.load_kube_config()
        
        self.k8s_apps_v1 = client.AppsV1Api()
        self.k8s_core_v1 = client.CoreV1Api()
    
    def _setup_prometheus_metrics(self):
        """Setup Prometheus metrics for monitoring"""
        self.experiment_requests = Counter(
            'ab_test_requests_total',
            'Total requests per experiment variant',
            ['experiment_name', 'variant'],
            registry=self.prometheus_registry
        )
        
        self.experiment_conversions = Counter(
            'ab_test_conversions_total',
            'Total conversions per experiment variant',
            ['experiment_name', 'variant', 'metric'],
            registry=self.prometheus_registry
        )
        
        self.experiment_response_time = Histogram(
            'ab_test_response_time_seconds',
            'Response time distribution per variant',
            ['experiment_name', 'variant'],
            registry=self.prometheus_registry
        )
        
        self.experiment_statistical_power = Gauge(
            'ab_test_statistical_power',
            'Current statistical power of the experiment',
            ['experiment_name'],
            registry=self.prometheus_registry
        )
        
        self.experiment_significance = Gauge(
            'ab_test_significance_level',
            'Current p-value of the experiment',
            ['experiment_name', 'metric'],
            registry=self.prometheus_registry
        )
    
    def create_experiment_infrastructure(self):
        """Create Kubernetes infrastructure for A/B testing"""
        logger.info(f"Creating A/B test infrastructure for {self.config.experiment_name}")
        
        # Create deployment for control model
        self._create_model_deployment("control", self.config.control_model_version)
        
        # Create deployment for treatment model
        self._create_model_deployment("treatment", self.config.treatment_model_version)
        
        # Create traffic splitting service
        self._create_traffic_splitter()
        
        # Create experiment tracking resources
        self._create_experiment_tracker()
        
        logger.info("A/B test infrastructure created successfully")
    
    def _create_model_deployment(self, variant: str, model_version: str):
        """Create Kubernetes deployment for a model variant"""
        deployment_name = f"{self.config.experiment_name}-{variant}"
        
        deployment_spec = {
            'apiVersion': 'apps/v1',
            'kind': 'Deployment',
            'metadata': {
                'name': deployment_name,
                'namespace': self.config.kubernetes_namespace,
                'labels': {
                    'app': deployment_name,
                    'experiment': self.config.experiment_name,
                    'variant': variant,
                    'model-type': self.config.model_type
                }
            },
            'spec': {
                'replicas': 3,  # Start with 3 replicas
                'selector': {
                    'matchLabels': {
                        'app': deployment_name,
                        'variant': variant
                    }
                },
                'template': {
                    'metadata': {
                        'labels': {
                            'app': deployment_name,
                            'experiment': self.config.experiment_name,
                            'variant': variant,
                            'model-type': self.config.model_type
                        }
                    },
                    'spec': {
                        'serviceAccountName': 'ml-serving-service-account',
                        'containers': [{
                            'name': 'model-server',
                            'image': 'ecommerce-analytics/ml-serving:latest',
                            'ports': [{'containerPort': 8080}],
                            'env': [
                                {'name': 'MODEL_NAME', 'value': self.config.model_type},
                                {'name': 'MODEL_VERSION', 'value': model_version},
                                {'name': 'VARIANT', 'value': variant},
                                {'name': 'EXPERIMENT_NAME', 'value': self.config.experiment_name},
                                {'name': 'MLFLOW_TRACKING_URI', 'value': self.config.mlflow_tracking_uri},
                                {'name': 'REDIS_HOST', 'value': self.config.redis_host},
                                {'name': 'REDIS_PORT', 'value': str(self.config.redis_port)}
                            ],
                            'resources': {
                                'requests': {
                                    'cpu': '500m',
                                    'memory': '1Gi'
                                },
                                'limits': {
                                    'cpu': '2000m',
                                    'memory': '4Gi'
                                }
                            },
                            'livenessProbe': {
                                'httpGet': {
                                    'path': '/health',
                                    'port': 8080
                                },
                                'initialDelaySeconds': 30,
                                'periodSeconds': 10
                            },
                            'readinessProbe': {
                                'httpGet': {
                                    'path': '/ready',
                                    'port': 8080
                                },
                                'initialDelaySeconds': 10,
                                'periodSeconds': 5
                            }
                        }]
                    }
                }
            }
        }
        
        # Create deployment
        self.k8s_apps_v1.create_namespaced_deployment(
            namespace=self.config.kubernetes_namespace,
            body=deployment_spec
        )
        
        # Create service
        service_spec = {
            'apiVersion': 'v1',
            'kind': 'Service',
            'metadata': {
                'name': f"{deployment_name}-service",
                'namespace': self.config.kubernetes_namespace,
                'labels': {
                    'app': deployment_name,
                    'variant': variant
                }
            },
            'spec': {
                'selector': {
                    'app': deployment_name,
                    'variant': variant
                },
                'ports': [{
                    'port': 8080,
                    'targetPort': 8080,
                    'protocol': 'TCP'
                }],
                'type': 'ClusterIP'
            }
        }
        
        self.k8s_core_v1.create_namespaced_service(
            namespace=self.config.kubernetes_namespace,
            body=service_spec
        )
        
        logger.info(f"Created deployment and service for {variant} variant")
    
    def _create_traffic_splitter(self):
        """Create traffic splitting service using Istio VirtualService"""
        vs_name = f"{self.config.experiment_name}-traffic-split"
        
        virtual_service = {
            'apiVersion': 'networking.istio.io/v1alpha3',
            'kind': 'VirtualService',
            'metadata': {
                'name': vs_name,
                'namespace': self.config.kubernetes_namespace
            },
            'spec': {
                'hosts': [f"{self.config.experiment_name}-service"],
                'http': [{
                    'match': [{'headers': {'x-experiment-variant': {'exact': 'control'}}}],
                    'route': [{
                        'destination': {
                            'host': f"{self.config.experiment_name}-control-service",
                            'port': {'number': 8080}
                        }
                    }]
                }, {
                    'match': [{'headers': {'x-experiment-variant': {'exact': 'treatment'}}}],
                    'route': [{
                        'destination': {
                            'host': f"{self.config.experiment_name}-treatment-service",
                            'port': {'number': 8080}
                        }
                    }]
                }, {
                    'route': [{
                        'destination': {
                            'host': f"{self.config.experiment_name}-control-service",
                            'port': {'number': 8080}
                        },
                        'weight': int(self.config.traffic_allocation['control'] * 100)
                    }, {
                        'destination': {
                            'host': f"{self.config.experiment_name}-treatment-service",
                            'port': {'number': 8080}
                        },
                        'weight': int(self.config.traffic_allocation['treatment'] * 100)
                    }]
                }]
            }
        }
        
        # Apply VirtualService (this would require Istio CRD, simplified here)
        logger.info("Traffic splitting configuration created")
        
        # Store traffic split configuration in Redis
        self.redis_client.hset(
            f"experiment:{self.config.experiment_name}:config",
            "traffic_allocation",
            json.dumps(self.config.traffic_allocation)
        )
    
    def _create_experiment_tracker(self):
        """Create experiment tracking service"""
        tracker_name = f"{self.config.experiment_name}-tracker"
        
        deployment_spec = {
            'apiVersion': 'apps/v1',
            'kind': 'Deployment',
            'metadata': {
                'name': tracker_name,
                'namespace': self.config.kubernetes_namespace,
                'labels': {
                    'app': tracker_name,
                    'component': 'experiment-tracker'
                }
            },
            'spec': {
                'replicas': 2,
                'selector': {
                    'matchLabels': {
                        'app': tracker_name
                    }
                },
                'template': {
                    'metadata': {
                        'labels': {
                            'app': tracker_name,
                            'component': 'experiment-tracker'
                        }
                    },
                    'spec': {
                        'containers': [{
                            'name': 'experiment-tracker',
                            'image': 'ecommerce-analytics/experiment-tracker:latest',
                            'ports': [{'containerPort': 8090}],
                            'env': [
                                {'name': 'EXPERIMENT_NAME', 'value': self.config.experiment_name},
                                {'name': 'REDIS_HOST', 'value': self.config.redis_host},
                                {'name': 'DB_HOST', 'value': self.config.db_host},
                                {'name': 'DB_NAME', 'value': self.config.db_name}
                            ],
                            'resources': {
                                'requests': {
                                    'cpu': '100m',
                                    'memory': '256Mi'
                                },
                                'limits': {
                                    'cpu': '500m',
                                    'memory': '1Gi'
                                }
                            }
                        }]
                    }
                }
            }
        }
        
        self.k8s_apps_v1.create_namespaced_deployment(
            namespace=self.config.kubernetes_namespace,
            body=deployment_spec
        )
        
        logger.info("Experiment tracker deployed")
    
    def assign_user_to_variant(self, user_id: str, context: Dict = None) -> str:
        """Assign user to experiment variant using consistent hashing"""
        
        # Check if user already assigned
        existing_assignment = self.redis_client.hget(
            f"experiment:{self.config.experiment_name}:assignments",
            user_id
        )
        
        if existing_assignment:
            return existing_assignment
        
        # Check if experiment is running
        if self.experiment_state['status'] != ExperimentStatus.RUNNING:
            return "control"  # Default to control if experiment not running
        
        # Hash user ID for consistent assignment
        hash_input = f"{self.config.experiment_name}:{user_id}"
        user_hash = int(hashlib.md5(hash_input.encode()).hexdigest(), 16)
        hash_bucket = (user_hash % 10000) / 10000  # Normalize to 0-1
        
        # Determine variant based on current allocation
        current_allocation = self.experiment_state['current_allocation']
        
        if hash_bucket < current_allocation['control']:
            variant = "control"
        else:
            variant = "treatment"
        
        # Store assignment
        self.redis_client.hset(
            f"experiment:{self.config.experiment_name}:assignments",
            user_id,
            variant
        )
        
        # Store assignment timestamp
        self.redis_client.hset(
            f"experiment:{self.config.experiment_name}:assignment_times",
            user_id,
            datetime.now().isoformat()
        )
        
        # Update sample counts
        self.experiment_state['total_samples'][variant] += 1
        
        # Update Prometheus metrics
        self.experiment_requests.labels(
            experiment_name=self.config.experiment_name,
            variant=variant
        ).inc()
        
        logger.debug(f"Assigned user {user_id} to variant {variant}")
        return variant
    
    def track_event(self, user_id: str, event_type: str, value: float = 1.0, 
                   metadata: Dict = None):
        """Track experiment event (conversion, revenue, etc.)"""
        
        # Get user's variant assignment
        variant = self.redis_client.hget(
            f"experiment:{self.config.experiment_name}:assignments",
            user_id
        )
        
        if not variant:
            logger.warning(f"No variant assignment found for user {user_id}")
            return
        
        # Create event record
        event = {
            'experiment_name': self.config.experiment_name,
            'user_id': user_id,
            'variant': variant,
            'event_type': event_type,
            'value': value,
            'timestamp': datetime.now().isoformat(),
            'metadata': metadata or {}
        }
        
        # Store event in database
        self._store_event(event)
        
        # Update Redis for real-time metrics
        self.redis_client.hincrby(
            f"experiment:{self.config.experiment_name}:metrics:{variant}",
            event_type,
            1
        )
        
        if event_type in [self.config.primary_metric] + self.config.secondary_metrics:
            self.redis_client.hincrbyfloat(
                f"experiment:{self.config.experiment_name}:values:{variant}",
                event_type,
                value
            )
        
        # Update Prometheus metrics
        self.experiment_conversions.labels(
            experiment_name=self.config.experiment_name,
            variant=variant,
            metric=event_type
        ).inc()
        
        logger.debug(f"Tracked event {event_type} for user {user_id} in variant {variant}")
    
    def _store_event(self, event: Dict):
        """Store event in database"""
        try:
            query = text("""
                INSERT INTO ab_test_events 
                (experiment_name, user_id, variant, event_type, value, timestamp, metadata)
                VALUES 
                (:experiment_name, :user_id, :variant, :event_type, :value, :timestamp, :metadata)
            """)
            
            self.db_engine.execute(query, {
                'experiment_name': event['experiment_name'],
                'user_id': event['user_id'],
                'variant': event['variant'],
                'event_type': event['event_type'],
                'value': event['value'],
                'timestamp': event['timestamp'],
                'metadata': json.dumps(event['metadata'])
            })
            
        except Exception as e:
            logger.error(f"Failed to store event: {str(e)}")
    
    def start_experiment(self):
        """Start the A/B test experiment"""
        logger.info(f"Starting experiment {self.config.experiment_name}")
        
        # Validate experiment setup
        self._validate_experiment_setup()
        
        # Update experiment state
        self.experiment_state['status'] = ExperimentStatus.RUNNING
        self.experiment_state['start_time'] = datetime.now()
        
        # Initialize sample size calculation
        required_sample_size = self._calculate_required_sample_size()
        logger.info(f"Required sample size per variant: {required_sample_size}")
        
        # Store experiment configuration
        self._store_experiment_config()
        
        # Send notification
        self._send_notification(
            f"🚀 A/B Test Started",
            f"Experiment '{self.config.experiment_name}' started successfully.\n"
            f"Control: {self.config.control_model_version}\n"
            f"Treatment: {self.config.treatment_model_version}\n"
            f"Traffic split: {self.config.traffic_allocation}"
        )
        
        logger.info("Experiment started successfully")
    
    def _validate_experiment_setup(self):
        """Validate experiment configuration"""
        
        # Check traffic allocation sums to 1
        total_allocation = sum(self.config.traffic_allocation.values())
        if abs(total_allocation - 1.0) > 0.001:
            raise ValueError(f"Traffic allocation must sum to 1.0, got {total_allocation}")
        
        # Check model versions exist
        for variant, version in [
            ('control', self.config.control_model_version),
            ('treatment', self.config.treatment_model_version)
        ]:
            try:
                model_name = f"{self.config.model_type.replace('_', '-')}-model"
                self.mlflow_client.get_model_version(model_name, version)
            except Exception as e:
                raise ValueError(f"Model version {version} not found for {variant}: {str(e)}")
        
        # Check infrastructure readiness
        try:
            deployments = self.k8s_apps_v1.list_namespaced_deployment(
                namespace=self.config.kubernetes_namespace
            )
            
            required_deployments = [
                f"{self.config.experiment_name}-control",
                f"{self.config.experiment_name}-treatment"
            ]
            
            existing_deployments = [d.metadata.name for d in deployments.items]
            
            for required in required_deployments:
                if required not in existing_deployments:
                    raise ValueError(f"Required deployment {required} not found")
                    
        except Exception as e:
            raise ValueError(f"Infrastructure validation failed: {str(e)}")
    
    def _calculate_required_sample_size(self) -> int:
        """Calculate required sample size for statistical significance"""
        
        # For binary metrics (conversion rates)
        if self.config.primary_metric in ['conversion_rate', 'click_through_rate']:
            baseline_rate = 0.1  # Assume 10% baseline conversion rate
            effect_size = self.config.minimum_effect_size
            
            # Use standard sample size calculation for proportions
            z_alpha = stats.norm.ppf(1 - self.config.significance_level / 2)
            z_beta = stats.norm.ppf(self.config.statistical_power)
            
            p1 = baseline_rate
            p2 = baseline_rate * (1 + effect_size)
            p_pooled = (p1 + p2) / 2
            
            n = (
                (z_alpha * np.sqrt(2 * p_pooled * (1 - p_pooled)) + 
                 z_beta * np.sqrt(p1 * (1 - p1) + p2 * (1 - p2))) ** 2
            ) / (p2 - p1) ** 2
            
            return int(np.ceil(n))
        
        # For continuous metrics (revenue, response time)
        else:
            # Use Cohen's d for effect size
            cohen_d = self.config.minimum_effect_size
            z_alpha = stats.norm.ppf(1 - self.config.significance_level / 2)
            z_beta = stats.norm.ppf(self.config.statistical_power)
            
            n = (2 * (z_alpha + z_beta) ** 2) / (cohen_d ** 2)
            return int(np.ceil(n))
    
    def _store_experiment_config(self):
        """Store experiment configuration in Redis"""
        config_key = f"experiment:{self.config.experiment_name}:config"
        
        config_data = {
            'experiment_name': self.config.experiment_name,
            'model_type': self.config.model_type,
            'control_model_version': self.config.control_model_version,
            'treatment_model_version': self.config.treatment_model_version,
            'traffic_allocation': self.config.traffic_allocation,
            'primary_metric': self.config.primary_metric,
            'secondary_metrics': self.config.secondary_metrics,
            'minimum_effect_size': self.config.minimum_effect_size,
            'significance_level': self.config.significance_level,
            'start_time': self.experiment_state['start_time'].isoformat(),
            'status': self.experiment_state['status'].value
        }
        
        for key, value in config_data.items():
            self.redis_client.hset(config_key, key, json.dumps(value))
    
    def analyze_experiment_results(self) -> Dict:
        """Analyze current experiment results"""
        logger.info(f"Analyzing results for experiment {self.config.experiment_name}")
        
        # Get experiment data from database
        experiment_data = self._get_experiment_data()
        
        if experiment_data.empty:
            logger.warning("No experiment data found")
            return {"error": "No data available"}
        
        # Calculate metrics for each variant
        results = {}
        variants = ['control', 'treatment']
        
        for variant in variants:
            variant_data = experiment_data[experiment_data['variant'] == variant]
            results[variant] = self._calculate_variant_metrics(variant_data)
        
        # Perform statistical tests
        statistical_results = {}
        
        for metric in [self.config.primary_metric] + self.config.secondary_metrics:
            if metric in experiment_data['event_type'].values:
                statistical_results[metric] = self._perform_statistical_test(
                    experiment_data, metric
                )
        
        # Check guardrail metrics
        guardrail_results = self._check_guardrail_metrics(experiment_data)
        
        # Calculate statistical power
        current_power = self._calculate_current_statistical_power(experiment_data)
        
        # Update Prometheus metrics
        self.experiment_statistical_power.labels(
            experiment_name=self.config.experiment_name
        ).set(current_power)
        
        for metric, test_result in statistical_results.items():
            if 'p_value' in test_result:
                self.experiment_significance.labels(
                    experiment_name=self.config.experiment_name,
                    metric=metric
                ).set(test_result['p_value'])
        
        # Compile final results
        analysis_results = {
            'experiment_name': self.config.experiment_name,
            'analysis_timestamp': datetime.now().isoformat(),
            'experiment_duration_days': (
                datetime.now() - self.experiment_state['start_time']
            ).days if self.experiment_state['start_time'] else 0,
            'variant_results': results,
            'statistical_tests': statistical_results,
            'guardrail_metrics': guardrail_results,
            'statistical_power': current_power,
            'sample_sizes': {
                variant: len(experiment_data[experiment_data['variant'] == variant])
                for variant in variants
            }
        }
        
        # Store results
        self.experiment_state['results'] = analysis_results
        
        return analysis_results
    
    def _get_experiment_data(self) -> pd.DataFrame:
        """Get experiment data from database"""
        query = text("""
            SELECT experiment_name, user_id, variant, event_type, value, timestamp, metadata
            FROM ab_test_events 
            WHERE experiment_name = :experiment_name
            ORDER BY timestamp
        """)
        
        try:
            return pd.read_sql(
                query, 
                self.db_engine, 
                params={'experiment_name': self.config.experiment_name}
            )
        except Exception as e:
            logger.error(f"Failed to get experiment data: {str(e)}")
            return pd.DataFrame()
    
    def _calculate_variant_metrics(self, variant_data: pd.DataFrame) -> Dict:
        """Calculate metrics for a variant"""
        metrics = {
            'total_users': variant_data['user_id'].nunique(),
            'total_events': len(variant_data),
            'events_by_type': variant_data['event_type'].value_counts().to_dict()
        }
        
        # Calculate conversion rates
        total_users = metrics['total_users']
        if total_users > 0:
            for event_type in variant_data['event_type'].unique():
                converted_users = variant_data[
                    variant_data['event_type'] == event_type
                ]['user_id'].nunique()
                metrics[f'{event_type}_conversion_rate'] = converted_users / total_users
        
        # Calculate average values
        for event_type in variant_data['event_type'].unique():
            event_data = variant_data[variant_data['event_type'] == event_type]
            if len(event_data) > 0:
                metrics[f'{event_type}_avg_value'] = event_data['value'].mean()
                metrics[f'{event_type}_total_value'] = event_data['value'].sum()
        
        return metrics
    
    def _perform_statistical_test(self, experiment_data: pd.DataFrame, metric: str) -> Dict:
        """Perform statistical test for a specific metric"""
        
        # Filter data for the specific metric
        metric_data = experiment_data[experiment_data['event_type'] == metric]
        
        if len(metric_data) < 2:
            return {"error": "Insufficient data for statistical test"}
        
        # Get data for each variant
        control_data = metric_data[metric_data['variant'] == 'control']
        treatment_data = metric_data[metric_data['variant'] == 'treatment']
        
        if len(control_data) == 0 or len(treatment_data) == 0:
            return {"error": "Missing data for one or both variants"}
        
        # Determine test type based on metric
        if metric in ['conversion_rate', 'click_through_rate']:
            # Proportion test
            return self._proportion_test(control_data, treatment_data)
        else:
            # Continuous metric test
            return self._continuous_test(control_data, treatment_data)
    
    def _proportion_test(self, control_data: pd.DataFrame, treatment_data: pd.DataFrame) -> Dict:
        """Perform proportion test (z-test)"""
        
        # Calculate conversion rates
        control_users = control_data['user_id'].nunique()
        treatment_users = treatment_data['user_id'].nunique()
        
        control_conversions = len(control_data)
        treatment_conversions = len(treatment_data)
        
        if control_users == 0 or treatment_users == 0:
            return {"error": "No users in one or both variants"}
        
        control_rate = control_conversions / control_users
        treatment_rate = treatment_conversions / treatment_users
        
        # Perform z-test for proportions
        counts = np.array([control_conversions, treatment_conversions])
        nobs = np.array([control_users, treatment_users])
        
        try:
            z_stat, p_value = proportions_ztest(counts, nobs)
            
            # Calculate confidence interval for difference
            diff = treatment_rate - control_rate
            se_diff = np.sqrt(
                control_rate * (1 - control_rate) / control_users +
                treatment_rate * (1 - treatment_rate) / treatment_users
            )
            
            z_alpha = stats.norm.ppf(1 - self.config.significance_level / 2)
            ci_lower = diff - z_alpha * se_diff
            ci_upper = diff + z_alpha * se_diff
            
            return {
                'test_type': 'proportion_z_test',
                'control_rate': control_rate,
                'treatment_rate': treatment_rate,
                'difference': diff,
                'relative_difference': diff / control_rate if control_rate > 0 else 0,
                'z_statistic': z_stat,
                'p_value': p_value,
                'is_significant': p_value < self.config.significance_level,
                'confidence_interval': [ci_lower, ci_upper],
                'sample_sizes': {'control': control_users, 'treatment': treatment_users}
            }
            
        except Exception as e:
            return {"error": f"Statistical test failed: {str(e)}"}
    
    def _continuous_test(self, control_data: pd.DataFrame, treatment_data: pd.DataFrame) -> Dict:
        """Perform t-test for continuous metrics"""
        
        control_values = control_data['value'].values
        treatment_values = treatment_data['value'].values
        
        try:
            # Perform Welch's t-test (unequal variances)
            t_stat, p_value = stats.ttest_ind(treatment_values, control_values, equal_var=False)
            
            # Calculate effect size (Cohen's d)
            pooled_std = np.sqrt(
                ((len(control_values) - 1) * np.var(control_values, ddof=1) +
                 (len(treatment_values) - 1) * np.var(treatment_values, ddof=1)) /
                (len(control_values) + len(treatment_values) - 2)
            )
            
            cohens_d = (treatment_values.mean() - control_values.mean()) / pooled_std
            
            # Calculate confidence interval for difference
            diff = treatment_values.mean() - control_values.mean()
            se_diff = np.sqrt(
                np.var(control_values, ddof=1) / len(control_values) +
                np.var(treatment_values, ddof=1) / len(treatment_values)
            )
            
            df = len(control_values) + len(treatment_values) - 2
            t_alpha = stats.t.ppf(1 - self.config.significance_level / 2, df)
            ci_lower = diff - t_alpha * se_diff
            ci_upper = diff + t_alpha * se_diff
            
            return {
                'test_type': 'welch_t_test',
                'control_mean': control_values.mean(),
                'treatment_mean': treatment_values.mean(),
                'difference': diff,
                'relative_difference': diff / control_values.mean() if control_values.mean() > 0 else 0,
                't_statistic': t_stat,
                'p_value': p_value,
                'degrees_of_freedom': df,
                'cohens_d': cohens_d,
                'is_significant': p_value < self.config.significance_level,
                'confidence_interval': [ci_lower, ci_upper],
                'sample_sizes': {'control': len(control_values), 'treatment': len(treatment_values)}
            }
            
        except Exception as e:
            return {"error": f"Statistical test failed: {str(e)}"}
    
    def _frequentist_test(self, control_data: pd.DataFrame, treatment_data: pd.DataFrame) -> Dict:
        """Perform frequentist statistical test"""
        return self._perform_statistical_test(
            pd.concat([control_data, treatment_data]), 
            control_data['event_type'].iloc[0] if len(control_data) > 0 else 'unknown'
        )
    
    def _bayesian_test(self, control_data: pd.DataFrame, treatment_data: pd.DataFrame) -> Dict:
        """Perform Bayesian statistical test"""
        try:
            if len(control_data) == 0 or len(treatment_data) == 0:
                return {"error": "Insufficient data for Bayesian test"}
            
            # For binary metrics
            if control_data['event_type'].iloc[0] in ['conversion_rate', 'click_through_rate']:
                control_conversions = len(control_data)
                control_total = control_data['user_id'].nunique()
                treatment_conversions = len(treatment_data)
                treatment_total = treatment_data['user_id'].nunique()
                
                # Use Beta-Binomial model
                test = bt.BinaryDataTest()
                test.add_variant_data("control", [1] * control_conversions + [0] * (control_total - control_conversions))
                test.add_variant_data("treatment", [1] * treatment_conversions + [0] * (treatment_total - treatment_conversions))
                
                probability_treatment_better = test.probability(variant="treatment", lift=0)
                
                return {
                    'test_type': 'bayesian_binary',
                    'probability_treatment_better': probability_treatment_better,
                    'is_significant': probability_treatment_better > 0.95,
                    'control_conversions': control_conversions,
                    'control_total': control_total,
                    'treatment_conversions': treatment_conversions,
                    'treatment_total': treatment_total
                }
            
            # For continuous metrics
            else:
                control_values = control_data['value'].values
                treatment_values = treatment_data['value'].values
                
                # Use Normal model
                test = bt.NormalDataTest()
                test.add_variant_data("control", control_values)
                test.add_variant_data("treatment", treatment_values)
                
                probability_treatment_better = test.probability(variant="treatment", lift=0)
                
                return {
                    'test_type': 'bayesian_normal',
                    'probability_treatment_better': probability_treatment_better,
                    'is_significant': probability_treatment_better > 0.95,
                    'control_mean': control_values.mean(),
                    'treatment_mean': treatment_values.mean(),
                    'control_std': control_values.std(),
                    'treatment_std': treatment_values.std()
                }
                
        except Exception as e:
            return {"error": f"Bayesian test failed: {str(e)}"}
    
    def _sequential_test(self, control_data: pd.DataFrame, treatment_data: pd.DataFrame) -> Dict:
        """Perform sequential statistical test with early stopping"""
        # Simplified sequential test - would implement proper sequential analysis
        # For now, use frequentist test with alpha spending function
        
        frequentist_result = self._frequentist_test(control_data, treatment_data)
        
        if 'error' in frequentist_result:
            return frequentist_result
        
        # Adjust alpha for multiple testing (simplified O'Brien-Fleming)
        total_planned_analyses = 5  # Assume 5 interim analyses
        current_analysis = 1  # Would track this
        
        adjusted_alpha = self.config.significance_level * (
            2 * (1 - stats.norm.cdf(stats.norm.ppf(1 - self.config.significance_level / 2) / 
                                   np.sqrt(current_analysis / total_planned_analyses)))
        )
        
        frequentist_result['adjusted_alpha'] = adjusted_alpha
        frequentist_result['early_stop_significant'] = frequentist_result['p_value'] < adjusted_alpha
        frequentist_result['test_type'] = 'sequential_' + frequentist_result['test_type']
        
        return frequentist_result
    
    def _check_guardrail_metrics(self, experiment_data: pd.DataFrame) -> Dict:
        """Check guardrail metrics for safety"""
        guardrail_results = {}
        
        for metric_name, criteria in self.config.guardrail_metrics.items():
            metric_data = experiment_data[experiment_data['event_type'] == metric_name]
            
            if len(metric_data) == 0:
                guardrail_results[metric_name] = {
                    'status': 'no_data',
                    'message': f'No data available for {metric_name}'
                }
                continue
            
            # Calculate metric values for each variant
            control_data = metric_data[metric_data['variant'] == 'control']
            treatment_data = metric_data[metric_data['variant'] == 'treatment']
            
            if len(control_data) == 0 or len(treatment_data) == 0:
                guardrail_results[metric_name] = {
                    'status': 'insufficient_data',
                    'message': f'Insufficient data for {metric_name}'
                }
                continue
            
            control_value = control_data['value'].mean()
            treatment_value = treatment_data['value'].mean()
            
            threshold = criteria['threshold']
            direction = criteria['direction']
            
            # Check if guardrail is violated
            violation = False
            if direction == 'lower':
                violation = treatment_value > threshold
            elif direction == 'higher':
                violation = treatment_value < threshold
            
            guardrail_results[metric_name] = {
                'status': 'violation' if violation else 'healthy',
                'control_value': control_value,
                'treatment_value': treatment_value,
                'threshold': threshold,
                'direction': direction,
                'violation': violation
            }
        
        return guardrail_results
    
    def _calculate_current_statistical_power(self, experiment_data: pd.DataFrame) -> float:
        """Calculate current statistical power of the experiment"""
        
        # Get sample sizes
        control_size = len(experiment_data[experiment_data['variant'] == 'control'])
        treatment_size = len(experiment_data[experiment_data['variant'] == 'treatment'])
        
        if control_size == 0 or treatment_size == 0:
            return 0.0
        
        # Calculate power for primary metric (simplified)
        effect_size = self.config.minimum_effect_size
        alpha = self.config.significance_level
        
        # Use the smaller sample size for conservative estimate
        n = min(control_size, treatment_size)
        
        # Calculate power using standard formula for two-sample test
        z_alpha = stats.norm.ppf(1 - alpha / 2)
        z_beta = effect_size * np.sqrt(n / 2) - z_alpha
        power = stats.norm.cdf(z_beta)
        
        return max(0.0, min(1.0, power))
    
    def make_experiment_decision(self, analysis_results: Dict) -> Dict:
        """Make automated decision about experiment continuation"""
        logger.info("Making experiment decision based on current results")
        
        decision = {
            'timestamp': datetime.now().isoformat(),
            'action': 'continue',
            'reasons': [],
            'recommendations': []
        }
        
        # Check experiment duration
        duration_days = analysis_results.get('experiment_duration_days', 0)
        if duration_days >= self.config.max_runtime_days:
            decision['action'] = 'stop'
            decision['reasons'].append(f"Maximum runtime reached ({self.config.max_runtime_days} days)")
        
        # Check sample size
        sample_sizes = analysis_results.get('sample_sizes', {})
        min_sample_size = self.config.min_sample_size_per_variant
        
        for variant, size in sample_sizes.items():
            if size < min_sample_size:
                decision['reasons'].append(f"Insufficient sample size for {variant}: {size} < {min_sample_size}")
        
        # Check statistical significance
        statistical_tests = analysis_results.get('statistical_tests', {})
        primary_test = statistical_tests.get(self.config.primary_metric, {})
        
        if primary_test.get('is_significant', False):
            if self.config.early_stopping_enabled:
                # Check if we have sufficient power
                power = analysis_results.get('statistical_power', 0)
                if power >= self.config.statistical_power:
                    decision['action'] = 'stop'
                    decision['reasons'].append("Statistically significant result with sufficient power")
                    
                    # Determine winner
                    if primary_test.get('relative_difference', 0) > 0:
                        decision['winner'] = 'treatment'
                        decision['recommendations'].append("Deploy treatment model to 100% of traffic")
                    else:
                        decision['winner'] = 'control'
                        decision['recommendations'].append("Keep control model")
        
        # Check guardrail metrics
        guardrail_results = analysis_results.get('guardrail_metrics', {})
        guardrail_violations = [
            metric for metric, result in guardrail_results.items()
            if result.get('violation', False)
        ]
        
        if guardrail_violations:
            decision['action'] = 'stop'
            decision['reasons'].append(f"Guardrail violations: {', '.join(guardrail_violations)}")
            decision['winner'] = 'control'
            decision['recommendations'].append("Revert to control model due to safety concerns")
        
        # Store decision
        self.experiment_state['decisions'].append(decision)
        
        return decision
    
    def update_traffic_allocation(self, new_allocation: Dict[str, float]):
        """Update traffic allocation during experiment"""
        logger.info(f"Updating traffic allocation to {new_allocation}")
        
        # Validate new allocation
        if abs(sum(new_allocation.values()) - 1.0) > 0.001:
            raise ValueError("Traffic allocation must sum to 1.0")
        
        # Update experiment state
        self.experiment_state['current_allocation'] = new_allocation
        
        # Update Redis configuration
        self.redis_client.hset(
            f"experiment:{self.config.experiment_name}:config",
            "traffic_allocation",
            json.dumps(new_allocation)
        )
        
        # Update Kubernetes traffic splitting (would update VirtualService)
        # This is simplified - in practice would update Istio VirtualService
        logger.info("Traffic allocation updated successfully")
    
    def stop_experiment(self, reason: str = "Manual stop"):
        """Stop the experiment"""
        logger.info(f"Stopping experiment {self.config.experiment_name}. Reason: {reason}")
        
        # Update experiment state
        self.experiment_state['status'] = ExperimentStatus.STOPPED
        self.experiment_state['end_time'] = datetime.now()
        
        # Generate final analysis
        final_results = self.analyze_experiment_results()
        final_decision = self.make_experiment_decision(final_results)
        
        # Send notification
        winner = final_decision.get('winner', 'inconclusive')
        self._send_notification(
            f"🏁 A/B Test Completed",
            f"Experiment '{self.config.experiment_name}' stopped.\n"
            f"Reason: {reason}\n"
            f"Winner: {winner}\n"
            f"Duration: {self.experiment_state['end_time'] - self.experiment_state['start_time']}"
        )
        
        # Clean up infrastructure (optional)
        # self._cleanup_experiment_infrastructure()
        
        return {
            'final_results': final_results,
            'final_decision': final_decision,
            'experiment_state': self.experiment_state
        }
    
    def create_experiment_dashboard(self, analysis_results: Dict):
        """Create real-time experiment dashboard"""
        logger.info("Creating experiment dashboard")
        
        # Create comprehensive dashboard
        fig = make_subplots(
            rows=3, cols=2,
            subplot_titles=[
                'Conversion Rate Over Time',
                'Sample Size by Variant',
                'Statistical Significance',
                'Guardrail Metrics',
                'Revenue Impact',
                'Confidence Intervals'
            ],
            specs=[
                [{"secondary_y": False}, {"type": "bar"}],
                [{"secondary_y": False}, {"type": "bar"}],
                [{"secondary_y": False}, {"secondary_y": False}]
            ]
        )
        
        # Sample data for visualization (in practice, would query real data)
        variants = ['control', 'treatment']
        variant_results = analysis_results.get('variant_results', {})
        
        # 1. Sample sizes
        sample_sizes = analysis_results.get('sample_sizes', {})
        fig.add_trace(
            go.Bar(
                x=list(sample_sizes.keys()),
                y=list(sample_sizes.values()),
                name='Sample Size',
                marker_color=['blue', 'red']
            ),
            row=1, col=2
        )
        
        # 2. Statistical tests results
        statistical_tests = analysis_results.get('statistical_tests', {})
        if statistical_tests:
            metrics = list(statistical_tests.keys())
            p_values = [test.get('p_value', 1.0) for test in statistical_tests.values()]
            
            fig.add_trace(
                go.Bar(
                    x=metrics,
                    y=p_values,
                    name='P-values',
                    marker_color='green'
                ),
                row=2, col=1
            )
            
            # Add significance threshold line
            fig.add_hline(
                y=self.config.significance_level,
                line_dash="dash",
                line_color="red",
                row=2, col=1
            )
        
        # 3. Guardrail metrics
        guardrail_results = analysis_results.get('guardrail_metrics', {})
        if guardrail_results:
            guardrail_names = list(guardrail_results.keys())
            guardrail_values = [
                result.get('treatment_value', 0) for result in guardrail_results.values()
            ]
            
            fig.add_trace(
                go.Bar(
                    x=guardrail_names,
                    y=guardrail_values,
                    name='Guardrail Values',
                    marker_color='orange'
                ),
                row=2, col=2
            )
        
        fig.update_layout(
            title=f'A/B Test Dashboard: {self.config.experiment_name}',
            height=1200,
            showlegend=True
        )
        
        # Save dashboard
        try:
            dashboard_path = f'/tmp/ab_test_dashboard_{self.config.experiment_name}.html'
            fig.write_html(dashboard_path)
            
            # Upload to S3 for sharing
            s3_key = f"ab-test-dashboards/{self.config.experiment_name}/dashboard_{int(datetime.now().timestamp())}.html"
            self.s3_client.upload_file(dashboard_path, 'ecommerce-analytics-reports', s3_key)
            
            logger.info(f"Dashboard saved to S3: s3://ecommerce-analytics-reports/{s3_key}")
            
        except Exception as e:
            logger.warning(f"Could not save dashboard: {str(e)}")
    
    def _send_notification(self, title: str, message: str):
        """Send notification via Slack webhook"""
        if not self.config.slack_webhook:
            return
        
        try:
            payload = {
                "text": f"{title}\n{message}",
                "username": "A/B Test Framework",
                "icon_emoji": ":chart_with_upwards_trend:"
            }
            
            response = requests.post(self.config.slack_webhook, json=payload)
            response.raise_for_status()
            
        except Exception as e:
            logger.error(f"Failed to send notification: {str(e)}")
    
    def run_experiment_monitoring(self):
        """Run continuous experiment monitoring"""
        logger.info("Starting experiment monitoring")
        
        while self.experiment_state['status'] == ExperimentStatus.RUNNING:
            try:
                # Analyze current results
                analysis_results = self.analyze_experiment_results()
                
                # Make decision about continuation
                decision = self.make_experiment_decision(analysis_results)
                
                # Create dashboard
                self.create_experiment_dashboard(analysis_results)
                
                # Check if experiment should stop
                if decision['action'] == 'stop':
                    self.stop_experiment(f"Automated stop: {', '.join(decision['reasons'])}")
                    break
                
                # Update traffic allocation if needed (implement gradual ramp-up)
                if self.config.ramp_up_schedule:
                    self._update_ramp_up_allocation()
                
                # Wait before next check
                time.sleep(3600)  # Check every hour
                
            except Exception as e:
                logger.error(f"Error in experiment monitoring: {str(e)}")
                time.sleep(300)  # Wait 5 minutes before retry
    
    def _update_ramp_up_allocation(self):
        """Update traffic allocation based on ramp-up schedule"""
        if not self.config.ramp_up_schedule:
            return
        
        days_running = (datetime.now() - self.experiment_state['start_time']).days
        
        if str(days_running) in self.config.ramp_up_schedule:
            new_treatment_percentage = self.config.ramp_up_schedule[str(days_running)]
            new_allocation = {
                'control': 1.0 - new_treatment_percentage,
                'treatment': new_treatment_percentage
            }
            
            if new_allocation != self.experiment_state['current_allocation']:
                self.update_traffic_allocation(new_allocation)
                
                self._send_notification(
                    f"📈 Traffic Ramp-Up",
                    f"Updated traffic allocation for day {days_running}:\n"
                    f"Treatment: {new_treatment_percentage:.1%}"
                )

def main():
    """Example usage of A/B testing framework"""
    
    # Example configuration
    config = ABTestConfig(
        experiment_name="churn-model-v2-test",
        model_type="churn_prediction",
        control_model_version="1",
        treatment_model_version="2",
        traffic_allocation={"control": 0.8, "treatment": 0.2},
        primary_metric="conversion_rate",
        secondary_metrics=["revenue_per_user", "engagement_score"],
        minimum_effect_size=0.05,  # 5% minimum improvement
        max_runtime_days=14,
        min_sample_size_per_variant=2000
    )
    
    # Create A/B test framework
    ab_test = ABTestFramework(config)
    
    try:
        # Create infrastructure
        ab_test.create_experiment_infrastructure()
        
        # Start experiment
        ab_test.start_experiment()
        
        # Run monitoring (this would run continuously)
        # ab_test.run_experiment_monitoring()
        
        logger.info("A/B test framework setup completed successfully")
        
    except Exception as e:
        logger.error(f"A/B test framework failed: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()