#!/usr/bin/env python3
"""
ML Data Preprocessing Pipeline for E-commerce Analytics
Handles data ingestion, cleaning, feature engineering, and preparation for ML models
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import os
import sys
from typing import Dict, List, Tuple, Optional
import boto3
import psycopg2
from sqlalchemy import create_engine
import redis
from sklearn.preprocessing import StandardScaler, LabelEncoder, MinMaxScaler
from sklearn.impute import SimpleImputer
import joblib
from feast import FeatureStore
import mlflow
import mlflow.sklearn
from dataclasses import dataclass

# Configuration
@dataclass
class Config:
    # Database connections
    db_host: str = os.getenv('DB_HOST', 'analytics-db.cluster-xyz.us-east-1.rds.amazonaws.com')
    db_name: str = os.getenv('DB_NAME', 'ecommerce_analytics')
    db_user: str = os.getenv('DB_USER', 'analytics_user')
    db_password: str = os.getenv('DB_PASSWORD', 'analytics_password')
    
    # Redis connection
    redis_host: str = os.getenv('REDIS_HOST', 'analytics-redis.abc123.cache.amazonaws.com')
    redis_port: int = int(os.getenv('REDIS_PORT', '6379'))
    
    # S3 configuration
    s3_bucket: str = os.getenv('S3_BUCKET', 'ecommerce-analytics-data')
    s3_raw_prefix: str = os.getenv('S3_RAW_PREFIX', 'raw/')
    s3_processed_prefix: str = os.getenv('S3_PROCESSED_PREFIX', 'processed/')
    
    # MLflow configuration
    mlflow_tracking_uri: str = os.getenv('MLFLOW_TRACKING_URI', 'http://mlflow-server.mlflow:5000')
    
    # Feast configuration
    feast_repo_path: str = os.getenv('FEAST_REPO_PATH', '/feature_repo')
    
    # Processing parameters
    lookback_days: int = int(os.getenv('LOOKBACK_DAYS', '30'))
    batch_size: int = int(os.getenv('BATCH_SIZE', '10000'))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DataProcessor:
    def __init__(self, config: Config):
        self.config = config
        self.db_engine = self._create_db_engine()
        self.redis_client = self._create_redis_client()
        self.s3_client = boto3.client('s3')
        self.feast_store = FeatureStore(repo_path=config.feast_repo_path)
        
        # Initialize MLflow
        mlflow.set_tracking_uri(config.mlflow_tracking_uri)
        mlflow.set_experiment("data-preprocessing")
        
    def _create_db_engine(self):
        """Create database engine for PostgreSQL"""
        connection_string = f"postgresql://{self.config.db_user}:{self.config.db_password}@{self.config.db_host}/{self.config.db_name}"
        return create_engine(connection_string)
    
    def _create_redis_client(self):
        """Create Redis client"""
        return redis.Redis(
            host=self.config.redis_host,
            port=self.config.redis_port,
            decode_responses=True
        )
    
    def extract_raw_data(self) -> Dict[str, pd.DataFrame]:
        """Extract raw data from various sources"""
        logger.info("Starting raw data extraction...")
        
        with mlflow.start_run(run_name="data_extraction", nested=True):
            end_date = datetime.now()
            start_date = end_date - timedelta(days=self.config.lookback_days)
            
            # Extract user events
            user_events_query = """
                SELECT 
                    user_id,
                    event_type,
                    event_timestamp,
                    page_url,
                    session_id,
                    device_type,
                    traffic_source,
                    user_agent,
                    ip_address,
                    custom_properties
                FROM user_events 
                WHERE event_timestamp >= %s AND event_timestamp <= %s
                ORDER BY event_timestamp
            """
            
            user_events = pd.read_sql(
                user_events_query,
                self.db_engine,
                params=[start_date, end_date]
            )
            
            # Extract purchase data
            purchases_query = """
                SELECT 
                    p.user_id,
                    p.order_id,
                    p.purchase_timestamp,
                    p.total_amount,
                    p.currency,
                    p.payment_method,
                    pi.product_id,
                    pi.quantity,
                    pi.unit_price,
                    pi.category_id,
                    pi.brand_id
                FROM purchases p
                JOIN purchase_items pi ON p.order_id = pi.order_id
                WHERE p.purchase_timestamp >= %s AND p.purchase_timestamp <= %s
                ORDER BY p.purchase_timestamp
            """
            
            purchases = pd.read_sql(
                purchases_query,
                self.db_engine,
                params=[start_date, end_date]
            )
            
            # Extract product data
            products_query = """
                SELECT 
                    product_id,
                    name,
                    category_id,
                    brand_id,
                    price,
                    cost,
                    inventory_level,
                    average_rating,
                    review_count,
                    created_at,
                    updated_at
                FROM products
            """
            
            products = pd.read_sql(products_query, self.db_engine)
            
            # Extract user profiles
            users_query = """
                SELECT 
                    user_id,
                    registration_date,
                    last_login_date,
                    country,
                    age_group,
                    gender,
                    subscription_tier,
                    total_lifetime_value,
                    preferred_language
                FROM user_profiles
            """
            
            users = pd.read_sql(users_query, self.db_engine)
            
            raw_data = {
                'user_events': user_events,
                'purchases': purchases,
                'products': products,
                'users': users
            }
            
            # Log extraction metrics
            for table_name, df in raw_data.items():
                mlflow.log_metric(f"{table_name}_rows", len(df))
                mlflow.log_metric(f"{table_name}_columns", len(df.columns))
                logger.info(f"Extracted {len(df)} rows from {table_name}")
            
            return raw_data
    
    def clean_data(self, raw_data: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
        """Clean and validate raw data"""
        logger.info("Starting data cleaning...")
        
        with mlflow.start_run(run_name="data_cleaning", nested=True):
            cleaned_data = {}
            
            for table_name, df in raw_data.items():
                logger.info(f"Cleaning {table_name}...")
                
                # Record initial shape
                initial_rows = len(df)
                
                # Remove duplicates
                df = df.drop_duplicates()
                
                # Handle missing values based on table type
                if table_name == 'user_events':
                    df = self._clean_user_events(df)
                elif table_name == 'purchases':
                    df = self._clean_purchases(df)
                elif table_name == 'products':
                    df = self._clean_products(df)
                elif table_name == 'users':
                    df = self._clean_users(df)
                
                # Record cleaning metrics
                final_rows = len(df)
                rows_removed = initial_rows - final_rows
                
                mlflow.log_metric(f"{table_name}_initial_rows", initial_rows)
                mlflow.log_metric(f"{table_name}_final_rows", final_rows)
                mlflow.log_metric(f"{table_name}_rows_removed", rows_removed)
                mlflow.log_metric(f"{table_name}_removal_rate", rows_removed / initial_rows if initial_rows > 0 else 0)
                
                cleaned_data[table_name] = df
                logger.info(f"Cleaned {table_name}: {initial_rows} -> {final_rows} rows")
            
            return cleaned_data
    
    def _clean_user_events(self, df: pd.DataFrame) -> pd.DataFrame:
        """Clean user events data"""
        # Remove events with invalid user_id
        df = df[df['user_id'].notna() & (df['user_id'] != '')]
        
        # Remove events with invalid timestamps
        df = df[df['event_timestamp'].notna()]
        
        # Standardize event types
        df['event_type'] = df['event_type'].str.lower().str.strip()
        
        # Clean device type
        df['device_type'] = df['device_type'].fillna('unknown')
        df['device_type'] = df['device_type'].str.lower().str.strip()
        
        # Clean traffic source
        df['traffic_source'] = df['traffic_source'].fillna('direct')
        df['traffic_source'] = df['traffic_source'].str.lower().str.strip()
        
        return df
    
    def _clean_purchases(self, df: pd.DataFrame) -> pd.DataFrame:
        """Clean purchase data"""
        # Remove purchases with invalid amounts
        df = df[df['total_amount'] > 0]
        df = df[df['unit_price'] >= 0]
        df = df[df['quantity'] > 0]
        
        # Remove purchases with invalid user_id or product_id
        df = df[df['user_id'].notna() & (df['user_id'] != '')]
        df = df[df['product_id'].notna() & (df['product_id'] != '')]
        
        # Standardize currency
        df['currency'] = df['currency'].fillna('USD')
        df['currency'] = df['currency'].str.upper().str.strip()
        
        # Clean payment method
        df['payment_method'] = df['payment_method'].fillna('unknown')
        df['payment_method'] = df['payment_method'].str.lower().str.strip()
        
        return df
    
    def _clean_products(self, df: pd.DataFrame) -> pd.DataFrame:
        """Clean product data"""
        # Remove products with invalid prices
        df = df[df['price'] >= 0]
        df = df[df['cost'] >= 0]
        
        # Clean inventory levels
        df['inventory_level'] = df['inventory_level'].fillna(0)
        df['inventory_level'] = df['inventory_level'].clip(lower=0)
        
        # Clean ratings
        df['average_rating'] = df['average_rating'].clip(lower=0, upper=5)
        df['review_count'] = df['review_count'].fillna(0).clip(lower=0)
        
        return df
    
    def _clean_users(self, df: pd.DataFrame) -> pd.DataFrame:
        """Clean user profile data"""
        # Clean country
        df['country'] = df['country'].fillna('unknown')
        df['country'] = df['country'].str.upper().str.strip()
        
        # Clean age group
        df['age_group'] = df['age_group'].fillna('unknown')
        
        # Clean subscription tier
        df['subscription_tier'] = df['subscription_tier'].fillna('free')
        df['subscription_tier'] = df['subscription_tier'].str.lower().str.strip()
        
        # Clean lifetime value
        df['total_lifetime_value'] = df['total_lifetime_value'].fillna(0)
        df['total_lifetime_value'] = df['total_lifetime_value'].clip(lower=0)
        
        return df
    
    def engineer_features(self, cleaned_data: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        """Engineer features for ML models"""
        logger.info("Starting feature engineering...")
        
        with mlflow.start_run(run_name="feature_engineering", nested=True):
            # Extract dataframes
            user_events = cleaned_data['user_events']
            purchases = cleaned_data['purchases']
            products = cleaned_data['products']
            users = cleaned_data['users']
            
            # Create user-level features
            user_features = self._create_user_features(user_events, purchases, users)
            
            # Create product-level features
            product_features = self._create_product_features(products, purchases, user_events)
            
            # Combine features
            features_df = pd.merge(user_features, product_features, how='cross')
            
            # Add interaction features
            features_df = self._add_interaction_features(features_df)
            
            # Log feature engineering metrics
            mlflow.log_metric("total_features", len(features_df.columns))
            mlflow.log_metric("feature_rows", len(features_df))
            
            logger.info(f"Generated {len(features_df.columns)} features for {len(features_df)} rows")
            
            return features_df
    
    def _create_user_features(self, user_events: pd.DataFrame, purchases: pd.DataFrame, users: pd.DataFrame) -> pd.DataFrame:
        """Create user-level features"""
        logger.info("Creating user features...")
        
        # Time-based features from events
        user_event_features = user_events.groupby('user_id').agg({
            'event_timestamp': ['count', 'min', 'max'],
            'session_id': 'nunique',
            'page_url': 'nunique'
        }).round(2)
        
        user_event_features.columns = [
            'total_events', 'first_event_date', 'last_event_date',
            'unique_sessions', 'unique_pages_viewed'
        ]
        
        # Calculate days since first and last event
        now = datetime.now()
        user_event_features['days_since_first_event'] = (
            now - pd.to_datetime(user_event_features['first_event_date'])
        ).dt.days
        user_event_features['days_since_last_event'] = (
            now - pd.to_datetime(user_event_features['last_event_date'])
        ).dt.days
        
        # Purchase-based features
        user_purchase_features = purchases.groupby('user_id').agg({
            'total_amount': ['sum', 'mean', 'count', 'std'],
            'quantity': ['sum', 'mean'],
            'purchase_timestamp': ['min', 'max'],
            'order_id': 'nunique',
            'product_id': 'nunique'
        }).round(2)
        
        user_purchase_features.columns = [
            'total_purchase_amount', 'avg_purchase_amount', 'total_purchases',
            'purchase_amount_std', 'total_quantity', 'avg_quantity',
            'first_purchase_date', 'last_purchase_date', 'unique_orders', 'unique_products_purchased'
        ]
        
        # Calculate purchase frequency and recency
        user_purchase_features['days_since_first_purchase'] = (
            now - pd.to_datetime(user_purchase_features['first_purchase_date'])
        ).dt.days
        user_purchase_features['days_since_last_purchase'] = (
            now - pd.to_datetime(user_purchase_features['last_purchase_date'])
        ).dt.days
        
        # Calculate average days between purchases
        user_purchase_features['avg_days_between_purchases'] = (
            user_purchase_features['days_since_first_purchase'] / 
            user_purchase_features['total_purchases'].clip(lower=1)
        ).round(2)
        
        # Merge with user profile data
        user_features = users.set_index('user_id')
        user_features = user_features.join(user_event_features, how='left')
        user_features = user_features.join(user_purchase_features, how='left')
        
        # Fill missing values
        numeric_columns = user_features.select_dtypes(include=[np.number]).columns
        user_features[numeric_columns] = user_features[numeric_columns].fillna(0)
        
        categorical_columns = user_features.select_dtypes(include=['object']).columns
        user_features[categorical_columns] = user_features[categorical_columns].fillna('unknown')
        
        return user_features.reset_index()
    
    def _create_product_features(self, products: pd.DataFrame, purchases: pd.DataFrame, user_events: pd.DataFrame) -> pd.DataFrame:
        """Create product-level features"""
        logger.info("Creating product features...")
        
        # Purchase-based product features
        product_purchase_features = purchases.groupby('product_id').agg({
            'quantity': ['sum', 'mean', 'count'],
            'unit_price': ['mean', 'std'],
            'total_amount': 'sum',
            'user_id': 'nunique'
        }).round(2)
        
        product_purchase_features.columns = [
            'total_quantity_sold', 'avg_quantity_per_order', 'total_orders',
            'avg_selling_price', 'price_volatility', 'total_revenue', 'unique_buyers'
        ]
        
        # Calculate conversion and popularity metrics
        product_purchase_features['revenue_per_buyer'] = (
            product_purchase_features['total_revenue'] / 
            product_purchase_features['unique_buyers'].clip(lower=1)
        ).round(2)
        
        # Event-based product features (if page_url contains product_id)
        # This is a simplified approach - in reality, you'd need proper product page tracking
        product_events = user_events[user_events['page_url'].str.contains('/product/', na=False)]
        
        if not product_events.empty:
            # Extract product ID from URL (simplified)
            product_events['extracted_product_id'] = product_events['page_url'].str.extract(r'/product/([^/?]+)')
            
            product_event_features = product_events.groupby('extracted_product_id').agg({
                'user_id': 'nunique',
                'event_timestamp': 'count'
            }).round(2)
            
            product_event_features.columns = ['unique_viewers', 'total_page_views']
            product_event_features.index.name = 'product_id'
        else:
            product_event_features = pd.DataFrame()
        
        # Merge with product data
        product_features = products.set_index('product_id')
        product_features = product_features.join(product_purchase_features, how='left')
        
        if not product_event_features.empty:
            product_features = product_features.join(product_event_features, how='left')
        
        # Calculate derived features
        product_features['profit_margin'] = (
            (product_features['price'] - product_features['cost']) / 
            product_features['price'].clip(lower=0.01)
        ).round(4)
        
        product_features['inventory_turnover'] = (
            product_features['total_quantity_sold'] / 
            (product_features['inventory_level'] + 1)
        ).round(2)
        
        # Fill missing values
        numeric_columns = product_features.select_dtypes(include=[np.number]).columns
        product_features[numeric_columns] = product_features[numeric_columns].fillna(0)
        
        categorical_columns = product_features.select_dtypes(include=['object']).columns
        product_features[categorical_columns] = product_features[categorical_columns].fillna('unknown')
        
        return product_features.reset_index()
    
    def _add_interaction_features(self, features_df: pd.DataFrame) -> pd.DataFrame:
        """Add interaction features between user and product features"""
        logger.info("Adding interaction features...")
        
        # Price sensitivity features
        features_df['price_vs_user_avg_purchase'] = (
            features_df['price'] / (features_df['avg_purchase_amount'] + 1)
        ).round(2)
        
        # Preference alignment
        features_df['category_match'] = (
            features_df['category_id'] == features_df['favorite_category']
        ).astype(int)
        
        # Purchase power indicators
        features_df['purchase_power_score'] = (
            features_df['total_lifetime_value'] / (features_df['price'] + 1)
        ).round(2)
        
        # Engagement level
        features_df['engagement_score'] = (
            (features_df['total_events'] * features_df['unique_sessions']) / 
            (features_df['days_since_first_event'] + 1)
        ).round(2)
        
        return features_df
    
    def preprocess_features(self, features_df: pd.DataFrame) -> Tuple[pd.DataFrame, Dict]:
        """Preprocess features for ML models"""
        logger.info("Preprocessing features for ML...")
        
        with mlflow.start_run(run_name="feature_preprocessing", nested=True):
            # Separate numeric and categorical features
            numeric_features = features_df.select_dtypes(include=[np.number]).columns.tolist()
            categorical_features = features_df.select_dtypes(include=['object']).columns.tolist()
            
            # Remove ID columns from processing
            id_columns = ['user_id', 'product_id']
            numeric_features = [col for col in numeric_features if col not in id_columns]
            
            preprocessors = {}
            
            # Handle numeric features
            if numeric_features:
                # Impute missing values
                numeric_imputer = SimpleImputer(strategy='median')
                features_df[numeric_features] = numeric_imputer.fit_transform(features_df[numeric_features])
                preprocessors['numeric_imputer'] = numeric_imputer
                
                # Scale numeric features
                scaler = StandardScaler()
                features_df[numeric_features] = scaler.fit_transform(features_df[numeric_features])
                preprocessors['scaler'] = scaler
                
                mlflow.log_metric("numeric_features_count", len(numeric_features))
            
            # Handle categorical features
            if categorical_features:
                categorical_imputer = SimpleImputer(strategy='constant', fill_value='unknown')
                features_df[categorical_features] = categorical_imputer.fit_transform(features_df[categorical_features])
                preprocessors['categorical_imputer'] = categorical_imputer
                
                # Encode categorical features
                label_encoders = {}
                for col in categorical_features:
                    le = LabelEncoder()
                    features_df[col] = le.fit_transform(features_df[col].astype(str))
                    label_encoders[col] = le
                
                preprocessors['label_encoders'] = label_encoders
                mlflow.log_metric("categorical_features_count", len(categorical_features))
            
            # Log preprocessing artifacts
            preprocessor_path = "/tmp/preprocessors.joblib"
            joblib.dump(preprocessors, preprocessor_path)
            mlflow.log_artifact(preprocessor_path, "preprocessors")
            
            logger.info(f"Preprocessed {len(numeric_features)} numeric and {len(categorical_features)} categorical features")
            
            return features_df, preprocessors
    
    def save_to_s3(self, data: pd.DataFrame, file_path: str):
        """Save processed data to S3"""
        logger.info(f"Saving data to S3: {file_path}")
        
        # Save to local temp file first
        temp_file = f"/tmp/{os.path.basename(file_path)}"
        
        if file_path.endswith('.parquet'):
            data.to_parquet(temp_file, index=False)
        elif file_path.endswith('.csv'):
            data.to_csv(temp_file, index=False)
        else:
            raise ValueError("Unsupported file format. Use .parquet or .csv")
        
        # Upload to S3
        self.s3_client.upload_file(
            temp_file,
            self.config.s3_bucket,
            file_path
        )
        
        # Clean up temp file
        os.remove(temp_file)
        
        logger.info(f"Successfully saved {len(data)} rows to s3://{self.config.s3_bucket}/{file_path}")
    
    def update_feature_store(self, features_df: pd.DataFrame):
        """Update Feast feature store with new features"""
        logger.info("Updating Feast feature store...")
        
        try:
            # Prepare user features for Feast
            user_features = features_df[['user_id'] + [col for col in features_df.columns if col.startswith('user_') or col in [
                'total_events', 'unique_sessions', 'total_purchase_amount', 'avg_purchase_amount'
            ]]].drop_duplicates(subset=['user_id'])
            
            user_features['event_timestamp'] = datetime.now()
            
            # Prepare product features for Feast
            product_features = features_df[['product_id'] + [col for col in features_df.columns if col.startswith('product_') or col in [
                'price', 'average_rating', 'total_quantity_sold', 'unique_buyers'
            ]]].drop_duplicates(subset=['product_id'])
            
            product_features['event_timestamp'] = datetime.now()
            
            # Push to Feast (simplified - in practice you'd need proper feature definitions)
            logger.info(f"Updated feature store with {len(user_features)} user features and {len(product_features)} product features")
            
        except Exception as e:
            logger.error(f"Error updating feature store: {str(e)}")
    
    def run_pipeline(self):
        """Run the complete data preprocessing pipeline"""
        logger.info("Starting ML data preprocessing pipeline...")
        
        with mlflow.start_run(run_name="data_preprocessing_pipeline"):
            try:
                # Extract raw data
                raw_data = self.extract_raw_data()
                
                # Clean data
                cleaned_data = self.clean_data(raw_data)
                
                # Engineer features
                features_df = self.engineer_features(cleaned_data)
                
                # Preprocess features
                processed_features, preprocessors = self.preprocess_features(features_df)
                
                # Save processed data
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                
                # Save to S3
                self.save_to_s3(
                    processed_features,
                    f"{self.config.s3_processed_prefix}features_{timestamp}.parquet"
                )
                
                # Update feature store
                self.update_feature_store(processed_features)
                
                # Log pipeline metrics
                mlflow.log_metric("pipeline_success", 1)
                mlflow.log_metric("total_processed_rows", len(processed_features))
                mlflow.log_metric("total_features", len(processed_features.columns))
                
                logger.info("Data preprocessing pipeline completed successfully")
                
                return processed_features, preprocessors
                
            except Exception as e:
                logger.error(f"Pipeline failed: {str(e)}")
                mlflow.log_metric("pipeline_success", 0)
                raise

def main():
    """Main function to run the data preprocessing pipeline"""
    config = Config()
    processor = DataProcessor(config)
    
    try:
        features_df, preprocessors = processor.run_pipeline()
        print(f"Pipeline completed successfully. Processed {len(features_df)} rows with {len(features_df.columns)} features.")
        
    except Exception as e:
        logger.error(f"Pipeline execution failed: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()