#!/usr/bin/env python3
"""
GraphQL Federation Schema Definitions and Service Orchestration
Distributed schema management with automatic service discovery and schema stitching
"""

import asyncio
import logging
import json
import yaml
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any, Union, Set
from dataclasses import dataclass, field
from enum import Enum
import os
import re

# GraphQL and schema tools
import strawberry
from strawberry.federation import FederatedSchema
from strawberry.federation.schema_directives import Key, External, Requires, Provides
from strawberry.types import Info
from strawberry.schema_directive import Location
from graphql import build_schema, print_schema, validate_schema, GraphQLError
from graphql.utilities import schema_from_ast, build_ast_schema

# HTTP client for schema introspection
import httpx

# Configuration
@dataclass
class SchemaConfig:
    schema_registry_url: str = "http://schema-registry:8080"
    schema_refresh_interval: int = 300  # 5 minutes
    enable_schema_validation: bool = True
    enable_schema_caching: bool = True
    schema_cache_ttl: int = 600  # 10 minutes
    
    # Service schema endpoints
    service_schemas: Dict[str, str] = field(default_factory=lambda: {
        'user-service': 'http://user-service:8001/graphql/schema',
        'analytics-service': 'http://analytics-service:8002/graphql/schema',
        'ml-service': 'http://ml-service:8003/graphql/schema',
        'recommendation-service': 'http://recommendation-service:8004/graphql/schema',
        'fraud-detection-service': 'http://fraud-service:8005/graphql/schema',
        'notification-service': 'http://notification-service:8006/graphql/schema',
        'inventory-service': 'http://inventory-service:8007/graphql/schema',
        'payment-service': 'http://payment-service:8008/graphql/schema'
    })

# Schema entities and relationships
@strawberry.federation.type(keys=["id"])
class User:
    id: strawberry.ID
    email: str
    name: str
    created_at: datetime
    
    @strawberry.field
    def orders(self) -> List["Order"]:
        return []  # Resolved by order service
    
    @strawberry.field
    def analytics_profile(self) -> Optional["UserAnalytics"]:
        return None  # Resolved by analytics service
    
    @strawberry.field
    def recommendations(self) -> List["Recommendation"]:
        return []  # Resolved by recommendation service

@strawberry.federation.type(keys=["id"])
class Product:
    id: strawberry.ID
    name: str
    description: str
    price: float
    category: str
    created_at: datetime
    
    @strawberry.field
    def analytics(self) -> Optional["ProductAnalytics"]:
        return None  # Resolved by analytics service
    
    @strawberry.field
    def recommendations(self) -> List["Recommendation"]:
        return []  # Resolved by recommendation service
    
    @strawberry.field
    def inventory(self) -> Optional["InventoryItem"]:
        return None  # Resolved by inventory service

@strawberry.federation.type(keys=["id"])
class Order:
    id: strawberry.ID
    user_id: strawberry.ID
    total_amount: float
    status: str
    created_at: datetime
    
    @strawberry.field
    def user(self) -> Optional[User]:
        return None  # Resolved by user service
    
    @strawberry.field
    def items(self) -> List["OrderItem"]:
        return []  # Resolved by order service
    
    @strawberry.field
    def fraud_score(self) -> Optional["FraudScore"]:
        return None  # Resolved by fraud detection service
    
    @strawberry.field
    def payment(self) -> Optional["Payment"]:
        return None  # Resolved by payment service

@strawberry.federation.type(keys=["order_id", "product_id"])
class OrderItem:
    order_id: strawberry.ID
    product_id: strawberry.ID
    quantity: int
    unit_price: float
    
    @strawberry.field
    def product(self) -> Optional[Product]:
        return None  # Resolved by product service
    
    @strawberry.field
    def order(self) -> Optional[Order]:
        return None  # Resolved by order service

@strawberry.federation.type(keys=["user_id"])
class UserAnalytics:
    user_id: strawberry.ID
    total_orders: int
    total_spent: float
    avg_order_value: float
    last_activity: datetime
    churn_probability: float
    lifetime_value: float
    
    @strawberry.field
    def user(self) -> Optional[User]:
        return None  # Resolved by user service
    
    @strawberry.field
    def behavior_insights(self) -> List["BehaviorInsight"]:
        return []  # Resolved by analytics service

@strawberry.federation.type(keys=["product_id"])
class ProductAnalytics:
    product_id: strawberry.ID
    total_views: int
    total_sales: int
    conversion_rate: float
    revenue: float
    popularity_score: float
    
    @strawberry.field
    def product(self) -> Optional[Product]:
        return None  # Resolved by product service
    
    @strawberry.field
    def performance_metrics(self) -> List["PerformanceMetric"]:
        return []  # Resolved by analytics service

@strawberry.federation.type(keys=["id"])
class Recommendation:
    id: strawberry.ID
    user_id: strawberry.ID
    item_id: strawberry.ID
    item_type: str
    score: float
    reason: str
    created_at: datetime
    
    @strawberry.field
    def user(self) -> Optional[User]:
        return None  # Resolved by user service
    
    @strawberry.field
    def item(self) -> Optional[Union[Product, "Category"]]:
        return None  # Resolved by appropriate service

@strawberry.federation.type(keys=["prediction_id"])
class MLPrediction:
    prediction_id: strawberry.ID
    model_name: str
    model_version: str
    input_features: Dict[str, Any]
    prediction: str
    confidence: float
    created_at: datetime
    
    @strawberry.field
    def model_metadata(self) -> Optional["MLModel"]:
        return None  # Resolved by ML service

@strawberry.federation.type(keys=["order_id"])
class FraudScore:
    order_id: strawberry.ID
    score: float
    risk_level: str
    factors: List[str]
    created_at: datetime
    
    @strawberry.field
    def order(self) -> Optional[Order]:
        return None  # Resolved by order service
    
    @strawberry.field
    def alerts(self) -> List["FraudAlert"]:
        return []  # Resolved by fraud detection service

@strawberry.federation.type(keys=["product_id"])
class InventoryItem:
    product_id: strawberry.ID
    quantity: int
    reserved: int
    available: int
    reorder_point: int
    last_updated: datetime
    
    @strawberry.field
    def product(self) -> Optional[Product]:
        return None  # Resolved by product service

@strawberry.federation.type(keys=["order_id"])
class Payment:
    order_id: strawberry.ID
    amount: float
    currency: str
    method: str
    status: str
    transaction_id: str
    created_at: datetime
    
    @strawberry.field
    def order(self) -> Optional[Order]:
        return None  # Resolved by order service

# Supporting types
@strawberry.type
class BehaviorInsight:
    insight_type: str
    description: str
    confidence: float
    impact_score: float

@strawberry.type
class PerformanceMetric:
    metric_name: str
    value: float
    period: str
    trend: str

@strawberry.type
class MLModel:
    name: str
    version: str
    algorithm: str
    accuracy: float
    training_date: datetime

@strawberry.type
class FraudAlert:
    alert_id: strawberry.ID
    severity: str
    description: str
    triggered_at: datetime

@strawberry.type
class Category:
    id: strawberry.ID
    name: str
    description: str
    parent_id: Optional[strawberry.ID]

# Schema management and federation
class SchemaStitcher:
    """Handles GraphQL schema federation and stitching"""
    
    def __init__(self, config: SchemaConfig):
        self.config = config
        self.http_client = httpx.AsyncClient(timeout=30.0)
        self.service_schemas: Dict[str, str] = {}
        self.federated_schema = None
        self.last_update = None
    
    async def fetch_service_schema(self, service_name: str, schema_url: str) -> Optional[str]:
        """Fetch GraphQL schema from a service"""
        try:
            response = await self.http_client.get(schema_url)
            if response.status_code == 200:
                return response.text
            else:
                logger.error(f"Failed to fetch schema from {service_name}: HTTP {response.status_code}")
                return None
        except Exception as e:
            logger.error(f"Error fetching schema from {service_name}: {str(e)}")
            return None
    
    async def introspect_service_schema(self, service_name: str, graphql_url: str) -> Optional[str]:
        """Introspect GraphQL schema from a service using introspection query"""
        try:
            introspection_query = """
            query IntrospectionQuery {
                __schema {
                    queryType { name }
                    mutationType { name }
                    subscriptionType { name }
                    types {
                        ...FullType
                    }
                    directives {
                        name
                        description
                        locations
                        args {
                            ...InputValue
                        }
                    }
                }
            }
            
            fragment FullType on __Type {
                kind
                name
                description
                fields(includeDeprecated: true) {
                    name
                    description
                    args {
                        ...InputValue
                    }
                    type {
                        ...TypeRef
                    }
                    isDeprecated
                    deprecationReason
                }
                inputFields {
                    ...InputValue
                }
                interfaces {
                    ...TypeRef
                }
                enumValues(includeDeprecated: true) {
                    name
                    description
                    isDeprecated
                    deprecationReason
                }
                possibleTypes {
                    ...TypeRef
                }
            }
            
            fragment InputValue on __InputValue {
                name
                description
                type { ...TypeRef }
                defaultValue
            }
            
            fragment TypeRef on __Type {
                kind
                name
                ofType {
                    kind
                    name
                    ofType {
                        kind
                        name
                        ofType {
                            kind
                            name
                            ofType {
                                kind
                                name
                                ofType {
                                    kind
                                    name
                                    ofType {
                                        kind
                                        name
                                        ofType {
                                            kind
                                            name
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            """
            
            response = await self.http_client.post(
                graphql_url,
                json={"query": introspection_query}
            )
            
            if response.status_code == 200:
                introspection_result = response.json()
                if "data" in introspection_result and "__schema" in introspection_result["data"]:
                    # Convert introspection result to SDL
                    return self._introspection_to_sdl(introspection_result["data"]["__schema"])
            
            logger.error(f"Failed to introspect schema from {service_name}: HTTP {response.status_code}")
            return None
            
        except Exception as e:
            logger.error(f"Error introspecting schema from {service_name}: {str(e)}")
            return None
    
    def _introspection_to_sdl(self, schema_introspection: Dict[str, Any]) -> str:
        """Convert GraphQL introspection result to SDL (Schema Definition Language)"""
        # This is a simplified conversion - in production, use a proper library like graphql-core
        sdl_parts = []
        
        # Add types
        for type_def in schema_introspection.get("types", []):
            if type_def["name"].startswith("__"):
                continue  # Skip introspection types
            
            if type_def["kind"] == "OBJECT":
                sdl_parts.append(f"type {type_def['name']} {{")
                for field in type_def.get("fields", []):
                    sdl_parts.append(f"  {field['name']}: {self._type_to_string(field['type'])}")
                sdl_parts.append("}")
            elif type_def["kind"] == "ENUM":
                sdl_parts.append(f"enum {type_def['name']} {{")
                for value in type_def.get("enumValues", []):
                    sdl_parts.append(f"  {value['name']}")
                sdl_parts.append("}")
        
        return "\n".join(sdl_parts)
    
    def _type_to_string(self, type_ref: Dict[str, Any]) -> str:
        """Convert GraphQL type reference to string representation"""
        if type_ref["kind"] == "NON_NULL":
            return f"{self._type_to_string(type_ref['ofType'])}!"
        elif type_ref["kind"] == "LIST":
            return f"[{self._type_to_string(type_ref['ofType'])}]"
        else:
            return type_ref["name"]
    
    async def fetch_all_schemas(self) -> Dict[str, str]:
        """Fetch schemas from all configured services"""
        schemas = {}
        
        for service_name, schema_url in self.config.service_schemas.items():
            # Try to fetch schema directly first
            schema = await self.fetch_service_schema(service_name, schema_url)
            
            # If that fails, try introspection
            if not schema:
                graphql_url = schema_url.replace("/schema", "")
                schema = await self.introspect_service_schema(service_name, graphql_url)
            
            if schema:
                schemas[service_name] = schema
                logger.info(f"Successfully fetched schema from {service_name}")
            else:
                logger.warning(f"Failed to fetch schema from {service_name}")
        
        return schemas
    
    def validate_schema_compatibility(self, schemas: Dict[str, str]) -> List[str]:
        """Validate schema compatibility for federation"""
        issues = []
        
        try:
            # Check for duplicate type definitions
            type_definitions = {}
            
            for service_name, schema_sdl in schemas.items():
                # Extract type names from schema
                type_names = re.findall(r'type\s+(\w+)', schema_sdl)
                
                for type_name in type_names:
                    if type_name in type_definitions:
                        if type_definitions[type_name] != service_name:
                            issues.append(
                                f"Type '{type_name}' is defined in both {type_definitions[type_name]} "
                                f"and {service_name}"
                            )
                    else:
                        type_definitions[type_name] = service_name
            
            # Check for federation directives
            for service_name, schema_sdl in schemas.items():
                if "@key" not in schema_sdl and "@external" not in schema_sdl:
                    logger.warning(f"Service {service_name} may not be properly configured for federation")
            
        except Exception as e:
            issues.append(f"Schema validation error: {str(e)}")
        
        return issues
    
    def stitch_schemas(self, schemas: Dict[str, str]) -> str:
        """Stitch multiple GraphQL schemas into a federated schema"""
        try:
            # Start with base schema
            federated_schema_parts = [
                "directive @key(fields: String!) on OBJECT | INTERFACE",
                "directive @external on FIELD_DEFINITION",
                "directive @requires(fields: String!) on FIELD_DEFINITION",
                "directive @provides(fields: String!) on FIELD_DEFINITION",
                "",
                "type Query {",
                "  _service: _Service!",
                "}",
                "",
                "type _Service {",
                "  sdl: String",
                "}",
                ""
            ]
            
            # Add schemas from services
            for service_name, schema_sdl in schemas.items():
                federated_schema_parts.append(f"# Schema from {service_name}")
                federated_schema_parts.append(schema_sdl)
                federated_schema_parts.append("")
            
            return "\n".join(federated_schema_parts)
            
        except Exception as e:
            logger.error(f"Error stitching schemas: {str(e)}")
            return ""
    
    async def update_federated_schema(self) -> bool:
        """Update the federated schema from all services"""
        try:
            logger.info("Updating federated schema...")
            
            # Fetch all service schemas
            schemas = await self.fetch_all_schemas()
            
            if not schemas:
                logger.error("No schemas fetched from services")
                return False
            
            # Validate compatibility
            if self.config.enable_schema_validation:
                issues = self.validate_schema_compatibility(schemas)
                if issues:
                    logger.error(f"Schema compatibility issues: {issues}")
                    return False
            
            # Stitch schemas together
            federated_schema_sdl = self.stitch_schemas(schemas)
            
            if federated_schema_sdl:
                self.service_schemas = schemas
                self.federated_schema = federated_schema_sdl
                self.last_update = datetime.now(timezone.utc)
                
                logger.info(f"Federated schema updated with {len(schemas)} services")
                return True
            else:
                logger.error("Failed to create federated schema")
                return False
                
        except Exception as e:
            logger.error(f"Error updating federated schema: {str(e)}")
            return False
    
    async def get_federated_schema(self) -> Optional[str]:
        """Get the current federated schema"""
        # Check if schema needs refresh
        if (self.federated_schema is None or 
            self.last_update is None or
            (datetime.now(timezone.utc) - self.last_update).total_seconds() > self.config.schema_refresh_interval):
            
            await self.update_federated_schema()
        
        return self.federated_schema
    
    async def close(self):
        """Close HTTP client"""
        await self.http_client.aclose()

# Schema registry for centralized schema management
class SchemaRegistry:
    """Centralized schema registry for microservices"""
    
    def __init__(self, config: SchemaConfig):
        self.config = config
        self.stitcher = SchemaStitcher(config)
        self.schemas_cache: Dict[str, Dict[str, Any]] = {}
    
    async def register_service_schema(self, service_name: str, schema_sdl: str, 
                                    version: str = "1.0.0") -> bool:
        """Register a service schema"""
        try:
            schema_info = {
                "service_name": service_name,
                "schema_sdl": schema_sdl,
                "version": version,
                "registered_at": datetime.now(timezone.utc),
                "status": "active"
            }
            
            self.schemas_cache[service_name] = schema_info
            logger.info(f"Registered schema for service {service_name} (v{version})")
            
            # Trigger federated schema update
            await self.stitcher.update_federated_schema()
            
            return True
            
        except Exception as e:
            logger.error(f"Error registering schema for {service_name}: {str(e)}")
            return False
    
    async def get_service_schema(self, service_name: str) -> Optional[Dict[str, Any]]:
        """Get schema for a specific service"""
        return self.schemas_cache.get(service_name)
    
    async def list_service_schemas(self) -> Dict[str, Dict[str, Any]]:
        """List all registered service schemas"""
        return self.schemas_cache.copy()
    
    async def get_federated_schema(self) -> Optional[str]:
        """Get the complete federated schema"""
        return await self.stitcher.get_federated_schema()
    
    async def validate_query_against_schema(self, query: str) -> List[str]:
        """Validate a GraphQL query against the federated schema"""
        try:
            federated_schema = await self.get_federated_schema()
            if not federated_schema:
                return ["Federated schema not available"]
            
            # Parse and validate query
            from graphql import parse, validate, build_schema
            
            schema = build_schema(federated_schema)
            document = parse(query)
            errors = validate(schema, document)
            
            return [str(error) for error in errors]
            
        except Exception as e:
            return [f"Query validation error: {str(e)}"]
    
    async def close(self):
        """Close schema registry"""
        await self.stitcher.close()

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def main():
    """Test schema federation"""
    config = SchemaConfig()
    registry = SchemaRegistry(config)
    
    try:
        # Get federated schema
        federated_schema = await registry.get_federated_schema()
        
        if federated_schema:
            print("✅ Federated schema created successfully")
            print(f"Schema length: {len(federated_schema)} characters")
            
            # Save schema to file
            with open("/tmp/federated_schema.graphql", "w") as f:
                f.write(federated_schema)
            print("📄 Schema saved to /tmp/federated_schema.graphql")
        else:
            print("❌ Failed to create federated schema")
        
        # List service schemas
        schemas = await registry.list_service_schemas()
        print(f"📋 Registered schemas: {list(schemas.keys())}")
        
    finally:
        await registry.close()

if __name__ == "__main__":
    asyncio.run(main())