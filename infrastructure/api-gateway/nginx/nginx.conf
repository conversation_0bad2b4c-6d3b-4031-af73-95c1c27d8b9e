# NGINX Configuration for GraphQL Federation Gateway
# High-performance load balancing with SSL termination

user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

# Optimize for high concurrency
events {
    worker_connections 4096;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Logging format
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    'rt=$request_time uct="$upstream_connect_time" '
                    'uht="$upstream_header_time" urt="$upstream_response_time"';

    access_log /var/log/nginx/access.log main;

    # Performance optimizations
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 10M;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # Rate limiting zones
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=graphql:10m rate=100r/m;
    
    # Connection limiting
    limit_conn_zone $binary_remote_addr zone=perip:10m;
    limit_conn_zone $server_name zone=perserver:10m;

    # Upstream servers for GraphQL Gateway
    upstream graphql_gateway {
        least_conn;
        server graphql-gateway:8000 max_fails=3 fail_timeout=30s;
        # Add more replicas as needed
        # server graphql-gateway-2:8000 max_fails=3 fail_timeout=30s;
        # server graphql-gateway-3:8000 max_fails=3 fail_timeout=30s;
        
        keepalive 32;
    }

    # Upstream for Schema Registry
    upstream schema_registry {
        server schema-registry:8080 max_fails=3 fail_timeout=30s;
        keepalive 16;
    }

    # Health check upstream
    upstream health_check {
        server graphql-gateway:8000;
        keepalive 8;
    }

    # SSL Configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Security headers
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' wss: ws:;" always;

    # Main server block
    server {
        listen 80;
        listen [::]:80;
        server_name analytics-gateway.local _;

        # Redirect HTTP to HTTPS in production
        # return 301 https://$server_name$request_uri;

        # For development, serve directly
        location / {
            return 301 https://$server_name$request_uri;
        }

        # Health check endpoint (allow HTTP for load balancers)
        location /health {
            proxy_pass http://health_check;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            access_log off;
        }
    }

    # HTTPS server block
    server {
        listen 443 ssl http2;
        listen [::]:443 ssl http2;
        server_name analytics-gateway.local _;

        # SSL certificate (replace with your certificates)
        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;

        # Enable HSTS
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

        # Rate limiting
        limit_req zone=api burst=20 nodelay;
        limit_conn perip 10;
        limit_conn perserver 100;

        # GraphQL endpoint
        location /graphql {
            # Apply specific rate limiting for GraphQL
            limit_req zone=graphql burst=50 nodelay;
            
            # CORS headers
            add_header Access-Control-Allow-Origin "$http_origin" always;
            add_header Access-Control-Allow-Methods "GET, POST, OPTIONS" always;
            add_header Access-Control-Allow-Headers "Authorization, Content-Type, Accept, Origin, X-Requested-With" always;
            add_header Access-Control-Allow-Credentials "true" always;
            add_header Access-Control-Max-Age "3600" always;

            # Handle preflight requests
            if ($request_method = 'OPTIONS') {
                return 204;
            }

            # Proxy to GraphQL gateway
            proxy_pass http://graphql_gateway;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $host;
            proxy_set_header X-Forwarded-Port $server_port;
            
            # Timeouts
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
            
            # Buffer settings
            proxy_buffering on;
            proxy_buffer_size 128k;
            proxy_buffers 4 256k;
            proxy_busy_buffers_size 256k;
        }

        # WebSocket endpoint for GraphQL subscriptions
        location /graphql/ws {
            proxy_pass http://graphql_gateway;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # WebSocket specific settings
            proxy_read_timeout 3600s;
            proxy_send_timeout 3600s;
            proxy_connect_timeout 60s;
        }

        # Schema registry endpoint
        location /schema {
            proxy_pass http://schema_registry;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Cache schema responses
            proxy_cache_valid 200 302 10m;
            proxy_cache_valid 404 1m;
        }

        # Services endpoint
        location /services {
            proxy_pass http://graphql_gateway;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Health check endpoint
        location /health {
            proxy_pass http://health_check;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            access_log off;
        }

        # Metrics endpoint (restrict access)
        location /metrics {
            allow 10.0.0.0/8;
            allow **********/12;
            allow ***********/16;
            deny all;
            
            proxy_pass http://graphql_gateway;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # GraphQL Playground (development only)
        location /playground {
            # Comment out in production
            proxy_pass http://graphql_gateway;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Static assets
        location /static/ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            try_files $uri $uri/ =404;
        }

        # Favicon
        location = /favicon.ico {
            log_not_found off;
            access_log off;
        }

        # Robots.txt
        location = /robots.txt {
            log_not_found off;
            access_log off;
        }

        # Security: block sensitive files
        location ~ /\. {
            deny all;
            access_log off;
            log_not_found off;
        }

        location ~ ~$ {
            deny all;
            access_log off;
            log_not_found off;
        }

        # Custom error pages
        error_page 404 /404.html;
        error_page 500 502 503 504 /50x.html;
        
        location = /50x.html {
            root /usr/share/nginx/html;
        }
    }

    # Monitoring and admin interface (restrict access)
    server {
        listen 8080;
        server_name admin.analytics-gateway.local;
        
        # Restrict access to admin interface
        allow 10.0.0.0/8;
        allow **********/12;
        allow ***********/16;
        deny all;

        location /nginx_status {
            stub_status on;
            access_log off;
        }

        location /health {
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
    }
}

# Stream block for TCP/UDP load balancing (if needed)
stream {
    # Example: Load balance Redis connections
    upstream redis_cluster {
        server redis-cluster:6379 max_fails=3 fail_timeout=30s;
    }
    
    server {
        listen 6379;
        proxy_pass redis_cluster;
        proxy_timeout 1s;
        proxy_responses 1;
        error_log /var/log/nginx/redis.log;
    }
}