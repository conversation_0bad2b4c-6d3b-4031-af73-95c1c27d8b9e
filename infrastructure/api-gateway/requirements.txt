# GraphQL Federation Gateway Requirements

# FastAPI and ASGI
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# GraphQL and Strawberry
strawberry-graphql[fastapi]==0.214.1
graphql-core==3.2.3

# HTTP client
httpx==0.25.2
aiohttp==3.9.1

# Redis and caching
redis==5.0.1
aioredis==2.0.1

# Authentication and security
pyjwt[crypto]==2.8.0
passlib[bcrypt]==1.7.4
python-jose[cryptography]==3.3.0

# Rate limiting
slowapi==0.1.9
limits==3.6.0

# Database connectivity
asyncpg==0.29.0
sqlalchemy[asyncio]==2.0.23
psycopg2-binary==2.9.9

# Monitoring and observability
prometheus-client==0.19.0
opentelemetry-api==1.21.0
opentelemetry-sdk==1.21.0
opentelemetry-instrumentation-fastapi==0.42b0
opentelemetry-instrumentation-httpx==0.42b0
opentelemetry-instrumentation-redis==0.42b0
jaeger-client==4.8.0

# Configuration and environment
pydantic==2.5.0
pydantic-settings==2.1.0
python-dotenv==1.0.0

# Logging and utilities
structlog==23.2.0
rich==13.7.0
typer==0.9.0
click==8.1.7

# Data validation and serialization
marshmallow==3.20.1
cerberus==1.3.5

# Async utilities
asyncio-mqtt==0.16.1
aiokafka==0.8.11

# Testing (development)
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-mock==3.12.0
httpx-oauth==0.10.2

# Code quality
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# Performance
uvloop==0.19.0
orjson==3.9.10

# Cryptography
cryptography==41.0.8
bcrypt==4.1.2

# WebSocket support
websockets==12.0
starlette==0.27.0

# Schema validation
jsonschema==4.20.0
pyyaml==6.0.1

# Service discovery
consul-python==1.1.0
etcd3==0.12.0

# Load balancing and circuit breaker
circuit-breaker==1.1.0

# Metrics and health checks
psutil==5.9.6
distro==1.8.0