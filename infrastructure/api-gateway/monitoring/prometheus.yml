# Prometheus Configuration for GraphQL Federation Gateway Monitoring

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'analytics-platform'
    environment: 'production'

# Rule files
rule_files:
  - "alert_rules.yml"

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# Scrape configurations
scrape_configs:
  # GraphQL Federation Gateway
  - job_name: 'graphql-gateway'
    static_configs:
      - targets: ['graphql-gateway:8000']
    metrics_path: '/metrics'
    scrape_interval: 15s
    scrape_timeout: 10s
    scheme: http
    
  # Schema Registry
  - job_name: 'schema-registry'
    static_configs:
      - targets: ['schema-registry:8080']
    metrics_path: '/metrics'
    scrape_interval: 30s
    
  # NGINX Load Balancer
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx-lb:8080']
    metrics_path: '/nginx_status'
    scrape_interval: 15s
    
  # Redis Cluster
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-cluster:6379']
    scrape_interval: 15s
    
  # Microservices
  - job_name: 'user-service'
    static_configs:
      - targets: ['user-service:8001']
    metrics_path: '/metrics'
    scrape_interval: 15s
    
  - job_name: 'analytics-service'
    static_configs:
      - targets: ['analytics-service:8002']
    metrics_path: '/metrics'
    scrape_interval: 15s
    
  - job_name: 'ml-service'
    static_configs:
      - targets: ['ml-service:8003']
    metrics_path: '/metrics'
    scrape_interval: 15s
    
  - job_name: 'recommendation-service'
    static_configs:
      - targets: ['recommendation-service:8004']
    metrics_path: '/metrics'
    scrape_interval: 15s
    
  - job_name: 'fraud-detection-service'
    static_configs:
      - targets: ['fraud-detection-service:8005']
    metrics_path: '/metrics'
    scrape_interval: 15s
    
  - job_name: 'notification-service'
    static_configs:
      - targets: ['notification-service:8006']
    metrics_path: '/metrics'
    scrape_interval: 15s
    
  # Infrastructure Components
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-user:5432']
    scrape_interval: 30s
    
  - job_name: 'clickhouse'
    static_configs:
      - targets: ['clickhouse:8123']
    metrics_path: '/metrics'
    scrape_interval: 30s
    
  - job_name: 'kafka'
    static_configs:
      - targets: ['kafka:9092']
    scrape_interval: 30s
    
  # System monitoring
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 15s
    
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 15s
    
  # Grafana
  - job_name: 'grafana'
    static_configs:
      - targets: ['grafana:3000']
    metrics_path: '/metrics'
    scrape_interval: 30s
    
  # Jaeger
  - job_name: 'jaeger'
    static_configs:
      - targets: ['jaeger:14269']
    metrics_path: '/metrics'
    scrape_interval: 30s

# Remote write configuration (for long-term storage)
remote_write:
  - url: "http://cortex:9009/api/prom/push"
    queue_config:
      max_samples_per_send: 10000
      max_shards: 200
      capacity: 500000