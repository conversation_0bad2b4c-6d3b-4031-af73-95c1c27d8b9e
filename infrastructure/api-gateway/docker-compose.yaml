# GraphQL Federation API Gateway Docker Compose
# Production-ready deployment with load balancing and monitoring

version: '3.8'

services:
  # GraphQL Federation Gateway
  graphql-gateway:
    build:
      context: .
      dockerfile: Dockerfile.gateway
    ports:
      - "8000:8000"
    environment:
      - GATEWAY_HOST=0.0.0.0
      - GATEWAY_PORT=8000
      - JWT_SECRET=${JWT_SECRET:-your-secret-key-change-in-production}
      - REDIS_URL=redis://redis-cluster:6379/0
      - RATE_LIMIT_PER_MINUTE=100
      - RATE_LIMIT_PER_HOUR=1000
    depends_on:
      - redis-cluster
      - schema-registry
    volumes:
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      replicas: 3
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'
    networks:
      - analytics-network

  # Schema Registry Service
  schema-registry:
    build:
      context: .
      dockerfile: Dockerfile.schema-registry
    ports:
      - "8080:8080"
    environment:
      - REGISTRY_HOST=0.0.0.0
      - REGISTRY_PORT=8080
      - REDIS_URL=redis://redis-cluster:6379/1
      - SCHEMA_REFRESH_INTERVAL=300
    depends_on:
      - redis-cluster
    volumes:
      - ./schemas:/app/schemas
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - analytics-network

  # NGINX Load Balancer
  nginx-lb:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - graphql-gateway
    networks:
      - analytics-network
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.2'

  # Redis Cluster for Caching and Pub/Sub
  redis-cluster:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --maxmemory 1gb --maxmemory-policy allkeys-lru
    volumes:
      - redis-data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - analytics-network
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.3'

  # Prometheus for Metrics
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    networks:
      - analytics-network

  # Grafana for Visualization
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    networks:
      - analytics-network

  # Jaeger for Distributed Tracing
  jaeger:
    image: jaegertracing/all-in-one:latest
    ports:
      - "16686:16686"
      - "14268:14268"
    environment:
      - COLLECTOR_OTLP_ENABLED=true
    networks:
      - analytics-network

  # Service Discovery and Health Check
  consul:
    image: consul:latest
    ports:
      - "8500:8500"
    command: consul agent -dev -client=0.0.0.0 -ui
    volumes:
      - consul-data:/consul/data
    networks:
      - analytics-network

  # API Rate Limiting and Security
  api-gateway-security:
    image: kong:latest
    ports:
      - "8001:8001"
      - "8444:8444"
    environment:
      - KONG_DATABASE=off
      - KONG_DECLARATIVE_CONFIG=/kong/declarative/kong.yml
      - KONG_PROXY_ACCESS_LOG=/dev/stdout
      - KONG_ADMIN_ACCESS_LOG=/dev/stdout
      - KONG_PROXY_ERROR_LOG=/dev/stderr
      - KONG_ADMIN_ERROR_LOG=/dev/stderr
      - KONG_ADMIN_LISTEN=0.0.0.0:8001
    volumes:
      - ./kong/kong.yml:/kong/declarative/kong.yml
    networks:
      - analytics-network

# Microservices (External services that the gateway federates)
  user-service:
    image: analytics-platform/user-service:latest
    ports:
      - "8001:8001"
    environment:
      - SERVICE_PORT=8001
      - DATABASE_URL=*********************************************/userdb
      - REDIS_URL=redis://redis-cluster:6379/2
    depends_on:
      - postgres-user
      - redis-cluster
    networks:
      - analytics-network

  analytics-service:
    image: analytics-platform/analytics-service:latest
    ports:
      - "8002:8002"
    environment:
      - SERVICE_PORT=8002
      - CLICKHOUSE_URL=http://clickhouse:8123
      - KAFKA_BROKERS=kafka:9092
    depends_on:
      - clickhouse
      - kafka
    networks:
      - analytics-network

  ml-service:
    image: analytics-platform/ml-service:latest
    ports:
      - "8003:8003"
    environment:
      - SERVICE_PORT=8003
      - MODEL_STORE=s3://ml-models
      - MLFLOW_TRACKING_URI=http://mlflow:5000
    networks:
      - analytics-network

  recommendation-service:
    image: analytics-platform/recommendation-service:latest
    ports:
      - "8004:8004"
    environment:
      - SERVICE_PORT=8004
      - REDIS_URL=redis://redis-cluster:6379/3
      - VECTOR_DB_URL=http://milvus:19530
    depends_on:
      - redis-cluster
    networks:
      - analytics-network

  fraud-detection-service:
    image: analytics-platform/fraud-service:latest
    ports:
      - "8005:8005"
    environment:
      - SERVICE_PORT=8005
      - STREAM_PROCESSOR=kafka:9092
      - ALERT_QUEUE=redis://redis-cluster:6379/4
    depends_on:
      - kafka
      - redis-cluster
    networks:
      - analytics-network

  notification-service:
    image: analytics-platform/notification-service:latest
    ports:
      - "8006:8006"
    environment:
      - SERVICE_PORT=8006
      - EMAIL_PROVIDER=sendgrid
      - SMS_PROVIDER=twilio
      - WEBHOOK_QUEUE=redis://redis-cluster:6379/5
    depends_on:
      - redis-cluster
    networks:
      - analytics-network

# Supporting Infrastructure
  postgres-user:
    image: postgres:14
    environment:
      - POSTGRES_DB=userdb
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres-user-data:/var/lib/postgresql/data
    networks:
      - analytics-network

  clickhouse:
    image: clickhouse/clickhouse-server:latest
    ports:
      - "8123:8123"
      - "9000:9000"
    volumes:
      - clickhouse-data:/var/lib/clickhouse
    networks:
      - analytics-network

  kafka:
    image: confluentinc/cp-kafka:latest
    ports:
      - "9092:9092"
    environment:
      - KAFKA_BROKER_ID=1
      - KAFKA_ZOOKEEPER_CONNECT=zookeeper:2181
      - KAFKA_ADVERTISED_LISTENERS=PLAINTEXT://kafka:9092
      - KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR=1
    depends_on:
      - zookeeper
    volumes:
      - kafka-data:/var/lib/kafka/data
    networks:
      - analytics-network

  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    environment:
      - ZOOKEEPER_CLIENT_PORT=2181
      - ZOOKEEPER_TICK_TIME=2000
    volumes:
      - zookeeper-data:/var/lib/zookeeper
    networks:
      - analytics-network

volumes:
  redis-data:
  postgres-user-data:
  clickhouse-data:
  kafka-data:
  zookeeper-data:
  prometheus-data:
  grafana-data:
  consul-data:

networks:
  analytics-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16