# Kubernetes Deployment for GraphQL Federation Gateway
# Production-ready deployment with auto-scaling and high availability

apiVersion: v1
kind: Namespace
metadata:
  name: graphql-federation
  labels:
    name: graphql-federation

---
# ConfigMap for Gateway Configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: gateway-config
  namespace: graphql-federation
data:
  gateway.yaml: |
    gateway:
      host: "0.0.0.0"
      port: 8000
      name: "analytics-gateway"
      
    security:
      jwt_secret: "${JWT_SECRET}"
      jwt_algorithm: "HS256"
      jwt_expire_minutes: 60
      
    rate_limiting:
      per_minute: 100
      per_hour: 1000
      
    services:
      user-service:
        url: "http://user-service.services.svc.cluster.local:8001"
        health_check: "/health"
        schema_endpoint: "/graphql/schema"
      analytics-service:
        url: "http://analytics-service.services.svc.cluster.local:8002"
        health_check: "/health"
        schema_endpoint: "/graphql/schema"
      ml-service:
        url: "http://ml-service.services.svc.cluster.local:8003"
        health_check: "/health"
        schema_endpoint: "/graphql/schema"
      recommendation-service:
        url: "http://recommendation-service.services.svc.cluster.local:8004"
        health_check: "/health"
        schema_endpoint: "/graphql/schema"
      fraud-detection-service:
        url: "http://fraud-service.services.svc.cluster.local:8005"
        health_check: "/health"
        schema_endpoint: "/graphql/schema"
      notification-service:
        url: "http://notification-service.services.svc.cluster.local:8006"
        health_check: "/health"
        schema_endpoint: "/graphql/schema"

---
# Secret for sensitive configuration
apiVersion: v1
kind: Secret
metadata:
  name: gateway-secrets
  namespace: graphql-federation
type: Opaque
data:
  jwt-secret: ${JWT_SECRET_B64}
  redis-password: ${REDIS_PASSWORD_B64}

---
# Service Account
apiVersion: v1
kind: ServiceAccount
metadata:
  name: graphql-gateway
  namespace: graphql-federation

---
# ClusterRole for service discovery
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: graphql-gateway
rules:
- apiGroups: [""]
  resources: ["services", "endpoints"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apps"]
  resources: ["deployments"]
  verbs: ["get", "list", "watch"]

---
# ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: graphql-gateway
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: graphql-gateway
subjects:
- kind: ServiceAccount
  name: graphql-gateway
  namespace: graphql-federation

---
# Deployment for GraphQL Gateway
apiVersion: apps/v1
kind: Deployment
metadata:
  name: graphql-gateway
  namespace: graphql-federation
  labels:
    app: graphql-gateway
    version: v1
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 1
  selector:
    matchLabels:
      app: graphql-gateway
  template:
    metadata:
      labels:
        app: graphql-gateway
        version: v1
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8000"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: graphql-gateway
      containers:
      - name: graphql-gateway
        image: analytics-platform/graphql-gateway:latest
        ports:
        - containerPort: 8000
          name: http
        env:
        - name: GATEWAY_HOST
          value: "0.0.0.0"
        - name: GATEWAY_PORT
          value: "8000"
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: gateway-secrets
              key: jwt-secret
        - name: REDIS_URL
          value: "redis://redis-cluster.infrastructure.svc.cluster.local:6379/0"
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: gateway-secrets
              key: redis-password
        - name: RATE_LIMIT_PER_MINUTE
          value: "100"
        - name: RATE_LIMIT_PER_HOUR
          value: "1000"
        - name: LOG_LEVEL
          value: "INFO"
        - name: ENVIRONMENT
          value: "production"
        
        # Resource limits
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        
        # Health checks
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 2
        
        # Mount configuration
        volumeMounts:
        - name: config
          mountPath: /app/config
          readOnly: true
        - name: logs
          mountPath: /app/logs
      
      # Volumes
      volumes:
      - name: config
        configMap:
          name: gateway-config
      - name: logs
        emptyDir: {}
      
      # Security context
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 1000

---
# Service for GraphQL Gateway
apiVersion: v1
kind: Service
metadata:
  name: graphql-gateway
  namespace: graphql-federation
  labels:
    app: graphql-gateway
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "8000"
spec:
  type: ClusterIP
  ports:
  - port: 8000
    targetPort: 8000
    protocol: TCP
    name: http
  selector:
    app: graphql-gateway

---
# Horizontal Pod Autoscaler
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: graphql-gateway-hpa
  namespace: graphql-federation
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: graphql-gateway
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60

---
# Pod Disruption Budget
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: graphql-gateway-pdb
  namespace: graphql-federation
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: graphql-gateway

---
# Schema Registry Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: schema-registry
  namespace: graphql-federation
  labels:
    app: schema-registry
spec:
  replicas: 2
  selector:
    matchLabels:
      app: schema-registry
  template:
    metadata:
      labels:
        app: schema-registry
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8080"
    spec:
      containers:
      - name: schema-registry
        image: analytics-platform/schema-registry:latest
        ports:
        - containerPort: 8080
          name: http
        env:
        - name: REGISTRY_HOST
          value: "0.0.0.0"
        - name: REGISTRY_PORT
          value: "8080"
        - name: REDIS_URL
          value: "redis://redis-cluster.infrastructure.svc.cluster.local:6379/1"
        
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "250m"
        
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5

---
# Schema Registry Service
apiVersion: v1
kind: Service
metadata:
  name: schema-registry
  namespace: graphql-federation
  labels:
    app: schema-registry
spec:
  type: ClusterIP
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
    name: http
  selector:
    app: schema-registry

---
# Ingress for external access
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: graphql-gateway-ingress
  namespace: graphql-federation
  annotations:
    kubernetes.io/ingress.class: "nginx"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, POST, OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-headers: "Authorization, Content-Type, Accept"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "10m"
spec:
  tls:
  - hosts:
    - api.analytics-platform.com
    secretName: api-tls-secret
  rules:
  - host: api.analytics-platform.com
    http:
      paths:
      - path: /graphql
        pathType: Prefix
        backend:
          service:
            name: graphql-gateway
            port:
              number: 8000
      - path: /schema
        pathType: Prefix
        backend:
          service:
            name: schema-registry
            port:
              number: 8080
      - path: /health
        pathType: Prefix
        backend:
          service:
            name: graphql-gateway
            port:
              number: 8000

---
# Network Policy for security
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: graphql-gateway-netpol
  namespace: graphql-federation
spec:
  podSelector:
    matchLabels:
      app: graphql-gateway
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    ports:
    - protocol: TCP
      port: 8000
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: services
    ports:
    - protocol: TCP
      port: 8001
    - protocol: TCP
      port: 8002
    - protocol: TCP
      port: 8003
    - protocol: TCP
      port: 8004
    - protocol: TCP
      port: 8005
    - protocol: TCP
      port: 8006
  - to:
    - namespaceSelector:
        matchLabels:
          name: infrastructure
    ports:
    - protocol: TCP
      port: 6379  # Redis