#!/usr/bin/env python3
"""
GraphQL Federation API Gateway for E-commerce Analytics Platform
Advanced GraphQL federation with distributed schema stitching and real-time subscriptions
"""

import asyncio
import logging
import json
import uuid
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any, Union, Callable
from dataclasses import dataclass, field
from enum import Enum
import os

# FastAPI and GraphQL
from fastapi import FastAPI, WebSocket, HTTPException, Depends, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials
import strawberry
from strawberry.fastapi import Graph<PERSON>Router
from strawberry.federation import FederatedSchema
from strawberry.subscriptions import GRAPHQL_WS_PROTOCOL
from strawberry.types import Info

# HTTP client for service communication
import httpx
import aioredis

# Authentication and security
import jwt
from passlib.context import CryptContext

# Rate limiting
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded

# Configuration
@dataclass
class GraphQLFederationConfig:
    # API Gateway settings
    gateway_host: str = "0.0.0.0"
    gateway_port: int = 8000
    gateway_name: str = "analytics-gateway"
    
    # Security settings
    jwt_secret: str = os.getenv('JWT_SECRET', 'your-secret-key-change-in-production')
    jwt_algorithm: str = "HS256"
    jwt_expire_minutes: int = 60
    
    # Rate limiting
    rate_limit_per_minute: int = 100
    rate_limit_per_hour: int = 1000
    
    # Service discovery
    services: Dict[str, Dict[str, Any]] = field(default_factory=lambda: {
        'user-service': {
            'url': 'http://user-service:8001',
            'health_check': '/health',
            'schema_endpoint': '/graphql/schema'
        },
        'analytics-service': {
            'url': 'http://analytics-service:8002',
            'health_check': '/health',
            'schema_endpoint': '/graphql/schema'
        },
        'ml-service': {
            'url': 'http://ml-service:8003',
            'health_check': '/health',
            'schema_endpoint': '/graphql/schema'
        },
        'recommendation-service': {
            'url': 'http://recommendation-service:8004',
            'health_check': '/health',
            'schema_endpoint': '/graphql/schema'
        },
        'fraud-detection-service': {
            'url': 'http://fraud-service:8005',
            'health_check': '/health',
            'schema_endpoint': '/graphql/schema'
        },
        'notification-service': {
            'url': 'http://notification-service:8006',
            'health_check': '/health',
            'schema_endpoint': '/graphql/schema'
        }
    })
    
    # Redis for caching and subscriptions
    redis_url: str = os.getenv('REDIS_URL', 'redis://redis-cluster:6379/0')
    
    # Performance settings
    max_query_depth: int = 15
    max_query_complexity: int = 1000
    cache_ttl_seconds: int = 300  # 5 minutes
    
    # Monitoring
    enable_query_logging: bool = True
    enable_performance_monitoring: bool = True
    slow_query_threshold_ms: int = 1000

# Service status tracking
class ServiceStatus(Enum):
    HEALTHY = "healthy"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"

@dataclass
class ServiceHealth:
    name: str
    status: ServiceStatus
    url: str
    last_check: datetime
    response_time_ms: Optional[float] = None
    error: Optional[str] = None

# Authentication and authorization
class AuthenticationError(Exception):
    pass

class AuthorizationError(Exception):
    pass

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ServiceRegistry:
    """Manages service discovery and health checking"""
    
    def __init__(self, config: GraphQLFederationConfig):
        self.config = config
        self.services_health: Dict[str, ServiceHealth] = {}
        self.http_client = httpx.AsyncClient(timeout=10.0)
    
    async def initialize(self):
        """Initialize service registry"""
        try:
            # Initial health check for all services
            await self.check_all_services_health()
            logger.info("Service registry initialized")
        except Exception as e:
            logger.error(f"Error initializing service registry: {str(e)}")
    
    async def check_service_health(self, service_name: str) -> ServiceHealth:
        """Check health of a specific service"""
        try:
            if service_name not in self.config.services:
                return ServiceHealth(
                    name=service_name,
                    status=ServiceStatus.UNKNOWN,
                    url="",
                    last_check=datetime.now(timezone.utc),
                    error="Service not configured"
                )
            
            service_config = self.config.services[service_name]
            health_url = f"{service_config['url']}{service_config['health_check']}"
            
            start_time = datetime.now()
            response = await self.http_client.get(health_url)
            response_time = (datetime.now() - start_time).total_seconds() * 1000
            
            if response.status_code == 200:
                health = ServiceHealth(
                    name=service_name,
                    status=ServiceStatus.HEALTHY,
                    url=service_config['url'],
                    last_check=datetime.now(timezone.utc),
                    response_time_ms=response_time
                )
            else:
                health = ServiceHealth(
                    name=service_name,
                    status=ServiceStatus.UNHEALTHY,
                    url=service_config['url'],
                    last_check=datetime.now(timezone.utc),
                    response_time_ms=response_time,
                    error=f"HTTP {response.status_code}"
                )
                
        except Exception as e:
            health = ServiceHealth(
                name=service_name,
                status=ServiceStatus.UNHEALTHY,
                url=service_config.get('url', ''),
                last_check=datetime.now(timezone.utc),
                error=str(e)
            )
        
        self.services_health[service_name] = health
        return health
    
    async def check_all_services_health(self):
        """Check health of all registered services"""
        tasks = []
        for service_name in self.config.services.keys():
            task = asyncio.create_task(self.check_service_health(service_name))
            tasks.append(task)
        
        await asyncio.gather(*tasks, return_exceptions=True)
    
    async def get_healthy_services(self) -> List[str]:
        """Get list of healthy services"""
        await self.check_all_services_health()
        return [
            name for name, health in self.services_health.items()
            if health.status == ServiceStatus.HEALTHY
        ]
    
    async def proxy_request(self, service_name: str, path: str, method: str = "POST", 
                          data: Optional[Dict] = None, headers: Optional[Dict] = None) -> Dict:
        """Proxy request to a service"""
        try:
            if service_name not in self.config.services:
                raise HTTPException(status_code=404, detail=f"Service {service_name} not found")
            
            service_health = self.services_health.get(service_name)
            if service_health and service_health.status != ServiceStatus.HEALTHY:
                raise HTTPException(status_code=503, detail=f"Service {service_name} is unhealthy")
            
            service_config = self.config.services[service_name]
            url = f"{service_config['url']}{path}"
            
            if method.upper() == "POST":
                response = await self.http_client.post(url, json=data, headers=headers)
            elif method.upper() == "GET":
                response = await self.http_client.get(url, headers=headers)
            else:
                raise HTTPException(status_code=405, detail=f"Method {method} not supported")
            
            if response.status_code == 200:
                return response.json()
            else:
                raise HTTPException(status_code=response.status_code, detail=response.text)
                
        except httpx.TimeoutException:
            raise HTTPException(status_code=504, detail=f"Service {service_name} timeout")
        except Exception as e:
            logger.error(f"Error proxying request to {service_name}: {str(e)}")
            raise HTTPException(status_code=500, detail=str(e))
    
    async def close(self):
        """Close HTTP client"""
        await self.http_client.aclose()

class AuthenticationManager:
    """Handles JWT authentication and authorization"""
    
    def __init__(self, config: GraphQLFederationConfig):
        self.config = config
        self.pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
    
    def create_access_token(self, user_id: str, email: str, roles: List[str] = None) -> str:
        """Create JWT access token"""
        if roles is None:
            roles = ["user"]
        
        payload = {
            "user_id": user_id,
            "email": email,
            "roles": roles,
            "exp": datetime.utcnow().timestamp() + (self.config.jwt_expire_minutes * 60),
            "iat": datetime.utcnow().timestamp(),
            "iss": self.config.gateway_name
        }
        
        return jwt.encode(payload, self.config.jwt_secret, algorithm=self.config.jwt_algorithm)
    
    def verify_token(self, token: str) -> Dict[str, Any]:
        """Verify and decode JWT token"""
        try:
            payload = jwt.decode(token, self.config.jwt_secret, algorithms=[self.config.jwt_algorithm])
            return payload
        except jwt.ExpiredSignatureError:
            raise AuthenticationError("Token has expired")
        except jwt.InvalidTokenError:
            raise AuthenticationError("Invalid token")
    
    def check_permission(self, user_payload: Dict[str, Any], required_roles: List[str]) -> bool:
        """Check if user has required permissions"""
        user_roles = user_payload.get("roles", [])
        return any(role in user_roles for role in required_roles)

# GraphQL Schema Federation
@strawberry.type
class User:
    id: str
    email: str
    name: str
    created_at: datetime

@strawberry.type
class AnalyticsData:
    metric_name: str
    value: float
    timestamp: datetime
    dimensions: Dict[str, str]

@strawberry.type
class MLPrediction:
    prediction_id: str
    model_name: str
    prediction: str
    confidence: float
    input_data: Dict[str, Any]

@strawberry.type
class Recommendation:
    item_id: str
    score: float
    reason: str
    metadata: Dict[str, Any]

@strawberry.type
class FraudAlert:
    alert_id: str
    severity: str
    description: str
    detected_at: datetime
    user_id: Optional[str]

@strawberry.type
class ServiceHealthInfo:
    name: str
    status: str
    url: str
    response_time_ms: Optional[float]
    last_check: datetime
    error: Optional[str]

# GraphQL Queries
@strawberry.type
class Query:
    """Federated GraphQL queries"""
    
    @strawberry.field
    async def user(self, info: Info, user_id: str) -> Optional[User]:
        """Get user by ID"""
        # This would be federated to user-service
        service_registry = info.context["service_registry"]
        response = await service_registry.proxy_request(
            "user-service", 
            "/graphql",
            data={"query": f"query {{ user(id: \"{user_id}\") {{ id email name createdAt }} }}"}
        )
        user_data = response.get("data", {}).get("user")
        if user_data:
            return User(
                id=user_data["id"],
                email=user_data["email"],
                name=user_data["name"],
                created_at=datetime.fromisoformat(user_data["createdAt"])
            )
        return None
    
    @strawberry.field
    async def analytics_metrics(self, info: Info, metric_name: str, 
                               start_date: datetime, end_date: datetime) -> List[AnalyticsData]:
        """Get analytics metrics"""
        service_registry = info.context["service_registry"]
        response = await service_registry.proxy_request(
            "analytics-service",
            "/graphql", 
            data={
                "query": f"""
                query {{
                    metrics(name: "{metric_name}", startDate: "{start_date.isoformat()}", 
                           endDate: "{end_date.isoformat()}") {{
                        metricName value timestamp dimensions
                    }}
                }}
                """
            }
        )
        
        metrics_data = response.get("data", {}).get("metrics", [])
        return [
            AnalyticsData(
                metric_name=metric["metricName"],
                value=metric["value"],
                timestamp=datetime.fromisoformat(metric["timestamp"]),
                dimensions=metric["dimensions"]
            )
            for metric in metrics_data
        ]
    
    @strawberry.field
    async def ml_predictions(self, info: Info, model_name: str, limit: int = 10) -> List[MLPrediction]:
        """Get ML predictions"""
        service_registry = info.context["service_registry"]
        response = await service_registry.proxy_request(
            "ml-service",
            "/graphql",
            data={
                "query": f"""
                query {{
                    predictions(modelName: "{model_name}", limit: {limit}) {{
                        predictionId modelName prediction confidence inputData
                    }}
                }}
                """
            }
        )
        
        predictions_data = response.get("data", {}).get("predictions", [])
        return [
            MLPrediction(
                prediction_id=pred["predictionId"],
                model_name=pred["modelName"],
                prediction=pred["prediction"],
                confidence=pred["confidence"],
                input_data=pred["inputData"]
            )
            for pred in predictions_data
        ]
    
    @strawberry.field
    async def recommendations(self, info: Info, user_id: str, limit: int = 10) -> List[Recommendation]:
        """Get recommendations for user"""
        service_registry = info.context["service_registry"]
        response = await service_registry.proxy_request(
            "recommendation-service",
            "/graphql",
            data={
                "query": f"""
                query {{
                    recommendations(userId: "{user_id}", limit: {limit}) {{
                        itemId score reason metadata
                    }}
                }}
                """
            }
        )
        
        recs_data = response.get("data", {}).get("recommendations", [])
        return [
            Recommendation(
                item_id=rec["itemId"],
                score=rec["score"],
                reason=rec["reason"],
                metadata=rec["metadata"]
            )
            for rec in recs_data
        ]
    
    @strawberry.field
    async def fraud_alerts(self, info: Info, limit: int = 10) -> List[FraudAlert]:
        """Get recent fraud alerts"""
        service_registry = info.context["service_registry"]
        response = await service_registry.proxy_request(
            "fraud-detection-service",
            "/graphql",
            data={
                "query": f"""
                query {{
                    alerts(limit: {limit}) {{
                        alertId severity description detectedAt userId
                    }}
                }}
                """
            }
        )
        
        alerts_data = response.get("data", {}).get("alerts", [])
        return [
            FraudAlert(
                alert_id=alert["alertId"],
                severity=alert["severity"],
                description=alert["description"],
                detected_at=datetime.fromisoformat(alert["detectedAt"]),
                user_id=alert.get("userId")
            )
            for alert in alerts_data
        ]
    
    @strawberry.field
    async def service_health(self, info: Info) -> List[ServiceHealthInfo]:
        """Get health status of all services"""
        service_registry = info.context["service_registry"]
        await service_registry.check_all_services_health()
        
        return [
            ServiceHealthInfo(
                name=health.name,
                status=health.status.value,
                url=health.url,
                response_time_ms=health.response_time_ms,
                last_check=health.last_check,
                error=health.error
            )
            for health in service_registry.services_health.values()
        ]

# GraphQL Mutations
@strawberry.type
class Mutation:
    """Federated GraphQL mutations"""
    
    @strawberry.field
    async def create_user(self, info: Info, email: str, name: str, password: str) -> User:
        """Create new user"""
        service_registry = info.context["service_registry"]
        response = await service_registry.proxy_request(
            "user-service",
            "/graphql",
            data={
                "query": f"""
                mutation {{
                    createUser(email: "{email}", name: "{name}", password: "{password}") {{
                        id email name createdAt
                    }}
                }}
                """
            }
        )
        
        user_data = response.get("data", {}).get("createUser")
        if user_data:
            return User(
                id=user_data["id"],
                email=user_data["email"],
                name=user_data["name"],
                created_at=datetime.fromisoformat(user_data["createdAt"])
            )
        raise HTTPException(status_code=400, detail="Failed to create user")
    
    @strawberry.field
    async def trigger_ml_prediction(self, info: Info, model_name: str, 
                                   input_data: Dict[str, Any]) -> MLPrediction:
        """Trigger ML prediction"""
        service_registry = info.context["service_registry"]
        response = await service_registry.proxy_request(
            "ml-service",
            "/graphql",
            data={
                "query": f"""
                mutation {{
                    predict(modelName: "{model_name}", inputData: {json.dumps(input_data)}) {{
                        predictionId modelName prediction confidence inputData
                    }}
                }}
                """
            }
        )
        
        pred_data = response.get("data", {}).get("predict")
        if pred_data:
            return MLPrediction(
                prediction_id=pred_data["predictionId"],
                model_name=pred_data["modelName"],
                prediction=pred_data["prediction"],
                confidence=pred_data["confidence"],
                input_data=pred_data["inputData"]
            )
        raise HTTPException(status_code=400, detail="Failed to create prediction")

# GraphQL Subscriptions
@strawberry.type
class Subscription:
    """Real-time GraphQL subscriptions"""
    
    @strawberry.subscription
    async def fraud_alerts_feed(self, info: Info) -> FraudAlert:
        """Subscribe to real-time fraud alerts"""
        # This would connect to Redis pub/sub or Kafka
        redis = info.context["redis"]
        pubsub = redis.pubsub()
        await pubsub.subscribe("fraud_alerts")
        
        async for message in pubsub.listen():
            if message["type"] == "message":
                alert_data = json.loads(message["data"])
                yield FraudAlert(
                    alert_id=alert_data["alertId"],
                    severity=alert_data["severity"],
                    description=alert_data["description"],
                    detected_at=datetime.fromisoformat(alert_data["detectedAt"]),
                    user_id=alert_data.get("userId")
                )
    
    @strawberry.subscription
    async def analytics_stream(self, info: Info, metric_name: str) -> AnalyticsData:
        """Subscribe to real-time analytics updates"""
        redis = info.context["redis"]
        pubsub = redis.pubsub()
        await pubsub.subscribe(f"analytics_{metric_name}")
        
        async for message in pubsub.listen():
            if message["type"] == "message":
                metric_data = json.loads(message["data"])
                yield AnalyticsData(
                    metric_name=metric_data["metricName"],
                    value=metric_data["value"],
                    timestamp=datetime.fromisoformat(metric_data["timestamp"]),
                    dimensions=metric_data["dimensions"]
                )

class GraphQLFederationGateway:
    """Main GraphQL federation gateway"""
    
    def __init__(self, config: GraphQLFederationConfig):
        self.config = config
        self.app = FastAPI(title="E-commerce Analytics GraphQL Gateway")
        self.service_registry = ServiceRegistry(config)
        self.auth_manager = AuthenticationManager(config)
        self.redis = None
        self.limiter = Limiter(key_func=get_remote_address)
        
        # Add CORS middleware
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        # Add rate limiting
        self.app.state.limiter = self.limiter
        self.app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)
        
        # Create federated schema
        self.schema = strawberry.Schema(
            query=Query,
            mutation=Mutation,
            subscription=Subscription
        )
        
        self._setup_routes()
    
    async def initialize(self):
        """Initialize the gateway"""
        try:
            # Initialize Redis
            self.redis = aioredis.from_url(self.config.redis_url)
            
            # Initialize service registry
            await self.service_registry.initialize()
            
            logger.info("GraphQL federation gateway initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing gateway: {str(e)}")
            raise
    
    def _setup_routes(self):
        """Setup API routes"""
        
        # GraphQL endpoint with rate limiting
        @self.app.post("/graphql")
        @self.limiter.limit(f"{self.config.rate_limit_per_minute}/minute")
        async def graphql_endpoint(request: Request, 
                                 credentials: HTTPAuthorizationCredentials = Depends(HTTPBearer())):
            try:
                # Verify authentication
                user_payload = self.auth_manager.verify_token(credentials.credentials)
                
                # Create context
                context = {
                    "request": request,
                    "user": user_payload,
                    "service_registry": self.service_registry,
                    "redis": self.redis
                }
                
                # Execute GraphQL query
                return await self.schema.execute(
                    await request.json(),
                    context_value=context
                )
                
            except AuthenticationError as e:
                raise HTTPException(status_code=401, detail=str(e))
            except Exception as e:
                logger.error(f"GraphQL execution error: {str(e)}")
                raise HTTPException(status_code=500, detail="Internal server error")
        
        # Health check endpoint
        @self.app.get("/health")
        async def health_check():
            healthy_services = await self.service_registry.get_healthy_services()
            return {
                "status": "healthy",
                "gateway_name": self.config.gateway_name,
                "healthy_services": healthy_services,
                "total_services": len(self.config.services),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        
        # Service discovery endpoint
        @self.app.get("/services")
        async def list_services():
            await self.service_registry.check_all_services_health()
            return {
                "services": [
                    {
                        "name": health.name,
                        "status": health.status.value,
                        "url": health.url,
                        "response_time_ms": health.response_time_ms,
                        "last_check": health.last_check.isoformat(),
                        "error": health.error
                    }
                    for health in self.service_registry.services_health.values()
                ]
            }
        
        # WebSocket endpoint for subscriptions
        @self.app.websocket("/graphql/ws")
        async def websocket_endpoint(websocket):
            await self.schema.subscription(websocket, protocol=GRAPHQL_WS_PROTOCOL)
    
    async def close(self):
        """Cleanup resources"""
        try:
            await self.service_registry.close()
            if self.redis:
                await self.redis.close()
            logger.info("GraphQL federation gateway closed")
        except Exception as e:
            logger.error(f"Error closing gateway: {str(e)}")

async def main():
    """Main function to run the GraphQL federation gateway"""
    config = GraphQLFederationConfig()
    gateway = GraphQLFederationGateway(config)
    
    try:
        await gateway.initialize()
        
        # Start the server
        import uvicorn
        await uvicorn.run(
            gateway.app,
            host=config.gateway_host,
            port=config.gateway_port,
            log_level="info"
        )
        
    except Exception as e:
        logger.error(f"Error running gateway: {str(e)}")
        raise
    finally:
        await gateway.close()

if __name__ == "__main__":
    asyncio.run(main())