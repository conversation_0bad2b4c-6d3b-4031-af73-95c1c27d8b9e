#!/usr/bin/env python3
"""
Intelligent Alerting and Notification System for E-commerce Analytics
Advanced alerting with ML-driven severity classification, smart routing, and adaptive thresholds
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import os
import sys
import json
import asyncio
import aiohttp
from typing import Dict, List, Tuple, Optional, Any, Union
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
from enum import Enum
import threading
import time
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders

# ML for intelligent alerting
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, accuracy_score
import xgboost as xgb

# Real-time processing
from kafka import KafkaConsumer, KafkaProducer
import redis
import websocket

# Database and storage
import asyncpg
from sqlalchemy import create_engine
import boto3

# Communication channels
import slack_sdk
from twilio.rest import Client as TwilioClient
import discord
from pushbullet import Pushbullet

# Templates and formatting
from jinja2 import Template, Environment, FileSystemLoader
import markdown
import plotly.graph_objects as go
import plotly.io as pio

# MLflow integration
import mlflow
import mlflow.sklearn
from mlflow.tracking import MlflowClient

# Configuration
class AlertSeverity(Enum):
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    INFO = "info"

class AlertChannel(Enum):
    EMAIL = "email"
    SLACK = "slack"
    SMS = "sms"
    DISCORD = "discord"
    PUSHBULLET = "pushbullet"
    WEBHOOK = "webhook"
    PHONE_CALL = "phone_call"

@dataclass
class AlertingConfig:
    # ML model parameters
    severity_model_retrain_hours: int = 24
    alert_fatigue_threshold: int = 50  # Max alerts per hour per user
    adaptive_threshold_enabled: bool = True
    context_awareness_enabled: bool = True
    
    # Channel configurations
    email_smtp_server: str = os.getenv('SMTP_SERVER', 'smtp.gmail.com')
    email_smtp_port: int = int(os.getenv('SMTP_PORT', '587'))
    email_username: str = os.getenv('EMAIL_USERNAME', '')
    email_password: str = os.getenv('EMAIL_PASSWORD', '')
    
    slack_bot_token: str = os.getenv('SLACK_BOT_TOKEN', '')
    slack_workspace_url: str = os.getenv('SLACK_WORKSPACE_URL', '')
    
    twilio_account_sid: str = os.getenv('TWILIO_ACCOUNT_SID', '')
    twilio_auth_token: str = os.getenv('TWILIO_AUTH_TOKEN', '')
    twilio_phone_number: str = os.getenv('TWILIO_PHONE_NUMBER', '')
    
    discord_webhook_url: str = os.getenv('DISCORD_WEBHOOK_URL', '')
    pushbullet_api_key: str = os.getenv('PUSHBULLET_API_KEY', '')
    
    # Escalation rules
    escalation_enabled: bool = True
    escalation_timeout_minutes: int = 15
    max_escalation_levels: int = 3
    
    # Template paths
    template_directory: str = '/templates/alerts'
    
    # Kafka configuration
    kafka_bootstrap_servers: str = os.getenv('KAFKA_BOOTSTRAP_SERVERS', 'kafka-cluster:9092')
    kafka_consumer_group: str = 'intelligent-alerting'
    kafka_input_topics: List[str] = None
    
    # Redis configuration
    redis_host: str = os.getenv('REDIS_HOST', 'redis-cluster')
    redis_port: int = int(os.getenv('REDIS_PORT', '6379'))
    redis_db: int = int(os.getenv('REDIS_DB', '3'))
    
    # Database configuration
    db_host: str = os.getenv('DB_HOST', 'analytics-db.cluster-xyz.us-east-1.rds.amazonaws.com')
    db_name: str = os.getenv('DB_NAME', 'ecommerce_analytics')
    db_user: str = os.getenv('DB_USER', 'analytics_user')
    db_password: str = os.getenv('DB_PASSWORD', 'analytics_password')
    
    # MLflow configuration
    mlflow_tracking_uri: str = os.getenv('MLFLOW_TRACKING_URI', 'http://mlflow-server.mlflow:5000')
    experiment_name: str = 'intelligent-alerting'
    
    def __post_init__(self):
        if self.kafka_input_topics is None:
            self.kafka_input_topics = ['fraud-alerts', 'pattern-alerts', 'system-alerts', 'business-alerts']

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class AlertSeverityClassifier:
    """ML-based alert severity classification"""
    
    def __init__(self, config: AlertingConfig):
        self.config = config
        self.model = None
        self.scaler = StandardScaler()
        self.label_encoder = LabelEncoder()
        self.feature_columns = []
        self.is_trained = False
        
        # MLflow setup
        mlflow.set_tracking_uri(self.config.mlflow_tracking_uri)
        mlflow.set_experiment(self.config.experiment_name)
    
    def extract_features(self, alert: Dict) -> Dict[str, float]:
        """Extract features for severity classification"""
        try:
            features = {}
            
            # Alert type features
            alert_type = alert.get('alert_type', 'unknown')
            features['is_fraud_alert'] = 1.0 if alert_type == 'fraud_detection' else 0.0
            features['is_pattern_alert'] = 1.0 if alert_type == 'pattern_match' else 0.0
            features['is_system_alert'] = 1.0 if alert_type == 'system_error' else 0.0
            features['is_business_alert'] = 1.0 if alert_type == 'business_rule' else 0.0
            
            # Confidence/score features
            features['confidence_score'] = alert.get('confidence', 0.0)
            features['fraud_score'] = alert.get('fraud_score', 0.0)
            features['risk_score'] = alert.get('risk_score', 0.0)
            
            # Temporal features
            alert_time = datetime.fromisoformat(alert.get('timestamp', datetime.now().isoformat()))
            features['hour_of_day'] = alert_time.hour / 24.0
            features['day_of_week'] = alert_time.weekday() / 7.0
            features['is_weekend'] = 1.0 if alert_time.weekday() >= 5 else 0.0
            features['is_business_hours'] = 1.0 if 9 <= alert_time.hour <= 17 else 0.0
            
            # Impact features
            features['affected_users'] = alert.get('affected_users', 0)
            features['financial_impact'] = alert.get('financial_impact', 0.0)
            features['transaction_count'] = alert.get('transaction_count', 0)
            
            # Historical context features
            features['similar_alerts_last_hour'] = self._count_similar_alerts(alert, hours=1)
            features['similar_alerts_last_day'] = self._count_similar_alerts(alert, hours=24)
            features['alert_frequency_score'] = self._calculate_alert_frequency_score(alert)
            
            # Customer/merchant specific features
            if alert.get('customer_id'):
                features['customer_risk_history'] = self._get_customer_risk_history(alert['customer_id'])
            else:
                features['customer_risk_history'] = 0.0
            
            if alert.get('merchant_id'):
                features['merchant_fraud_history'] = self._get_merchant_fraud_history(alert['merchant_id'])
            else:
                features['merchant_fraud_history'] = 0.0
            
            # System health features
            features['system_load'] = self._get_system_load()
            features['error_rate'] = self._get_current_error_rate()
            
            return features
            
        except Exception as e:
            logger.error(f"Error extracting features: {str(e)}")
            return {}
    
    def _count_similar_alerts(self, alert: Dict, hours: int) -> float:
        """Count similar alerts in the specified time window"""
        try:
            # This would query the database for similar alerts
            # For now, return a random value for demonstration
            return np.random.poisson(3)
        except:
            return 0.0
    
    def _calculate_alert_frequency_score(self, alert: Dict) -> float:
        """Calculate alert frequency score based on historical patterns"""
        try:
            alert_type = alert.get('alert_type', 'unknown')
            # This would calculate based on historical frequency
            # Higher score means more frequent alert type
            frequency_map = {
                'fraud_detection': 0.3,
                'pattern_match': 0.5,
                'system_error': 0.7,
                'business_rule': 0.2
            }
            return frequency_map.get(alert_type, 0.5)
        except:
            return 0.5
    
    def _get_customer_risk_history(self, customer_id: str) -> float:
        """Get customer's historical risk score"""
        try:
            # This would query customer's fraud history from database
            return np.random.uniform(0, 1)
        except:
            return 0.0
    
    def _get_merchant_fraud_history(self, merchant_id: str) -> float:
        """Get merchant's historical fraud rate"""
        try:
            # This would query merchant's fraud history from database
            return np.random.uniform(0, 0.1)
        except:
            return 0.0
    
    def _get_system_load(self) -> float:
        """Get current system load metric"""
        try:
            # This would get actual system metrics
            return np.random.uniform(0, 1)
        except:
            return 0.5
    
    def _get_current_error_rate(self) -> float:
        """Get current system error rate"""
        try:
            # This would get actual error rate metrics
            return np.random.uniform(0, 0.1)
        except:
            return 0.01
    
    def train_model(self, training_data: pd.DataFrame):
        """Train the severity classification model"""
        try:
            with mlflow.start_run(run_name="severity_classifier_training"):
                # Extract features for training data
                feature_data = []
                labels = []
                
                for _, alert_row in training_data.iterrows():
                    alert_dict = alert_row.to_dict()
                    features = self.extract_features(alert_dict)
                    
                    if features:
                        feature_data.append(features)
                        labels.append(alert_row.get('severity', 'medium'))
                
                if len(feature_data) < 10:
                    logger.warning("Insufficient training data for severity classifier")
                    return
                
                # Convert to DataFrame
                features_df = pd.DataFrame(feature_data)
                self.feature_columns = features_df.columns.tolist()
                
                # Prepare data
                X = features_df.fillna(0)
                y = self.label_encoder.fit_transform(labels)
                
                # Scale features
                X_scaled = self.scaler.fit_transform(X)
                
                # Split data
                X_train, X_test, y_train, y_test = train_test_split(
                    X_scaled, y, test_size=0.2, random_state=42, stratify=y
                )
                
                # Train XGBoost model
                self.model = xgb.XGBClassifier(
                    n_estimators=100,
                    max_depth=6,
                    learning_rate=0.1,
                    random_state=42
                )
                
                self.model.fit(X_train, y_train)
                
                # Evaluate model
                y_pred = self.model.predict(X_test)
                accuracy = accuracy_score(y_test, y_pred)
                
                # Log metrics
                mlflow.log_param("n_features", len(self.feature_columns))
                mlflow.log_param("n_training_samples", len(X_train))
                mlflow.log_metric("accuracy", accuracy)
                
                # Log model
                mlflow.sklearn.log_model(self.model, "severity_classifier")
                
                self.is_trained = True
                logger.info(f"Severity classifier trained successfully. Accuracy: {accuracy:.3f}")
                
        except Exception as e:
            logger.error(f"Error training severity classifier: {str(e)}")
    
    def predict_severity(self, alert: Dict) -> Tuple[str, float]:
        """Predict alert severity"""
        try:
            if not self.is_trained:
                # Return default severity if model not trained
                return "medium", 0.5
            
            # Extract features
            features = self.extract_features(alert)
            
            if not features:
                return "medium", 0.5
            
            # Convert to DataFrame with correct column order
            features_df = pd.DataFrame([features])[self.feature_columns].fillna(0)
            
            # Scale features
            features_scaled = self.scaler.transform(features_df)
            
            # Predict
            severity_encoded = self.model.predict(features_scaled)[0]
            severity_proba = self.model.predict_proba(features_scaled)[0]
            
            # Convert back to severity label
            severity = self.label_encoder.inverse_transform([severity_encoded])[0]
            confidence = np.max(severity_proba)
            
            return severity, confidence
            
        except Exception as e:
            logger.error(f"Error predicting severity: {str(e)}")
            return "medium", 0.5

class AlertRouter:
    """Intelligent alert routing based on severity, context, and user preferences"""
    
    def __init__(self, config: AlertingConfig):
        self.config = config
        self.user_preferences = {}
        self.escalation_rules = {}
        self.alert_history = defaultdict(list)
        
    def load_user_preferences(self):
        """Load user notification preferences from database"""
        try:
            # This would load from database
            # For demo, using default preferences
            self.user_preferences = {
                'admin': {
                    'channels': {
                        AlertSeverity.CRITICAL: [AlertChannel.EMAIL, AlertChannel.SMS, AlertChannel.SLACK],
                        AlertSeverity.HIGH: [AlertChannel.EMAIL, AlertChannel.SLACK],
                        AlertSeverity.MEDIUM: [AlertChannel.SLACK],
                        AlertSeverity.LOW: [AlertChannel.EMAIL],
                        AlertSeverity.INFO: []
                    },
                    'email': '<EMAIL>',
                    'phone': '+1234567890',
                    'slack_user_id': 'U123456789',
                    'quiet_hours': {'start': 22, 'end': 7}
                },
                'security_team': {
                    'channels': {
                        AlertSeverity.CRITICAL: [AlertChannel.EMAIL, AlertChannel.SMS, AlertChannel.DISCORD],
                        AlertSeverity.HIGH: [AlertChannel.EMAIL, AlertChannel.DISCORD],
                        AlertSeverity.MEDIUM: [AlertChannel.DISCORD],
                        AlertSeverity.LOW: [],
                        AlertSeverity.INFO: []
                    },
                    'email': '<EMAIL>',
                    'phone': '+1234567891',
                    'discord_webhook': self.config.discord_webhook_url
                },
                'ops_team': {
                    'channels': {
                        AlertSeverity.CRITICAL: [AlertChannel.SLACK, AlertChannel.EMAIL],
                        AlertSeverity.HIGH: [AlertChannel.SLACK],
                        AlertSeverity.MEDIUM: [AlertChannel.SLACK],
                        AlertSeverity.LOW: [],
                        AlertSeverity.INFO: []
                    },
                    'email': '<EMAIL>',
                    'slack_channel': '#ops-alerts'
                }
            }
            
            logger.info("User preferences loaded successfully")
            
        except Exception as e:
            logger.error(f"Error loading user preferences: {str(e)}")
    
    def determine_recipients(self, alert: Dict, severity: AlertSeverity) -> List[Dict]:
        """Determine who should receive the alert based on type and severity"""
        try:
            recipients = []
            alert_type = alert.get('alert_type', 'unknown')
            
            # Route based on alert type
            if alert_type in ['fraud_detection', 'pattern_match']:
                # Security team handles fraud alerts
                if 'security_team' in self.user_preferences:
                    recipients.append({
                        'user_id': 'security_team',
                        'preferences': self.user_preferences['security_team']
                    })
            
            if alert_type in ['system_error', 'performance_issue']:
                # Ops team handles system alerts
                if 'ops_team' in self.user_preferences:
                    recipients.append({
                        'user_id': 'ops_team',
                        'preferences': self.user_preferences['ops_team']
                    })
            
            # Admin always gets critical alerts
            if severity == AlertSeverity.CRITICAL and 'admin' in self.user_preferences:
                recipients.append({
                    'user_id': 'admin',
                    'preferences': self.user_preferences['admin']
                })
            
            # Apply alert fatigue protection
            recipients = self._apply_alert_fatigue_protection(recipients, alert)
            
            return recipients
            
        except Exception as e:
            logger.error(f"Error determining recipients: {str(e)}")
            return []
    
    def _apply_alert_fatigue_protection(self, recipients: List[Dict], alert: Dict) -> List[Dict]:
        """Apply alert fatigue protection by limiting alerts per user"""
        try:
            current_time = datetime.now()
            cutoff_time = current_time - timedelta(hours=1)
            
            filtered_recipients = []
            
            for recipient in recipients:
                user_id = recipient['user_id']
                
                # Count recent alerts for this user
                recent_alerts = [
                    a for a in self.alert_history[user_id]
                    if a['timestamp'] > cutoff_time
                ]
                
                if len(recent_alerts) < self.config.alert_fatigue_threshold:
                    filtered_recipients.append(recipient)
                    
                    # Add to alert history
                    self.alert_history[user_id].append({
                        'alert_id': alert.get('alert_id', 'unknown'),
                        'timestamp': current_time,
                        'severity': alert.get('severity', 'medium')
                    })
                else:
                    logger.warning(f"Alert fatigue protection triggered for user {user_id}")
            
            # Clean up old alert history
            for user_id in self.alert_history:
                self.alert_history[user_id] = [
                    a for a in self.alert_history[user_id]
                    if a['timestamp'] > cutoff_time
                ]
            
            return filtered_recipients
            
        except Exception as e:
            logger.error(f"Error applying alert fatigue protection: {str(e)}")
            return recipients

class NotificationChannels:
    """Handles different notification channels"""
    
    def __init__(self, config: AlertingConfig):
        self.config = config
        self.template_env = None
        self._initialize_templates()
        self._initialize_clients()
    
    def _initialize_templates(self):
        """Initialize Jinja2 templates"""
        try:
            if os.path.exists(self.config.template_directory):
                self.template_env = Environment(
                    loader=FileSystemLoader(self.config.template_directory)
                )
            else:
                # Create default templates in memory
                self.template_env = Environment(loader=FileSystemLoader('.'))
                self._create_default_templates()
            
            logger.info("Alert templates initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing templates: {str(e)}")
    
    def _create_default_templates(self):
        """Create default alert templates"""
        try:
            os.makedirs(self.config.template_directory, exist_ok=True)
            
            # Email template
            email_template = """
            <html>
            <head><title>{{ alert_type|title }} Alert</title></head>
            <body>
                <h2 style="color: {% if severity == 'critical' %}red{% elif severity == 'high' %}orange{% else %}blue{% endif %};">
                    {{ severity|upper }} ALERT: {{ alert_type|title }}
                </h2>
                
                <table border="1" style="border-collapse: collapse;">
                    <tr><td><strong>Alert ID:</strong></td><td>{{ alert_id }}</td></tr>
                    <tr><td><strong>Timestamp:</strong></td><td>{{ timestamp }}</td></tr>
                    <tr><td><strong>Severity:</strong></td><td>{{ severity|upper }}</td></tr>
                    {% if description %}
                    <tr><td><strong>Description:</strong></td><td>{{ description }}</td></tr>
                    {% endif %}
                    {% if details %}
                    <tr><td><strong>Details:</strong></td><td><pre>{{ details|tojson(indent=2) }}</pre></td></tr>
                    {% endif %}
                </table>
                
                {% if action_required %}
                <h3>Action Required:</h3>
                <p>{{ action_required }}</p>
                {% endif %}
                
                <p><em>This alert was generated by the Intelligent Alerting System at {{ timestamp }}</em></p>
            </body>
            </html>
            """
            
            with open(f"{self.config.template_directory}/email_alert.html", 'w') as f:
                f.write(email_template)
            
            # Slack template
            slack_template = """
            {
                "blocks": [
                    {
                        "type": "header",
                        "text": {
                            "type": "plain_text",
                            "text": "🚨 {{ severity|upper }} ALERT: {{ alert_type|title }}"
                        }
                    },
                    {
                        "type": "section",
                        "fields": [
                            {
                                "type": "mrkdwn",
                                "text": "*Alert ID:*\n{{ alert_id }}"
                            },
                            {
                                "type": "mrkdwn",
                                "text": "*Timestamp:*\n{{ timestamp }}"
                            },
                            {
                                "type": "mrkdwn",
                                "text": "*Severity:*\n{{ severity|upper }}"
                            }
                        ]
                    }
                    {% if description %},
                    {
                        "type": "section",
                        "text": {
                            "type": "mrkdwn",
                            "text": "*Description:*\n{{ description }}"
                        }
                    }
                    {% endif %}
                ]
            }
            """
            
            with open(f"{self.config.template_directory}/slack_alert.json", 'w') as f:
                f.write(slack_template)
            
        except Exception as e:
            logger.error(f"Error creating default templates: {str(e)}")
    
    def _initialize_clients(self):
        """Initialize notification service clients"""
        try:
            # Slack client
            if self.config.slack_bot_token:
                self.slack_client = slack_sdk.WebClient(token=self.config.slack_bot_token)
            
            # Twilio client
            if self.config.twilio_account_sid and self.config.twilio_auth_token:
                self.twilio_client = TwilioClient(
                    self.config.twilio_account_sid,
                    self.config.twilio_auth_token
                )
            
            # Pushbullet client
            if self.config.pushbullet_api_key:
                self.pushbullet_client = Pushbullet(self.config.pushbullet_api_key)
            
            logger.info("Notification clients initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing notification clients: {str(e)}")
    
    async def send_email(self, alert: Dict, recipient_email: str) -> bool:
        """Send email notification"""
        try:
            if not self.config.email_username or not self.config.email_password:
                logger.warning("Email credentials not configured")
                return False
            
            # Render template
            template = self.template_env.get_template('email_alert.html')
            html_content = template.render(**alert)
            
            # Create message
            msg = MIMEMultipart('alternative')
            msg['Subject'] = f"{alert.get('severity', 'ALERT').upper()}: {alert.get('alert_type', 'Unknown')}"
            msg['From'] = self.config.email_username
            msg['To'] = recipient_email
            
            # Add HTML content
            html_part = MIMEText(html_content, 'html')
            msg.attach(html_part)
            
            # Send email
            with smtplib.SMTP(self.config.email_smtp_server, self.config.email_smtp_port) as server:
                server.starttls()
                server.login(self.config.email_username, self.config.email_password)
                server.send_message(msg)
            
            logger.info(f"Email alert sent to {recipient_email}")
            return True
            
        except Exception as e:
            logger.error(f"Error sending email: {str(e)}")
            return False
    
    async def send_slack(self, alert: Dict, slack_channel: str) -> bool:
        """Send Slack notification"""
        try:
            if not hasattr(self, 'slack_client'):
                logger.warning("Slack client not configured")
                return False
            
            # Render template
            template = self.template_env.get_template('slack_alert.json')
            slack_content = template.render(**alert)
            blocks = json.loads(slack_content)
            
            # Send message
            response = self.slack_client.chat_postMessage(
                channel=slack_channel,
                blocks=blocks['blocks']
            )
            
            if response['ok']:
                logger.info(f"Slack alert sent to {slack_channel}")
                return True
            else:
                logger.error(f"Slack API error: {response.get('error', 'Unknown error')}")
                return False
            
        except Exception as e:
            logger.error(f"Error sending Slack message: {str(e)}")
            return False
    
    async def send_sms(self, alert: Dict, phone_number: str) -> bool:
        """Send SMS notification"""
        try:
            if not hasattr(self, 'twilio_client'):
                logger.warning("Twilio client not configured")
                return False
            
            # Create SMS content
            sms_content = f"""
🚨 {alert.get('severity', 'ALERT').upper()}: {alert.get('alert_type', 'Unknown')}

Alert ID: {alert.get('alert_id', 'N/A')}
Time: {alert.get('timestamp', 'N/A')}

{alert.get('description', 'No description available')}
            """.strip()
            
            # Send SMS
            message = self.twilio_client.messages.create(
                body=sms_content,
                from_=self.config.twilio_phone_number,
                to=phone_number
            )
            
            logger.info(f"SMS alert sent to {phone_number} (SID: {message.sid})")
            return True
            
        except Exception as e:
            logger.error(f"Error sending SMS: {str(e)}")
            return False
    
    async def send_discord(self, alert: Dict, webhook_url: str) -> bool:
        """Send Discord notification"""
        try:
            # Create Discord embed
            color_map = {
                'critical': 0xFF0000,  # Red
                'high': 0xFF8C00,      # Orange
                'medium': 0xFFFF00,    # Yellow
                'low': 0x0000FF,       # Blue
                'info': 0x808080       # Gray
            }
            
            embed = {
                "embeds": [{
                    "title": f"🚨 {alert.get('severity', 'ALERT').upper()}: {alert.get('alert_type', 'Unknown')}",
                    "color": color_map.get(alert.get('severity', 'medium'), 0x808080),
                    "fields": [
                        {"name": "Alert ID", "value": alert.get('alert_id', 'N/A'), "inline": True},
                        {"name": "Timestamp", "value": alert.get('timestamp', 'N/A'), "inline": True},
                        {"name": "Severity", "value": alert.get('severity', 'Unknown').upper(), "inline": True}
                    ],
                    "timestamp": datetime.now().isoformat()
                }]
            }
            
            if alert.get('description'):
                embed['embeds'][0]['description'] = alert['description']
            
            # Send to Discord webhook
            async with aiohttp.ClientSession() as session:
                async with session.post(webhook_url, json=embed) as response:
                    if response.status == 204:
                        logger.info("Discord alert sent successfully")
                        return True
                    else:
                        logger.error(f"Discord webhook error: {response.status}")
                        return False
            
        except Exception as e:
            logger.error(f"Error sending Discord message: {str(e)}")
            return False
    
    async def send_pushbullet(self, alert: Dict, device_iden: str = None) -> bool:
        """Send Pushbullet notification"""
        try:
            if not hasattr(self, 'pushbullet_client'):
                logger.warning("Pushbullet client not configured")
                return False
            
            title = f"{alert.get('severity', 'ALERT').upper()}: {alert.get('alert_type', 'Unknown')}"
            body = f"Alert ID: {alert.get('alert_id', 'N/A')}\nTime: {alert.get('timestamp', 'N/A')}\n\n{alert.get('description', 'No description available')}"
            
            # Send push notification
            push = self.pushbullet_client.push_note(title, body, device=device_iden)
            
            logger.info("Pushbullet alert sent successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error sending Pushbullet notification: {str(e)}")
            return False
    
    async def send_webhook(self, alert: Dict, webhook_url: str) -> bool:
        """Send webhook notification"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(webhook_url, json=alert) as response:
                    if 200 <= response.status < 300:
                        logger.info(f"Webhook alert sent to {webhook_url}")
                        return True
                    else:
                        logger.error(f"Webhook error: {response.status}")
                        return False
                        
        except Exception as e:
            logger.error(f"Error sending webhook: {str(e)}")
            return False

class IntelligentAlertingSystem:
    """Main intelligent alerting system"""
    
    def __init__(self, config: AlertingConfig):
        self.config = config
        self.severity_classifier = AlertSeverityClassifier(config)
        self.alert_router = AlertRouter(config)
        self.notification_channels = NotificationChannels(config)
        
        # Initialize connections
        self.kafka_consumer = None
        self.redis_client = None
        self.db_pool = None
        
        # Control flags
        self.running = False
        self.processing_thread = None
        
        # Alert tracking
        self.processed_alerts = set()
        self.escalation_tracker = defaultdict(dict)
        
        # MLflow setup
        mlflow.set_tracking_uri(self.config.mlflow_tracking_uri)
        mlflow.set_experiment(self.config.experiment_name)
    
    async def initialize(self):
        """Initialize the alerting system"""
        try:
            await self._initialize_kafka()
            await self._initialize_redis()
            await self._initialize_database()
            
            # Load configuration
            self.alert_router.load_user_preferences()
            
            # Train severity classifier if training data available
            await self._train_severity_classifier()
            
            logger.info("Intelligent alerting system initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing alerting system: {str(e)}")
            raise
    
    async def _initialize_kafka(self):
        """Initialize Kafka consumer"""
        try:
            from kafka import KafkaConsumer
            
            self.kafka_consumer = KafkaConsumer(
                *self.config.kafka_input_topics,
                bootstrap_servers=self.config.kafka_bootstrap_servers,
                group_id=self.config.kafka_consumer_group,
                auto_offset_reset='latest',
                enable_auto_commit=True,
                value_deserializer=lambda x: json.loads(x.decode('utf-8'))
            )
            
            logger.info("Kafka consumer initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing Kafka: {str(e)}")
            raise
    
    async def _initialize_redis(self):
        """Initialize Redis connection"""
        try:
            self.redis_client = redis.Redis(
                host=self.config.redis_host,
                port=self.config.redis_port,
                db=self.config.redis_db,
                decode_responses=True
            )
            
            await self.redis_client.ping()
            logger.info("Redis initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing Redis: {str(e)}")
            raise
    
    async def _initialize_database(self):
        """Initialize database connection pool"""
        try:
            self.db_pool = await asyncpg.create_pool(
                host=self.config.db_host,
                database=self.config.db_name,
                user=self.config.db_user,
                password=self.config.db_password,
                min_size=5,
                max_size=20
            )
            
            logger.info("Database connection pool initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing database: {str(e)}")
            raise
    
    async def _train_severity_classifier(self):
        """Train the severity classifier with historical data"""
        try:
            if not self.db_pool:
                return
            
            # Get historical alert data
            async with self.db_pool.acquire() as conn:
                query = """
                SELECT alert_type, alert_data, severity, created_at
                FROM processed_alerts 
                WHERE created_at >= NOW() - INTERVAL '30 days'
                AND severity IS NOT NULL
                LIMIT 10000
                """
                
                rows = await conn.fetch(query)
                
                if len(rows) < 100:
                    logger.warning("Insufficient historical data for training severity classifier")
                    return
                
                training_data = []
                for row in rows:
                    alert_data = json.loads(row['alert_data'])
                    alert_data['severity'] = row['severity']
                    training_data.append(alert_data)
                
                training_df = pd.DataFrame(training_data)
                self.severity_classifier.train_model(training_df)
                
        except Exception as e:
            logger.error(f"Error training severity classifier: {str(e)}")
    
    async def start_alerting(self):
        """Start the alerting system"""
        try:
            self.running = True
            
            # Start processing thread
            self.processing_thread = threading.Thread(target=self._process_alerts)
            self.processing_thread.start()
            
            logger.info("Intelligent alerting system started")
            
            # Keep main thread alive
            while self.running:
                await asyncio.sleep(1)
                
        except KeyboardInterrupt:
            logger.info("Shutdown signal received")
            await self.stop_alerting()
        except Exception as e:
            logger.error(f"Error in alerting system: {str(e)}")
            await self.stop_alerting()
    
    def _process_alerts(self):
        """Main alert processing loop"""
        logger.info("Starting alert processing loop")
        
        try:
            for message in self.kafka_consumer:
                if not self.running:
                    break
                
                try:
                    alert = message.value
                    alert_id = alert.get('alert_id', f"alert_{int(time.time())}")
                    
                    # Skip if already processed
                    if alert_id in self.processed_alerts:
                        continue
                    
                    # Process alert
                    asyncio.create_task(self._process_single_alert(alert))
                    
                    # Mark as processed
                    self.processed_alerts.add(alert_id)
                    
                    # Clean up old processed alerts
                    if len(self.processed_alerts) > 10000:
                        # Remove oldest 1000 alerts
                        for _ in range(1000):
                            self.processed_alerts.pop()
                    
                except Exception as e:
                    logger.error(f"Error processing alert message: {str(e)}")
                    continue
                    
        except Exception as e:
            logger.error(f"Error in alert processing loop: {str(e)}")
        finally:
            logger.info("Alert processing loop stopped")
    
    async def _process_single_alert(self, alert: Dict):
        """Process a single alert"""
        try:
            start_time = time.time()
            
            # Add metadata
            alert['alert_id'] = alert.get('alert_id', f"alert_{int(time.time())}")
            alert['received_at'] = datetime.now().isoformat()
            
            # Classify severity
            severity_str, confidence = self.severity_classifier.predict_severity(alert)
            severity = AlertSeverity(severity_str)
            
            alert['severity'] = severity.value
            alert['severity_confidence'] = confidence
            
            # Determine recipients
            recipients = self.alert_router.determine_recipients(alert, severity)
            
            if not recipients:
                logger.warning(f"No recipients found for alert {alert['alert_id']}")
                return
            
            # Send notifications
            notification_results = []
            for recipient in recipients:
                result = await self._send_notifications_to_recipient(alert, recipient, severity)
                notification_results.extend(result)
            
            # Store alert in database
            await self._store_processed_alert(alert, notification_results)
            
            # Setup escalation if needed
            if severity in [AlertSeverity.CRITICAL, AlertSeverity.HIGH] and self.config.escalation_enabled:
                await self._setup_escalation(alert, recipients)
            
            processing_time = time.time() - start_time
            
            # Log metrics
            with mlflow.start_run(run_name=f"alert_processing_{alert['alert_id']}"):
                mlflow.log_metric("processing_time_seconds", processing_time)
                mlflow.log_metric("severity_confidence", confidence)
                mlflow.log_metric("notification_count", len(notification_results))
                mlflow.log_param("alert_type", alert.get('alert_type', 'unknown'))
                mlflow.log_param("severity", severity.value)
            
            logger.info(f"Alert {alert['alert_id']} processed successfully in {processing_time:.3f}s")
            
        except Exception as e:
            logger.error(f"Error processing single alert: {str(e)}")
    
    async def _send_notifications_to_recipient(self, alert: Dict, recipient: Dict, severity: AlertSeverity) -> List[Dict]:
        """Send notifications to a single recipient"""
        try:
            user_id = recipient['user_id']
            preferences = recipient['preferences']
            
            # Get channels for this severity level
            channels = preferences.get('channels', {}).get(severity, [])
            
            if not channels:
                return []
            
            # Check quiet hours
            if 'quiet_hours' in preferences:
                current_hour = datetime.now().hour
                quiet_start = preferences['quiet_hours']['start']
                quiet_end = preferences['quiet_hours']['end']
                
                # Skip non-critical alerts during quiet hours
                if (severity not in [AlertSeverity.CRITICAL] and
                    ((quiet_start <= current_hour) or (current_hour <= quiet_end))):
                    logger.info(f"Skipping alert for {user_id} due to quiet hours")
                    return []
            
            notification_results = []
            
            for channel in channels:
                try:
                    success = False
                    
                    if channel == AlertChannel.EMAIL:
                        email = preferences.get('email')
                        if email:
                            success = await self.notification_channels.send_email(alert, email)
                    
                    elif channel == AlertChannel.SLACK:
                        slack_channel = preferences.get('slack_channel', '#alerts')
                        success = await self.notification_channels.send_slack(alert, slack_channel)
                    
                    elif channel == AlertChannel.SMS:
                        phone = preferences.get('phone')
                        if phone:
                            success = await self.notification_channels.send_sms(alert, phone)
                    
                    elif channel == AlertChannel.DISCORD:
                        discord_webhook = preferences.get('discord_webhook')
                        if discord_webhook:
                            success = await self.notification_channels.send_discord(alert, discord_webhook)
                    
                    elif channel == AlertChannel.PUSHBULLET:
                        device = preferences.get('pushbullet_device')
                        success = await self.notification_channels.send_pushbullet(alert, device)
                    
                    elif channel == AlertChannel.WEBHOOK:
                        webhook_url = preferences.get('webhook_url')
                        if webhook_url:
                            success = await self.notification_channels.send_webhook(alert, webhook_url)
                    
                    notification_results.append({
                        'user_id': user_id,
                        'channel': channel.value,
                        'success': success,
                        'timestamp': datetime.now().isoformat()
                    })
                    
                except Exception as e:
                    logger.error(f"Error sending notification via {channel.value} to {user_id}: {str(e)}")
                    notification_results.append({
                        'user_id': user_id,
                        'channel': channel.value,
                        'success': False,
                        'error': str(e),
                        'timestamp': datetime.now().isoformat()
                    })
            
            return notification_results
            
        except Exception as e:
            logger.error(f"Error sending notifications to recipient: {str(e)}")
            return []
    
    async def _setup_escalation(self, alert: Dict, initial_recipients: List[Dict]):
        """Setup alert escalation"""
        try:
            alert_id = alert['alert_id']
            escalation_time = datetime.now() + timedelta(minutes=self.config.escalation_timeout_minutes)
            
            self.escalation_tracker[alert_id] = {
                'alert': alert,
                'initial_recipients': initial_recipients,
                'escalation_time': escalation_time,
                'escalation_level': 0,
                'acknowledged': False
            }
            
            logger.info(f"Escalation setup for alert {alert_id} at {escalation_time}")
            
        except Exception as e:
            logger.error(f"Error setting up escalation: {str(e)}")
    
    async def _store_processed_alert(self, alert: Dict, notification_results: List[Dict]):
        """Store processed alert in database"""
        try:
            if not self.db_pool:
                return
            
            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO processed_alerts 
                    (alert_id, alert_type, alert_data, severity, notification_results, created_at)
                    VALUES ($1, $2, $3, $4, $5, $6)
                """,
                alert['alert_id'],
                alert.get('alert_type', 'unknown'),
                json.dumps(alert),
                alert.get('severity', 'medium'),
                json.dumps(notification_results),
                datetime.now()
                )
                
        except Exception as e:
            logger.error(f"Error storing processed alert: {str(e)}")
    
    async def stop_alerting(self):
        """Stop the alerting system"""
        try:
            logger.info("Stopping intelligent alerting system...")
            self.running = False
            
            # Close Kafka consumer
            if self.kafka_consumer:
                self.kafka_consumer.close()
            
            # Close Redis connection
            if self.redis_client:
                self.redis_client.close()
            
            # Close database pool
            if self.db_pool:
                await self.db_pool.close()
            
            # Wait for processing thread
            if self.processing_thread:
                self.processing_thread.join(timeout=5)
            
            logger.info("Intelligent alerting system stopped")
            
        except Exception as e:
            logger.error(f"Error stopping alerting system: {str(e)}")

async def main():
    """Main execution function"""
    config = AlertingConfig()
    
    # Initialize alerting system
    alerting_system = IntelligentAlertingSystem(config)
    await alerting_system.initialize()
    
    # Start alerting
    await alerting_system.start_alerting()

if __name__ == "__main__":
    asyncio.run(main())