#!/usr/bin/env python3
"""
CQRS Framework Implementation for E-commerce Analytics
Command Query Responsibility Segregation with Event Sourcing integration
"""

import asyncio
import logging
import uuid
from abc import ABC, abstractmethod
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any, Union, Type, Callable
from dataclasses import dataclass, field
from enum import Enum
import json

# Import event store components
from event_store import EventStore, EventStoreConfig, DomainEvent, EventMetadata, EventType

# Database and caching
import asyncpg
import redis.asyncio as redis

# Message handling
from kafka import KafkaProducer, KafkaConsumer
from kafka.errors import KafkaError

# Validation
from pydantic import BaseModel, validator
from typing_extensions import Annotated

# Performance monitoring
import time
from collections import defaultdict

# Configuration
@dataclass
class CQRSConfig:
    # Event store configuration
    event_store_config: EventStoreConfig = field(default_factory=EventStoreConfig)
    
    # Read model database configuration
    read_db_host: str = "analytics-db.cluster-xyz.us-east-1.rds.amazonaws.com"
    read_db_name: str = "analytics_read_models"
    read_db_user: str = "analytics_user"
    read_db_password: str = "analytics_password"
    
    # Command handling
    command_timeout_seconds: int = 30
    max_retry_attempts: int = 3
    enable_command_validation: bool = True
    
    # Query handling
    query_cache_ttl_seconds: int = 300  # 5 minutes
    enable_query_caching: bool = True
    max_query_results: int = 10000
    
    # Projection handling
    projection_batch_size: int = 100
    projection_checkpoint_frequency: int = 10
    enable_projection_snapshots: bool = True
    
    # Performance monitoring
    enable_metrics: bool = True
    slow_command_threshold_ms: int = 1000
    slow_query_threshold_ms: int = 500

# Base classes for CQRS pattern
class Command(BaseModel):
    """Base class for all commands"""
    command_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    correlation_id: Optional[str] = None
    user_id: Optional[str] = None
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class Query(BaseModel):
    """Base class for all queries"""
    query_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    user_id: Optional[str] = None
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    limit: Optional[int] = 100
    offset: Optional[int] = 0
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class CommandResult(BaseModel):
    """Result of command execution"""
    success: bool
    command_id: str
    aggregate_id: Optional[str] = None
    version: Optional[int] = None
    events: List[str] = field(default_factory=list)  # Event IDs
    error: Optional[str] = None
    execution_time_ms: Optional[float] = None

class QueryResult(BaseModel):
    """Result of query execution"""
    success: bool
    query_id: str
    data: Any = None
    total_count: Optional[int] = None
    error: Optional[str] = None
    execution_time_ms: Optional[float] = None
    from_cache: bool = False

# Command handlers
class CommandHandler(ABC):
    """Abstract base class for command handlers"""
    
    @abstractmethod
    async def handle(self, command: Command) -> CommandResult:
        """Handle a command and return result"""
        pass
    
    @abstractmethod
    def get_command_type(self) -> Type[Command]:
        """Return the command type this handler processes"""
        pass

class QueryHandler(ABC):
    """Abstract base class for query handlers"""
    
    @abstractmethod
    async def handle(self, query: Query) -> QueryResult:
        """Handle a query and return result"""
        pass
    
    @abstractmethod
    def get_query_type(self) -> Type[Query]:
        """Return the query type this handler processes"""
        pass

# Aggregate root for domain modeling
class AggregateRoot(ABC):
    """Base class for aggregate roots in domain model"""
    
    def __init__(self, aggregate_id: str):
        self.aggregate_id = aggregate_id
        self.version = 0
        self.uncommitted_events: List[DomainEvent] = []
    
    def apply_event(self, event: DomainEvent):
        """Apply an event to update aggregate state"""
        self.version = event.metadata.version
        self._when(event)
    
    def raise_event(self, event_type: str, event_data: Dict[str, Any], 
                   correlation_id: Optional[str] = None, user_id: Optional[str] = None):
        """Raise a new domain event"""
        self.version += 1
        
        metadata = EventMetadata(
            event_id=str(uuid.uuid4()),
            event_type=event_type,
            aggregate_id=self.aggregate_id,
            aggregate_type=self.__class__.__name__,
            version=self.version,
            timestamp=datetime.now(timezone.utc),
            correlation_id=correlation_id,
            user_id=user_id,
            source="domain_model"
        )
        
        event = DomainEvent(metadata=metadata, data=event_data)
        self.uncommitted_events.append(event)
        self.apply_event(event)
    
    def mark_events_as_committed(self):
        """Mark all uncommitted events as committed"""
        self.uncommitted_events.clear()
    
    @abstractmethod
    def _when(self, event: DomainEvent):
        """Handle specific event types to update aggregate state"""
        pass

# Projection for read models
class Projection(ABC):
    """Base class for projections (read models)"""
    
    def __init__(self, projection_name: str):
        self.projection_name = projection_name
        self.last_processed_version = 0
    
    @abstractmethod
    async def handle_event(self, event: DomainEvent):
        """Handle an event to update the projection"""
        pass
    
    @abstractmethod
    async def get_checkpoint(self) -> int:
        """Get the last processed event version"""
        pass
    
    @abstractmethod
    async def save_checkpoint(self, version: int):
        """Save the last processed event version"""
        pass

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class CommandBus:
    """Command bus for routing commands to appropriate handlers"""
    
    def __init__(self, config: CQRSConfig):
        self.config = config
        self.handlers: Dict[Type[Command], CommandHandler] = {}
        self.middleware: List[Callable] = []
        self.metrics = defaultdict(int) if config.enable_metrics else None
    
    def register_handler(self, handler: CommandHandler):
        """Register a command handler"""
        command_type = handler.get_command_type()
        self.handlers[command_type] = handler
        logger.info(f"Registered command handler for {command_type.__name__}")
    
    def add_middleware(self, middleware: Callable):
        """Add middleware to the command pipeline"""
        self.middleware.append(middleware)
    
    async def send(self, command: Command) -> CommandResult:
        """Send a command for processing"""
        start_time = time.time()
        
        try:
            # Validate command if enabled
            if self.config.enable_command_validation:
                self._validate_command(command)
            
            # Find handler
            handler = self.handlers.get(type(command))
            if not handler:
                return CommandResult(
                    success=False,
                    command_id=command.command_id,
                    error=f"No handler registered for command type: {type(command).__name__}"
                )
            
            # Apply middleware
            for middleware in self.middleware:
                command = await middleware(command)
            
            # Handle command
            result = await asyncio.wait_for(
                handler.handle(command),
                timeout=self.config.command_timeout_seconds
            )
            
            # Calculate execution time
            execution_time = (time.time() - start_time) * 1000
            result.execution_time_ms = execution_time
            
            # Update metrics
            if self.metrics:
                self.metrics[f"command.{type(command).__name__}.count"] += 1
                if execution_time > self.config.slow_command_threshold_ms:
                    self.metrics[f"command.{type(command).__name__}.slow_count"] += 1
            
            logger.debug(f"Command {command.command_id} processed in {execution_time:.2f}ms")
            return result
            
        except asyncio.TimeoutError:
            execution_time = (time.time() - start_time) * 1000
            logger.error(f"Command {command.command_id} timed out after {execution_time:.2f}ms")
            return CommandResult(
                success=False,
                command_id=command.command_id,
                error="Command execution timed out",
                execution_time_ms=execution_time
            )
        except Exception as e:
            execution_time = (time.time() - start_time) * 1000
            logger.error(f"Error processing command {command.command_id}: {str(e)}")
            return CommandResult(
                success=False,
                command_id=command.command_id,
                error=str(e),
                execution_time_ms=execution_time
            )
    
    def _validate_command(self, command: Command):
        """Validate command before processing"""
        if not command.command_id:
            raise ValueError("Command must have a command_id")
        
        # Additional validation can be added here
        # For example, schema validation, business rules, etc.

class QueryBus:
    """Query bus for routing queries to appropriate handlers"""
    
    def __init__(self, config: CQRSConfig):
        self.config = config
        self.handlers: Dict[Type[Query], QueryHandler] = {}
        self.cache: Optional[redis.Redis] = None
        self.metrics = defaultdict(int) if config.enable_metrics else None
    
    async def initialize(self):
        """Initialize query bus with caching if enabled"""
        if self.config.enable_query_caching:
            try:
                self.cache = redis.Redis(
                    host=self.config.event_store_config.redis_host,
                    port=self.config.event_store_config.redis_port,
                    db=self.config.event_store_config.redis_db + 1,  # Use different DB
                    decode_responses=True
                )
                await self.cache.ping()
                logger.info("Query cache initialized")
            except Exception as e:
                logger.warning(f"Could not initialize query cache: {str(e)}")
                self.cache = None
    
    def register_handler(self, handler: QueryHandler):
        """Register a query handler"""
        query_type = handler.get_query_type()
        self.handlers[query_type] = handler
        logger.info(f"Registered query handler for {query_type.__name__}")
    
    async def send(self, query: Query) -> QueryResult:
        """Send a query for processing"""
        start_time = time.time()
        
        try:
            # Check cache first
            if self.cache and self.config.enable_query_caching:
                cached_result = await self._get_from_cache(query)
                if cached_result:
                    execution_time = (time.time() - start_time) * 1000
                    cached_result.execution_time_ms = execution_time
                    cached_result.from_cache = True
                    return cached_result
            
            # Find handler
            handler = self.handlers.get(type(query))
            if not handler:
                return QueryResult(
                    success=False,
                    query_id=query.query_id,
                    error=f"No handler registered for query type: {type(query).__name__}"
                )
            
            # Handle query
            result = await handler.handle(query)
            
            # Calculate execution time
            execution_time = (time.time() - start_time) * 1000
            result.execution_time_ms = execution_time
            
            # Cache result if successful
            if result.success and self.cache and self.config.enable_query_caching:
                await self._cache_result(query, result)
            
            # Update metrics
            if self.metrics:
                self.metrics[f"query.{type(query).__name__}.count"] += 1
                if execution_time > self.config.slow_query_threshold_ms:
                    self.metrics[f"query.{type(query).__name__}.slow_count"] += 1
            
            logger.debug(f"Query {query.query_id} processed in {execution_time:.2f}ms")
            return result
            
        except Exception as e:
            execution_time = (time.time() - start_time) * 1000
            logger.error(f"Error processing query {query.query_id}: {str(e)}")
            return QueryResult(
                success=False,
                query_id=query.query_id,
                error=str(e),
                execution_time_ms=execution_time
            )
    
    async def _get_from_cache(self, query: Query) -> Optional[QueryResult]:
        """Get query result from cache"""
        try:
            cache_key = self._get_cache_key(query)
            cached_data = await self.cache.get(cache_key)
            
            if cached_data:
                result_dict = json.loads(cached_data)
                return QueryResult(**result_dict)
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting from cache: {str(e)}")
            return None
    
    async def _cache_result(self, query: Query, result: QueryResult):
        """Cache query result"""
        try:
            cache_key = self._get_cache_key(query)
            result_dict = result.dict()
            
            await self.cache.setex(
                cache_key,
                self.config.query_cache_ttl_seconds,
                json.dumps(result_dict, default=str)
            )
            
        except Exception as e:
            logger.error(f"Error caching result: {str(e)}")
    
    def _get_cache_key(self, query: Query) -> str:
        """Generate cache key for query"""
        query_type = type(query).__name__
        query_hash = hash(query.json())
        return f"query:{query_type}:{query_hash}"

class ProjectionManager:
    """Manages projections and their event processing"""
    
    def __init__(self, config: CQRSConfig, event_store: EventStore):
        self.config = config
        self.event_store = event_store
        self.projections: Dict[str, Projection] = {}
        self.db_pool: Optional[asyncpg.Pool] = None
        self.running = False
    
    async def initialize(self):
        """Initialize projection manager"""
        try:
            # Initialize read model database connection
            self.db_pool = await asyncpg.create_pool(
                host=self.config.read_db_host,
                database=self.config.read_db_name,
                user=self.config.read_db_user,
                password=self.config.read_db_password,
                min_size=5,
                max_size=20
            )
            
            # Create projection checkpoints table
            await self._create_checkpoint_table()
            
            logger.info("Projection manager initialized")
            
        except Exception as e:
            logger.error(f"Error initializing projection manager: {str(e)}")
            raise
    
    async def _create_checkpoint_table(self):
        """Create checkpoint table for tracking projection progress"""
        try:
            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    CREATE TABLE IF NOT EXISTS projection_checkpoints (
                        projection_name VARCHAR(255) PRIMARY KEY,
                        last_processed_version BIGINT NOT NULL DEFAULT 0,
                        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                    )
                """)
        except Exception as e:
            logger.error(f"Error creating checkpoint table: {str(e)}")
    
    def register_projection(self, projection: Projection):
        """Register a projection for event processing"""
        self.projections[projection.projection_name] = projection
        logger.info(f"Registered projection: {projection.projection_name}")
    
    async def start_processing(self):
        """Start processing events for all projections"""
        self.running = True
        
        # Start processing for each projection
        tasks = []
        for projection in self.projections.values():
            task = asyncio.create_task(self._process_projection_events(projection))
            tasks.append(task)
        
        # Wait for all tasks
        await asyncio.gather(*tasks)
    
    async def stop_processing(self):
        """Stop processing events"""
        self.running = False
        logger.info("Stopping projection processing")
    
    async def _process_projection_events(self, projection: Projection):
        """Process events for a specific projection"""
        try:
            # Get last checkpoint
            last_version = await self._get_checkpoint(projection.projection_name)
            
            logger.info(f"Starting projection {projection.projection_name} from version {last_version}")
            
            while self.running:
                try:
                    # Get events from the last processed version
                    events = await self._get_events_from_version(last_version + 1)
                    
                    if not events:
                        # No new events, wait before checking again
                        await asyncio.sleep(1)
                        continue
                    
                    # Process events in batches
                    batch_size = self.config.projection_batch_size
                    for i in range(0, len(events), batch_size):
                        batch = events[i:i + batch_size]
                        
                        for event in batch:
                            await projection.handle_event(event)
                            last_version = max(last_version, event.metadata.version)
                        
                        # Save checkpoint periodically
                        if (i // batch_size) % self.config.projection_checkpoint_frequency == 0:
                            await self._save_checkpoint(projection.projection_name, last_version)
                    
                    # Save final checkpoint for this batch
                    await self._save_checkpoint(projection.projection_name, last_version)
                    
                except Exception as e:
                    logger.error(f"Error processing events for projection {projection.projection_name}: {str(e)}")
                    await asyncio.sleep(5)  # Wait before retrying
                    
        except Exception as e:
            logger.error(f"Fatal error in projection {projection.projection_name}: {str(e)}")
    
    async def _get_events_from_version(self, from_version: int) -> List[DomainEvent]:
        """Get events starting from a specific version"""
        try:
            # This is a simplified implementation
            # In practice, you'd implement proper event streaming from the event store
            # For now, we'll return an empty list to avoid infinite loops
            return []
            
        except Exception as e:
            logger.error(f"Error getting events from version {from_version}: {str(e)}")
            return []
    
    async def _get_checkpoint(self, projection_name: str) -> int:
        """Get checkpoint for a projection"""
        try:
            async with self.db_pool.acquire() as conn:
                row = await conn.fetchrow(
                    "SELECT last_processed_version FROM projection_checkpoints WHERE projection_name = $1",
                    projection_name
                )
                
                if row:
                    return row['last_processed_version']
                else:
                    # Initialize checkpoint
                    await conn.execute(
                        "INSERT INTO projection_checkpoints (projection_name, last_processed_version) VALUES ($1, $2)",
                        projection_name, 0
                    )
                    return 0
                    
        except Exception as e:
            logger.error(f"Error getting checkpoint for {projection_name}: {str(e)}")
            return 0
    
    async def _save_checkpoint(self, projection_name: str, version: int):
        """Save checkpoint for a projection"""
        try:
            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    UPDATE projection_checkpoints 
                    SET last_processed_version = $1, updated_at = NOW()
                    WHERE projection_name = $2
                """, version, projection_name)
                
        except Exception as e:
            logger.error(f"Error saving checkpoint for {projection_name}: {str(e)}")

class CQRSFramework:
    """Main CQRS framework orchestrator"""
    
    def __init__(self, config: CQRSConfig):
        self.config = config
        self.event_store = EventStore(config.event_store_config)
        self.command_bus = CommandBus(config)
        self.query_bus = QueryBus(config)
        self.projection_manager = ProjectionManager(config, self.event_store)
    
    async def initialize(self):
        """Initialize all CQRS components"""
        try:
            await self.event_store.initialize()
            await self.query_bus.initialize()
            await self.projection_manager.initialize()
            
            logger.info("CQRS framework initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing CQRS framework: {str(e)}")
            raise
    
    async def start(self):
        """Start the CQRS framework"""
        try:
            # Start projection processing
            await self.projection_manager.start_processing()
            
        except Exception as e:
            logger.error(f"Error starting CQRS framework: {str(e)}")
            raise
    
    async def stop(self):
        """Stop the CQRS framework"""
        try:
            await self.projection_manager.stop_processing()
            await self.event_store.close()
            
            logger.info("CQRS framework stopped")
            
        except Exception as e:
            logger.error(f"Error stopping CQRS framework: {str(e)}")
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get performance metrics"""
        metrics = {}
        
        if self.command_bus.metrics:
            metrics['commands'] = dict(self.command_bus.metrics)
        
        if self.query_bus.metrics:
            metrics['queries'] = dict(self.query_bus.metrics)
        
        return metrics

# Example implementations for e-commerce domain

# Customer aggregate
class Customer(AggregateRoot):
    """Customer aggregate root"""
    
    def __init__(self, customer_id: str):
        super().__init__(customer_id)
        self.email = None
        self.name = None
        self.created_at = None
        self.is_active = True
    
    def register(self, email: str, name: str, user_id: str = None):
        """Register a new customer"""
        if self.version > 0:
            raise ValueError("Customer already exists")
        
        self.raise_event(
            EventType.CUSTOMER_REGISTERED.value,
            {
                "customer_id": self.aggregate_id,
                "email": email,
                "name": name,
                "created_at": datetime.now(timezone.utc).isoformat()
            },
            user_id=user_id
        )
    
    def update_profile(self, email: str = None, name: str = None, user_id: str = None):
        """Update customer profile"""
        changes = {}
        if email and email != self.email:
            changes["email"] = email
        if name and name != self.name:
            changes["name"] = name
        
        if changes:
            self.raise_event(
                EventType.CUSTOMER_UPDATED.value,
                {
                    "customer_id": self.aggregate_id,
                    "changes": changes,
                    "updated_at": datetime.now(timezone.utc).isoformat()
                },
                user_id=user_id
            )
    
    def _when(self, event: DomainEvent):
        """Apply events to update customer state"""
        if event.metadata.event_type == EventType.CUSTOMER_REGISTERED.value:
            self.email = event.data["email"]
            self.name = event.data["name"]
            self.created_at = event.data["created_at"]
        
        elif event.metadata.event_type == EventType.CUSTOMER_UPDATED.value:
            changes = event.data.get("changes", {})
            if "email" in changes:
                self.email = changes["email"]
            if "name" in changes:
                self.name = changes["name"]

# Commands
class RegisterCustomerCommand(Command):
    customer_id: str
    email: str
    name: str

class UpdateCustomerCommand(Command):
    customer_id: str
    email: Optional[str] = None
    name: Optional[str] = None

# Queries
class GetCustomerQuery(Query):
    customer_id: str

class ListCustomersQuery(Query):
    email_filter: Optional[str] = None
    name_filter: Optional[str] = None

# Command handler example
class CustomerCommandHandler(CommandHandler):
    """Command handler for customer-related commands"""
    
    def __init__(self, event_store: EventStore):
        self.event_store = event_store
    
    async def handle(self, command: Command) -> CommandResult:
        """Handle customer commands"""
        try:
            if isinstance(command, RegisterCustomerCommand):
                return await self._handle_register_customer(command)
            elif isinstance(command, UpdateCustomerCommand):
                return await self._handle_update_customer(command)
            else:
                return CommandResult(
                    success=False,
                    command_id=command.command_id,
                    error=f"Unsupported command type: {type(command).__name__}"
                )
                
        except Exception as e:
            return CommandResult(
                success=False,
                command_id=command.command_id,
                error=str(e)
            )
    
    async def _handle_register_customer(self, command: RegisterCustomerCommand) -> CommandResult:
        """Handle customer registration"""
        customer = Customer(command.customer_id)
        customer.register(command.email, command.name, command.user_id)
        
        # Save events
        for event in customer.uncommitted_events:
            success = await self.event_store.append_event(event)
            if not success:
                return CommandResult(
                    success=False,
                    command_id=command.command_id,
                    error="Failed to save events"
                )
        
        customer.mark_events_as_committed()
        
        return CommandResult(
            success=True,
            command_id=command.command_id,
            aggregate_id=customer.aggregate_id,
            version=customer.version,
            events=[e.metadata.event_id for e in customer.uncommitted_events]
        )
    
    async def _handle_update_customer(self, command: UpdateCustomerCommand) -> CommandResult:
        """Handle customer update"""
        # Load customer from events
        stream_id = f"Customer:{command.customer_id}"
        events = await self.event_store.get_events(stream_id)
        
        if not events:
            return CommandResult(
                success=False,
                command_id=command.command_id,
                error=f"Customer not found: {command.customer_id}"
            )
        
        # Reconstruct customer state
        customer = Customer(command.customer_id)
        for event in events:
            customer.apply_event(event)
        
        # Apply update
        customer.update_profile(command.email, command.name, command.user_id)
        
        # Save new events
        for event in customer.uncommitted_events:
            success = await self.event_store.append_event(event)
            if not success:
                return CommandResult(
                    success=False,
                    command_id=command.command_id,
                    error="Failed to save events"
                )
        
        event_ids = [e.metadata.event_id for e in customer.uncommitted_events]
        customer.mark_events_as_committed()
        
        return CommandResult(
            success=True,
            command_id=command.command_id,
            aggregate_id=customer.aggregate_id,
            version=customer.version,
            events=event_ids
        )
    
    def get_command_type(self) -> Type[Command]:
        return Command  # This handler handles multiple command types

async def main():
    """Test the CQRS framework"""
    config = CQRSConfig()
    cqrs = CQRSFramework(config)
    
    try:
        await cqrs.initialize()
        
        # Register command handler
        customer_handler = CustomerCommandHandler(cqrs.event_store)
        cqrs.command_bus.register_handler(customer_handler)
        
        # Test command
        command = RegisterCustomerCommand(
            customer_id="customer-123",
            email="<EMAIL>",
            name="Test Customer"
        )
        
        result = await cqrs.command_bus.send(command)
        
        if result.success:
            print(f"✅ Command executed successfully: {result.command_id}")
            print(f"   Aggregate ID: {result.aggregate_id}")
            print(f"   Version: {result.version}")
            print(f"   Events: {result.events}")
        else:
            print(f"❌ Command failed: {result.error}")
        
        # Get metrics
        metrics = cqrs.get_metrics()
        print(f"📊 Metrics: {metrics}")
        
    finally:
        await cqrs.stop()

if __name__ == "__main__":
    asyncio.run(main())