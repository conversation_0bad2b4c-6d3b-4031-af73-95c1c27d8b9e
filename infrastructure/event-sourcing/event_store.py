#!/usr/bin/env python3
"""
Event Store Implementation for Event Sourcing
Advanced event storage with CQRS pattern support for e-commerce analytics
"""

import asyncio
import json
import logging
import uuid
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any, Union, AsyncIterator
from dataclasses import dataclass, asdict
from enum import Enum
import hashlib
import os

# Database drivers
import asyncpg
from sqlalchemy import create_engine, text
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

# Kafka integration
from kafka import KafkaProducer
from kafka.errors import KafkaError

# Redis for caching and snapshots
import redis.asyncio as redis

# Serialization
import pickle
import orjson

# Configuration
@dataclass
class EventStoreConfig:
    # Database configuration
    db_host: str = os.getenv('DB_HOST', 'analytics-db.cluster-xyz.us-east-1.rds.amazonaws.com')
    db_name: str = os.getenv('DB_NAME', 'ecommerce_analytics')
    db_user: str = os.getenv('DB_USER', 'analytics_user')
    db_password: str = os.getenv('DB_PASSWORD', 'analytics_password')
    
    # Event store specific database
    eventstore_db_name: str = os.getenv('EVENTSTORE_DB_NAME', 'eventstore')
    
    # Kafka configuration
    kafka_bootstrap_servers: str = os.getenv('KAFKA_BOOTSTRAP_SERVERS', 'kafka:9092')
    kafka_topic_prefix: str = 'events'
    
    # Redis configuration
    redis_host: str = os.getenv('REDIS_HOST', 'redis-cluster')
    redis_port: int = int(os.getenv('REDIS_PORT', '6379'))
    redis_db: int = int(os.getenv('REDIS_DB', '5'))
    
    # Event store settings
    snapshot_frequency: int = 100  # Take snapshot every N events
    max_events_per_stream: int = 10000
    event_retention_days: int = 365
    enable_encryption: bool = True
    encryption_key: str = os.getenv('EVENT_ENCRYPTION_KEY', 'default-key-change-in-production')

# Event types and base classes
class EventType(Enum):
    # Customer events
    CUSTOMER_REGISTERED = "customer.registered"
    CUSTOMER_UPDATED = "customer.updated"
    CUSTOMER_DELETED = "customer.deleted"
    
    # Order events
    ORDER_CREATED = "order.created"
    ORDER_UPDATED = "order.updated"
    ORDER_CANCELLED = "order.cancelled"
    ORDER_COMPLETED = "order.completed"
    ORDER_REFUNDED = "order.refunded"
    
    # Product events
    PRODUCT_CREATED = "product.created"
    PRODUCT_UPDATED = "product.updated"
    PRODUCT_DELETED = "product.deleted"
    PRODUCT_PRICE_CHANGED = "product.price_changed"
    INVENTORY_UPDATED = "product.inventory_updated"
    
    # Payment events
    PAYMENT_INITIATED = "payment.initiated"
    PAYMENT_COMPLETED = "payment.completed"
    PAYMENT_FAILED = "payment.failed"
    PAYMENT_REFUNDED = "payment.refunded"
    
    # Analytics events
    PAGE_VIEW = "analytics.page_view"
    PRODUCT_VIEW = "analytics.product_view"
    CART_ITEM_ADDED = "analytics.cart_item_added"
    CART_ITEM_REMOVED = "analytics.cart_item_removed"
    CHECKOUT_STARTED = "analytics.checkout_started"
    
    # ML events
    PREDICTION_MADE = "ml.prediction_made"
    MODEL_TRAINED = "ml.model_trained"
    RECOMMENDATION_GENERATED = "ml.recommendation_generated"
    
    # System events
    SNAPSHOT_CREATED = "system.snapshot_created"
    MIGRATION_COMPLETED = "system.migration_completed"

@dataclass
class EventMetadata:
    event_id: str
    event_type: str
    aggregate_id: str
    aggregate_type: str
    version: int
    timestamp: datetime
    correlation_id: Optional[str] = None
    causation_id: Optional[str] = None
    user_id: Optional[str] = None
    source: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'event_id': self.event_id,
            'event_type': self.event_type,
            'aggregate_id': self.aggregate_id,
            'aggregate_type': self.aggregate_type,
            'version': self.version,
            'timestamp': self.timestamp.isoformat(),
            'correlation_id': self.correlation_id,
            'causation_id': self.causation_id,
            'user_id': self.user_id,
            'source': self.source
        }

@dataclass
class DomainEvent:
    metadata: EventMetadata
    data: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'metadata': self.metadata.to_dict(),
            'data': self.data
        }
    
    @classmethod
    def from_dict(cls, event_dict: Dict[str, Any]) -> 'DomainEvent':
        metadata_dict = event_dict['metadata']
        metadata = EventMetadata(
            event_id=metadata_dict['event_id'],
            event_type=metadata_dict['event_type'],
            aggregate_id=metadata_dict['aggregate_id'],
            aggregate_type=metadata_dict['aggregate_type'],
            version=metadata_dict['version'],
            timestamp=datetime.fromisoformat(metadata_dict['timestamp']),
            correlation_id=metadata_dict.get('correlation_id'),
            causation_id=metadata_dict.get('causation_id'),
            user_id=metadata_dict.get('user_id'),
            source=metadata_dict.get('source')
        )
        
        return cls(metadata=metadata, data=event_dict['data'])

@dataclass
class StreamMetadata:
    stream_id: str
    aggregate_type: str
    version: int
    created_at: datetime
    updated_at: datetime
    snapshot_version: Optional[int] = None
    snapshot_data: Optional[Dict[str, Any]] = None

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class EventEncryption:
    """Handles event data encryption for sensitive information"""
    
    def __init__(self, encryption_key: str):
        self.encryption_key = encryption_key.encode()
    
    def encrypt_data(self, data: Dict[str, Any]) -> str:
        """Encrypt event data using AES encryption"""
        try:
            from cryptography.fernet import Fernet
            import base64
            
            # Create key from provided string
            key = base64.urlsafe_b64encode(hashlib.sha256(self.encryption_key).digest())
            fernet = Fernet(key)
            
            # Serialize and encrypt data
            serialized_data = orjson.dumps(data)
            encrypted_data = fernet.encrypt(serialized_data)
            
            return base64.b64encode(encrypted_data).decode()
            
        except Exception as e:
            logger.error(f"Error encrypting event data: {str(e)}")
            # Fallback to unencrypted if encryption fails
            return orjson.dumps(data).decode()
    
    def decrypt_data(self, encrypted_data: str) -> Dict[str, Any]:
        """Decrypt event data"""
        try:
            from cryptography.fernet import Fernet
            import base64
            
            # Create key from provided string
            key = base64.urlsafe_b64encode(hashlib.sha256(self.encryption_key).digest())
            fernet = Fernet(key)
            
            # Decode and decrypt data
            encrypted_bytes = base64.b64decode(encrypted_data.encode())
            decrypted_data = fernet.decrypt(encrypted_bytes)
            
            return orjson.loads(decrypted_data)
            
        except Exception as e:
            logger.error(f"Error decrypting event data: {str(e)}")
            # Fallback to direct JSON parsing
            try:
                return orjson.loads(encrypted_data)
            except:
                return {}

class EventStore:
    """Core event store implementation with PostgreSQL backend"""
    
    def __init__(self, config: EventStoreConfig):
        self.config = config
        self.db_pool = None
        self.redis_client = None
        self.kafka_producer = None
        self.encryption = EventEncryption(config.encryption_key) if config.enable_encryption else None
        
    async def initialize(self):
        """Initialize database connections and create tables"""
        try:
            # Initialize PostgreSQL connection pool
            self.db_pool = await asyncpg.create_pool(
                host=self.config.db_host,
                database=self.config.eventstore_db_name,
                user=self.config.db_user,
                password=self.config.db_password,
                min_size=5,
                max_size=20
            )
            
            # Initialize Redis connection
            self.redis_client = redis.Redis(
                host=self.config.redis_host,
                port=self.config.redis_port,
                db=self.config.redis_db,
                decode_responses=True
            )
            
            # Initialize Kafka producer
            self.kafka_producer = KafkaProducer(
                bootstrap_servers=self.config.kafka_bootstrap_servers,
                value_serializer=lambda v: orjson.dumps(v),
                key_serializer=lambda k: str(k).encode('utf-8'),
                compression_type='lz4',
                acks='all',
                retries=3
            )
            
            # Create database tables
            await self._create_tables()
            
            logger.info("Event store initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing event store: {str(e)}")
            raise
    
    async def _create_tables(self):
        """Create event store tables"""
        try:
            async with self.db_pool.acquire() as conn:
                # Events table
                await conn.execute("""
                    CREATE TABLE IF NOT EXISTS events (
                        id BIGSERIAL PRIMARY KEY,
                        event_id UUID UNIQUE NOT NULL,
                        stream_id VARCHAR(255) NOT NULL,
                        aggregate_type VARCHAR(100) NOT NULL,
                        aggregate_id VARCHAR(255) NOT NULL,
                        event_type VARCHAR(100) NOT NULL,
                        version INTEGER NOT NULL,
                        event_data TEXT NOT NULL,
                        metadata JSONB,
                        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                        correlation_id UUID,
                        causation_id UUID,
                        user_id VARCHAR(255),
                        source VARCHAR(100),
                        checksum VARCHAR(64)
                    )
                """)
                
                # Streams table
                await conn.execute("""
                    CREATE TABLE IF NOT EXISTS streams (
                        stream_id VARCHAR(255) PRIMARY KEY,
                        aggregate_type VARCHAR(100) NOT NULL,
                        version INTEGER NOT NULL DEFAULT 0,
                        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                    )
                """)
                
                # Snapshots table
                await conn.execute("""
                    CREATE TABLE IF NOT EXISTS snapshots (
                        id BIGSERIAL PRIMARY KEY,
                        stream_id VARCHAR(255) NOT NULL,
                        aggregate_type VARCHAR(100) NOT NULL,
                        version INTEGER NOT NULL,
                        snapshot_data TEXT NOT NULL,
                        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                        UNIQUE(stream_id, version)
                    )
                """)
                
                # Projections table (for CQRS read models)
                await conn.execute("""
                    CREATE TABLE IF NOT EXISTS projections (
                        id BIGSERIAL PRIMARY KEY,
                        projection_name VARCHAR(255) NOT NULL,
                        aggregate_id VARCHAR(255) NOT NULL,
                        version INTEGER NOT NULL,
                        data JSONB NOT NULL,
                        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                        UNIQUE(projection_name, aggregate_id)
                    )
                """)
                
                # Create indexes
                await conn.execute("CREATE INDEX IF NOT EXISTS idx_events_stream_id ON events(stream_id)")
                await conn.execute("CREATE INDEX IF NOT EXISTS idx_events_aggregate_id ON events(aggregate_id)")
                await conn.execute("CREATE INDEX IF NOT EXISTS idx_events_event_type ON events(event_type)")
                await conn.execute("CREATE INDEX IF NOT EXISTS idx_events_created_at ON events(created_at)")
                await conn.execute("CREATE INDEX IF NOT EXISTS idx_events_correlation_id ON events(correlation_id)")
                
                await conn.execute("CREATE INDEX IF NOT EXISTS idx_streams_aggregate_type ON streams(aggregate_type)")
                await conn.execute("CREATE INDEX IF NOT EXISTS idx_snapshots_stream_id ON snapshots(stream_id)")
                await conn.execute("CREATE INDEX IF NOT EXISTS idx_projections_name_id ON projections(projection_name, aggregate_id)")
            
            logger.info("Event store tables created successfully")
            
        except Exception as e:
            logger.error(f"Error creating event store tables: {str(e)}")
            raise
    
    async def append_event(self, event: DomainEvent) -> bool:
        """Append a single event to the event store"""
        try:
            async with self.db_pool.acquire() as conn:
                async with conn.transaction():
                    # Check if stream exists, create if not
                    stream_id = f"{event.metadata.aggregate_type}:{event.metadata.aggregate_id}"
                    
                    stream_result = await conn.fetchrow(
                        "SELECT version FROM streams WHERE stream_id = $1",
                        stream_id
                    )
                    
                    if stream_result is None:
                        # Create new stream
                        await conn.execute(
                            "INSERT INTO streams (stream_id, aggregate_type, version) VALUES ($1, $2, $3)",
                            stream_id, event.metadata.aggregate_type, 0
                        )
                        current_version = 0
                    else:
                        current_version = stream_result['version']
                    
                    # Check version consistency
                    expected_version = current_version + 1
                    if event.metadata.version != expected_version:
                        raise Exception(f"Version mismatch: expected {expected_version}, got {event.metadata.version}")
                    
                    # Encrypt event data if encryption is enabled
                    event_data = self.encryption.encrypt_data(event.data) if self.encryption else orjson.dumps(event.data).decode()
                    
                    # Calculate checksum
                    checksum = hashlib.sha256(f"{event.metadata.event_id}{event_data}".encode()).hexdigest()
                    
                    # Insert event
                    await conn.execute("""
                        INSERT INTO events (
                            event_id, stream_id, aggregate_type, aggregate_id, event_type, 
                            version, event_data, metadata, correlation_id, causation_id, 
                            user_id, source, checksum
                        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
                    """,
                        event.metadata.event_id,
                        stream_id,
                        event.metadata.aggregate_type,
                        event.metadata.aggregate_id,
                        event.metadata.event_type,
                        event.metadata.version,
                        event_data,
                        orjson.dumps(event.metadata.to_dict()).decode(),
                        event.metadata.correlation_id,
                        event.metadata.causation_id,
                        event.metadata.user_id,
                        event.metadata.source,
                        checksum
                    )
                    
                    # Update stream version
                    await conn.execute(
                        "UPDATE streams SET version = $1, updated_at = NOW() WHERE stream_id = $2",
                        event.metadata.version, stream_id
                    )
                    
                    # Publish to Kafka
                    await self._publish_to_kafka(event)
                    
                    # Cache latest event
                    await self._cache_event(event)
                    
                    # Check if snapshot is needed
                    if event.metadata.version % self.config.snapshot_frequency == 0:
                        await self._create_snapshot_if_needed(stream_id, event.metadata.aggregate_type, event.metadata.version)
                    
                    logger.debug(f"Event appended: {event.metadata.event_id}")
                    return True
                    
        except Exception as e:
            logger.error(f"Error appending event: {str(e)}")
            return False
    
    async def append_events(self, events: List[DomainEvent]) -> bool:
        """Append multiple events atomically"""
        try:
            async with self.db_pool.acquire() as conn:
                async with conn.transaction():
                    for event in events:
                        success = await self.append_event(event)
                        if not success:
                            raise Exception(f"Failed to append event: {event.metadata.event_id}")
                    
                    return True
                    
        except Exception as e:
            logger.error(f"Error appending events batch: {str(e)}")
            return False
    
    async def get_events(self, stream_id: str, from_version: int = 1, to_version: Optional[int] = None) -> List[DomainEvent]:
        """Get events from a specific stream"""
        try:
            async with self.db_pool.acquire() as conn:
                query = """
                    SELECT event_id, aggregate_type, aggregate_id, event_type, version, 
                           event_data, metadata, created_at, correlation_id, causation_id, 
                           user_id, source
                    FROM events 
                    WHERE stream_id = $1 AND version >= $2
                """
                params = [stream_id, from_version]
                
                if to_version is not None:
                    query += " AND version <= $3"
                    params.append(to_version)
                
                query += " ORDER BY version"
                
                rows = await conn.fetch(query, *params)
                
                events = []
                for row in rows:
                    # Decrypt event data if needed
                    if self.encryption:
                        event_data = self.encryption.decrypt_data(row['event_data'])
                    else:
                        event_data = orjson.loads(row['event_data'])
                    
                    metadata = EventMetadata(
                        event_id=row['event_id'],
                        event_type=row['event_type'],
                        aggregate_id=row['aggregate_id'],
                        aggregate_type=row['aggregate_type'],
                        version=row['version'],
                        timestamp=row['created_at'],
                        correlation_id=row['correlation_id'],
                        causation_id=row['causation_id'],
                        user_id=row['user_id'],
                        source=row['source']
                    )
                    
                    events.append(DomainEvent(metadata=metadata, data=event_data))
                
                return events
                
        except Exception as e:
            logger.error(f"Error getting events for stream {stream_id}: {str(e)}")
            return []
    
    async def get_events_by_type(self, event_type: str, limit: int = 100, offset: int = 0) -> List[DomainEvent]:
        """Get events by event type"""
        try:
            async with self.db_pool.acquire() as conn:
                rows = await conn.fetch("""
                    SELECT event_id, aggregate_type, aggregate_id, event_type, version, 
                           event_data, metadata, created_at, correlation_id, causation_id, 
                           user_id, source
                    FROM events 
                    WHERE event_type = $1
                    ORDER BY created_at DESC
                    LIMIT $2 OFFSET $3
                """, event_type, limit, offset)
                
                events = []
                for row in rows:
                    # Decrypt event data if needed
                    if self.encryption:
                        event_data = self.encryption.decrypt_data(row['event_data'])
                    else:
                        event_data = orjson.loads(row['event_data'])
                    
                    metadata = EventMetadata(
                        event_id=row['event_id'],
                        event_type=row['event_type'],
                        aggregate_id=row['aggregate_id'],
                        aggregate_type=row['aggregate_type'],
                        version=row['version'],
                        timestamp=row['created_at'],
                        correlation_id=row['correlation_id'],
                        causation_id=row['causation_id'],
                        user_id=row['user_id'],
                        source=row['source']
                    )
                    
                    events.append(DomainEvent(metadata=metadata, data=event_data))
                
                return events
                
        except Exception as e:
            logger.error(f"Error getting events by type {event_type}: {str(e)}")
            return []
    
    async def get_aggregate_events(self, aggregate_type: str, aggregate_id: str) -> List[DomainEvent]:
        """Get all events for a specific aggregate"""
        stream_id = f"{aggregate_type}:{aggregate_id}"
        return await self.get_events(stream_id)
    
    async def _publish_to_kafka(self, event: DomainEvent):
        """Publish event to Kafka for event streaming"""
        try:
            topic = f"{self.config.kafka_topic_prefix}.{event.metadata.aggregate_type}"
            
            # Prepare event for Kafka
            kafka_event = {
                'eventId': event.metadata.event_id,
                'eventType': event.metadata.event_type,
                'aggregateId': event.metadata.aggregate_id,
                'aggregateType': event.metadata.aggregate_type,
                'version': event.metadata.version,
                'timestamp': event.metadata.timestamp.isoformat(),
                'data': event.data,
                'metadata': {
                    'correlationId': event.metadata.correlation_id,
                    'causationId': event.metadata.causation_id,
                    'userId': event.metadata.user_id,
                    'source': event.metadata.source
                }
            }
            
            # Send to Kafka
            future = self.kafka_producer.send(
                topic, 
                key=event.metadata.aggregate_id,
                value=kafka_event
            )
            
            # Don't wait for delivery to maintain performance
            logger.debug(f"Event published to Kafka topic {topic}: {event.metadata.event_id}")
            
        except Exception as e:
            logger.error(f"Error publishing event to Kafka: {str(e)}")
    
    async def _cache_event(self, event: DomainEvent):
        """Cache event in Redis for fast access"""
        try:
            if self.redis_client:
                # Cache latest event for stream
                stream_key = f"stream:latest:{event.metadata.aggregate_type}:{event.metadata.aggregate_id}"
                await self.redis_client.setex(
                    stream_key, 
                    3600,  # 1 hour expiry
                    orjson.dumps(event.to_dict()).decode()
                )
                
                # Cache event by ID
                event_key = f"event:{event.metadata.event_id}"
                await self.redis_client.setex(
                    event_key,
                    3600,  # 1 hour expiry
                    orjson.dumps(event.to_dict()).decode()
                )
                
        except Exception as e:
            logger.error(f"Error caching event: {str(e)}")
    
    async def _create_snapshot_if_needed(self, stream_id: str, aggregate_type: str, version: int):
        """Create snapshot for aggregate at specific version"""
        try:
            # Get all events up to this version
            events = await self.get_events(stream_id, 1, version)
            
            if events:
                # Create snapshot data by replaying events
                snapshot_data = await self._replay_events_for_snapshot(events, aggregate_type)
                
                # Store snapshot
                if self.encryption:
                    encrypted_data = self.encryption.encrypt_data(snapshot_data)
                else:
                    encrypted_data = orjson.dumps(snapshot_data).decode()
                
                async with self.db_pool.acquire() as conn:
                    await conn.execute("""
                        INSERT INTO snapshots (stream_id, aggregate_type, version, snapshot_data)
                        VALUES ($1, $2, $3, $4)
                        ON CONFLICT (stream_id, version) DO UPDATE SET
                        snapshot_data = EXCLUDED.snapshot_data
                    """, stream_id, aggregate_type, version, encrypted_data)
                
                logger.info(f"Snapshot created for {stream_id} at version {version}")
                
        except Exception as e:
            logger.error(f"Error creating snapshot: {str(e)}")
    
    async def _replay_events_for_snapshot(self, events: List[DomainEvent], aggregate_type: str) -> Dict[str, Any]:
        """Replay events to create snapshot data"""
        try:
            # This is a simplified example - in practice, you'd have
            # aggregate-specific logic to replay events
            snapshot = {
                'aggregate_type': aggregate_type,
                'version': events[-1].metadata.version if events else 0,
                'last_updated': events[-1].metadata.timestamp.isoformat() if events else None,
                'event_count': len(events),
                'state': {}
            }
            
            # Apply events in order
            for event in events:
                snapshot['state'][event.metadata.event_type] = event.data
            
            return snapshot
            
        except Exception as e:
            logger.error(f"Error replaying events for snapshot: {str(e)}")
            return {}
    
    async def get_snapshot(self, stream_id: str) -> Optional[Dict[str, Any]]:
        """Get latest snapshot for a stream"""
        try:
            async with self.db_pool.acquire() as conn:
                row = await conn.fetchrow("""
                    SELECT version, snapshot_data, created_at
                    FROM snapshots
                    WHERE stream_id = $1
                    ORDER BY version DESC
                    LIMIT 1
                """, stream_id)
                
                if row:
                    if self.encryption:
                        snapshot_data = self.encryption.decrypt_data(row['snapshot_data'])
                    else:
                        snapshot_data = orjson.loads(row['snapshot_data'])
                    
                    return {
                        'version': row['version'],
                        'data': snapshot_data,
                        'created_at': row['created_at']
                    }
                
                return None
                
        except Exception as e:
            logger.error(f"Error getting snapshot for {stream_id}: {str(e)}")
            return None
    
    async def stream_events(self, from_timestamp: Optional[datetime] = None) -> AsyncIterator[DomainEvent]:
        """Stream events in real-time (generator)"""
        try:
            async with self.db_pool.acquire() as conn:
                query = "SELECT * FROM events"
                params = []
                
                if from_timestamp:
                    query += " WHERE created_at > $1"
                    params.append(from_timestamp)
                
                query += " ORDER BY created_at"
                
                async for row in conn.cursor(query, *params):
                    # Decrypt event data if needed
                    if self.encryption:
                        event_data = self.encryption.decrypt_data(row['event_data'])
                    else:
                        event_data = orjson.loads(row['event_data'])
                    
                    metadata = EventMetadata(
                        event_id=row['event_id'],
                        event_type=row['event_type'],
                        aggregate_id=row['aggregate_id'],
                        aggregate_type=row['aggregate_type'],
                        version=row['version'],
                        timestamp=row['created_at'],
                        correlation_id=row['correlation_id'],
                        causation_id=row['causation_id'],
                        user_id=row['user_id'],
                        source=row['source']
                    )
                    
                    yield DomainEvent(metadata=metadata, data=event_data)
                    
        except Exception as e:
            logger.error(f"Error streaming events: {str(e)}")
    
    async def close(self):
        """Close all connections"""
        try:
            if self.kafka_producer:
                self.kafka_producer.flush()
                self.kafka_producer.close()
            
            if self.redis_client:
                await self.redis_client.close()
            
            if self.db_pool:
                await self.db_pool.close()
            
            logger.info("Event store connections closed")
            
        except Exception as e:
            logger.error(f"Error closing event store: {str(e)}")

async def main():
    """Test the event store implementation"""
    config = EventStoreConfig()
    event_store = EventStore(config)
    
    try:
        await event_store.initialize()
        
        # Create test event
        event_id = str(uuid.uuid4())
        metadata = EventMetadata(
            event_id=event_id,
            event_type=EventType.CUSTOMER_REGISTERED.value,
            aggregate_id="customer-123",
            aggregate_type="Customer",
            version=1,
            timestamp=datetime.now(timezone.utc),
            user_id="admin",
            source="test"
        )
        
        event_data = {
            "customer_id": "customer-123",
            "email": "<EMAIL>",
            "name": "Test Customer",
            "created_at": datetime.now(timezone.utc).isoformat()
        }
        
        event = DomainEvent(metadata=metadata, data=event_data)
        
        # Append event
        success = await event_store.append_event(event)
        if success:
            print(f"✅ Event appended successfully: {event_id}")
        else:
            print(f"❌ Failed to append event: {event_id}")
        
        # Retrieve events
        stream_id = "Customer:customer-123"
        events = await event_store.get_events(stream_id)
        print(f"📋 Retrieved {len(events)} events from stream {stream_id}")
        
        for e in events:
            print(f"  - {e.metadata.event_type} (v{e.metadata.version})")
        
    finally:
        await event_store.close()

if __name__ == "__main__":
    asyncio.run(main())