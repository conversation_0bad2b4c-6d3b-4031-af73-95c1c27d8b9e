#!/usr/bin/env python3
"""
Real-Time Collaboration Server for E-commerce Analytics Platform
WebSocket-based real-time collaboration with live data sharing and dashboard editing
"""

import asyncio
import logging
import json
import uuid
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any, Set, Union
from dataclasses import dataclass, field
from enum import Enum
import os

# WebSocket and FastAPI
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException, Depends
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# Database and storage
import asyncpg
import aioredis

# Message handling
import orjson
from aiokafka import AIOKafkaProducer, AIOKafkaConsumer

# Authentication
import jwt
from passlib.context import CryptContext

# Data models
from pydantic import BaseModel, Field, validator
from typing_extensions import Literal

# Monitoring
import time
from collections import defaultdict
import prometheus_client
from prometheus_client import Counter, Histogram, Gauge

# Configuration
@dataclass
class CollaborationConfig:
    # Server settings
    host: str = "0.0.0.0"
    port: int = 8090
    
    # Database configuration
    db_host: str = os.getenv('DB_HOST', 'postgres-collaboration')
    db_name: str = os.getenv('DB_NAME', 'collaboration_system')
    db_user: str = os.getenv('DB_USER', 'collab_user')
    db_password: str = os.getenv('DB_PASSWORD', 'collab_password')
    
    # Redis for real-time state
    redis_url: str = os.getenv('REDIS_URL', 'redis://redis-cluster:6379/7')
    
    # Kafka for event streaming
    kafka_bootstrap_servers: str = os.getenv('KAFKA_BOOTSTRAP_SERVERS', 'kafka:9092')
    kafka_topic_collaboration: str = 'collaboration-events'
    
    # Security
    jwt_secret: str = os.getenv('JWT_SECRET', 'your-secret-key-change-in-production')
    jwt_algorithm: str = "HS256"
    
    # Performance settings
    max_connections_per_room: int = 100
    max_rooms: int = 1000
    ping_interval: int = 30  # seconds
    ping_timeout: int = 10   # seconds
    
    # Collaboration features
    enable_cursor_tracking: bool = True
    enable_live_editing: bool = True
    enable_voice_chat: bool = False  # Future feature
    enable_screen_sharing: bool = False  # Future feature
    
    # Data sync settings
    sync_interval: int = 5  # seconds
    max_sync_batch_size: int = 100
    conflict_resolution: str = "last_writer_wins"  # or "operational_transform"

# Collaboration event types
class CollaborationEventType(Enum):
    # User presence
    USER_JOINED = "user.joined"
    USER_LEFT = "user.left"
    USER_ACTIVE = "user.active"
    USER_IDLE = "user.idle"
    
    # Cursor tracking
    CURSOR_MOVED = "cursor.moved"
    CURSOR_SELECTION = "cursor.selection"
    
    # Dashboard editing
    DASHBOARD_EDIT_START = "dashboard.edit_start"
    DASHBOARD_EDIT_END = "dashboard.edit_end"
    DASHBOARD_COMPONENT_ADD = "dashboard.component.add"
    DASHBOARD_COMPONENT_UPDATE = "dashboard.component.update"
    DASHBOARD_COMPONENT_DELETE = "dashboard.component.delete"
    DASHBOARD_LAYOUT_CHANGE = "dashboard.layout.change"
    
    # Data sharing
    DATA_FILTER_CHANGE = "data.filter.change"
    DATA_QUERY_EXECUTE = "data.query.execute"
    DATA_EXPORT_START = "data.export.start"
    DATA_SHARE = "data.share"
    
    # Chat and communication
    CHAT_MESSAGE = "chat.message"
    CHAT_REACTION = "chat.reaction"
    ANNOTATION_ADD = "annotation.add"
    ANNOTATION_UPDATE = "annotation.update"
    ANNOTATION_DELETE = "annotation.delete"
    
    # System events
    ROOM_CREATED = "room.created"
    ROOM_UPDATED = "room.updated"
    ROOM_DELETED = "room.deleted"
    SYNC_STATE = "sync.state"

# Pydantic models
class User(BaseModel):
    id: str
    name: str
    email: str
    avatar_url: Optional[str] = None
    role: str = "viewer"  # viewer, editor, admin
    
class Room(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    description: Optional[str] = None
    dashboard_id: Optional[str] = None
    created_by: str
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    settings: Dict[str, Any] = Field(default_factory=dict)
    
class CollaborationEvent(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    type: CollaborationEventType
    room_id: str
    user_id: str
    data: Dict[str, Any] = Field(default_factory=dict)
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            CollaborationEventType: lambda v: v.value
        }

class CursorPosition(BaseModel):
    x: float
    y: float
    element_id: Optional[str] = None
    viewport: Optional[Dict[str, float]] = None

class UserPresence(BaseModel):
    user_id: str
    room_id: str
    status: Literal["active", "idle", "away"] = "active"
    cursor_position: Optional[CursorPosition] = None
    current_view: Optional[str] = None
    last_activity: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Metrics
active_connections = Gauge('collaboration_active_connections', 'Number of active WebSocket connections')
active_rooms = Gauge('collaboration_active_rooms', 'Number of active collaboration rooms')
events_total = Counter('collaboration_events_total', 'Total collaboration events', ['event_type', 'room_id'])
connection_duration = Histogram('collaboration_connection_duration_seconds', 'Connection duration')

class ConnectionManager:
    """Manages WebSocket connections and room memberships"""
    
    def __init__(self):
        # Active connections: {user_id: {room_id: websocket}}
        self.connections: Dict[str, Dict[str, WebSocket]] = defaultdict(dict)
        
        # Room memberships: {room_id: {user_id: user_info}}
        self.rooms: Dict[str, Dict[str, User]] = defaultdict(dict)
        
        # User presence: {user_id: UserPresence}
        self.user_presence: Dict[str, UserPresence] = {}
    
    async def connect(self, websocket: WebSocket, user: User, room_id: str):
        """Connect user to a room"""
        await websocket.accept()
        
        # Store connection
        self.connections[user.id][room_id] = websocket
        self.rooms[room_id][user.id] = user
        
        # Update presence
        self.user_presence[user.id] = UserPresence(
            user_id=user.id,
            room_id=room_id,
            status="active"
        )
        
        # Update metrics
        active_connections.inc()
        active_rooms.set(len(self.rooms))
        
        logger.info(f"User {user.name} connected to room {room_id}")
        
        # Notify other users in the room
        await self.broadcast_to_room(
            room_id,
            CollaborationEvent(
                type=CollaborationEventType.USER_JOINED,
                room_id=room_id,
                user_id=user.id,
                data={
                    "user": user.dict(),
                    "room_members": [u.dict() for u in self.rooms[room_id].values()]
                }
            ),
            exclude_user=user.id
        )
    
    async def disconnect(self, user_id: str, room_id: str):
        """Disconnect user from a room"""
        if user_id in self.connections and room_id in self.connections[user_id]:
            del self.connections[user_id][room_id]
            
            if not self.connections[user_id]:
                del self.connections[user_id]
        
        if room_id in self.rooms and user_id in self.rooms[room_id]:
            user = self.rooms[room_id][user_id]
            del self.rooms[room_id][user_id]
            
            # Clean up empty rooms
            if not self.rooms[room_id]:
                del self.rooms[room_id]
        
        # Update presence
        if user_id in self.user_presence:
            del self.user_presence[user_id]
        
        # Update metrics
        active_connections.dec()
        active_rooms.set(len(self.rooms))
        
        logger.info(f"User {user_id} disconnected from room {room_id}")
        
        # Notify other users in the room
        if room_id in self.rooms:
            await self.broadcast_to_room(
                room_id,
                CollaborationEvent(
                    type=CollaborationEventType.USER_LEFT,
                    room_id=room_id,
                    user_id=user_id,
                    data={
                        "user_id": user_id,
                        "room_members": [u.dict() for u in self.rooms[room_id].values()]
                    }
                )
            )
    
    async def broadcast_to_room(self, room_id: str, event: CollaborationEvent, exclude_user: Optional[str] = None):
        """Broadcast event to all users in a room"""
        if room_id not in self.rooms:
            return
        
        message = orjson.dumps(event.dict()).decode()
        
        for user_id, user in self.rooms[room_id].items():
            if exclude_user and user_id == exclude_user:
                continue
                
            if user_id in self.connections and room_id in self.connections[user_id]:
                websocket = self.connections[user_id][room_id]
                try:
                    await websocket.send_text(message)
                except Exception as e:
                    logger.error(f"Error sending message to {user_id}: {str(e)}")
                    # Clean up broken connection
                    await self.disconnect(user_id, room_id)
    
    async def send_to_user(self, user_id: str, room_id: str, event: CollaborationEvent):
        """Send event to specific user"""
        if user_id in self.connections and room_id in self.connections[user_id]:
            websocket = self.connections[user_id][room_id]
            try:
                await websocket.send_text(orjson.dumps(event.dict()).decode())
            except Exception as e:
                logger.error(f"Error sending message to {user_id}: {str(e)}")
                await self.disconnect(user_id, room_id)
    
    def get_room_members(self, room_id: str) -> List[User]:
        """Get all members of a room"""
        return list(self.rooms.get(room_id, {}).values())
    
    def get_user_rooms(self, user_id: str) -> List[str]:
        """Get all rooms a user is connected to"""
        return list(self.connections.get(user_id, {}).keys())

class CollaborationDatabase:
    """Database operations for collaboration system"""
    
    def __init__(self, config: CollaborationConfig):
        self.config = config
        self.db_pool = None
    
    async def initialize(self):
        """Initialize database connection and create tables"""
        try:
            self.db_pool = await asyncpg.create_pool(
                host=self.config.db_host,
                database=self.config.db_name,
                user=self.config.db_user,
                password=self.config.db_password,
                min_size=5,
                max_size=20
            )
            
            await self._create_tables()
            logger.info("Collaboration database initialized")
            
        except Exception as e:
            logger.error(f"Error initializing collaboration database: {str(e)}")
            raise
    
    async def _create_tables(self):
        """Create collaboration system tables"""
        async with self.db_pool.acquire() as conn:
            # Rooms table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS collaboration_rooms (
                    id VARCHAR(255) PRIMARY KEY,
                    name VARCHAR(255) NOT NULL,
                    description TEXT,
                    dashboard_id VARCHAR(255),
                    created_by VARCHAR(255) NOT NULL,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    settings JSONB,
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                )
            """)
            
            # Room memberships table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS room_memberships (
                    id SERIAL PRIMARY KEY,
                    room_id VARCHAR(255) NOT NULL REFERENCES collaboration_rooms(id) ON DELETE CASCADE,
                    user_id VARCHAR(255) NOT NULL,
                    role VARCHAR(50) NOT NULL DEFAULT 'viewer',
                    joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    UNIQUE(room_id, user_id)
                )
            """)
            
            # Collaboration events table (for audit/replay)
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS collaboration_events (
                    id VARCHAR(255) PRIMARY KEY,
                    event_type VARCHAR(100) NOT NULL,
                    room_id VARCHAR(255) NOT NULL,
                    user_id VARCHAR(255) NOT NULL,
                    event_data JSONB NOT NULL,
                    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                )
            """)
            
            # Dashboard states table (for collaboration state persistence)
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS dashboard_states (
                    id SERIAL PRIMARY KEY,
                    room_id VARCHAR(255) NOT NULL REFERENCES collaboration_rooms(id) ON DELETE CASCADE,
                    dashboard_id VARCHAR(255) NOT NULL,
                    state_data JSONB NOT NULL,
                    version INTEGER NOT NULL DEFAULT 1,
                    created_by VARCHAR(255) NOT NULL,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    UNIQUE(room_id, dashboard_id)
                )
            """)
            
            # Annotations table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS annotations (
                    id VARCHAR(255) PRIMARY KEY,
                    room_id VARCHAR(255) NOT NULL REFERENCES collaboration_rooms(id) ON DELETE CASCADE,
                    dashboard_id VARCHAR(255),
                    component_id VARCHAR(255),
                    annotation_type VARCHAR(50) NOT NULL,
                    content TEXT NOT NULL,
                    position JSONB,
                    created_by VARCHAR(255) NOT NULL,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                )
            """)
            
            # Create indexes
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_events_room_timestamp ON collaboration_events(room_id, timestamp)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_events_type ON collaboration_events(event_type)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_memberships_user ON room_memberships(user_id)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_annotations_room ON annotations(room_id)")
    
    async def save_room(self, room: Room) -> bool:
        """Save collaboration room"""
        try:
            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO collaboration_rooms (id, name, description, dashboard_id, created_by, created_at, settings)
                    VALUES ($1, $2, $3, $4, $5, $6, $7)
                    ON CONFLICT (id) DO UPDATE SET
                    name = EXCLUDED.name,
                    description = EXCLUDED.description,
                    dashboard_id = EXCLUDED.dashboard_id,
                    settings = EXCLUDED.settings,
                    updated_at = NOW()
                """, 
                    room.id, room.name, room.description, room.dashboard_id,
                    room.created_by, room.created_at, json.dumps(room.settings)
                )
                return True
        except Exception as e:
            logger.error(f"Error saving room: {str(e)}")
            return False
    
    async def get_room(self, room_id: str) -> Optional[Room]:
        """Get room by ID"""
        try:
            async with self.db_pool.acquire() as conn:
                row = await conn.fetchrow(
                    "SELECT * FROM collaboration_rooms WHERE id = $1", room_id
                )
                if row:
                    return Room(
                        id=row['id'],
                        name=row['name'],
                        description=row['description'],
                        dashboard_id=row['dashboard_id'],
                        created_by=row['created_by'],
                        created_at=row['created_at'],
                        settings=json.loads(row['settings']) if row['settings'] else {}
                    )
                return None
        except Exception as e:
            logger.error(f"Error getting room: {str(e)}")
            return None
    
    async def save_event(self, event: CollaborationEvent) -> bool:
        """Save collaboration event"""
        try:
            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO collaboration_events (id, event_type, room_id, user_id, event_data, timestamp)
                    VALUES ($1, $2, $3, $4, $5, $6)
                """, 
                    event.id, event.type.value, event.room_id, event.user_id,
                    json.dumps(event.data), event.timestamp
                )
                return True
        except Exception as e:
            logger.error(f"Error saving event: {str(e)}")
            return False
    
    async def close(self):
        """Close database connection"""
        if self.db_pool:
            await self.db_pool.close()

class CollaborationServer:
    """Main collaboration server"""
    
    def __init__(self, config: CollaborationConfig):
        self.config = config
        self.connection_manager = ConnectionManager()
        self.database = CollaborationDatabase(config)
        self.redis = None
        self.kafka_producer = None
        self.app = FastAPI(title="Real-Time Collaboration Server")
        
        # Add CORS middleware
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        self._setup_routes()
    
    async def initialize(self):
        """Initialize collaboration server"""
        try:
            await self.database.initialize()
            
            # Initialize Redis
            self.redis = aioredis.from_url(self.config.redis_url)
            
            # Initialize Kafka producer
            self.kafka_producer = AIOKafkaProducer(
                bootstrap_servers=self.config.kafka_bootstrap_servers,
                value_serializer=lambda v: orjson.dumps(v)
            )
            await self.kafka_producer.start()
            
            logger.info("Collaboration server initialized")
            
        except Exception as e:
            logger.error(f"Error initializing collaboration server: {str(e)}")
            raise
    
    def _setup_routes(self):
        """Setup API routes"""
        
        @self.app.websocket("/ws/{room_id}")
        async def websocket_endpoint(websocket: WebSocket, room_id: str, token: str):
            """WebSocket endpoint for real-time collaboration"""
            try:
                # Verify JWT token
                user = await self._verify_token(token)
                if not user:
                    await websocket.close(code=1008, reason="Invalid token")
                    return
                
                # Connect user to room
                await self.connection_manager.connect(websocket, user, room_id)
                
                try:
                    while True:
                        # Receive message from client
                        data = await websocket.receive_text()
                        message = json.loads(data)
                        
                        # Create collaboration event
                        event = CollaborationEvent(
                            type=CollaborationEventType(message['type']),
                            room_id=room_id,
                            user_id=user.id,
                            data=message.get('data', {})
                        )
                        
                        # Process event
                        await self._process_collaboration_event(event)
                        
                except WebSocketDisconnect:
                    pass
                finally:
                    await self.connection_manager.disconnect(user.id, room_id)
                    
            except Exception as e:
                logger.error(f"WebSocket error: {str(e)}")
                await websocket.close(code=1011, reason="Internal error")
        
        @self.app.post("/rooms")
        async def create_room(room: Room, token: str = Depends(self._get_token)):
            """Create a new collaboration room"""
            user = await self._verify_token(token)
            if not user:
                raise HTTPException(status_code=401, detail="Invalid token")
            
            room.created_by = user.id
            success = await self.database.save_room(room)
            
            if not success:
                raise HTTPException(status_code=500, detail="Failed to create room")
            
            return {"room_id": room.id, "status": "created"}
        
        @self.app.get("/rooms/{room_id}")
        async def get_room(room_id: str, token: str = Depends(self._get_token)):
            """Get room information"""
            user = await self._verify_token(token)
            if not user:
                raise HTTPException(status_code=401, detail="Invalid token")
            
            room = await self.database.get_room(room_id)
            if not room:
                raise HTTPException(status_code=404, detail="Room not found")
            
            return room.dict()
        
        @self.app.get("/rooms/{room_id}/members")
        async def get_room_members(room_id: str, token: str = Depends(self._get_token)):
            """Get room members"""
            user = await self._verify_token(token)
            if not user:
                raise HTTPException(status_code=401, detail="Invalid token")
            
            members = self.connection_manager.get_room_members(room_id)
            return [member.dict() for member in members]
        
        @self.app.get("/health")
        async def health_check():
            """Health check endpoint"""
            return {
                "status": "healthy",
                "active_connections": len(self.connection_manager.connections),
                "active_rooms": len(self.connection_manager.rooms),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
    
    async def _process_collaboration_event(self, event: CollaborationEvent):
        """Process a collaboration event"""
        try:
            # Save event to database
            await self.database.save_event(event)
            
            # Update metrics
            events_total.labels(
                event_type=event.type.value,
                room_id=event.room_id
            ).inc()
            
            # Handle specific event types
            if event.type == CollaborationEventType.CURSOR_MOVED:
                await self._handle_cursor_movement(event)
            elif event.type == CollaborationEventType.DASHBOARD_COMPONENT_UPDATE:
                await self._handle_dashboard_update(event)
            elif event.type == CollaborationEventType.CHAT_MESSAGE:
                await self._handle_chat_message(event)
            elif event.type == CollaborationEventType.DATA_FILTER_CHANGE:
                await self._handle_data_filter_change(event)
            
            # Broadcast event to other users in the room
            await self.connection_manager.broadcast_to_room(
                event.room_id,
                event,
                exclude_user=event.user_id
            )
            
            # Publish to Kafka for external systems
            await self._publish_to_kafka(event)
            
        except Exception as e:
            logger.error(f"Error processing collaboration event: {str(e)}")
    
    async def _handle_cursor_movement(self, event: CollaborationEvent):
        """Handle cursor movement event"""
        if self.config.enable_cursor_tracking:
            # Update user presence in Redis
            presence_key = f"presence:{event.user_id}:{event.room_id}"
            await self.redis.setex(
                presence_key,
                60,  # 1 minute expiry
                orjson.dumps({
                    "cursor_position": event.data.get("cursor_position"),
                    "timestamp": event.timestamp.isoformat()
                })
            )
    
    async def _handle_dashboard_update(self, event: CollaborationEvent):
        """Handle dashboard component update"""
        if self.config.enable_live_editing:
            # Store dashboard state in Redis for real-time sync
            state_key = f"dashboard:{event.room_id}:{event.data.get('dashboard_id')}"
            await self.redis.setex(
                state_key,
                3600,  # 1 hour expiry
                orjson.dumps({
                    "component_id": event.data.get("component_id"),
                    "changes": event.data.get("changes"),
                    "updated_by": event.user_id,
                    "timestamp": event.timestamp.isoformat()
                })
            )
    
    async def _handle_chat_message(self, event: CollaborationEvent):
        """Handle chat message"""
        # Store chat message with expiry
        message_key = f"chat:{event.room_id}:{event.id}"
        await self.redis.setex(
            message_key,
            86400,  # 24 hours
            orjson.dumps({
                "message": event.data.get("message"),
                "user_id": event.user_id,
                "timestamp": event.timestamp.isoformat()
            })
        )
    
    async def _handle_data_filter_change(self, event: CollaborationEvent):
        """Handle data filter change"""
        # Sync filter state across all users
        filter_key = f"filters:{event.room_id}"
        await self.redis.setex(
            filter_key,
            1800,  # 30 minutes
            orjson.dumps({
                "filters": event.data.get("filters"),
                "applied_by": event.user_id,
                "timestamp": event.timestamp.isoformat()
            })
        )
    
    async def _publish_to_kafka(self, event: CollaborationEvent):
        """Publish event to Kafka"""
        try:
            await self.kafka_producer.send(
                self.config.kafka_topic_collaboration,
                value=event.dict()
            )
        except Exception as e:
            logger.error(f"Error publishing to Kafka: {str(e)}")
    
    async def _verify_token(self, token: str) -> Optional[User]:
        """Verify JWT token and return user"""
        try:
            payload = jwt.decode(token, self.config.jwt_secret, algorithms=[self.config.jwt_algorithm])
            return User(
                id=payload["user_id"],
                name=payload.get("name", "Unknown"),
                email=payload.get("email", ""),
                role=payload.get("role", "viewer")
            )
        except jwt.ExpiredSignatureError:
            logger.warning("Token has expired")
            return None
        except jwt.InvalidTokenError:
            logger.warning("Invalid token")
            return None
    
    def _get_token(self, authorization: HTTPAuthorizationCredentials = Depends(HTTPBearer())):
        """Extract token from Authorization header"""
        return authorization.credentials
    
    async def close(self):
        """Close collaboration server"""
        try:
            if self.kafka_producer:
                await self.kafka_producer.stop()
            if self.redis:
                await self.redis.close()
            await self.database.close()
        except Exception as e:
            logger.error(f"Error closing collaboration server: {str(e)}")

async def main():
    """Run the collaboration server"""
    config = CollaborationConfig()
    server = CollaborationServer(config)
    
    try:
        await server.initialize()
        
        # Run FastAPI server
        await uvicorn.run(
            server.app,
            host=config.host,
            port=config.port,
            log_level="info"
        )
        
    except Exception as e:
        logger.error(f"Error running collaboration server: {str(e)}")
        raise
    finally:
        await server.close()

if __name__ == "__main__":
    asyncio.run(main())