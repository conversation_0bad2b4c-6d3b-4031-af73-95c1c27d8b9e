/**
 * Real-Time Collaboration Client Library
 * JavaScript/TypeScript client for real-time collaboration features
 */

class CollaborationClient {
    constructor(config = {}) {
        this.config = {
            serverUrl: config.serverUrl || 'ws://localhost:8090',
            token: config.token || null,
            autoReconnect: config.autoReconnect !== false,
            reconnectInterval: config.reconnectInterval || 5000,
            maxReconnectAttempts: config.maxReconnectAttempts || 10,
            heartbeatInterval: config.heartbeatInterval || 30000,
            ...config
        };
        
        this.ws = null;
        this.roomId = null;
        this.userId = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.heartbeatTimer = null;
        
        // Event handlers
        this.eventHandlers = new Map();
        this.presenceHandlers = new Map();
        this.cursorHandlers = new Map();
        this.chatHandlers = new Map();
        
        // User presence tracking
        this.roomMembers = new Map();
        this.userCursors = new Map();
        
        // Dashboard collaboration state
        this.dashboardState = new Map();
        this.pendingChanges = new Map();
        
        // Chat state
        this.chatMessages = [];
        
        this.setupEventListeners();
    }
    
    /**
     * Connect to a collaboration room
     */
    async connect(roomId, token = null) {
        this.roomId = roomId;
        this.token = token || this.config.token;
        
        if (!this.token) {
            throw new Error('Authentication token is required');
        }
        
        const wsUrl = `${this.config.serverUrl}/ws/${roomId}?token=${this.token}`;
        
        try {
            this.ws = new WebSocket(wsUrl);
            
            this.ws.onopen = this.handleOpen.bind(this);
            this.ws.onmessage = this.handleMessage.bind(this);
            this.ws.onclose = this.handleClose.bind(this);
            this.ws.onerror = this.handleError.bind(this);
            
            return new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                    reject(new Error('Connection timeout'));
                }, 10000);
                
                this.ws.onopen = () => {
                    clearTimeout(timeout);
                    this.handleOpen();
                    resolve();
                };
                
                this.ws.onerror = (error) => {
                    clearTimeout(timeout);
                    reject(error);
                };
            });
            
        } catch (error) {
            console.error('Failed to connect to collaboration server:', error);
            throw error;
        }
    }
    
    /**
     * Disconnect from the collaboration room
     */
    disconnect() {
        this.isConnected = false;
        
        if (this.heartbeatTimer) {
            clearInterval(this.heartbeatTimer);
            this.heartbeatTimer = null;
        }
        
        if (this.ws) {
            this.ws.close();
            this.ws = null;
        }
        
        this.emit('disconnected');
    }
    
    /**
     * Handle WebSocket connection open
     */
    handleOpen() {
        this.isConnected = true;
        this.reconnectAttempts = 0;
        
        // Start heartbeat
        this.startHeartbeat();
        
        console.log(`Connected to collaboration room: ${this.roomId}`);
        this.emit('connected', { roomId: this.roomId });
    }
    
    /**
     * Handle incoming WebSocket messages
     */
    handleMessage(event) {
        try {
            const message = JSON.parse(event.data);
            this.processCollaborationEvent(message);
        } catch (error) {
            console.error('Error parsing message:', error);
        }
    }
    
    /**
     * Handle WebSocket connection close
     */
    handleClose(event) {
        this.isConnected = false;
        
        if (this.heartbeatTimer) {
            clearInterval(this.heartbeatTimer);
            this.heartbeatTimer = null;
        }
        
        console.log('Disconnected from collaboration server');
        this.emit('disconnected', { code: event.code, reason: event.reason });
        
        // Auto-reconnect if enabled
        if (this.config.autoReconnect && this.reconnectAttempts < this.config.maxReconnectAttempts) {
            this.scheduleReconnect();
        }
    }
    
    /**
     * Handle WebSocket errors
     */
    handleError(error) {
        console.error('WebSocket error:', error);
        this.emit('error', error);
    }
    
    /**
     * Process collaboration events
     */
    processCollaborationEvent(event) {
        const { type, user_id, data, timestamp } = event;
        
        switch (type) {
            case 'user.joined':
                this.handleUserJoined(data);
                break;
                
            case 'user.left':
                this.handleUserLeft(data);
                break;
                
            case 'cursor.moved':
                this.handleCursorMoved(user_id, data);
                break;
                
            case 'dashboard.component.update':
                this.handleDashboardUpdate(data);
                break;
                
            case 'chat.message':
                this.handleChatMessage(user_id, data, timestamp);
                break;
                
            case 'data.filter.change':
                this.handleDataFilterChange(data);
                break;
                
            default:
                console.log('Unknown event type:', type);
        }
        
        // Emit generic event
        this.emit('event', event);
        
        // Emit specific event type
        this.emit(type, { userId: user_id, data, timestamp });
    }
    
    /**
     * Handle user joined event
     */
    handleUserJoined(data) {
        const { user, room_members } = data;
        
        // Update room members
        room_members.forEach(member => {
            this.roomMembers.set(member.id, member);
        });
        
        console.log(`User joined: ${user.name}`);
        this.emit('userJoined', user);
        this.emit('membersUpdated', Array.from(this.roomMembers.values()));
    }
    
    /**
     * Handle user left event
     */
    handleUserLeft(data) {
        const { user_id, room_members } = data;
        
        // Remove user from members
        const user = this.roomMembers.get(user_id);
        this.roomMembers.delete(user_id);
        this.userCursors.delete(user_id);
        
        console.log(`User left: ${user ? user.name : user_id}`);
        this.emit('userLeft', { userId: user_id, user });
        this.emit('membersUpdated', Array.from(this.roomMembers.values()));
    }
    
    /**
     * Handle cursor movement
     */
    handleCursorMoved(userId, data) {
        this.userCursors.set(userId, {
            position: data.cursor_position,
            timestamp: Date.now()
        });
        
        this.emit('cursorMoved', { userId, position: data.cursor_position });
    }
    
    /**
     * Handle dashboard updates
     */
    handleDashboardUpdate(data) {
        const { dashboard_id, component_id, changes } = data;
        
        // Update local dashboard state
        const stateKey = `${dashboard_id}:${component_id}`;
        this.dashboardState.set(stateKey, {
            ...this.dashboardState.get(stateKey),
            ...changes,
            lastUpdated: Date.now()
        });
        
        this.emit('dashboardUpdated', { dashboardId: dashboard_id, componentId: component_id, changes });
    }
    
    /**
     * Handle chat messages
     */
    handleChatMessage(userId, data, timestamp) {
        const message = {
            id: data.id || Date.now().toString(),
            userId,
            message: data.message,
            timestamp: new Date(timestamp)
        };
        
        this.chatMessages.push(message);
        
        // Keep only last 100 messages
        if (this.chatMessages.length > 100) {
            this.chatMessages.shift();
        }
        
        this.emit('chatMessage', message);
    }
    
    /**
     * Handle data filter changes
     */
    handleDataFilterChange(data) {
        this.emit('dataFiltersChanged', data.filters);
    }
    
    /**
     * Send cursor position
     */
    sendCursorPosition(x, y, elementId = null) {
        if (!this.isConnected) return;
        
        this.sendEvent({
            type: 'cursor.moved',
            data: {
                cursor_position: { x, y, element_id: elementId }
            }
        });
    }
    
    /**
     * Send dashboard component update
     */
    updateDashboardComponent(dashboardId, componentId, changes) {
        if (!this.isConnected) return;
        
        this.sendEvent({
            type: 'dashboard.component.update',
            data: {
                dashboard_id: dashboardId,
                component_id: componentId,
                changes
            }
        });
    }
    
    /**
     * Send chat message
     */
    sendChatMessage(message) {
        if (!this.isConnected) return;
        
        const messageId = Date.now().toString();
        
        this.sendEvent({
            type: 'chat.message',
            data: {
                id: messageId,
                message
            }
        });
        
        return messageId;
    }
    
    /**
     * Send data filter change
     */
    sendDataFilterChange(filters) {
        if (!this.isConnected) return;
        
        this.sendEvent({
            type: 'data.filter.change',
            data: {
                filters
            }
        });
    }
    
    /**
     * Send annotation
     */
    addAnnotation(dashboardId, componentId, content, position) {
        if (!this.isConnected) return;
        
        this.sendEvent({
            type: 'annotation.add',
            data: {
                dashboard_id: dashboardId,
                component_id: componentId,
                content,
                position
            }
        });
    }
    
    /**
     * Send generic event
     */
    sendEvent(event) {
        if (!this.isConnected || !this.ws) {
            console.warn('Cannot send event: not connected');
            return;
        }
        
        try {
            this.ws.send(JSON.stringify(event));
        } catch (error) {
            console.error('Error sending event:', error);
        }
    }
    
    /**
     * Start heartbeat to keep connection alive
     */
    startHeartbeat() {
        this.heartbeatTimer = setInterval(() => {
            if (this.isConnected && this.ws) {
                this.ws.send(JSON.stringify({ type: 'ping' }));
            }
        }, this.config.heartbeatInterval);
    }
    
    /**
     * Schedule reconnection attempt
     */
    scheduleReconnect() {
        this.reconnectAttempts++;
        
        const delay = Math.min(
            this.config.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1),
            30000
        );
        
        console.log(`Scheduling reconnect attempt ${this.reconnectAttempts} in ${delay}ms`);
        
        setTimeout(() => {
            if (this.roomId && this.token) {
                this.connect(this.roomId, this.token).catch(error => {
                    console.error('Reconnection failed:', error);
                });
            }
        }, delay);
    }
    
    /**
     * Setup event listeners for DOM interactions
     */
    setupEventListeners() {
        // Track mouse movements for cursor position
        if (typeof document !== 'undefined') {
            let lastCursorSend = 0;
            const throttleInterval = 100; // Send cursor updates max every 100ms
            
            document.addEventListener('mousemove', (event) => {
                const now = Date.now();
                if (now - lastCursorSend > throttleInterval && this.isConnected) {
                    this.sendCursorPosition(event.clientX, event.clientY);
                    lastCursorSend = now;
                }
            });
            
            // Track user activity
            ['mousedown', 'keydown', 'scroll'].forEach(eventType => {
                document.addEventListener(eventType, () => {
                    this.sendEvent({
                        type: 'user.active',
                        data: { timestamp: Date.now() }
                    });
                });
            });
        }
    }
    
    /**
     * Event emitter methods
     */
    on(event, handler) {
        if (!this.eventHandlers.has(event)) {
            this.eventHandlers.set(event, []);
        }
        this.eventHandlers.get(event).push(handler);
    }
    
    off(event, handler) {
        if (this.eventHandlers.has(event)) {
            const handlers = this.eventHandlers.get(event);
            const index = handlers.indexOf(handler);
            if (index > -1) {
                handlers.splice(index, 1);
            }
        }
    }
    
    emit(event, data) {
        if (this.eventHandlers.has(event)) {
            this.eventHandlers.get(event).forEach(handler => {
                try {
                    handler(data);
                } catch (error) {
                    console.error(`Error in event handler for ${event}:`, error);
                }
            });
        }
    }
    
    /**
     * Get current room members
     */
    getRoomMembers() {
        return Array.from(this.roomMembers.values());
    }
    
    /**
     * Get user cursors
     */
    getUserCursors() {
        return Object.fromEntries(this.userCursors);
    }
    
    /**
     * Get chat messages
     */
    getChatMessages() {
        return [...this.chatMessages];
    }
    
    /**
     * Get dashboard state
     */
    getDashboardState(dashboardId, componentId = null) {
        if (componentId) {
            return this.dashboardState.get(`${dashboardId}:${componentId}`);
        }
        
        const state = {};
        for (const [key, value] of this.dashboardState.entries()) {
            if (key.startsWith(`${dashboardId}:`)) {
                const compId = key.split(':')[1];
                state[compId] = value;
            }
        }
        return state;
    }
}

// Export for different module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CollaborationClient;
} else if (typeof window !== 'undefined') {
    window.CollaborationClient = CollaborationClient;
}

/**
 * Collaboration UI Components
 */
class CollaborationUI {
    constructor(client, container) {
        this.client = client;
        this.container = container;
        this.cursors = new Map();
        
        this.setupEventHandlers();
        this.createUI();
    }
    
    setupEventHandlers() {
        this.client.on('cursorMoved', this.updateCursor.bind(this));
        this.client.on('userJoined', this.showUserJoinedNotification.bind(this));
        this.client.on('userLeft', this.removeUserCursor.bind(this));
        this.client.on('chatMessage', this.displayChatMessage.bind(this));
    }
    
    createUI() {
        // Create collaboration sidebar
        this.sidebar = document.createElement('div');
        this.sidebar.className = 'collaboration-sidebar';
        this.sidebar.innerHTML = `
            <div class="collaboration-header">
                <h3>Collaboration</h3>
                <button class="close-btn">×</button>
            </div>
            
            <div class="members-section">
                <h4>Members</h4>
                <div class="members-list"></div>
            </div>
            
            <div class="chat-section">
                <h4>Chat</h4>
                <div class="chat-messages"></div>
                <div class="chat-input-container">
                    <input type="text" class="chat-input" placeholder="Type a message...">
                    <button class="send-btn">Send</button>
                </div>
            </div>
        `;
        
        this.container.appendChild(this.sidebar);
        this.setupUIEventHandlers();
    }
    
    setupUIEventHandlers() {
        // Chat input
        const chatInput = this.sidebar.querySelector('.chat-input');
        const sendBtn = this.sidebar.querySelector('.send-btn');
        
        const sendMessage = () => {
            const message = chatInput.value.trim();
            if (message) {
                this.client.sendChatMessage(message);
                chatInput.value = '';
            }
        };
        
        sendBtn.addEventListener('click', sendMessage);
        chatInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
        
        // Close button
        this.sidebar.querySelector('.close-btn').addEventListener('click', () => {
            this.sidebar.style.display = 'none';
        });
    }
    
    updateCursor(data) {
        const { userId, position } = data;
        const user = this.client.roomMembers.get(userId);
        
        if (!user) return;
        
        let cursor = this.cursors.get(userId);
        if (!cursor) {
            cursor = this.createCursor(user);
            this.cursors.set(userId, cursor);
            document.body.appendChild(cursor);
        }
        
        cursor.style.left = `${position.x}px`;
        cursor.style.top = `${position.y}px`;
        cursor.style.display = 'block';
        
        // Hide cursor after 5 seconds of inactivity
        clearTimeout(cursor.hideTimeout);
        cursor.hideTimeout = setTimeout(() => {
            cursor.style.display = 'none';
        }, 5000);
    }
    
    createCursor(user) {
        const cursor = document.createElement('div');
        cursor.className = 'collaboration-cursor';
        cursor.innerHTML = `
            <div class="cursor-pointer"></div>
            <div class="cursor-label">${user.name}</div>
        `;
        
        // Add some color based on user ID
        const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3'];
        const color = colors[parseInt(user.id.slice(-1), 16) % colors.length];
        cursor.style.setProperty('--cursor-color', color);
        
        return cursor;
    }
    
    removeUserCursor(data) {
        const cursor = this.cursors.get(data.userId);
        if (cursor) {
            cursor.remove();
            this.cursors.delete(data.userId);
        }
    }
    
    showUserJoinedNotification(user) {
        const notification = document.createElement('div');
        notification.className = 'collaboration-notification';
        notification.textContent = `${user.name} joined the session`;
        
        document.body.appendChild(notification);
        setTimeout(() => notification.remove(), 3000);
    }
    
    displayChatMessage(message) {
        const chatMessages = this.sidebar.querySelector('.chat-messages');
        const messageEl = document.createElement('div');
        messageEl.className = 'chat-message';
        
        const user = this.client.roomMembers.get(message.userId);
        const userName = user ? user.name : 'Unknown';
        
        messageEl.innerHTML = `
            <div class="message-header">
                <span class="user-name">${userName}</span>
                <span class="timestamp">${message.timestamp.toLocaleTimeString()}</span>
            </div>
            <div class="message-content">${this.escapeHtml(message.message)}</div>
        `;
        
        chatMessages.appendChild(messageEl);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }
    
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    updateMembersList() {
        const membersList = this.sidebar.querySelector('.members-list');
        const members = this.client.getRoomMembers();
        
        membersList.innerHTML = members.map(member => `
            <div class="member-item">
                <div class="member-avatar">${member.name.charAt(0)}</div>
                <span class="member-name">${member.name}</span>
                <span class="member-role">${member.role}</span>
            </div>
        `).join('');
    }
}

// CSS styles (can be included in a separate CSS file)
const collabStyles = `
.collaboration-sidebar {
    position: fixed;
    right: 0;
    top: 0;
    width: 300px;
    height: 100vh;
    background: white;
    border-left: 1px solid #e0e0e0;
    box-shadow: -2px 0 10px rgba(0,0,0,0.1);
    z-index: 1000;
    display: flex;
    flex-direction: column;
}

.collaboration-header {
    padding: 1rem;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.members-section, .chat-section {
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
}

.chat-messages {
    height: 200px;
    overflow-y: auto;
    border: 1px solid #e0e0e0;
    padding: 0.5rem;
    margin-bottom: 0.5rem;
}

.chat-input-container {
    display: flex;
    gap: 0.5rem;
}

.chat-input {
    flex: 1;
    padding: 0.5rem;
    border: 1px solid #ccc;
    border-radius: 4px;
}

.collaboration-cursor {
    position: fixed;
    pointer-events: none;
    z-index: 9999;
    --cursor-color: #4ecdc4;
}

.cursor-pointer {
    width: 0;
    height: 0;
    border-left: 8px solid var(--cursor-color);
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
}

.cursor-label {
    background: var(--cursor-color);
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 12px;
    margin-left: 10px;
    margin-top: -5px;
}

.collaboration-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #4ecdc4;
    color: white;
    padding: 1rem;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 1001;
}
`;

// Inject styles
if (typeof document !== 'undefined') {
    const style = document.createElement('style');
    style.textContent = collabStyles;
    document.head.appendChild(style);
}