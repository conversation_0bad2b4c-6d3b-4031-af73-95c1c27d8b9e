#!/usr/bin/env python3
"""
Regional Data Processing System for Global Analytics Platform
Multi-region data processing with intelligent routing, local aggregation, and cross-region synchronization
"""

import asyncio
import logging
import json
import time
import uuid
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any, Set, Union, Tuple
from dataclasses import dataclass, field
from enum import Enum
import os
import geoip2.database
import numpy as np
import pandas as pd

# FastAPI and HTTP
from fastapi import FastAPI, Request, Response, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import httpx

# Database and storage
import asyncpg
import aioredis
from motor.motor_asyncio import AsyncIOMotorClient

# Message queues and streaming
from aiokafka import AIOKafkaProducer, AIOKafkaConsumer
import aiokafka.errors

# Data processing
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import KMeans
from sklearn.decomposition import PCA
import scipy.stats as stats

# Time series processing
import pandas as pd
from statsmodels.tsa.seasonal import seasonal_decompose
from statsmodels.tsa.holtwinters import ExponentialSmoothing

# Monitoring and metrics
import prometheus_client
from prometheus_client import Counter, Histogram, Gauge, start_http_server

# Serialization and compression
import orjson
import lz4.frame
import pickle

# Configuration
@dataclass
class RegionalProcessorConfig:
    # Region identification
    region: str = os.getenv('REGION', 'us-east-1')
    datacenter: str = os.getenv('DATACENTER', 'us-east-1a')
    processor_id: str = os.getenv('PROCESSOR_ID', f'regional-{uuid.uuid4().hex[:8]}')
    
    # Service settings
    host: str = "0.0.0.0"
    port: int = 8080
    metrics_port: int = 9090
    
    # Database configurations
    postgres_url: str = os.getenv('POSTGRES_URL', 'postgresql://postgres:password@localhost:5432/analytics')
    redis_url: str = os.getenv('REDIS_URL', 'redis://localhost:6379/0')
    mongodb_url: str = os.getenv('MONGODB_URL', 'mongodb://localhost:27017/analytics')
    
    # Kafka configuration
    kafka_brokers: str = os.getenv('KAFKA_BROKERS', 'localhost:9092')
    kafka_consumer_group: str = 'regional-processor'
    
    # Cross-region settings
    central_api_url: str = os.getenv('CENTRAL_API_URL', 'https://api.analytics-platform.com')
    peer_regions: List[str] = field(default_factory=lambda: ['us-west-2', 'eu-west-1', 'ap-southeast-1'])
    
    # Processing settings
    batch_size: int = 1000
    processing_interval: int = 10  # seconds
    aggregation_window: int = 300  # 5 minutes
    max_memory_usage: float = 0.8
    
    # Data retention
    raw_data_retention_days: int = 7
    aggregated_data_retention_days: int = 365
    hot_data_hours: int = 24
    
    # Advanced features
    enable_ml_processing: bool = True
    enable_real_time_aggregation: bool = True
    enable_cross_region_sync: bool = True
    enable_data_compression: bool = True
    enable_intelligent_routing: bool = True

class DataType(Enum):
    RAW_EVENT = "raw_event"
    AGGREGATED_METRIC = "aggregated_metric"
    ML_FEATURE = "ml_feature"
    USER_PROFILE = "user_profile"
    SESSION_DATA = "session_data"
    TRANSACTION = "transaction"

class ProcessingPriority(Enum):
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4

class RegionStatus(Enum):
    ACTIVE = "active"
    DEGRADED = "degraded"
    OFFLINE = "offline"

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Metrics
events_processed = Counter('regional_events_processed_total', 'Total events processed', ['region', 'data_type'])
processing_duration = Histogram('regional_processing_duration_seconds', 'Processing duration', ['operation'])
queue_size = Gauge('regional_processing_queue_size', 'Processing queue size')
memory_usage = Gauge('regional_memory_usage_bytes', 'Memory usage')
cross_region_latency = Histogram('regional_cross_region_latency_seconds', 'Cross-region sync latency', ['target_region'])
aggregation_lag = Gauge('regional_aggregation_lag_seconds', 'Aggregation lag behind real-time')

class DataStorage:
    """Multi-tier data storage with intelligent routing"""
    
    def __init__(self, config: RegionalProcessorConfig):
        self.config = config
        self.postgres_pool = None
        self.redis = None
        self.mongodb = None
        
    async def initialize(self):
        """Initialize all storage systems"""
        try:
            # PostgreSQL for structured data
            self.postgres_pool = await asyncpg.create_pool(
                self.config.postgres_url,
                min_size=5,
                max_size=20
            )
            
            # Redis for caching and real-time data
            self.redis = aioredis.from_url(self.config.redis_url)
            
            # MongoDB for document storage
            self.mongodb = AsyncIOMotorClient(self.config.mongodb_url)
            self.mongodb_db = self.mongodb.analytics
            
            await self._create_schemas()
            logger.info("Data storage initialized")
            
        except Exception as e:
            logger.error(f"Error initializing storage: {str(e)}")
            raise
    
    async def _create_schemas(self):
        """Create database schemas and collections"""
        async with self.postgres_pool.acquire() as conn:
            # Raw events table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS raw_events (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    event_type VARCHAR(100) NOT NULL,
                    user_id VARCHAR(255),
                    session_id VARCHAR(255),
                    event_data JSONB NOT NULL,
                    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
                    region VARCHAR(50) NOT NULL,
                    processed BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                )
            """)
            
            # Aggregated metrics table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS aggregated_metrics (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    metric_name VARCHAR(255) NOT NULL,
                    dimensions JSONB NOT NULL,
                    value DOUBLE PRECISION NOT NULL,
                    window_start TIMESTAMP WITH TIME ZONE NOT NULL,
                    window_end TIMESTAMP WITH TIME ZONE NOT NULL,
                    region VARCHAR(50) NOT NULL,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    UNIQUE(metric_name, dimensions, window_start, region)
                )
            """)
            
            # User profiles table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS user_profiles (
                    user_id VARCHAR(255) PRIMARY KEY,
                    profile_data JSONB NOT NULL,
                    last_activity TIMESTAMP WITH TIME ZONE,
                    region VARCHAR(50) NOT NULL,
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                )
            """)
            
            # Create indexes
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_raw_events_timestamp ON raw_events(timestamp)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_raw_events_type ON raw_events(event_type)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_raw_events_user ON raw_events(user_id)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_aggregated_metrics_name_time ON aggregated_metrics(metric_name, window_start)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_user_profiles_activity ON user_profiles(last_activity)")
    
    async def store_raw_event(self, event: Dict[str, Any]) -> bool:
        """Store raw event"""
        try:
            async with self.postgres_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO raw_events (event_type, user_id, session_id, event_data, timestamp, region)
                    VALUES ($1, $2, $3, $4, $5, $6)
                """, 
                    event['type'],
                    event.get('user_id'),
                    event.get('session_id'),
                    json.dumps(event),
                    datetime.fromisoformat(event['timestamp']),
                    self.config.region
                )
                return True
        except Exception as e:
            logger.error(f"Error storing raw event: {str(e)}")
            return False
    
    async def store_aggregated_metric(self, metric: Dict[str, Any]) -> bool:
        """Store aggregated metric"""
        try:
            async with self.postgres_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO aggregated_metrics 
                    (metric_name, dimensions, value, window_start, window_end, region)
                    VALUES ($1, $2, $3, $4, $5, $6)
                    ON CONFLICT (metric_name, dimensions, window_start, region) 
                    DO UPDATE SET value = EXCLUDED.value, created_at = NOW()
                """, 
                    metric['name'],
                    json.dumps(metric['dimensions']),
                    metric['value'],
                    metric['window_start'],
                    metric['window_end'],
                    self.config.region
                )
                return True
        except Exception as e:
            logger.error(f"Error storing aggregated metric: {str(e)}")
            return False
    
    async def get_user_profile(self, user_id: str) -> Optional[Dict[str, Any]]:
        """Get user profile"""
        try:
            async with self.postgres_pool.acquire() as conn:
                row = await conn.fetchrow(
                    "SELECT profile_data FROM user_profiles WHERE user_id = $1",
                    user_id
                )
                return json.loads(row['profile_data']) if row else None
        except Exception as e:
            logger.error(f"Error getting user profile: {str(e)}")
            return None
    
    async def update_user_profile(self, user_id: str, profile_data: Dict[str, Any]) -> bool:
        """Update user profile"""
        try:
            async with self.postgres_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO user_profiles (user_id, profile_data, last_activity, region)
                    VALUES ($1, $2, NOW(), $3)
                    ON CONFLICT (user_id) DO UPDATE SET
                    profile_data = EXCLUDED.profile_data,
                    last_activity = EXCLUDED.last_activity,
                    updated_at = NOW()
                """, 
                    user_id,
                    json.dumps(profile_data),
                    self.config.region
                )
                return True
        except Exception as e:
            logger.error(f"Error updating user profile: {str(e)}")
            return False
    
    async def cache_set(self, key: str, value: Any, ttl: int = 3600) -> bool:
        """Set cache value"""
        try:
            await self.redis.setex(key, ttl, orjson.dumps(value))
            return True
        except Exception as e:
            logger.error(f"Error setting cache: {str(e)}")
            return False
    
    async def cache_get(self, key: str) -> Optional[Any]:
        """Get cache value"""
        try:
            cached = await self.redis.get(key)
            return orjson.loads(cached) if cached else None
        except Exception as e:
            logger.error(f"Error getting cache: {str(e)}")
            return None
    
    async def close(self):
        """Close all connections"""
        try:
            if self.postgres_pool:
                await self.postgres_pool.close()
            if self.redis:
                await self.redis.close()
            if self.mongodb:
                self.mongodb.close()
        except Exception as e:
            logger.error(f"Error closing storage: {str(e)}")

class DataAggregator:
    """Real-time data aggregation engine"""
    
    def __init__(self, config: RegionalProcessorConfig, storage: DataStorage):
        self.config = config
        self.storage = storage
        self.aggregation_windows = [60, 300, 900, 3600]  # 1min, 5min, 15min, 1hour
        self.running_aggregations = {}
        
    async def process_event_for_aggregation(self, event: Dict[str, Any]):
        """Process event for real-time aggregation"""
        try:
            event_time = datetime.fromisoformat(event['timestamp'])
            event_type = event['type']
            
            # Update running aggregations for different windows
            for window_seconds in self.aggregation_windows:
                window_key = self._get_window_key(event_time, window_seconds)
                
                # Update counters
                await self._update_counter_metric(f"{event_type}_count", window_key, 1)
                
                # Update numeric metrics if present
                if 'value' in event and isinstance(event['value'], (int, float)):
                    await self._update_numeric_metric(f"{event_type}_sum", window_key, event['value'])
                    await self._update_avg_metric(f"{event_type}_avg", window_key, event['value'])
                
                # Update unique metrics
                if 'user_id' in event:
                    await self._update_unique_metric(f"{event_type}_unique_users", window_key, event['user_id'])
        
        except Exception as e:
            logger.error(f"Error processing event for aggregation: {str(e)}")
    
    def _get_window_key(self, event_time: datetime, window_seconds: int) -> str:
        """Get window key for time-based aggregation"""
        window_start = event_time.replace(second=0, microsecond=0)
        window_start = window_start.replace(minute=(window_start.minute // (window_seconds // 60)) * (window_seconds // 60))
        return f"{window_start.isoformat()}_{window_seconds}"
    
    async def _update_counter_metric(self, metric_name: str, window_key: str, increment: int = 1):
        """Update counter metric"""
        cache_key = f"agg:counter:{metric_name}:{window_key}"
        current_value = await self.storage.cache_get(cache_key) or 0
        await self.storage.cache_set(cache_key, current_value + increment, ttl=7200)
    
    async def _update_numeric_metric(self, metric_name: str, window_key: str, value: float):
        """Update numeric metric (sum)"""
        cache_key = f"agg:numeric:{metric_name}:{window_key}"
        current_value = await self.storage.cache_get(cache_key) or 0
        await self.storage.cache_set(cache_key, current_value + value, ttl=7200)
    
    async def _update_avg_metric(self, metric_name: str, window_key: str, value: float):
        """Update average metric"""
        cache_key_sum = f"agg:avg_sum:{metric_name}:{window_key}"
        cache_key_count = f"agg:avg_count:{metric_name}:{window_key}"
        
        current_sum = await self.storage.cache_get(cache_key_sum) or 0
        current_count = await self.storage.cache_get(cache_key_count) or 0
        
        await self.storage.cache_set(cache_key_sum, current_sum + value, ttl=7200)
        await self.storage.cache_set(cache_key_count, current_count + 1, ttl=7200)
    
    async def _update_unique_metric(self, metric_name: str, window_key: str, value: str):
        """Update unique count metric using HyperLogLog approximation"""
        cache_key = f"agg:unique:{metric_name}:{window_key}"
        unique_set = await self.storage.cache_get(cache_key) or set()
        
        if isinstance(unique_set, list):
            unique_set = set(unique_set)
        
        unique_set.add(value)
        
        # Limit set size for memory efficiency
        if len(unique_set) > 10000:
            unique_set = set(list(unique_set)[:10000])
        
        await self.storage.cache_set(cache_key, list(unique_set), ttl=7200)
    
    async def flush_aggregations(self):
        """Flush completed aggregations to storage"""
        try:
            current_time = datetime.now(timezone.utc)
            
            # Get all aggregation keys from Redis
            keys = await self.storage.redis.keys("agg:*")
            
            aggregations_to_flush = []
            
            for key_bytes in keys:
                key = key_bytes.decode() if isinstance(key_bytes, bytes) else key_bytes
                key_parts = key.split(":")
                
                if len(key_parts) >= 4:
                    metric_type = key_parts[1]
                    metric_name = key_parts[2]
                    window_info = ":".join(key_parts[3:])
                    
                    # Parse window info
                    window_parts = window_info.rsplit("_", 1)
                    if len(window_parts) == 2:
                        window_start_str, window_seconds_str = window_parts
                        window_start = datetime.fromisoformat(window_start_str)
                        window_seconds = int(window_seconds_str)
                        window_end = window_start + timedelta(seconds=window_seconds)
                        
                        # Check if window is complete
                        if current_time > window_end:
                            value = await self.storage.cache_get(key)
                            
                            if value is not None:
                                # Calculate final value based on metric type
                                final_value = self._calculate_final_value(metric_type, metric_name, value, window_info)
                                
                                aggregation = {
                                    'name': metric_name,
                                    'dimensions': {'region': self.config.region, 'window_seconds': window_seconds},
                                    'value': final_value,
                                    'window_start': window_start,
                                    'window_end': window_end
                                }
                                
                                aggregations_to_flush.append((key, aggregation))
            
            # Flush to persistent storage
            for key, aggregation in aggregations_to_flush:
                await self.storage.store_aggregated_metric(aggregation)
                await self.storage.redis.delete(key)
            
            if aggregations_to_flush:
                logger.info(f"Flushed {len(aggregations_to_flush)} aggregations to storage")
                
        except Exception as e:
            logger.error(f"Error flushing aggregations: {str(e)}")
    
    def _calculate_final_value(self, metric_type: str, metric_name: str, value: Any, window_info: str) -> float:
        """Calculate final value for aggregation"""
        if metric_type == "counter" or metric_type == "numeric":
            return float(value)
        elif metric_type == "unique":
            return float(len(value) if isinstance(value, (list, set)) else 0)
        elif metric_type == "avg_sum":
            # Need to get count for average calculation
            count_key = f"agg:avg_count:{metric_name.replace('_sum', '_avg')}:{window_info}"
            # This would need to be handled differently in a real implementation
            return float(value)
        else:
            return float(value)

class MLProcessor:
    """Machine learning processing for regional data"""
    
    def __init__(self, config: RegionalProcessorConfig, storage: DataStorage):
        self.config = config
        self.storage = storage
        self.models = {}
        self.feature_extractors = {}
        
    async def initialize(self):
        """Initialize ML models"""
        try:
            # Initialize user clustering model
            self.models['user_clustering'] = KMeans(n_clusters=5, random_state=42)
            
            # Initialize anomaly detection
            from sklearn.ensemble import IsolationForest
            self.models['anomaly_detection'] = IsolationForest(contamination=0.1, random_state=42)
            
            # Initialize feature scaler
            self.feature_extractors['scaler'] = StandardScaler()
            
            logger.info("ML processor initialized")
            
        except Exception as e:
            logger.error(f"Error initializing ML processor: {str(e)}")
    
    async def process_user_behavior(self, user_id: str, events: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Process user behavior for ML insights"""
        try:
            # Extract behavioral features
            features = self._extract_user_features(events)
            
            if not features:
                return {}
            
            # Perform clustering
            if self.models['user_clustering'].n_features_in_ is None:
                # Fit model if not already fitted
                sample_data = np.array([features] * 10)  # Dummy data for fitting
                self.models['user_clustering'].fit(sample_data)
            
            cluster = self.models['user_clustering'].predict([features])[0]
            
            # Anomaly detection
            anomaly_score = self.models['anomaly_detection'].fit([features]).score_samples([features])[0]
            
            insights = {
                'user_cluster': int(cluster),
                'anomaly_score': float(anomaly_score),
                'features': {
                    'session_count': len(set(e.get('session_id') for e in events)),
                    'event_count': len(events),
                    'avg_session_duration': np.mean([e.get('duration', 0) for e in events]),
                    'unique_event_types': len(set(e.get('type') for e in events))
                },
                'processed_at': datetime.now(timezone.utc).isoformat(),
                'region': self.config.region
            }
            
            # Update user profile with insights
            await self.storage.update_user_profile(user_id, insights)
            
            return insights
            
        except Exception as e:
            logger.error(f"Error processing user behavior: {str(e)}")
            return {}
    
    def _extract_user_features(self, events: List[Dict[str, Any]]) -> List[float]:
        """Extract numerical features from user events"""
        if not events:
            return []
        
        try:
            # Basic behavioral features
            session_count = len(set(e.get('session_id') for e in events if e.get('session_id')))
            event_count = len(events)
            unique_event_types = len(set(e.get('type') for e in events))
            
            # Time-based features
            timestamps = [datetime.fromisoformat(e['timestamp']) for e in events if 'timestamp' in e]
            if timestamps:
                time_span = (max(timestamps) - min(timestamps)).total_seconds()
                avg_time_between_events = time_span / len(timestamps) if len(timestamps) > 1 else 0
            else:
                time_span = 0
                avg_time_between_events = 0
            
            # Value-based features
            values = [e.get('value', 0) for e in events if isinstance(e.get('value'), (int, float))]
            avg_value = np.mean(values) if values else 0
            total_value = sum(values) if values else 0
            
            return [
                float(session_count),
                float(event_count),
                float(unique_event_types),
                float(time_span),
                float(avg_time_between_events),
                float(avg_value),
                float(total_value)
            ]
            
        except Exception as e:
            logger.error(f"Error extracting features: {str(e)}")
            return []

class CrossRegionSync:
    """Cross-region synchronization and replication"""
    
    def __init__(self, config: RegionalProcessorConfig, storage: DataStorage):
        self.config = config
        self.storage = storage
        self.http_client = None
        self.sync_queue = asyncio.Queue()
        
    async def initialize(self):
        """Initialize cross-region sync"""
        try:
            self.http_client = httpx.AsyncClient(timeout=30.0)
            logger.info("Cross-region sync initialized")
        except Exception as e:
            logger.error(f"Error initializing cross-region sync: {str(e)}")
    
    async def sync_aggregated_metrics(self, metrics: List[Dict[str, Any]]):
        """Sync aggregated metrics to other regions"""
        if not self.config.enable_cross_region_sync:
            return
        
        try:
            sync_payload = {
                'source_region': self.config.region,
                'metrics': metrics,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
            
            # Send to peer regions
            for region in self.config.peer_regions:
                asyncio.create_task(self._send_to_region(region, sync_payload))
                
        except Exception as e:
            logger.error(f"Error syncing metrics: {str(e)}")
    
    async def _send_to_region(self, target_region: str, payload: Dict[str, Any]):
        """Send data to specific region"""
        try:
            start_time = time.time()
            
            endpoint_url = f"https://{target_region}.analytics-platform.com/api/regional/sync"
            
            response = await self.http_client.post(
                endpoint_url,
                json=payload,
                headers={'X-Source-Region': self.config.region}
            )
            
            latency = time.time() - start_time
            cross_region_latency.labels(target_region=target_region).observe(latency)
            
            if response.status_code != 200:
                logger.warning(f"Failed to sync to {target_region}: HTTP {response.status_code}")
                
        except Exception as e:
            logger.error(f"Error sending to region {target_region}: {str(e)}")
    
    async def close(self):
        """Close HTTP client"""
        if self.http_client:
            await self.http_client.aclose()

class RegionalDataProcessor:
    """Main regional data processor"""
    
    def __init__(self, config: RegionalProcessorConfig):
        self.config = config
        self.app = FastAPI(title=f"Regional Data Processor - {config.region}")
        self.storage = DataStorage(config)
        self.aggregator = DataAggregator(config, self.storage)
        self.ml_processor = MLProcessor(config, self.storage)
        self.cross_region_sync = CrossRegionSync(config, self.storage)
        
        self.kafka_producer = None
        self.kafka_consumer = None
        self.processing_queue = asyncio.Queue(maxsize=10000)
        self.running = False
        
        # Add middleware
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        self._setup_routes()
        self._start_background_tasks()
    
    async def initialize(self):
        """Initialize regional processor"""
        try:
            await self.storage.initialize()
            await self.ml_processor.initialize()
            await self.cross_region_sync.initialize()
            
            # Initialize Kafka
            self.kafka_producer = AIOKafkaProducer(
                bootstrap_servers=self.config.kafka_brokers,
                value_serializer=lambda v: orjson.dumps(v)
            )
            await self.kafka_producer.start()
            
            self.kafka_consumer = AIOKafkaConsumer(
                f'regional-{self.config.region}',
                bootstrap_servers=self.config.kafka_brokers,
                group_id=self.config.kafka_consumer_group,
                value_deserializer=lambda v: orjson.loads(v)
            )
            await self.kafka_consumer.start()
            
            logger.info(f"Regional processor initialized for {self.config.region}")
            
        except Exception as e:
            logger.error(f"Error initializing regional processor: {str(e)}")
            raise
    
    def _setup_routes(self):
        """Setup API routes"""
        
        @self.app.post("/events/batch")
        async def process_event_batch(events: List[Dict[str, Any]], background_tasks: BackgroundTasks):
            """Process batch of events"""
            try:
                processed_count = 0
                
                for event in events:
                    # Add to processing queue
                    await self.processing_queue.put({
                        'type': 'event',
                        'data': event,
                        'priority': ProcessingPriority.MEDIUM.value
                    })
                    processed_count += 1
                
                return {
                    'status': 'success',
                    'processed_count': processed_count,
                    'region': self.config.region
                }
                
            except Exception as e:
                logger.error(f"Error processing event batch: {str(e)}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.post("/sync")
        async def receive_cross_region_sync(payload: Dict[str, Any]):
            """Receive cross-region sync data"""
            try:
                source_region = payload.get('source_region')
                metrics = payload.get('metrics', [])
                
                # Store synced metrics
                for metric in metrics:
                    metric['synced_from'] = source_region
                    await self.storage.store_aggregated_metric(metric)
                
                return {
                    'status': 'success',
                    'received_metrics': len(metrics),
                    'from_region': source_region
                }
                
            except Exception as e:
                logger.error(f"Error processing sync: {str(e)}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/health")
        async def health_check():
            """Health check endpoint"""
            return {
                'status': 'healthy',
                'region': self.config.region,
                'processor_id': self.config.processor_id,
                'queue_size': self.processing_queue.qsize(),
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
        
        @self.app.get("/metrics")
        async def get_metrics():
            """Prometheus metrics endpoint"""
            # Update queue size metric
            queue_size.set(self.processing_queue.qsize())
            
            from prometheus_client import generate_latest, CONTENT_TYPE_LATEST
            return Response(generate_latest(), media_type=CONTENT_TYPE_LATEST)
    
    def _start_background_tasks(self):
        """Start background processing tasks"""
        
        @self.app.on_event("startup")
        async def startup_event():
            # Start metrics server
            start_http_server(self.config.metrics_port)
            
            # Start background processing
            self.running = True
            asyncio.create_task(self._process_queue())
            asyncio.create_task(self._consume_kafka())
            asyncio.create_task(self._aggregation_flush_loop())
            
            await self.initialize()
        
        @self.app.on_event("shutdown")
        async def shutdown_event():
            self.running = False
            await self.close()
    
    async def _process_queue(self):
        """Process items from the queue"""
        while self.running:
            try:
                # Process items in batches
                batch = []
                for _ in range(self.config.batch_size):
                    try:
                        item = await asyncio.wait_for(
                            self.processing_queue.get(),
                            timeout=1.0
                        )
                        batch.append(item)
                    except asyncio.TimeoutError:
                        break
                
                if batch:
                    await self._process_batch(batch)
                
                await asyncio.sleep(self.config.processing_interval)
                
            except Exception as e:
                logger.error(f"Error in processing queue: {str(e)}")
                await asyncio.sleep(5)
    
    async def _process_batch(self, batch: List[Dict[str, Any]]):
        """Process a batch of items"""
        try:
            with processing_duration.labels(operation="batch_processing").time():
                for item in batch:
                    if item['type'] == 'event':
                        await self._process_single_event(item['data'])
                    
                events_processed.labels(
                    region=self.config.region,
                    data_type="batch"
                ).inc(len(batch))
                
        except Exception as e:
            logger.error(f"Error processing batch: {str(e)}")
    
    async def _process_single_event(self, event: Dict[str, Any]):
        """Process a single event"""
        try:
            # Store raw event
            await self.storage.store_raw_event(event)
            
            # Real-time aggregation
            if self.config.enable_real_time_aggregation:
                await self.aggregator.process_event_for_aggregation(event)
            
            # ML processing for user events
            if self.config.enable_ml_processing and event.get('user_id'):
                # This would be optimized to batch user events
                user_events = [event]  # In practice, collect multiple events per user
                await self.ml_processor.process_user_behavior(event['user_id'], user_events)
            
        except Exception as e:
            logger.error(f"Error processing single event: {str(e)}")
    
    async def _consume_kafka(self):
        """Consume events from Kafka"""
        while self.running:
            try:
                async for message in self.kafka_consumer:
                    event_data = message.value
                    
                    await self.processing_queue.put({
                        'type': 'event',
                        'data': event_data,
                        'priority': ProcessingPriority.MEDIUM.value
                    })
                    
            except Exception as e:
                logger.error(f"Error consuming from Kafka: {str(e)}")
                await asyncio.sleep(5)
    
    async def _aggregation_flush_loop(self):
        """Periodic aggregation flushing"""
        while self.running:
            try:
                await self.aggregator.flush_aggregations()
                await asyncio.sleep(60)  # Flush every minute
                
            except Exception as e:
                logger.error(f"Error in aggregation flush: {str(e)}")
                await asyncio.sleep(60)
    
    async def close(self):
        """Close regional processor"""
        try:
            if self.kafka_producer:
                await self.kafka_producer.stop()
            if self.kafka_consumer:
                await self.kafka_consumer.stop()
            await self.storage.close()
            await self.cross_region_sync.close()
            
            logger.info("Regional processor closed")
            
        except Exception as e:
            logger.error(f"Error closing regional processor: {str(e)}")

async def main():
    """Run the regional data processor"""
    config = RegionalProcessorConfig()
    processor = RegionalDataProcessor(config)
    
    try:
        # Run FastAPI server
        await uvicorn.run(
            processor.app,
            host=config.host,
            port=config.port,
            log_level="info"
        )
        
    except Exception as e:
        logger.error(f"Error running regional processor: {str(e)}")
        raise
    finally:
        await processor.close()

if __name__ == "__main__":
    asyncio.run(main())