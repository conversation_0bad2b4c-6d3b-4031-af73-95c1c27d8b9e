# Staging Environment Configuration

# Environment
environment = "staging"
aws_region  = "us-east-1"

# VPC Configuration
vpc_cidr = "10.1.0.0/16"
private_subnet_cidrs = ["10.1.1.0/24", "10.1.2.0/24"]
public_subnet_cidrs  = ["10.1.101.0/24", "10.1.102.0/24"]
database_subnet_cidrs = ["10.1.201.0/24", "10.1.202.0/24"]

# EKS Configuration
kubernetes_version = "1.27"
node_instance_types = ["t3.medium", "t3.large"]
node_group_min_size = 1
node_group_max_size = 5
node_group_desired_size = 2

# Spot instances for cost optimization
spot_instance_types = ["t3.medium", "t3.large"]
spot_max_size = 5
spot_desired_size = 1

# RDS Configuration
postgres_version = "14.9"
db_instance_class = "db.t3.large"
db_allocated_storage = 100
db_max_allocated_storage = 500
db_name = "ecommerce_analytics_staging"
db_username = "postgres"
db_backup_retention_period = 7

# ElastiCache Configuration
redis_version = "7.0"
redis_node_type = "cache.t3.medium"
redis_num_cache_nodes = 1

# Domain Configuration
frontend_domain = "staging.yourdomain.com"
api_domain = "staging-api.yourdomain.com"

# Security Configuration
allowed_cidr_blocks = ["0.0.0.0/0"]
enable_waf = false
enable_guard_duty = false

# High Availability
multi_az_deployment = false
enable_auto_scaling = true

# Performance
enable_performance_insights = false
enable_enhanced_monitoring = false

# Monitoring
enable_detailed_monitoring = false
log_retention_days = 14

# Backup
backup_retention_days = 7
backup_schedule = "cron(0 6 * * ? *)"

# Compliance
enable_encryption_at_rest = true
enable_encryption_in_transit = true
enable_audit_logging = false

# Cost Optimization
enable_spot_instances = true
schedule_shutdown = true

# Notifications
notification_email = "<EMAIL>"