#!/usr/bin/env python3
"""
Debug the task parser to see what's happening
"""

import sys
from pathlib import Path
import re

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def debug_parsing():
    test_tasks_file = Path("test-tasks.md")
    
    if not test_tasks_file.exists():
        print(f"❌ File {test_tasks_file} does not exist")
        return
    
    content = test_tasks_file.read_text()
    print(f"📄 File content ({len(content)} chars):")
    print("-" * 50)
    print(content[:500] + "..." if len(content) > 500 else content)
    print("-" * 50)
    
    # Test the regex patterns
    task_pattern = re.compile(r'^## Task \d+: (.+)$', re.MULTILINE)
    field_pattern = re.compile(r'^- \*\*([^*]+)\*\*:\s*(.+)$', re.MULTILINE)
    
    print(f"\n🔍 REGEX TESTING:")
    print(f"Task pattern: {task_pattern.pattern}")
    print(f"Field pattern: {field_pattern.pattern}")
    
    # Find task matches
    task_matches = list(task_pattern.finditer(content))
    print(f"\n📋 Found {len(task_matches)} task matches:")
    
    for i, match in enumerate(task_matches):
        print(f"  {i+1}. '{match.group(1)}' at position {match.start()}-{match.end()}")
    
    # Find field matches  
    field_matches = list(field_pattern.finditer(content))
    print(f"\n🏷️ Found {len(field_matches)} field matches:")
    
    for i, match in enumerate(field_matches[:10]):  # Show first 10
        print(f"  {i+1}. {match.group(1)}: {match.group(2)}")
    
    if len(field_matches) > 10:
        print(f"  ... and {len(field_matches) - 10} more")

if __name__ == '__main__':
    debug_parsing()