name: Continuous Integration

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  NODE_VERSION: '18'
  GO_VERSION: '1.21'
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  code-quality:
    name: Code Quality & Linting
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Run ESLint
        run: npm run lint
        
      - name: Run Prettier check
        run: npm run format:check
        
      - name: Check TypeScript
        run: npm run type-check
        
      - name: Validate package.json files
        run: |
          find . -name "package.json" -not -path "./node_modules/*" | \
          xargs -I {} sh -c 'echo "Validating {}" && npm pkg fix --prefix $(dirname {})'

  unit-tests:
    name: Unit Tests
    runs-on: ubuntu-latest
    strategy:
      matrix:
        service: [dashboard, analytics, integration, error-tracking, admin]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install dependencies
        run: |
          npm ci
          cd testing && npm ci
          
      - name: Run unit tests for ${{ matrix.service }}
        run: |
          cd testing
          npm run test:unit -- --selectProjects "${{ matrix.service }}" --coverage
          
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./testing/coverage/lcov.info
          flags: unit-tests,${{ matrix.service }}
          name: codecov-${{ matrix.service }}

  integration-tests:
    name: Integration Tests
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_PASSWORD: password
          POSTGRES_DB: ecommerce_analytics_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
          
      redis:
        image: redis:6
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install dependencies
        run: |
          npm ci
          cd testing && npm ci
          
      - name: Setup test environment
        run: |
          cp .env.example .env.test
          echo "DB_HOST=localhost" >> .env.test
          echo "DB_PORT=5432" >> .env.test
          echo "REDIS_HOST=localhost" >> .env.test
          echo "REDIS_PORT=6379" >> .env.test
          
      - name: Run database migrations
        run: |
          export $(cat .env.test | grep -v '^#' | xargs)
          npm run migrate:test
          
      - name: Run integration tests
        run: |
          cd testing
          npm run test:integration -- --coverage
          
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./testing/coverage/lcov.info
          flags: integration-tests
          name: codecov-integration

  e2e-tests:
    name: End-to-End Tests
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_PASSWORD: password
          POSTGRES_DB: ecommerce_analytics_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
          
      redis:
        image: redis:6
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install dependencies
        run: |
          npm ci
          cd testing && npm ci
          
      - name: Setup test environment
        run: |
          cp .env.example .env.test
          echo "DB_HOST=localhost" >> .env.test
          echo "REDIS_HOST=localhost" >> .env.test
          
      - name: Start services
        run: |
          npm run build
          npm run start:test &
          sleep 30
          
      - name: Run E2E tests
        run: |
          cd testing
          npm run test:e2e
          
      - name: Upload test artifacts
        if: failure()
        uses: actions/upload-artifact@v3
        with:
          name: e2e-artifacts
          path: |
            testing/screenshots/
            testing/reports/

  docker-build:
    name: Docker Build & Test
    runs-on: ubuntu-latest
    strategy:
      matrix:
        service: [dashboard, analytics, integration, error-tracking, admin]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        
      - name: Build Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./services/${{ matrix.service }}/Dockerfile
          push: false
          tags: ${{ matrix.service }}:test
          cache-from: type=gha
          cache-to: type=gha,mode=max
          
      - name: Test Docker image
        run: |
          docker run --rm --name test-${{ matrix.service }} \
            -e NODE_ENV=test \
            ${{ matrix.service }}:test \
            npm test
            
      - name: Security scan with Trivy
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: ${{ matrix.service }}:test
          format: 'sarif'
          output: 'trivy-results-${{ matrix.service }}.sarif'
          
      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: 'trivy-results-${{ matrix.service }}.sarif'

  performance-tests:
    name: Performance Tests
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_PASSWORD: password
          POSTGRES_DB: ecommerce_analytics_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
          
      redis:
        image: redis:6
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install dependencies
        run: |
          npm ci
          cd testing && npm ci
          
      - name: Setup test environment
        run: |
          cp .env.example .env.test
          echo "DB_HOST=localhost" >> .env.test
          echo "REDIS_HOST=localhost" >> .env.test
          
      - name: Start services
        run: |
          npm run build
          npm run start:test &
          sleep 30
          
      - name: Run performance tests
        run: |
          cd testing
          npm run test:performance
          
      - name: Upload performance results
        uses: actions/upload-artifact@v3
        with:
          name: performance-results
          path: testing/reports/performance-report.json

  security-tests:
    name: Security Scanning
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Run npm audit
        run: npm audit --audit-level moderate
        
      - name: Run security tests
        run: |
          cd testing
          npm ci
          npm run test:security
          
      - name: Initialize CodeQL
        uses: github/codeql-action/init@v2
        with:
          languages: javascript
          
      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v2
        
      - name: Run Semgrep
        uses: returntocorp/semgrep-action@v1
        with:
          config: >-
            p/security-audit
            p/secrets
            p/owasp-top-ten

  dependency-check:
    name: Dependency Analysis
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Check for outdated packages
        run: npm outdated || true
        
      - name: Check for security vulnerabilities
        run: npm audit --audit-level high
        
      - name: Generate dependency tree
        run: npm ls --all > dependency-tree.txt
        
      - name: Upload dependency analysis
        uses: actions/upload-artifact@v3
        with:
          name: dependency-analysis
          path: dependency-tree.txt

  build-status:
    name: Build Status Report
    runs-on: ubuntu-latest
    needs: [code-quality, unit-tests, integration-tests, docker-build, security-tests]
    if: always()
    
    steps:
      - name: Check job results
        run: |
          echo "Code Quality: ${{ needs.code-quality.result }}"
          echo "Unit Tests: ${{ needs.unit-tests.result }}"
          echo "Integration Tests: ${{ needs.integration-tests.result }}"
          echo "Docker Build: ${{ needs.docker-build.result }}"
          echo "Security Tests: ${{ needs.security-tests.result }}"
          
      - name: Report success
        if: >-
          needs.code-quality.result == 'success' &&
          needs.unit-tests.result == 'success' &&
          needs.integration-tests.result == 'success' &&
          needs.docker-build.result == 'success' &&
          needs.security-tests.result == 'success'
        run: |
          echo "✅ All CI checks passed!"
          echo "BUILD_STATUS=success" >> $GITHUB_ENV
          
      - name: Report failure
        if: >-
          needs.code-quality.result == 'failure' ||
          needs.unit-tests.result == 'failure' ||
          needs.integration-tests.result == 'failure' ||
          needs.docker-build.result == 'failure' ||
          needs.security-tests.result == 'failure'
        run: |
          echo "❌ Some CI checks failed!"
          echo "BUILD_STATUS=failure" >> $GITHUB_ENV
          exit 1

  notify-deployment:
    name: Notify Deployment Ready
    runs-on: ubuntu-latest
    needs: [build-status]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main' && needs.build-status.result == 'success'
    
    steps:
      - name: Trigger deployment workflow
        uses: peter-evans/repository-dispatch@v2
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          event-type: deployment-ready
          client-payload: |
            {
              "ref": "${{ github.ref }}",
              "sha": "${{ github.sha }}",
              "environment": "staging"
            }