# Performance Testing Pipeline
# Runs performance tests and benchmarks

name: Performance Testing

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  schedule:
    # Run performance tests weekly on Sundays at 3 AM UTC
    - cron: '0 3 * * 0'

env:
  NODE_VERSION: '18'

jobs:
  # Load testing with Artillery
  load-testing:
    name: Load Testing
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_USER: test
          POSTGRES_PASSWORD: test
          POSTGRES_DB: ecommerce_analytics_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: |
        npm ci --frozen-lockfile
        cd services/analytics && npm ci --frozen-lockfile
        cd ../dashboard && npm ci --frozen-lockfile
        cd ../integration && npm ci --frozen-lockfile

    - name: Setup test environment
      env:
        DB_HOST: localhost
        DB_PORT: 5432
        DB_NAME: ecommerce_analytics_test
        DB_USER: test
        DB_PASSWORD: test
        REDIS_HOST: localhost
        REDIS_PORT: 6379
        NODE_ENV: test
      run: |
        # Run migrations
        cd services/analytics && npm run migrate
        
        # Seed test data
        npm run seed:performance

    - name: Start services
      env:
        DB_HOST: localhost
        DB_PORT: 5432
        DB_NAME: ecommerce_analytics_test
        DB_USER: test
        DB_PASSWORD: test
        REDIS_HOST: localhost
        REDIS_PORT: 6379
        NODE_ENV: test
      run: |
        # Start analytics service
        cd services/analytics
        npm start &
        ANALYTICS_PID=$!
        echo "ANALYTICS_PID=$ANALYTICS_PID" >> $GITHUB_ENV
        
        # Start dashboard service
        cd ../dashboard
        npm start &
        DASHBOARD_PID=$!
        echo "DASHBOARD_PID=$DASHBOARD_PID" >> $GITHUB_ENV
        
        # Wait for services to be ready
        timeout 60 bash -c 'until curl -f http://localhost:3002/health; do sleep 2; done'
        timeout 60 bash -c 'until curl -f http://localhost:3001/health; do sleep 2; done'

    - name: Install Artillery
      run: npm install -g artillery@latest

    - name: Run load tests
      run: |
        # Analytics API load test
        artillery run tests/performance/analytics-load-test.yml --output analytics-report.json
        
        # Dashboard API load test  
        artillery run tests/performance/dashboard-load-test.yml --output dashboard-report.json
        
        # Generate HTML reports
        artillery report analytics-report.json --output analytics-report.html
        artillery report dashboard-report.json --output dashboard-report.html

    - name: Stop services
      if: always()
      run: |
        kill $ANALYTICS_PID || true
        kill $DASHBOARD_PID || true

    - name: Upload performance reports
      uses: actions/upload-artifact@v4
      with:
        name: load-test-reports
        path: |
          *-report.json
          *-report.html

  # Database performance testing
  database-performance:
    name: Database Performance
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_USER: test
          POSTGRES_PASSWORD: test
          POSTGRES_DB: ecommerce_analytics_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: |
        cd services/analytics
        npm ci --frozen-lockfile

    - name: Setup database
      env:
        DB_HOST: localhost
        DB_PORT: 5432
        DB_NAME: ecommerce_analytics_test
        DB_USER: test
        DB_PASSWORD: test
        NODE_ENV: test
      run: |
        cd services/analytics
        npm run migrate
        npm run seed:performance

    - name: Run database performance tests
      env:
        DB_HOST: localhost
        DB_PORT: 5432
        DB_NAME: ecommerce_analytics_test
        DB_USER: test
        DB_PASSWORD: test
        NODE_ENV: test
      run: |
        cd services/analytics
        npm run test:db-performance

    - name: Upload database performance results
      uses: actions/upload-artifact@v4
      with:
        name: database-performance-results
        path: services/analytics/performance-results.json

  # Memory and CPU profiling
  profiling:
    name: Memory & CPU Profiling
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_USER: test
          POSTGRES_PASSWORD: test
          POSTGRES_DB: ecommerce_analytics_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies and profiling tools
      run: |
        cd services/analytics
        npm ci --frozen-lockfile
        npm install -g clinic autocannon

    - name: Setup test environment
      env:
        DB_HOST: localhost
        DB_PORT: 5432
        DB_NAME: ecommerce_analytics_test
        DB_USER: test
        DB_PASSWORD: test
        REDIS_HOST: localhost
        REDIS_PORT: 6379
        NODE_ENV: test
      run: |
        cd services/analytics
        npm run migrate
        npm run seed:performance

    - name: CPU profiling
      env:
        DB_HOST: localhost
        DB_PORT: 5432
        DB_NAME: ecommerce_analytics_test
        DB_USER: test
        DB_PASSWORD: test
        REDIS_HOST: localhost
        REDIS_PORT: 6379
        NODE_ENV: test
      run: |
        cd services/analytics
        
        # Start service with CPU profiling
        clinic doctor --on-port 'autocannon -c 10 -d 30 http://localhost:3002/api/analytics/dashboard' -- node src/server.js
        
        # Move profiling results
        mv .clinic ./cpu-profile

    - name: Memory profiling
      env:
        DB_HOST: localhost
        DB_PORT: 5432
        DB_NAME: ecommerce_analytics_test
        DB_USER: test
        DB_PASSWORD: test
        REDIS_HOST: localhost
        REDIS_PORT: 6379
        NODE_ENV: test
      run: |
        cd services/analytics
        
        # Start service with memory profiling
        clinic heapprofiler --on-port 'autocannon -c 10 -d 30 http://localhost:3002/api/analytics/cohorts' -- node src/server.js
        
        # Move profiling results
        mv .clinic ./memory-profile

    - name: Upload profiling results
      uses: actions/upload-artifact@v4
      with:
        name: profiling-results
        path: |
          services/analytics/cpu-profile/
          services/analytics/memory-profile/

  # Frontend performance testing
  frontend-performance:
    name: Frontend Performance
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json

    - name: Install dependencies
      run: |
        cd frontend
        npm ci --frozen-lockfile

    - name: Build frontend
      run: |
        cd frontend
        npm run build

    - name: Install Lighthouse CI
      run: npm install -g @lhci/cli@0.12.x

    - name: Serve built frontend
      run: |
        cd frontend
        npx serve -s dist -l 3000 &
        sleep 5

    - name: Run Lighthouse CI
      run: |
        lhci autorun --upload.target=temporary-public-storage
      env:
        LHCI_BUILD_CONTEXT__CURRENT_HASH: ${{ github.sha }}
        LHCI_BUILD_CONTEXT__COMMIT_TIME: ${{ github.event.head_commit.timestamp }}

    - name: Bundle size analysis
      run: |
        cd frontend
        npm install -g webpack-bundle-analyzer
        
        # Analyze bundle
        npx webpack-bundle-analyzer dist/static/js/*.js --report --mode static --open-analyzer false

    - name: Upload frontend performance results
      uses: actions/upload-artifact@v4
      with:
        name: frontend-performance-results
        path: |
          frontend/report.html
          .lighthouseci/

  # API response time benchmarks
  api-benchmarks:
    name: API Response Time Benchmarks
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_USER: test
          POSTGRES_PASSWORD: test
          POSTGRES_DB: ecommerce_analytics_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: |
        cd services/analytics
        npm ci --frozen-lockfile

    - name: Setup test environment
      env:
        DB_HOST: localhost
        DB_PORT: 5432
        DB_NAME: ecommerce_analytics_test
        DB_USER: test
        DB_PASSWORD: test
        REDIS_HOST: localhost
        REDIS_PORT: 6379
        NODE_ENV: test
      run: |
        cd services/analytics
        npm run migrate
        npm run seed:performance

    - name: Start analytics service
      env:
        DB_HOST: localhost
        DB_PORT: 5432
        DB_NAME: ecommerce_analytics_test
        DB_USER: test
        DB_PASSWORD: test
        REDIS_HOST: localhost
        REDIS_PORT: 6379
        NODE_ENV: test
      run: |
        cd services/analytics
        npm start &
        timeout 60 bash -c 'until curl -f http://localhost:3002/health; do sleep 2; done'

    - name: Install hyperfine for benchmarking
      run: |
        wget https://github.com/sharkdp/hyperfine/releases/download/v1.18.0/hyperfine_1.18.0_amd64.deb
        sudo dpkg -i hyperfine_1.18.0_amd64.deb

    - name: Run API benchmarks
      run: |
        echo "# API Response Time Benchmarks" > benchmark-results.md
        echo "Generated: $(date)" >> benchmark-results.md
        echo "" >> benchmark-results.md
        
        # Dashboard endpoint
        echo "## Dashboard API" >> benchmark-results.md
        hyperfine --warmup 3 --runs 50 'curl -s http://localhost:3002/api/analytics/dashboard?timeRange=last_30_days' --export-markdown dashboard-bench.md
        cat dashboard-bench.md >> benchmark-results.md
        echo "" >> benchmark-results.md
        
        # Cohort analysis endpoint
        echo "## Cohort Analysis API" >> benchmark-results.md
        hyperfine --warmup 3 --runs 30 'curl -s http://localhost:3002/api/analytics/cohorts?period=monthly' --export-markdown cohort-bench.md
        cat cohort-bench.md >> benchmark-results.md
        echo "" >> benchmark-results.md
        
        # Attribution endpoint
        echo "## Attribution API" >> benchmark-results.md
        hyperfine --warmup 3 --runs 30 'curl -s http://localhost:3002/api/analytics/attribution?model=last_touch' --export-markdown attribution-bench.md
        cat attribution-bench.md >> benchmark-results.md

    - name: Upload benchmark results
      uses: actions/upload-artifact@v4
      with:
        name: api-benchmark-results
        path: |
          benchmark-results.md
          *-bench.md

  # Performance regression detection
  performance-regression:
    name: Performance Regression Detection
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    needs: [load-testing, api-benchmarks]

    steps:
    - name: Download benchmark artifacts
      uses: actions/download-artifact@v4
      with:
        name: api-benchmark-results

    - name: Download load test artifacts
      uses: actions/download-artifact@v4
      with:
        name: load-test-reports

    - name: Compare with baseline
      run: |
        # This would compare current performance with main branch baseline
        # For now, we'll create a simple analysis
        
        echo "# Performance Regression Analysis" > regression-report.md
        echo "Generated: $(date)" >> regression-report.md
        echo "PR: #${{ github.event.number }}" >> regression-report.md
        echo "" >> regression-report.md
        
        # Analyze load test results
        if [ -f "analytics-report.json" ]; then
          ANALYTICS_P95=$(jq -r '.aggregate.summaries.latency.p95' analytics-report.json)
          echo "Analytics API P95 latency: ${ANALYTICS_P95}ms" >> regression-report.md
          
          # Simple threshold check (in production, this would compare with baseline)
          if (( $(echo "$ANALYTICS_P95 > 1000" | bc -l) )); then
            echo "⚠️ Analytics API P95 latency exceeds threshold (1000ms)" >> regression-report.md
          fi
        fi
        
        if [ -f "dashboard-report.json" ]; then
          DASHBOARD_P95=$(jq -r '.aggregate.summaries.latency.p95' dashboard-report.json)
          echo "Dashboard API P95 latency: ${DASHBOARD_P95}ms" >> regression-report.md
          
          if (( $(echo "$DASHBOARD_P95 > 800" | bc -l) )); then
            echo "⚠️ Dashboard API P95 latency exceeds threshold (800ms)" >> regression-report.md
          fi
        fi

    - name: Comment performance results on PR
      uses: actions/github-script@v7
      with:
        script: |
          const fs = require('fs');
          const report = fs.readFileSync('regression-report.md', 'utf8');
          
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: '## Performance Test Results\n\n' + report
          });

  # Performance monitoring
  performance-monitoring:
    name: Performance Monitoring
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    needs: [load-testing, api-benchmarks, database-performance]

    steps:
    - name: Download all performance artifacts
      uses: actions/download-artifact@v4

    - name: Send metrics to monitoring system
      run: |
        # In a real implementation, this would send metrics to:
        # - DataDog
        # - New Relic
        # - Prometheus/Grafana
        # - CloudWatch
        
        echo "📊 Performance metrics collected and ready for monitoring system"
        echo "Artifacts available:"
        ls -la
        
        # Example: Send to DataDog (would need API key)
        # curl -X POST "https://api.datadoghq.com/api/v1/metrics" \
        #   -H "Content-Type: application/json" \
        #   -H "DD-API-KEY: $DD_API_KEY" \
        #   -d @metrics.json

    - name: Archive performance history
      run: |
        # Archive performance results for trend analysis
        mkdir -p performance-history/$(date +%Y-%m-%d)
        cp -r . performance-history/$(date +%Y-%m-%d)/
        
        echo "📈 Performance results archived for historical analysis"