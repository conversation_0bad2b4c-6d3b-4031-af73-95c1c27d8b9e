name: Security Scanning

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # Run security scans daily at 2 AM UTC
    - cron: '0 2 * * *'

env:
  NODE_VERSION: '18'

jobs:
  dependency-security:
    name: Dependency Security Scan
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install dependencies
        run: |
          npm ci
          cd testing && npm ci
          
      - name: Run npm audit
        run: |
          # Check root dependencies
          npm audit --audit-level moderate --output json > audit-root.json || true
          
          # Check testing dependencies
          cd testing
          npm audit --audit-level moderate --output json > ../audit-testing.json || true
          cd ..
          
          # Check each service
          for service in dashboard analytics integration error-tracking admin; do
            if [ -d "services/$service" ]; then
              cd "services/$service"
              npm audit --audit-level moderate --output json > "../../audit-$service.json" || true
              cd ../..
            fi
          done
          
      - name: Upload audit results
        uses: actions/upload-artifact@v3
        with:
          name: npm-audit-results
          path: audit-*.json
          
      - name: Run Snyk security scan
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --severity-threshold=high --file=package.json
          
      - name: Upload Snyk results
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: snyk.sarif

  codeql-analysis:
    name: CodeQL Analysis
    runs-on: ubuntu-latest
    permissions:
      actions: read
      contents: read
      security-events: write
    
    strategy:
      fail-fast: false
      matrix:
        language: [ 'javascript' ]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Initialize CodeQL
        uses: github/codeql-action/init@v2
        with:
          languages: ${{ matrix.language }}
          queries: security-extended,security-and-quality
          
      - name: Autobuild
        uses: github/codeql-action/autobuild@v2
        
      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v2
        with:
          category: "/language:${{matrix.language}}"

  semgrep-scan:
    name: Semgrep Security Scan
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Run Semgrep
        uses: returntocorp/semgrep-action@v1
        with:
          config: >-
            p/security-audit
            p/secrets
            p/owasp-top-ten
            p/javascript
            p/typescript
            p/docker
          generateSarif: "1"
          
      - name: Upload Semgrep results
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: semgrep.sarif

  docker-security:
    name: Docker Security Scan
    runs-on: ubuntu-latest
    if: github.event_name != 'schedule'
    strategy:
      matrix:
        service: [dashboard, analytics, integration, error-tracking, admin]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        
      - name: Build Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./services/${{ matrix.service }}/Dockerfile
          push: false
          tags: ${{ matrix.service }}:security-scan
          cache-from: type=gha
          
      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: ${{ matrix.service }}:security-scan
          format: 'sarif'
          output: 'trivy-${{ matrix.service }}.sarif'
          
      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: 'trivy-${{ matrix.service }}.sarif'
          
      - name: Run Snyk Docker scan
        continue-on-error: true
        run: |
          docker run --rm -v /var/run/docker.sock:/var/run/docker.sock \
            -v $(pwd):/project \
            snyk/snyk:docker test ${{ matrix.service }}:security-scan \
            --file=./services/${{ matrix.service }}/Dockerfile \
            --severity-threshold=high

  secret-scan:
    name: Secret Detection
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          
      - name: TruffleHog OSS scan
        uses: trufflesecurity/trufflehog@main
        with:
          path: ./
          base: main
          head: HEAD
          extra_args: --debug --only-verified
          
      - name: GitLeaks scan
        uses: gitleaks/gitleaks-action@v2
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          GITLEAKS_LICENSE: ${{ secrets.GITLEAKS_LICENSE }}

  infrastructure-scan:
    name: Infrastructure Security
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Run Checkov
        uses: bridgecrewio/checkov-action@master
        with:
          directory: .
          framework: dockerfile,kubernetes,terraform
          output_format: sarif
          output_file_path: checkov-results.sarif
          
      - name: Upload Checkov results
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: checkov-results.sarif
          
      - name: Run Kics
        uses: checkmarx/kics-github-action@v1.7.0
        with:
          path: .
          output_path: kics-results
          output_formats: 'sarif'
          
      - name: Upload Kics results
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: kics-results/results.sarif

  security-tests:
    name: Security Tests
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_PASSWORD: password
          POSTGRES_DB: ecommerce_analytics_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
          
      redis:
        image: redis:6
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install dependencies
        run: |
          npm ci
          cd testing && npm ci
          
      - name: Setup test environment
        run: |
          cp .env.example .env.test
          echo "DB_HOST=localhost" >> .env.test
          echo "REDIS_HOST=localhost" >> .env.test
          
      - name: Run security tests
        run: |
          cd testing
          npm run test:security
          
      - name: Upload security test results
        uses: actions/upload-artifact@v3
        with:
          name: security-test-results
          path: testing/reports/security-*.json

  compliance-check:
    name: Compliance Check
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Check GDPR compliance
        run: |
          # Check for GDPR-related data handling
          echo "Checking GDPR compliance..."
          
          # Look for data collection patterns
          grep -r "personal.*data\|email\|phone\|address" services/ || true
          
          # Check for privacy policy references
          grep -r "privacy.*policy\|data.*protection" . || true
          
      - name: Check security headers
        run: |
          # Check for security header implementations
          echo "Checking security headers..."
          
          grep -r "helmet\|csp\|hsts\|x-frame-options" services/ || true
          
      - name: Check authentication patterns
        run: |
          # Check for secure authentication
          echo "Checking authentication patterns..."
          
          grep -r "jwt\|bcrypt\|password.*hash" services/ || true
          grep -r "session\|cookie.*secure" services/ || true
          
      - name: Generate compliance report
        run: |
          cat > compliance-report.md << EOF
          # Security Compliance Report
          
          Generated on: $(date)
          
          ## Security Headers
          - [ ] Helmet.js implementation
          - [ ] Content Security Policy
          - [ ] HSTS headers
          - [ ] X-Frame-Options
          
          ## Authentication
          - [ ] JWT implementation
          - [ ] Password hashing (bcrypt)
          - [ ] Secure session management
          
          ## Data Protection
          - [ ] GDPR compliance
          - [ ] Data encryption at rest
          - [ ] Data encryption in transit
          - [ ] Privacy policy implementation
          
          ## Infrastructure
          - [ ] Docker security
          - [ ] Kubernetes security
          - [ ] Network security
          EOF
          
      - name: Upload compliance report
        uses: actions/upload-artifact@v3
        with:
          name: compliance-report
          path: compliance-report.md

  security-summary:
    name: Security Summary
    runs-on: ubuntu-latest
    needs: [dependency-security, codeql-analysis, semgrep-scan, secret-scan, security-tests]
    if: always()
    
    steps:
      - name: Download all artifacts
        uses: actions/download-artifact@v3
        
      - name: Generate security summary
        run: |
          echo "# Security Scan Summary" > security-summary.md
          echo "" >> security-summary.md
          echo "Scan completed on: $(date)" >> security-summary.md
          echo "" >> security-summary.md
          
          echo "## Scan Results" >> security-summary.md
          echo "- Dependency Security: ${{ needs.dependency-security.result }}" >> security-summary.md
          echo "- CodeQL Analysis: ${{ needs.codeql-analysis.result }}" >> security-summary.md
          echo "- Semgrep Scan: ${{ needs.semgrep-scan.result }}" >> security-summary.md
          echo "- Secret Detection: ${{ needs.secret-scan.result }}" >> security-summary.md
          echo "- Security Tests: ${{ needs.security-tests.result }}" >> security-summary.md
          echo "" >> security-summary.md
          
          # Count findings if artifacts exist
          if [ -d "npm-audit-results" ]; then
            echo "## Dependency Vulnerabilities" >> security-summary.md
            find npm-audit-results -name "*.json" -exec echo "- {}" \; >> security-summary.md
          fi
          
          echo "" >> security-summary.md
          echo "## Recommendations" >> security-summary.md
          echo "- Review all HIGH and CRITICAL findings" >> security-summary.md
          echo "- Update dependencies with known vulnerabilities" >> security-summary.md
          echo "- Address any secrets detected in code" >> security-summary.md
          echo "- Implement missing security controls" >> security-summary.md
          
      - name: Upload security summary
        uses: actions/upload-artifact@v3
        with:
          name: security-summary
          path: security-summary.md
          
      - name: Comment on PR
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            const summary = fs.readFileSync('security-summary.md', 'utf8');
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: `## 🔒 Security Scan Results\n\n${summary}`
            })