name: Continuous Deployment

on:
  repository_dispatch:
    types: [deployment-ready]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Deployment environment'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production
      ref:
        description: 'Git ref to deploy'
        required: true
        default: 'main'

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  build-and-push:
    name: Build & Push Images
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
    
    outputs:
      image-tag: ${{ steps.meta.outputs.tags }}
      image-digest: ${{ steps.build.outputs.digest }}
    
    strategy:
      matrix:
        service: [dashboard, analytics, integration, error-tracking, admin]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.client_payload.ref || github.event.inputs.ref || 'main' }}
          
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        
      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
          
      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-${{ matrix.service }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix=git-
            type=raw,value=latest,enable={{is_default_branch}}
            
      - name: Build and push Docker image
        id: build
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./services/${{ matrix.service }}/Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          
      - name: Generate SBOM
        uses: anchore/sbom-action@v0
        with:
          image: ${{ steps.meta.outputs.tags }}
          format: spdx-json
          output-file: sbom-${{ matrix.service }}.spdx.json
          
      - name: Upload SBOM
        uses: actions/upload-artifact@v3
        with:
          name: sbom-${{ matrix.service }}
          path: sbom-${{ matrix.service }}.spdx.json

  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [build-and-push]
    if: |
      (github.event_name == 'repository_dispatch' && github.event.client_payload.environment == 'staging') ||
      (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'staging')
    
    environment:
      name: staging
      url: https://staging.your-domain.com
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'v1.28.0'
          
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1
          
      - name: Update kubeconfig
        run: aws eks update-kubeconfig --name staging-cluster --region us-east-1
        
      - name: Deploy to staging
        run: |
          # Update image tags in deployment manifests
          for service in dashboard analytics integration error-tracking admin; do
            sed -i "s|image: .*/$service:.*|image: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-$service:git-${{ github.sha }}|g" \
              k8s/overlays/staging/$service-deployment.yaml
          done
          
          # Apply Kubernetes manifests
          kubectl apply -k k8s/overlays/staging/
          
          # Wait for rollout to complete
          for service in dashboard analytics integration error-tracking admin; do
            kubectl rollout status deployment/$service -n staging --timeout=600s
          done
          
      - name: Run smoke tests
        run: |
          # Wait for services to be ready
          sleep 60
          
          # Run smoke tests against staging
          cd testing
          export TEST_BASE_URL=https://staging-api.your-domain.com
          npm run test:smoke
          
      - name: Notify deployment success
        if: success()
        uses: 8398a7/action-slack@v3
        with:
          status: success
          text: '🚀 Staging deployment successful!'
          webhook_url: ${{ secrets.SLACK_WEBHOOK_URL }}
          
      - name: Notify deployment failure
        if: failure()
        uses: 8398a7/action-slack@v3
        with:
          status: failure
          text: '❌ Staging deployment failed!'
          webhook_url: ${{ secrets.SLACK_WEBHOOK_URL }}

  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [build-and-push]
    if: github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'production'
    
    environment:
      name: production
      url: https://app.your-domain.com
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'v1.28.0'
          
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1
          
      - name: Update kubeconfig
        run: aws eks update-kubeconfig --name production-cluster --region us-east-1
        
      - name: Create backup
        run: |
          # Create database backup before deployment
          kubectl create job backup-$(date +%Y%m%d-%H%M%S) \
            --from=cronjob/database-backup -n production
            
      - name: Blue-green deployment
        run: |
          # Deploy to green environment
          for service in dashboard analytics integration error-tracking admin; do
            sed -i "s|image: .*/$service:.*|image: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-$service:git-${{ github.sha }}|g" \
              k8s/overlays/production-green/$service-deployment.yaml
          done
          
          kubectl apply -k k8s/overlays/production-green/
          
          # Wait for green deployment to be ready
          for service in dashboard analytics integration error-tracking admin; do
            kubectl rollout status deployment/$service-green -n production --timeout=600s
          done
          
      - name: Run production smoke tests
        run: |
          # Test green environment
          cd testing
          export TEST_BASE_URL=https://green-api.your-domain.com
          npm run test:smoke
          
      - name: Switch traffic to green
        run: |
          # Update service selectors to point to green
          kubectl patch service dashboard-service -n production \
            -p '{"spec":{"selector":{"version":"green"}}}'
          kubectl patch service analytics-service -n production \
            -p '{"spec":{"selector":{"version":"green"}}}'
          kubectl patch service integration-service -n production \
            -p '{"spec":{"selector":{"version":"green"}}}'
          kubectl patch service error-tracking-service -n production \
            -p '{"spec":{"selector":{"version":"green"}}}'
          kubectl patch service admin-service -n production \
            -p '{"spec":{"selector":{"version":"green"}}}'
            
      - name: Monitor deployment
        run: |
          # Monitor for 5 minutes after traffic switch
          sleep 300
          
          # Check error rates and response times
          cd testing
          export TEST_BASE_URL=https://api.your-domain.com
          npm run test:smoke
          
      - name: Cleanup old deployment
        if: success()
        run: |
          # Remove blue deployment
          kubectl delete -k k8s/overlays/production-blue/ || true
          
          # Rename green to blue for next deployment
          for service in dashboard analytics integration error-tracking admin; do
            kubectl patch deployment $service-green -n production \
              -p '{"metadata":{"name":"'$service'-blue"}}'
            kubectl patch deployment $service-green -n production \
              -p '{"spec":{"selector":{"matchLabels":{"version":"blue"}}}}'
            kubectl patch deployment $service-green -n production \
              -p '{"spec":{"template":{"metadata":{"labels":{"version":"blue"}}}}}'
          done
          
      - name: Rollback on failure
        if: failure()
        run: |
          echo "Production deployment failed, rolling back..."
          
          # Switch traffic back to blue
          kubectl patch service dashboard-service -n production \
            -p '{"spec":{"selector":{"version":"blue"}}}'
          kubectl patch service analytics-service -n production \
            -p '{"spec":{"selector":{"version":"blue"}}}'
          kubectl patch service integration-service -n production \
            -p '{"spec":{"selector":{"version":"blue"}}}'
          kubectl patch service error-tracking-service -n production \
            -p '{"spec":{"selector":{"version":"blue"}}}'
          kubectl patch service admin-service -n production \
            -p '{"spec":{"selector":{"version":"blue"}}}'
          
          # Remove failed green deployment
          kubectl delete -k k8s/overlays/production-green/
          
      - name: Notify production success
        if: success()
        uses: 8398a7/action-slack@v3
        with:
          status: success
          text: '🎉 Production deployment successful!'
          webhook_url: ${{ secrets.SLACK_WEBHOOK_URL }}
          
      - name: Notify production failure
        if: failure()
        uses: 8398a7/action-slack@v3
        with:
          status: failure
          text: '🚨 Production deployment failed and rolled back!'
          webhook_url: ${{ secrets.SLACK_WEBHOOK_URL }}

  post-deployment:
    name: Post-Deployment Tasks
    runs-on: ubuntu-latest
    needs: [deploy-staging, deploy-production]
    if: always() && (needs.deploy-staging.result == 'success' || needs.deploy-production.result == 'success')
    
    steps:
      - name: Update deployment status
        run: |
          if [ "${{ needs.deploy-production.result }}" == "success" ]; then
            echo "Production deployment completed successfully"
            ENVIRONMENT="production"
          elif [ "${{ needs.deploy-staging.result }}" == "success" ]; then
            echo "Staging deployment completed successfully"
            ENVIRONMENT="staging"
          fi
          
          # Record deployment in monitoring system
          curl -X POST "${{ secrets.MONITORING_WEBHOOK_URL }}" \
            -H "Content-Type: application/json" \
            -d '{
              "deployment": {
                "environment": "'$ENVIRONMENT'",
                "version": "${{ github.sha }}",
                "timestamp": "'$(date -u +%Y-%m-%dT%H:%M:%SZ)'",
                "status": "success"
              }
            }'
            
      - name: Generate deployment report
        run: |
          cat > deployment-report.md << EOF
          # Deployment Report
          
          **Environment:** ${{ github.event.client_payload.environment || github.event.inputs.environment }}
          **Git SHA:** ${{ github.sha }}
          **Deployed At:** $(date -u +%Y-%m-%dT%H:%M:%SZ)
          **Deployed By:** ${{ github.actor }}
          
          ## Services Deployed
          - Dashboard API
          - Analytics Service  
          - Integration Service
          - Error Tracking Service
          - Admin Service
          
          ## Deployment Status
          ${{ needs.deploy-staging.result == 'success' && '✅ Staging: Success' || '' }}
          ${{ needs.deploy-production.result == 'success' && '✅ Production: Success' || '' }}
          EOF
          
      - name: Upload deployment report
        uses: actions/upload-artifact@v3
        with:
          name: deployment-report
          path: deployment-report.md