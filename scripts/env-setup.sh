#!/bin/bash

# Environment setup script for e-commerce analytics platform
# Handles environment file creation and validation

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# Function to show usage
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS] [COMMAND]

Environment setup and validation for e-commerce analytics platform.

COMMANDS:
    init            Create .env file from template
    validate        Validate environment configuration
    generate        Generate secure random values
    backup          Backup current environment files
    restore         Restore environment files from backup

OPTIONS:
    -e, --env ENV   Environment: development, production (default: development)
    -f, --force     Force overwrite existing files
    -v, --verbose   Verbose output
    -h, --help      Show this help message

EXAMPLES:
    $0 init                     # Create development .env file
    $0 init -e production       # Create production .env file
    $0 validate                 # Validate current environment
    $0 generate                 # Generate secure random values
    $0 backup                   # Backup environment files

SECURITY:
    - Never commit .env files to version control
    - Use strong passwords and secrets in production
    - Regularly rotate secrets and passwords
EOF
}

# Default values
ENVIRONMENT="development"
COMMAND="init"
FORCE=false
VERBOSE=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--env)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -f|--force)
            FORCE=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        init|validate|generate|backup|restore)
            COMMAND="$1"
            shift
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate environment
if [[ "$ENVIRONMENT" != "development" && "$ENVIRONMENT" != "production" ]]; then
    print_error "Invalid environment: $ENVIRONMENT. Must be 'development' or 'production'"
    exit 1
fi

# Function to generate secure random string
generate_random() {
    local length=${1:-32}
    openssl rand -base64 $length | tr -d "=+/" | cut -c1-$length
}

# Function to generate secure password
generate_password() {
    local length=${1:-24}
    openssl rand -base64 $length | tr -d "=+/" | cut -c1-$length
}

# Function to backup environment files
backup_env_files() {
    local backup_dir="./backups/env/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    print_status "Creating backup in $backup_dir"
    
    if [[ -f ".env" ]]; then
        cp ".env" "$backup_dir/.env"
        print_status "Backed up .env"
    fi
    
    if [[ -f ".env.development" ]]; then
        cp ".env.development" "$backup_dir/.env.development"
        print_status "Backed up .env.development"
    fi
    
    if [[ -f ".env.production" ]]; then
        cp ".env.production" "$backup_dir/.env.production"
        print_status "Backed up .env.production"
    fi
    
    print_status "Backup completed: $backup_dir"
}

# Function to validate environment configuration
validate_env() {
    local env_file=".env"
    if [[ ! -f "$env_file" ]]; then
        print_error "Environment file $env_file not found"
        return 1
    fi
    
    print_status "Validating environment configuration..."
    
    # Required variables
    local required_vars=(
        "NODE_ENV"
        "DB_PASSWORD"
        "JWT_SECRET"
        "DB_HOST"
        "DB_PORT"
        "DB_NAME"
        "DB_USER"
        "REDIS_HOST"
        "REDIS_PORT"
    )
    
    local missing_vars=()
    local weak_secrets=()
    
    # Source the environment file
    set -a
    source "$env_file"
    set +a
    
    # Check required variables
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var}" ]]; then
            missing_vars+=("$var")
        fi
    done
    
    # Check for weak secrets
    if [[ ${#JWT_SECRET} -lt 32 ]]; then
        weak_secrets+=("JWT_SECRET (too short, minimum 32 characters)")
    fi
    
    if [[ "$JWT_SECRET" == *"change"* || "$JWT_SECRET" == *"secret"* || "$JWT_SECRET" == *"dev"* ]]; then
        weak_secrets+=("JWT_SECRET (contains common words)")
    fi
    
    if [[ ${#DB_PASSWORD} -lt 12 ]]; then
        weak_secrets+=("DB_PASSWORD (too short, minimum 12 characters)")
    fi
    
    if [[ "$DB_PASSWORD" == "password" || "$DB_PASSWORD" == "admin" ]]; then
        weak_secrets+=("DB_PASSWORD (using common password)")
    fi
    
    # Production-specific checks
    if [[ "$NODE_ENV" == "production" ]]; then
        if [[ "$CORS_ORIGIN" == "*" ]]; then
            weak_secrets+=("CORS_ORIGIN (allows all origins in production)")
        fi
        
        if [[ -z "$SENTRY_DSN" ]]; then
            print_warning "SENTRY_DSN not set for production environment"
        fi
    fi
    
    # Report results
    if [[ ${#missing_vars[@]} -gt 0 ]]; then
        print_error "Missing required variables:"
        for var in "${missing_vars[@]}"; do
            echo "  - $var"
        done
    fi
    
    if [[ ${#weak_secrets[@]} -gt 0 ]]; then
        print_error "Security issues found:"
        for issue in "${weak_secrets[@]}"; do
            echo "  - $issue"
        done
    fi
    
    if [[ ${#missing_vars[@]} -eq 0 && ${#weak_secrets[@]} -eq 0 ]]; then
        print_status "✓ Environment configuration is valid"
        return 0
    else
        print_error "✗ Environment configuration has issues"
        return 1
    fi
}

# Function to create environment file
create_env_file() {
    local target_file=".env"
    local template_file=""
    
    if [[ "$ENVIRONMENT" == "development" ]]; then
        template_file=".env.development"
    else
        template_file=".env.production"
    fi
    
    # Check if target file exists
    if [[ -f "$target_file" && "$FORCE" != "true" ]]; then
        print_warning "$target_file already exists. Use --force to overwrite."
        return 1
    fi
    
    # Copy from template
    if [[ -f "$template_file" ]]; then
        cp "$template_file" "$target_file"
        print_status "Created $target_file from $template_file"
    elif [[ -f ".env.example" ]]; then
        cp ".env.example" "$target_file"
        print_status "Created $target_file from .env.example"
    else
        print_error "No template file found (.env.example or $template_file)"
        return 1
    fi
    
    # Generate secure values if needed
    if [[ "$ENVIRONMENT" == "production" ]]; then
        print_status "Generating secure random values for production..."
        
        # Generate JWT secret
        local jwt_secret=$(generate_random 64)
        sed -i "s/your_very_secure_jwt_secret_minimum_32_characters_long/$jwt_secret/g" "$target_file"
        
        # Generate database password
        local db_password=$(generate_password 24)
        sed -i "s/your_secure_postgres_password_here/$db_password/g" "$target_file"
        
        # Generate Redis password
        local redis_password=$(generate_password 24)
        sed -i "s/your_secure_redis_password_here/$redis_password/g" "$target_file"
        
        print_status "✓ Secure values generated"
        print_warning "IMPORTANT: Update other placeholder values before deployment!"
    fi
    
    print_status "Environment file created successfully"
    print_status "Next steps:"
    echo "  1. Review and update the configuration in $target_file"
    echo "  2. Set up your database and Redis credentials"
    echo "  3. Configure third-party integration keys"
    echo "  4. Run: $0 validate"
}

# Function to generate and display secure values
generate_secure_values() {
    print_status "Generating secure random values..."
    echo
    echo "JWT Secret (64 chars):"
    echo "  $(generate_random 64)"
    echo
    echo "Database Password (24 chars):"
    echo "  $(generate_password 24)"
    echo
    echo "Redis Password (24 chars):"
    echo "  $(generate_password 24)"
    echo
    echo "Session Secret (32 chars):"
    echo "  $(generate_random 32)"
    echo
    echo "Webhook Secret (32 chars):"
    echo "  $(generate_random 32)"
    echo
    print_warning "Save these values securely and update your .env file"
}

# Main command handling
case $COMMAND in
    "init")
        print_status "Initializing environment for: $ENVIRONMENT"
        create_env_file
        ;;
        
    "validate")
        validate_env
        ;;
        
    "generate")
        generate_secure_values
        ;;
        
    "backup")
        backup_env_files
        ;;
        
    "restore")
        print_error "Restore functionality not yet implemented"
        exit 1
        ;;
        
    *)
        print_error "Unknown command: $COMMAND"
        show_usage
        exit 1
        ;;
esac