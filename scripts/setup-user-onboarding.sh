#!/bin/bash

# User Onboarding Setup Script for E-commerce Analytics Platform
# Creates initial user accounts, demo organizations, and onboarding flows

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Default values
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD=""
DEMO_USER_EMAIL="<EMAIL>"
DEMO_USER_PASSWORD=""
ORGANIZATION_NAME="Demo E-commerce Store"
DRY_RUN=false
RESET_USERS=false
ENVIRONMENT="development"

# Database connection
DB_HOST=${DB_HOST:-localhost}
DB_PORT=${DB_PORT:-5432}
DB_NAME=${DB_NAME:-ecommerce_analytics}
DB_USER=${DB_USER:-postgres}

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# Function to show usage
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Set up user onboarding for the e-commerce analytics platform.

OPTIONS:
    --admin-email EMAIL       Admin user email (default: <EMAIL>)
    --admin-password PWD      Admin user password (will prompt if not provided)
    --demo-email EMAIL        Demo user email (default: <EMAIL>)
    --demo-password PWD       Demo user password (will prompt if not provided)
    --org-name NAME           Demo organization name (default: Demo E-commerce Store)
    -e, --environment ENV     Environment: development, staging, production (default: development)
    --reset                   Reset existing users and create new ones
    -n, --dry-run             Show what would be created without making changes
    -h, --help                Show this help message

EXAMPLES:
    $0                                    # Create default admin and demo users
    $0 --admin-email <EMAIL>    # Create with custom admin email
    $0 --reset                           # Reset existing users and recreate
    $0 -n                                # Dry run to see what would be created

FEATURES:
    - Creates admin and demo user accounts
    - Sets up demo organization
    - Configures user roles and permissions
    - Creates onboarding tutorial data
    - Sets up API keys for integrations
EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --admin-email)
            ADMIN_EMAIL="$2"
            shift 2
            ;;
        --admin-password)
            ADMIN_PASSWORD="$2"
            shift 2
            ;;
        --demo-email)
            DEMO_USER_EMAIL="$2"
            shift 2
            ;;
        --demo-password)
            DEMO_USER_PASSWORD="$2"
            shift 2
            ;;
        --org-name)
            ORGANIZATION_NAME="$2"
            shift 2
            ;;
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        --reset)
            RESET_USERS=true
            shift
            ;;
        -n|--dry-run)
            DRY_RUN=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate environment
if [[ "$ENVIRONMENT" != "development" && "$ENVIRONMENT" != "staging" && "$ENVIRONMENT" != "production" ]]; then
    print_error "Invalid environment: $ENVIRONMENT"
    exit 1
fi

if [[ -z "$DB_PASSWORD" ]]; then
    print_error "DB_PASSWORD environment variable is required"
    exit 1
fi

print_status "User Onboarding Setup for E-commerce Analytics Platform"
print_status "Environment: $ENVIRONMENT"
print_status "Admin Email: $ADMIN_EMAIL"
print_status "Demo Email: $DEMO_USER_EMAIL"
print_status "Organization: $ORGANIZATION_NAME"
print_status "Dry Run: $DRY_RUN"

# Function to generate secure password
generate_password() {
    local length=${1:-16}
    openssl rand -base64 $length | tr -d "=+/" | cut -c1-$length
}

# Function to prompt for passwords
prompt_for_passwords() {
    if [[ -z "$ADMIN_PASSWORD" ]]; then
        if [[ "$DRY_RUN" == "true" ]]; then
            ADMIN_PASSWORD="generated-admin-password"
        else
            echo -n "Enter admin password (leave empty to generate): "
            read -s ADMIN_PASSWORD
            echo
            if [[ -z "$ADMIN_PASSWORD" ]]; then
                ADMIN_PASSWORD=$(generate_password 16)
                print_status "Generated admin password: $ADMIN_PASSWORD"
            fi
        fi
    fi
    
    if [[ -z "$DEMO_USER_PASSWORD" ]]; then
        if [[ "$DRY_RUN" == "true" ]]; then
            DEMO_USER_PASSWORD="generated-demo-password"
        else
            echo -n "Enter demo user password (leave empty to generate): "
            read -s DEMO_USER_PASSWORD
            echo
            if [[ -z "$DEMO_USER_PASSWORD" ]]; then
                DEMO_USER_PASSWORD=$(generate_password 12)
                print_status "Generated demo user password: $DEMO_USER_PASSWORD"
            fi
        fi
    fi
}

# Function to check database connection
check_database() {
    print_status "Checking database connection..."
    
    export PGPASSWORD="$DB_PASSWORD"
    if ! psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT 1;" &> /dev/null; then
        print_error "Cannot connect to database"
        exit 1
    fi
    
    print_status "Database connection successful"
}

# Function to create users table if not exists
create_users_table() {
    print_status "Creating users table if not exists..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_debug "DRY RUN: Would create users table"
        return 0
    fi
    
    psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" << 'EOF'
-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    role VARCHAR(50) DEFAULT 'user',
    status VARCHAR(20) DEFAULT 'active',
    email_verified BOOLEAN DEFAULT false,
    last_login_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create organizations table
CREATE TABLE IF NOT EXISTS organizations (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    website VARCHAR(255),
    industry VARCHAR(100),
    size VARCHAR(50),
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create user_organizations table
CREATE TABLE IF NOT EXISTS user_organizations (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    organization_id INTEGER REFERENCES organizations(id) ON DELETE CASCADE,
    role VARCHAR(50) DEFAULT 'member',
    status VARCHAR(20) DEFAULT 'active',
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, organization_id)
);

-- Create api_keys table
CREATE TABLE IF NOT EXISTS api_keys (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    organization_id INTEGER REFERENCES organizations(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    key_hash VARCHAR(255) NOT NULL,
    key_prefix VARCHAR(20) NOT NULL,
    permissions JSONB DEFAULT '{}',
    last_used_at TIMESTAMP,
    expires_at TIMESTAMP,
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create onboarding_progress table
CREATE TABLE IF NOT EXISTS onboarding_progress (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    step VARCHAR(100) NOT NULL,
    completed BOOLEAN DEFAULT false,
    completed_at TIMESTAMP,
    data JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, step)
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_organizations_slug ON organizations(slug);
CREATE INDEX IF NOT EXISTS idx_user_organizations_user_id ON user_organizations(user_id);
CREATE INDEX IF NOT EXISTS idx_user_organizations_org_id ON user_organizations(organization_id);
CREATE INDEX IF NOT EXISTS idx_api_keys_user_id ON api_keys(user_id);
CREATE INDEX IF NOT EXISTS idx_api_keys_key_hash ON api_keys(key_hash);
CREATE INDEX IF NOT EXISTS idx_onboarding_progress_user_id ON onboarding_progress(user_id);
EOF
    
    print_status "User tables created/verified"
}

# Function to reset existing users
reset_users() {
    if [[ "$RESET_USERS" == "false" ]]; then
        return 0
    fi
    
    print_warning "Resetting existing users..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_debug "DRY RUN: Would reset existing users"
        return 0
    fi
    
    psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" << EOF
-- Remove existing demo data
DELETE FROM onboarding_progress WHERE user_id IN (
    SELECT id FROM users WHERE email IN ('$ADMIN_EMAIL', '$DEMO_USER_EMAIL')
);
DELETE FROM api_keys WHERE user_id IN (
    SELECT id FROM users WHERE email IN ('$ADMIN_EMAIL', '$DEMO_USER_EMAIL')
);
DELETE FROM user_organizations WHERE user_id IN (
    SELECT id FROM users WHERE email IN ('$ADMIN_EMAIL', '$DEMO_USER_EMAIL')
);
DELETE FROM users WHERE email IN ('$ADMIN_EMAIL', '$DEMO_USER_EMAIL');
DELETE FROM organizations WHERE name = '$ORGANIZATION_NAME';
EOF
    
    print_status "Existing users reset"
}

# Function to create admin user
create_admin_user() {
    print_status "Creating admin user..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_debug "DRY RUN: Would create admin user $ADMIN_EMAIL"
        return 0
    fi
    
    # Hash password (simple bcrypt alternative for demo)
    local password_hash=$(python3 -c "
import bcrypt
import sys
password = '$ADMIN_PASSWORD'.encode('utf-8')
salt = bcrypt.gensalt()
hashed = bcrypt.hashpw(password, salt)
print(hashed.decode('utf-8'))
")
    
    psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" << EOF
INSERT INTO users (email, password_hash, first_name, last_name, role, status, email_verified)
VALUES ('$ADMIN_EMAIL', '$password_hash', 'Admin', 'User', 'admin', 'active', true)
ON CONFLICT (email) DO UPDATE SET
    password_hash = EXCLUDED.password_hash,
    role = EXCLUDED.role,
    status = EXCLUDED.status,
    email_verified = EXCLUDED.email_verified,
    updated_at = CURRENT_TIMESTAMP;
EOF
    
    print_status "Admin user created: $ADMIN_EMAIL"
}

# Function to create demo user
create_demo_user() {
    print_status "Creating demo user..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_debug "DRY RUN: Would create demo user $DEMO_USER_EMAIL"
        return 0
    fi
    
    # Hash password
    local password_hash=$(python3 -c "
import bcrypt
import sys
password = '$DEMO_USER_PASSWORD'.encode('utf-8')
salt = bcrypt.gensalt()
hashed = bcrypt.hashpw(password, salt)
print(hashed.decode('utf-8'))
")
    
    psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" << EOF
INSERT INTO users (email, password_hash, first_name, last_name, role, status, email_verified)
VALUES ('$DEMO_USER_EMAIL', '$password_hash', 'Demo', 'User', 'user', 'active', true)
ON CONFLICT (email) DO UPDATE SET
    password_hash = EXCLUDED.password_hash,
    role = EXCLUDED.role,
    status = EXCLUDED.status,
    email_verified = EXCLUDED.email_verified,
    updated_at = CURRENT_TIMESTAMP;
EOF
    
    print_status "Demo user created: $DEMO_USER_EMAIL"
}

# Function to create demo organization
create_demo_organization() {
    print_status "Creating demo organization..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_debug "DRY RUN: Would create organization '$ORGANIZATION_NAME'"
        return 0
    fi
    
    local org_slug=$(echo "$ORGANIZATION_NAME" | tr '[:upper:]' '[:lower:]' | sed 's/[^a-z0-9]/-/g' | sed 's/--*/-/g' | sed 's/^-\|-$//g')
    
    psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" << EOF
INSERT INTO organizations (name, slug, description, industry, size, status)
VALUES (
    '$ORGANIZATION_NAME',
    '$org_slug',
    'Demo e-commerce organization for testing and demonstration purposes',
    'E-commerce',
    'small',
    'active'
)
ON CONFLICT (slug) DO UPDATE SET
    name = EXCLUDED.name,
    description = EXCLUDED.description,
    industry = EXCLUDED.industry,
    size = EXCLUDED.size,
    updated_at = CURRENT_TIMESTAMP;
EOF
    
    print_status "Demo organization created: $ORGANIZATION_NAME"
}

# Function to link users to organization
link_users_to_organization() {
    print_status "Linking users to organization..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_debug "DRY RUN: Would link users to organization"
        return 0
    fi
    
    local org_slug=$(echo "$ORGANIZATION_NAME" | tr '[:upper:]' '[:lower:]' | sed 's/[^a-z0-9]/-/g' | sed 's/--*/-/g' | sed 's/^-\|-$//g')
    
    psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" << EOF
-- Link admin user as owner
INSERT INTO user_organizations (user_id, organization_id, role, status)
SELECT u.id, o.id, 'owner', 'active'
FROM users u, organizations o
WHERE u.email = '$ADMIN_EMAIL' AND o.slug = '$org_slug'
ON CONFLICT (user_id, organization_id) DO UPDATE SET
    role = EXCLUDED.role,
    status = EXCLUDED.status;

-- Link demo user as member
INSERT INTO user_organizations (user_id, organization_id, role, status)
SELECT u.id, o.id, 'member', 'active'
FROM users u, organizations o
WHERE u.email = '$DEMO_USER_EMAIL' AND o.slug = '$org_slug'
ON CONFLICT (user_id, organization_id) DO UPDATE SET
    role = EXCLUDED.role,
    status = EXCLUDED.status;
EOF
    
    print_status "Users linked to organization"
}

# Function to create API keys
create_api_keys() {
    print_status "Creating API keys..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_debug "DRY RUN: Would create API keys"
        return 0
    fi
    
    # Generate API keys
    local admin_api_key="ak_admin_$(openssl rand -hex 16)"
    local demo_api_key="ak_demo_$(openssl rand -hex 16)"
    
    # Hash API keys
    local admin_key_hash=$(echo -n "$admin_api_key" | sha256sum | cut -d' ' -f1)
    local demo_key_hash=$(echo -n "$demo_api_key" | sha256sum | cut -d' ' -f1)
    
    local org_slug=$(echo "$ORGANIZATION_NAME" | tr '[:upper:]' '[:lower:]' | sed 's/[^a-z0-9]/-/g' | sed 's/--*/-/g' | sed 's/^-\|-$//g')
    
    psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" << EOF
-- Create admin API key
INSERT INTO api_keys (user_id, organization_id, name, key_hash, key_prefix, permissions, status)
SELECT u.id, o.id, 'Admin API Key', '$admin_key_hash', 'ak_admin', 
    '{"read": true, "write": true, "admin": true}', 'active'
FROM users u, organizations o
WHERE u.email = '$ADMIN_EMAIL' AND o.slug = '$org_slug'
ON CONFLICT (user_id, name) DO UPDATE SET
    key_hash = EXCLUDED.key_hash,
    key_prefix = EXCLUDED.key_prefix,
    permissions = EXCLUDED.permissions,
    updated_at = CURRENT_TIMESTAMP;

-- Create demo API key
INSERT INTO api_keys (user_id, organization_id, name, key_hash, key_prefix, permissions, status)
SELECT u.id, o.id, 'Demo API Key', '$demo_key_hash', 'ak_demo',
    '{"read": true, "write": false, "admin": false}', 'active'
FROM users u, organizations o
WHERE u.email = '$DEMO_USER_EMAIL' AND o.slug = '$org_slug'
ON CONFLICT (user_id, name) DO UPDATE SET
    key_hash = EXCLUDED.key_hash,
    key_prefix = EXCLUDED.key_prefix,
    permissions = EXCLUDED.permissions,
    updated_at = CURRENT_TIMESTAMP;
EOF
    
    # Save API keys to file for reference
    if [[ "$ENVIRONMENT" != "production" ]]; then
        cat > "$PROJECT_ROOT/demo-credentials.txt" << EOF
Demo Credentials for E-commerce Analytics Platform
================================================

Admin User:
Email: $ADMIN_EMAIL
Password: $ADMIN_PASSWORD
API Key: $admin_api_key

Demo User:
Email: $DEMO_USER_EMAIL
Password: $DEMO_USER_PASSWORD
API Key: $demo_api_key

Organization: $ORGANIZATION_NAME

IMPORTANT: Keep these credentials secure and do not use in production!
EOF
        chmod 600 "$PROJECT_ROOT/demo-credentials.txt"
        print_status "API keys created and saved to demo-credentials.txt"
    else
        print_status "API keys created (not saved to file in production)"
    fi
}

# Function to setup onboarding steps
setup_onboarding_steps() {
    print_status "Setting up onboarding steps..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_debug "DRY RUN: Would setup onboarding steps"
        return 0
    fi
    
    psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" << EOF
-- Setup onboarding steps for demo user
INSERT INTO onboarding_progress (user_id, step, completed, data)
SELECT u.id, 'welcome', true, '{"completed_at": "$(date -Iseconds)", "message": "Welcome to the platform!"}'
FROM users u WHERE u.email = '$DEMO_USER_EMAIL'
ON CONFLICT (user_id, step) DO UPDATE SET
    completed = EXCLUDED.completed,
    data = EXCLUDED.data,
    updated_at = CURRENT_TIMESTAMP;

INSERT INTO onboarding_progress (user_id, step, completed, data)
SELECT u.id, 'setup_organization', true, '{"organization_name": "$ORGANIZATION_NAME"}'
FROM users u WHERE u.email = '$DEMO_USER_EMAIL'
ON CONFLICT (user_id, step) DO UPDATE SET
    completed = EXCLUDED.completed,
    data = EXCLUDED.data,
    updated_at = CURRENT_TIMESTAMP;

INSERT INTO onboarding_progress (user_id, step, completed, data)
SELECT u.id, 'connect_data_source', false, '{"next_action": "Connect your e-commerce platform"}'
FROM users u WHERE u.email = '$DEMO_USER_EMAIL'
ON CONFLICT (user_id, step) DO UPDATE SET
    completed = EXCLUDED.completed,
    data = EXCLUDED.data,
    updated_at = CURRENT_TIMESTAMP;

INSERT INTO onboarding_progress (user_id, step, completed, data)
SELECT u.id, 'view_first_report', false, '{"next_action": "Explore your analytics dashboard"}'
FROM users u WHERE u.email = '$DEMO_USER_EMAIL'
ON CONFLICT (user_id, step) DO UPDATE SET
    completed = EXCLUDED.completed,
    data = EXCLUDED.data,
    updated_at = CURRENT_TIMESTAMP;

INSERT INTO onboarding_progress (user_id, step, completed, data)
SELECT u.id, 'setup_alerts', false, '{"next_action": "Configure alerts and notifications"}'
FROM users u WHERE u.email = '$DEMO_USER_EMAIL'
ON CONFLICT (user_id, step) DO UPDATE SET
    completed = EXCLUDED.completed,
    data = EXCLUDED.data,
    updated_at = CURRENT_TIMESTAMP;

-- Mark admin as fully onboarded
INSERT INTO onboarding_progress (user_id, step, completed, completed_at, data)
SELECT u.id, 'welcome', true, CURRENT_TIMESTAMP, '{"role": "admin"}'
FROM users u WHERE u.email = '$ADMIN_EMAIL'
ON CONFLICT (user_id, step) DO UPDATE SET
    completed = EXCLUDED.completed,
    completed_at = EXCLUDED.completed_at,
    data = EXCLUDED.data,
    updated_at = CURRENT_TIMESTAMP;

INSERT INTO onboarding_progress (user_id, step, completed, completed_at, data)
SELECT u.id, 'setup_organization', true, CURRENT_TIMESTAMP, '{"role": "admin"}'
FROM users u WHERE u.email = '$ADMIN_EMAIL'
ON CONFLICT (user_id, step) DO UPDATE SET
    completed = EXCLUDED.completed,
    completed_at = EXCLUDED.completed_at,
    data = EXCLUDED.data,
    updated_at = CURRENT_TIMESTAMP;

INSERT INTO onboarding_progress (user_id, step, completed, completed_at, data)
SELECT u.id, 'connect_data_source', true, CURRENT_TIMESTAMP, '{"role": "admin"}'
FROM users u WHERE u.email = '$ADMIN_EMAIL'
ON CONFLICT (user_id, step) DO UPDATE SET
    completed = EXCLUDED.completed,
    completed_at = EXCLUDED.completed_at,
    data = EXCLUDED.data,
    updated_at = CURRENT_TIMESTAMP;

INSERT INTO onboarding_progress (user_id, step, completed, completed_at, data)
SELECT u.id, 'view_first_report', true, CURRENT_TIMESTAMP, '{"role": "admin"}'
FROM users u WHERE u.email = '$ADMIN_EMAIL'
ON CONFLICT (user_id, step) DO UPDATE SET
    completed = EXCLUDED.completed,
    completed_at = EXCLUDED.completed_at,
    data = EXCLUDED.data,
    updated_at = CURRENT_TIMESTAMP;

INSERT INTO onboarding_progress (user_id, step, completed, completed_at, data)
SELECT u.id, 'setup_alerts', true, CURRENT_TIMESTAMP, '{"role": "admin"}'
FROM users u WHERE u.email = '$ADMIN_EMAIL'
ON CONFLICT (user_id, step) DO UPDATE SET
    completed = EXCLUDED.completed,
    completed_at = EXCLUDED.completed_at,
    data = EXCLUDED.data,
    updated_at = CURRENT_TIMESTAMP;
EOF
    
    print_status "Onboarding steps configured"
}

# Function to create onboarding guide
create_onboarding_guide() {
    print_status "Creating onboarding guide..."
    
    cat > "$PROJECT_ROOT/docs/ONBOARDING.md" << 'EOF'
# User Onboarding Guide

Welcome to the E-commerce Analytics Platform! This guide will help you get started with analyzing your e-commerce data.

## Getting Started

### 1. Account Setup
- Log in with your credentials
- Complete your profile information
- Join or create an organization

### 2. Connect Your Data Source
Connect your e-commerce platform to start analyzing data:

#### Shopify Integration
1. Go to Integrations → Shopify
2. Enter your store URL
3. Authorize the connection
4. Configure sync settings

#### WooCommerce Integration
1. Go to Integrations → WooCommerce
2. Enter your store URL and API credentials
3. Test the connection
4. Configure sync frequency

#### Custom Integration
1. Go to Integrations → Custom
2. Use our REST API or webhooks
3. Follow the API documentation
4. Test data flow

### 3. Explore Your Dashboard
Once data is connected, explore these key areas:

#### Revenue Analytics
- Total revenue and growth trends
- Revenue by channel, product, and customer segment
- Monthly/weekly/daily breakdowns

#### Customer Analytics
- Customer acquisition and retention
- Lifetime value analysis
- Cohort analysis
- Customer segmentation

#### Attribution Analysis
- Multi-touch attribution models
- Channel performance comparison
- Customer journey visualization

#### Forecasting
- Revenue predictions
- Customer behavior forecasting
- Seasonal trend analysis

### 4. Set Up Alerts
Configure alerts for important metrics:
1. Go to Settings → Alerts
2. Set thresholds for key metrics
3. Choose notification channels
4. Test alert delivery

### 5. Team Collaboration
Invite team members and set permissions:
1. Go to Settings → Team
2. Send invitations
3. Assign roles (Admin, Member, Viewer)
4. Configure access permissions

## Key Features

### Dashboard Overview
- Real-time metrics and KPIs
- Customizable widgets and charts
- Time range filtering
- Export capabilities

### Analytics Reports
- Revenue analysis
- Customer segmentation
- Product performance
- Channel attribution
- Cohort analysis
- Funnel analysis

### Data Integration
- Multiple e-commerce platforms
- Real-time and batch syncing
- Data validation and cleaning
- Custom field mapping

### Advanced Analytics
- Machine learning forecasting
- Anomaly detection
- Predictive customer scoring
- Advanced segmentation

## Best Practices

### Data Quality
- Ensure consistent product naming
- Use UTM parameters for campaign tracking
- Implement proper event tracking
- Regular data validation

### Analysis Approach
- Start with high-level trends
- Drill down into specific segments
- Compare time periods
- Use cohort analysis for retention insights

### Team Workflows
- Define KPI ownership
- Schedule regular review meetings
- Share insights with stakeholders
- Document important findings

## Support Resources

### Documentation
- API Documentation: `/docs/api/`
- Technical Guides: `/docs/guides/`
- FAQ: `/docs/faq.md`

### Getting Help
- In-app help tooltips
- Email support: <EMAIL>
- Knowledge base: help.ecommerce-analytics.com
- Community forum: community.ecommerce-analytics.com

## Troubleshooting

### Common Issues

#### Data Not Syncing
1. Check integration status
2. Verify API credentials
3. Review sync logs
4. Contact support if needed

#### Incorrect Metrics
1. Verify date ranges
2. Check filters and segments
3. Review data mapping
4. Validate source data

#### Performance Issues
1. Optimize date ranges
2. Use appropriate filters
3. Clear browser cache
4. Check network connection

### Getting Help
If you encounter issues:
1. Check the FAQ section
2. Review documentation
3. Contact support team
4. Join community discussions

Welcome aboard! We're excited to help you unlock insights from your e-commerce data.
EOF
    
    print_status "Onboarding guide created at docs/ONBOARDING.md"
}

# Function to show setup summary
show_summary() {
    if [[ "$DRY_RUN" == "true" ]]; then
        print_status "User Onboarding Setup Summary (DRY RUN)"
    else
        print_status "User Onboarding Setup Summary"
    fi
    
    echo "============================="
    echo "Environment: $ENVIRONMENT"
    echo "Organization: $ORGANIZATION_NAME"
    echo ""
    echo "Users created:"
    echo "  Admin: $ADMIN_EMAIL"
    echo "  Demo:  $DEMO_USER_EMAIL"
    echo ""
    
    if [[ "$DRY_RUN" == "false" ]]; then
        echo "Credentials saved to: demo-credentials.txt"
        echo "Onboarding guide: docs/ONBOARDING.md"
        echo ""
        print_status "Setup completed successfully!"
        echo ""
        print_status "Next steps:"
        echo "1. Start the application services"
        echo "2. Log in with admin or demo credentials"
        echo "3. Follow the onboarding guide"
        echo "4. Connect data sources and explore analytics"
        
        if [[ "$ENVIRONMENT" != "production" ]]; then
            echo ""
            print_warning "Demo credentials are saved in demo-credentials.txt"
            print_warning "Remember to secure this file and not use these credentials in production!"
        fi
    else
        print_status "This was a dry run. Use the same command without -n to create actual users."
    fi
}

# Main function
main() {
    print_status "Starting user onboarding setup..."
    
    # Prompt for passwords if not provided
    prompt_for_passwords
    
    # Check database connection
    check_database
    
    # Create database tables
    create_users_table
    
    # Reset users if requested
    reset_users
    
    # Create users and organization
    create_admin_user
    create_demo_user
    create_demo_organization
    link_users_to_organization
    
    # Setup additional features
    create_api_keys
    setup_onboarding_steps
    create_onboarding_guide
    
    # Show summary
    show_summary
}

# Check for required tools
if ! command -v python3 &> /dev/null; then
    print_error "python3 is required but not installed"
    exit 1
fi

if ! python3 -c "import bcrypt" 2>/dev/null; then
    print_error "python3-bcrypt is required but not installed"
    print_error "Install with: pip3 install bcrypt"
    exit 1
fi

# Run main function
main "$@"