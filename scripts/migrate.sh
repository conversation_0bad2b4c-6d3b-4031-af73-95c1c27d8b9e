#!/bin/bash

set -e

echo "🔄 Running database migrations..."

# Check if podman-compose is running
if ! podman-compose ps | grep -q "postgres.*Up"; then
    echo "❌ PostgreSQL container is not running. Please start it first:"
    echo "   podman-compose up -d postgres"
    exit 1
fi

# Wait for PostgreSQL to be ready
echo "⏳ Waiting for PostgreSQL to be ready..."
until podman-compose exec -T postgres pg_isready -U postgres; do
  echo "   Waiting for database..."
  sleep 2
done

echo "✅ PostgreSQL is ready!"

# Check if database exists and has tables
if podman-compose exec -T postgres psql -U postgres -d ecommerce_analytics -c "\dt" | grep -q "users"; then
    echo "📊 Database tables already exist. Skipping initial migration."
else
    echo "🚀 Running initial database setup..."
    podman-compose exec -T postgres psql -U postgres -d ecommerce_analytics -f /docker-entrypoint-initdb.d/init.sql
fi

# Run any additional migrations here
# For now, we'll just verify the schema
echo "🔍 Verifying database schema..."
podman-compose exec -T postgres psql -U postgres -d ecommerce_analytics -c "
SELECT 
    schemaname,
    tablename 
FROM pg_tables 
WHERE schemaname = 'public' 
ORDER BY tablename;
"

echo "✅ Database migration completed successfully!"
echo ""
echo "📋 Available tables:"
podman-compose exec -T postgres psql -U postgres -d ecommerce_analytics -c "\dt"

echo ""
echo "🎉 Database is ready for development!"
echo "💡 You can connect to the database with:"
echo "   psql postgres://postgres:password@localhost:5432/ecommerce_analytics"