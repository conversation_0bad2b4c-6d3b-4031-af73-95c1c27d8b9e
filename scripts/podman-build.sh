#!/bin/bash

# <PERSON><PERSON> build script for e-commerce analytics platform
# Compatible with both <PERSON><PERSON> and Docker

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Default values
TARGET="production"
REBUILD=false
PRUNE=false
VERBOSE=false

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Build all services for the e-commerce analytics platform using Podman/Docker.

OPTIONS:
    -t, --target TARGET     Build target: development, production (default: production)
    -r, --rebuild          Force rebuild without cache
    -p, --prune            Prune unused images after build
    -v, --verbose          Verbose output
    -h, --help             Show this help message

EXAMPLES:
    $0                     # Build all services for production
    $0 -t development      # Build for development
    $0 -r -p              # Rebuild without cache and prune after
    
ENVIRONMENT:
    Use either 'podman' or 'docker' command. Script auto-detects available tool.
EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -t|--target)
            TARGET="$2"
            shift 2
            ;;
        -r|--rebuild)
            REBUILD=true
            shift
            ;;
        -p|--prune)
            PRUNE=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Detect container tool (podman or docker)
if command -v podman &> /dev/null; then
    CONTAINER_TOOL="podman"
    COMPOSE_TOOL="podman-compose"
elif command -v docker &> /dev/null; then
    CONTAINER_TOOL="docker"
    COMPOSE_TOOL="docker-compose"
else
    print_error "Neither podman nor docker found. Please install one of them."
    exit 1
fi

print_status "Using container tool: $CONTAINER_TOOL"

# Validate target
if [[ "$TARGET" != "development" && "$TARGET" != "production" ]]; then
    print_error "Invalid target: $TARGET. Must be 'development' or 'production'"
    exit 1
fi

print_status "Building for target: $TARGET"

# Build options
BUILD_OPTS=""
if [[ "$REBUILD" == "true" ]]; then
    BUILD_OPTS="$BUILD_OPTS --no-cache"
    print_status "Building without cache"
fi

if [[ "$VERBOSE" == "true" ]]; then
    BUILD_OPTS="$BUILD_OPTS --progress=plain"
fi

# Services to build
SERVICES=("analytics" "dashboard" "integration" "frontend")

# Build each service
for service in "${SERVICES[@]}"; do
    print_status "Building $service service..."
    
    if [[ "$service" == "frontend" ]]; then
        SERVICE_DIR="frontend"
    else
        SERVICE_DIR="services/$service"
    fi
    
    if [[ ! -d "$SERVICE_DIR" ]]; then
        print_warning "Directory $SERVICE_DIR not found, skipping $service"
        continue
    fi
    
    # Build the service
    if [[ "$VERBOSE" == "true" ]]; then
        $CONTAINER_TOOL build $BUILD_OPTS \
            --target "$TARGET" \
            --tag "ecommerce-$service:$TARGET" \
            --tag "ecommerce-$service:latest" \
            "$SERVICE_DIR"
    else
        $CONTAINER_TOOL build $BUILD_OPTS \
            --target "$TARGET" \
            --tag "ecommerce-$service:$TARGET" \
            --tag "ecommerce-$service:latest" \
            "$SERVICE_DIR" > /dev/null
    fi
    
    if [[ $? -eq 0 ]]; then
        print_status "✓ $service service built successfully"
    else
        print_error "✗ Failed to build $service service"
        exit 1
    fi
done

# Build with docker-compose for integration testing
print_status "Building with compose for integration..."
export BUILD_TARGET="$TARGET"

if [[ "$TARGET" == "development" ]]; then
    COMPOSE_FILE="-f docker-compose.yml -f docker-compose.dev.yml"
else
    COMPOSE_FILE="-f docker-compose.yml -f docker-compose.prod.yml"
fi

if [[ "$REBUILD" == "true" ]]; then
    $COMPOSE_TOOL $COMPOSE_FILE build --no-cache
else
    $COMPOSE_TOOL $COMPOSE_FILE build
fi

print_status "✓ All services built successfully with compose"

# Prune if requested
if [[ "$PRUNE" == "true" ]]; then
    print_status "Pruning unused images..."
    $CONTAINER_TOOL image prune -f
    print_status "✓ Unused images pruned"
fi

# Show built images
print_status "Built images:"
$CONTAINER_TOOL images | grep "ecommerce-"

print_status "Build completed successfully!"
print_status "To start services, run:"
if [[ "$TARGET" == "development" ]]; then
    echo "  $COMPOSE_TOOL -f docker-compose.yml -f docker-compose.dev.yml up"
else
    echo "  $COMPOSE_TOOL -f docker-compose.yml -f docker-compose.prod.yml up"
fi