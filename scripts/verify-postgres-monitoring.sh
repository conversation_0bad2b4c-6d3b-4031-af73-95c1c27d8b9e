#!/bin/bash

# PostgreSQL Performance Monitoring Verification Script
# This script verifies that pg_stat_statements and performance monitoring are properly configured

set -e

echo "🔍 PostgreSQL Performance Monitoring Verification"
echo "=================================================="

# Database connection parameters
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-5432}"
DB_NAME="${DB_NAME:-ecommerce_analytics}"
DB_USER="${DB_USER:-postgres}"
DB_PASSWORD="${DB_PASSWORD:-password}"

# Function to run SQL query
run_query() {
    local query="$1"
    PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "$query" 2>/dev/null
}

# Check if PostgreSQL is running
echo "📡 Checking PostgreSQL connection..."
if run_query "SELECT 1;" > /dev/null; then
    echo "✅ PostgreSQL is running and accessible"
else
    echo "❌ Cannot connect to PostgreSQL"
    exit 1
fi

# Check if pg_stat_statements extension is installed
echo "🔧 Checking pg_stat_statements extension..."
if run_query "SELECT 1 FROM pg_extension WHERE extname = 'pg_stat_statements';" | grep -q "1"; then
    echo "✅ pg_stat_statements extension is installed"
else
    echo "⚠️  pg_stat_statements extension not found, attempting to install..."
    if run_query "CREATE EXTENSION IF NOT EXISTS pg_stat_statements;"; then
        echo "✅ pg_stat_statements extension installed successfully"
    else
        echo "❌ Failed to install pg_stat_statements extension"
        echo "💡 Make sure shared_preload_libraries includes 'pg_stat_statements' in postgresql.conf"
        exit 1
    fi
fi

# Check if pg_stat_statements is collecting data
echo "📊 Checking pg_stat_statements data collection..."
query_count=$(run_query "SELECT COUNT(*) FROM pg_stat_statements;" | tr -d ' ')
if [ "$query_count" -gt 0 ]; then
    echo "✅ pg_stat_statements is collecting data ($query_count queries tracked)"
else
    echo "⚠️  pg_stat_statements is not collecting data yet (this is normal for new installations)"
fi

# Check shared_preload_libraries setting
echo "⚙️  Checking shared_preload_libraries configuration..."
shared_libs=$(run_query "SHOW shared_preload_libraries;" | tr -d ' ')
if echo "$shared_libs" | grep -q "pg_stat_statements"; then
    echo "✅ pg_stat_statements is in shared_preload_libraries: $shared_libs"
else
    echo "❌ pg_stat_statements is NOT in shared_preload_libraries: $shared_libs"
    echo "💡 Add 'pg_stat_statements' to shared_preload_libraries in postgresql.conf and restart PostgreSQL"
fi

# Check performance monitoring settings
echo "📈 Checking performance monitoring settings..."
settings_to_check=(
    "log_statement"
    "log_duration"
    "log_min_duration_statement"
    "track_activities"
    "track_counts"
    "track_io_timing"
)

for setting in "${settings_to_check[@]}"; do
    value=$(run_query "SHOW $setting;" | tr -d ' ')
    echo "   $setting: $value"
done

# Test query performance view
echo "🔍 Testing query performance view..."
if run_query "SELECT COUNT(*) FROM query_performance;" > /dev/null 2>&1; then
    echo "✅ query_performance view is accessible"
else
    echo "⚠️  query_performance view not found, creating it..."
    run_query "
    CREATE OR REPLACE VIEW query_performance AS
    SELECT 
        query,
        calls,
        total_exec_time,
        mean_exec_time,
        stddev_exec_time,
        rows,
        100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
    FROM pg_stat_statements
    ORDER BY total_exec_time DESC;
    "
    echo "✅ query_performance view created"
fi

# Show top queries if available
echo "🏆 Top 5 queries by execution time:"
run_query "
SELECT 
    LEFT(query, 80) as query_preview,
    calls,
    ROUND(total_exec_time::numeric, 2) as total_time_ms,
    ROUND(mean_exec_time::numeric, 2) as mean_time_ms
FROM pg_stat_statements 
ORDER BY total_exec_time DESC 
LIMIT 5;
" || echo "   No query data available yet"

echo ""
echo "🎉 PostgreSQL Performance Monitoring Verification Complete!"
echo "💡 To view real-time performance data:"
echo "   - Query pg_stat_statements table directly"
echo "   - Use the query_performance view"
echo "   - Check Prometheus metrics at http://localhost:9187/metrics"
echo "   - View Grafana dashboards for visual monitoring"
