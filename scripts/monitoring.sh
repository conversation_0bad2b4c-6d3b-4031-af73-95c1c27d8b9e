#!/bin/bash

# System monitoring script for e-commerce analytics platform
# Monitors services, resources, and sends alerts

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_FILE="/var/log/monitoring.log"
METRICS_FILE="/var/log/metrics.json"
ALERT_LOG="/var/log/alerts.log"

# Default values
CHECK_INTERVAL=60
ALERT_COOLDOWN=300
CPU_THRESHOLD=80
MEMORY_THRESHOLD=85
DISK_THRESHOLD=90
RESPONSE_TIME_THRESHOLD=5000
ERROR_RATE_THRESHOLD=5
DAEMON_MODE=false
SEND_ALERTS=true
QUIET=false

# Service endpoints
declare -A SERVICE_ENDPOINTS=(
    ["analytics"]="http://localhost:3002/health"
    ["dashboard"]="http://localhost:3001/health"
    ["integration"]="http://localhost:3003/health"
    ["frontend"]="http://localhost:3000/health"
)

# Alert state tracking
declare -A LAST_ALERT_TIME
declare -A ALERT_COUNT

# Function to print colored output
print_status() {
    [[ "$QUIET" == "true" ]] && return
    echo -e "${GREEN}[INFO]${NC} $1" | tee -a "$LOG_FILE"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

print_debug() {
    [[ "$QUIET" == "true" ]] && return
    echo -e "${BLUE}[DEBUG]${NC} $1" | tee -a "$LOG_FILE"
}

# Function to show usage
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Monitor the e-commerce analytics platform services and system resources.

OPTIONS:
    -i, --interval SECONDS    Check interval in seconds (default: 60)
    -c, --cooldown SECONDS    Alert cooldown in seconds (default: 300)
    -d, --daemon              Run in daemon mode
    -q, --quiet               Quiet mode (no console output)
    -a, --no-alerts           Disable alert sending
    --cpu-threshold PERCENT   CPU usage threshold (default: 80)
    --memory-threshold PERCENT Memory usage threshold (default: 85)
    --disk-threshold PERCENT  Disk usage threshold (default: 90)
    --response-threshold MS   Response time threshold in ms (default: 5000)
    --error-threshold PERCENT Error rate threshold (default: 5)
    -h, --help                Show this help message

DAEMON MODE:
    In daemon mode, the script runs continuously and monitors the system.
    Use systemd or similar to manage the daemon process.

EXAMPLES:
    $0                        # Run once with default settings
    $0 -d -i 30              # Run as daemon with 30-second intervals
    $0 --cpu-threshold 90    # Set CPU threshold to 90%
    $0 -q -a                 # Quiet mode without alerts

ENVIRONMENT VARIABLES:
    SLACK_WEBHOOK_URL         Slack webhook for alerts
    EMAIL_RECIPIENTS          Email recipients for alerts
    PAGERDUTY_API_KEY         PagerDuty API key for critical alerts
    DATADOG_API_KEY           DataDog API key for metrics
EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -i|--interval)
            CHECK_INTERVAL="$2"
            shift 2
            ;;
        -c|--cooldown)
            ALERT_COOLDOWN="$2"
            shift 2
            ;;
        -d|--daemon)
            DAEMON_MODE=true
            shift
            ;;
        -q|--quiet)
            QUIET=true
            shift
            ;;
        -a|--no-alerts)
            SEND_ALERTS=false
            shift
            ;;
        --cpu-threshold)
            CPU_THRESHOLD="$2"
            shift 2
            ;;
        --memory-threshold)
            MEMORY_THRESHOLD="$2"
            shift 2
            ;;
        --disk-threshold)
            DISK_THRESHOLD="$2"
            shift 2
            ;;
        --response-threshold)
            RESPONSE_TIME_THRESHOLD="$2"
            shift 2
            ;;
        --error-threshold)
            ERROR_RATE_THRESHOLD="$2"
            shift 2
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Initialize logging
mkdir -p "$(dirname "$LOG_FILE")"
mkdir -p "$(dirname "$METRICS_FILE")"
mkdir -p "$(dirname "$ALERT_LOG")"

# Function to check if alert should be sent
should_send_alert() {
    local alert_key="$1"
    local current_time=$(date +%s)
    local last_alert=${LAST_ALERT_TIME[$alert_key]:-0}
    
    if [[ $((current_time - last_alert)) -gt $ALERT_COOLDOWN ]]; then
        LAST_ALERT_TIME[$alert_key]=$current_time
        return 0
    fi
    
    return 1
}

# Function to send alerts
send_alert() {
    local severity="$1"
    local title="$2"
    local message="$3"
    local alert_key="$4"
    
    if [[ "$SEND_ALERTS" == "false" ]]; then
        return 0
    fi
    
    if ! should_send_alert "$alert_key"; then
        print_debug "Alert suppressed due to cooldown: $alert_key"
        return 0
    fi
    
    # Log alert
    echo "$(date -Iseconds): [$severity] $title - $message" >> "$ALERT_LOG"
    
    # Increment alert count
    ALERT_COUNT[$alert_key]=$((${ALERT_COUNT[$alert_key]:-0} + 1))
    
    # Send to Slack
    if [[ -n "$SLACK_WEBHOOK_URL" ]]; then
        send_slack_alert "$severity" "$title" "$message"
    fi
    
    # Send email
    if [[ -n "$EMAIL_RECIPIENTS" ]]; then
        send_email_alert "$severity" "$title" "$message"
    fi
    
    # Send to PagerDuty for critical alerts
    if [[ "$severity" == "critical" && -n "$PAGERDUTY_API_KEY" ]]; then
        send_pagerduty_alert "$title" "$message"
    fi
    
    print_warning "Alert sent: [$severity] $title"
}

# Function to send Slack alert
send_slack_alert() {
    local severity="$1"
    local title="$2"
    local message="$3"
    
    local color="warning"
    local emoji="⚠️"
    
    case "$severity" in
        "critical")
            color="danger"
            emoji="🚨"
            ;;
        "warning")
            color="warning"
            emoji="⚠️"
            ;;
        "info")
            color="good"
            emoji="ℹ️"
            ;;
    esac
    
    curl -s -X POST "$SLACK_WEBHOOK_URL" \
        -H 'Content-type: application/json' \
        --data "{
            \"attachments\": [{
                \"color\": \"$color\",
                \"title\": \"$emoji E-commerce Analytics Alert\",
                \"text\": \"**$title**\n$message\",
                \"fields\": [
                    {\"title\": \"Severity\", \"value\": \"$severity\", \"short\": true},
                    {\"title\": \"Host\", \"value\": \"$(hostname)\", \"short\": true},
                    {\"title\": \"Time\", \"value\": \"$(date)\", \"short\": false}
                ],
                \"ts\": $(date +%s)
            }]
        }" &> /dev/null || print_debug "Failed to send Slack alert"
}

# Function to send email alert
send_email_alert() {
    local severity="$1"
    local title="$2"
    local message="$3"
    
    echo "Subject: E-commerce Analytics Alert - $severity

Alert: $title
Severity: $severity
Host: $(hostname)
Time: $(date)

Details:
$message

System Status:
$(get_system_summary)
    " | sendmail "$EMAIL_RECIPIENTS" &> /dev/null || print_debug "Failed to send email alert"
}

# Function to send PagerDuty alert
send_pagerduty_alert() {
    local title="$1"
    local message="$2"
    
    curl -s -X POST "https://events.pagerduty.com/v2/enqueue" \
        -H "Content-Type: application/json" \
        --data "{
            \"routing_key\": \"$PAGERDUTY_API_KEY\",
            \"event_action\": \"trigger\",
            \"payload\": {
                \"summary\": \"$title\",
                \"source\": \"$(hostname)\",
                \"severity\": \"critical\",
                \"custom_details\": {
                    \"message\": \"$message\",
                    \"host\": \"$(hostname)\",
                    \"timestamp\": \"$(date -Iseconds)\"
                }
            }
        }" &> /dev/null || print_debug "Failed to send PagerDuty alert"
}

# Function to check service health
check_service_health() {
    local service="$1"
    local endpoint="${SERVICE_ENDPOINTS[$service]}"
    
    if [[ -z "$endpoint" ]]; then
        print_debug "No endpoint defined for service: $service"
        return 1
    fi
    
    local start_time=$(date +%s%3N)
    local http_code=$(curl -s -o /dev/null -w "%{http_code}" --max-time 10 "$endpoint" 2>/dev/null || echo "000")
    local end_time=$(date +%s%3N)
    local response_time=$((end_time - start_time))
    
    # Check if service is healthy
    if [[ "$http_code" == "200" ]]; then
        print_debug "Service $service is healthy (${response_time}ms)"
        
        # Check response time
        if [[ $response_time -gt $RESPONSE_TIME_THRESHOLD ]]; then
            send_alert "warning" "Slow Response Time" \
                "Service $service response time is ${response_time}ms (threshold: ${RESPONSE_TIME_THRESHOLD}ms)" \
                "slow_response_$service"
        fi
        
        return 0
    else
        print_error "Service $service is unhealthy (HTTP $http_code)"
        send_alert "critical" "Service Down" \
            "Service $service is not responding (HTTP $http_code)" \
            "service_down_$service"
        return 1
    fi
}

# Function to check system resources
check_system_resources() {
    # CPU usage
    local cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | sed 's/%us,//')
    cpu_usage=${cpu_usage%.*}  # Remove decimal part
    
    if [[ $cpu_usage -gt $CPU_THRESHOLD ]]; then
        send_alert "warning" "High CPU Usage" \
            "CPU usage is ${cpu_usage}% (threshold: ${CPU_THRESHOLD}%)" \
            "high_cpu"
    fi
    
    # Memory usage
    local memory_info=$(free | grep '^Mem:')
    local total_memory=$(echo $memory_info | awk '{print $2}')
    local used_memory=$(echo $memory_info | awk '{print $3}')
    local memory_usage=$((used_memory * 100 / total_memory))
    
    if [[ $memory_usage -gt $MEMORY_THRESHOLD ]]; then
        send_alert "warning" "High Memory Usage" \
            "Memory usage is ${memory_usage}% (threshold: ${MEMORY_THRESHOLD}%)" \
            "high_memory"
    fi
    
    # Disk usage
    df -h | grep -vE '^Filesystem|tmpfs|cdrom' | awk '{print $5 " " $1 " " $6}' | while read output; do
        local usage=$(echo $output | awk '{print $1}' | sed 's/%//')
        local partition=$(echo $output | awk '{print $2}')
        local mount=$(echo $output | awk '{print $3}')
        
        if [[ $usage -gt $DISK_THRESHOLD ]]; then
            send_alert "warning" "High Disk Usage" \
                "Disk usage on $mount ($partition) is ${usage}% (threshold: ${DISK_THRESHOLD}%)" \
                "high_disk_$mount"
        fi
    done
    
    # Load average
    local load_avg=$(uptime | awk -F'load average:' '{print $2}' | awk -F',' '{print $1}' | xargs)
    local cpu_cores=$(nproc)
    local load_threshold=$((cpu_cores * 2))
    
    if (( $(echo "$load_avg > $load_threshold" | bc -l) )); then
        send_alert "warning" "High Load Average" \
            "Load average is $load_avg (threshold: $load_threshold for $cpu_cores cores)" \
            "high_load"
    fi
}

# Function to check Docker services
check_docker_services() {
    if ! command -v docker &> /dev/null; then
        return 0
    fi
    
    # Check if Docker daemon is running
    if ! docker info &> /dev/null; then
        send_alert "critical" "Docker Daemon Down" \
            "Docker daemon is not running or not accessible" \
            "docker_daemon_down"
        return 1
    fi
    
    # Check container status
    local containers=(
        "ecommerce-analytics"
        "ecommerce-dashboard" 
        "ecommerce-integration"
        "ecommerce-frontend"
        "ecommerce-postgres"
        "ecommerce-redis"
    )
    
    for container in "${containers[@]}"; do
        local status=$(docker inspect -f '{{.State.Status}}' "$container" 2>/dev/null || echo "not_found")
        
        if [[ "$status" != "running" ]]; then
            send_alert "critical" "Container Not Running" \
                "Container $container is $status" \
                "container_down_$container"
        fi
        
        # Check container health if health check is defined
        local health=$(docker inspect -f '{{.State.Health.Status}}' "$container" 2>/dev/null || echo "none")
        
        if [[ "$health" == "unhealthy" ]]; then
            send_alert "critical" "Container Unhealthy" \
                "Container $container health check is failing" \
                "container_unhealthy_$container"
        fi
    done
}

# Function to check database connection
check_database() {
    if [[ -z "$DB_PASSWORD" ]]; then
        return 0
    fi
    
    export PGPASSWORD="$DB_PASSWORD"
    local db_host=${DB_HOST:-localhost}
    local db_port=${DB_PORT:-5432}
    local db_name=${DB_NAME:-ecommerce_analytics}
    local db_user=${DB_USER:-postgres}
    
    # Check connection
    if ! psql -h "$db_host" -p "$db_port" -U "$db_user" -d "$db_name" -c "SELECT 1;" &> /dev/null; then
        send_alert "critical" "Database Connection Failed" \
            "Cannot connect to PostgreSQL database" \
            "database_connection"
        return 1
    fi
    
    # Check database size
    local db_size=$(psql -h "$db_host" -p "$db_port" -U "$db_user" -d "$db_name" -tAc "
        SELECT pg_size_pretty(pg_database_size('$db_name'));
    " 2>/dev/null || echo "unknown")
    
    print_debug "Database size: $db_size"
    
    # Check active connections
    local active_connections=$(psql -h "$db_host" -p "$db_port" -U "$db_user" -d "$db_name" -tAc "
        SELECT COUNT(*) FROM pg_stat_activity WHERE state = 'active';
    " 2>/dev/null || echo "0")
    
    # Alert if too many active connections
    if [[ $active_connections -gt 50 ]]; then
        send_alert "warning" "High Database Connections" \
            "Database has $active_connections active connections" \
            "high_db_connections"
    fi
}

# Function to check Redis connection
check_redis() {
    local redis_host=${REDIS_HOST:-localhost}
    local redis_port=${REDIS_PORT:-6379}
    
    # Check Redis connection
    if ! redis-cli -h "$redis_host" -p "$redis_port" ping &> /dev/null; then
        send_alert "critical" "Redis Connection Failed" \
            "Cannot connect to Redis server" \
            "redis_connection"
        return 1
    fi
    
    # Check Redis memory usage
    local redis_memory=$(redis-cli -h "$redis_host" -p "$redis_port" info memory | grep "used_memory_human:" | cut -d: -f2 | tr -d '\r\n')
    print_debug "Redis memory usage: $redis_memory"
    
    # Check Redis connected clients
    local redis_clients=$(redis-cli -h "$redis_host" -p "$redis_port" info clients | grep "connected_clients:" | cut -d: -f2 | tr -d '\r\n')
    
    if [[ $redis_clients -gt 100 ]]; then
        send_alert "warning" "High Redis Connections" \
            "Redis has $redis_clients connected clients" \
            "high_redis_connections"
    fi
}

# Function to collect metrics
collect_metrics() {
    local timestamp=$(date -Iseconds)
    local metrics="{
        \"timestamp\": \"$timestamp\",
        \"system\": {
            \"cpu_usage\": $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | sed 's/%us,//' | sed 's/\..*//' || echo 0),
            \"memory_usage\": $(free | grep '^Mem:' | awk '{printf "%.1f", $3/$2 * 100.0}'),
            \"load_average\": \"$(uptime | awk -F'load average:' '{print $2}' | xargs)\",
            \"disk_usage\": $(df -h / | awk 'NR==2 {print $5}' | sed 's/%//' || echo 0)
        },
        \"services\": {"
    
    local service_metrics=""
    for service in "${!SERVICE_ENDPOINTS[@]}"; do
        local endpoint="${SERVICE_ENDPOINTS[$service]}"
        local start_time=$(date +%s%3N)
        local http_code=$(curl -s -o /dev/null -w "%{http_code}" --max-time 5 "$endpoint" 2>/dev/null || echo "000")
        local end_time=$(date +%s%3N)
        local response_time=$((end_time - start_time))
        
        local status="down"
        if [[ "$http_code" == "200" ]]; then
            status="up"
        fi
        
        if [[ -n "$service_metrics" ]]; then
            service_metrics+=", "
        fi
        
        service_metrics+="\"$service\": {
            \"status\": \"$status\",
            \"response_time\": $response_time,
            \"http_code\": $http_code
        }"
    done
    
    metrics+="$service_metrics"
    metrics+="}}"
    
    echo "$metrics" > "$METRICS_FILE"
    
    # Send metrics to external systems
    if [[ -n "$DATADOG_API_KEY" ]]; then
        send_datadog_metrics "$metrics"
    fi
}

# Function to send metrics to DataDog
send_datadog_metrics() {
    local metrics="$1"
    
    # Extract metrics and send to DataDog
    local cpu_usage=$(echo "$metrics" | jq -r '.system.cpu_usage')
    local memory_usage=$(echo "$metrics" | jq -r '.system.memory_usage')
    local timestamp=$(date +%s)
    
    curl -s -X POST "https://api.datadoghq.com/api/v1/metrics" \
        -H "Content-Type: application/json" \
        -H "DD-API-KEY: $DATADOG_API_KEY" \
        --data "{
            \"series\": [
                {
                    \"metric\": \"ecommerce.system.cpu_usage\",
                    \"points\": [[$timestamp, $cpu_usage]],
                    \"host\": \"$(hostname)\"
                },
                {
                    \"metric\": \"ecommerce.system.memory_usage\",
                    \"points\": [[$timestamp, $memory_usage]],
                    \"host\": \"$(hostname)\"
                }
            ]
        }" &> /dev/null || print_debug "Failed to send DataDog metrics"
}

# Function to get system summary
get_system_summary() {
    echo "CPU: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | sed 's/%us,//' || echo "N/A")%"
    echo "Memory: $(free | grep '^Mem:' | awk '{printf "%.1f", $3/$2 * 100.0}')%"
    echo "Load: $(uptime | awk -F'load average:' '{print $2}' | xargs)"
    echo "Disk: $(df -h / | awk 'NR==2 {print $5}' || echo "N/A")"
    echo "Uptime: $(uptime | awk '{print $3,$4}' | sed 's/,//')"
}

# Function to run single check
run_single_check() {
    print_status "Running system monitoring check..."
    
    # Check system resources
    check_system_resources
    
    # Check services
    for service in "${!SERVICE_ENDPOINTS[@]}"; do
        check_service_health "$service"
    done
    
    # Check Docker services
    check_docker_services
    
    # Check database
    check_database
    
    # Check Redis
    check_redis
    
    # Collect metrics
    collect_metrics
    
    print_status "Monitoring check completed"
}

# Function to run daemon mode
run_daemon() {
    print_status "Starting monitoring daemon (interval: ${CHECK_INTERVAL}s)"
    
    # Create PID file
    echo $$ > /var/run/ecommerce-monitor.pid
    
    # Cleanup function
    cleanup() {
        print_status "Shutting down monitoring daemon"
        rm -f /var/run/ecommerce-monitor.pid
        exit 0
    }
    
    # Set up signal handlers
    trap cleanup SIGTERM SIGINT
    
    while true; do
        run_single_check
        sleep "$CHECK_INTERVAL"
    done
}

# Main function
main() {
    print_status "E-commerce Analytics Platform Monitoring"
    print_status "Check interval: ${CHECK_INTERVAL}s"
    print_status "CPU threshold: ${CPU_THRESHOLD}%"
    print_status "Memory threshold: ${MEMORY_THRESHOLD}%"
    print_status "Disk threshold: ${DISK_THRESHOLD}%"
    
    if [[ "$DAEMON_MODE" == "true" ]]; then
        run_daemon
    else
        run_single_check
    fi
}

# Check for required tools
if ! command -v curl &> /dev/null; then
    print_error "curl is required but not installed"
    exit 1
fi

if ! command -v jq &> /dev/null; then
    print_warning "jq is not installed - some features may not work"
fi

# Run main function
main "$@"