#!/bin/bash

# Database restore script for e-commerce analytics platform
# Supports various backup formats and restoration strategies

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BACKUP_DIR="/opt/backups"
LOG_FILE="/var/log/restore.log"

# Default values
RESTORE_TYPE="full"
BACKUP_FILE=""
TARGET_DB=""
DROP_EXISTING=false
DRY_RUN=false
QUIET=false
VERIFY_RESTORE=true
PARALLEL_JOBS=4
PRE_RESTORE_BACKUP=true

# Function to print colored output
print_status() {
    [[ "$QUIET" == "true" ]] && return
    echo -e "${GREEN}[INFO]${NC} $1" | tee -a "$LOG_FILE"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

print_debug() {
    [[ "$QUIET" == "true" ]] && return
    echo -e "${BLUE}[DEBUG]${NC} $1" | tee -a "$LOG_FILE"
}

# Function to show usage
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS] BACKUP_FILE

Restore database from backup for the e-commerce analytics platform.

OPTIONS:
    -t, --type TYPE           Restore type: full, schema, data (default: full)
    -d, --database DB         Target database name
    -D, --drop                Drop existing database before restore
    -n, --dry-run             Perform a dry run
    -q, --quiet               Quiet mode
    -v, --no-verify           Skip restore verification
    -j, --jobs JOBS           Number of parallel jobs (default: 4)
    -b, --no-backup           Skip pre-restore backup
    -h, --help                Show this help message

RESTORE TYPES:
    full                      Complete database restore
    schema                    Schema-only restore
    data                      Data-only restore

BACKUP_FILE:
    Path to backup file to restore from

EXAMPLES:
    $0 /opt/backups/full_20240101_120000.sql.gz
    $0 -t schema backup.sql   # Schema-only restore
    $0 -D -d test_db backup.sql # Drop existing DB and restore to test_db
    $0 -j 8 large_backup.sql  # Use 8 parallel jobs

ENVIRONMENT VARIABLES:
    DB_HOST                   Database host (default: localhost)
    DB_PORT                   Database port (default: 5432)
    DB_NAME                   Database name (default: ecommerce_analytics)
    DB_USER                   Database user (default: postgres)
    DB_PASSWORD               Database password (required)
    BACKUP_ENCRYPTION_KEY     Encryption key for encrypted backups
EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -t|--type)
            RESTORE_TYPE="$2"
            shift 2
            ;;
        -d|--database)
            TARGET_DB="$2"
            shift 2
            ;;
        -D|--drop)
            DROP_EXISTING=true
            shift
            ;;
        -n|--dry-run)
            DRY_RUN=true
            shift
            ;;
        -q|--quiet)
            QUIET=true
            shift
            ;;
        -v|--no-verify)
            VERIFY_RESTORE=false
            shift
            ;;
        -j|--jobs)
            PARALLEL_JOBS="$2"
            shift 2
            ;;
        -b|--no-backup)
            PRE_RESTORE_BACKUP=false
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        -*)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
        *)
            BACKUP_FILE="$1"
            shift
            ;;
    esac
done

# Validate inputs
if [[ -z "$BACKUP_FILE" ]]; then
    print_error "BACKUP_FILE is required"
    show_usage
    exit 1
fi

if [[ "$RESTORE_TYPE" != "full" && "$RESTORE_TYPE" != "schema" && "$RESTORE_TYPE" != "data" ]]; then
    print_error "Invalid restore type: $RESTORE_TYPE"
    exit 1
fi

# Set database connection parameters
DB_HOST=${DB_HOST:-localhost}
DB_PORT=${DB_PORT:-5432}
DB_NAME=${DB_NAME:-ecommerce_analytics}
DB_USER=${DB_USER:-postgres}
TARGET_DB=${TARGET_DB:-$DB_NAME}

if [[ -z "$DB_PASSWORD" ]]; then
    print_error "DB_PASSWORD environment variable is required"
    exit 1
fi

# Initialize logging
mkdir -p "$(dirname "$LOG_FILE")"

print_status "Starting restore process"
print_status "Type: $RESTORE_TYPE"
print_status "Backup file: $BACKUP_FILE"
print_status "Target database: $TARGET_DB"
print_status "Drop existing: $DROP_EXISTING"

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check required commands
    local required_commands=("pg_restore" "psql" "createdb" "dropdb")
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" &> /dev/null; then
            print_error "Required command not found: $cmd"
            exit 1
        fi
    done
    
    # Check backup file exists
    if [[ ! -f "$BACKUP_FILE" ]]; then
        print_error "Backup file not found: $BACKUP_FILE"
        exit 1
    fi
    
    # Check if backup file is readable
    if [[ ! -r "$BACKUP_FILE" ]]; then
        print_error "Backup file is not readable: $BACKUP_FILE"
        exit 1
    fi
    
    # Check database connectivity
    export PGPASSWORD="$DB_PASSWORD"
    if ! psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d postgres -c "SELECT 1;" &> /dev/null; then
        print_error "Cannot connect to database server"
        exit 1
    fi
    
    # Check if target database exists
    local db_exists=$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d postgres -tAc "SELECT 1 FROM pg_database WHERE datname='$TARGET_DB';" 2>/dev/null || echo "")
    
    if [[ -n "$db_exists" ]]; then
        if [[ "$DROP_EXISTING" == "false" ]]; then
            print_warning "Target database '$TARGET_DB' already exists"
            echo "Use --drop to drop existing database or specify different target"
            read -p "Continue with existing database? (y/N): " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                exit 1
            fi
        fi
    fi
    
    print_status "Prerequisites check passed"
}

# Function to detect backup format
detect_backup_format() {
    local file="$BACKUP_FILE"
    
    # Handle compressed files
    if [[ "$file" == *.gz ]]; then
        # Check if it's a PostgreSQL custom format
        if zcat "$file" | head -c 5 | grep -q "PGDMP"; then
            echo "custom_compressed"
        else
            echo "sql_compressed"
        fi
    elif [[ "$file" == *.gpg ]]; then
        if [[ -z "$BACKUP_ENCRYPTION_KEY" ]]; then
            print_error "BACKUP_ENCRYPTION_KEY required for encrypted backup"
            exit 1
        fi
        
        # Decrypt and check format
        if gpg --batch --quiet --decrypt --passphrase "$BACKUP_ENCRYPTION_KEY" "$file" | head -c 5 | grep -q "PGDMP"; then
            echo "custom_encrypted"
        else
            echo "sql_encrypted"
        fi
    else
        # Check if it's a PostgreSQL custom format
        if head -c 5 "$file" | grep -q "PGDMP"; then
            echo "custom"
        else
            echo "sql"
        fi
    fi
}

# Function to prepare backup file for restore
prepare_backup_file() {
    local format=$(detect_backup_format)
    local temp_file=""
    
    print_status "Detected backup format: $format"
    
    case "$format" in
        "custom"|"sql")
            echo "$BACKUP_FILE"
            ;;
        "custom_compressed"|"sql_compressed")
            temp_file="/tmp/restore_$(date +%s).sql"
            print_status "Decompressing backup file..."
            if [[ "$DRY_RUN" == "true" ]]; then
                print_debug "DRY RUN: Would decompress to $temp_file"
                echo "$temp_file"
            else
                zcat "$BACKUP_FILE" > "$temp_file"
                echo "$temp_file"
            fi
            ;;
        "custom_encrypted"|"sql_encrypted")
            temp_file="/tmp/restore_$(date +%s).sql"
            print_status "Decrypting backup file..."
            if [[ "$DRY_RUN" == "true" ]]; then
                print_debug "DRY RUN: Would decrypt to $temp_file"
                echo "$temp_file"
            else
                gpg --batch --quiet --decrypt --passphrase "$BACKUP_ENCRYPTION_KEY" "$BACKUP_FILE" > "$temp_file"
                echo "$temp_file"
            fi
            ;;
        *)
            print_error "Unknown backup format"
            exit 1
            ;;
    esac
}

# Function to create pre-restore backup
create_pre_restore_backup() {
    if [[ "$PRE_RESTORE_BACKUP" == "false" ]]; then
        return 0
    fi
    
    print_status "Creating pre-restore backup..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_debug "DRY RUN: Would create pre-restore backup"
        return 0
    fi
    
    # Check if target database exists
    local db_exists=$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d postgres -tAc "SELECT 1 FROM pg_database WHERE datname='$TARGET_DB';" 2>/dev/null || echo "")
    
    if [[ -z "$db_exists" ]]; then
        print_debug "Target database doesn't exist, skipping pre-restore backup"
        return 0
    fi
    
    local backup_file="$BACKUP_DIR/pre_restore_$(date +%Y%m%d_%H%M%S).sql"
    mkdir -p "$BACKUP_DIR"
    
    if ! pg_dump -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$TARGET_DB" --format=custom --compress=9 > "$backup_file"; then
        print_error "Pre-restore backup failed"
        exit 1
    fi
    
    print_status "Pre-restore backup created: $backup_file"
    export PRE_RESTORE_BACKUP_FILE="$backup_file"
}

# Function to drop existing database
drop_database() {
    if [[ "$DROP_EXISTING" == "false" ]]; then
        return 0
    fi
    
    print_status "Dropping existing database: $TARGET_DB"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_debug "DRY RUN: Would drop database $TARGET_DB"
        return 0
    fi
    
    # Terminate active connections
    psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d postgres -c "
        SELECT pg_terminate_backend(pid)
        FROM pg_stat_activity
        WHERE datname = '$TARGET_DB' AND pid <> pg_backend_pid();
    " &> /dev/null || true
    
    # Drop database
    if ! dropdb -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" "$TARGET_DB" 2>/dev/null; then
        print_warning "Failed to drop database (may not exist)"
    fi
}

# Function to create target database
create_database() {
    print_status "Creating target database: $TARGET_DB"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_debug "DRY RUN: Would create database $TARGET_DB"
        return 0
    fi
    
    # Check if database already exists
    local db_exists=$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d postgres -tAc "SELECT 1 FROM pg_database WHERE datname='$TARGET_DB';" 2>/dev/null || echo "")
    
    if [[ -z "$db_exists" ]]; then
        if ! createdb -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" "$TARGET_DB"; then
            print_error "Failed to create database: $TARGET_DB"
            exit 1
        fi
        print_status "Database created: $TARGET_DB"
    else
        print_debug "Database already exists: $TARGET_DB"
    fi
}

# Function to restore from custom format
restore_custom_format() {
    local backup_file="$1"
    
    print_status "Restoring from PostgreSQL custom format..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_debug "DRY RUN: Would restore from custom format backup"
        return 0
    fi
    
    local pg_restore_options=(
        "-h" "$DB_HOST"
        "-p" "$DB_PORT"
        "-U" "$DB_USER"
        "-d" "$TARGET_DB"
        "--verbose"
        "--no-password"
        "--jobs=$PARALLEL_JOBS"
    )
    
    # Add restore type specific options
    case "$RESTORE_TYPE" in
        "schema")
            pg_restore_options+=("--schema-only")
            ;;
        "data")
            pg_restore_options+=("--data-only")
            ;;
        "full")
            # No additional options for full restore
            ;;
    esac
    
    # Perform restore
    if ! pg_restore "${pg_restore_options[@]}" "$backup_file"; then
        print_error "Custom format restore failed"
        return 1
    fi
    
    print_status "Custom format restore completed"
}

# Function to restore from SQL format
restore_sql_format() {
    local backup_file="$1"
    
    print_status "Restoring from SQL format..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_debug "DRY RUN: Would restore from SQL format backup"
        return 0
    fi
    
    # SQL format restore
    if ! psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$TARGET_DB" -f "$backup_file"; then
        print_error "SQL format restore failed"
        return 1
    fi
    
    print_status "SQL format restore completed"
}

# Function to verify restore
verify_restore() {
    if [[ "$VERIFY_RESTORE" == "false" ]]; then
        return 0
    fi
    
    print_status "Verifying restore..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_debug "DRY RUN: Would verify restore"
        return 0
    fi
    
    # Check database connectivity
    if ! psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$TARGET_DB" -c "SELECT 1;" &> /dev/null; then
        print_error "Cannot connect to restored database"
        return 1
    fi
    
    # Check if main tables exist
    local expected_tables=(
        "conversions"
        "attribution_touchpoints"
        "customer_events"
        "cohorts"
        "cohort_members"
        "forecast_data"
    )
    
    for table in "${expected_tables[@]}"; do
        local table_exists=$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$TARGET_DB" -tAc "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = '$table');" 2>/dev/null || echo "f")
        
        if [[ "$table_exists" != "t" ]]; then
            print_warning "Expected table not found: $table"
        else
            print_debug "Table verified: $table"
        fi
    done
    
    # Check data integrity
    local total_records=$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$TARGET_DB" -tAc "
        SELECT COALESCE(
            (SELECT COUNT(*) FROM conversions) +
            (SELECT COUNT(*) FROM customer_events) +
            (SELECT COUNT(*) FROM cohorts),
            0
        );
    " 2>/dev/null || echo "0")
    
    print_status "Restore verification completed"
    print_status "Total records in main tables: $total_records"
    
    if [[ "$total_records" -eq 0 ]]; then
        print_warning "No data found in main tables"
    fi
}

# Function to update database statistics
update_statistics() {
    print_status "Updating database statistics..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_debug "DRY RUN: Would update database statistics"
        return 0
    fi
    
    # Analyze all tables to update statistics
    psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$TARGET_DB" -c "ANALYZE;" &> /dev/null
    
    print_status "Database statistics updated"
}

# Function to cleanup temporary files
cleanup_temp_files() {
    if [[ -n "$TEMP_BACKUP_FILE" && -f "$TEMP_BACKUP_FILE" ]]; then
        rm -f "$TEMP_BACKUP_FILE"
        print_debug "Cleaned up temporary file: $TEMP_BACKUP_FILE"
    fi
}

# Function to rollback on failure
rollback_on_failure() {
    if [[ -n "$PRE_RESTORE_BACKUP_FILE" && -f "$PRE_RESTORE_BACKUP_FILE" ]]; then
        print_status "Rolling back to pre-restore backup..."
        
        # Drop current database
        dropdb -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" "$TARGET_DB" 2>/dev/null || true
        
        # Recreate database
        createdb -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" "$TARGET_DB"
        
        # Restore from pre-restore backup
        pg_restore -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$TARGET_DB" "$PRE_RESTORE_BACKUP_FILE"
        
        print_status "Rollback completed"
    fi
}

# Main restore function
main() {
    # Check prerequisites
    check_prerequisites
    
    # Create pre-restore backup
    create_pre_restore_backup
    
    # Prepare backup file
    local prepared_file=$(prepare_backup_file)
    export TEMP_BACKUP_FILE="$prepared_file"
    
    # Drop existing database if requested
    drop_database
    
    # Create target database
    create_database
    
    # Detect backup format and restore accordingly
    local format=$(detect_backup_format)
    
    case "$format" in
        "custom"|"custom_compressed"|"custom_encrypted")
            restore_custom_format "$prepared_file"
            ;;
        "sql"|"sql_compressed"|"sql_encrypted")
            restore_sql_format "$prepared_file"
            ;;
        *)
            print_error "Unsupported backup format: $format"
            exit 1
            ;;
    esac
    
    # Verify restore
    verify_restore
    
    # Update statistics
    update_statistics
    
    # Cleanup
    cleanup_temp_files
    
    print_status "Restore completed successfully"
    print_status "Database: $TARGET_DB"
    print_status "Backup file: $BACKUP_FILE"
    
    # Log restore completion
    echo "$(date): Restore completed - $BACKUP_FILE to $TARGET_DB" >> "$LOG_FILE"
}

# Set error handling with rollback
set -E
trap 'print_error "Restore failed at line $LINENO"; rollback_on_failure; cleanup_temp_files' ERR
trap cleanup_temp_files EXIT

# Run main function
main "$@"