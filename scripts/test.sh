#!/bin/bash

set -e

echo "🧪 Running tests for all services..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Test results
FAILED_SERVICES=()
PASSED_SERVICES=()

# Function to run tests for a service
run_tests() {
    local service=$1
    echo -e "${YELLOW}Testing $service...${NC}"
    
    cd "services/$service"
    
    if [ "$service" = "link-tracking" ]; then
        # Go tests
        if go test -v ./...; then
            echo -e "${GREEN}✅ $service tests passed${NC}"
            PASSED_SERVICES+=("$service")
        else
            echo -e "${RED}❌ $service tests failed${NC}"
            FAILED_SERVICES+=("$service")
        fi
        
        # Go vet
        if go vet ./...; then
            echo -e "${GREEN}✅ $service vet passed${NC}"
        else
            echo -e "${RED}❌ $service vet failed${NC}"
            FAILED_SERVICES+=("$service (vet)")
        fi
    else
        # Node.js tests
        if [ -f "package.json" ]; then
            # Install dependencies if node_modules doesn't exist
            if [ ! -d "node_modules" ]; then
                echo "📦 Installing dependencies for $service..."
                npm ci
            fi
            
            # Run tests
            if npm test; then
                echo -e "${GREEN}✅ $service tests passed${NC}"
                PASSED_SERVICES+=("$service")
            else
                echo -e "${RED}❌ $service tests failed${NC}"
                FAILED_SERVICES+=("$service")
            fi
            
            # Run linting
            if npm run lint; then
                echo -e "${GREEN}✅ $service linting passed${NC}"
            else
                echo -e "${RED}❌ $service linting failed${NC}"
                FAILED_SERVICES+=("$service (lint)")
            fi
        else
            echo -e "${YELLOW}⚠️  No package.json found for $service, skipping...${NC}"
        fi
    fi
    
    cd ../..
}

# Run tests for each service
for service in link-tracking integration analytics dashboard; do
    if [ -d "services/$service" ]; then
        run_tests "$service"
        echo ""
    else
        echo -e "${YELLOW}⚠️  Service directory not found: services/$service${NC}"
    fi
done

# Summary
echo "📊 Test Summary:"
echo "================"

if [ ${#PASSED_SERVICES[@]} -gt 0 ]; then
    echo -e "${GREEN}✅ Passed (${#PASSED_SERVICES[@]}):${NC}"
    for service in "${PASSED_SERVICES[@]}"; do
        echo "   - $service"
    done
fi

if [ ${#FAILED_SERVICES[@]} -gt 0 ]; then
    echo -e "${RED}❌ Failed (${#FAILED_SERVICES[@]}):${NC}"
    for service in "${FAILED_SERVICES[@]}"; do
        echo "   - $service"
    done
    echo ""
    echo -e "${RED}Some tests failed. Please fix the issues before deploying.${NC}"
    exit 1
else
    echo ""
    echo -e "${GREEN}🎉 All tests passed!${NC}"
fi