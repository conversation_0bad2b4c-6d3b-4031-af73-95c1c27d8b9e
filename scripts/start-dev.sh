#!/bin/bash

# E-commerce Analytics SaaS Development Startup Script
echo "🚀 Starting E-commerce Analytics SaaS Development Environment"

# Check if <PERSON><PERSON>/<PERSON> is available
if command -v podman &> /dev/null; then
    CONTAINER_CMD="podman"
    COMPOSE_CMD="podman-compose"
elif command -v docker &> /dev/null; then
    CONTAINER_CMD="docker"
    COMPOSE_CMD="docker-compose"
else
    echo "❌ Neither <PERSON><PERSON> nor <PERSON><PERSON> found. Please install one of them."
    exit 1
fi

echo "📦 Using $CONTAINER_CMD with $COMPOSE_CMD"

# Navigate to project root
cd "$(dirname "$0")/.."

# Install dependencies for workspace
echo "📥 Installing workspace dependencies..."
if command -v bun &> /dev/null; then
    bun install
elif command -v npm &> /dev/null; then
    npm install
else
    echo "❌ Neither Bun nor npm found. Please install Node.js and npm."
    exit 1
fi

# Build shared types package
echo "🔧 Building shared types package..."
cd packages/shared-types
if command -v bun &> /dev/null; then
    bun run build
else
    npm run build
fi
cd ../..

# Start the services
echo "🐳 Starting microservices with $COMPOSE_CMD..."
$COMPOSE_CMD up -d postgres redis

echo "⏳ Waiting for database to be ready..."
sleep 10

# Run migrations if needed
echo "🗄️ Running database migrations..."
./scripts/migrate.sh

# Start all services
echo "🚀 Starting all services..."
$COMPOSE_CMD up -d

# Show status
echo ""
echo "🎉 E-commerce Analytics SaaS is starting up!"
echo ""
echo "📊 Services:"
echo "  - Frontend (React):     http://localhost:5173"
echo "  - Dashboard API:        http://localhost:3000"
echo "  - Analytics API:        http://localhost:3002"
echo "  - Link Tracking API:    http://localhost:8080"
echo "  - Integration API:      http://localhost:3001"
echo "  - PostgreSQL:           localhost:5432"
echo "  - Redis:                localhost:6379"
echo ""
echo "🔍 Monitoring:"
echo "  - Prometheus:           http://localhost:9090 (with --profile monitoring)"
echo "  - Grafana:              http://localhost:3001 (with --profile monitoring)"
echo ""
echo "📝 Logs: $COMPOSE_CMD logs -f [service-name]"
echo "🛑 Stop: $COMPOSE_CMD down"
echo ""

# Wait a bit more and show service status
sleep 5
echo "📋 Service Status:"
$COMPOSE_CMD ps