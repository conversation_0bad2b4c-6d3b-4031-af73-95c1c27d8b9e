#!/bin/bash

# Demo Data Setup Script for E-commerce Analytics Platform
# Creates realistic sample data for development, testing, and demonstrations

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Default values
DATA_SIZE="medium"
DAYS_BACK=90
DRY_RUN=false
CLEAN_EXISTING=false
ENVIRONMENT="development"
SEED=12345

# Database connection
DB_HOST=${DB_HOST:-localhost}
DB_PORT=${DB_PORT:-5432}
DB_NAME=${DB_NAME:-ecommerce_analytics}
DB_USER=${DB_USER:-postgres}

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# Function to show usage
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Generate demo data for the e-commerce analytics platform.

OPTIONS:
    -s, --size SIZE           Data size: small, medium, large (default: medium)
    -d, --days DAYS           Days of historical data (default: 90)
    -e, --environment ENV     Environment: development, staging, demo (default: development)
    --seed SEED               Random seed for reproducible data (default: 12345)
    -c, --clean               Clean existing demo data before generating new
    -n, --dry-run             Show what would be generated without creating data
    -h, --help                Show this help message

DATA SIZES:
    small                     ~1,000 customers, ~5,000 orders, ~20,000 events
    medium                    ~10,000 customers, ~50,000 orders, ~200,000 events
    large                     ~100,000 customers, ~500,000 orders, ~2,000,000 events

EXAMPLES:
    $0                        # Generate medium dataset for 90 days
    $0 -s large -d 180        # Generate large dataset for 6 months
    $0 -c -s small            # Clean existing data and generate small dataset
    $0 -n -s large            # Dry run for large dataset

ENVIRONMENT VARIABLES:
    DB_HOST                   Database host (default: localhost)
    DB_PORT                   Database port (default: 5432)
    DB_NAME                   Database name (default: ecommerce_analytics)
    DB_USER                   Database user (default: postgres)
    DB_PASSWORD               Database password (required)
EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -s|--size)
            DATA_SIZE="$2"
            shift 2
            ;;
        -d|--days)
            DAYS_BACK="$2"
            shift 2
            ;;
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        --seed)
            SEED="$2"
            shift 2
            ;;
        -c|--clean)
            CLEAN_EXISTING=true
            shift
            ;;
        -n|--dry-run)
            DRY_RUN=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate inputs
if [[ "$DATA_SIZE" != "small" && "$DATA_SIZE" != "medium" && "$DATA_SIZE" != "large" ]]; then
    print_error "Invalid data size: $DATA_SIZE"
    exit 1
fi

if [[ "$ENVIRONMENT" != "development" && "$ENVIRONMENT" != "staging" && "$ENVIRONMENT" != "demo" ]]; then
    print_error "Invalid environment: $ENVIRONMENT"
    exit 1
fi

if [[ -z "$DB_PASSWORD" ]]; then
    print_error "DB_PASSWORD environment variable is required"
    exit 1
fi

# Set data quantities based on size
case "$DATA_SIZE" in
    "small")
        CUSTOMERS=1000
        ORDERS_PER_CUSTOMER=5
        EVENTS_PER_ORDER=4
        PRODUCTS=100
        ;;
    "medium")
        CUSTOMERS=10000
        ORDERS_PER_CUSTOMER=5
        EVENTS_PER_ORDER=4
        PRODUCTS=500
        ;;
    "large")
        CUSTOMERS=100000
        ORDERS_PER_CUSTOMER=5
        EVENTS_PER_ORDER=4
        PRODUCTS=2000
        ;;
esac

TOTAL_ORDERS=$((CUSTOMERS * ORDERS_PER_CUSTOMER))
TOTAL_EVENTS=$((TOTAL_ORDERS * EVENTS_PER_ORDER))

print_status "Demo Data Generation for E-commerce Analytics Platform"
print_status "Size: $DATA_SIZE"
print_status "Environment: $ENVIRONMENT"
print_status "Days back: $DAYS_BACK"
print_status "Customers: $CUSTOMERS"
print_status "Orders: $TOTAL_ORDERS"
print_status "Events: $TOTAL_EVENTS"
print_status "Products: $PRODUCTS"
print_status "Dry Run: $DRY_RUN"

# Function to check database connection
check_database() {
    print_status "Checking database connection..."
    
    export PGPASSWORD="$DB_PASSWORD"
    if ! psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT 1;" &> /dev/null; then
        print_error "Cannot connect to database"
        print_error "Host: $DB_HOST:$DB_PORT"
        print_error "Database: $DB_NAME"
        print_error "User: $DB_USER"
        exit 1
    fi
    
    print_status "Database connection successful"
}

# Function to clean existing demo data
clean_demo_data() {
    if [[ "$CLEAN_EXISTING" == "false" ]]; then
        return 0
    fi
    
    print_status "Cleaning existing demo data..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_debug "DRY RUN: Would clean existing demo data"
        return 0
    fi
    
    # Clean in reverse dependency order
    psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" << EOF
-- Clean demo data (preserve real customer data)
DELETE FROM attribution_touchpoints WHERE created_at >= NOW() - INTERVAL '$DAYS_BACK days';
DELETE FROM customer_events WHERE created_at >= NOW() - INTERVAL '$DAYS_BACK days';
DELETE FROM conversions WHERE created_at >= NOW() - INTERVAL '$DAYS_BACK days';
DELETE FROM cohort_members WHERE created_at >= NOW() - INTERVAL '$DAYS_BACK days';
DELETE FROM cohorts WHERE created_at >= NOW() - INTERVAL '$DAYS_BACK days';
DELETE FROM forecast_data WHERE created_at >= NOW() - INTERVAL '$DAYS_BACK days';

-- Reset sequences
SELECT setval('conversions_id_seq', COALESCE((SELECT MAX(id) FROM conversions), 1));
SELECT setval('customer_events_id_seq', COALESCE((SELECT MAX(id) FROM customer_events), 1));
SELECT setval('attribution_touchpoints_id_seq', COALESCE((SELECT MAX(id) FROM attribution_touchpoints), 1));
SELECT setval('cohorts_id_seq', COALESCE((SELECT MAX(id) FROM cohorts), 1));
SELECT setval('forecast_data_id_seq', COALESCE((SELECT MAX(id) FROM forecast_data), 1));
EOF
    
    print_status "Demo data cleaned"
}

# Function to generate sample products
generate_products() {
    print_status "Generating sample products..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_debug "DRY RUN: Would generate $PRODUCTS products"
        return 0
    fi
    
    # Create temporary file with product data
    local temp_file="/tmp/demo_products.csv"
    
    cat > "$temp_file" << EOF
id,name,category,price,cost
EOF

    # Generate products using a simple script
    python3 << PYTHON_SCRIPT
import random
import csv

# Set seed for reproducibility
random.seed($SEED)

categories = [
    'Electronics', 'Clothing', 'Home & Garden', 'Sports & Outdoors',
    'Books', 'Beauty & Personal Care', 'Toys & Games', 'Automotive',
    'Health & Wellness', 'Food & Beverages'
]

product_names = {
    'Electronics': ['Wireless Headphones', 'Smartphone Case', 'Bluetooth Speaker', 'Tablet Stand', 'USB Cable', 'Power Bank', 'Smart Watch', 'Webcam'],
    'Clothing': ['Cotton T-Shirt', 'Denim Jeans', 'Running Shoes', 'Winter Jacket', 'Baseball Cap', 'Dress Shirt', 'Sneakers', 'Hoodie'],
    'Home & Garden': ['Coffee Maker', 'Table Lamp', 'Storage Box', 'Garden Hose', 'Throw Pillow', 'Wall Clock', 'Plant Pot', 'Candle'],
    'Sports & Outdoors': ['Yoga Mat', 'Water Bottle', 'Tennis Racket', 'Hiking Backpack', 'Bicycle Helmet', 'Camping Chair', 'Fitness Tracker'],
    'Books': ['Mystery Novel', 'Cookbook', 'Self-Help Guide', 'Science Fiction', 'Biography', 'Children\'s Book', 'Technical Manual'],
    'Beauty & Personal Care': ['Face Cream', 'Shampoo', 'Lip Balm', 'Sunscreen', 'Perfume', 'Hair Brush', 'Nail Polish'],
    'Toys & Games': ['Board Game', 'Puzzle', 'Action Figure', 'Building Blocks', 'Stuffed Animal', 'Remote Control Car'],
    'Automotive': ['Car Charger', 'Air Freshener', 'Floor Mats', 'Phone Mount', 'Tire Pressure Gauge', 'Jump Starter'],
    'Health & Wellness': ['Vitamin Supplement', 'Protein Powder', 'Essential Oil', 'First Aid Kit', 'Thermometer'],
    'Food & Beverages': ['Organic Tea', 'Protein Bar', 'Honey', 'Olive Oil', 'Spice Set', 'Coffee Beans']
}

with open('$temp_file', 'a', newline='') as csvfile:
    writer = csv.writer(csvfile)
    
    for i in range(1, $PRODUCTS + 1):
        category = random.choice(categories)
        base_name = random.choice(product_names[category])
        
        # Add variation to product names
        variations = ['Premium', 'Deluxe', 'Pro', 'Classic', 'Eco', 'Smart', 'Ultra']
        if random.random() < 0.3:
            name = f"{random.choice(variations)} {base_name}"
        else:
            name = base_name
            
        # Generate realistic prices based on category
        price_ranges = {
            'Electronics': (20, 500),
            'Clothing': (15, 200),
            'Home & Garden': (10, 150),
            'Sports & Outdoors': (25, 300),
            'Books': (10, 50),
            'Beauty & Personal Care': (5, 100),
            'Toys & Games': (15, 80),
            'Automotive': (20, 200),
            'Health & Wellness': (15, 120),
            'Food & Beverages': (5, 50)
        }
        
        min_price, max_price = price_ranges[category]
        price = round(random.uniform(min_price, max_price), 2)
        cost = round(price * random.uniform(0.3, 0.7), 2)  # 30-70% of price
        
        writer.writerow([f'prod_{i:06d}', name, category, price, cost])

print(f"Generated $PRODUCTS products")
PYTHON_SCRIPT

    print_status "Sample products generated"
}

# Function to generate customers
generate_customers() {
    print_status "Generating customers..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_debug "DRY RUN: Would generate $CUSTOMERS customers"
        return 0
    fi
    
    python3 << PYTHON_SCRIPT
import random
import psycopg2
from datetime import datetime, timedelta
import uuid

# Set seed for reproducibility
random.seed($SEED)

# Database connection
conn = psycopg2.connect(
    host='$DB_HOST',
    port='$DB_PORT',
    database='$DB_NAME',
    user='$DB_USER',
    password='$DB_PASSWORD'
)
cur = conn.cursor()

# Sample data
first_names = [
    'James', 'Mary', 'John', 'Patricia', 'Robert', 'Jennifer', 'Michael', 'Linda',
    'David', 'Elizabeth', 'William', 'Barbara', 'Richard', 'Susan', 'Joseph', 'Jessica',
    'Thomas', 'Sarah', 'Christopher', 'Karen', 'Charles', 'Helen', 'Daniel', 'Nancy',
    'Matthew', 'Betty', 'Anthony', 'Dorothy', 'Mark', 'Lisa', 'Donald', 'Sandra'
]

last_names = [
    'Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis',
    'Rodriguez', 'Martinez', 'Hernandez', 'Lopez', 'Gonzalez', 'Wilson', 'Anderson',
    'Thomas', 'Taylor', 'Moore', 'Jackson', 'Martin', 'Lee', 'Perez', 'Thompson',
    'White', 'Harris', 'Sanchez', 'Clark', 'Ramirez', 'Lewis', 'Robinson', 'Walker'
]

domains = [
    'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 'aol.com',
    'icloud.com', 'protonmail.com', 'company.com', 'business.net'
]

countries = ['US', 'CA', 'GB', 'AU', 'DE', 'FR', 'IT', 'ES', 'NL', 'SE']
sources = ['organic_search', 'paid_search', 'social_media', 'email', 'direct', 'referral']

# Generate registration dates over the past period
end_date = datetime.now()
start_date = end_date - timedelta(days=$DAYS_BACK)

customers_generated = 0
batch_size = 1000

for batch in range(0, $CUSTOMERS, batch_size):
    batch_customers = []
    
    for i in range(batch, min(batch + batch_size, $CUSTOMERS)):
        customer_id = f"cust_{i+1:06d}"
        first_name = random.choice(first_names)
        last_name = random.choice(last_names)
        email = f"{first_name.lower()}.{last_name.lower()}{random.randint(1, 999)}@{random.choice(domains)}"
        
        # Generate registration date with higher frequency in recent months
        days_ago = random.expovariate(1.0 / 30)  # Exponential distribution
        if days_ago > $DAYS_BACK:
            days_ago = random.randint(0, $DAYS_BACK)
        
        registration_date = end_date - timedelta(days=days_ago)
        
        customer_data = (
            customer_id,
            email,
            first_name,
            last_name,
            random.choice(countries),
            random.choice(sources),
            registration_date
        )
        
        batch_customers.append(customer_data)
    
    # Insert batch
    cur.executemany("""
        INSERT INTO conversions (
            customer_id, customer_email, customer_first_name, customer_last_name,
            customer_country, channel, created_at, updated_at
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
        ON CONFLICT (customer_id) DO NOTHING
    """, [(c[0], c[1], c[2], c[3], c[4], c[5], c[6], c[6]) for c in batch_customers])
    
    customers_generated += len(batch_customers)
    
    if customers_generated % 5000 == 0:
        print(f"Generated {customers_generated} customers...")

conn.commit()
cur.close()
conn.close()

print(f"Generated {customers_generated} customers")
PYTHON_SCRIPT

    print_status "Customers generated"
}

# Function to generate orders and events
generate_orders_events() {
    print_status "Generating orders and events..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_debug "DRY RUN: Would generate $TOTAL_ORDERS orders and $TOTAL_EVENTS events"
        return 0
    fi
    
    python3 << PYTHON_SCRIPT
import random
import psycopg2
from datetime import datetime, timedelta
import json

# Set seed for reproducibility
random.seed($SEED)

# Database connection
conn = psycopg2.connect(
    host='$DB_HOST',
    port='$DB_PORT',
    database='$DB_NAME',
    user='$DB_USER',
    password='$DB_PASSWORD'
)
cur = conn.cursor()

# Get customers
cur.execute("SELECT customer_id, created_at FROM conversions ORDER BY customer_id")
customers = cur.fetchall()

if not customers:
    print("No customers found. Generate customers first.")
    exit(1)

# Event types for customer journey
event_types = [
    'page_view', 'product_view', 'add_to_cart', 'remove_from_cart',
    'begin_checkout', 'add_payment_info', 'purchase'
]

channels = ['organic_search', 'paid_search', 'social_media', 'email', 'direct', 'referral']
sources = ['google', 'facebook', 'instagram', 'twitter', 'bing', 'yahoo', 'direct']
mediums = ['organic', 'cpc', 'social', 'email', 'none', 'referral']
devices = ['desktop', 'mobile', 'tablet']
browsers = ['chrome', 'firefox', 'safari', 'edge']
os_list = ['windows', 'macos', 'linux', 'ios', 'android']

# Generate orders for each customer
orders_generated = 0
events_generated = 0

for customer_id, registration_date in customers:
    # Number of orders for this customer (some customers have more orders)
    if random.random() < 0.1:  # 10% are power users
        num_orders = random.randint(8, 20)
    elif random.random() < 0.3:  # 30% are regular customers
        num_orders = random.randint(3, 8)
    else:  # 60% are occasional customers
        num_orders = random.randint(1, 3)
    
    if orders_generated >= $TOTAL_ORDERS:
        break
        
    customer_orders = []
    customer_events = []
    
    for order_num in range(num_orders):
        if orders_generated >= $TOTAL_ORDERS:
            break
            
        order_id = f"order_{orders_generated+1:08d}"
        
        # Order date after registration
        days_since_reg = (datetime.now() - registration_date).days
        if days_since_reg > 0:
            order_days_ago = random.randint(0, min(days_since_reg, $DAYS_BACK))
        else:
            order_days_ago = 0
            
        order_date = datetime.now() - timedelta(days=order_days_ago)
        
        # Order details
        num_items = random.randint(1, 5)
        total_amount = round(random.uniform(25, 500), 2)
        
        # Customer journey events leading to purchase
        session_id = f"session_{random.randint(100000, 999999)}"
        channel = random.choice(channels)
        source = random.choice(sources)
        medium = random.choice(mediums)
        device = random.choice(devices)
        browser = random.choice(browsers)
        os = random.choice(os_list)
        
        # Create conversion record
        customer_orders.append((
            customer_id, order_id, order_date, total_amount, num_items,
            channel, source, medium, session_id, order_date, order_date
        ))
        
        # Generate customer journey events
        journey_start = order_date - timedelta(minutes=random.randint(5, 120))
        
        # Page view
        customer_events.append((
            customer_id, session_id, 'page_view', journey_start,
            json.dumps({
                'page_url': '/',
                'page_title': 'Homepage',
                'referrer': f'https://{source}.com'
            }),
            0.0, 'USD', channel, source, medium, device, browser, os,
            journey_start, journey_start
        ))
        
        # Product views
        for _ in range(random.randint(1, 4)):
            event_time = journey_start + timedelta(minutes=random.randint(1, 30))
            product_id = f"prod_{random.randint(1, $PRODUCTS):06d}"
            
            customer_events.append((
                customer_id, session_id, 'product_view', event_time,
                json.dumps({
                    'product_id': product_id,
                    'product_name': f'Product {product_id}',
                    'product_price': round(random.uniform(10, 200), 2),
                    'category': random.choice(['Electronics', 'Clothing', 'Home'])
                }),
                0.0, 'USD', channel, source, medium, device, browser, os,
                event_time, event_time
            ))
        
        # Add to cart
        cart_time = journey_start + timedelta(minutes=random.randint(5, 45))
        customer_events.append((
            customer_id, session_id, 'add_to_cart', cart_time,
            json.dumps({
                'product_id': f"prod_{random.randint(1, $PRODUCTS):06d}",
                'quantity': random.randint(1, 3),
                'value': round(random.uniform(20, 100), 2)
            }),
            round(random.uniform(20, 100), 2), 'USD', channel, source, medium, device, browser, os,
            cart_time, cart_time
        ))
        
        # Checkout process
        checkout_time = cart_time + timedelta(minutes=random.randint(1, 15))
        customer_events.append((
            customer_id, session_id, 'begin_checkout', checkout_time,
            json.dumps({'cart_value': total_amount}),
            total_amount, 'USD', channel, source, medium, device, browser, os,
            checkout_time, checkout_time
        ))
        
        # Purchase
        customer_events.append((
            customer_id, session_id, 'purchase', order_date,
            json.dumps({
                'order_id': order_id,
                'items': num_items,
                'payment_method': random.choice(['credit_card', 'paypal', 'apple_pay'])
            }),
            total_amount, 'USD', channel, source, medium, device, browser, os,
            order_date, order_date
        ))
        
        orders_generated += 1
        events_generated += len(customer_events) - len(customer_events) + 5  # Approximate
    
    # Batch insert orders
    if customer_orders:
        cur.executemany("""
            INSERT INTO conversions (
                customer_id, order_id, order_date, revenue, items,
                channel, source, medium, session_id, created_at, updated_at
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            ON CONFLICT (order_id) DO NOTHING
        """, customer_orders)
    
    # Batch insert events
    if customer_events:
        cur.executemany("""
            INSERT INTO customer_events (
                customer_id, session_id, event_type, event_timestamp, properties,
                revenue, currency, channel, source, medium, device, browser, os,
                created_at, updated_at
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """, customer_events)
    
    if orders_generated % 1000 == 0:
        print(f"Generated {orders_generated} orders...")
        conn.commit()

conn.commit()
cur.close()
conn.close()

print(f"Generated {orders_generated} orders and customer journey events")
PYTHON_SCRIPT

    print_status "Orders and events generated"
}

# Function to generate attribution touchpoints
generate_attribution() {
    print_status "Generating attribution touchpoints..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_debug "DRY RUN: Would generate attribution touchpoints"
        return 0
    fi
    
    python3 << PYTHON_SCRIPT
import random
import psycopg2
from datetime import datetime, timedelta
import json

# Set seed for reproducibility
random.seed($SEED)

# Database connection
conn = psycopg2.connect(
    host='$DB_HOST',
    port='$DB_PORT',
    database='$DB_NAME',
    user='$DB_USER',
    password='$DB_PASSWORD'
)
cur = conn.cursor()

# Get conversions
cur.execute("""
    SELECT customer_id, order_id, order_date, revenue, channel, source, medium
    FROM conversions 
    WHERE order_id IS NOT NULL 
    ORDER BY order_date
""")
conversions = cur.fetchall()

touchpoints_generated = 0
channels = ['organic_search', 'paid_search', 'social_media', 'email', 'direct', 'referral']
attribution_models = ['first_touch', 'last_touch', 'linear', 'time_decay', 'position_based']

for customer_id, order_id, order_date, revenue, conv_channel, conv_source, conv_medium in conversions:
    # Generate 1-5 touchpoints leading to conversion
    num_touchpoints = random.randint(1, 5)
    touchpoints = []
    
    for i in range(num_touchpoints):
        # Touchpoint timing (days before conversion)
        days_before = random.randint(0, min(30, 7 * (num_touchpoints - i)))
        touchpoint_time = order_date - timedelta(days=days_before, hours=random.randint(0, 23))
        
        # Use conversion channel for last touchpoint, random for others
        if i == num_touchpoints - 1:
            channel = conv_channel
            source = conv_source
            medium = conv_medium
        else:
            channel = random.choice(channels)
            source = random.choice(['google', 'facebook', 'instagram', 'email', 'direct'])
            medium = random.choice(['organic', 'cpc', 'social', 'email', 'none'])
        
        # Attribution values for different models
        attribution_values = {}
        
        if num_touchpoints == 1:
            # Single touchpoint gets all credit
            for model in attribution_models:
                attribution_values[model] = float(revenue)
        else:
            # First touch
            attribution_values['first_touch'] = float(revenue) if i == 0 else 0.0
            
            # Last touch
            attribution_values['last_touch'] = float(revenue) if i == num_touchpoints - 1 else 0.0
            
            # Linear
            attribution_values['linear'] = float(revenue) / num_touchpoints
            
            # Time decay (more recent gets more credit)
            decay_factor = 0.5 ** (num_touchpoints - 1 - i)
            total_decay = sum(0.5 ** j for j in range(num_touchpoints))
            attribution_values['time_decay'] = float(revenue) * decay_factor / total_decay
            
            # Position based (40% first, 40% last, 20% middle)
            if i == 0:
                attribution_values['position_based'] = float(revenue) * 0.4
            elif i == num_touchpoints - 1:
                attribution_values['position_based'] = float(revenue) * 0.4
            else:
                attribution_values['position_based'] = float(revenue) * 0.2 / max(1, num_touchpoints - 2)
        
        touchpoints.append((
            customer_id, order_id, touchpoint_time, channel, source, medium,
            i + 1, json.dumps(attribution_values), touchpoint_time, touchpoint_time
        ))
    
    # Insert touchpoints
    cur.executemany("""
        INSERT INTO attribution_touchpoints (
            customer_id, conversion_id, touchpoint_timestamp, channel, source, medium,
            position, attribution_values, created_at, updated_at
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
    """, touchpoints)
    
    touchpoints_generated += len(touchpoints)
    
    if touchpoints_generated % 1000 == 0:
        print(f"Generated {touchpoints_generated} attribution touchpoints...")
        conn.commit()

conn.commit()
cur.close()
conn.close()

print(f"Generated {touchpoints_generated} attribution touchpoints")
PYTHON_SCRIPT

    print_status "Attribution touchpoints generated"
}

# Function to generate cohorts
generate_cohorts() {
    print_status "Generating cohort data..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_debug "DRY RUN: Would generate cohort data"
        return 0
    fi
    
    python3 << PYTHON_SCRIPT
import random
import psycopg2
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta

# Set seed for reproducibility
random.seed($SEED)

# Database connection
conn = psycopg2.connect(
    host='$DB_HOST',
    port='$DB_PORT',
    database='$DB_NAME',
    user='$DB_USER',
    password='$DB_PASSWORD'
)
cur = conn.cursor()

# Generate monthly cohorts for the past period
end_date = datetime.now().replace(day=1)  # First day of current month
start_date = end_date - relativedelta(months=$DAYS_BACK // 30)

current_date = start_date
cohorts_generated = 0

while current_date <= end_date:
    cohort_id = current_date.strftime("cohort_%Y_%m")
    cohort_name = current_date.strftime("%B %Y")
    
    # Get customers who first purchased in this month
    next_month = current_date + relativedelta(months=1)
    
    cur.execute("""
        SELECT customer_id, MIN(order_date) as first_order, COUNT(*) as orders, SUM(revenue) as total_revenue
        FROM conversions 
        WHERE order_date >= %s AND order_date < %s AND order_id IS NOT NULL
        GROUP BY customer_id
    """, (current_date, next_month))
    
    cohort_customers = cur.fetchall()
    
    if cohort_customers:
        # Create cohort
        cohort_size = len(cohort_customers)
        total_revenue = sum(c[3] for c in cohort_customers)
        
        cur.execute("""
            INSERT INTO cohorts (id, name, period_start, period_end, size, total_revenue, created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            ON CONFLICT (id) DO UPDATE SET
                size = EXCLUDED.size,
                total_revenue = EXCLUDED.total_revenue,
                updated_at = EXCLUDED.updated_at
        """, (cohort_id, cohort_name, current_date, next_month - timedelta(days=1), 
              cohort_size, total_revenue, current_date, current_date))
        
        # Create cohort members
        members = []
        for customer_id, first_order, orders, revenue in cohort_customers:
            members.append((
                cohort_id, customer_id, first_order, orders, revenue, 
                'active' if orders > 1 else 'inactive', current_date, current_date
            ))
        
        cur.executemany("""
            INSERT INTO cohort_members (cohort_id, customer_id, first_order_date, total_orders, total_revenue, status, created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            ON CONFLICT (cohort_id, customer_id) DO UPDATE SET
                total_orders = EXCLUDED.total_orders,
                total_revenue = EXCLUDED.total_revenue,
                status = EXCLUDED.status,
                updated_at = EXCLUDED.updated_at
        """, members)
        
        cohorts_generated += 1
        print(f"Generated cohort {cohort_name} with {cohort_size} customers")
    
    current_date += relativedelta(months=1)

conn.commit()
cur.close()
conn.close()

print(f"Generated {cohorts_generated} cohorts")
PYTHON_SCRIPT

    print_status "Cohort data generated"
}

# Function to generate forecast data
generate_forecasts() {
    print_status "Generating forecast data..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_debug "DRY RUN: Would generate forecast data"
        return 0
    fi
    
    python3 << PYTHON_SCRIPT
import random
import psycopg2
from datetime import datetime, timedelta
import json

# Set seed for reproducibility
random.seed($SEED)

# Database connection
conn = psycopg2.connect(
    host='$DB_HOST',
    port='$DB_PORT',
    database='$DB_NAME',
    user='$DB_USER',
    password='$DB_PASSWORD'
)
cur = conn.cursor()

# Get historical revenue data
cur.execute("""
    SELECT DATE(order_date) as date, SUM(revenue) as daily_revenue
    FROM conversions 
    WHERE order_date >= NOW() - INTERVAL '90 days' AND order_id IS NOT NULL
    GROUP BY DATE(order_date)
    ORDER BY date
""")
historical_data = cur.fetchall()

if not historical_data:
    print("No historical data found for forecasting")
    exit(0)

# Calculate trend and seasonality
daily_revenues = [float(row[1]) for row in historical_data]
avg_revenue = sum(daily_revenues) / len(daily_revenues)
trend = (daily_revenues[-7:] and sum(daily_revenues[-7:]) / 7 or avg_revenue) - avg_revenue

# Generate forecasts for next 30 days
forecast_start = datetime.now().date() + timedelta(days=1)
forecasts = []

for i in range(30):
    forecast_date = forecast_start + timedelta(days=i)
    
    # Simple trend + noise forecast
    base_value = avg_revenue + (trend * i)
    
    # Add weekly seasonality (weekends typically lower)
    weekday = forecast_date.weekday()
    if weekday >= 5:  # Weekend
        seasonal_factor = 0.8
    else:  # Weekday
        seasonal_factor = 1.0 + (0.1 * random.random())
    
    # Add random noise
    noise_factor = 1.0 + random.uniform(-0.2, 0.2)
    
    predicted_value = base_value * seasonal_factor * noise_factor
    confidence = max(0.6, 1.0 - (i * 0.01))  # Confidence decreases over time
    
    # Confidence intervals
    margin = predicted_value * (1 - confidence) * 2
    lower_bound = max(0, predicted_value - margin)
    upper_bound = predicted_value + margin
    
    metadata = {
        'trend_component': trend * i,
        'seasonal_factor': seasonal_factor,
        'confidence_level': confidence,
        'methodology': 'trend_with_seasonality'
    }
    
    forecasts.append((
        forecast_date, 'revenue', 'daily', predicted_value, lower_bound, upper_bound,
        confidence, json.dumps(metadata), datetime.now(), datetime.now()
    ))

# Insert forecasts
cur.executemany("""
    INSERT INTO forecast_data (
        forecast_date, metric, period, predicted_value, lower_bound, upper_bound,
        confidence, metadata, created_at, updated_at
    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
    ON CONFLICT (forecast_date, metric, period) DO UPDATE SET
        predicted_value = EXCLUDED.predicted_value,
        lower_bound = EXCLUDED.lower_bound,
        upper_bound = EXCLUDED.upper_bound,
        confidence = EXCLUDED.confidence,
        metadata = EXCLUDED.metadata,
        updated_at = EXCLUDED.updated_at
""", forecasts)

conn.commit()
cur.close()
conn.close()

print(f"Generated {len(forecasts)} daily revenue forecasts")
PYTHON_SCRIPT

    print_status "Forecast data generated"
}

# Function to show generation summary
show_summary() {
    if [[ "$DRY_RUN" == "true" ]]; then
        print_status "Demo Data Generation Summary (DRY RUN)"
    else
        print_status "Demo Data Generation Summary"
    fi
    
    echo "=========================="
    echo "Size: $DATA_SIZE"
    echo "Environment: $ENVIRONMENT"
    echo "Days back: $DAYS_BACK"
    echo "Estimated data:"
    echo "  - Customers: $CUSTOMERS"
    echo "  - Orders: $TOTAL_ORDERS"
    echo "  - Events: $TOTAL_EVENTS"
    echo "  - Products: $PRODUCTS"
    echo ""
    print_status "Generated data includes:"
    echo "1. Sample products with categories and pricing"
    echo "2. Customer registrations over time"
    echo "3. Purchase orders with realistic patterns"
    echo "4. Customer journey events"
    echo "5. Multi-touch attribution data"
    echo "6. Monthly cohort analysis"
    echo "7. Revenue forecasting data"
    echo ""
    
    if [[ "$DRY_RUN" == "false" ]]; then
        print_status "Data generation completed successfully!"
        print_status "You can now use the analytics platform with realistic demo data"
        echo ""
        print_status "Next steps:"
        echo "1. Start the application services"
        echo "2. Access the dashboard to view analytics"
        echo "3. Test different analytics features"
        echo "4. Explore cohort and attribution analysis"
    else
        print_status "This was a dry run. Use the same command without -n to generate actual data."
    fi
}

# Main function
main() {
    print_status "Starting demo data generation process..."
    
    # Check database connection
    check_database
    
    # Clean existing data if requested
    clean_demo_data
    
    # Generate data
    generate_products
    generate_customers
    generate_orders_events
    generate_attribution
    generate_cohorts
    generate_forecasts
    
    # Show summary
    show_summary
}

# Run main function
main "$@"