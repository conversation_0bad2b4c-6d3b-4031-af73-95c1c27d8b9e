#!/bin/bash

set -e

echo "🏗️  Building Docker images for all services..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Build results
FAILED_BUILDS=()
SUCCESSFUL_BUILDS=()

# Function to build a service
build_service() {
    local service=$1
    echo -e "${BLUE}Building $service...${NC}"
    
    if [ -d "services/$service" ]; then
        cd "services/$service"
        
        # Build the Docker image
        if docker build -t "ecommerce-analytics-$service:latest" .; then
            echo -e "${GREEN}✅ $service built successfully${NC}"
            SUCCESSFUL_BUILDS+=("$service")
            
            # Tag with current git commit if available
            if command -v git &> /dev/null && git rev-parse --git-dir > /dev/null 2>&1; then
                local commit_sha=$(git rev-parse --short HEAD)
                docker tag "ecommerce-analytics-$service:latest" "ecommerce-analytics-$service:$commit_sha"
                echo -e "${GREEN}   Tagged as: ecommerce-analytics-$service:$commit_sha${NC}"
            fi
        else
            echo -e "${RED}❌ $service build failed${NC}"
            FAILED_BUILDS+=("$service")
        fi
        
        cd ../..
    else
        echo -e "${YELLOW}⚠️  Service directory not found: services/$service${NC}"
        FAILED_BUILDS+=("$service (not found)")
    fi
    
    echo ""
}

# Build each service
echo "Building all services..."
echo "========================"

for service in link-tracking integration analytics dashboard; do
    build_service "$service"
done

# Summary
echo "📊 Build Summary:"
echo "=================="

if [ ${#SUCCESSFUL_BUILDS[@]} -gt 0 ]; then
    echo -e "${GREEN}✅ Successful builds (${#SUCCESSFUL_BUILDS[@]}):${NC}"
    for service in "${SUCCESSFUL_BUILDS[@]}"; do
        echo "   - ecommerce-analytics-$service:latest"
    done
fi

if [ ${#FAILED_BUILDS[@]} -gt 0 ]; then
    echo -e "${RED}❌ Failed builds (${#FAILED_BUILDS[@]}):${NC}"
    for service in "${FAILED_BUILDS[@]}"; do
        echo "   - $service"
    done
    echo ""
    echo -e "${RED}Some builds failed. Please fix the issues.${NC}"
    exit 1
else
    echo ""
    echo -e "${GREEN}🎉 All images built successfully!${NC}"
    
    # Show built images
    echo ""
    echo "📦 Built images:"
    docker images --filter "reference=ecommerce-analytics-*" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
fi

echo ""
echo "💡 Next steps:"
echo "   - Run 'make dev' to start development environment"
echo "   - Run 'make test' to run tests"
echo "   - Run 'docker-compose up' to start all services"