#!/bin/bash

# E-commerce Analytics SaaS - Monitoring Stack Startup Script
# This script starts the comprehensive monitoring infrastructure

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if port is available
check_port() {
    local port=$1
    local service=$2
    if netstat -tuln | grep -q ":$port "; then
        warn "Port $port is already in use (required for $service)"
        return 1
    fi
    return 0
}

# Function to wait for service to be ready
wait_for_service() {
    local url=$1
    local service_name=$2
    local max_attempts=${3:-30}
    local attempt=1

    info "Waiting for $service_name to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -sf "$url" >/dev/null 2>&1; then
            log "$service_name is ready!"
            return 0
        fi
        
        info "Attempt $attempt/$max_attempts: $service_name not ready yet, waiting..."
        sleep 10
        attempt=$((attempt + 1))
    done
    
    error "$service_name failed to start within expected time"
    return 1
}

# Function to create monitoring directories
create_directories() {
    log "Creating monitoring directories..."
    
    local dirs=(
        "monitoring/prometheus/data"
        "monitoring/grafana/data"
        "monitoring/alertmanager/data"
        "monitoring/loki/data"
        "monitoring/victoria-metrics/data"
        "monitoring/jaeger/data"
    )
    
    for dir in "${dirs[@]}"; do
        mkdir -p "$dir"
        info "Created directory: $dir"
    done
}

# Function to set proper permissions
set_permissions() {
    log "Setting proper permissions for monitoring volumes..."
    
    # Prometheus data directory (UID 65534)
    if [ -d "monitoring/prometheus/data" ]; then
        sudo chown -R 65534:65534 monitoring/prometheus/data
    fi
    
    # Grafana data directory (UID 472)
    if [ -d "monitoring/grafana/data" ]; then
        sudo chown -R 472:472 monitoring/grafana/data
    fi
    
    # AlertManager data directory (UID 65534)
    if [ -d "monitoring/alertmanager/data" ]; then
        sudo chown -R 65534:65534 monitoring/alertmanager/data
    fi
    
    # Loki data directory (UID 10001)
    if [ -d "monitoring/loki/data" ]; then
        sudo chown -R 10001:10001 monitoring/loki/data
    fi
}

# Function to validate configuration files
validate_configs() {
    log "Validating monitoring configuration files..."
    
    # Check Prometheus config
    if [ -f "monitoring/prometheus/prometheus.yml" ]; then
        info "✓ Prometheus configuration found"
    else
        error "Prometheus configuration file not found"
        return 1
    fi
    
    # Check AlertManager config
    if [ -f "monitoring/alertmanager/alertmanager.yml" ]; then
        info "✓ AlertManager configuration found"
    else
        error "AlertManager configuration file not found"
        return 1
    fi
    
    # Check Grafana provisioning
    if [ -d "monitoring/grafana/provisioning" ]; then
        info "✓ Grafana provisioning directory found"
    else
        error "Grafana provisioning directory not found"
        return 1
    fi
    
    # Check Loki config
    if [ -f "monitoring/loki/loki.yaml" ]; then
        info "✓ Loki configuration found"
    else
        error "Loki configuration file not found"
        return 1
    fi
}

# Function to check required ports
check_required_ports() {
    log "Checking required ports..."
    
    local ports=(
        "9090:Prometheus"
        "3007:Grafana"
        "9093:AlertManager"
        "3100:Loki"
        "16686:Jaeger"
        "8428:Victoria Metrics"
        "9100:Node Exporter"
        "9187:PostgreSQL Exporter"
        "9121:Redis Exporter"
        "9113:Nginx Exporter"
        "8080:cAdvisor"
        "9115:Blackbox Exporter"
    )
    
    local port_conflicts=false
    
    for port_service in "${ports[@]}"; do
        IFS=':' read -r port service <<< "$port_service"
        if ! check_port "$port" "$service"; then
            port_conflicts=true
        fi
    done
    
    if [ "$port_conflicts" = true ]; then
        error "Port conflicts detected. Please stop conflicting services or change port configurations."
        return 1
    fi
    
    log "All required ports are available"
}

# Function to start monitoring stack
start_monitoring() {
    log "Starting monitoring stack..."
    
    # Start monitoring services
    info "Starting monitoring infrastructure..."
    docker-compose -f monitoring/docker-compose.monitoring.yml up -d
    
    # Wait for core services to be ready
    log "Waiting for core monitoring services to start..."
    sleep 20
    
    # Check Prometheus
    wait_for_service "http://localhost:9090/-/ready" "Prometheus" 30
    
    # Check Grafana
    wait_for_service "http://localhost:3007/api/health" "Grafana" 30
    
    # Check AlertManager
    wait_for_service "http://localhost:9093/-/ready" "AlertManager" 30
    
    # Check Loki
    wait_for_service "http://localhost:3100/ready" "Loki" 30
    
    log "Core monitoring services are ready!"
}

# Function to configure Grafana dashboards
configure_grafana() {
    log "Configuring Grafana dashboards..."
    
    # Wait a bit more for Grafana to fully initialize
    sleep 30
    
    # Check if dashboards are loaded
    local dashboard_count
    dashboard_count=$(curl -s -H "Authorization: Bearer admin:admin123" \
        "http://localhost:3007/api/search?query=&type=dash-db" | \
        jq length 2>/dev/null || echo "0")
    
    if [ "$dashboard_count" -gt 0 ]; then
        log "Grafana dashboards loaded successfully ($dashboard_count dashboards)"
    else
        warn "Grafana dashboards might not be loaded yet. They should appear shortly."
    fi
}

# Function to display service URLs
display_urls() {
    log "Monitoring stack is ready! Access URLs:"
    echo ""
    echo -e "${BLUE}📊 Grafana Dashboard:${NC}     http://localhost:3007"
    echo -e "${BLUE}   Username: admin${NC}"
    echo -e "${BLUE}   Password: admin123${NC}"
    echo ""
    echo -e "${BLUE}📈 Prometheus:${NC}             http://localhost:9090"
    echo -e "${BLUE}🚨 AlertManager:${NC}           http://localhost:9093"
    echo -e "${BLUE}📝 Loki:${NC}                   http://localhost:3100"
    echo -e "${BLUE}🔍 Jaeger Tracing:${NC}         http://localhost:16686"
    echo -e "${BLUE}💾 Victoria Metrics:${NC}       http://localhost:8428"
    echo -e "${BLUE}🖥️  Node Exporter:${NC}          http://localhost:9100"
    echo -e "${BLUE}🗄️  PostgreSQL Exporter:${NC}   http://localhost:9187"
    echo -e "${BLUE}📦 Redis Exporter:${NC}         http://localhost:9121"
    echo -e "${BLUE}🌐 Nginx Exporter:${NC}         http://localhost:9113"
    echo -e "${BLUE}🐳 cAdvisor:${NC}               http://localhost:8080"
    echo -e "${BLUE}🔍 Blackbox Exporter:${NC}      http://localhost:9115"
    echo ""
}

# Function to show monitoring status
show_status() {
    log "Monitoring Stack Status:"
    docker-compose -f monitoring/docker-compose.monitoring.yml ps
}

# Main execution
main() {
    log "Starting E-commerce Analytics Monitoring Stack Setup"
    
    # Check prerequisites
    if ! command_exists docker; then
        error "Docker is not installed or not in PATH"
        exit 1
    fi
    
    if ! command_exists docker-compose; then
        error "Docker Compose is not installed or not in PATH"
        exit 1
    fi
    
    if ! command_exists curl; then
        error "curl is required but not installed"
        exit 1
    fi
    
    if ! command_exists jq; then
        warn "jq is not installed. Some features may not work properly."
    fi
    
    # Check if we're in the right directory
    if [ ! -f "docker-compose.yml" ] || [ ! -d "services" ]; then
        error "Please run this script from the project root directory"
        exit 1
    fi
    
    # Validate configurations
    validate_configs || exit 1
    
    # Check ports
    check_required_ports || exit 1
    
    # Create directories and set permissions
    create_directories
    set_permissions
    
    # Start monitoring
    start_monitoring
    
    # Configure Grafana
    configure_grafana
    
    # Show status
    show_status
    
    # Display access URLs
    display_urls
    
    log "Monitoring stack setup completed successfully!"
    
    info "To stop the monitoring stack, run:"
    info "docker-compose -f monitoring/docker-compose.monitoring.yml down"
    
    info "To view logs, run:"
    info "docker-compose -f monitoring/docker-compose.monitoring.yml logs -f [service-name]"
}

# Handle script arguments
case "${1:-}" in
    "start")
        main
        ;;
    "stop")
        log "Stopping monitoring stack..."
        docker-compose -f monitoring/docker-compose.monitoring.yml down
        log "Monitoring stack stopped"
        ;;
    "restart")
        log "Restarting monitoring stack..."
        docker-compose -f monitoring/docker-compose.monitoring.yml down
        sleep 5
        main
        ;;
    "status")
        show_status
        ;;
    "logs")
        service_name=${2:-}
        if [ -n "$service_name" ]; then
            docker-compose -f monitoring/docker-compose.monitoring.yml logs -f "$service_name"
        else
            docker-compose -f monitoring/docker-compose.monitoring.yml logs -f
        fi
        ;;
    *)
        echo "Usage: $0 {start|stop|restart|status|logs [service-name]}"
        echo ""
        echo "Commands:"
        echo "  start    - Start the monitoring stack"
        echo "  stop     - Stop the monitoring stack"
        echo "  restart  - Restart the monitoring stack"
        echo "  status   - Show status of monitoring services"
        echo "  logs     - Show logs (optionally for specific service)"
        echo ""
        echo "Examples:"
        echo "  $0 start"
        echo "  $0 logs prometheus"
        echo "  $0 status"
        exit 1
        ;;
esac