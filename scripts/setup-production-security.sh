#!/bin/bash

# Production Security Setup Script for E-commerce Analytics Platform
# Configures security headers, SSL, firewall, and other security measures

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Default values
DOMAIN=""
EMAIL=""
ENABLE_FIREWALL=true
ENABLE_FAIL2BAN=true
DRY_RUN=false
SKIP_SSL=false

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# Function to show usage
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Set up production security for the e-commerce analytics platform.

OPTIONS:
    -d, --domain DOMAIN       Domain name for SSL certificate
    -e, --email EMAIL         Email for SSL certificate registration
    --no-firewall             Skip firewall configuration
    --no-fail2ban             Skip fail2ban setup
    --skip-ssl                Skip SSL certificate generation
    -n, --dry-run             Show what would be done without making changes
    -h, --help                Show this help message

EXAMPLES:
    $0 -d example.com -e <EMAIL>
    $0 --skip-ssl --no-fail2ban
    $0 -n -d test.com          # Dry run

SECURITY FEATURES:
    - SSL/TLS certificate generation
    - Nginx security headers
    - UFW firewall configuration
    - Fail2ban intrusion prevention
    - Docker security hardening
    - System security updates
EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -d|--domain)
            DOMAIN="$2"
            shift 2
            ;;
        -e|--email)
            EMAIL="$2"
            shift 2
            ;;
        --no-firewall)
            ENABLE_FIREWALL=false
            shift
            ;;
        --no-fail2ban)
            ENABLE_FAIL2BAN=false
            shift
            ;;
        --skip-ssl)
            SKIP_SSL=true
            shift
            ;;
        -n|--dry-run)
            DRY_RUN=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

print_status "Production Security Setup for E-commerce Analytics Platform"
print_status "Domain: ${DOMAIN:-localhost}"
print_status "Dry Run: $DRY_RUN"

# Function to check if running as root
check_root() {
    if [[ $EUID -ne 0 && "$DRY_RUN" == "false" ]]; then
        print_error "This script must be run as root for security configuration"
        print_error "Use sudo: sudo $0 $*"
        exit 1
    fi
}

# Function to detect OS
detect_os() {
    if [[ -f /etc/os-release ]]; then
        . /etc/os-release
        OS=$ID
        OS_VERSION=$VERSION_ID
    else
        print_error "Cannot detect operating system"
        exit 1
    fi
    
    print_debug "Detected OS: $OS $OS_VERSION"
}

# Function to update system packages
update_system() {
    print_status "Updating system packages..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_debug "DRY RUN: Would update system packages"
        return 0
    fi
    
    case "$OS" in
        "ubuntu"|"debian")
            apt-get update
            apt-get upgrade -y
            apt-get autoremove -y
            ;;
        "centos"|"rhel"|"fedora")
            if command -v dnf &> /dev/null; then
                dnf update -y
                dnf autoremove -y
            else
                yum update -y
                yum autoremove -y
            fi
            ;;
        *)
            print_warning "Unsupported OS for automatic updates: $OS"
            ;;
    esac
    
    print_status "System packages updated"
}

# Function to install required packages
install_packages() {
    print_status "Installing security packages..."
    
    local packages=()
    
    case "$OS" in
        "ubuntu"|"debian")
            packages=(
                "ufw"
                "fail2ban"
                "unattended-upgrades"
                "apt-listchanges"
                "logwatch"
                "rkhunter"
                "chkrootkit"
            )
            
            if [[ "$DRY_RUN" == "false" ]]; then
                apt-get install -y "${packages[@]}"
            else
                print_debug "DRY RUN: Would install packages: ${packages[*]}"
            fi
            ;;
        "centos"|"rhel"|"fedora")
            packages=(
                "firewalld"
                "fail2ban"
                "yum-cron"
                "logwatch"
                "rkhunter"
                "chkrootkit"
            )
            
            if [[ "$DRY_RUN" == "false" ]]; then
                if command -v dnf &> /dev/null; then
                    dnf install -y "${packages[@]}"
                else
                    yum install -y "${packages[@]}"
                fi
            else
                print_debug "DRY RUN: Would install packages: ${packages[*]}"
            fi
            ;;
    esac
    
    print_status "Security packages installed"
}

# Function to configure firewall
configure_firewall() {
    if [[ "$ENABLE_FIREWALL" == "false" ]]; then
        print_warning "Skipping firewall configuration"
        return 0
    fi
    
    print_status "Configuring firewall..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_debug "DRY RUN: Would configure firewall rules"
        return 0
    fi
    
    case "$OS" in
        "ubuntu"|"debian")
            # Reset UFW to defaults
            ufw --force reset
            
            # Set default policies
            ufw default deny incoming
            ufw default allow outgoing
            
            # Allow SSH (be careful not to lock yourself out)
            ufw allow ssh
            
            # Allow HTTP and HTTPS
            ufw allow 80/tcp
            ufw allow 443/tcp
            
            # Allow database access from Docker network only
            ufw allow from **********/16 to any port 5432
            ufw allow from **********/16 to any port 6379
            
            # Enable UFW
            ufw --force enable
            
            print_status "UFW firewall configured and enabled"
            ;;
        "centos"|"rhel"|"fedora")
            # Configure firewalld
            systemctl start firewalld
            systemctl enable firewalld
            
            # Set default zone
            firewall-cmd --set-default-zone=public
            
            # Allow services
            firewall-cmd --permanent --add-service=ssh
            firewall-cmd --permanent --add-service=http
            firewall-cmd --permanent --add-service=https
            
            # Allow Docker network access to databases
            firewall-cmd --permanent --add-rich-rule="rule family='ipv4' source address='**********/16' port protocol='tcp' port='5432' accept"
            firewall-cmd --permanent --add-rich-rule="rule family='ipv4' source address='**********/16' port protocol='tcp' port='6379' accept"
            
            # Reload firewall
            firewall-cmd --reload
            
            print_status "Firewalld configured and enabled"
            ;;
    esac
}

# Function to configure fail2ban
configure_fail2ban() {
    if [[ "$ENABLE_FAIL2BAN" == "false" ]]; then
        print_warning "Skipping fail2ban configuration"
        return 0
    fi
    
    print_status "Configuring fail2ban..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_debug "DRY RUN: Would configure fail2ban"
        return 0
    fi
    
    # Create fail2ban jail configuration
    cat > /etc/fail2ban/jail.local << EOF
[DEFAULT]
# Ban IP for 10 minutes after 5 failed attempts within 10 minutes
bantime = 600
findtime = 600
maxretry = 5

# Email notifications
destemail = ${EMAIL:-root@localhost}
sender = ${EMAIL:-root@localhost}
mta = sendmail

# Ban action
action = %(action_mwl)s

[sshd]
enabled = true
port = ssh
logpath = /var/log/auth.log
maxretry = 3

[nginx-http-auth]
enabled = true
filter = nginx-http-auth
port = http,https
logpath = /var/log/nginx/error.log

[nginx-limit-req]
enabled = true
filter = nginx-limit-req
port = http,https
logpath = /var/log/nginx/error.log
maxretry = 2

[nginx-botsearch]
enabled = true
filter = nginx-botsearch
port = http,https
logpath = /var/log/nginx/access.log
maxretry = 2

[docker-api]
enabled = true
filter = docker-api
port = 3001,3002,3003
logpath = /var/log/docker/api.log
maxretry = 3
EOF

    # Create custom filters
    cat > /etc/fail2ban/filter.d/nginx-limit-req.conf << EOF
[Definition]
failregex = limiting requests, excess: .* by zone .*, client: <HOST>
ignoreregex =
EOF

    cat > /etc/fail2ban/filter.d/nginx-botsearch.conf << EOF
[Definition]
failregex = ^<HOST> -.*"(GET|POST).*HTTP.*" (4|5)0[0-9]
ignoreregex =
EOF

    cat > /etc/fail2ban/filter.d/docker-api.conf << EOF
[Definition]
failregex = ^.*\[ERROR\].*Authentication failed.*client: <HOST>
            ^.*\[WARN\].*Rate limit exceeded.*client: <HOST>
ignoreregex =
EOF

    # Start and enable fail2ban
    systemctl start fail2ban
    systemctl enable fail2ban
    
    print_status "Fail2ban configured and enabled"
}

# Function to setup SSL certificates
setup_ssl() {
    if [[ "$SKIP_SSL" == "true" ]]; then
        print_warning "Skipping SSL certificate setup"
        return 0
    fi
    
    print_status "Setting up SSL certificates..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_debug "DRY RUN: Would setup SSL certificates"
        return 0
    fi
    
    # Run SSL certificate generation script
    local ssl_args=()
    
    if [[ -n "$DOMAIN" ]]; then
        ssl_args+=("-d" "$DOMAIN")
        
        if [[ -n "$EMAIL" ]]; then
            ssl_args+=("-t" "letsencrypt" "-e" "$EMAIL")
        else
            print_warning "No email provided, generating self-signed certificate"
        fi
    else
        print_warning "No domain provided, generating self-signed certificate for localhost"
    fi
    
    "$SCRIPT_DIR/generate-ssl-certs.sh" "${ssl_args[@]}"
}

# Function to configure Docker security
configure_docker_security() {
    print_status "Configuring Docker security..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_debug "DRY RUN: Would configure Docker security"
        return 0
    fi
    
    # Create Docker daemon configuration for security
    mkdir -p /etc/docker
    
    cat > /etc/docker/daemon.json << EOF
{
    "log-driver": "json-file",
    "log-opts": {
        "max-size": "10m",
        "max-file": "3"
    },
    "live-restore": true,
    "userland-proxy": false,
    "no-new-privileges": true,
    "seccomp-profile": "/etc/docker/seccomp.json",
    "storage-driver": "overlay2",
    "storage-opts": [
        "overlay2.override_kernel_check=true"
    ]
}
EOF

    # Create seccomp profile
    curl -sSL https://raw.githubusercontent.com/moby/moby/master/profiles/seccomp/default.json -o /etc/docker/seccomp.json
    
    # Restart Docker to apply configuration
    systemctl restart docker
    
    print_status "Docker security configured"
}

# Function to configure system security
configure_system_security() {
    print_status "Configuring system security settings..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_debug "DRY RUN: Would configure system security"
        return 0
    fi
    
    # Disable unnecessary services
    local services_to_disable=(
        "telnet"
        "rsh"
        "rlogin"
        "ftp"
        "finger"
    )
    
    for service in "${services_to_disable[@]}"; do
        if systemctl is-enabled "$service" &>/dev/null; then
            systemctl disable "$service"
            systemctl stop "$service"
            print_debug "Disabled service: $service"
        fi
    done
    
    # Configure kernel parameters for security
    cat >> /etc/sysctl.conf << EOF

# Security configurations added by ecommerce-analytics setup
# Disable IP forwarding (unless needed for Docker)
net.ipv4.ip_forward = 0

# Disable ICMP redirects
net.ipv4.conf.all.accept_redirects = 0
net.ipv4.conf.default.accept_redirects = 0
net.ipv6.conf.all.accept_redirects = 0
net.ipv6.conf.default.accept_redirects = 0

# Disable source routing
net.ipv4.conf.all.accept_source_route = 0
net.ipv4.conf.default.accept_source_route = 0
net.ipv6.conf.all.accept_source_route = 0
net.ipv6.conf.default.accept_source_route = 0

# Enable SYN flood protection
net.ipv4.tcp_syncookies = 1

# Disable ping responses
net.ipv4.icmp_echo_ignore_all = 1

# Log suspicious packets
net.ipv4.conf.all.log_martians = 1
net.ipv4.conf.default.log_martians = 1

# Ignore broadcast pings
net.ipv4.icmp_echo_ignore_broadcasts = 1

# Disable send redirects
net.ipv4.conf.all.send_redirects = 0
net.ipv4.conf.default.send_redirects = 0
EOF

    # Apply sysctl settings
    sysctl -p
    
    print_status "System security settings configured"
}

# Function to setup automatic security updates
setup_auto_updates() {
    print_status "Configuring automatic security updates..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_debug "DRY RUN: Would configure automatic updates"
        return 0
    fi
    
    case "$OS" in
        "ubuntu"|"debian")
            # Configure unattended-upgrades
            cat > /etc/apt/apt.conf.d/50unattended-upgrades << EOF
Unattended-Upgrade::Allowed-Origins {
    "\${distro_id}:\${distro_codename}-security";
    "\${distro_id}ESMApps:\${distro_codename}-apps-security";
    "\${distro_id}ESM:\${distro_codename}-infra-security";
};

Unattended-Upgrade::AutoFixInterruptedDpkg "true";
Unattended-Upgrade::MinimalSteps "true";
Unattended-Upgrade::Remove-Unused-Dependencies "true";
Unattended-Upgrade::Automatic-Reboot "false";
Unattended-Upgrade::Mail "${EMAIL:-root}";
EOF

            # Enable automatic updates
            echo 'APT::Periodic::Update-Package-Lists "1";' > /etc/apt/apt.conf.d/20auto-upgrades
            echo 'APT::Periodic::Unattended-Upgrade "1";' >> /etc/apt/apt.conf.d/20auto-upgrades
            
            systemctl enable unattended-upgrades
            ;;
        "centos"|"rhel"|"fedora")
            # Configure yum-cron
            sed -i 's/apply_updates = no/apply_updates = yes/' /etc/yum/yum-cron.conf
            sed -i "s/email_to = root/email_to = ${EMAIL:-root}/" /etc/yum/yum-cron.conf
            
            systemctl enable yum-cron
            systemctl start yum-cron
            ;;
    esac
    
    print_status "Automatic security updates configured"
}

# Function to create security monitoring script
create_monitoring_script() {
    print_status "Creating security monitoring script..."
    
    cat > "$PROJECT_ROOT/scripts/security-monitor.sh" << 'EOF'
#!/bin/bash

# Security monitoring script for e-commerce analytics platform
# Run this script regularly to check for security issues

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_FILE="/var/log/security-monitor.log"

echo "$(date): Security monitoring check started" >> "$LOG_FILE"

# Check for failed login attempts
echo "=== Failed Login Attempts ===" >> "$LOG_FILE"
grep "Failed password" /var/log/auth.log | tail -10 >> "$LOG_FILE"

# Check for unusual network connections
echo "=== Unusual Network Connections ===" >> "$LOG_FILE"
netstat -tulpn | grep -E ":(22|80|443|3000|3001|3002|3003|5432|6379)" >> "$LOG_FILE"

# Check Docker container status
echo "=== Docker Container Status ===" >> "$LOG_FILE"
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" >> "$LOG_FILE"

# Check for root kit
if command -v rkhunter &> /dev/null; then
    echo "=== Rootkit Check ===" >> "$LOG_FILE"
    rkhunter --check --sk --quiet >> "$LOG_FILE" 2>&1
fi

# Check disk usage
echo "=== Disk Usage ===" >> "$LOG_FILE"
df -h >> "$LOG_FILE"

echo "$(date): Security monitoring check completed" >> "$LOG_FILE"
echo "---" >> "$LOG_FILE"
EOF

    chmod +x "$PROJECT_ROOT/scripts/security-monitor.sh"
    
    # Add to crontab
    if [[ "$DRY_RUN" == "false" ]]; then
        (crontab -l 2>/dev/null; echo "0 2 * * * $PROJECT_ROOT/scripts/security-monitor.sh") | crontab -
    fi
    
    print_status "Security monitoring script created"
}

# Function to display security summary
show_security_summary() {
    print_status "Security Setup Summary"
    echo "======================"
    echo "Domain: ${DOMAIN:-localhost}"
    echo "SSL: $(if [[ "$SKIP_SSL" == "true" ]]; then echo "Skipped"; else echo "Configured"; fi)"
    echo "Firewall: $(if [[ "$ENABLE_FIREWALL" == "true" ]]; then echo "Enabled"; else echo "Disabled"; fi)"
    echo "Fail2ban: $(if [[ "$ENABLE_FAIL2BAN" == "true" ]]; then echo "Enabled"; else echo "Disabled"; fi)"
    echo "Auto Updates: Enabled"
    echo "Docker Security: Configured"
    echo "System Hardening: Applied"
    echo ""
    print_status "Security recommendations:"
    echo "1. Regularly update your system and Docker images"
    echo "2. Monitor logs in /var/log for suspicious activity"
    echo "3. Review and update firewall rules as needed"
    echo "4. Test SSL certificate renewal process"
    echo "5. Run security monitoring script regularly"
    echo "6. Consider additional monitoring tools (e.g., OSSEC, Nagios)"
    echo ""
    print_status "Files created/modified:"
    echo "- /etc/fail2ban/jail.local"
    echo "- /etc/docker/daemon.json"
    echo "- $PROJECT_ROOT/scripts/security-monitor.sh"
    echo "- SSL certificates in $PROJECT_ROOT/nginx/ssl/"
}

# Main function
main() {
    print_status "Starting production security setup..."
    
    # Check prerequisites
    check_root
    detect_os
    
    # Update system
    update_system
    
    # Install required packages
    install_packages
    
    # Configure security components
    configure_firewall
    configure_fail2ban
    setup_ssl
    configure_docker_security
    configure_system_security
    setup_auto_updates
    
    # Create monitoring script
    create_monitoring_script
    
    # Show summary
    show_security_summary
    
    print_status "Production security setup completed successfully!"
    
    if [[ "$DRY_RUN" == "false" ]]; then
        print_warning "Please review all configurations before deploying to production"
        print_warning "Test SSL certificates and firewall rules carefully"
    fi
}

# Run main function
main "$@"