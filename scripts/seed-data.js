#!/usr/bin/env node

const { Client } = require('pg');
const Redis = require('redis');
const { faker } = require('@faker-js/faker');

// Database configuration
const dbConfig = {
  host: process.env.POSTGRES_HOST || 'localhost',
  port: process.env.POSTGRES_PORT || 5432,
  database: process.env.POSTGRES_DB || 'ecommerce_analytics',
  user: process.env.POSTGRES_USER || 'postgres',
  password: process.env.POSTGRES_PASSWORD || 'password',
};

// Redis configuration
const redisConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: process.env.REDIS_PORT || 6379,
};

class DataSeeder {
  constructor() {
    this.client = new Client(dbConfig);
    this.redis = null;
  }

  async connect() {
    await this.client.connect();
    console.log('Connected to PostgreSQL');
    
    this.redis = Redis.createClient(redisConfig);
    await this.redis.connect();
    console.log('Connected to Redis');
  }

  async disconnect() {
    await this.client.end();
    if (this.redis) {
      await this.redis.disconnect();
    }
    console.log('Disconnected from databases');
  }

  // Generate sample users with tenants
  async seedUsers(count = 5) {
    console.log(`Seeding ${count} users with tenants...`);
    
    const users = [];
    for (let i = 0; i < count; i++) {
      const companyName = faker.company.name();
      const user = {
        id: faker.string.uuid(),
        email: faker.internet.email(),
        firstName: faker.person.firstName(),
        lastName: faker.person.lastName(),
        companyName: companyName,
        role: 'user',
        createdAt: faker.date.past({ years: 1 }),
        tenant: {
          id: faker.string.uuid(),
          name: companyName,
          slug: companyName.toLowerCase().replace(/[^a-z0-9]/g, '-').replace(/-+/g, '-'),
          plan: faker.helpers.arrayElement(['free', 'starter', 'professional', 'enterprise']),
        }
      };
      users.push(user);
    }

    // Insert users and tenants
    for (const user of users) {
      const hashedPassword = '$2b$10$rQ7Q1ZK1ZQ1ZQ1ZQ1ZQ1ZuO1ZQ1ZQ1ZQ1ZQ1ZQ1ZQ1ZQ1ZQ1ZQ1Z'; // "password123"
      
      // Insert tenant
      await this.client.query(`
        INSERT INTO tenants (id, name, slug, plan, created_at)
        VALUES ($1, $2, $3, $4, $5)
        ON CONFLICT (slug) DO NOTHING
      `, [user.tenant.id, user.tenant.name, user.tenant.slug, user.tenant.plan, user.createdAt]);
      
      // Insert user
      await this.client.query(`
        INSERT INTO users (id, email, password_hash, first_name, last_name, company_name, role, created_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        ON CONFLICT (email) DO NOTHING
      `, [user.id, user.email, hashedPassword, user.firstName, user.lastName, user.companyName, user.role, user.createdAt]);
      
      // Insert user-tenant relationship
      await this.client.query(`
        INSERT INTO user_tenants (user_id, tenant_id, role, created_at)
        VALUES ($1, $2, $3, $4)
        ON CONFLICT (user_id, tenant_id) DO NOTHING
      `, [user.id, user.tenant.id, 'admin', user.createdAt]);
    }

    console.log(`✅ Seeded ${users.length} users with tenants`);
    return users;
  }

  // Generate sample links for each user
  async seedLinks(users, linksPerUser = 10) {
    console.log(`Seeding ${linksPerUser} links per user...`);
    
    const links = [];
    
    for (const user of users) {
      for (let i = 0; i < linksPerUser; i++) {
        const link = {
          id: faker.string.uuid(),
          tenantId: user.tenant.id,
          title: faker.commerce.productName(),
          targetUrl: faker.internet.url(),
          shortCode: faker.string.alphanumeric(8),
          utmSource: faker.helpers.arrayElement(['facebook', 'google', 'instagram', 'email', 'twitter']),
          utmMedium: faker.helpers.arrayElement(['cpc', 'social', 'email', 'organic', 'referral']),
          utmCampaign: faker.lorem.words(2),
          isActive: faker.datatype.boolean(0.9), // 90% active
          createdAt: faker.date.past({ years: 1 }),
        };
        links.push(link);
      }
    }

    // Insert links
    for (const link of links) {
      await this.client.query(`
        INSERT INTO links (id, tenant_id, title, target_url, short_code, utm_source, utm_medium, utm_campaign, is_active, created_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
        ON CONFLICT (short_code) DO NOTHING
      `, [
        link.id, link.tenantId, link.title, link.targetUrl, link.shortCode,
        link.utmSource, link.utmMedium, link.utmCampaign, link.isActive, link.createdAt
      ]);
    }

    console.log(`✅ Seeded ${links.length} links`);
    return links;
  }

  // Generate realistic click patterns
  async seedClicks(links, clicksPerLink = 50) {
    console.log(`Seeding ${clicksPerLink} clicks per link...`);
    
    const countries = ['US', 'CA', 'GB', 'DE', 'FR', 'AU', 'JP', 'BR', 'IN', 'CN'];
    const devices = ['desktop', 'mobile', 'tablet'];
    const browsers = ['chrome', 'firefox', 'safari', 'edge'];
    const os = ['windows', 'macos', 'ios', 'android', 'linux'];
    
    let totalClicks = 0;
    
    for (const link of links) {
      if (!link.isActive) continue;
      
      // Generate varied click counts (some links more popular)
      const clickCount = Math.floor(Math.random() * clicksPerLink * 2);
      
      for (let i = 0; i < clickCount; i++) {
        const click = {
          id: faker.string.uuid(),
          linkId: link.id,
          sessionId: faker.string.alphanumeric(32),
          ipAddress: faker.internet.ipv4(),
          country: faker.helpers.arrayElement(countries),
          city: faker.location.city(),
          deviceType: faker.helpers.arrayElement(devices),
          browser: faker.helpers.arrayElement(browsers),
          os: faker.helpers.arrayElement(os),
          referrer: Math.random() > 0.3 ? faker.internet.url() : null,
          userAgent: faker.internet.userAgent(),
          trackingId: faker.string.alphanumeric(16),
          // Clicks distributed over the past 90 days with recent bias
          clickedAt: faker.date.recent({ days: 90 }),
        };

        await this.client.query(`
          INSERT INTO clicks (id, link_id, session_id, ip_address, country, city, device_type, browser, os, referrer, user_agent, tracking_id, clicked_at)
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
        `, [
          click.id, click.linkId, click.sessionId, click.ipAddress, click.country,
          click.city, click.deviceType, click.browser, click.os,
          click.referrer, click.userAgent, click.trackingId, click.clickedAt
        ]);
        
        totalClicks++;
      }
    }

    console.log(`✅ Seeded ${totalClicks} clicks`);
    return totalClicks;
  }

  // Generate sample orders and conversions
  async seedOrdersAndConversions(links) {
    console.log('Seeding orders and conversions...');
    
    const clicks = await this.client.query('SELECT c.*, l.tenant_id FROM clicks c JOIN links l ON c.link_id = l.id');
    const clickRows = clicks.rows;
    
    let totalOrders = 0;
    let totalConversions = 0;
    
    // Generate orders (conversion rate around 2-5%)
    for (const click of clickRows) {
      if (Math.random() > 0.96) continue; // ~4% conversion rate
      
      const order = {
        id: faker.string.uuid(),
        tenantId: click.tenant_id,
        platformOrderId: faker.string.alphanumeric(10),
        totalAmount: parseFloat(faker.commerce.price({ min: 10, max: 500 })),
        currency: 'USD',
        status: faker.helpers.arrayElement(['completed', 'pending', 'cancelled']),
        customerEmail: faker.internet.email(),
        customerId: faker.string.uuid(),
        trackingId: click.tracking_id,
        orderData: JSON.stringify({
          items: [
            {
              name: faker.commerce.productName(),
              price: faker.commerce.price(),
              quantity: faker.number.int({ min: 1, max: 3 })
            }
          ]
        }),
        createdAt: new Date(click.clicked_at.getTime() + Math.random() * 3600000), // Within 1 hour of click
      };

      if (order.status === 'cancelled') continue;

      await this.client.query(`
        INSERT INTO orders (id, tenant_id, platform_order_id, total_amount, currency, status, customer_email, customer_id, tracking_id, order_data, created_at, platform_created_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $11)
      `, [
        order.id, order.tenantId, order.platformOrderId, order.totalAmount, order.currency,
        order.status, order.customerEmail, order.customerId, order.trackingId, order.orderData, order.createdAt
      ]);

      totalOrders++;

      // Create attribution (conversion)
      const attribution = {
        id: faker.string.uuid(),
        tenantId: click.tenant_id,
        clickId: click.id,
        linkId: click.link_id,
        orderId: order.id,
        attributionModel: 'last-click',
        conversionValue: order.totalAmount,
        commissionRate: 0.05, // 5% commission
        commissionAmount: order.totalAmount * 0.05,
        createdAt: order.createdAt,
      };

      await this.client.query(`
        INSERT INTO attributions (id, tenant_id, click_id, link_id, order_id, attribution_model, conversion_value, commission_rate, commission_amount, created_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
      `, [
        attribution.id, attribution.tenantId, attribution.clickId, attribution.linkId, attribution.orderId,
        attribution.attributionModel, attribution.conversionValue, attribution.commissionRate, attribution.commissionAmount, attribution.createdAt
      ]);

      totalConversions++;
    }

    console.log(`✅ Seeded ${totalOrders} orders and ${totalConversions} conversions`);
  }

  // Generate integrations
  async seedIntegrations(users) {
    console.log('Seeding integrations...');
    
    const platforms = [
      { name: 'Shopify', type: 'shopify', features: ['orders', 'products', 'customers'] },
      { name: 'WooCommerce', type: 'woocommerce', features: ['orders', 'products'] },
      { name: 'Amazon Seller Central', type: 'amazon', features: ['orders', 'inventory'] },
      { name: 'BigCommerce', type: 'bigcommerce', features: ['orders', 'products', 'customers'] },
    ];

    let totalIntegrations = 0;

    for (const user of users) {
      // Each user has 1-3 integrations
      const integrationCount = Math.floor(Math.random() * 3) + 1;
      const userPlatforms = faker.helpers.arrayElements(platforms, integrationCount);

      for (const platform of userPlatforms) {
        const integration = {
          id: faker.string.uuid(),
          tenantId: user.tenant.id,
          platform: platform.type,
          storeName: platform.type === 'shopify' ? `${faker.internet.domainWord()} Store` : faker.company.name(),
          storeUrl: platform.type === 'shopify' ? `${faker.internet.domainWord()}.myshopify.com` : faker.internet.url(),
          isActive: faker.datatype.boolean(0.8), // 80% active
          apiCredentials: JSON.stringify({
            apiKey: faker.string.alphanumeric(32),
            apiSecret: faker.string.alphanumeric(64),
            features: platform.features,
            syncFrequency: faker.helpers.arrayElement(['hourly', 'daily', 'weekly']),
          }),
          webhookUrl: `https://api.example.com/webhooks/${faker.string.alphanumeric(16)}`,
          webhookSecret: faker.string.alphanumeric(32),
          lastSyncAt: faker.date.recent({ days: 7 }),
          createdAt: faker.date.past({ months: 6 }),
        };

        await this.client.query(`
          INSERT INTO integrations (id, tenant_id, platform, store_name, store_url, is_active, api_credentials, webhook_url, webhook_secret, last_sync_at, created_at)
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
        `, [
          integration.id, integration.tenantId, integration.platform, integration.storeName,
          integration.storeUrl, integration.isActive, integration.apiCredentials, integration.webhookUrl,
          integration.webhookSecret, integration.lastSyncAt, integration.createdAt
        ]);

        totalIntegrations++;
      }
    }

    console.log(`✅ Seeded ${totalIntegrations} integrations`);
  }

  // Clear existing data
  async clearData() {
    console.log('Clearing existing sample data...');
    
    // Clear in dependency order (foreign key constraints)
    const tables = [
      { name: 'attributions', condition: 'created_at > NOW() - INTERVAL \'2 hours\'' },
      { name: 'orders', condition: 'created_at > NOW() - INTERVAL \'2 hours\'' },
      { name: 'clicks', condition: 'clicked_at > NOW() - INTERVAL \'2 hours\'' },
      { name: 'links', condition: 'created_at > NOW() - INTERVAL \'2 hours\'' },
      { name: 'integrations', condition: 'created_at > NOW() - INTERVAL \'2 hours\'' },
      { name: 'user_tenants', condition: 'created_at > NOW() - INTERVAL \'2 hours\'' },
      { name: 'users', condition: 'created_at > NOW() - INTERVAL \'2 hours\' OR email LIKE \'%@example.%\' OR email LIKE \'%faker%\'' },
      { name: 'tenants', condition: 'created_at > NOW() - INTERVAL \'2 hours\'' },
    ];

    for (const table of tables) {
      try {
        const result = await this.client.query(`DELETE FROM ${table.name} WHERE ${table.condition}`);
        console.log(`  Cleared ${result.rowCount} rows from ${table.name}`);
      } catch (error) {
        console.log(`  Warning: Could not clear ${table.name}: ${error.message}`);
      }
    }

    // Clear Redis cache
    await this.redis.flushAll();
    
    console.log('✅ Cleared existing sample data');
  }

  // Main seeding function
  async seed() {
    try {
      await this.connect();
      
      console.log('🌱 Starting data seeding...\n');
      
      // Clear any existing sample data
      await this.clearData();
      
      // Seed data in dependency order
      const users = await this.seedUsers(5);
      const links = await this.seedLinks(users, 15);
      await this.seedClicks(links, 75);
      await this.seedOrdersAndConversions(links);
      await this.seedIntegrations(users);
      
      console.log('\n🎉 Data seeding completed successfully!');
      console.log('\nSample user credentials:');
      console.log('Email: <EMAIL>');
      console.log('Password: password123');
      
    } catch (error) {
      console.error('❌ Error seeding data:', error);
      throw error;
    } finally {
      await this.disconnect();
    }
  }
}

// Run seeding if called directly
if (require.main === module) {
  const seeder = new DataSeeder();
  seeder.seed().catch(console.error);
}

module.exports = DataSeeder;