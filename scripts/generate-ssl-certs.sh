#!/bin/bash

# SSL Certificate Generation Script for E-commerce Analytics Platform
# Supports both self-signed certificates for development and Let's Encrypt for production

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
SSL_DIR="$PROJECT_ROOT/nginx/ssl"

# Default values
CERT_TYPE="self-signed"
DOMAIN="localhost"
EMAIL=""
STAGING=false
FORCE=false
DAYS=365

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# Function to show usage
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Generate SSL certificates for the e-commerce analytics platform.

OPTIONS:
    -t, --type TYPE           Certificate type: self-signed, letsencrypt (default: self-signed)
    -d, --domain DOMAIN       Domain name (default: localhost)
    -e, --email EMAIL         Email for Let's Encrypt registration
    -s, --staging             Use Let's Encrypt staging environment
    -f, --force               Force regeneration of existing certificates
    --days DAYS               Certificate validity in days for self-signed (default: 365)
    -h, --help                Show this help message

EXAMPLES:
    $0                                    # Generate self-signed cert for localhost
    $0 -d example.com                     # Generate self-signed cert for example.com
    $0 -t letsencrypt -d example.com -e <EMAIL>  # Generate Let's Encrypt cert
    $0 -t letsencrypt -s -d staging.example.com -e <EMAIL>  # Staging cert

NOTES:
    - Self-signed certificates are for development only
    - Let's Encrypt requires a publicly accessible domain
    - Staging certificates are for testing Let's Encrypt integration
EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -t|--type)
            CERT_TYPE="$2"
            shift 2
            ;;
        -d|--domain)
            DOMAIN="$2"
            shift 2
            ;;
        -e|--email)
            EMAIL="$2"
            shift 2
            ;;
        -s|--staging)
            STAGING=true
            shift
            ;;
        -f|--force)
            FORCE=true
            shift
            ;;
        --days)
            DAYS="$2"
            shift 2
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate inputs
if [[ "$CERT_TYPE" != "self-signed" && "$CERT_TYPE" != "letsencrypt" ]]; then
    print_error "Invalid certificate type: $CERT_TYPE"
    exit 1
fi

if [[ "$CERT_TYPE" == "letsencrypt" && -z "$EMAIL" ]]; then
    print_error "Email is required for Let's Encrypt certificates"
    exit 1
fi

# Create SSL directory
mkdir -p "$SSL_DIR"

print_status "SSL Certificate Generation"
print_status "Type: $CERT_TYPE"
print_status "Domain: $DOMAIN"
print_status "SSL Directory: $SSL_DIR"

# Function to check if certificates exist
check_existing_certs() {
    if [[ -f "$SSL_DIR/cert.pem" && -f "$SSL_DIR/key.pem" ]]; then
        if [[ "$FORCE" == "false" ]]; then
            print_warning "SSL certificates already exist"
            echo "Use --force to regenerate existing certificates"
            
            # Show certificate info
            print_status "Current certificate info:"
            openssl x509 -in "$SSL_DIR/cert.pem" -text -noout | grep -E "(Subject:|Issuer:|Not Before|Not After)"
            
            read -p "Continue with existing certificates? (y/N): " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                exit 0
            fi
            return 1
        else
            print_status "Removing existing certificates..."
            rm -f "$SSL_DIR"/*.pem "$SSL_DIR"/*.key "$SSL_DIR"/*.csr
        fi
    fi
    return 0
}

# Function to generate DH parameters
generate_dhparam() {
    if [[ ! -f "$SSL_DIR/dhparam.pem" || "$FORCE" == "true" ]]; then
        print_status "Generating DH parameters (this may take a while)..."
        openssl dhparam -out "$SSL_DIR/dhparam.pem" 2048
        print_status "DH parameters generated"
    else
        print_debug "DH parameters already exist"
    fi
}

# Function to generate self-signed certificate
generate_self_signed() {
    print_status "Generating self-signed certificate..."
    
    # Create OpenSSL configuration
    cat > "$SSL_DIR/openssl.cnf" << EOF
[req]
default_bits = 2048
prompt = no
default_md = sha256
distinguished_name = dn
req_extensions = v3_req

[dn]
C = US
ST = California
L = San Francisco
O = E-commerce Analytics Platform
OU = Development
CN = $DOMAIN

[v3_req]
basicConstraints = CA:FALSE
keyUsage = nonRepudiation, digitalSignature, keyEncipherment
subjectAltName = @alt_names

[alt_names]
DNS.1 = $DOMAIN
DNS.2 = localhost
DNS.3 = 127.0.0.1
DNS.4 = *.localhost
IP.1 = 127.0.0.1
IP.2 = ::1
EOF

    # Generate private key
    openssl genrsa -out "$SSL_DIR/key.pem" 2048
    
    # Generate certificate signing request
    openssl req -new -key "$SSL_DIR/key.pem" -out "$SSL_DIR/cert.csr" -config "$SSL_DIR/openssl.cnf"
    
    # Generate self-signed certificate
    openssl x509 -req -in "$SSL_DIR/cert.csr" -signkey "$SSL_DIR/key.pem" -out "$SSL_DIR/cert.pem" \
        -days "$DAYS" -extensions v3_req -extfile "$SSL_DIR/openssl.cnf"
    
    # Create certificate chain (self-signed, so just copy the cert)
    cp "$SSL_DIR/cert.pem" "$SSL_DIR/chain.pem"
    
    # Set proper permissions
    chmod 600 "$SSL_DIR/key.pem"
    chmod 644 "$SSL_DIR/cert.pem" "$SSL_DIR/chain.pem"
    
    # Clean up
    rm -f "$SSL_DIR/cert.csr" "$SSL_DIR/openssl.cnf"
    
    print_status "Self-signed certificate generated successfully"
    
    # Show certificate info
    print_status "Certificate details:"
    openssl x509 -in "$SSL_DIR/cert.pem" -text -noout | grep -E "(Subject:|Issuer:|Not Before|Not After|DNS:|IP Address:)"
}

# Function to generate Let's Encrypt certificate
generate_letsencrypt() {
    print_status "Generating Let's Encrypt certificate..."
    
    # Check if certbot is installed
    if ! command -v certbot &> /dev/null; then
        print_error "certbot is not installed. Please install it first:"
        print_error "  Ubuntu/Debian: sudo apt-get install certbot"
        print_error "  CentOS/RHEL: sudo yum install certbot"
        print_error "  macOS: brew install certbot"
        exit 1
    fi
    
    # Prepare certbot options
    local certbot_options=(
        "certonly"
        "--standalone"
        "--non-interactive"
        "--agree-tos"
        "--email" "$EMAIL"
        "--domains" "$DOMAIN"
        "--cert-path" "$SSL_DIR/cert.pem"
        "--key-path" "$SSL_DIR/key.pem"
        "--fullchain-path" "$SSL_DIR/chain.pem"
    )
    
    if [[ "$STAGING" == "true" ]]; then
        certbot_options+=("--staging")
        print_warning "Using Let's Encrypt staging environment"
    fi
    
    if [[ "$FORCE" == "true" ]]; then
        certbot_options+=("--force-renewal")
    fi
    
    # Run certbot
    if ! certbot "${certbot_options[@]}"; then
        print_error "Let's Encrypt certificate generation failed"
        print_error "Make sure:"
        print_error "  - Domain $DOMAIN points to this server"
        print_error "  - Port 80 is accessible from the internet"
        print_error "  - No other web server is running on port 80"
        exit 1
    fi
    
    # Set proper permissions
    chmod 600 "$SSL_DIR/key.pem"
    chmod 644 "$SSL_DIR/cert.pem" "$SSL_DIR/chain.pem"
    
    print_status "Let's Encrypt certificate generated successfully"
    
    # Show certificate info
    print_status "Certificate details:"
    openssl x509 -in "$SSL_DIR/cert.pem" -text -noout | grep -E "(Subject:|Issuer:|Not Before|Not After)"
    
    # Set up auto-renewal reminder
    print_status "Setting up auto-renewal..."
    print_status "Add this to your crontab for automatic renewal:"
    echo "0 12 * * * /usr/bin/certbot renew --quiet --deploy-hook 'docker-compose -f $PROJECT_ROOT/docker-compose.yml -f $PROJECT_ROOT/docker-compose.production.yml restart nginx'"
}

# Function to verify certificate
verify_certificate() {
    print_status "Verifying certificate..."
    
    # Check if files exist
    local required_files=("cert.pem" "key.pem" "chain.pem")
    for file in "${required_files[@]}"; do
        if [[ ! -f "$SSL_DIR/$file" ]]; then
            print_error "Missing certificate file: $file"
            return 1
        fi
    done
    
    # Verify certificate and key match
    local cert_hash=$(openssl x509 -noout -modulus -in "$SSL_DIR/cert.pem" | openssl md5)
    local key_hash=$(openssl rsa -noout -modulus -in "$SSL_DIR/key.pem" | openssl md5)
    
    if [[ "$cert_hash" != "$key_hash" ]]; then
        print_error "Certificate and private key do not match"
        return 1
    fi
    
    # Check certificate validity
    if ! openssl x509 -in "$SSL_DIR/cert.pem" -checkend 86400 > /dev/null; then
        print_warning "Certificate expires within 24 hours"
    fi
    
    print_status "Certificate verification passed"
}

# Function to create certificate info file
create_cert_info() {
    cat > "$SSL_DIR/cert_info.txt" << EOF
Certificate Information
Generated: $(date)
Type: $CERT_TYPE
Domain: $DOMAIN
$(if [[ "$CERT_TYPE" == "letsencrypt" ]]; then echo "Email: $EMAIL"; fi)
$(if [[ "$STAGING" == "true" ]]; then echo "Environment: Staging"; fi)

Files:
- cert.pem: Server certificate
- key.pem: Private key (keep secure!)
- chain.pem: Certificate chain
- dhparam.pem: DH parameters

Certificate Details:
$(openssl x509 -in "$SSL_DIR/cert.pem" -text -noout | grep -E "(Subject:|Issuer:|Not Before|Not After)")

To use these certificates:
1. Mount the SSL directory in your nginx container
2. Update nginx configuration to use the certificates
3. Restart nginx service

For Let's Encrypt certificates:
- Set up auto-renewal in crontab
- Monitor certificate expiration
- Test renewal process regularly
EOF
}

# Main execution
main() {
    print_status "Starting SSL certificate generation process..."
    
    # Check existing certificates
    if ! check_existing_certs; then
        return 0
    fi
    
    # Generate DH parameters
    generate_dhparam
    
    # Generate certificate based on type
    case "$CERT_TYPE" in
        "self-signed")
            generate_self_signed
            ;;
        "letsencrypt")
            generate_letsencrypt
            ;;
    esac
    
    # Verify certificate
    verify_certificate
    
    # Create info file
    create_cert_info
    
    print_status "SSL certificate generation completed successfully!"
    print_status "Certificate files are located in: $SSL_DIR"
    
    if [[ "$CERT_TYPE" == "self-signed" ]]; then
        print_warning "Note: Self-signed certificates will show security warnings in browsers"
        print_warning "For production use, consider using Let's Encrypt certificates"
    fi
    
    print_status "Next steps:"
    echo "1. Update your docker-compose configuration to use SSL"
    echo "2. Start the services with: docker-compose -f docker-compose.yml -f docker-compose.production.yml up -d"
    echo "3. Test HTTPS access to your application"
}

# Run main function
main "$@"