#!/bin/bash

# E-commerce Analytics SaaS - Production Deployment Script
# This script handles deployment to staging and production environments with Podman support

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Default values
ENVIRONMENT="staging"
CONTAINER_ENGINE="podman"
COMPOSE_FILE="docker-compose.yml"
BUILD_IMAGES=true
RUN_MIGRATIONS=true
RUN_HEALTH_CHECKS=true
BACKUP_BEFORE_DEPLOY=false
ROLLBACK_ON_FAILURE=true
DRY_RUN=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        --docker)
            CONTAINER_ENGINE="docker"
            shift
            ;;
        --podman)
            CONTAINER_ENGINE="podman"
            shift
            ;;
        -f|--compose-file)
            COMPOSE_FILE="$2"
            shift 2
            ;;
        --no-build)
            BUILD_IMAGES=false
            shift
            ;;
        --no-migrations)
            RUN_MIGRATIONS=false
            shift
            ;;
        --no-health-checks)
            RUN_HEALTH_CHECKS=false
            shift
            ;;
        --backup)
            BACKUP_BEFORE_DEPLOY=true
            shift
            ;;
        --no-rollback)
            ROLLBACK_ON_FAILURE=false
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

show_help() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -e, --environment ENV      Target environment (staging, production)"
    echo "  --docker                   Use Docker instead of Podman"
    echo "  --podman                   Use Podman (default)"
    echo "  -f, --compose-file FILE    Compose file to use"
    echo "  --no-build                 Skip building images"
    echo "  --no-migrations            Skip running database migrations"
    echo "  --no-health-checks         Skip health checks"
    echo "  --backup                   Create backup before deployment"
    echo "  --no-rollback              Don't rollback on failure"
    echo "  --dry-run                  Show what would be done without executing"
    echo "  -h, --help                 Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 --environment staging"
    echo "  $0 --environment production --backup --docker"
    echo "  $0 --dry-run --environment production"
}

# Function to check prerequisites
check_prerequisites() {
    log "Checking deployment prerequisites..."
    
    # Check if we're in the right directory
    if [ ! -f "package.json" ] || [ ! -d "services" ]; then
        error "Please run this script from the project root directory"
        exit 1
    fi
    
    # Check container engine
    if ! command -v "$CONTAINER_ENGINE" >/dev/null 2>&1; then
        error "$CONTAINER_ENGINE is not installed"
        exit 1
    fi
    
    # Check compose command
    local compose_cmd
    if [ "$CONTAINER_ENGINE" = "podman" ]; then
        if command -v podman-compose >/dev/null 2>&1; then
            compose_cmd="podman-compose"
        elif command -v docker-compose >/dev/null 2>&1; then
            compose_cmd="docker-compose"
            warn "Using docker-compose with Podman"
        else
            error "Neither podman-compose nor docker-compose found"
            exit 1
        fi
    else
        if command -v docker-compose >/dev/null 2>&1; then
            compose_cmd="docker-compose"
        elif docker compose version >/dev/null 2>&1; then
            compose_cmd="docker compose"
        else
            error "Docker Compose not found"
            exit 1
        fi
    fi
    
    # Set global compose command
    COMPOSE_CMD="$compose_cmd"
    
    # Check environment file
    local env_file=".env.${ENVIRONMENT}"
    if [ ! -f "$env_file" ]; then
        error "Environment file $env_file not found"
        exit 1
    fi
    
    # Check compose files
    if [ ! -f "$COMPOSE_FILE" ]; then
        error "Compose file $COMPOSE_FILE not found"
        exit 1
    fi
    
    local env_compose_file="docker-compose.${ENVIRONMENT}.yml"
    if [ ! -f "$env_compose_file" ]; then
        warn "Environment-specific compose file $env_compose_file not found"
    fi
    
    info "Prerequisites check passed"
    info "Using container engine: $CONTAINER_ENGINE"
    info "Using compose command: $COMPOSE_CMD"
    info "Target environment: $ENVIRONMENT"
}

# Function to load environment variables
load_environment() {
    local env_file=".env.${ENVIRONMENT}"
    log "Loading environment variables from $env_file..."
    
    if [ -f "$env_file" ]; then
        # Export variables for use in compose
        set -a
        source "$env_file"
        set +a
        info "Environment variables loaded"
    else
        error "Environment file $env_file not found"
        exit 1
    fi
}

# Function to create backup
create_backup() {
    if [ "$BACKUP_BEFORE_DEPLOY" = false ]; then
        info "Skipping backup creation"
        return
    fi
    
    log "Creating backup before deployment..."
    
    local backup_dir="backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    # Create database backup if PostgreSQL is available
    if [ -n "${DB_HOST:-}" ] && [ -n "${DB_NAME:-}" ]; then
        info "Creating database backup..."
        
        if [ "$DRY_RUN" = false ]; then
            PGPASSWORD="${DB_PASSWORD:-}" pg_dump \
                -h "${DB_HOST:-localhost}" \
                -p "${DB_PORT:-5432}" \
                -U "${DB_USER:-postgres}" \
                -d "${DB_NAME}" \
                > "$backup_dir/database.sql" 2>/dev/null || warn "Database backup failed"
        else
            info "[DRY RUN] Would create database backup in $backup_dir/database.sql"
        fi
    fi
    
    # Backup current image tags
    if [ "$DRY_RUN" = false ]; then
        $CONTAINER_ENGINE images --format "table {{.Repository}}:{{.Tag}}" | grep ecommerce-analytics > "$backup_dir/image-tags.txt" || true
    else
        info "[DRY RUN] Would backup current image tags"
    fi
    
    info "Backup created in $backup_dir"
}

# Function to build images
build_images() {
    if [ "$BUILD_IMAGES" = false ]; then
        info "Skipping image building"
        return
    fi
    
    log "Building application images..."
    
    local services=("dashboard" "analytics" "integration" "error-tracking" "admin")
    
    for service in "${services[@]}"; do
        info "Building $service service..."
        
        if [ "$DRY_RUN" = false ]; then
            $CONTAINER_ENGINE build \
                -t "ecommerce-analytics-${service}:${ENVIRONMENT}" \
                -t "ecommerce-analytics-${service}:latest" \
                -f "services/$service/Dockerfile" \
                . || {
                    error "Failed to build $service service"
                    exit 1
                }
        else
            info "[DRY RUN] Would build ecommerce-analytics-${service}:${ENVIRONMENT}"
        fi
    done
    
    info "All images built successfully"
}

# Function to run database migrations
run_migrations() {
    if [ "$RUN_MIGRATIONS" = false ]; then
        info "Skipping database migrations"
        return
    fi
    
    log "Running database migrations..."
    
    # Determine compose files to use
    local compose_files="-f $COMPOSE_FILE"
    local env_compose_file="docker-compose.${ENVIRONMENT}.yml"
    if [ -f "$env_compose_file" ]; then
        compose_files="$compose_files -f $env_compose_file"
    fi
    
    if [ "$DRY_RUN" = false ]; then
        # Run migrations using the analytics service
        $COMPOSE_CMD $compose_files run --rm analytics-service npm run migrate || {
            error "Database migrations failed"
            return 1
        }
    else
        info "[DRY RUN] Would run: $COMPOSE_CMD $compose_files run --rm analytics-service npm run migrate"
    fi
    
    info "Database migrations completed"
}

# Function to deploy services
deploy_services() {
    log "Deploying services to $ENVIRONMENT environment..."
    
    # Determine compose files to use
    local compose_files="-f $COMPOSE_FILE"
    local env_compose_file="docker-compose.${ENVIRONMENT}.yml"
    if [ -f "$env_compose_file" ]; then
        compose_files="$compose_files -f $env_compose_file"
    fi
    
    if [ "$DRY_RUN" = false ]; then
        # Pull latest images if not building locally
        if [ "$BUILD_IMAGES" = false ]; then
            info "Pulling latest images..."
            $COMPOSE_CMD $compose_files pull || warn "Failed to pull some images"
        fi
        
        # Start services
        $COMPOSE_CMD $compose_files up -d --remove-orphans || {
            error "Failed to start services"
            return 1
        }
        
        # Wait for services to start
        info "Waiting for services to start..."
        sleep 10
    else
        info "[DRY RUN] Would run: $COMPOSE_CMD $compose_files up -d --remove-orphans"
    fi
    
    info "Services deployed successfully"
}

# Function to run health checks
run_health_checks() {
    if [ "$RUN_HEALTH_CHECKS" = false ]; then
        info "Skipping health checks"
        return
    fi
    
    log "Running health checks..."
    
    local services=("dashboard" "analytics" "integration" "error-tracking" "admin")
    local max_attempts=30
    local attempt=1
    
    for service in "${services[@]}"; do
        info "Checking health of $service service..."
        
        if [ "$DRY_RUN" = false ]; then
            while [ $attempt -le $max_attempts ]; do
                if $CONTAINER_ENGINE exec "ecommerce-analytics-${service}-1" curl -f http://localhost:3000/health >/dev/null 2>&1; then
                    info "$service service is healthy"
                    break
                fi
                
                if [ $attempt -eq $max_attempts ]; then
                    error "$service service health check failed"
                    return 1
                fi
                
                sleep 5
                attempt=$((attempt + 1))
            done
        else
            info "[DRY RUN] Would check health of $service service"
        fi
    done
    
    info "All health checks passed"
}

# Function to run smoke tests
run_smoke_tests() {
    log "Running smoke tests..."
    
    local base_url
    if [ "$ENVIRONMENT" = "production" ]; then
        base_url="${PRODUCTION_URL:-http://localhost}"
    else
        base_url="${STAGING_URL:-http://localhost}"
    fi
    
    if [ "$DRY_RUN" = false ]; then
        # Test health endpoints
        local endpoints=("/health" "/health/ready" "/health/live")
        
        for endpoint in "${endpoints[@]}"; do
            info "Testing $base_url$endpoint..."
            if ! curl -f "$base_url$endpoint" >/dev/null 2>&1; then
                error "Smoke test failed for $endpoint"
                return 1
            fi
        done
        
        # Run additional API tests if testing framework is available
        if [ -d "testing" ]; then
            cd testing
            if npm list --depth=0 | grep -q "supertest"; then
                info "Running API smoke tests..."
                export TEST_BASE_URL="$base_url"
                npm run test:smoke || {
                    error "API smoke tests failed"
                    cd ..
                    return 1
                }
            fi
            cd ..
        fi
    else
        info "[DRY RUN] Would run smoke tests against $base_url"
    fi
    
    info "Smoke tests passed"
}

# Function to rollback deployment
rollback_deployment() {
    if [ "$ROLLBACK_ON_FAILURE" = false ]; then
        warn "Rollback is disabled, manual intervention required"
        return
    fi
    
    error "Deployment failed, rolling back..."
    
    # Determine compose files to use
    local compose_files="-f $COMPOSE_FILE"
    local env_compose_file="docker-compose.${ENVIRONMENT}.yml"
    if [ -f "$env_compose_file" ]; then
        compose_files="$compose_files -f $env_compose_file"
    fi
    
    if [ "$DRY_RUN" = false ]; then
        # Stop current services
        $COMPOSE_CMD $compose_files down || warn "Failed to stop services"
        
        # Find the latest backup
        local latest_backup=$(ls -t backups/ | head -n 1)
        if [ -n "$latest_backup" ] && [ -f "backups/$latest_backup/database.sql" ]; then
            warn "Restoring database from backup: $latest_backup"
            
            if [ -n "${DB_HOST:-}" ] && [ -n "${DB_NAME:-}" ]; then
                PGPASSWORD="${DB_PASSWORD:-}" psql \
                    -h "${DB_HOST:-localhost}" \
                    -p "${DB_PORT:-5432}" \
                    -U "${DB_USER:-postgres}" \
                    -d "${DB_NAME}" \
                    < "backups/$latest_backup/database.sql" || warn "Database rollback failed"
            fi
        fi
        
        # Try to restart with previous images
        if [ -f "backups/$latest_backup/image-tags.txt" ]; then
            warn "Rolling back to previous image versions"
            # This would require more sophisticated image management
        fi
        
        # Restart services
        $COMPOSE_CMD $compose_files up -d --remove-orphans || error "Rollback failed"
    else
        info "[DRY RUN] Would rollback deployment"
    fi
    
    warn "Rollback completed"
}

# Function to cleanup old resources
cleanup_old_resources() {
    log "Cleaning up old resources..."
    
    if [ "$DRY_RUN" = false ]; then
        # Remove unused images
        $CONTAINER_ENGINE image prune -f || warn "Failed to prune images"
        
        # Remove old backups (keep last 5)
        if [ -d "backups" ]; then
            ls -t backups/ | tail -n +6 | xargs -I {} rm -rf "backups/{}" || warn "Failed to cleanup old backups"
        fi
    else
        info "[DRY RUN] Would cleanup old images and backups"
    fi
    
    info "Cleanup completed"
}

# Function to generate deployment report
generate_deployment_report() {
    log "Generating deployment report..."
    
    local report_file="deployment-report-${ENVIRONMENT}-$(date +%Y%m%d_%H%M%S).md"
    
    cat > "$report_file" << EOF
# Deployment Report

**Environment:** $ENVIRONMENT
**Date:** $(date)
**Container Engine:** $CONTAINER_ENGINE
**Deployed By:** $(whoami)

## Deployment Configuration

- Build Images: $BUILD_IMAGES
- Run Migrations: $RUN_MIGRATIONS
- Health Checks: $RUN_HEALTH_CHECKS
- Backup Created: $BACKUP_BEFORE_DEPLOY
- Rollback Enabled: $ROLLBACK_ON_FAILURE

## Services Deployed

- Dashboard API
- Analytics Service
- Integration Service
- Error Tracking Service
- Admin Service

## Environment Variables

$(env | grep -E '^(DB_|REDIS_|NODE_ENV|SERVICE_VERSION)' | sort)

## Container Status

EOF

    if [ "$DRY_RUN" = false ]; then
        $CONTAINER_ENGINE ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" >> "$report_file"
    else
        echo "[DRY RUN] Container status would be listed here" >> "$report_file"
    fi
    
    info "Deployment report generated: $report_file"
}

# Main execution function
main() {
    log "Starting deployment to $ENVIRONMENT environment"
    
    if [ "$DRY_RUN" = true ]; then
        warn "DRY RUN MODE - No actual changes will be made"
    fi
    
    # Execute deployment pipeline
    check_prerequisites
    load_environment
    
    # Set error handler for rollback
    if [ "$ROLLBACK_ON_FAILURE" = true ]; then
        trap rollback_deployment ERR
    fi
    
    create_backup
    build_images
    
    # Run migrations before deploying services
    if ! run_migrations; then
        error "Migration failed, aborting deployment"
        exit 1
    fi
    
    deploy_services
    
    # Wait a bit for services to stabilize
    if [ "$DRY_RUN" = false ]; then
        sleep 30
    fi
    
    if ! run_health_checks; then
        error "Health checks failed, deployment unsuccessful"
        exit 1
    fi
    
    if ! run_smoke_tests; then
        error "Smoke tests failed, deployment unsuccessful"
        exit 1
    fi
    
    cleanup_old_resources
    generate_deployment_report
    
    # Clear error trap
    trap - ERR
    
    log "Deployment to $ENVIRONMENT completed successfully! 🎉"
    
    if [ "$ENVIRONMENT" = "production" ]; then
        info "Production URL: ${PRODUCTION_URL:-http://localhost}"
    else
        info "Staging URL: ${STAGING_URL:-http://localhost}"
    fi
}

# Execute main function
main "$@"