#!/usr/bin/env node

/**
 * Create Test User Script
 * Creates a test user for development and testing purposes
 */

const bcrypt = require('bcryptjs');
const { Client } = require('pg');

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'ecommerce_analytics',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'password',
};

async function createTestUser() {
  const client = new Client(dbConfig);
  
  try {
    await client.connect();
    console.log('Connected to PostgreSQL');

    // Test user data
    const testUser = {
      email: '<EMAIL>',
      password: 'password123',
      firstName: 'Test',
      lastName: 'User',
      companyName: 'Test Company',
      role: 'admin',
    };

    // Check if user already exists
    const existingUser = await client.query(
      'SELECT id FROM users WHERE email = $1',
      [testUser.email]
    );

    if (existingUser.rows.length > 0) {
      console.log('✅ Test user already exists');
      console.log('Email:', testUser.email);
      console.log('Password:', testUser.password);
      return;
    }

    // Hash password
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(testUser.password, saltRounds);

    // Create user
    const userQuery = `
      INSERT INTO users (email, password_hash, first_name, last_name, company_name, role, is_active, email_verified, created_at)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
      RETURNING id, email, first_name, last_name, company_name, role, created_at
    `;

    const userResult = await client.query(userQuery, [
      testUser.email,
      hashedPassword,
      testUser.firstName,
      testUser.lastName,
      testUser.companyName,
      testUser.role,
      true, // is_active
      true, // email_verified
      new Date()
    ]);

    const user = userResult.rows[0];

    console.log('🎉 Test user created successfully!');
    console.log('');
    console.log('Login Credentials:');
    console.log('Email:', testUser.email);
    console.log('Password:', testUser.password);
    console.log('');
    console.log('User Details:');
    console.log('ID:', user.id);
    console.log('Name:', `${user.first_name} ${user.last_name}`);
    console.log('Company:', user.company_name);
    console.log('Role:', user.role);
    console.log('Created:', user.created_at);

  } catch (error) {
    console.error('❌ Error creating test user:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('');
      console.log('💡 Database connection failed. Make sure:');
      console.log('1. PostgreSQL is running');
      console.log('2. Database exists');
      console.log('3. Connection details are correct');
      console.log('');
      console.log('Current configuration:');
      console.log('Host:', dbConfig.host);
      console.log('Port:', dbConfig.port);
      console.log('Database:', dbConfig.database);
      console.log('User:', dbConfig.user);
    }
    
    if (error.code === '42P01') {
      console.log('');
      console.log('💡 Users table not found. Run migrations first:');
      console.log('cd /home/<USER>/ecommerce-analytics-saas');
      console.log('npm run migrate');
    }
    
    throw error;
  } finally {
    await client.end();
    console.log('Disconnected from database');
  }
}

// Run if called directly
if (require.main === module) {
  createTestUser().catch(process.exit);
}

module.exports = createTestUser;