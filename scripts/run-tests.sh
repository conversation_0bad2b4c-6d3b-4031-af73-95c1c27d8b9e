#!/bin/bash

# E-commerce Analytics SaaS - Comprehensive Testing Script
# This script runs the complete test suite with proper setup and teardown

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Default values
TEST_TYPE="all"
COVERAGE=false
WATCH=false
CI_MODE=false
PARALLEL=false
CLEANUP_AFTER=true
SETUP_DB=true
VERBOSE=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -t|--type)
            TEST_TYPE="$2"
            shift 2
            ;;
        -c|--coverage)
            COVERAGE=true
            shift
            ;;
        -w|--watch)
            WATCH=true
            shift
            ;;
        --ci)
            CI_MODE=true
            shift
            ;;
        -p|--parallel)
            PARALLEL=true
            shift
            ;;
        --no-cleanup)
            CLEANUP_AFTER=false
            shift
            ;;
        --no-setup)
            SETUP_DB=false
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

show_help() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -t, --type TYPE      Test type to run (all, unit, integration, e2e, performance)"
    echo "  -c, --coverage       Generate coverage reports"
    echo "  -w, --watch          Run tests in watch mode"
    echo "  --ci                 Run in CI mode (optimized for CI/CD)"
    echo "  -p, --parallel       Run tests in parallel"
    echo "  --no-cleanup         Skip cleanup after tests"
    echo "  --no-setup           Skip database setup"
    echo "  -v, --verbose        Enable verbose output"
    echo "  -h, --help           Show this help message"
    echo ""
    echo "Test Types:"
    echo "  all                  Run all test suites"
    echo "  unit                 Run unit tests only"
    echo "  integration          Run integration tests only"
    echo "  e2e                  Run end-to-end tests only"
    echo "  performance          Run performance tests only"
    echo "  smoke                Run smoke tests only"
    echo "  security             Run security tests only"
    echo ""
    echo "Examples:"
    echo "  $0 --type unit --coverage"
    echo "  $0 --type e2e --verbose"
    echo "  $0 --ci --type all"
    echo "  $0 --watch --type unit"
}

# Function to check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check if we're in the right directory
    if [ ! -f "package.json" ] || [ ! -d "testing" ]; then
        error "Please run this script from the project root directory"
        exit 1
    fi
    
    # Check Node.js version
    if ! command -v node >/dev/null 2>&1; then
        error "Node.js is not installed"
        exit 1
    fi
    
    local node_version=$(node -v | cut -d 'v' -f 2 | cut -d '.' -f 1)
    if [ "$node_version" -lt 18 ]; then
        error "Node.js version 18 or higher is required"
        exit 1
    fi
    
    # Check if npm/yarn is available
    if ! command -v npm >/dev/null 2>&1; then
        error "npm is not installed"
        exit 1
    fi
    
    # Check Docker for integration tests
    if [ "$TEST_TYPE" = "all" ] || [ "$TEST_TYPE" = "integration" ] || [ "$TEST_TYPE" = "e2e" ]; then
        if ! command -v docker >/dev/null 2>&1; then
            warn "Docker is not available. Integration and E2E tests may fail."
        fi
    fi
    
    info "Prerequisites check passed"
}

# Function to install dependencies
install_dependencies() {
    log "Installing test dependencies..."
    
    cd testing
    
    if [ ! -f "package.json" ]; then
        error "Testing package.json not found"
        exit 1
    fi
    
    npm install --silent
    
    cd ..
    
    # Install root dependencies if needed
    npm install --silent
    
    info "Dependencies installed successfully"
}

# Function to setup test environment
setup_test_environment() {
    if [ "$SETUP_DB" = false ]; then
        info "Skipping database setup"
        return
    fi
    
    log "Setting up test environment..."
    
    # Set test environment variables
    export NODE_ENV=test
    export LOG_LEVEL=error
    export DB_NAME=ecommerce_analytics_test
    export REDIS_DB=15
    
    # Create .env.test if it doesn't exist
    if [ ! -f ".env.test" ]; then
        info "Creating .env.test file..."
        cat > .env.test << EOF
NODE_ENV=test
LOG_LEVEL=error
DB_HOST=localhost
DB_PORT=5432
DB_NAME=ecommerce_analytics_test
DB_USER=postgres
DB_PASSWORD=password
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=15
JWT_SECRET=test_jwt_secret_key_for_testing_only
RATE_LIMIT_MAX=1000
CORS_ORIGINS=http://localhost:3000,http://localhost:5173
EOF
    fi
    
    # Load test environment
    if [ -f ".env.test" ]; then
        export $(cat .env.test | grep -v '^#' | xargs)
    fi
    
    # Run global setup
    node testing/setup/global.setup.js
    
    info "Test environment setup completed"
}

# Function to start required services
start_services() {
    if [ "$TEST_TYPE" = "unit" ]; then
        info "Unit tests don't require services"
        return
    fi
    
    log "Starting required services..."
    
    # Check if services are already running
    if docker-compose ps | grep -q "Up"; then
        info "Services are already running"
        return
    fi
    
    # Start core services for testing
    docker-compose up -d postgres redis
    
    # Wait for services to be ready
    info "Waiting for services to be ready..."
    sleep 10
    
    # Test database connection
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if docker-compose exec -T postgres pg_isready -h localhost -p 5432 >/dev/null 2>&1; then
            info "Database is ready"
            break
        fi
        
        if [ $attempt -eq $max_attempts ]; then
            error "Database failed to start within expected time"
            exit 1
        fi
        
        sleep 2
        attempt=$((attempt + 1))
    done
    
    # Test Redis connection
    if docker-compose exec -T redis redis-cli ping >/dev/null 2>&1; then
        info "Redis is ready"
    else
        warn "Redis connection test failed"
    fi
    
    info "Services started successfully"
}

# Function to run tests based on type
run_tests() {
    log "Running $TEST_TYPE tests..."
    
    cd testing
    
    local jest_args=""
    local test_command="npm run test"
    
    # Configure Jest arguments based on options
    if [ "$COVERAGE" = true ]; then
        jest_args="$jest_args --coverage"
    fi
    
    if [ "$WATCH" = true ]; then
        jest_args="$jest_args --watch"
    fi
    
    if [ "$CI_MODE" = true ]; then
        jest_args="$jest_args --ci --watchAll=false --passWithNoTests"
        export CI=true
    fi
    
    if [ "$PARALLEL" = true ]; then
        jest_args="$jest_args --maxWorkers=4"
    else
        jest_args="$jest_args --runInBand"
    fi
    
    if [ "$VERBOSE" = true ]; then
        jest_args="$jest_args --verbose"
        export TEST_VERBOSE=true
    fi
    
    # Set test command based on type
    case $TEST_TYPE in
        "unit")
            test_command="npm run test:unit"
            ;;
        "integration")
            test_command="npm run test:integration"
            ;;
        "e2e")
            test_command="npm run test:e2e"
            ;;
        "performance")
            test_command="npm run test:performance"
            ;;
        "smoke")
            test_command="npm run test:smoke"
            ;;
        "security")
            test_command="npm run test:security"
            ;;
        "all")
            test_command="npm run test"
            ;;
        *)
            error "Unknown test type: $TEST_TYPE"
            exit 1
            ;;
    esac
    
    # Add Jest arguments to command
    if [ -n "$jest_args" ]; then
        test_command="$test_command -- $jest_args"
    fi
    
    info "Executing: $test_command"
    
    # Run the tests
    if eval $test_command; then
        log "Tests completed successfully"
    else
        error "Tests failed"
        exit 1
    fi
    
    cd ..
}

# Function to generate reports
generate_reports() {
    if [ "$COVERAGE" = false ]; then
        return
    fi
    
    log "Generating test reports..."
    
    cd testing
    
    # Generate coverage badge
    if command -v npx >/dev/null 2>&1; then
        npx coverage-badge-creator || warn "Failed to generate coverage badge"
    fi
    
    # Generate HTML reports
    if [ -d "reports" ]; then
        info "Test reports generated in testing/reports/"
        
        if [ -f "reports/jest-report.html" ]; then
            info "HTML Report: testing/reports/jest-report.html"
        fi
        
        if [ -f "reports/junit.xml" ]; then
            info "JUnit Report: testing/reports/junit.xml"
        fi
    fi
    
    # Generate coverage reports
    if [ -d "coverage" ]; then
        info "Coverage reports generated in testing/coverage/"
        
        if [ -f "coverage/lcov-report/index.html" ]; then
            info "Coverage HTML: testing/coverage/lcov-report/index.html"
        fi
        
        if [ -f "coverage/coverage-summary.json" ]; then
            # Display coverage summary
            info "Coverage Summary:"
            node -e "
                const coverage = require('./coverage/coverage-summary.json');
                const total = coverage.total;
                console.log('  Lines: ' + total.lines.pct + '%');
                console.log('  Functions: ' + total.functions.pct + '%');
                console.log('  Branches: ' + total.branches.pct + '%');
                console.log('  Statements: ' + total.statements.pct + '%');
            " 2>/dev/null || true
        fi
    fi
    
    cd ..
}

# Function to cleanup test environment
cleanup_test_environment() {
    if [ "$CLEANUP_AFTER" = false ]; then
        info "Skipping cleanup"
        return
    fi
    
    log "Cleaning up test environment..."
    
    # Run global teardown
    node testing/setup/global.teardown.js 2>/dev/null || warn "Global teardown failed"
    
    # Stop test services if they were started by this script
    if [ "$TEST_TYPE" != "unit" ]; then
        docker-compose stop postgres redis 2>/dev/null || warn "Failed to stop services"
    fi
    
    info "Cleanup completed"
}

# Function to handle script interruption
handle_interrupt() {
    error "Script interrupted"
    cleanup_test_environment
    exit 1
}

# Main execution
main() {
    log "Starting E-commerce Analytics Test Suite"
    info "Test Type: $TEST_TYPE"
    info "Coverage: $COVERAGE"
    info "CI Mode: $CI_MODE"
    
    # Set up interrupt handler
    trap handle_interrupt INT TERM
    
    # Execute test pipeline
    check_prerequisites
    install_dependencies
    setup_test_environment
    start_services
    run_tests
    generate_reports
    cleanup_test_environment
    
    log "All tests completed successfully! 🎉"
    
    if [ "$COVERAGE" = true ]; then
        info "Open testing/reports/jest-report.html to view detailed test results"
        info "Open testing/coverage/lcov-report/index.html to view coverage report"
    fi
}

# Execute main function
main "$@"