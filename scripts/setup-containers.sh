#!/bin/bash

# Container setup and management script for e-commerce analytics platform
# Supports both <PERSON><PERSON> and <PERSON><PERSON> with comprehensive environment management

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
ENVIRONMENT="production"
ACTION="up"
DETACH=true
REBUILD=false
CLEAN=false
LOGS=false
PROFILE=""

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# Function to show usage
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS] [ACTION]

Manage containers for the e-commerce analytics platform.

ACTIONS:
    up          Start all services (default)
    down        Stop all services
    restart     Restart all services
    status      Show container status
    logs        Show logs from all services
    shell       Open shell in a service container
    clean       Stop and remove all containers, networks, and volumes
    health      Check health of all services

OPTIONS:
    -e, --env ENV          Environment: development, production (default: production)
    -f, --foreground       Run in foreground (don't detach)
    -r, --rebuild          Rebuild containers before starting
    -c, --clean            Clean up before starting
    -l, --logs             Show logs after starting
    -p, --profile PROFILE  Use docker-compose profile (dev-tools)
    -s, --service SERVICE  Target specific service for shell/logs
    -h, --help             Show this help message

EXAMPLES:
    $0                           # Start production environment
    $0 -e development            # Start development environment
    $0 -e development -p dev-tools  # Start dev environment with tools
    $0 down                      # Stop all services
    $0 logs -s analytics-service # Show logs for analytics service
    $0 shell -s postgres         # Open shell in postgres container
    $0 clean                     # Clean up everything

ENVIRONMENT FILES:
    .env                    # Default environment variables
    .env.development        # Development overrides
    .env.production         # Production overrides
EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--env)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -f|--foreground)
            DETACH=false
            shift
            ;;
        -r|--rebuild)
            REBUILD=true
            shift
            ;;
        -c|--clean)
            CLEAN=true
            shift
            ;;
        -l|--logs)
            LOGS=true
            shift
            ;;
        -p|--profile)
            PROFILE="$2"
            shift 2
            ;;
        -s|--service)
            SERVICE="$2"
            shift 2
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        up|down|restart|status|logs|shell|clean|health)
            ACTION="$1"
            shift
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Detect container tool (podman or docker)
if command -v podman-compose &> /dev/null; then
    COMPOSE_TOOL="podman-compose"
    CONTAINER_TOOL="podman"
elif command -v docker-compose &> /dev/null; then
    COMPOSE_TOOL="docker-compose"
    CONTAINER_TOOL="docker"
elif command -v docker &> /dev/null && docker compose version &> /dev/null; then
    COMPOSE_TOOL="docker compose"
    CONTAINER_TOOL="docker"
else
    print_error "No compatible container orchestration tool found."
    print_error "Please install docker-compose, podman-compose, or docker with compose plugin."
    exit 1
fi

print_status "Using container tool: $COMPOSE_TOOL"

# Validate environment
if [[ "$ENVIRONMENT" != "development" && "$ENVIRONMENT" != "production" ]]; then
    print_error "Invalid environment: $ENVIRONMENT. Must be 'development' or 'production'"
    exit 1
fi

# Set up compose files
BASE_COMPOSE="-f docker-compose.yml"
if [[ "$ENVIRONMENT" == "development" ]]; then
    COMPOSE_FILES="$BASE_COMPOSE -f docker-compose.dev.yml"
else
    COMPOSE_FILES="$BASE_COMPOSE -f docker-compose.prod.yml"
fi

# Add profile if specified
PROFILE_OPTS=""
if [[ -n "$PROFILE" ]]; then
    PROFILE_OPTS="--profile $PROFILE"
fi

# Set environment variables
export BUILD_TARGET="$ENVIRONMENT"
export NODE_ENV="$ENVIRONMENT"

# Load environment file if it exists
ENV_FILE=".env"
if [[ -f ".env.$ENVIRONMENT" ]]; then
    ENV_FILE=".env.$ENVIRONMENT"
fi

if [[ -f "$ENV_FILE" ]]; then
    print_status "Loading environment from $ENV_FILE"
    set -a
    source "$ENV_FILE"
    set +a
fi

# Function to check if services are healthy
check_health() {
    print_status "Checking service health..."
    
    local healthy=true
    local services=("analytics-service" "dashboard-service" "integration-service" "frontend" "postgres" "redis")
    
    for service in "${services[@]}"; do
        local container_name="ecommerce-${service//-service/}"
        local health_status=$($CONTAINER_TOOL inspect --format='{{.State.Health.Status}}' "$container_name" 2>/dev/null || echo "unknown")
        
        case $health_status in
            "healthy")
                print_status "✓ $service: healthy"
                ;;
            "unhealthy")
                print_error "✗ $service: unhealthy"
                healthy=false
                ;;
            "starting")
                print_warning "⏳ $service: starting"
                ;;
            *)
                print_warning "? $service: $health_status"
                ;;
        esac
    done
    
    if [[ "$healthy" == "true" ]]; then
        print_status "All services are healthy!"
    else
        print_error "Some services are unhealthy. Check logs for details."
        return 1
    fi
}

# Function to show container status
show_status() {
    print_status "Container status:"
    $COMPOSE_TOOL $COMPOSE_FILES ps
    
    print_status "\nService URLs:"
    echo "  Frontend:     http://localhost:${FRONTEND_PORT:-3000}"
    echo "  Dashboard:    http://localhost:${DASHBOARD_PORT:-3001}"
    echo "  Analytics:    http://localhost:${ANALYTICS_PORT:-3002}"
    echo "  Integration:  http://localhost:${INTEGRATION_PORT:-3003}"
    
    if [[ "$ENVIRONMENT" == "development" && "$PROFILE" == "dev-tools" ]]; then
        echo "  PgAdmin:      http://localhost:5050"
        echo "  Redis UI:     http://localhost:8081"
        echo "  Mailcatcher:  http://localhost:1080"
    fi
}

# Main action handling
case $ACTION in
    "up")
        if [[ "$CLEAN" == "true" ]]; then
            print_status "Cleaning up before start..."
            $COMPOSE_TOOL $COMPOSE_FILES down -v --remove-orphans
        fi
        
        if [[ "$REBUILD" == "true" ]]; then
            print_status "Rebuilding containers..."
            $COMPOSE_TOOL $COMPOSE_FILES build --no-cache
        fi
        
        print_status "Starting services in $ENVIRONMENT mode..."
        
        if [[ "$DETACH" == "true" ]]; then
            $COMPOSE_TOOL $COMPOSE_FILES up -d $PROFILE_OPTS
            
            print_status "Waiting for services to be ready..."
            sleep 10
            
            if [[ "$LOGS" == "true" ]]; then
                $COMPOSE_TOOL $COMPOSE_FILES logs --tail=50
            fi
            
            show_status
            check_health
        else
            $COMPOSE_TOOL $COMPOSE_FILES up $PROFILE_OPTS
        fi
        ;;
        
    "down")
        print_status "Stopping services..."
        $COMPOSE_TOOL $COMPOSE_FILES down
        ;;
        
    "restart")
        print_status "Restarting services..."
        $COMPOSE_TOOL $COMPOSE_FILES restart
        show_status
        ;;
        
    "status")
        show_status
        ;;
        
    "logs")
        if [[ -n "$SERVICE" ]]; then
            $COMPOSE_TOOL $COMPOSE_FILES logs -f "$SERVICE"
        else
            $COMPOSE_TOOL $COMPOSE_FILES logs -f
        fi
        ;;
        
    "shell")
        if [[ -z "$SERVICE" ]]; then
            print_error "Service name required for shell access. Use -s SERVICE"
            exit 1
        fi
        
        print_status "Opening shell in $SERVICE..."
        if [[ "$SERVICE" == "postgres" ]]; then
            $COMPOSE_TOOL $COMPOSE_FILES exec "$SERVICE" psql -U postgres -d ecommerce_analytics
        elif [[ "$SERVICE" == "redis" ]]; then
            $COMPOSE_TOOL $COMPOSE_FILES exec "$SERVICE" redis-cli
        else
            $COMPOSE_TOOL $COMPOSE_FILES exec "$SERVICE" sh
        fi
        ;;
        
    "clean")
        print_warning "This will remove all containers, networks, and volumes. Are you sure? (y/N)"
        read -r response
        if [[ "$response" =~ ^[Yy]$ ]]; then
            print_status "Cleaning up everything..."
            $COMPOSE_TOOL $COMPOSE_FILES down -v --remove-orphans --rmi local
            $CONTAINER_TOOL system prune -f
            print_status "Cleanup completed"
        else
            print_status "Cleanup cancelled"
        fi
        ;;
        
    "health")
        check_health
        ;;
        
    *)
        print_error "Unknown action: $ACTION"
        show_usage
        exit 1
        ;;
esac